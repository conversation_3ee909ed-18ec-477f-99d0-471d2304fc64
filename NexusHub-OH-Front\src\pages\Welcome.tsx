import { PageContainer } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Card, theme, Row, Col, Statistic, Typography, Button, Divider, Space } from 'antd';
import { AppstoreOutlined, UserOutlined, MessageOutlined, FileTextOutlined } from '@ant-design/icons';
import React from 'react';

const { Title, Paragraph } = Typography;

/**
 * 功能卡片组件
 */
const FeatureCard: React.FC<{
  title: string;
  icon: React.ReactNode;
  description: string;
  link: string;
}> = ({ title, icon, description, link }) => {
  const { useToken } = theme;
  const { token } = useToken();

  return (
    <Card
      hoverable
      style={{ height: '100%' }}
      actions={[
        <Button type="link" href={link}>
          进入管理
        </Button>,
      ]}
    >
      <div style={{ fontSize: '24px', color: token.colorPrimary, marginBottom: '16px' }}>
        {icon}
      </div>
      <Title level={4}>{title}</Title>
      <Paragraph style={{ minHeight: '60px' }}>{description}</Paragraph>
    </Card>
  );
};

/**
 * 统计卡片组件
 */
const StatCard: React.FC<{
  title: string;
  value: number;
  suffix?: string;
  color?: string;
}> = ({ title, value, suffix, color }) => {
  return (
    <Card>
      <Statistic 
        title={title} 
        value={value} 
        suffix={suffix}
        valueStyle={{ color }} 
      />
    </Card>
  );
};

const Welcome: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const username = initialState?.currentUser?.username || '管理员';
  
  return (
    <PageContainer>
      <Card style={{ marginBottom: 24 }}>
        <Title level={2}>欢迎使用 NexusHub 应用管理平台</Title>
        <Paragraph>
          你好，{username}！NexusHub 是一站式应用管理与分发平台，为 OpenHarmony 生态提供完整的应用上架、审核和分发解决方案。
        </Paragraph>
        <Divider />
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <StatCard 
              title="应用总数" 
              value={128} 
              suffix="个" 
              color="#1890ff" 
            />
          </Col>
          <Col span={6}>
            <StatCard 
              title="用户总数" 
              value={2455} 
              suffix="人" 
              color="#52c41a" 
            />
          </Col>
          <Col span={6}>
            <StatCard 
              title="本月下载" 
              value={15890} 
              suffix="次" 
              color="#722ed1" 
            />
          </Col>
          <Col span={6}>
            <StatCard 
              title="待审核应用" 
              value={12} 
              suffix="个" 
              color="#fa8c16" 
            />
          </Col>
        </Row>
      </Card>
      
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <FeatureCard
            title="应用管理"
            icon={<AppstoreOutlined />}
            description="管理所有应用，包括上架、审核、下架等操作。查看应用详情、版本历史和统计数据。"
            link="/App"
          />
        </Col>
        <Col span={6}>
          <FeatureCard
            title="用户管理"
            icon={<UserOutlined />}
            description="管理平台用户，包括普通用户和开发者。查看用户信息、审核开发者资格。"
            link="/UserManagement"
          />
        </Col>
        <Col span={6}>
          <FeatureCard
            title="评论管理"
            icon={<MessageOutlined />}
            description="监控和管理用户评论，处理违规内容，回复用户反馈。"
            link="/Review"
          />
        </Col>
        <Col span={6}>
          <FeatureCard
            title="内容管理"
            icon={<FileTextOutlined />}
            description="管理平台内容，包括轮播图、推荐应用、活动等。"
            link="/Content"
          />
        </Col>
      </Row>
    </PageContainer>
  );
};

export default Welcome;
