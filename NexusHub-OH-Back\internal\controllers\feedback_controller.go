package controllers

import (
	"strconv"
	"time"

	"nexushub-oh-back/internal/models"
	"nexushub-oh-back/pkg/response"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// FeedbackController 意见反馈控制器
type FeedbackController struct {
	DB *gorm.DB
}

// NewFeedbackController 创建意见反馈控制器
func NewFeedbackController(db *gorm.DB) *FeedbackController {
	return &FeedbackController{
		DB: db,
	}
}

// ========== 用户反馈功能 ==========

// CreateFeedback 创建反馈
// @Summary 创建反馈
// @Description 用户提交新的反馈
// @Tags 意见反馈
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param request body CreateFeedbackRequest true "反馈信息"
// @Success 200 {object} response.Response{data=models.FeedbackResponse} "提交成功"
// @Failure 400 {object} response.ErrorResponse "参数错误"
// @Failure 401 {object} response.ErrorResponse "未授权"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/feedback [post]
func (c *FeedbackController) CreateFeedback(ctx *gin.Context) {
	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "用户未登录")
		return
	}

	var req CreateFeedbackRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, "参数错误: "+err.Error())
		return
	}

	// 检查分类是否存在
	if req.CategoryID != nil {
		var category models.FeedbackCategory
		if err := c.DB.First(&category, *req.CategoryID).Error; err != nil {
			response.BadRequest(ctx, "反馈分类不存在")
			return
		}
	}

	// 获取客户端信息
	ipAddress := ctx.ClientIP()
	userAgent := ctx.GetHeader("User-Agent")

	feedback := models.Feedback{
		UserID:       userID.(uint),
		CategoryID:   req.CategoryID,
		Type:         models.FeedbackType(req.Type),
		Title:        req.Title,
		Content:      req.Content,
		ContactEmail: req.ContactInfo, // 使用ContactInfo作为邮箱
		AppID:        req.AppID,
		AppVersion:   req.AppVersion,
		DeviceInfo:   req.DeviceInfo,
		Screenshots:  req.Screenshots,
		Priority:     "normal",  // 默认优先级
		Status:       "pending", // 默认状态
		IPAddress:    ipAddress,
		UserAgent:    userAgent,
	}

	if err := c.DB.Create(&feedback).Error; err != nil {
		response.InternalServerError(ctx, "提交反馈失败")
		return
	}

	// 预加载关联数据
	if err := c.DB.Preload("User").Preload("Category").Preload("App").
		First(&feedback, feedback.ID).Error; err != nil {
		response.InternalServerError(ctx, "查询反馈失败")
		return
	}

	resp := convertToFeedbackResponse(feedback)
	response.Success(ctx, resp)
}

// GetMyFeedback 获取我的反馈
// @Summary 获取我的反馈
// @Description 获取当前用户的反馈列表
// @Tags 意见反馈
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Param type query string false "反馈类型"
// @Param status query string false "反馈状态"
// @Param category_id query int false "分类ID"
// @Success 200 {object} response.PageResponse{data=[]models.FeedbackResponse} "获取成功"
// @Failure 401 {object} response.ErrorResponse "未授权"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/feedback [get]
func (c *FeedbackController) GetMyFeedback(ctx *gin.Context) {
	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "用户未登录")
		return
	}

	// 获取分页参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}
	offset := (page - 1) * limit

	// 构建查询条件
	query := c.DB.Model(&models.Feedback{}).Where("user_id = ?", userID)

	// 应用筛选条件
	if feedbackType := ctx.Query("type"); feedbackType != "" {
		query = query.Where("type = ?", feedbackType)
	}

	if status := ctx.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}

	if categoryIDStr := ctx.Query("category_id"); categoryIDStr != "" {
		if categoryID, err := strconv.Atoi(categoryIDStr); err == nil {
			query = query.Where("category_id = ?", categoryID)
		}
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		response.InternalServerError(ctx, "查询反馈总数失败")
		return
	}

	// 获取数据
	var feedbacks []models.Feedback
	if err := query.Preload("User").Preload("Category").Preload("App").
		Order("created_at DESC").
		Offset(offset).Limit(limit).
		Find(&feedbacks).Error; err != nil {
		response.InternalServerError(ctx, "查询反馈失败")
		return
	}

	// 转换为响应格式
	var result []models.FeedbackResponse
	for _, feedback := range feedbacks {
		result = append(result, convertToFeedbackResponse(feedback))
	}

	response.SuccessWithPagination(ctx, result, total, page, limit)
}

// GetFeedbackDetail 获取反馈详情
// @Summary 获取反馈详情
// @Description 获取反馈详情（只能查看自己的反馈）
// @Tags 意见反馈
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "反馈ID"
// @Success 200 {object} response.Response{data=models.FeedbackResponse} "获取成功"
// @Failure 400 {object} response.ErrorResponse "参数错误"
// @Failure 401 {object} response.ErrorResponse "未授权"
// @Failure 403 {object} response.ErrorResponse "无权限"
// @Failure 404 {object} response.ErrorResponse "反馈不存在"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/feedback/{id} [get]
func (c *FeedbackController) GetFeedbackDetail(ctx *gin.Context) {
	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "用户未登录")
		return
	}

	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.BadRequest(ctx, "无效的反馈ID")
		return
	}

	var feedback models.Feedback
	if err := c.DB.Preload("User").Preload("Category").Preload("App").Preload("Replies").
		Where("id = ? AND user_id = ?", id, userID).
		First(&feedback).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "反馈不存在")
			return
		}
		response.InternalServerError(ctx, "查询反馈失败")
		return
	}

	resp := convertToFeedbackResponse(feedback)
	response.Success(ctx, resp)
}

// ========== 管理员功能 ==========

// GetAllFeedback 获取所有反馈（管理员）
// @Summary 获取所有反馈
// @Description 获取所有用户的反馈列表（管理员权限）
// @Tags 意见反馈管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Param type query string false "反馈类型"
// @Param status query string false "反馈状态"
// @Param priority query string false "优先级"
// @Param category_id query int false "分类ID"
// @Param keyword query string false "关键词搜索"
// @Success 200 {object} response.PageResponse{data=[]models.FeedbackResponse} "获取成功"
// @Failure 401 {object} response.ErrorResponse "未授权"
// @Failure 403 {object} response.ErrorResponse "无权限"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/admin/feedback [get]
func (c *FeedbackController) GetAllFeedback(ctx *gin.Context) {
	// 检查管理员权限
	if !c.checkAdminPermission(ctx) {
		return
	}

	// 获取分页参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}
	offset := (page - 1) * limit

	// 构建查询条件
	query := c.DB.Model(&models.Feedback{})

	// 应用筛选条件
	if feedbackType := ctx.Query("type"); feedbackType != "" {
		query = query.Where("type = ?", feedbackType)
	}

	if status := ctx.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}

	if priority := ctx.Query("priority"); priority != "" {
		query = query.Where("priority = ?", priority)
	}

	if categoryIDStr := ctx.Query("category_id"); categoryIDStr != "" {
		if categoryID, err := strconv.Atoi(categoryIDStr); err == nil {
			query = query.Where("category_id = ?", categoryID)
		}
	}

	// 关键词搜索
	if keyword := ctx.Query("keyword"); keyword != "" {
		query = query.Where("title LIKE ? OR content LIKE ?",
			"%"+keyword+"%", "%"+keyword+"%")
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		response.InternalServerError(ctx, "查询反馈总数失败")
		return
	}

	// 获取数据
	var feedbacks []models.Feedback
	if err := query.Preload("User").Preload("Category").Preload("App").
		Order("priority DESC, created_at DESC").
		Offset(offset).Limit(limit).
		Find(&feedbacks).Error; err != nil {
		response.InternalServerError(ctx, "查询反馈失败")
		return
	}

	// 转换为响应格式
	var result []models.FeedbackResponse
	for _, feedback := range feedbacks {
		result = append(result, convertToFeedbackResponse(feedback))
	}

	response.SuccessWithPagination(ctx, result, total, page, limit)
}

// UpdateFeedbackStatus 更新反馈状态（管理员）
// @Summary 更新反馈状态
// @Description 更新反馈的状态和优先级（管理员权限）
// @Tags 意见反馈管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "反馈ID"
// @Param request body UpdateFeedbackStatusRequest true "状态信息"
// @Success 200 {object} response.Response{data=models.FeedbackResponse} "更新成功"
// @Failure 400 {object} response.ErrorResponse "参数错误"
// @Failure 401 {object} response.ErrorResponse "未授权"
// @Failure 403 {object} response.ErrorResponse "无权限"
// @Failure 404 {object} response.ErrorResponse "反馈不存在"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/admin/feedback/{id}/status [put]
func (c *FeedbackController) UpdateFeedbackStatus(ctx *gin.Context) {
	// 检查管理员权限
	if !c.checkAdminPermission(ctx) {
		return
	}

	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.BadRequest(ctx, "无效的反馈ID")
		return
	}

	var req UpdateFeedbackStatusRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, "参数错误: "+err.Error())
		return
	}

	var feedback models.Feedback
	if err := c.DB.First(&feedback, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "反馈不存在")
			return
		}
		response.InternalServerError(ctx, "查询反馈失败")
		return
	}

	// 更新状态和优先级
	feedback.Status = models.FeedbackStatus(req.Status)
	feedback.Priority = models.FeedbackPriority(req.Priority)

	// 如果状态变为已解决，记录解决时间
	if req.Status == "resolved" && feedback.ResolvedAt == nil {
		now := time.Now()
		feedback.ResolvedAt = &now
	}

	if err := c.DB.Save(&feedback).Error; err != nil {
		response.InternalServerError(ctx, "更新反馈状态失败")
		return
	}

	// 预加载关联数据
	if err := c.DB.Preload("User").Preload("Category").Preload("App").
		First(&feedback, feedback.ID).Error; err != nil {
		response.InternalServerError(ctx, "查询反馈失败")
		return
	}

	resp := convertToFeedbackResponse(feedback)
	response.Success(ctx, resp)
}

// ReplyToFeedback 回复反馈（管理员）
// @Summary 回复反馈
// @Description 管理员回复用户反馈
// @Tags 意见反馈管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "反馈ID"
// @Param request body ReplyFeedbackRequest true "回复内容"
// @Success 200 {object} response.Response{data=models.FeedbackReplyResponse} "回复成功"
// @Failure 400 {object} response.ErrorResponse "参数错误"
// @Failure 401 {object} response.ErrorResponse "未授权"
// @Failure 403 {object} response.ErrorResponse "无权限"
// @Failure 404 {object} response.ErrorResponse "反馈不存在"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/admin/feedback/{id}/reply [post]
func (c *FeedbackController) ReplyToFeedback(ctx *gin.Context) {
	// 检查管理员权限
	if !c.checkAdminPermission(ctx) {
		return
	}

	idStr := ctx.Param("id")
	feedbackID, err := strconv.Atoi(idStr)
	if err != nil {
		response.BadRequest(ctx, "无效的反馈ID")
		return
	}

	var req ReplyFeedbackRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, "参数错误: "+err.Error())
		return
	}

	// 检查反馈是否存在
	var feedback models.Feedback
	if err := c.DB.First(&feedback, feedbackID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "反馈不存在")
			return
		}
		response.InternalServerError(ctx, "查询反馈失败")
		return
	}

	// 获取当前管理员ID
	adminID, _ := ctx.Get("user_id")

	reply := models.FeedbackReply{
		FeedbackID: uint(feedbackID),
		ReplyBy:    adminID.(uint),
		Content:    req.Content,
		IsInternal: !req.IsPublic, // IsInternal与IsPublic相反
	}

	if err := c.DB.Create(&reply).Error; err != nil {
		response.InternalServerError(ctx, "创建回复失败")
		return
	}

	// 预加载关联数据
	if err := c.DB.Preload("Admin").First(&reply, reply.ID).Error; err != nil {
		response.InternalServerError(ctx, "查询回复失败")
		return
	}

	// 更新反馈状态为已回复
	if feedback.Status == "pending" {
		c.DB.Model(&feedback).Update("status", "replied")
	}

	resp := convertToReplyResponse(reply)
	response.Success(ctx, resp)
}

// ========== 分类管理 ==========

// CreateFeedbackCategory 创建反馈分类（管理员）
// @Summary 创建反馈分类
// @Description 创建新的反馈分类（管理员权限）
// @Tags 意见反馈管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param request body CreateFeedbackCategoryRequest true "分类信息"
// @Success 200 {object} response.Response{data=models.FeedbackCategoryResponse} "创建成功"
// @Failure 400 {object} response.ErrorResponse "参数错误"
// @Failure 401 {object} response.ErrorResponse "未授权"
// @Failure 403 {object} response.ErrorResponse "无权限"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/admin/feedback/categories [post]
func (c *FeedbackController) CreateFeedbackCategory(ctx *gin.Context) {
	// 检查管理员权限
	if !c.checkAdminPermission(ctx) {
		return
	}

	var req CreateFeedbackCategoryRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, "参数错误: "+err.Error())
		return
	}

	// 检查分类名称是否已存在
	var existingCategory models.FeedbackCategory
	if err := c.DB.Where("name = ?", req.Name).First(&existingCategory).Error; err == nil {
		response.BadRequest(ctx, "分类名称已存在")
		return
	}

	category := models.FeedbackCategory{
		Name:        req.Name,
		Description: req.Description,
		Icon:        req.Icon,
		SortOrder:   req.SortOrder,
		IsActive:    req.IsActive,
	}

	if err := c.DB.Create(&category).Error; err != nil {
		response.InternalServerError(ctx, "创建分类失败")
		return
	}

	resp := convertToFeedbackCategoryResponse(category)
	response.Success(ctx, resp)
}

// GetFeedbackCategories 获取反馈分类列表
// @Summary 获取反馈分类列表
// @Description 获取反馈分类列表
// @Tags 意见反馈
// @Accept json
// @Produce json
// @Param is_active query bool false "是否激活"
// @Success 200 {object} response.Response{data=[]models.FeedbackCategoryResponse} "获取成功"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/feedback/categories [get]
func (c *FeedbackController) GetFeedbackCategories(ctx *gin.Context) {
	// 构建查询条件
	query := c.DB.Model(&models.FeedbackCategory{})

	// 筛选激活状态
	if isActiveStr := ctx.Query("is_active"); isActiveStr != "" {
		if isActive, err := strconv.ParseBool(isActiveStr); err == nil {
			query = query.Where("is_active = ?", isActive)
		}
	} else {
		// 默认只显示激活的分类
		query = query.Where("is_active = ?", true)
	}

	// 获取数据
	var categories []models.FeedbackCategory
	if err := query.Order("sort_order ASC, id ASC").Find(&categories).Error; err != nil {
		response.InternalServerError(ctx, "查询分类失败")
		return
	}

	// 转换为响应格式
	var result []models.FeedbackCategoryResponse
	for _, category := range categories {
		result = append(result, convertToFeedbackCategoryResponse(category))
	}

	response.Success(ctx, result)
}

// UpdateFeedbackCategory 更新反馈分类（管理员）
// @Summary 更新反馈分类
// @Description 更新反馈分类信息（管理员权限）
// @Tags 意见反馈管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "分类ID"
// @Param request body UpdateFeedbackCategoryRequest true "分类信息"
// @Success 200 {object} response.Response{data=models.FeedbackCategoryResponse} "更新成功"
// @Failure 400 {object} response.ErrorResponse "参数错误"
// @Failure 401 {object} response.ErrorResponse "未授权"
// @Failure 403 {object} response.ErrorResponse "无权限"
// @Failure 404 {object} response.ErrorResponse "分类不存在"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/admin/feedback/categories/{id} [put]
func (c *FeedbackController) UpdateFeedbackCategory(ctx *gin.Context) {
	// 检查管理员权限
	if !c.checkAdminPermission(ctx) {
		return
	}

	// 获取分类ID
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.BadRequest(ctx, "无效的分类ID")
		return
	}

	var req UpdateFeedbackCategoryRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, "参数错误: "+err.Error())
		return
	}

	// 查找分类
	var category models.FeedbackCategory
	if err := c.DB.First(&category, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "分类不存在")
		} else {
			response.InternalServerError(ctx, "查询分类失败")
		}
		return
	}

	// 检查分类名称是否已存在（排除当前分类）
	var existingCategory models.FeedbackCategory
	if err := c.DB.Where("name = ? AND id != ?", req.Name, id).First(&existingCategory).Error; err == nil {
		response.BadRequest(ctx, "分类名称已存在")
		return
	}

	// 更新分类信息
	category.Name = req.Name
	category.Description = req.Description
	category.Icon = req.Icon
	category.SortOrder = req.SortOrder
	category.IsActive = req.IsActive

	if err := c.DB.Save(&category).Error; err != nil {
		response.InternalServerError(ctx, "更新分类失败")
		return
	}

	resp := convertToFeedbackCategoryResponse(category)
	response.Success(ctx, resp)
}

// DeleteFeedbackCategory 删除反馈分类（管理员）
// @Summary 删除反馈分类
// @Description 删除反馈分类（管理员权限）
// @Tags 意见反馈管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "分类ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.ErrorResponse "参数错误"
// @Failure 401 {object} response.ErrorResponse "未授权"
// @Failure 403 {object} response.ErrorResponse "无权限"
// @Failure 404 {object} response.ErrorResponse "分类不存在"
// @Failure 409 {object} response.ErrorResponse "分类正在使用中"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/admin/feedback/categories/{id} [delete]
func (c *FeedbackController) DeleteFeedbackCategory(ctx *gin.Context) {
	// 检查管理员权限
	if !c.checkAdminPermission(ctx) {
		return
	}

	// 获取分类ID
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.BadRequest(ctx, "无效的分类ID")
		return
	}

	// 查找分类
	var category models.FeedbackCategory
	if err := c.DB.First(&category, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "分类不存在")
		} else {
			response.InternalServerError(ctx, "查询分类失败")
		}
		return
	}

	// 检查是否有反馈使用此分类
	var feedbackCount int64
	if err := c.DB.Model(&models.Feedback{}).Where("category_id = ?", id).Count(&feedbackCount).Error; err != nil {
		response.InternalServerError(ctx, "检查分类使用情况失败")
		return
	}

	if feedbackCount > 0 {
		response.Conflict(ctx, "该分类正在使用中，无法删除")
		return
	}

	// 删除分类
	if err := c.DB.Delete(&category).Error; err != nil {
		response.InternalServerError(ctx, "删除分类失败")
		return
	}

	response.Success(ctx, nil)
}

// GetFeedbackStats 获取反馈统计（管理员）
// @Summary 获取反馈统计
// @Description 获取反馈统计信息（管理员权限）
// @Tags 意见反馈管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} response.Response{data=FeedbackStatsResponse} "获取成功"
// @Failure 401 {object} response.ErrorResponse "未授权"
// @Failure 403 {object} response.ErrorResponse "无权限"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/admin/feedback/stats [get]
func (c *FeedbackController) GetFeedbackStats(ctx *gin.Context) {
	// 检查管理员权限
	if !c.checkAdminPermission(ctx) {
		return
	}

	// 总反馈数
	var totalCount int64
	c.DB.Model(&models.Feedback{}).Count(&totalCount)

	// 按状态统计
	type StatusCount struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}

	var statusStats []StatusCount
	c.DB.Model(&models.Feedback{}).
		Select("status, COUNT(*) as count").
		Group("status").
		Scan(&statusStats)

	// 按类型统计
	type TypeCount struct {
		Type  string `json:"type"`
		Count int64  `json:"count"`
	}

	var typeStats []TypeCount
	c.DB.Model(&models.Feedback{}).
		Select("type, COUNT(*) as count").
		Group("type").
		Scan(&typeStats)

	// 按优先级统计
	type PriorityCount struct {
		Priority string `json:"priority"`
		Count    int64  `json:"count"`
	}

	var priorityStats []PriorityCount
	c.DB.Model(&models.Feedback{}).
		Select("priority, COUNT(*) as count").
		Group("priority").
		Scan(&priorityStats)

	// 最近7天的反馈趋势
	type DailyCount struct {
		Date  string `json:"date"`
		Count int64  `json:"count"`
	}

	var dailyStats []DailyCount
	c.DB.Model(&models.Feedback{}).
		Select("DATE(created_at) as date, COUNT(*) as count").
		Where("created_at >= ?", time.Now().AddDate(0, 0, -7)).
		Group("DATE(created_at)").
		Order("date ASC").
		Scan(&dailyStats)

	stats := FeedbackStatsResponse{
		TotalCount:    totalCount,
		StatusStats:   statusStats,
		TypeStats:     typeStats,
		PriorityStats: priorityStats,
		DailyStats:    dailyStats,
	}

	response.Success(ctx, stats)
}

// ========== 辅助方法 ==========

// checkAdminPermission 检查管理员权限
func (c *FeedbackController) checkAdminPermission(ctx *gin.Context) bool {
	userRole, exists := ctx.Get("role")
	if !exists || userRole != "admin" {
		response.Forbidden(ctx, "需要管理员权限")
		return false
	}
	return true
}

// 转换函数

// convertToFeedbackResponse 转换为反馈响应格式
func convertToFeedbackResponse(feedback models.Feedback) models.FeedbackResponse {
	resp := models.FeedbackResponse{
		ID:             feedback.ID,
		Title:          feedback.Title,
		Content:        feedback.Content,
		Type:           feedback.Type,
		Status:         feedback.Status,
		Priority:       feedback.Priority,
		ContactEmail:   feedback.ContactEmail,
		ContactPhone:   feedback.ContactPhone,
		DeviceInfo:     feedback.DeviceInfo,
		SystemVersion:  feedback.SystemVersion,
		AppVersion:     feedback.AppVersion,
		Resolution:     feedback.Resolution,
		ResolutionNote: feedback.ResolutionNote,
		Rating:         feedback.Rating,
		RatingNote:     feedback.RatingNote,
		CreatedAt:      feedback.CreatedAt,
		UpdatedAt:      feedback.UpdatedAt,
		ResolvedAt:     feedback.ResolvedAt,
		RatedAt:        feedback.RatedAt,
	}

	// 设置用户信息
	if feedback.User.ID != 0 {
		resp.User.ID = feedback.User.ID
		resp.User.Username = feedback.User.Username
		resp.User.Email = feedback.User.Email
		resp.User.Avatar = feedback.User.Avatar
	}

	// 设置分类信息
	if feedback.Category != nil {
		resp.Category = &struct {
			ID          uint   `json:"id"`
			Name        string `json:"name"`
			Description string `json:"description"`
			Color       string `json:"color"`
			Icon        string `json:"icon"`
		}{
			ID:          feedback.Category.ID,
			Name:        feedback.Category.Name,
			Description: feedback.Category.Description,
			Color:       feedback.Category.Color,
			Icon:        feedback.Category.Icon,
		}
	}

	// 设置应用信息
	if feedback.App != nil {
		resp.App = &struct {
			ID          uint   `json:"id"`
			Name        string `json:"name"`
			PackageName string `json:"package_name"`
			Icon        string `json:"icon"`
		}{
			ID:          feedback.App.ID,
			Name:        feedback.App.Name,
			PackageName: feedback.App.Package,
			Icon:        feedback.App.Icon,
		}
	}

	return resp
}

// convertToReplyResponse 转换为回复响应格式
func convertToReplyResponse(reply models.FeedbackReply) models.FeedbackReplyResponse {
	return models.FeedbackReplyResponse{
		ID:         reply.ID,
		Content:    reply.Content,
		ReplyType:  reply.ReplyType,
		IsInternal: reply.IsInternal,
		CreatedAt:  reply.CreatedAt,
	}
	// TODO: 需要手动设置Replier和Attachments字段
	// 这些字段需要根据实际的关联数据进行设置
}

// convertToFeedbackCategoryResponse 转换为反馈分类响应格式
func convertToFeedbackCategoryResponse(category models.FeedbackCategory) models.FeedbackCategoryResponse {
	return models.FeedbackCategoryResponse{
		ID:          category.ID,
		Name:        category.Name,
		Description: category.Description,
		Color:       category.Color,
		Icon:        category.Icon,
		SortOrder:   category.SortOrder,
		IsActive:    category.IsActive,
		CreatedAt:   category.CreatedAt,
		UpdatedAt:   category.UpdatedAt,
	}
}

// 请求结构体

// CreateFeedbackRequest 创建反馈请求
type CreateFeedbackRequest struct {
	CategoryID  *uint  `json:"category_id"`
	Type        string `json:"type" binding:"required,oneof=bug feature suggestion complaint other"`
	Title       string `json:"title" binding:"required,max=200"`
	Content     string `json:"content" binding:"required"`
	ContactInfo string `json:"contact_info" binding:"max=255"`
	AppID       *uint  `json:"app_id"`
	AppVersion  string `json:"app_version" binding:"max=50"`
	DeviceInfo  string `json:"device_info" binding:"max=500"`
	Screenshots string `json:"screenshots"`
}

// UpdateFeedbackStatusRequest 更新反馈状态请求
type UpdateFeedbackStatusRequest struct {
	Status     string `json:"status" binding:"required,oneof=pending processing replied resolved closed"`
	Priority   string `json:"priority" binding:"required,oneof=low normal high urgent"`
	AdminNotes string `json:"admin_notes" binding:"max=1000"`
}

// ReplyFeedbackRequest 回复反馈请求
type ReplyFeedbackRequest struct {
	Content  string `json:"content" binding:"required"`
	IsPublic bool   `json:"is_public"`
}

// CreateFeedbackCategoryRequest 创建反馈分类请求
type CreateFeedbackCategoryRequest struct {
	Name        string `json:"name" binding:"required,max=100"`
	Description string `json:"description" binding:"max=500"`
	Icon        string `json:"icon" binding:"max=255"`
	SortOrder   int    `json:"sort_order"`
	IsActive    bool   `json:"is_active"`
}

// UpdateFeedbackCategoryRequest 更新反馈分类请求
type UpdateFeedbackCategoryRequest struct {
	Name        string `json:"name" binding:"required,max=100"`
	Description string `json:"description" binding:"max=500"`
	Icon        string `json:"icon" binding:"max=255"`
	SortOrder   int    `json:"sort_order"`
	IsActive    bool   `json:"is_active"`
}

// 响应结构体

// FeedbackStatsResponse 反馈统计响应
type FeedbackStatsResponse struct {
	TotalCount    int64       `json:"total_count"`
	StatusStats   interface{} `json:"status_stats"`
	TypeStats     interface{} `json:"type_stats"`
	PriorityStats interface{} `json:"priority_stats"`
	DailyStats    interface{} `json:"daily_stats"`
}
