import { Constants } from '../utils/Constants';
import { ApiService } from '../services/ApiService';
import { LoadingView, LoadingState } from '../components/LoadingView';
import { router } from '@kit.ArkUI';
import { preferences } from '@kit.ArkData';
import { hilog } from '@kit.PerformanceAnalysisKit';
// getContext is deprecated, use this.getUIContext().getHostContext() instead

/**
 * 通知设置数据模型
 */
interface NotificationSettings extends Record<string, boolean> {
  app_updates: boolean;
  security_alerts: boolean;
  promotional: boolean;
  reviews: boolean;
  downloads: boolean;
  system: boolean;
  email_notifications: boolean;
  push_notifications: boolean;
}

/**
 * 通知设置响应数据模型
 */
interface NotificationSettingsData {
  app_updates?: boolean;
  security_alerts?: boolean;
  promotional?: boolean;
  reviews?: boolean;
  downloads?: boolean;
  system?: boolean;
  email_notifications?: boolean;
  push_notifications?: boolean;
}

/**
 * 通知设置页面
 */
@Entry
@Component
struct NotificationSettingsPage {
  @State settings: NotificationSettings = {
    app_updates: true,
    security_alerts: true,
    promotional: false,
    reviews: true,
    downloads: true,
    system: true,
    email_notifications: false,
    push_notifications: true
  };
  @State loadingState: LoadingState = LoadingState.LOADING;
  @State isSaving: boolean = false;

  private apiService = ApiService.getInstance();

  aboutToAppear() {
    this.checkAndSetAuthToken().then(() => {
      this.loadSettings();
    });
  }

  /**
   * 检查并设置认证token
   */
  private async checkAndSetAuthToken(): Promise<void> {
    try {
      const context = this.getUIContext().getHostContext();
      const dataPreferences = preferences.getPreferencesSync(context, { name: 'user_data' });
      const token: preferences.ValueType = dataPreferences.getSync('token', '');
      
      if (token && typeof token === 'string' && token.length > 0) {
        this.apiService.setAuthToken(token);
      } else {
        // 没有token，跳转到登录页面
        this.getUIContext().getRouter().pushUrl({
          url: 'pages/LoginPage'
        });
      }
    } catch (error) {
        hilog.error(0x0000, 'NotificationSettings', '检查登录状态失败: %{public}s', JSON.stringify(error));
      this.getUIContext().getRouter().pushUrl({
        url: 'pages/LoginPage'
      });
    }
  }

  /**
   * 加载通知设置
   */
  private async loadSettings() {
    try {
      this.loadingState = LoadingState.LOADING;
      
      const response = await this.apiService.getNotificationSettings();
      if (response.code === 200 && response.data) {
        const responseData = response.data as NotificationSettingsData;
        const newSettings: NotificationSettings = {
          app_updates: responseData.app_updates ?? this.settings.app_updates,
          security_alerts: responseData.security_alerts ?? this.settings.security_alerts,
          promotional: responseData.promotional ?? this.settings.promotional,
          reviews: responseData.reviews ?? this.settings.reviews,
          downloads: responseData.downloads ?? this.settings.downloads,
          system: responseData.system ?? this.settings.system,
          email_notifications: responseData.email_notifications ?? this.settings.email_notifications,
          push_notifications: responseData.push_notifications ?? this.settings.push_notifications
        };
        this.settings = newSettings;
        this.loadingState = LoadingState.SUCCESS;
      } else {
        this.loadingState = LoadingState.ERROR;
      }
    } catch (error) {
        hilog.error(0x0000, 'NotificationSettings', '加载通知设置失败: %{public}s', JSON.stringify(error));
      this.loadingState = LoadingState.ERROR;
    }
  }

  /**
   * 保存通知设置
   */
  private async saveSettings() {
    try {
      this.isSaving = true;
      
      const response = await this.apiService.updateNotificationSettings(this.settings);
      if (response.code === 200) {
        // 显示成功提示
        hilog.info(0x0000, 'NotificationSettings', '通知设置保存成功');
      } else {
          hilog.error(0x0000, 'NotificationSettings', '保存通知设置失败: %{public}s', response.message);
        }
      } catch (error) {
        hilog.error(0x0000, 'NotificationSettings', '保存通知设置失败: %{public}s', JSON.stringify(error));
    } finally {
      this.isSaving = false;
    }
  }

  /**
   * 获取设置值
   */
  private getSettingValue(key: keyof NotificationSettings): boolean {
    switch (key) {
      case 'app_updates':
        return this.settings.app_updates;
      case 'security_alerts':
        return this.settings.security_alerts;
      case 'promotional':
        return this.settings.promotional;
      case 'reviews':
        return this.settings.reviews;
      case 'downloads':
        return this.settings.downloads;
      case 'system':
        return this.settings.system;
      case 'email_notifications':
        return this.settings.email_notifications;
      case 'push_notifications':
        return this.settings.push_notifications;
      default:
        return false;
    }
  }

  /**
   * 切换设置项
   */
  private toggleSetting(key: keyof NotificationSettings) {
    const newSettings: NotificationSettings = {
      app_updates: this.settings.app_updates,
      security_alerts: this.settings.security_alerts,
      promotional: this.settings.promotional,
      reviews: this.settings.reviews,
      downloads: this.settings.downloads,
      system: this.settings.system,
      email_notifications: this.settings.email_notifications,
      push_notifications: this.settings.push_notifications
    };
    
    // 使用switch语句替代索引访问
    switch (key) {
      case 'app_updates':
        newSettings.app_updates = !this.settings.app_updates;
        break;
      case 'security_alerts':
        newSettings.security_alerts = !this.settings.security_alerts;
        break;
      case 'promotional':
        newSettings.promotional = !this.settings.promotional;
        break;
      case 'reviews':
        newSettings.reviews = !this.settings.reviews;
        break;
      case 'downloads':
        newSettings.downloads = !this.settings.downloads;
        break;
      case 'system':
        newSettings.system = !this.settings.system;
        break;
      case 'email_notifications':
        newSettings.email_notifications = !this.settings.email_notifications;
        break;
      case 'push_notifications':
        newSettings.push_notifications = !this.settings.push_notifications;
        break;
    }
    
    this.settings = newSettings;
    
    // 自动保存
    this.saveSettings();
  }

  /**
   * 构建设置项
   */
  @Builder
  buildSettingItem(title: string, description: string, key: keyof NotificationSettings) {
    Row() {
      Column() {
        Text(title)
          .fontSize(Constants.FONT_SIZE.NORMAL)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .fontWeight(FontWeight.Medium)
          .margin({ bottom: 4 })
        
        Text(description)
          .fontSize(Constants.FONT_SIZE.SMALL)
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
          .maxLines(2)
          .textOverflow({ overflow: TextOverflow.Ellipsis })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start)
      
      Toggle({ type: ToggleType.Switch, isOn: this.getSettingValue(key) })
        .selectedColor(Constants.COLORS.PRIMARY)
        .switchPointColor(Constants.COLORS.WHITE)
        .onChange((isOn: boolean) => {
          this.toggleSetting(key);
        })
    }
    .width('100%')
    .padding(16)
    .backgroundColor(Constants.COLORS.WHITE)
  }

  /**
   * 构建分组标题
   */
  @Builder
  buildGroupTitle(title: string) {
    Text(title)
      .fontSize(Constants.FONT_SIZE.SMALL)
      .fontColor(Constants.COLORS.TEXT_HINT)
      .fontWeight(FontWeight.Medium)
      .padding({ left: 16, right: 16, top: 16, bottom: 8 })
  }

  build() {
    Column() {
      // 标题栏
      Row() {
        Button() {
          Image($r('app.media.ic_back'))
            .width(24)
            .height(24)
            .fillColor(Constants.COLORS.TEXT_PRIMARY)
        }
        .width(40)
        .height(40)
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          this.getUIContext().getRouter().back();
        })

        Text('通知设置')
          .fontSize(Constants.FONT_SIZE.LARGE)
          .fontWeight(FontWeight.Medium)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        // 占位符保持居中
        Row()
          .width(40)
          .height(40)
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor(Constants.COLORS.WHITE)

      // 内容区域
      if (this.loadingState === LoadingState.LOADING) {
        LoadingView()
          .layoutWeight(1)
      } else if (this.loadingState === LoadingState.ERROR) {
        Column() {
          Image($r('app.media.ic_error'))
            .width(64)
            .height(64)
            .fillColor(Constants.COLORS.TEXT_HINT)
            .margin({ bottom: 16 })
          
          Text('加载失败')
            .fontSize(Constants.FONT_SIZE.MEDIUM)
            .fontColor(Constants.COLORS.TEXT_SECONDARY)
            .margin({ bottom: 16 })
          
          Button('重试')
            .fontSize(Constants.FONT_SIZE.NORMAL)
            .fontColor(Constants.COLORS.WHITE)
            .backgroundColor(Constants.COLORS.PRIMARY)
            .borderRadius(8)
            .padding({ left: 24, right: 24, top: 8, bottom: 8 })
            .onClick(() => {
              this.loadSettings();
            })
        }
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
      } else {
        Scroll() {
          Column() {
            // 推送通知设置
            this.buildGroupTitle('推送通知')
            
            Column() {
              this.buildSettingItem(
                '启用推送通知',
                '接收应用推送的通知消息',
                'push_notifications'
              )
              
              Divider()
                .color(Constants.COLORS.BORDER)
                .margin({ left: 16, right: 16 })
              
              this.buildSettingItem(
                '邮件通知',
                '通过邮件接收重要通知',
                'email_notifications'
              )
            }
            .backgroundColor(Constants.COLORS.WHITE)
            .borderRadius(12)
            .margin({ left: 16, right: 16, bottom: 16 })
            
            // 应用相关通知
            this.buildGroupTitle('应用通知')
            
            Column() {
              this.buildSettingItem(
                '应用更新',
                '当有新版本发布时通知我',
                'app_updates'
              )
              
              Divider()
                .color(Constants.COLORS.BORDER)
                .margin({ left: 16, right: 16 })
              
              this.buildSettingItem(
                '下载完成',
                '应用下载完成后通知我',
                'downloads'
              )
              
              Divider()
                .color(Constants.COLORS.BORDER)
                .margin({ left: 16, right: 16 })
              
              this.buildSettingItem(
                '评论回复',
                '当有人回复我的评论时通知我',
                'reviews'
              )
            }
            .backgroundColor(Constants.COLORS.WHITE)
            .borderRadius(12)
            .margin({ left: 16, right: 16, bottom: 16 })
            
            // 系统通知
            this.buildGroupTitle('系统通知')
            
            Column() {
              this.buildSettingItem(
                '安全提醒',
                '账户安全相关的重要提醒',
                'security_alerts'
              )
              
              Divider()
                .color(Constants.COLORS.BORDER)
                .margin({ left: 16, right: 16 })
              
              this.buildSettingItem(
                '系统消息',
                '系统维护、公告等消息',
                'system'
              )
              
              Divider()
                .color(Constants.COLORS.BORDER)
                .margin({ left: 16, right: 16 })
              
              this.buildSettingItem(
                '推广信息',
                '新功能介绍、活动推广等信息',
                'promotional'
              )
            }
            .backgroundColor(Constants.COLORS.WHITE)
            .borderRadius(12)
            .margin({ left: 16, right: 16, bottom: 16 })
            
            // 说明文字
            Text('关闭某些通知可能会影响您及时获取重要信息，请谨慎操作。')
              .fontSize(Constants.FONT_SIZE.SMALL)
              .fontColor(Constants.COLORS.TEXT_HINT)
              .textAlign(TextAlign.Center)
              .padding({ left: 32, right: 32, top: 16, bottom: 32 })
          }
        }
        .layoutWeight(1)
        .scrollBar(BarState.Off)
      }
      
      // 保存状态指示器
      if (this.isSaving) {
        Row() {
          LoadingProgress()
            .width(16)
            .height(16)
            .margin({ right: 8 })
          
          Text('保存中...')
            .fontSize(Constants.FONT_SIZE.SMALL)
            .fontColor(Constants.COLORS.TEXT_HINT)
        }
        .justifyContent(FlexAlign.Center)
        .padding(8)
        .backgroundColor(Constants.COLORS.BACKGROUND_LIGHT)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Constants.COLORS.BACKGROUND)
  }
}