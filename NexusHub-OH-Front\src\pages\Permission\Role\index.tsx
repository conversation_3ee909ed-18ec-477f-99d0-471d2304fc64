import { PageContainer } from '@ant-design/pro-components';
import { Card, Table, Button, Space, Input, Modal, Form, message, Popconfirm, Tooltip, Tree, Tabs, Switch, Tag, Select } from 'antd';
import { SearchOutlined, PlusOutlined, EditOutlined, DeleteOutlined, ReloadOutlined, LockOutlined, UnlockOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { useRequest } from '@umijs/max';
import type { ColumnsType } from 'antd/es/table';
import type { DataNode } from 'antd/es/tree';

const { TabPane } = Tabs;

interface RoleItem {
  id: string;
  name: string;
  code: string;
  description: string;
  status: 'active' | 'inactive';
  userCount: number;
  createdAt: string;
  updatedAt: string;
}

interface PermissionItem {
  id: string;
  name: string;
  code: string;
  type: 'menu' | 'operation' | 'data';
  status: 'active' | 'inactive';
}

// 模拟数据获取函数
const fetchRoles = async (params: any) => {
  console.log('Fetching roles with params:', params);
  
  // 模拟数据
  const mockData: RoleItem[] = [
    {
      id: 'role001',
      name: '超级管理员',
      code: 'SUPER_ADMIN',
      description: '拥有系统所有权限',
      status: 'active',
      userCount: 3,
      createdAt: '2023-01-01',
      updatedAt: '2023-11-20',
    },
    {
      id: 'role002',
      name: '内容管理员',
      code: 'CONTENT_ADMIN',
      description: '负责内容审核和管理',
      status: 'active',
      userCount: 8,
      createdAt: '2023-01-05',
      updatedAt: '2023-11-15',
    },
    {
      id: 'role003',
      name: '用户管理员',
      code: 'USER_ADMIN',
      description: '负责用户管理和支持',
      status: 'active',
      userCount: 5,
      createdAt: '2023-01-10',
      updatedAt: '2023-11-10',
    },
    {
      id: 'role004',
      name: '应用审核员',
      code: 'APP_REVIEWER',
      description: '负责应用审核',
      status: 'active',
      userCount: 12,
      createdAt: '2023-01-15',
      updatedAt: '2023-11-05',
    },
    {
      id: 'role005',
      name: '数据分析师',
      code: 'DATA_ANALYST',
      description: '负责数据分析和报表',
      status: 'active',
      userCount: 4,
      createdAt: '2023-01-20',
      updatedAt: '2023-10-30',
    },
    {
      id: 'role006',
      name: '财务管理员',
      code: 'FINANCE_ADMIN',
      description: '负责财务管理',
      status: 'active',
      userCount: 3,
      createdAt: '2023-01-25',
      updatedAt: '2023-10-25',
    },
    {
      id: 'role007',
      name: '客服人员',
      code: 'CUSTOMER_SERVICE',
      description: '负责客户服务和支持',
      status: 'active',
      userCount: 15,
      createdAt: '2023-02-01',
      updatedAt: '2023-10-20',
    },
    {
      id: 'role008',
      name: '营销管理员',
      code: 'MARKETING_ADMIN',
      description: '负责营销活动和推广',
      status: 'active',
      userCount: 6,
      createdAt: '2023-02-05',
      updatedAt: '2023-10-15',
    },
    {
      id: 'role009',
      name: '开发者关系',
      code: 'DEVELOPER_RELATION',
      description: '负责开发者关系管理',
      status: 'active',
      userCount: 4,
      createdAt: '2023-02-10',
      updatedAt: '2023-10-10',
    },
    {
      id: 'role010',
      name: '测试角色',
      code: 'TEST_ROLE',
      description: '用于测试的角色',
      status: 'inactive',
      userCount: 0,
      createdAt: '2023-02-15',
      updatedAt: '2023-10-05',
    },
  ];

  // 根据状态过滤
  const statusFilteredData = params.status ? mockData.filter(item => item.status === params.status) : mockData;
  
  // 根据关键词过滤
  const keywordFilteredData = params.keyword
    ? statusFilteredData.filter(item => 
        item.name.toLowerCase().includes(params.keyword.toLowerCase()) ||
        item.code.toLowerCase().includes(params.keyword.toLowerCase()) ||
        item.description.toLowerCase().includes(params.keyword.toLowerCase())
      )
    : statusFilteredData;

  return { data: keywordFilteredData, total: keywordFilteredData.length };
};

// 模拟获取权限树
const fetchPermissionTree = async () => {
  const permissionTree: DataNode[] = [
    {
      title: '系统管理',
      key: 'system',
      children: [
        {
          title: '用户管理',
          key: 'system:user',
          children: [
            { title: '查看用户', key: 'system:user:view' },
            { title: '创建用户', key: 'system:user:create' },
            { title: '编辑用户', key: 'system:user:edit' },
            { title: '删除用户', key: 'system:user:delete' },
          ],
        },
        {
          title: '角色管理',
          key: 'system:role',
          children: [
            { title: '查看角色', key: 'system:role:view' },
            { title: '创建角色', key: 'system:role:create' },
            { title: '编辑角色', key: 'system:role:edit' },
            { title: '删除角色', key: 'system:role:delete' },
          ],
        },
        {
          title: '权限管理',
          key: 'system:permission',
          children: [
            { title: '查看权限', key: 'system:permission:view' },
            { title: '分配权限', key: 'system:permission:assign' },
          ],
        },
      ],
    },
    {
      title: '应用管理',
      key: 'app',
      children: [
        {
          title: '应用列表',
          key: 'app:list',
          children: [
            { title: '查看应用', key: 'app:list:view' },
            { title: '编辑应用', key: 'app:list:edit' },
            { title: '删除应用', key: 'app:list:delete' },
          ],
        },
        {
          title: '应用审核',
          key: 'app:audit',
          children: [
            { title: '查看审核', key: 'app:audit:view' },
            { title: '审核通过', key: 'app:audit:approve' },
            { title: '审核拒绝', key: 'app:audit:reject' },
          ],
        },
      ],
    },
    {
      title: '内容管理',
      key: 'content',
      children: [
        {
          title: '推荐内容',
          key: 'content:featured',
          children: [
            { title: '查看推荐', key: 'content:featured:view' },
            { title: '添加推荐', key: 'content:featured:add' },
            { title: '移除推荐', key: 'content:featured:remove' },
          ],
        },
        {
          title: '活动管理',
          key: 'content:activity',
          children: [
            { title: '查看活动', key: 'content:activity:view' },
            { title: '创建活动', key: 'content:activity:create' },
            { title: '编辑活动', key: 'content:activity:edit' },
            { title: '删除活动', key: 'content:activity:delete' },
          ],
        },
        {
          title: '帮助中心',
          key: 'content:help',
          children: [
            { title: '查看帮助', key: 'content:help:view' },
            { title: '创建帮助', key: 'content:help:create' },
            { title: '编辑帮助', key: 'content:help:edit' },
            { title: '删除帮助', key: 'content:help:delete' },
          ],
        },
      ],
    },
    {
      title: '用户管理',
      key: 'user',
      children: [
        {
          title: '用户列表',
          key: 'user:list',
          children: [
            { title: '查看用户', key: 'user:list:view' },
            { title: '编辑用户', key: 'user:list:edit' },
            { title: '禁用用户', key: 'user:list:disable' },
          ],
        },
        {
          title: '开发者管理',
          key: 'user:developer',
          children: [
            { title: '查看开发者', key: 'user:developer:view' },
            { title: '审核开发者', key: 'user:developer:audit' },
          ],
        },
      ],
    },
    {
      title: '评论管理',
      key: 'review',
      children: [
        {
          title: '评论列表',
          key: 'review:list',
          children: [
            { title: '查看评论', key: 'review:list:view' },
            { title: '删除评论', key: 'review:list:delete' },
          ],
        },
        {
          title: '评论审核',
          key: 'review:audit',
          children: [
            { title: '查看审核', key: 'review:audit:view' },
            { title: '审核通过', key: 'review:audit:approve' },
            { title: '审核拒绝', key: 'review:audit:reject' },
          ],
        },
      ],
    },
    {
      title: '统计分析',
      key: 'statistics',
      children: [
        { title: '应用统计', key: 'statistics:app' },
        { title: '销售统计', key: 'statistics:sales' },
        { title: '用户统计', key: 'statistics:user' },
      ],
    },
    {
      title: '系统设置',
      key: 'settings',
      children: [
        { title: '参数配置', key: 'settings:params' },
        { title: '日志管理', key: 'settings:logs' },
        { title: '标签管理', key: 'settings:tags' },
      ],
    },
  ];

  return permissionTree;
};

// 模拟获取角色权限
const fetchRolePermissions = async (roleId: string) => {
  // 模拟不同角色的权限
  const permissionMap: Record<string, string[]> = {
    'role001': [
      'system', 'app', 'content', 'user', 'review', 'statistics', 'settings',
      // 所有子权限...
    ],
    'role002': [
      'content', 'content:featured', 'content:featured:view', 'content:featured:add', 'content:featured:remove',
      'content:activity', 'content:activity:view', 'content:activity:create', 'content:activity:edit', 'content:activity:delete',
      'content:help', 'content:help:view', 'content:help:create', 'content:help:edit', 'content:help:delete',
    ],
    'role003': [
      'user', 'user:list', 'user:list:view', 'user:list:edit', 'user:list:disable',
      'user:developer', 'user:developer:view', 'user:developer:audit',
    ],
    'role004': [
      'app:audit', 'app:audit:view', 'app:audit:approve', 'app:audit:reject',
    ],
    'role005': [
      'statistics', 'statistics:app', 'statistics:sales', 'statistics:user',
    ],
  };

  return permissionMap[roleId] || [];
};

const RoleManagement: React.FC = () => {
  const [searchParams, setSearchParams] = useState({});
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [permissionModalVisible, setPermissionModalVisible] = useState(false);
  const [editingRole, setEditingRole] = useState<RoleItem | null>(null);
  const [currentRoleId, setCurrentRoleId] = useState<string>('');
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
  const [form] = Form.useForm();

  const { data, loading, refresh } = useRequest(() => fetchRoles(searchParams), {
    refreshDeps: [searchParams],
  });

  const { data: permissionTree } = useRequest(fetchPermissionTree, {
    cacheKey: 'permissionTree',
  });

  const { data: rolePermissions, run: fetchPermissions } = useRequest(
    (roleId: string) => fetchRolePermissions(roleId),
    {
      manual: true,
    }
  );

  const handleSearch = (values: any) => {
    setSearchParams(values);
  };

  const showAddModal = () => {
    setEditingRole(null);
    form.resetFields();
    setModalVisible(true);
  };

  const showEditModal = (record: RoleItem) => {
    setEditingRole(record);
    form.setFieldsValue({
      name: record.name,
      code: record.code,
      description: record.description,
      status: record.status === 'active',
    });
    setModalVisible(true);
  };

  const showPermissionModal = (record: RoleItem) => {
    setCurrentRoleId(record.id);
    fetchPermissions(record.id);
    setPermissionModalVisible(true);
  };

  const handleModalOk = () => {
    form.validateFields().then(values => {
      if (editingRole) {
        message.success(`角色 "${values.name}" 已更新`);
      } else {
        message.success(`角色 "${values.name}" 已创建`);
      }
      setModalVisible(false);
      refresh();
    });
  };

  const handlePermissionModalOk = () => {
    message.success('权限已更新');
    setPermissionModalVisible(false);
  };

  const handleDelete = (id: string, name: string) => {
    message.success(`角色 "${name}" 已删除`);
    refresh();
  };

  const handleBatchDelete = () => {
    message.success(`已删除 ${selectedRowKeys.length} 个角色`);
    setSelectedRowKeys([]);
    refresh();
  };

  const handleStatusChange = (id: string, checked: boolean) => {
    message.success(`角色状态已${checked ? '启用' : '禁用'}`);
    refresh();
  };

  const onPermissionCheck = (checkedKeysValue: React.Key[]) => {
    setCheckedKeys(checkedKeysValue);
  };

  const columns: ColumnsType<RoleItem> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
    },
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: '角色编码',
      dataIndex: 'code',
      key: 'code',
      width: 150,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string, record: RoleItem) => (
        <Switch
          checkedChildren="启用"
          unCheckedChildren="禁用"
          checked={status === 'active'}
          onChange={(checked) => handleStatusChange(record.id, checked)}
        />
      ),
    },
    {
      title: '用户数',
      dataIndex: 'userCount',
      key: 'userCount',
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 120,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              onClick={() => showEditModal(record)}
            />
          </Tooltip>
          <Tooltip title="权限设置">
            <Button 
              type="text" 
              icon={<LockOutlined />} 
              onClick={() => showPermissionModal(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除这个角色吗？"
              onConfirm={() => handleDelete(record.id, record.name)}
              okText="确定"
              cancelText="取消"
            >
              <Button 
                type="text" 
                danger 
                icon={<DeleteOutlined />} 
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer
      header={{
        title: '角色管理',
        subTitle: '管理系统角色和权限',
      }}
    >
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Form layout="inline" onFinish={handleSearch}>
            <Form.Item name="keyword" label="关键词">
              <Input 
                placeholder="搜索角色名称/编码/描述" 
                prefix={<SearchOutlined />}
                style={{ width: 250 }}
              />
            </Form.Item>
            <Form.Item name="status" label="状态">
              <Select 
                placeholder="选择状态" 
                style={{ width: 120 }}
                allowClear
              >
                <Select.Option value="active">启用</Select.Option>
                <Select.Option value="inactive">禁用</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                搜索
              </Button>
            </Form.Item>
            <Form.Item>
              <Button onClick={() => setSearchParams({})}>
                重置
              </Button>
            </Form.Item>
          </Form>
        </div>

        <div style={{ marginBottom: 16 }}>
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={showAddModal}
            style={{ marginRight: 8 }}
          >
            新建角色
          </Button>
          <Popconfirm
            title="确定要删除选中的角色吗？"
            onConfirm={handleBatchDelete}
            okText="确定"
            cancelText="取消"
            disabled={selectedRowKeys.length === 0}
          >
            <Button 
              danger 
              disabled={selectedRowKeys.length === 0}
              style={{ marginRight: 8 }}
            >
              批量删除
            </Button>
          </Popconfirm>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={refresh}
          >
            刷新
          </Button>
          <span style={{ marginLeft: 8 }}>
            {selectedRowKeys.length > 0 ? `已选择 ${selectedRowKeys.length} 项` : ''}
          </span>
        </div>

        <Table
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
          columns={columns}
          dataSource={data?.data}
          rowKey="id"
          loading={loading}
          pagination={{
            total: data?.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
          }}
        />
      </Card>

      {/* 角色编辑模态框 */}
      <Modal
        title={editingRole ? '编辑角色' : '新建角色'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
        destroyOnHidden
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="角色名称"
            rules={[{ required: true, message: '请输入角色名称' }]}
          >
            <Input placeholder="请输入角色名称" />
          </Form.Item>
          <Form.Item
            name="code"
            label="角色编码"
            rules={[{ required: true, message: '请输入角色编码' }]}
          >
            <Input placeholder="请输入角色编码，如ADMIN、USER等" />
          </Form.Item>
          <Form.Item
            name="description"
            label="角色描述"
          >
            <Input.TextArea placeholder="请输入角色描述" rows={3} />
          </Form.Item>
          <Form.Item
            name="status"
            label="状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" defaultChecked />
          </Form.Item>
        </Form>
      </Modal>

      {/* 权限设置模态框 */}
      <Modal
        title="权限设置"
        open={permissionModalVisible}
        onOk={handlePermissionModalOk}
        onCancel={() => setPermissionModalVisible(false)}
        width={700}
        destroyOnHidden
      >
        <Tabs defaultActiveKey="menu">
          <TabPane tab="菜单权限" key="menu">
            <Tree
              checkable
              defaultExpandAll
              checkedKeys={rolePermissions}
              onCheck={onPermissionCheck}
              treeData={permissionTree}
            />
          </TabPane>
          <TabPane tab="数据权限" key="data">
            <div style={{ padding: '20px 0' }}>
              <Form layout="vertical">
                <Form.Item label="数据范围">
                  <Select defaultValue="all">
                    <Select.Option value="all">全部数据</Select.Option>
                    <Select.Option value="department">本部门数据</Select.Option>
                    <Select.Option value="self">本人数据</Select.Option>
                    <Select.Option value="custom">自定义数据</Select.Option>
                  </Select>
                </Form.Item>
                <Form.Item label="可见部门">
                  <Select mode="multiple" placeholder="请选择可见部门" disabled>
                    <Select.Option value="dept1">技术部</Select.Option>
                    <Select.Option value="dept2">产品部</Select.Option>
                    <Select.Option value="dept3">市场部</Select.Option>
                    <Select.Option value="dept4">运营部</Select.Option>
                  </Select>
                </Form.Item>
              </Form>
            </div>
          </TabPane>
        </Tabs>
      </Modal>
    </PageContainer>
  );
};

export default RoleManagement;