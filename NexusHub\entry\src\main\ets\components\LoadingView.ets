import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';

/**
 * 加载状态枚举
 */
export enum LoadingState {
  LOADING = 'loading',
  SUCCESS = 'success',
  ERROR = 'error',
  EMPTY = 'empty'
}

/**
 * 加载视图组件
 */
@Component
export struct LoadingView {
  @Prop state: LoadingState = LoadingState.LOADING;
  @Prop message: string = '';
  @Prop showRetry: boolean = true;
  @Prop emptyIcon: Resource = $r('app.media.ic_empty');
  @Prop errorIcon: Resource = $r('app.media.ic_error');
  onRetry?: () => void;

  private deviceUtils = DeviceUtils.getInstance();

  /**
   * 获取默认消息
   */
  private getDefaultMessage(state: LoadingState): string {
    switch (state) {
      case LoadingState.LOADING:
        return '正在加载...';
      case LoadingState.ERROR:
        return '网络连接异常，请检查网络后重试';
      case LoadingState.EMPTY:
        return '暂无相关内容';
      default:
        return '';
    }
  }

  /**
   * 加载中视图
   */
  @Builder
  private LoadingContent() {
    Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) }) {
      LoadingProgress()
        .width(48)
        .height(48)
        .color(Constants.COLORS.PRIMARY)

      Text(this.message || this.getDefaultMessage(this.state))
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
        .fontColor(Constants.COLORS.TEXT_SECONDARY)
        .textAlign(TextAlign.Center)
    }
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  /**
   * 错误视图
   */
  @Builder
  private ErrorContent() {
    Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) }) {
      Image(this.errorIcon)
        .width(64)
        .height(64)
        .objectFit(ImageFit.Contain)
        .fillColor(Constants.COLORS.ERROR)
        .onError(() => {
          console.error('LoadingView ErrorContent: Failed to load error icon:', this.errorIcon);
        })

      Text(this.message || this.getDefaultMessage(this.state))
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
        .fontColor(Constants.COLORS.TEXT_SECONDARY)
        .textAlign(TextAlign.Center)
        .maxLines(2)

      if (this.showRetry) {
        Button('重试')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .backgroundColor(Constants.COLORS.PRIMARY)
          .fontColor(Constants.COLORS.WHITE)
          .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
          .padding({ left: '24vp', right: '24vp', top: '8vp', bottom: '8vp' })
          .onClick(() => {
            this.onRetry?.();
          })
      }
    }
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  /**
   * 空数据视图
   */
  @Builder
  private EmptyContent() {
    Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) }) {
      Image(this.emptyIcon)
        .width(64)
        .height(64)
        .objectFit(ImageFit.Contain)
        .fillColor(Constants.COLORS.TEXT_HINT)
        .onError(() => {
          console.error('LoadingView EmptyContent: Failed to load empty icon:', this.emptyIcon);
        })

      Text(this.message || this.getDefaultMessage(this.state))
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
        .fontColor(Constants.COLORS.TEXT_SECONDARY)
        .textAlign(TextAlign.Center)
        .maxLines(2)
    }
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  build() {
    Column() {
      if (this.state === LoadingState.LOADING) {
        this.LoadingContent()
      } else if (this.state === LoadingState.ERROR) {
        this.ErrorContent()
      } else if (this.state === LoadingState.EMPTY) {
        this.EmptyContent()
      }
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE))
  }
}

/**
 * 页面加载组件（全屏）
 */
@Component
export struct PageLoading {
  @Prop message: string = '加载中...';
  @Prop bgColor: string = Constants.COLORS.WHITE;

  private deviceUtils = DeviceUtils.getInstance();

  build() {
    Column() {
      LoadingProgress()
        .width(48)
        .height(48)
        .color(Constants.COLORS.PRIMARY)

      Text(this.message)
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
        .fontColor(Constants.COLORS.TEXT_SECONDARY)
        .margin({ top: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) })
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .backgroundColor(this.bgColor)
  }
}

/**
 * 列表加载更多组件
 */
@Component
export struct LoadMoreView {
  @Prop isLoading: boolean = false;
  @Prop hasMore: boolean = true;
  @Prop loadingText: string = '加载中...';
  @Prop noMoreText: string = '没有更多了';
  onLoadMore?: () => void;

  private deviceUtils = DeviceUtils.getInstance();

  build() {
    Row() {
      if (this.isLoading) {
        LoadingProgress()
          .width(16)
          .height(16)
          .color(Constants.COLORS.PRIMARY)
          .margin({ right: 8 })

        Text(this.loadingText)
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
      } else if (this.hasMore) {
        Text('点击加载更多')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.PRIMARY)
          .onClick(() => {
            this.onLoadMore?.();
          })
      } else {
        Text(this.noMoreText)
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.TEXT_HINT)
      }
    }
    .width('100%')
    .height(48)
    .justifyContent(FlexAlign.Center)
    .alignItems(VerticalAlign.Center)
  }
}

/**
 * 下拉刷新组件
 */
@Component
export struct PullToRefresh {
  @State isRefreshing: boolean = false;
  @Prop refreshText: string = '下拉刷新';
  @Prop refreshingText: string = '刷新中...';
  @Prop releaseText: string = '释放刷新';
  onRefresh?: () => void;
  @BuilderParam content: () => void;

  private deviceUtils = DeviceUtils.getInstance();
  private pullDistance: number = 0;
  private readonly REFRESH_THRESHOLD = 80;

  /**
   * 开始刷新
   */
  private startRefresh() {
    if (!this.isRefreshing) {
      this.isRefreshing = true;
      this.onRefresh?.();
    }
  }

  /**
   * 停止刷新
   */
  stopRefresh() {
    this.isRefreshing = false;
  }

  build() {
    Column() {
      // 刷新指示器
      if (this.pullDistance > 0 || this.isRefreshing) {
        Row() {
          if (this.isRefreshing) {
            LoadingProgress()
              .width(16)
              .height(16)
              .color(Constants.COLORS.PRIMARY)
              .margin({ right: 8 })
          }

          Text(this.isRefreshing ? this.refreshingText : 
               (this.pullDistance >= this.REFRESH_THRESHOLD ? this.releaseText : this.refreshText))
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.TEXT_SECONDARY)
        }
        .width('100%')
        .height(Math.min(this.pullDistance, 48))
        .justifyContent(FlexAlign.Center)
        .alignItems(VerticalAlign.Center)
      }

      // 内容区域
      this.content()
    }
    .width('100%')
    .height('100%')
  }
}