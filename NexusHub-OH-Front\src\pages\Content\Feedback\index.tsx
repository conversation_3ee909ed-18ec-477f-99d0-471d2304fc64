import React, { useState, useRef, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  message,
  Drawer,
  Descriptions,
  Typography,
  Row,
  Col,
  Statistic,
  DatePicker,
  Popconfirm,
  Tabs,
  Switch,
  InputNumber,
  ColorPicker,
} from 'antd';
import {
  PlusOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  SearchOutlined,
  FolderOutlined,
  MessageOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import {
  getAdminFeedbackList,
  updateFeedbackStatus,
  replyFeedback,
  getFeedbackStats,
  getFeedbackCategories,
  createFeedbackCategory,
  updateFeedbackCategory,
  deleteFeedbackCategory,
} from '@/services/feedback';

const { TextArea } = Input;
const { Option } = Select;
const { Text } = Typography;
const { TabPane } = Tabs;

interface FeedbackItem {
  id: number;
  title: string;
  content: string;
  type: 'bug' | 'feature' | 'improvement' | 'complaint' | 'other';
  status: 'pending' | 'processing' | 'resolved' | 'closed' | 'rejected';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  user_id: number;
  user_name: string;
  contact_email?: string;
  contact_phone?: string;
  device_info?: string;
  system_version?: string;
  app_version?: string;
  created_at: string;
  updated_at: string;
  resolution?: string;
  replies?: Array<{
    id: number;
    content: string;
    is_public: boolean;
    created_at: string;
    admin_name: string;
  }>;
}

interface FeedbackCategory {
  id: number;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  sort_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface FeedbackStats {
  total_count: number;
  status_stats: Record<string, number>;
  type_stats: Record<string, number>;
  priority_stats: Record<string, number>;
  daily_stats: Array<{
    date: string;
    count: number;
  }>;
}

const FeedbackManagement: React.FC = () => {
  // 添加调试日志
  useEffect(() => {
    console.log('=== 反馈管理页面调试信息 ===');
    console.log('localStorage token:', localStorage.getItem('token'));
    console.log('localStorage logto_access_token:', localStorage.getItem('logto_access_token'));
    console.log('API_BASE_URL:', process.env.REACT_APP_API_BASE_URL);
    console.log('当前页面路径:', window.location.pathname);
  }, []);
  const [activeTab, setActiveTab] = useState('feedback');
  const [detailVisible, setDetailVisible] = useState(false);
  const [replyVisible, setReplyVisible] = useState(false);
  const [statusVisible, setStatusVisible] = useState(false);
  const [categoryVisible, setCategoryVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState<FeedbackCategory | null>(null);
  const [currentFeedback, setCurrentFeedback] = useState<FeedbackItem | null>(null);
  const [replyForm] = Form.useForm();
  const [statusForm] = Form.useForm();
  const [categoryForm] = Form.useForm();
  const actionRef = useRef<ActionType>();
  const categoryActionRef = useRef<ActionType>();

  // 获取统计数据
  const { data: stats, loading: statsLoading } = useRequest(getFeedbackStats);

  const typeMap = {
    bug: { text: 'Bug反馈', color: 'red' },
    feature: { text: '功能建议', color: 'blue' },
    improvement: { text: '改进建议', color: 'green' },
    complaint: { text: '投诉建议', color: 'orange' },
    other: { text: '其他', color: 'default' },
  };

  const statusMap = {
    pending: { text: '待处理', color: 'default' },
    processing: { text: '处理中', color: 'processing' },
    resolved: { text: '已解决', color: 'success' },
    closed: { text: '已关闭', color: 'default' },
    rejected: { text: '已拒绝', color: 'error' },
  };

  const priorityMap = {
    low: { text: '低', color: 'default' },
    normal: { text: '普通', color: 'blue' },
    high: { text: '高', color: 'orange' },
    urgent: { text: '紧急', color: 'red' },
  };

  const handleViewDetail = (record: FeedbackItem) => {
    setCurrentFeedback(record);
    setDetailVisible(true);
  };

  const handleReply = (record: FeedbackItem) => {
    setCurrentFeedback(record);
    replyForm.resetFields();
    setReplyVisible(true);
  };

  const handleUpdateStatus = (record: FeedbackItem) => {
    setCurrentFeedback(record);
    statusForm.setFieldsValue({
      status: record.status,
      priority: record.priority,
      resolution: record.resolution,
    });
    setStatusVisible(true);
  };

  const handleReplySubmit = async () => {
    try {
      const values = await replyForm.validateFields();
      await replyFeedback(currentFeedback!.id.toString(), values);
      message.success('回复成功');
      setReplyVisible(false);
      actionRef.current?.reload();
    } catch (error) {
      message.error('回复失败');
    }
  };

  const handleStatusSubmit = async () => {
    try {
      const values = await statusForm.validateFields();
      await updateFeedbackStatus(currentFeedback!.id.toString(), values);
      message.success('状态更新成功');
      setStatusVisible(false);
      actionRef.current?.reload();
    } catch (error) {
      message.error('状态更新失败');
    }
  };

  // 分类管理相关方法
  const handleCreateCategory = () => {
    setEditingCategory(null);
    categoryForm.resetFields();
    categoryForm.setFieldsValue({ is_active: true, sort_order: 1 });
    setCategoryVisible(true);
  };

  const handleEditCategory = (record: FeedbackCategory) => {
    setEditingCategory(record);
    categoryForm.setFieldsValue({
      name: record.name,
      description: record.description,
      icon: record.icon,
      sort_order: record.sort_order,
      is_active: record.is_active,
    });
    setCategoryVisible(true);
  };

  const handleDeleteCategory = async (id: number) => {
    try {
      await deleteFeedbackCategory(id.toString());
      message.success('删除成功');
      categoryActionRef.current?.reload();
    } catch (error: any) {
      if (error.response?.status === 409) {
        message.error('分类正在使用中，无法删除');
      } else {
        message.error('删除失败');
      }
    }
  };

  const handleCategorySubmit = async () => {
    try {
      const values = await categoryForm.validateFields();
      if (editingCategory) {
        await updateFeedbackCategory(editingCategory.id.toString(), values);
        message.success('更新成功');
      } else {
        await createFeedbackCategory(values);
        message.success('创建成功');
      }
      setCategoryVisible(false);
      categoryActionRef.current?.reload();
    } catch (error) {
      message.error(editingCategory ? '更新失败' : '创建失败');
    }
  };

  const columns: ProColumns<FeedbackItem>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      search: false,
    },
    {
      title: '标题',
      dataIndex: 'title',
      ellipsis: true,
      width: 200,
    },
    {
      title: '类型',
      dataIndex: 'type',
      width: 100,
      valueType: 'select',
      valueEnum: {
        bug: { text: 'Bug反馈' },
        feature: { text: '功能建议' },
        improvement: { text: '改进建议' },
        complaint: { text: '投诉建议' },
        other: { text: '其他' },
      },
      render: (_, record) => (
        <Tag color={typeMap[record.type].color}>
          {typeMap[record.type].text}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      valueType: 'select',
      valueEnum: {
        pending: { text: '待处理' },
        processing: { text: '处理中' },
        resolved: { text: '已解决' },
        closed: { text: '已关闭' },
        rejected: { text: '已拒绝' },
      },
      render: (_, record) => (
        <Tag color={statusMap[record.status].color}>
          {statusMap[record.status].text}
        </Tag>
      ),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      width: 100,
      valueType: 'select',
      valueEnum: {
        low: { text: '低' },
        normal: { text: '普通' },
        high: { text: '高' },
        urgent: { text: '紧急' },
      },
      render: (_, record) => (
        <Tag color={priorityMap[record.priority].color}>
          {priorityMap[record.priority].text}
        </Tag>
      ),
    },
    {
      title: '提交用户',
      dataIndex: 'user_name',
      width: 120,
      search: false,
    },
    {
      title: '联系方式',
      dataIndex: 'contact_email',
      width: 150,
      search: false,
      render: (_, record) => (
        <div>
          {record.contact_email && <div>{record.contact_email}</div>}
          {record.contact_phone && <div>{record.contact_phone}</div>}
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      width: 150,
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 200,
      render: (_, record) => [
        <Button
          key="view"
          type="link"
          size="small"
          icon={<EyeOutlined />}
          onClick={() => handleViewDetail(record)}
        >
          查看
        </Button>,
        <Button
          key="reply"
          type="link"
          size="small"
          icon={<EditOutlined />}
          onClick={() => handleReply(record)}
        >
          回复
        </Button>,
        <Button
          key="status"
          type="link"
          size="small"
          onClick={() => handleUpdateStatus(record)}
        >
          状态
        </Button>,
      ],
    },
  ];

  // 分类管理列配置
  const categoryColumns: ProColumns<FeedbackCategory>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      search: false,
    },
    {
      title: '分类名称',
      dataIndex: 'name',
      width: 150,
    },
    {
      title: '描述',
      dataIndex: 'description',
      width: 200,
      search: false,
      ellipsis: true,
    },
    {
      title: '图标',
      dataIndex: 'icon',
      width: 100,
      search: false,
      render: (text) => text || '-',
    },
    {
      title: '颜色',
      dataIndex: 'color',
      width: 100,
      search: false,
      render: (color) => (
        color ? (
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <div
              style={{
                width: 16,
                height: 16,
                backgroundColor: color,
                borderRadius: 2,
                border: '1px solid #d9d9d9',
              }}
            />
            <span>{color}</span>
          </div>
        ) : '-'
      ),
    },
    {
      title: '排序',
      dataIndex: 'sort_order',
      width: 80,
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      width: 100,
      valueType: 'select',
      valueEnum: {
        true: { text: '启用', status: 'Success' },
        false: { text: '禁用', status: 'Default' },
      },
      render: (_, record) => (
        <Tag color={record.is_active ? 'green' : 'default'}>
          {record.is_active ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      width: 150,
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 150,
      render: (_, record) => [
        <Button
          key="edit"
          type="link"
          size="small"
          icon={<EditOutlined />}
          onClick={() => handleEditCategory(record)}
        >
          编辑
        </Button>,
        <Popconfirm
          key="delete"
          title="确定要删除这个分类吗？"
          description="删除后无法恢复，且正在使用的分类无法删除"
          onConfirm={() => handleDeleteCategory(record.id)}
          okText="确定"
          cancelText="取消"
        >
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
          >
            删除
          </Button>
        </Popconfirm>,
      ],
    },
  ];

  return (
    <PageContainer>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <span>
              <MessageOutlined />
              反馈管理
            </span>
          }
          key="feedback"
        >
          {/* 统计卡片 */}
          {stats && (
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Card>
                  <Statistic title="总反馈数" value={stats.total_count} />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="待处理"
                    value={stats.status_stats?.pending || 0}
                    valueStyle={{ color: '#faad14' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="处理中"
                    value={stats.status_stats?.processing || 0}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="已解决"
                    value={stats.status_stats?.resolved || 0}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Card>
              </Col>
            </Row>
          )}

          <ProTable<FeedbackItem>
            headerTitle="意见反馈管理"
            actionRef={actionRef}
            rowKey="id"
            search={{
              labelWidth: 120,
            }}
            toolBarRender={() => [
              <Button
                key="refresh"
                icon={<ReloadOutlined />}
                onClick={() => actionRef.current?.reload()}
              >
                刷新
              </Button>,
            ]}
            request={async (params) => {
              console.log('=== 开始获取反馈列表 ===');
              console.log('请求参数:', params);
              try {
                const response = await getAdminFeedbackList({
                  page: params.current,
                  page_size: params.pageSize,
                  type: params.type,
                  status: params.status,
                  priority: params.priority,
                  keyword: params.title,
                });
                console.log('反馈列表API响应:', response);
                return {
                  data: response.data?.list || [],
                  success: response.success,
                  total: response.data?.total || 0,
                };
              } catch (error) {
                console.error('获取反馈列表失败:', error);
                throw error;
              }
            }}
            columns={columns}
            pagination={{
              defaultPageSize: 10,
              showSizeChanger: true,
            }}
          />
        </TabPane>

        <TabPane
          tab={
            <span>
              <FolderOutlined />
              分类管理
            </span>
          }
          key="category"
        >
          <ProTable<FeedbackCategory>
            headerTitle="反馈分类管理"
            actionRef={categoryActionRef}
            rowKey="id"
            search={{
              labelWidth: 120,
            }}
            toolBarRender={() => [
              <Button
                key="create"
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreateCategory}
              >
                新建分类
              </Button>,
              <Button
                key="refresh"
                icon={<ReloadOutlined />}
                onClick={() => categoryActionRef.current?.reload()}
              >
                刷新
              </Button>,
            ]}
            request={async (params) => {
              const response = await getFeedbackCategories();
              let data = response.data || [];
              
              // 客户端过滤
              if (params.name) {
                data = data.filter((item: FeedbackCategory) => 
                  item.name.toLowerCase().includes(params.name.toLowerCase())
                );
              }
              if (params.is_active !== undefined) {
                data = data.filter((item: FeedbackCategory) => 
                  item.is_active === (params.is_active === 'true')
                );
              }
              
              return {
                data,
                success: response.success,
                total: data.length,
              };
            }}
            columns={categoryColumns}
            pagination={{
              defaultPageSize: 10,
              showSizeChanger: true,
            }}
          />
        </TabPane>
      </Tabs>

      {/* 详情抽屉 */}
      <Drawer
        title="反馈详情"
        width={800}
        open={detailVisible}
        onClose={() => setDetailVisible(false)}
      >
        {currentFeedback && (
          <div>
            <Descriptions column={2} bordered>
              <Descriptions.Item label="ID">{currentFeedback.id}</Descriptions.Item>
              <Descriptions.Item label="类型">
                <Tag color={typeMap[currentFeedback.type].color}>
                  {typeMap[currentFeedback.type].text}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={statusMap[currentFeedback.status].color}>
                  {statusMap[currentFeedback.status].text}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="优先级">
                <Tag color={priorityMap[currentFeedback.priority].color}>
                  {priorityMap[currentFeedback.priority].text}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="提交用户" span={2}>
                {currentFeedback.user_name}
              </Descriptions.Item>
              <Descriptions.Item label="联系邮箱" span={2}>
                {currentFeedback.contact_email || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="联系电话" span={2}>
                {currentFeedback.contact_phone || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="设备信息" span={2}>
                {currentFeedback.device_info || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="系统版本">
                {currentFeedback.system_version || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="应用版本">
                {currentFeedback.app_version || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间" span={2}>
                {currentFeedback.created_at}
              </Descriptions.Item>
            </Descriptions>

            <Card title="反馈标题" style={{ marginTop: 16 }}>
              <Text strong>{currentFeedback.title}</Text>
            </Card>

            <Card title="反馈内容" style={{ marginTop: 16 }}>
              <div style={{ whiteSpace: 'pre-wrap' }}>
                {currentFeedback.content}
              </div>
            </Card>

            {currentFeedback.resolution && (
              <Card title="处理结果" style={{ marginTop: 16 }}>
                <div style={{ whiteSpace: 'pre-wrap' }}>
                  {currentFeedback.resolution}
                </div>
              </Card>
            )}

            {currentFeedback.replies && currentFeedback.replies.length > 0 && (
              <Card title="回复记录" style={{ marginTop: 16 }}>
                {currentFeedback.replies.map((reply) => (
                  <div key={reply.id} style={{ marginBottom: 16, padding: 12, background: '#f5f5f5', borderRadius: 4 }}>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>{reply.admin_name}</Text>
                      <Text type="secondary" style={{ marginLeft: 8 }}>
                        {reply.created_at}
                      </Text>
                      {reply.is_public && (
                        <Tag color="blue" style={{ marginLeft: 8 }}>公开</Tag>
                      )}
                    </div>
                    <div style={{ whiteSpace: 'pre-wrap' }}>
                      {reply.content}
                    </div>
                  </div>
                ))}
              </Card>
            )}
          </div>
        )}
      </Drawer>

      {/* 回复模态框 */}
      <Modal
        title="回复反馈"
        open={replyVisible}
        onOk={handleReplySubmit}
        onCancel={() => setReplyVisible(false)}
        width={600}
      >
        <Form form={replyForm} layout="vertical">
          <Form.Item
            name="content"
            label="回复内容"
            rules={[{ required: true, message: '请输入回复内容' }]}
          >
            <TextArea rows={6} placeholder="请输入回复内容" />
          </Form.Item>
          <Form.Item
            name="is_public"
            label="是否公开"
            valuePropName="checked"
          >
            <Select placeholder="选择是否公开">
              <Option value={true}>公开（用户可见）</Option>
              <Option value={false}>内部（仅管理员可见）</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 状态更新模态框 */}
      <Modal
        title="更新状态"
        open={statusVisible}
        onOk={handleStatusSubmit}
        onCancel={() => setStatusVisible(false)}
        width={500}
      >
        <Form form={statusForm} layout="vertical">
          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select placeholder="选择状态">
              <Option value="pending">待处理</Option>
              <Option value="processing">处理中</Option>
              <Option value="resolved">已解决</Option>
              <Option value="closed">已关闭</Option>
              <Option value="rejected">已拒绝</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="priority"
            label="优先级"
            rules={[{ required: true, message: '请选择优先级' }]}
          >
            <Select placeholder="选择优先级">
              <Option value="low">低</Option>
              <Option value="normal">普通</Option>
              <Option value="high">高</Option>
              <Option value="urgent">紧急</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="resolution"
            label="处理结果"
          >
            <TextArea rows={4} placeholder="请输入处理结果（可选）" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 分类创建/编辑模态框 */}
      <Modal
        title={editingCategory ? '编辑分类' : '新建分类'}
        open={categoryVisible}
        onOk={handleCategorySubmit}
        onCancel={() => setCategoryVisible(false)}
        width={600}
      >
        <Form form={categoryForm} layout="vertical">
          <Form.Item
            name="name"
            label="分类名称"
            rules={[{ required: true, message: '请输入分类名称' }]}
          >
            <Input placeholder="请输入分类名称" />
          </Form.Item>
          <Form.Item
            name="description"
            label="分类描述"
          >
            <TextArea rows={3} placeholder="请输入分类描述（可选）" />
          </Form.Item>
          <Form.Item
            name="icon"
            label="图标"
          >
            <Input placeholder="请输入图标名称（可选）" />
          </Form.Item>
          <Form.Item
            name="sort_order"
            label="排序"
            rules={[{ required: true, message: '请输入排序值' }]}
          >
            <InputNumber
              min={1}
              max={999}
              placeholder="请输入排序值"
              style={{ width: '100%' }}
            />
          </Form.Item>
          <Form.Item
            name="is_active"
            label="状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default FeedbackManagement;