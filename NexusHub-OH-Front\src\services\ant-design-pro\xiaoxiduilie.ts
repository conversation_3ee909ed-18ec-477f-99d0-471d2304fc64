/*
 * @Author: xiaoxiaozhou <EMAIL>
 * @Date: 2025-06-07 11:53:23
 * @LastEditors: xiaoxiaozhou <EMAIL>
 * @LastEditTime: 2025-06-11 19:22:59
 * @FilePath: \NexusHub-OH\NexusHub-OH-Front\src\services\ant-design-pro\xiaoxiduilie.ts
 * @Description: 
 * 技术生的工作日常
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 记录用户活动 记录用户活动到消息队列 POST /api/v1/messages/activity */
export async function postApiV1MessagesActivity(
  body: API.UserActivityRequest,
  options?: { [key: string]: any },
) {
  return request<API.Response>('/messages/activity', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 触发应用审核 触发应用审核流程 POST /api/v1/messages/app-review/${param0} */
export async function postApiV1MessagesAppReviewAppId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postApiV1MessagesAppReviewAppIdParams,
  options?: { [key: string]: any },
) {
  const { app_id: param0, ...queryParams } = params;
  return request<API.Response>(`/api/v1/messages/app-review/${param0}`, {
    method: 'POST',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 发送邮件消息 发送邮件到消息队列 POST /api/v1/messages/email */
export async function postApiV1MessagesEmail(
  body: API.SendEmailRequest,
  options?: { [key: string]: any },
) {
  return request<API.Response>('/messages/email', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 发送通知消息 向指定用户发送通知消息 POST /api/v1/messages/notification */
export async function postApiV1MessagesNotification(
  body: API.SendNotificationRequest,
  options?: { [key: string]: any },
) {
  return request<API.Response>('/messages/notification', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取队列状态 获取消息队列状态信息 GET /api/v1/messages/status */
export async function getApiV1MessagesStatus(options?: { [key: string]: any }) {
  return request<API.Response>('/messages/status', {
    method: 'GET',
    ...(options || {}),
  });
}
