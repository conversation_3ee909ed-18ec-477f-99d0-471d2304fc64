// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 获取分类统计 获取各应用分类的应用数量和下载统计 GET /dashboard/analytics/categories */
export async function getDashboardAnalyticsCategories(options?: { [key: string]: any }) {
  return request<API.Response & { data?: API.CategoryStats[] }>('/dashboard/analytics/categories', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取热门应用 获取平台上下载量和评分最高的应用 GET /dashboard/analytics/popular-apps */
export async function getDashboardAnalyticsPopularApps(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getDashboardAnalyticsPopularAppsParams,
  options?: { [key: string]: any },
) {
  return request<API.Response & { data?: API.PopularApp[] }>('/dashboard/analytics/popular-apps', {
    method: 'GET',
    params: {
      // limit has a default value: 10
      limit: '10',
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取分析页摘要数据 获取平台总体数据摘要，包括用户、应用、下载、评论等统计 GET /dashboard/analytics/summary */
export async function getDashboardAnalyticsSummary(options?: { [key: string]: any }) {
  return request<API.Response & { data?: API.AnalyticsSummary }>('/dashboard/analytics/summary', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取趋势分析数据 获取用户、应用、下载、开发者等趋势数据 GET /dashboard/analytics/trend */
export async function getDashboardAnalyticsTrend(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getDashboardAnalyticsTrendParams,
  options?: { [key: string]: any },
) {
  return request<API.Response & { data?: API.AnalyticsTrend }>('/dashboard/analytics/trend', {
    method: 'GET',
    params: {
      // days has a default value: 30
      days: '30',
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取告警事件 获取系统告警事件 GET /dashboard/monitoring/alerts */
export async function getDashboardMonitoringAlerts(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getDashboardMonitoringAlertsParams,
  options?: { [key: string]: any },
) {
  return request<API.PageResponse & { data?: API.AlertEvent[] }>('/dashboard/monitoring/alerts', {
    method: 'GET',
    params: {
      // page has a default value: 1
      page: '1',
      // page_size has a default value: 20
      page_size: '20',
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取监控数据 获取系统运行状态监控数据 GET /dashboard/monitoring/data */
export async function getDashboardMonitoringData(options?: { [key: string]: any }) {
  return request<API.Response & { data?: API.MonitoringData }>('/dashboard/monitoring/data', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取系统日志 获取系统运行日志记录 GET /dashboard/monitoring/logs */
export async function getDashboardMonitoringLogs(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getDashboardMonitoringLogsParams,
  options?: { [key: string]: any },
) {
  return request<API.PageResponse & { data?: API.SystemLog[] }>('/dashboard/monitoring/logs', {
    method: 'GET',
    params: {
      // page has a default value: 1
      page: '1',
      // page_size has a default value: 20
      page_size: '20',
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取最近活动 获取当前用户的最近活动记录 GET /dashboard/workbench/activities */
export async function getDashboardWorkbenchActivities(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getDashboardWorkbenchActivitiesParams,
  options?: { [key: string]: any },
) {
  return request<API.Response & { data?: API.RecentActivity[] }>(
    '/dashboard/workbench/activities',
    {
      method: 'GET',
      params: {
        // limit has a default value: 10
        limit: '10',
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 获取工作台摘要 获取当前用户的工作台摘要数据 GET /dashboard/workbench/summary */
export async function getDashboardWorkbenchSummary(options?: { [key: string]: any }) {
  return request<API.Response & { data?: API.WorkbenchSummary }>('/dashboard/workbench/summary', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取任务列表 获取当前用户的任务列表 GET /dashboard/workbench/tasks */
export async function getDashboardWorkbenchTasks(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getDashboardWorkbenchTasksParams,
  options?: { [key: string]: any },
) {
  return request<API.PageResponse & { data?: API.TaskItem[] }>('/dashboard/workbench/tasks', {
    method: 'GET',
    params: {
      // page has a default value: 1
      page: '1',
      // page_size has a default value: 20
      page_size: '20',
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建任务 创建新的任务 POST /dashboard/workbench/tasks */
export async function postDashboardWorkbenchTasks(
  body: API.TaskItem,
  options?: { [key: string]: any },
) {
  return request<API.Response & { data?: API.TaskItem }>('/dashboard/workbench/tasks', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新任务 更新任务信息 PUT /dashboard/workbench/tasks/${param0} */
export async function putDashboardWorkbenchTasksId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putDashboardWorkbenchTasksIdParams,
  body: API.TaskItem,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Response & { data?: API.TaskItem }>(`/dashboard/workbench/tasks/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 删除任务 删除指定的任务 DELETE /dashboard/workbench/tasks/${param0} */
export async function deleteDashboardWorkbenchTasksId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteDashboardWorkbenchTasksIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Response>(`/dashboard/workbench/tasks/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}
