package database

import (
	"fmt"
	"log"

	"nexushub-oh-back/config"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// PostgresDB 数据库连接结构体
type PostgresDB struct {
	DB *gorm.DB
}

// NewPostgresDB 创建一个新的 PostgreSQL 数据库连接
func NewPostgresDB(cfg *config.DatabaseConfig) (*PostgresDB, error) {
	dsn := fmt.Sprintf(
		"host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		cfg.Host, cfg.Port, cfg.User, cfg.Password, cfg.DBName, cfg.SSLMode,
	)

	// 配置GORM日志
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	}

	// 连接数据库
	db, err := gorm.Open(postgres.Open(dsn), gormConfig)
	if err != nil {
		return nil, fmt.Errorf("无法连接到数据库: %w", err)
	}

	log.Println("数据库连接成功")

	return &PostgresDB{
		DB: db,
	}, nil
}

// AutoMigrate 自动迁移数据库表结构
func (p *PostgresDB) AutoMigrate(models ...interface{}) error {
	return p.DB.AutoMigrate(models...)
}

// Close 关闭数据库连接
func (p *PostgresDB) Close() error {
	sqlDB, err := p.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}
