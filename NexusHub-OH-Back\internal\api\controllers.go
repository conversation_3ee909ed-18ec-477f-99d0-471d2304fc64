package api

import (
	"nexushub-oh-back/internal/controllers"
	"gorm.io/gorm"
)

// NewBrowseHistoryController 创建浏览历史控制器
func NewBrowseHistoryController(db *gorm.DB) *controllers.BrowseHistoryController {
	return controllers.NewBrowseHistoryController(db)
}

// NewHelpCenterController 创建帮助中心控制器
func NewHelpCenterController(db *gorm.DB) *controllers.HelpCenterController {
	return controllers.NewHelpCenterController(db)
}

// NewFeedbackController 创建意见反馈控制器
func NewFeedbackController(db *gorm.DB) *controllers.FeedbackController {
	return controllers.NewFeedbackController(db)
}