.statusCard {
  .ant-card-body {
    padding: 24px;
  }
}

.statusHeader {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;

  .statusTitle {
    margin-bottom: 4px !important;
  }
}

.statusContent {
  .statusAlert {
    margin-bottom: 24px;
  }

  .stepsContainer {
    margin: 24px 0;
    padding: 20px;
    background: #fafafa;
    border-radius: 6px;
  }

  .infoContainer {
    margin-top: 24px;
    padding: 16px;
    background: #f9f9f9;
    border-radius: 6px;

    .ant-typography-title {
      margin-bottom: 12px !important;
    }
  }
}

.emptyStatus {
  text-align: center;
  padding: 40px 20px;

  .emptyIcon {
    font-size: 48px;
    color: #d9d9d9;
    margin-bottom: 16px;
  }

  .ant-typography-title {
    color: rgba(0, 0, 0, 0.45);
    margin-bottom: 8px !important;
  }
}

// 状态图标样式
.pendingIcon {
  font-size: 24px;
  color: #faad14;
}

.approvedIcon {
  font-size: 24px;
  color: #52c41a;
}

.rejectedIcon {
  font-size: 24px;
  color: #ff4d4f;
}

.defaultIcon {
  font-size: 24px;
  color: #d9d9d9;
}

// 响应式设计
@media (max-width: 768px) {
  .statusCard {
    .ant-card-body {
      padding: 16px;
    }
  }

  .statusHeader {
    .ant-space {
      flex-direction: column;
      align-items: flex-start !important;
      width: 100%;
    }
  }

  .statusContent {
    .stepsContainer {
      padding: 16px;
    }

    .infoContainer {
      padding: 12px;

      .ant-descriptions {
        .ant-descriptions-item {
          padding-bottom: 8px;
        }
      }
    }
  }

  .emptyStatus {
    padding: 30px 16px;

    .emptyIcon {
      font-size: 36px;
    }
  }
}

// 深色主题适配
@media (prefers-color-scheme: dark) {
  .statusContent {
    .stepsContainer {
      background: rgba(255, 255, 255, 0.04);
    }

    .infoContainer {
      background: rgba(255, 255, 255, 0.04);
    }
  }
}