/*
 * @Author: xiaoxiaozhou <EMAIL>
 * @Date: 2025-06-07 00:57:05
 * @LastEditors: xiaoxiaozhou <EMAIL>
 * @LastEditTime: 2025-06-07 01:50:20
 * @FilePath: \NexusHub-OH\NexusHub-OH-Front\src\pages\dashboard\workplace\service.ts
 * @Description: 
 * 技术生的工作日常
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
import { request } from '@umijs/max';
import type { ActivitiesType, AnalysisData, NoticeType } from './data';

/**
 * 获取工作台摘要数据
 * 包括我的应用数量、总下载量、平均评分等
 */
export async function queryProjectNotice(): Promise<{ data: NoticeType[] }> {
  return request('/dashboard/workbench/summary');
}

/**
 * 获取最近活动
 * 获取用户最近的活动记录
 */
export async function queryActivities(limit?: number): Promise<{ data: ActivitiesType[] }> {
  return request('/dashboard/workbench/activities', {
    params: { limit: limit || 20 },
  });
}

/**
 * 获取任务列表
 * 获取用户的任务列表
 */
export async function queryTasks(params?: {
  status?: string;
  page?: number;
  page_size?: number;
}): Promise<{ data: any }> {
  return request('/dashboard/workbench/tasks', {
    params: {
      page: 1,
      page_size: 20,
      ...params,
    },
  });
}

/**
 * 创建任务
 * 创建新的任务
 */
export async function createTask(params: {
  title: string;
  description?: string;
  priority?: string;
  due_date?: string;
}): Promise<{ data: any }> {
  return request('/dashboard/workbench/tasks', {
    method: 'POST',
    data: params,
  });
}

/**
 * 更新任务
 * 更新指定任务的信息
 */
export async function updateTask(id: string, params: {
  title?: string;
  description?: string;
  status?: string;
  priority?: string;
  due_date?: string;
}): Promise<{ data: any }> {
  return request(`/dashboard/workbench/tasks/${id}`, {
    method: 'PUT',
    data: params,
  });
}

/**
 * 删除任务
 * 删除指定的任务
 */
export async function deleteTask(id: string): Promise<{ data: any }> {
  return request(`/dashboard/workbench/tasks/${id}`, {
    method: 'DELETE',
  });
}
