import React, { useState, useEffect } from 'react';
import { Card, List, Button, Tag, Space, Pagination, Tabs, Empty, message, Popconfirm } from 'antd';
import { BellOutlined, DeleteOutlined, CheckOutlined, EyeOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { createStyles } from 'antd-style';
import { 
  getNotifications, 
  markAsRead, 
  markAllAsRead, 
  deleteNotification,
  type NotificationItem 
} from '@/services/ant-design-pro/notifications';

const useStyles = createStyles(({ token }) => ({
  notificationItem: {
    padding: '16px 24px',
    borderBottom: `1px solid ${token.colorBorderSecondary}`,
    '&:hover': {
      backgroundColor: token.colorBgTextHover,
    },
    '&.unread': {
      backgroundColor: token.colorBgContainer,
      borderLeft: `4px solid ${token.colorPrimary}`,
    },
  },
  notificationHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: '8px',
  },
  notificationTitle: {
    fontSize: '16px',
    fontWeight: 500,
    color: token.colorText,
    marginBottom: '4px',
  },
  notificationDescription: {
    fontSize: '14px',
    color: token.colorTextSecondary,
    lineHeight: '20px',
    marginBottom: '8px',
  },
  notificationMeta: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  notificationTime: {
    fontSize: '12px',
    color: token.colorTextTertiary,
  },
  actionButtons: {
    display: 'flex',
    gap: '8px',
  },
  typeTag: {
    marginRight: '8px',
  },
  statusTag: {
    marginRight: '8px',
  },
  toolbarCard: {
    marginBottom: '16px',
  },
  toolbar: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  emptyContainer: {
    padding: '60px 0',
    textAlign: 'center',
  },
}));

const NotificationsPage: React.FC = () => {
  const { styles } = useStyles();
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [activeTab, setActiveTab] = useState('all');
  const [selectedIds, setSelectedIds] = useState<number[]>([]);

  // 获取通知列表
  const fetchNotifications = async () => {
    setLoading(true);
    try {
      const params = {
        page: current,
        pageSize,
        read: activeTab === 'unread' ? false : undefined,
      };
      const response = await getNotifications(params);
      if (response.success && response.data) {
        setNotifications(response.data.list);
        setTotal(response.data.total);
      }
    } catch (error) {
      message.error('获取通知列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 标记为已读
  const handleMarkAsRead = async (id: number) => {
    try {
      const response = await markAsRead({ id });
      if (response.success) {
        message.success('标记已读成功');
        setNotifications(prev => 
          prev.map(item => 
            item.id === id ? { ...item, read: true } : item
          )
        );
      }
    } catch (error) {
      message.error('标记已读失败');
    }
  };

  // 标记全部已读
  const handleMarkAllAsRead = async () => {
    try {
      const response = await markAllAsRead();
      if (response.success) {
        message.success('全部标记已读成功');
        setNotifications(prev => 
          prev.map(item => ({ ...item, read: true }))
        );
      }
    } catch (error) {
      message.error('标记全部已读失败');
    }
  };

  // 删除通知
  const handleDelete = async (id: number) => {
    try {
      const response = await deleteNotification({ id });
      if (response.success) {
        message.success('删除成功');
        setNotifications(prev => prev.filter(item => item.id !== id));
        setTotal(prev => prev - 1);
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    try {
      await Promise.all(selectedIds.map(id => deleteNotification({ id })));
      message.success('批量删除成功');
      setNotifications(prev => prev.filter(item => !selectedIds.includes(item.id)));
      setTotal(prev => prev - selectedIds.length);
      setSelectedIds([]);
    } catch (error) {
      message.error('批量删除失败');
    }
  };

  // 获取类型标签
  const getTypeTag = (type: string) => {
    const typeMap = {
      system: { color: 'blue', text: '系统通知' },
      review: { color: 'green', text: '审核通知' },
      developer: { color: 'orange', text: '开发者通知' },
      security: { color: 'red', text: '安全通知' },
    };
    const config = typeMap[type as keyof typeof typeMap] || { color: 'default', text: type };
    return <Tag color={config.color} className={styles.typeTag}>{config.text}</Tag>;
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      info: { color: 'blue', text: '信息' },
      success: { color: 'green', text: '成功' },
      warning: { color: 'orange', text: '警告' },
      error: { color: 'red', text: '错误' },
    };
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
    return <Tag color={config.color} className={styles.statusTag}>{config.text}</Tag>;
  };

  useEffect(() => {
    fetchNotifications();
  }, [current, pageSize, activeTab]);

  const unreadCount = notifications.filter(item => !item.read).length;

  return (
    <PageContainer
      title="通知中心"
      subTitle="查看和管理您的所有通知"
      extra={[
        <Button key="refresh" onClick={fetchNotifications}>
          刷新
        </Button>,
      ]}
    >
      {/* 工具栏 */}
      <Card className={styles.toolbarCard}>
        <div className={styles.toolbar}>
          <Tabs
            activeKey={activeTab}
            onChange={(key) => {
              setActiveTab(key);
              setCurrent(1);
            }}
            items={[
              {
                key: 'all',
                label: `全部 (${total})`,
              },
              {
                key: 'unread',
                label: `未读 (${unreadCount})`,
              },
            ]}
          />
          <Space>
            {unreadCount > 0 && (
              <Button 
                type="primary" 
                icon={<CheckOutlined />}
                onClick={handleMarkAllAsRead}
              >
                全部已读
              </Button>
            )}
            {selectedIds.length > 0 && (
              <Popconfirm
                title="确认删除"
                description={`确定要删除选中的 ${selectedIds.length} 条通知吗？`}
                onConfirm={handleBatchDelete}
                okText="确定"
                cancelText="取消"
              >
                <Button 
                  danger 
                  icon={<DeleteOutlined />}
                >
                  批量删除 ({selectedIds.length})
                </Button>
              </Popconfirm>
            )}
          </Space>
        </div>
      </Card>

      {/* 通知列表 */}
      <Card>
        {notifications.length > 0 ? (
          <>
            <List
              loading={loading}
              dataSource={notifications}
              renderItem={(item) => (
                <div 
                  key={item.id}
                  className={`${styles.notificationItem} ${!item.read ? 'unread' : ''}`}
                >
                  <div className={styles.notificationHeader}>
                    <div style={{ flex: 1 }}>
                      <div className={styles.notificationTitle}>
                        <BellOutlined style={{ marginRight: '8px' }} />
                        {item.title}
                        {!item.read && <Tag color="red" size="small" style={{ marginLeft: '8px' }}>未读</Tag>}
                      </div>
                      {item.description && (
                        <div className={styles.notificationDescription}>
                          {item.description}
                        </div>
                      )}
                      <div className={styles.notificationMeta}>
                        <Space>
                          {getTypeTag(item.type)}
                          {getStatusTag(item.status)}
                          <span className={styles.notificationTime}>{item.created_at}</span>
                        </Space>
                        <div className={styles.actionButtons}>
                          {!item.read && (
                            <Button 
                              size="small" 
                              icon={<EyeOutlined />}
                              onClick={() => handleMarkAsRead(item.id)}
                            >
                              标记已读
                            </Button>
                          )}
                          <Popconfirm
                            title="确认删除"
                            description="确定要删除这条通知吗？"
                            onConfirm={() => handleDelete(item.id)}
                            okText="确定"
                            cancelText="取消"
                          >
                            <Button 
                              size="small" 
                              danger 
                              icon={<DeleteOutlined />}
                            >
                              删除
                            </Button>
                          </Popconfirm>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            />
            
            {/* 分页 */}
            <div style={{ textAlign: 'center', marginTop: '24px' }}>
              <Pagination
                current={current}
                pageSize={pageSize}
                total={total}
                showSizeChanger
                showQuickJumper
                showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`}
                onChange={(page, size) => {
                  setCurrent(page);
                  setPageSize(size);
                }}
              />
            </div>
          </>
        ) : (
          <div className={styles.emptyContainer}>
            <Empty 
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={activeTab === 'unread' ? '暂无未读通知' : '暂无通知'}
            />
          </div>
        )}
      </Card>
    </PageContainer>
  );
};

export default NotificationsPage;