package models

import (
	"time"

	"gorm.io/gorm"
)

// Application 应用模型
type Application struct {
	ID              uint              `gorm:"primarykey" json:"id"`
	CreatedAt       time.Time         `json:"created_at"`
	UpdatedAt       time.Time         `json:"updated_at"`
	DeletedAt       *time.Time        `gorm:"index" json:"deleted_at,omitempty"`
	Name            string            `gorm:"type:varchar(100);not null" json:"name"`
	Package         string            `gorm:"type:varchar(100);uniqueIndex;not null" json:"package"`
	Description     string            `gorm:"type:text" json:"description"`
	ShortDesc       string            `gorm:"type:varchar(200)" json:"short_desc"`
	Icon            string            `gorm:"type:varchar(255)" json:"icon"`
	BannerImage     string            `gorm:"type:varchar(255)" json:"banner_image"`
	Category        string            `gorm:"type:varchar(50);index" json:"category"`
	DeveloperID     uint              `gorm:"index" json:"developer_id"`
	Developer       User              `gorm:"foreignKey:DeveloperID" json:"developer"`
	CurrentVersion  string            `gorm:"type:varchar(50)" json:"current_version"`
	PackageURL      string            `gorm:"type:varchar(255)" json:"packageURL"`
	Status          ApplicationStatus `gorm:"type:varchar(20);default:'pending'" json:"status"`
	ReleaseDate     *time.Time        `json:"release_date"`
	Size            int64             `json:"size"` // 单位：字节
	DownloadCount   int64             `gorm:"default:0" json:"download_count"`
	AverageRating   float64           `gorm:"default:0" json:"average_rating"`
	RatingCount     int               `gorm:"default:0" json:"rating_count"`
	MinOpenHarmonyOSVer string            `gorm:"type:varchar(20)" json:"min_open_harmony_os_ver"`
	Tags            string            `gorm:"type:varchar(255)" json:"tags"` // 以逗号分隔的标签
	WebsiteURL      string            `gorm:"type:varchar(255)" json:"website_url"`
	PrivacyURL      string            `gorm:"type:varchar(255)" json:"privacy_url"`
	IsVerified      bool              `gorm:"default:false" json:"is_verified"`
	IsFeatured      bool              `gorm:"default:false" json:"is_featured"`
	IsEditor        bool              `gorm:"default:false" json:"is_editor"` // 编辑推荐
	IsTop           bool              `gorm:"default:false" json:"is_top"`    // 置顶应用
	// 审核相关字段
	ReviewerID   *uint      `gorm:"index" json:"reviewer_id"` // 审核员ID
	Reviewer     *User      `gorm:"foreignKey:ReviewerID" json:"reviewer"`
	ApprovedAt   *time.Time `json:"approved_at"`                            // 审核通过时间
	RejectedAt   *time.Time `json:"rejected_at"`                            // 拒绝时间
	RejectReason string     `gorm:"type:varchar(255)" json:"reject_reason"` // 拒绝原因
}

// ApplicationStatus 应用状态
type ApplicationStatus string

const (
	// ApplicationStatusPending 待审核
	ApplicationStatusPending ApplicationStatus = "pending"
	// ApplicationStatusApproved 已发布
	ApplicationStatusApproved ApplicationStatus = "approved"
	// ApplicationStatusRejected 已拒绝
	ApplicationStatusRejected ApplicationStatus = "rejected"
	// ApplicationStatusRemoved 已下架
	ApplicationStatusRemoved ApplicationStatus = "removed"
	// ApplicationStatusDraft 草稿
	ApplicationStatusDraft ApplicationStatus = "draft"
)

// TableName 指定表名
func (Application) TableName() string {
	return "applications"
}

// AppScreenshot 应用截图
type AppScreenshot struct {
	ID            uint       `gorm:"primarykey" json:"id"`
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
	DeletedAt     *time.Time `gorm:"index" json:"deleted_at,omitempty"`
	ApplicationID uint       `gorm:"index" json:"application_id"`
	ImageURL      string     `gorm:"type:varchar(255);not null" json:"image_url"`
	SortOrder     int        `gorm:"default:0" json:"sort_order"`
}

// TableName 指定表名
func (AppScreenshot) TableName() string {
	return "app_screenshots"
}

// AppVersion 应用版本
type AppVersion struct {
	ID                uint              `gorm:"primarykey" json:"id"`
	CreatedAt         time.Time         `json:"created_at"`
	UpdatedAt         time.Time         `json:"updated_at"`
	DeletedAt         *time.Time        `gorm:"index" json:"deleted_at,omitempty"`
	ApplicationID     uint              `gorm:"index" json:"application_id"`
	Application       Application       `gorm:"foreignKey:ApplicationID" json:"application"`
	VersionName       string            `gorm:"type:varchar(50);not null" json:"version_name"`
	VersionCode       float64           `gorm:"not null" json:"version_code"`
	ChangeLog         string            `gorm:"type:text" json:"change_log"`
	PackageURL        string            `gorm:"type:varchar(255);not null" json:"package_url"`
	Size              int64             `json:"size"` // 单位：字节
	Status            ApplicationStatus `gorm:"type:varchar(20);default:'pending'" json:"status"`
	MinOpenHarmonyOSVer   string            `gorm:"type:varchar(20)" json:"min_open_harmony_os_ver"`
	ReleasedAt        *time.Time        `json:"released_at"`
	DownloadCount     int64             `gorm:"default:0" json:"download_count"`
	IncrementalUpdate bool              `gorm:"default:false" json:"incremental_update"` // 是否为增量更新
}

// TableName 指定表名
func (AppVersion) TableName() string {
	return "app_versions"
}

// CreateApplication 创建应用
func CreateApplication(db *gorm.DB, app *Application) error {
	return db.Create(app).Error
}

// GetApplicationByID 通过ID获取应用
func GetApplicationByID(db *gorm.DB, id uint) (*Application, error) {
	var app Application
	err := db.Preload("Developer").First(&app, id).Error
	return &app, err
}

// GetApplicationByPackage 通过包名获取应用
func GetApplicationByPackage(db *gorm.DB, pkg string) (*Application, error) {
	var app Application
	err := db.Preload("Developer").Where("package = ?", pkg).First(&app).Error
	return &app, err
}

// UpdateApplication 更新应用信息
func UpdateApplication(db *gorm.DB, app *Application) error {
	return db.Save(app).Error
}

// DeleteApplication 删除应用
func DeleteApplication(db *gorm.DB, id uint) error {
	return db.Delete(&Application{}, id).Error
}

// AddAppScreenshot 添加应用截图
func AddAppScreenshot(db *gorm.DB, screenshot *AppScreenshot) error {
	return db.Create(screenshot).Error
}

// GetAppScreenshots 获取应用截图
func GetAppScreenshots(db *gorm.DB, appID uint) ([]AppScreenshot, error) {
	var screenshots []AppScreenshot
	err := db.Where("application_id = ?", appID).Order("sort_order").Find(&screenshots).Error
	return screenshots, err
}

// AddAppVersion 添加应用版本
func AddAppVersion(db *gorm.DB, version *AppVersion) error {
	return db.Create(version).Error
}

// GetAppVersions 获取应用版本列表
func GetAppVersions(db *gorm.DB, appID uint) ([]AppVersion, error) {
	var versions []AppVersion
	err := db.Where("application_id = ?", appID).Order("version_code desc").Find(&versions).Error
	return versions, err
}

// GetLatestAppVersion 获取应用最新版本
func GetLatestAppVersion(db *gorm.DB, appID uint) (*AppVersion, error) {
	var version AppVersion
	err := db.Where("application_id = ? AND status = ?", appID, ApplicationStatusApproved).
		Order("version_code desc").First(&version).Error
	return &version, err
}

// GetAppVersionByID 根据ID获取应用版本
func GetAppVersionByID(db *gorm.DB, versionID uint) (*AppVersion, error) {
	var version AppVersion
	err := db.First(&version, versionID).Error
	return &version, err
}

// UpdateAppVersion 更新应用版本
func UpdateAppVersion(db *gorm.DB, version *AppVersion) error {
	return db.Save(version).Error
}

// DeleteAppVersion 删除应用版本
func DeleteAppVersion(db *gorm.DB, versionID uint) error {
	return db.Delete(&AppVersion{}, versionID).Error
}

// GetAppVersionsByStatus 根据状态获取应用版本列表
func GetAppVersionsByStatus(db *gorm.DB, appID uint, status ApplicationStatus) ([]AppVersion, error) {
	var versions []AppVersion
	err := db.Where("application_id = ? AND status = ?", appID, status).
		Order("version_code desc").Find(&versions).Error
	return versions, err
}

// GetPendingVersions 获取待审核的版本列表
func GetPendingVersions(db *gorm.DB, page, pageSize int) ([]AppVersion, int64, error) {
	var versions []AppVersion
	var total int64

	// 计算总数
	db.Model(&AppVersion{}).Where("status = ?", ApplicationStatusPending).Count(&total)

	// 获取分页数据
	offset := (page - 1) * pageSize
	err := db.Preload("Application").Preload("Application.Developer").
		Where("status = ?", ApplicationStatusPending).
		Order("created_at desc").
		Offset(offset).Limit(pageSize).Find(&versions).Error

	return versions, total, err
}

// PublishVersion 发布版本
func PublishVersion(db *gorm.DB, versionID uint) error {
	now := time.Now()
	return db.Model(&AppVersion{}).Where("id = ?", versionID).Updates(map[string]interface{}{
		"status":      ApplicationStatusApproved,
		"released_at": &now,
		"updated_at":  now,
	}).Error
}

// UnpublishVersion 下架版本
func UnpublishVersion(db *gorm.DB, versionID uint) error {
	return db.Model(&AppVersion{}).Where("id = ?", versionID).Updates(map[string]interface{}{
		"status":     ApplicationStatusRejected,
		"updated_at": time.Now(),
	}).Error
}
