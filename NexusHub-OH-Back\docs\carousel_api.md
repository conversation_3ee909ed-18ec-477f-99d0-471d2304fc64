# 轮播图管理 API 文档

## 概述

轮播图管理功能提供了完整的轮播图CRUD操作，支持多种类型的轮播图内容，包括应用推广、精选集推广、外部链接和公告等。该功能仅允许管理员访问管理接口，普通用户只能查看激活的轮播图。

## 功能特性

- ✅ 支持多种轮播图类型（应用、精选集、外部链接、公告）
- ✅ 状态管理（激活、停用、草稿）
- ✅ 时间范围控制（开始时间、结束时间）
- ✅ 排序功能
- ✅ 点击统计
- ✅ 权限控制（仅管理员可管理）
- ✅ 关联验证（验证目标应用/精选集是否存在）

## 数据模型

### 轮播图状态
- `active`: 激活
- `inactive`: 停用
- `draft`: 草稿

### 轮播图类型
- `app`: 应用推广
- `collection`: 精选集推广
- `external`: 外部链接
- `announcement`: 公告

## API 接口

### 公开接口（无需认证）

#### 1. 获取激活的轮播图列表

```http
GET /api/v1/public/carousels
```

**响应示例：**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "title": "热门应用推荐",
      "subtitle": "发现最新最热门的应用",
      "image_url": "https://example.com/carousel1.jpg",
      "type": "app",
      "target_id": 123,
      "target_url": "/app/detail/123",
      "status": "active",
      "sort_order": 1,
      "start_time": "2024-01-01T00:00:00Z",
      "end_time": "2024-12-31T23:59:59Z",
      "click_count": 1000,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      "target_info": {
        "id": 123,
        "name": "示例应用",
        "icon": "https://example.com/app-icon.png",
        "description": "这是一个示例应用"
      }
    }
  ]
}
```

#### 2. 轮播图点击统计

```http
POST /api/v1/public/carousels/click
Content-Type: application/json

{
  "carousel_id": 1
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "记录成功"
}
```

### 管理员接口（需要管理员权限）

#### 1. 创建轮播图

```http
POST /api/v1/admin/carousels
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "title": "热门应用推荐",
  "subtitle": "发现最新最热门的应用",
  "image_url": "https://example.com/carousel1.jpg",
  "type": "app",
  "target_id": 123,
  "target_url": "",
  "status": "active",
  "sort_order": 1,
  "start_time": "2024-01-01T00:00:00Z",
  "end_time": "2024-12-31T23:59:59Z"
}
```

**响应示例：**
```json
{
  "code": 201,
  "message": "创建成功",
  "data": {
    "id": 1,
    "title": "热门应用推荐",
    "subtitle": "发现最新最热门的应用",
    "image_url": "https://example.com/carousel1.jpg",
    "type": "app",
    "target_id": 123,
    "target_url": "/app/detail/123",
    "status": "active",
    "sort_order": 1,
    "start_time": "2024-01-01T00:00:00Z",
    "end_time": "2024-12-31T23:59:59Z",
    "click_count": 0,
    "created_by": 1,
    "updated_by": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z",
    "creator_name": "管理员",
    "updater_name": "管理员"
  }
}
```

#### 2. 获取轮播图列表（管理员）

```http
GET /api/v1/admin/carousels?page=1&page_size=10&status=active&type=app&keyword=热门
Authorization: Bearer <admin_token>
```

**查询参数：**
- `page`: 页码（默认1）
- `page_size`: 每页数量（默认10，最大100）
- `status`: 状态过滤（active/inactive/draft）
- `type`: 类型过滤（app/collection/external/announcement）
- `keyword`: 关键词搜索（搜索标题和副标题）

**响应示例：**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "title": "热门应用推荐",
        "subtitle": "发现最新最热门的应用",
        "image_url": "https://example.com/carousel1.jpg",
        "type": "app",
        "target_id": 123,
        "target_url": "/app/detail/123",
        "status": "active",
        "sort_order": 1,
        "start_time": "2024-01-01T00:00:00Z",
        "end_time": "2024-12-31T23:59:59Z",
        "click_count": 1000,
        "created_by": 1,
        "updated_by": 1,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "creator_name": "管理员",
        "updater_name": "管理员",
        "target_info": {
          "id": 123,
          "name": "示例应用",
          "icon": "https://example.com/app-icon.png",
          "description": "这是一个示例应用"
        }
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10
  }
}
```

#### 3. 获取轮播图详情

```http
GET /api/v1/admin/carousels/1
Authorization: Bearer <admin_token>
```

#### 4. 更新轮播图

```http
PUT /api/v1/admin/carousels/1
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "title": "更新后的标题",
  "status": "inactive"
}
```

#### 5. 删除轮播图

```http
DELETE /api/v1/admin/carousels/1
Authorization: Bearer <admin_token>
```

#### 6. 批量更新排序

```http
PUT /api/v1/admin/carousels/sort
Authorization: Bearer <admin_token>
Content-Type: application/json

[
  {
    "id": 1,
    "sort_order": 2
  },
  {
    "id": 2,
    "sort_order": 1
  }
]
```

## 使用场景

### 1. 应用推广轮播图

```json
{
  "title": "热门应用推荐",
  "subtitle": "发现最新最热门的应用",
  "image_url": "https://example.com/app-banner.jpg",
  "type": "app",
  "target_id": 123,
  "status": "active",
  "sort_order": 1
}
```

### 2. 精选集推广轮播图

```json
{
  "title": "游戏精选集",
  "subtitle": "精选优质游戏合集",
  "image_url": "https://example.com/collection-banner.jpg",
  "type": "collection",
  "target_id": 456,
  "status": "active",
  "sort_order": 2
}
```

### 3. 外部链接轮播图

```json
{
  "title": "开发者大会",
  "subtitle": "参加HarmonyOS开发者大会",
  "image_url": "https://example.com/event-banner.jpg",
  "type": "external",
  "target_url": "https://developer.harmonyos.com/event",
  "status": "active",
  "sort_order": 3
}
```

### 4. 公告轮播图

```json
{
  "title": "系统维护通知",
  "subtitle": "系统将于今晚进行维护升级",
  "image_url": "https://example.com/announcement-banner.jpg",
  "type": "announcement",
  "status": "active",
  "sort_order": 0,
  "start_time": "2024-01-01T00:00:00Z",
  "end_time": "2024-01-02T00:00:00Z"
}
```

## 错误码说明

- `400`: 请求参数错误
- `401`: 未授权（需要登录）
- `403`: 权限不足（需要管理员权限）
- `404`: 轮播图不存在
- `500`: 服务器内部错误

## 注意事项

1. **权限控制**：轮播图管理接口仅允许管理员访问
2. **时间验证**：开始时间不能晚于结束时间
3. **目标验证**：当类型为`app`或`collection`时，会验证目标ID是否存在
4. **状态控制**：只有状态为`active`且在有效时间范围内的轮播图才会在公开接口中返回
5. **排序规则**：轮播图按`sort_order`升序排列，相同排序值按创建时间降序排列
6. **图片要求**：建议轮播图尺寸为16:9比例，支持常见图片格式
7. **点击统计**：点击统计是累计值，不会重置

## 数据库表结构

```sql
CREATE TABLE carousels (
    id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    subtitle VARCHAR(500),
    image_url VARCHAR(500) NOT NULL,
    type VARCHAR(20) NOT NULL DEFAULT 'app',
    target_id INTEGER,
    target_url VARCHAR(500),
    status VARCHAR(20) NOT NULL DEFAULT 'draft',
    sort_order INTEGER NOT NULL DEFAULT 0,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    click_count BIGINT NOT NULL DEFAULT 0,
    created_by INTEGER NOT NULL,
    updated_by INTEGER,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

CREATE INDEX idx_carousels_status ON carousels(status);
CREATE INDEX idx_carousels_type ON carousels(type);
CREATE INDEX idx_carousels_target_id ON carousels(target_id);
CREATE INDEX idx_carousels_created_by ON carousels(created_by);
CREATE INDEX idx_carousels_updated_by ON carousels(updated_by);
CREATE INDEX idx_carousels_deleted_at ON carousels(deleted_at);
```