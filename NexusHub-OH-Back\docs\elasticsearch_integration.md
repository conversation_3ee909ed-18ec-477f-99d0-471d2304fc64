# Elasticsearch 集成指南

本文档说明如何在 NexusHub-OH-Back 项目中集成 Elasticsearch 搜索功能。

## 概述

已为用户、开发者认证管理、评论和标签功能添加了 Elasticsearch 支持，包括：

- **用户搜索服务** (`UserSearchService`)
- **评论搜索服务** (`ReviewSearchService`) 
- **标签搜索服务** (`TagSearchService`)

## 新增文件

### 1. 用户搜索服务
- `internal/services/user_search_service.go` - 用户搜索服务实现

### 2. 评论搜索服务
- `internal/services/review_search_service.go` - 评论搜索服务实现

### 3. 标签搜索服务
- `internal/services/tag_search_service.go` - 标签搜索服务实现

## 新增 API 端点

### 公共搜索接口
- `GET /api/v1/search/reviews` - 搜索评论
- `GET /api/v1/search/tags` - 搜索标签
- `GET /api/v1/search/tags/suggest` - 标签自动补全

### 管理员专用接口
- `GET /admin/search/users` - 搜索用户（管理员功能）
- `GET /admin/search/tags/stats` - 获取标签统计（管理员功能）
- `POST /admin/search/initialize-all` - 初始化所有搜索索引
- `POST /admin/search/sync` - 同步所有数据到 Elasticsearch

## 集成步骤

### 1. 在现有控制器中添加索引更新

需要在以下操作中添加 Elasticsearch 索引更新：

#### 用户相关操作
```go
// 在用户注册成功后
if userSearchService != nil {
    if err := userSearchService.IndexUser(ctx, &user); err != nil {
        logger.Warn("索引用户到Elasticsearch失败", zap.Error(err))
    }
}

// 在用户信息更新后
if userSearchService != nil {
    if err := userSearchService.IndexUser(ctx, &user); err != nil {
        logger.Warn("更新用户索引失败", zap.Error(err))
    }
}
```

#### 评论相关操作
```go
// 在评论创建后
if reviewSearchService != nil {
    if err := reviewSearchService.IndexReview(ctx, &review); err != nil {
        logger.Warn("索引评论到Elasticsearch失败", zap.Error(err))
    }
}

// 在评论更新后
if reviewSearchService != nil {
    if err := reviewSearchService.IndexReview(ctx, &review); err != nil {
        logger.Warn("更新评论索引失败", zap.Error(err))
    }
}

// 在评论删除后
if reviewSearchService != nil {
    if err := reviewSearchService.DeleteReview(ctx, reviewID); err != nil {
        logger.Warn("删除评论索引失败", zap.Error(err))
    }
}
```

#### 标签相关操作
```go
// 在标签创建后
if tagSearchService != nil {
    if err := tagSearchService.IndexTag(ctx, &tag); err != nil {
        logger.Warn("索引标签到Elasticsearch失败", zap.Error(err))
    }
}

// 在标签更新后
if tagSearchService != nil {
    if err := tagSearchService.IndexTag(ctx, &tag); err != nil {
        logger.Warn("更新标签索引失败", zap.Error(err))
    }
}

// 在标签删除后
if tagSearchService != nil {
    if err := tagSearchService.DeleteTag(ctx, tagID); err != nil {
        logger.Warn("删除标签索引失败", zap.Error(err))
    }
}
```

### 2. 修改控制器构造函数

需要在相关控制器中添加搜索服务依赖：

```go
// UserController
type UserController struct {
    DB                *gorm.DB
    JWTService        *auth.JWTService
    UserSearchService *services.UserSearchService
    Validate          *validator.Validate
}

// ReviewController
type ReviewController struct {
    DB                  *gorm.DB
    ReviewSearchService *services.ReviewSearchService
}

// TagController
type TagController struct {
    DB               *gorm.DB
    TagSearchService *services.TagSearchService
}
```

### 3. 更新路由初始化

在 `router.go` 中更新控制器初始化：

```go
userController := NewUserController(gormDB, jwtService, userSearchService)
reviewController := NewReviewController(gormDB, reviewSearchService)
tagController := NewTagController(gormDB, tagSearchService)
```

## 索引结构

### 用户索引 (nexushub_users)
- 用户基本信息（用户名、邮箱、电话）
- 开发者信息（开发者名称、公司名称、认证状态）
- 用户状态和角色信息
- 登录统计信息

### 评论索引 (nexushub_reviews)
- 评论内容和评分
- 用户和应用关联信息
- 评论状态和点赞数
- 开发者回复信息

### 标签索引 (nexushub_tags)
- 标签名称和描述
- 标签颜色和状态
- 关联应用数量统计

## 初始化和同步

### 首次部署
1. 调用 `POST /admin/search/initialize-all` 初始化所有索引
2. 调用 `POST /admin/search/sync` 同步现有数据

### 日常维护
- 定期调用同步接口确保数据一致性
- 监控 Elasticsearch 集群状态
- 根据需要调整索引映射和设置

## 搜索功能

### 用户搜索
- 支持用户名、邮箱、开发者信息搜索
- 支持角色、状态、认证状态筛选
- 支持多种排序方式

### 评论搜索
- 支持评论内容关键词搜索
- 支持按用户、应用、评分筛选
- 支持按时间、评分、点赞数排序

### 标签搜索
- 支持标签名称和描述搜索
- 提供自动补全功能
- 支持标签使用统计查询

## 注意事项

1. **错误处理**：Elasticsearch 操作失败不应影响主要业务流程
2. **性能考虑**：大批量操作时考虑使用批量索引接口
3. **数据一致性**：定期同步确保 Elasticsearch 与数据库数据一致
4. **权限控制**：用户搜索等敏感功能仅限管理员访问
5. **监控告警**：建议对 Elasticsearch 集群进行监控和告警配置