// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/**
 * 获取文件上传凭证
 * @param params - 上传参数
 */
export async function getUploadToken(params: {
  file_type: 'avatar' | 'license' | 'identity' | 'screenshot' | 'package';
  file_name: string;
}) {
  return request('/upload/token', {
    method: 'GET',
    params,
  });
}

/**
 * 上传文件到预签名URL
 * @param signedUrl - 预签名URL
 * @param file - 要上传的文件
 * @returns 上传后的文件URL
 */
export async function uploadToSignedUrl(signedUrl: string, file: File): Promise<string> {
  try {
    // 直接使用fetch API上传到预签名URL
    const response = await fetch(signedUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': file.type,
      },
      body: file,
    });

    if (!response.ok) {
      throw new Error(`上传失败，状态码: ${response.status}`);
    }

    // 从签名URL中提取文件访问路径（去除查询参数）
    const fileUrl = signedUrl.split('?')[0];
    return fileUrl;
  } catch (error) {
    console.error('文件上传错误:', error);
    throw error;
  }
}

/**
 * 组合函数：获取上传Token并上传文件
 * @param fileType - 文件类型
 * @param file - 文件对象
 * @returns 上传后的文件URL
 */
export async function uploadFile(
  fileType: 'avatar' | 'license' | 'identity' | 'screenshot' | 'package',
  file: File
): Promise<string> {
  try {
    // 1. 获取上传token
    const response = await getUploadToken({
      file_type: fileType,
      file_name: file.name,
    });

    if (response.code !== 200 || !response.data?.file_url) {
      throw new Error('获取上传凭证失败');
    }

    // 2. 上传文件到预签名URL
    const fileUrl = await uploadToSignedUrl(response.data.file_url, file);
    return fileUrl;
  } catch (error) {
    console.error('文件上传流程错误:', error);
    throw error;
  }
} 