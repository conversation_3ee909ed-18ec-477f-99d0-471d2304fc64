{"version": "2.0", "ppid": 32784, "events": [{"head": {"id": "a0a65610-3465-4be2-99f2-b54b38c6b790", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837362882100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51f21cf3-6620-4ca0-9351-b8a70f100d6d", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837363432000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ace69c2-cdd8-4ba1-8b0a-0cab7cd37a49", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837365511100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0e9a1c8-1289-4e9c-a2b1-4b7d53f8b2ca", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837365931200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ed4542c-dd8d-4a1e-a48b-141d796b3d05", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837368743500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88165ad2-5aca-4257-84f6-6e79f1527f59", "name": "worker[18] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837369258100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d6c74dd-a023-450b-930b-452ccc1aada7", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837369711100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8de19bb6-dd24-4f23-9729-9a54d661c19e", "name": "worker[4] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837411378900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6f06076-5955-42c4-8602-421801d245a3", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897246269400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68347f9c-70ff-4bf2-95f7-2166d8aef552", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897254906800, "endTime": 222897710452100}, "additional": {"children": ["5ea13eb5-476a-44b2-b3a4-e59d94f30266", "319ba27f-c5e7-40fe-9b3b-e39094bff505", "0c377888-e552-4038-96f7-c17b701c0e80", "b8894743-4ef0-4540-b10e-be0bd2eaa014", "6c0a1e66-cd75-4209-ba34-22f5ee2a5a95", "b4817620-ea25-4748-a563-4ad3fdf9b640", "ff52dc85-25c9-4ec2-94bf-febd0ebad7c7"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "25c8f425-8481-4396-b36c-af862d10c8e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ea13eb5-476a-44b2-b3a4-e59d94f30266", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897254908200, "endTime": 222897275676100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "68347f9c-70ff-4bf2-95f7-2166d8aef552", "logId": "03d415bd-821f-41a0-b70c-0c6213ff0c1b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "319ba27f-c5e7-40fe-9b3b-e39094bff505", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897275701100, "endTime": 222897708043500}, "additional": {"children": ["7a1f1565-a940-4a16-b4e8-8fc48417cd01", "dbcc9a4e-3926-455d-bf2e-6ec4a2e2ecd3", "70a2c013-88b6-4521-8f22-6b71b548358b", "984e8d0e-8c4b-433c-a9da-be496e3c791c", "327c9dbf-6c6d-48ce-9e30-6cf961ae3ef3", "765a0216-a40f-4a60-8430-af03c10d0332", "0a7e8355-e619-47ad-b11d-27d745224000", "99a5a164-bc1e-41d9-b1fb-f61d98f0d71d", "c9237298-db8d-4a2e-9f6e-d1168fa44d11"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "68347f9c-70ff-4bf2-95f7-2166d8aef552", "logId": "99ff1c69-71de-4d45-bbeb-92dfef3dcabe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c377888-e552-4038-96f7-c17b701c0e80", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897708079000, "endTime": 222897710396200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "68347f9c-70ff-4bf2-95f7-2166d8aef552", "logId": "0df15de6-08ca-421f-aa4a-d18c858205bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8894743-4ef0-4540-b10e-be0bd2eaa014", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897710405500, "endTime": 222897710445000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "68347f9c-70ff-4bf2-95f7-2166d8aef552", "logId": "79bce744-2d2d-4de6-8924-0543793c45e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c0a1e66-cd75-4209-ba34-22f5ee2a5a95", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897261161900, "endTime": 222897261207300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "68347f9c-70ff-4bf2-95f7-2166d8aef552", "logId": "d1c8ac24-1629-4aac-8dd5-d9cfd3c5020c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d1c8ac24-1629-4aac-8dd5-d9cfd3c5020c", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897261161900, "endTime": 222897261207300}, "additional": {"logType": "info", "children": [], "durationId": "6c0a1e66-cd75-4209-ba34-22f5ee2a5a95", "parent": "25c8f425-8481-4396-b36c-af862d10c8e2"}}, {"head": {"id": "b4817620-ea25-4748-a563-4ad3fdf9b640", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897267872500, "endTime": 222897267911300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "68347f9c-70ff-4bf2-95f7-2166d8aef552", "logId": "9bea9df4-95b9-4f9d-91aa-4ce37a418999"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9bea9df4-95b9-4f9d-91aa-4ce37a418999", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897267872500, "endTime": 222897267911300}, "additional": {"logType": "info", "children": [], "durationId": "b4817620-ea25-4748-a563-4ad3fdf9b640", "parent": "25c8f425-8481-4396-b36c-af862d10c8e2"}}, {"head": {"id": "3a178871-587e-4998-ae35-1df022babf50", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897268772200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41d4875b-8bea-4c2d-8136-16243ddb4387", "name": "Cache service initialization finished in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897275505600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03d415bd-821f-41a0-b70c-0c6213ff0c1b", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897254908200, "endTime": 222897275676100}, "additional": {"logType": "info", "children": [], "durationId": "5ea13eb5-476a-44b2-b3a4-e59d94f30266", "parent": "25c8f425-8481-4396-b36c-af862d10c8e2"}}, {"head": {"id": "7a1f1565-a940-4a16-b4e8-8fc48417cd01", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897282687600, "endTime": 222897282701100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "319ba27f-c5e7-40fe-9b3b-e39094bff505", "logId": "16349428-a27d-4dd8-8d95-1ca6b3c8a5aa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dbcc9a4e-3926-455d-bf2e-6ec4a2e2ecd3", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897282841800, "endTime": 222897289780800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "319ba27f-c5e7-40fe-9b3b-e39094bff505", "logId": "379d8803-9575-4ba7-883f-3aa2339fefb3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "70a2c013-88b6-4521-8f22-6b71b548358b", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897289796600, "endTime": 222897514081500}, "additional": {"children": ["36033966-7c3f-4d47-b98d-17214089dffa", "e278d4c6-1f2a-4b37-8460-12c4ca68d054", "17888542-889a-4f7b-8c4b-eab8fae23467"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "319ba27f-c5e7-40fe-9b3b-e39094bff505", "logId": "511dec0d-74ba-4bd9-a6ca-6cdf00863520"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "984e8d0e-8c4b-433c-a9da-be496e3c791c", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897514100800, "endTime": 222897567522700}, "additional": {"children": ["b94bbff1-b866-4b06-9a3e-7b0a88851506"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "319ba27f-c5e7-40fe-9b3b-e39094bff505", "logId": "6dad7adb-b452-4b78-b2d6-ec4856886ba6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "327c9dbf-6c6d-48ce-9e30-6cf961ae3ef3", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897567671200, "endTime": 222897649238100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "319ba27f-c5e7-40fe-9b3b-e39094bff505", "logId": "3554068c-e858-4a44-8c72-ed31bd2d2b38"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "765a0216-a40f-4a60-8430-af03c10d0332", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897653200200, "endTime": 222897677890800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "319ba27f-c5e7-40fe-9b3b-e39094bff505", "logId": "07d732f9-a7f3-409c-90eb-46b82fc7bae4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a7e8355-e619-47ad-b11d-27d745224000", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897677941400, "endTime": 222897707776400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "319ba27f-c5e7-40fe-9b3b-e39094bff505", "logId": "e0558d28-4628-4342-8243-ed2baf4affeb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "99a5a164-bc1e-41d9-b1fb-f61d98f0d71d", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897707813300, "endTime": 222897708024000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "319ba27f-c5e7-40fe-9b3b-e39094bff505", "logId": "000be674-bb69-4611-8093-bb1164df729f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16349428-a27d-4dd8-8d95-1ca6b3c8a5aa", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897282687600, "endTime": 222897282701100}, "additional": {"logType": "info", "children": [], "durationId": "7a1f1565-a940-4a16-b4e8-8fc48417cd01", "parent": "99ff1c69-71de-4d45-bbeb-92dfef3dcabe"}}, {"head": {"id": "379d8803-9575-4ba7-883f-3aa2339fefb3", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897282841800, "endTime": 222897289780800}, "additional": {"logType": "info", "children": [], "durationId": "dbcc9a4e-3926-455d-bf2e-6ec4a2e2ecd3", "parent": "99ff1c69-71de-4d45-bbeb-92dfef3dcabe"}}, {"head": {"id": "36033966-7c3f-4d47-b98d-17214089dffa", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897290521100, "endTime": 222897290556700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "70a2c013-88b6-4521-8f22-6b71b548358b", "logId": "902b9a95-cf8b-4bcd-833a-fb917f840790"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "902b9a95-cf8b-4bcd-833a-fb917f840790", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897290521100, "endTime": 222897290556700}, "additional": {"logType": "info", "children": [], "durationId": "36033966-7c3f-4d47-b98d-17214089dffa", "parent": "511dec0d-74ba-4bd9-a6ca-6cdf00863520"}}, {"head": {"id": "e278d4c6-1f2a-4b37-8460-12c4ca68d054", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897293447800, "endTime": 222897512960700}, "additional": {"children": ["1ee3da20-7b11-4292-8018-2cc9e7daacdb", "3d57d882-c259-47b4-b2e3-cde10490972e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "70a2c013-88b6-4521-8f22-6b71b548358b", "logId": "bcd28ed0-e903-4254-9bcc-1756d3c927d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ee3da20-7b11-4292-8018-2cc9e7daacdb", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897293450100, "endTime": 222897363422200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e278d4c6-1f2a-4b37-8460-12c4ca68d054", "logId": "1e410ba3-afb3-448b-80d2-22ec89fef250"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d57d882-c259-47b4-b2e3-cde10490972e", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897363445200, "endTime": 222897512942500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e278d4c6-1f2a-4b37-8460-12c4ca68d054", "logId": "8de9cbb9-dfe5-4a71-a254-a73d31ebcc1a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f6149e39-5a3a-408e-abab-6561e28a3f00", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897293463200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4eb4a460-790d-451e-8726-20ccafbad0b8", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897363238200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e410ba3-afb3-448b-80d2-22ec89fef250", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897293450100, "endTime": 222897363422200}, "additional": {"logType": "info", "children": [], "durationId": "1ee3da20-7b11-4292-8018-2cc9e7daacdb", "parent": "bcd28ed0-e903-4254-9bcc-1756d3c927d7"}}, {"head": {"id": "21047599-5125-408b-a300-0b017c72c134", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897363493800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0286885-eba7-4507-a8f6-0bbf0a949733", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897401705000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebe4ca12-579b-42d9-8aee-425c88624eb7", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897402208700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9390bff4-25c5-499d-aef8-645c3f038e8a", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897402434900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "976aa077-381d-4ba3-8b03-13933e83f14f", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897402628400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8d5e6c6-4a21-4cf8-8b01-3d46102c63b3", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897405292200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c4c7eb0-da0d-42be-88b9-49931b7a0a31", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897433763000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f59b8ea1-4046-4671-a888-495fa2d8db81", "name": "Sdk init in 52 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897471919800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb306ac1-238d-4780-be09-46c5b6326009", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897472192200}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 19, "minute": 52, "second": 7}, "markType": "other"}}, {"head": {"id": "4e11e983-0ea7-400c-b6c7-ef8248019c8c", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897472210000}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 19, "minute": 52, "second": 7}, "markType": "other"}}, {"head": {"id": "06c3c494-371f-48ea-aced-89804737210f", "name": "Project task initialization takes 38 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897512525500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79496e3f-4358-424a-9618-465414618b00", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897512698100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31c24f21-1d8d-45b3-84e3-14fdc8317088", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897512770800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "602771a9-b73c-4518-954e-cb242ee35b58", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897512826600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8de9cbb9-dfe5-4a71-a254-a73d31ebcc1a", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897363445200, "endTime": 222897512942500}, "additional": {"logType": "info", "children": [], "durationId": "3d57d882-c259-47b4-b2e3-cde10490972e", "parent": "bcd28ed0-e903-4254-9bcc-1756d3c927d7"}}, {"head": {"id": "bcd28ed0-e903-4254-9bcc-1756d3c927d7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897293447800, "endTime": 222897512960700}, "additional": {"logType": "info", "children": ["1e410ba3-afb3-448b-80d2-22ec89fef250", "8de9cbb9-dfe5-4a71-a254-a73d31ebcc1a"], "durationId": "e278d4c6-1f2a-4b37-8460-12c4ca68d054", "parent": "511dec0d-74ba-4bd9-a6ca-6cdf00863520"}}, {"head": {"id": "17888542-889a-4f7b-8c4b-eab8fae23467", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897513872500, "endTime": 222897514056000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "70a2c013-88b6-4521-8f22-6b71b548358b", "logId": "a37a6114-d4b8-4936-8ea5-61ad082c8a62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a37a6114-d4b8-4936-8ea5-61ad082c8a62", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897513872500, "endTime": 222897514056000}, "additional": {"logType": "info", "children": [], "durationId": "17888542-889a-4f7b-8c4b-eab8fae23467", "parent": "511dec0d-74ba-4bd9-a6ca-6cdf00863520"}}, {"head": {"id": "511dec0d-74ba-4bd9-a6ca-6cdf00863520", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897289796600, "endTime": 222897514081500}, "additional": {"logType": "info", "children": ["902b9a95-cf8b-4bcd-833a-fb917f840790", "bcd28ed0-e903-4254-9bcc-1756d3c927d7", "a37a6114-d4b8-4936-8ea5-61ad082c8a62"], "durationId": "70a2c013-88b6-4521-8f22-6b71b548358b", "parent": "99ff1c69-71de-4d45-bbeb-92dfef3dcabe"}}, {"head": {"id": "b94bbff1-b866-4b06-9a3e-7b0a88851506", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897514971700, "endTime": 222897567500300}, "additional": {"children": ["a1bc30d4-5d84-422c-9ac2-e82ea41a2298", "d4eab648-84de-4c37-8142-da6fbb561645", "be0bf4a1-fc7d-4288-b776-40f98c569325"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "984e8d0e-8c4b-433c-a9da-be496e3c791c", "logId": "6fe8265b-9878-43f9-8903-4c851f32a5ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1bc30d4-5d84-422c-9ac2-e82ea41a2298", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897520015500, "endTime": 222897520060400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b94bbff1-b866-4b06-9a3e-7b0a88851506", "logId": "b11a48c4-adec-449a-9550-d82dab8c99cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b11a48c4-adec-449a-9550-d82dab8c99cb", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897520015500, "endTime": 222897520060400}, "additional": {"logType": "info", "children": [], "durationId": "a1bc30d4-5d84-422c-9ac2-e82ea41a2298", "parent": "6fe8265b-9878-43f9-8903-4c851f32a5ed"}}, {"head": {"id": "d4eab648-84de-4c37-8142-da6fbb561645", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897522783900, "endTime": 222897565061800}, "additional": {"children": ["4982931c-2ec6-4062-8230-a91d27d81b5c", "cd44ba44-5c2c-470e-9a7a-43cc069c920a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b94bbff1-b866-4b06-9a3e-7b0a88851506", "logId": "9dc3b7b4-6cfd-404a-9524-a85638f20462"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4982931c-2ec6-4062-8230-a91d27d81b5c", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897522785200, "endTime": 222897527713700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d4eab648-84de-4c37-8142-da6fbb561645", "logId": "f0ec5be4-f089-4f66-ba13-7ba95498ea3c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd44ba44-5c2c-470e-9a7a-43cc069c920a", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897527745700, "endTime": 222897565046200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d4eab648-84de-4c37-8142-da6fbb561645", "logId": "81f62879-7c58-42eb-b40f-5c1403a8a9b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2995c92e-c0d5-43b6-9d19-6de61d177059", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897522794200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f98725c6-1286-4899-b4d1-5238f33928a6", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897526673400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0ec5be4-f089-4f66-ba13-7ba95498ea3c", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897522785200, "endTime": 222897527713700}, "additional": {"logType": "info", "children": [], "durationId": "4982931c-2ec6-4062-8230-a91d27d81b5c", "parent": "9dc3b7b4-6cfd-404a-9524-a85638f20462"}}, {"head": {"id": "f42f5efa-41cc-4c1a-ab1e-937b36392e0c", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897527776600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86c63958-dea0-4b3e-9687-b5c1c9b266ca", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897553755900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d856b18-7471-474b-8594-c6b362a05c32", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897553975000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4331f7b6-37b1-4405-977a-2fddd6570fa2", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897554223100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f1c50b3-e675-4484-ac03-e0b6ccda0f89", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897554354500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7b54239-668e-4d95-bb38-3aa34864f100", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897554413100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7e934fd-de54-46a7-b554-2f88b25f3a53", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897554463400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "454596c8-cc6f-45c0-bcc2-7ec8655485f6", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897554525600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b82dcc63-0445-4e55-a2d6-c54e4f1c7bc3", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897554570400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33e7d21a-a286-4fdb-bf58-4d29070468f9", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897554792600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbb1eaa2-80e7-44a1-b0f0-6ab93837d51f", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897554929600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00346d9d-c47c-4334-a481-0641f88409f3", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897554993100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e27e02c-6712-463a-97d3-46e5bd840a4d", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897555055400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c16956a3-2b39-43a5-a896-205ff27e608f", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897555126200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52ea42f3-f69a-4c13-877f-6c28229d231b", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897555170600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74fee7ee-b103-434b-ad9e-2c5dc2e11b47", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897555279300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "256ce056-59dc-41d3-a3d4-695de78f54b1", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897555366100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94a8e953-881b-4b12-8703-d10f4e504e99", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897555468200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6eb9e03-5200-49cf-8882-d2a946f0c5c1", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897555518500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "928b6cae-79db-4a75-bd24-8596872ffbde", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897555985700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59c0f9ad-1b83-4ec1-bb04-6b6553f24245", "name": "Module entry task initialization takes 5 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897564564000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81567ed2-5215-4693-ae52-5b7dfc085e85", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897564864400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d5bedf6-b9e5-4f91-b18b-0ef191605dbc", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897564953600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5ca542b-5e8a-412a-a34e-01463adf5386", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897564999800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81f62879-7c58-42eb-b40f-5c1403a8a9b6", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897527745700, "endTime": 222897565046200}, "additional": {"logType": "info", "children": [], "durationId": "cd44ba44-5c2c-470e-9a7a-43cc069c920a", "parent": "9dc3b7b4-6cfd-404a-9524-a85638f20462"}}, {"head": {"id": "9dc3b7b4-6cfd-404a-9524-a85638f20462", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897522783900, "endTime": 222897565061800}, "additional": {"logType": "info", "children": ["f0ec5be4-f089-4f66-ba13-7ba95498ea3c", "81f62879-7c58-42eb-b40f-5c1403a8a9b6"], "durationId": "d4eab648-84de-4c37-8142-da6fbb561645", "parent": "6fe8265b-9878-43f9-8903-4c851f32a5ed"}}, {"head": {"id": "be0bf4a1-fc7d-4288-b776-40f98c569325", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897567398700, "endTime": 222897567445600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b94bbff1-b866-4b06-9a3e-7b0a88851506", "logId": "ef533050-dca8-4771-bc1e-7b8faaae5d83"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef533050-dca8-4771-bc1e-7b8faaae5d83", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897567398700, "endTime": 222897567445600}, "additional": {"logType": "info", "children": [], "durationId": "be0bf4a1-fc7d-4288-b776-40f98c569325", "parent": "6fe8265b-9878-43f9-8903-4c851f32a5ed"}}, {"head": {"id": "6fe8265b-9878-43f9-8903-4c851f32a5ed", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897514971700, "endTime": 222897567500300}, "additional": {"logType": "info", "children": ["b11a48c4-adec-449a-9550-d82dab8c99cb", "9dc3b7b4-6cfd-404a-9524-a85638f20462", "ef533050-dca8-4771-bc1e-7b8faaae5d83"], "durationId": "b94bbff1-b866-4b06-9a3e-7b0a88851506", "parent": "6dad7adb-b452-4b78-b2d6-ec4856886ba6"}}, {"head": {"id": "6dad7adb-b452-4b78-b2d6-ec4856886ba6", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897514100800, "endTime": 222897567522700}, "additional": {"logType": "info", "children": ["6fe8265b-9878-43f9-8903-4c851f32a5ed"], "durationId": "984e8d0e-8c4b-433c-a9da-be496e3c791c", "parent": "99ff1c69-71de-4d45-bbeb-92dfef3dcabe"}}, {"head": {"id": "bf0ae0ef-0427-43ba-bec3-6e42c1be69c2", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897590272000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dd573c0-af2e-48e7-b6af-cb210309ecb9", "name": "hvigorfile, resolve hvigorfile dependencies in 82 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897649040700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3554068c-e858-4a44-8c72-ed31bd2d2b38", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897567671200, "endTime": 222897649238100}, "additional": {"logType": "info", "children": [], "durationId": "327c9dbf-6c6d-48ce-9e30-6cf961ae3ef3", "parent": "99ff1c69-71de-4d45-bbeb-92dfef3dcabe"}}, {"head": {"id": "c9237298-db8d-4a2e-9f6e-d1168fa44d11", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897650614100, "endTime": 222897653155700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "319ba27f-c5e7-40fe-9b3b-e39094bff505", "logId": "f6290cc5-39fe-4b96-9816-853fc197d345"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2020fe77-40be-4414-a561-20052582fd49", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897651975600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6290cc5-39fe-4b96-9816-853fc197d345", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897650614100, "endTime": 222897653155700}, "additional": {"logType": "info", "children": [], "durationId": "c9237298-db8d-4a2e-9f6e-d1168fa44d11", "parent": "99ff1c69-71de-4d45-bbeb-92dfef3dcabe"}}, {"head": {"id": "d4e516c4-548d-4038-9cb7-e1aff2f385af", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897658274200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99eeca27-f94e-4f8b-8f57-4c2abe2ed971", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897673878900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07d732f9-a7f3-409c-90eb-46b82fc7bae4", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897653200200, "endTime": 222897677890800}, "additional": {"logType": "info", "children": [], "durationId": "765a0216-a40f-4a60-8430-af03c10d0332", "parent": "99ff1c69-71de-4d45-bbeb-92dfef3dcabe"}}, {"head": {"id": "353ab985-a3a1-4873-ac96-765cfdc0be79", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897678158900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6686f552-124c-44d0-8e64-3d497fdaa4cb", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897693441700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b831286-a4bf-4bbb-bb73-788bf53c2511", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897693755400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbe95112-abb4-42de-bba5-c3ef1b5c94b2", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897694979300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdb1f45c-387e-46cd-b954-8be146871dae", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897699372100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5621daf0-3c81-4381-80f3-d108200dfbb5", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897699549800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0558d28-4628-4342-8243-ed2baf4affeb", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897677941400, "endTime": 222897707776400}, "additional": {"logType": "info", "children": [], "durationId": "0a7e8355-e619-47ad-b11d-27d745224000", "parent": "99ff1c69-71de-4d45-bbeb-92dfef3dcabe"}}, {"head": {"id": "a0f0e41a-a185-4443-8682-a2c619e1d927", "name": "Configuration phase cost:426 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897707854500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "000be674-bb69-4611-8093-bb1164df729f", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897707813300, "endTime": 222897708024000}, "additional": {"logType": "info", "children": [], "durationId": "99a5a164-bc1e-41d9-b1fb-f61d98f0d71d", "parent": "99ff1c69-71de-4d45-bbeb-92dfef3dcabe"}}, {"head": {"id": "99ff1c69-71de-4d45-bbeb-92dfef3dcabe", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897275701100, "endTime": 222897708043500}, "additional": {"logType": "info", "children": ["16349428-a27d-4dd8-8d95-1ca6b3c8a5aa", "379d8803-9575-4ba7-883f-3aa2339fefb3", "511dec0d-74ba-4bd9-a6ca-6cdf00863520", "6dad7adb-b452-4b78-b2d6-ec4856886ba6", "3554068c-e858-4a44-8c72-ed31bd2d2b38", "07d732f9-a7f3-409c-90eb-46b82fc7bae4", "e0558d28-4628-4342-8243-ed2baf4affeb", "000be674-bb69-4611-8093-bb1164df729f", "f6290cc5-39fe-4b96-9816-853fc197d345"], "durationId": "319ba27f-c5e7-40fe-9b3b-e39094bff505", "parent": "25c8f425-8481-4396-b36c-af862d10c8e2"}}, {"head": {"id": "ff52dc85-25c9-4ec2-94bf-febd0ebad7c7", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897710335800, "endTime": 222897710368800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "68347f9c-70ff-4bf2-95f7-2166d8aef552", "logId": "253d4796-3037-47ff-95b4-556c82f23b5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "253d4796-3037-47ff-95b4-556c82f23b5f", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897710335800, "endTime": 222897710368800}, "additional": {"logType": "info", "children": [], "durationId": "ff52dc85-25c9-4ec2-94bf-febd0ebad7c7", "parent": "25c8f425-8481-4396-b36c-af862d10c8e2"}}, {"head": {"id": "0df15de6-08ca-421f-aa4a-d18c858205bd", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897708079000, "endTime": 222897710396200}, "additional": {"logType": "info", "children": [], "durationId": "0c377888-e552-4038-96f7-c17b701c0e80", "parent": "25c8f425-8481-4396-b36c-af862d10c8e2"}}, {"head": {"id": "79bce744-2d2d-4de6-8924-0543793c45e0", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897710405500, "endTime": 222897710445000}, "additional": {"logType": "info", "children": [], "durationId": "b8894743-4ef0-4540-b10e-be0bd2eaa014", "parent": "25c8f425-8481-4396-b36c-af862d10c8e2"}}, {"head": {"id": "25c8f425-8481-4396-b36c-af862d10c8e2", "name": "init", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897254906800, "endTime": 222897710452100}, "additional": {"logType": "info", "children": ["03d415bd-821f-41a0-b70c-0c6213ff0c1b", "99ff1c69-71de-4d45-bbeb-92dfef3dcabe", "0df15de6-08ca-421f-aa4a-d18c858205bd", "79bce744-2d2d-4de6-8924-0543793c45e0", "d1c8ac24-1629-4aac-8dd5-d9cfd3c5020c", "9bea9df4-95b9-4f9d-91aa-4ce37a418999", "253d4796-3037-47ff-95b4-556c82f23b5f"], "durationId": "68347f9c-70ff-4bf2-95f7-2166d8aef552"}}, {"head": {"id": "9248df89-b85d-4fa1-9514-3672b51eca98", "name": "Configuration task cost before running: 460 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897711120500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3a65785-66c7-47a0-a236-40a7e205955c", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897730968100, "endTime": 222897756371300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "7b731bfd-ca56-4c22-b6e2-55a42bb58bdc", "logId": "b446a7f7-9e0b-43b4-81e4-2589ea926612"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b731bfd-ca56-4c22-b6e2-55a42bb58bdc", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897714885900}, "additional": {"logType": "detail", "children": [], "durationId": "a3a65785-66c7-47a0-a236-40a7e205955c"}}, {"head": {"id": "fc20762a-d9ec-49e7-9ad1-30f9169c478e", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897715893400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e81237d-7827-4592-835f-fe379dc66a25", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897716225700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43188e89-ca75-462e-9cbb-c9ea9e88cf31", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897717781300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85a40b06-20b0-4130-afe0-5843b2744f9b", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897719477600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d0b034d-8672-41fb-bf55-a1296d75d7d6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897721671800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc14f37d-2cf0-4649-bf05-064a2950c8f6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897721873800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa3f7bbf-39cf-42cc-a2b7-c5ffe8713f68", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897730990100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34976625-5a4d-480b-b868-9af414f53536", "name": "Incremental task entry:default@PreBuild pre-execution cost: 18 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897755957000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4445c6b-43a7-4e61-9bbb-0da62cc7b49e", "name": "entry : default@PreBuild cost memory 0.3466949462890625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897756237000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b446a7f7-9e0b-43b4-81e4-2589ea926612", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897730968100, "endTime": 222897756371300}, "additional": {"logType": "info", "children": [], "durationId": "a3a65785-66c7-47a0-a236-40a7e205955c"}}, {"head": {"id": "63f4d010-c2ec-4f40-97eb-2b175c2a9f65", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897811676100, "endTime": 222897816479000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7607792b-a477-4e6e-aa91-dec80b50a7be", "logId": "978ee904-9761-4abc-9798-30c207e6e3ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7607792b-a477-4e6e-aa91-dec80b50a7be", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897806551600}, "additional": {"logType": "detail", "children": [], "durationId": "63f4d010-c2ec-4f40-97eb-2b175c2a9f65"}}, {"head": {"id": "0855ba7e-cb8e-429c-85a5-a4a90f5d8480", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897808799900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61190236-f325-4ac1-8052-0f109e922fce", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897809095900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6e424d7-eba2-43c2-8852-f3d50f856a9f", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897811726500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "336ab690-e4a3-4c04-beef-8f3bba580a54", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897814231900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cf2f20a-e7dd-4aae-9c5d-2802de2e73ac", "name": "entry : default@CreateModuleInfo cost memory 0.061431884765625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897816123800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "030e795d-f2af-40cc-918c-db0d54c579e8", "name": "runTaskFromQueue task cost before running: 565 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897816373900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "978ee904-9761-4abc-9798-30c207e6e3ae", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897811676100, "endTime": 222897816479000, "totalTime": 4664200}, "additional": {"logType": "info", "children": [], "durationId": "63f4d010-c2ec-4f40-97eb-2b175c2a9f65"}}, {"head": {"id": "b3b8f08d-7065-4628-b369-8525d938680f", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897838011300, "endTime": 222897844463400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1b4b8b14-fb5a-488d-9f35-edf53722aace", "logId": "95005dbe-cbbf-45ea-8720-f90a9f960f31"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1b4b8b14-fb5a-488d-9f35-edf53722aace", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897821901900}, "additional": {"logType": "detail", "children": [], "durationId": "b3b8f08d-7065-4628-b369-8525d938680f"}}, {"head": {"id": "3cab4183-433c-4e4c-a842-704bf64f37dd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897824073000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18d0b524-429e-4871-ac99-2401431da7be", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897825214600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1ea12d5-725c-4512-9d88-a775579f058f", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897838048100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1bb44af-b8d9-45a9-984b-1bf9337d092a", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897840336200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e20bd0a6-d716-4eaf-a182-8807464926f6", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897842906300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10dcfea2-802f-4ec2-820b-322aa70d6f5f", "name": "entry : default@GenerateMetadata cost memory 0.17458343505859375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897843344600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95005dbe-cbbf-45ea-8720-f90a9f960f31", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897838011300, "endTime": 222897844463400}, "additional": {"logType": "info", "children": [], "durationId": "b3b8f08d-7065-4628-b369-8525d938680f"}}, {"head": {"id": "c023bca6-8ad7-45d6-9760-0e7394561019", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897857327100, "endTime": 222897859370600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "3642601e-3285-4e45-b446-7bff33f220ed", "logId": "98fafe78-d568-492f-808f-04fffd264916"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3642601e-3285-4e45-b446-7bff33f220ed", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897852891000}, "additional": {"logType": "detail", "children": [], "durationId": "c023bca6-8ad7-45d6-9760-0e7394561019"}}, {"head": {"id": "f5cdc85f-07d9-4f0a-ba71-d475dcbb87ed", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897856651900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48ffcdab-9265-406b-a6dc-46e2a0a94056", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897856869800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "936ab1cc-20bf-46b4-a5db-d26506a945b0", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897857346200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c955cfb8-e209-493e-838e-d97886808c50", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897857875200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "881d9200-fab6-4515-81ca-c9ccbd567472", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897858448700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca94adac-ac4c-4a27-9ceb-babb05e7d560", "name": "entry : default@ConfigureCmake cost memory 0.0374755859375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897858749200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da6fbb60-f2c4-4a44-b2c9-5a84cf9a265d", "name": "runTaskFromQueue task cost before running: 608 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897859110400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98fafe78-d568-492f-808f-04fffd264916", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897857327100, "endTime": 222897859370600, "totalTime": 1616300}, "additional": {"logType": "info", "children": [], "durationId": "c023bca6-8ad7-45d6-9760-0e7394561019"}}, {"head": {"id": "049b6ef6-71ba-4f4e-b43f-4630abf26063", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897872009400, "endTime": 222897878696900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "cac78b4b-1bea-4e11-8e93-63a43bd60186", "logId": "1158a793-4140-4d30-8eba-4e01dd8a7bdb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cac78b4b-1bea-4e11-8e93-63a43bd60186", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897865609200}, "additional": {"logType": "detail", "children": [], "durationId": "049b6ef6-71ba-4f4e-b43f-4630abf26063"}}, {"head": {"id": "780c944e-25ba-45a6-8d72-cbf7670d8dd3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897868829500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d449ae01-0b86-4288-8bd9-aea06ec8a038", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897869271500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2dff5c0-45ef-4ff5-a85a-56e4789c9aca", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897872047300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fd59743-f396-468d-8fe3-020f369053dd", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897878242800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8600433-9ec7-45fd-b56a-a293e5fe5090", "name": "entry : default@MergeProfile cost memory 0.11850738525390625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897878548500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1158a793-4140-4d30-8eba-4e01dd8a7bdb", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897872009400, "endTime": 222897878696900}, "additional": {"logType": "info", "children": [], "durationId": "049b6ef6-71ba-4f4e-b43f-4630abf26063"}}, {"head": {"id": "d3bfe68a-0c42-4070-b762-bca9c3a1fdef", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897889526400, "endTime": 222897898852000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f544c9b9-8052-4eed-b096-348d5a28fa62", "logId": "8cab9bed-b825-4d26-88b7-dc98468cc628"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f544c9b9-8052-4eed-b096-348d5a28fa62", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897882464600}, "additional": {"logType": "detail", "children": [], "durationId": "d3bfe68a-0c42-4070-b762-bca9c3a1fdef"}}, {"head": {"id": "8542270c-9154-431d-8e90-2e8e8c640d0b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897885343800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75197252-d91d-411f-b938-4193273f5151", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897885732100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "733340d1-c0f3-431f-bebe-ef012a84a60e", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897889550900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41472676-d9d4-4ab2-94da-aae3c8baeb47", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 5 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897894746000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3229da76-b7a3-44bf-940b-cd2c94b9cd56", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897898230300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb98596c-012e-4856-a1cc-24e6d4fc31c9", "name": "entry : default@CreateBuildProfile cost memory 0.1082305908203125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897898548100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cab9bed-b825-4d26-88b7-dc98468cc628", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897889526400, "endTime": 222897898852000}, "additional": {"logType": "info", "children": [], "durationId": "d3bfe68a-0c42-4070-b762-bca9c3a1fdef"}}, {"head": {"id": "1e8c7cfb-9d4b-43ae-91d3-19d1b8c1fb9c", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897912969500, "endTime": 222897914499600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "c18d9fa2-29f5-41f0-9cd7-0b682bd862bd", "logId": "d3f2ca3d-66ae-414a-83aa-28d7358bafef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c18d9fa2-29f5-41f0-9cd7-0b682bd862bd", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897907185900}, "additional": {"logType": "detail", "children": [], "durationId": "1e8c7cfb-9d4b-43ae-91d3-19d1b8c1fb9c"}}, {"head": {"id": "0bf98e3a-8074-4a03-9b06-9c8b5df9746e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897910685500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74c860e5-24bf-493d-8549-24cb9da347a9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897910955400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f96384a-42a2-4529-945c-a7a9a487de9d", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897912997200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0194d003-29a4-4eb3-8ae3-f82485bd0692", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897913276600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d84e922-2612-4322-9728-f3adee8a81e8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897913407500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b38c2e5-32af-4fc7-81a2-52935eb260b3", "name": "entry : default@PreCheckSyscap cost memory 0.041107177734375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897914106700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92eb63eb-0600-49e3-8c51-eabba0812249", "name": "runTaskFromQueue task cost before running: 663 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897914363600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3f2ca3d-66ae-414a-83aa-28d7358bafef", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897912969500, "endTime": 222897914499600, "totalTime": 1345100}, "additional": {"logType": "info", "children": [], "durationId": "1e8c7cfb-9d4b-43ae-91d3-19d1b8c1fb9c"}}, {"head": {"id": "a367b1ee-8af7-41fe-be96-a19159e7202e", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897927546800, "endTime": 222897939321900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "cb071732-f0fc-4a3f-8138-6534aab88810", "logId": "46cadc8e-37bc-4298-a78b-2e6198c604db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cb071732-f0fc-4a3f-8138-6534aab88810", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897919885200}, "additional": {"logType": "detail", "children": [], "durationId": "a367b1ee-8af7-41fe-be96-a19159e7202e"}}, {"head": {"id": "9a3c60c3-78c1-4a0d-abb7-d1a4ebb1c5f7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897923380400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1926ec89-a17d-481b-b16e-686c6bf6246d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897923639300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cdd86c3-d330-4e22-9917-384edfc2dd8c", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897927954100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5757f3c-68cf-4d33-944b-f79c1e6ac5d9", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897937823900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f43a3dc-5c99-4eba-bc53-d3d31e9ec7f4", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897939016500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70d781cc-ccde-4e67-a5d9-3425beddd14f", "name": "entry : default@GeneratePkgContextInfo cost memory 0.43779754638671875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897939219700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46cadc8e-37bc-4298-a78b-2e6198c604db", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897927546800, "endTime": 222897939321900}, "additional": {"logType": "info", "children": [], "durationId": "a367b1ee-8af7-41fe-be96-a19159e7202e"}}, {"head": {"id": "8da31d6c-6aaf-4e12-a3e7-3aca8e01d57f", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897954164400, "endTime": 222897957892900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "771025e5-9ec7-470d-aa82-31588875ad73", "logId": "9b585f4b-e10c-4e80-a318-3d93f7ad58df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "771025e5-9ec7-470d-aa82-31588875ad73", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897941912400}, "additional": {"logType": "detail", "children": [], "durationId": "8da31d6c-6aaf-4e12-a3e7-3aca8e01d57f"}}, {"head": {"id": "e4ae966b-a4c7-40d3-a582-45b819882ced", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897944867400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26cbf9f7-bc7a-49f8-944d-3eda1ead8b55", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897945116900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b5d8e02-d947-4bc1-9fe3-4e5ae51a6de5", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897954200600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d965fea3-e4be-43c6-a9cb-acab40d0d184", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897957119200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "363ddab7-acb8-47ff-b2dd-7655771887ce", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897957320500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37145994-2d36-4381-bc80-fbee2f9ce425", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897957435600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77c7bc35-7435-48b8-8fb2-eee0ffe1084d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897957486800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5a06a0c-66a0-4dbc-a87b-852d798c13ca", "name": "entry : default@ProcessIntegratedHsp cost memory 0.12094879150390625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897957581800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24af43d8-43ce-4cda-982c-bc00eba80dd7", "name": "runTaskFromQueue task cost before running: 707 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897957663400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b585f4b-e10c-4e80-a318-3d93f7ad58df", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897954164400, "endTime": 222897957892900, "totalTime": 3489600}, "additional": {"logType": "info", "children": [], "durationId": "8da31d6c-6aaf-4e12-a3e7-3aca8e01d57f"}}, {"head": {"id": "709888ce-ea06-461a-af23-1a9971c5a54a", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897965298900, "endTime": 222897965873900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "921833e6-dc43-4708-9693-f8be7f89a6b7", "logId": "26cb82f3-13b2-448a-a5d0-ae4fa55faec6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "921833e6-dc43-4708-9693-f8be7f89a6b7", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897962884400}, "additional": {"logType": "detail", "children": [], "durationId": "709888ce-ea06-461a-af23-1a9971c5a54a"}}, {"head": {"id": "f4badbc6-3ea8-4e8b-bce2-86096fe6d0a6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897964238400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97026402-99dc-48f8-9203-73e186ca6a37", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897964392700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95d796e2-e4b6-4fa9-8189-e5c3b16b71b6", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897965316700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "732afdc1-841b-457e-8291-a733c2672eb1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897965489600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14dee51d-d705-4eb3-bba0-f033282a8d2a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897965549900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4eb4661-908a-41ab-995a-f2ed87d1acd3", "name": "entry : default@BuildNativeWithCmake cost memory 0.03852081298828125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897965665000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cfa4623-4b28-4a19-a2e6-15e0542965dd", "name": "runTaskFromQueue task cost before running: 715 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897965786500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26cb82f3-13b2-448a-a5d0-ae4fa55faec6", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897965298900, "endTime": 222897965873900, "totalTime": 451500}, "additional": {"logType": "info", "children": [], "durationId": "709888ce-ea06-461a-af23-1a9971c5a54a"}}, {"head": {"id": "e016efee-45c4-4552-9584-8130b09b76e7", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897974450400, "endTime": 222897983396700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "532eba9b-12da-4783-8dd2-31b136cfee04", "logId": "64c53f2d-eaef-4a31-b7e0-61abb7d941bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "532eba9b-12da-4783-8dd2-31b136cfee04", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897970900500}, "additional": {"logType": "detail", "children": [], "durationId": "e016efee-45c4-4552-9584-8130b09b76e7"}}, {"head": {"id": "11728b2c-f533-4045-a1db-4816ce0977b4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897972968300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2774c2b2-c3cd-41a2-8c45-656237552551", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897973194100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2940ab19-ac05-48e1-98d2-64c98c0083c3", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897974467500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddde05ac-0406-46e6-ad04-7d7fbb7cbb8b", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897983125300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2b593bc-a7e8-4dcb-bddc-47b82ff7e35b", "name": "entry : default@MakePackInfo cost memory 0.1641387939453125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897983309200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64c53f2d-eaef-4a31-b7e0-61abb7d941bc", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897974450400, "endTime": 222897983396700}, "additional": {"logType": "info", "children": [], "durationId": "e016efee-45c4-4552-9584-8130b09b76e7"}}, {"head": {"id": "0eea12c0-be00-4c22-b17b-a362fde9ddf7", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897992301800, "endTime": 222898002488300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "dfb6b5d5-a686-4326-a696-d501819a4824", "logId": "b1ebbdbb-3763-4d2d-a3f4-5ea3608f901b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dfb6b5d5-a686-4326-a696-d501819a4824", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897988837800}, "additional": {"logType": "detail", "children": [], "durationId": "0eea12c0-be00-4c22-b17b-a362fde9ddf7"}}, {"head": {"id": "ee9fe321-20b0-4481-b010-45f2a355a26e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897990680300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f40e8db-0ddb-4826-8cc2-92f4f54e066d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897990821600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4968950e-6f8d-429a-9ae0-3742e42862aa", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897992314300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbffc17e-5a9a-4a8e-a3a3-0375f5572a4f", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897992627900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f4c1480-a10b-4c25-bd61-4afc7a7b7818", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 3 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897995515400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89ee5c89-f92b-4a77-8a07-bea67b722caa", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898002143900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdfeb45f-a9d6-45f6-ae70-81ebf182a263", "name": "entry : default@SyscapTransform cost memory 0.15142822265625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898002364600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1ebbdbb-3763-4d2d-a3f4-5ea3608f901b", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897992301800, "endTime": 222898002488300}, "additional": {"logType": "info", "children": [], "durationId": "0eea12c0-be00-4c22-b17b-a362fde9ddf7"}}, {"head": {"id": "673f4362-871a-4216-9131-ac516a9858e1", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898008548700, "endTime": 222898013132700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "de0a7a4b-39a8-418b-95b0-8b13b2c00d80", "logId": "3d392352-7de0-4ece-8253-1d2c9faa12e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de0a7a4b-39a8-418b-95b0-8b13b2c00d80", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898004979600}, "additional": {"logType": "detail", "children": [], "durationId": "673f4362-871a-4216-9131-ac516a9858e1"}}, {"head": {"id": "ea611556-6c04-498c-a5ff-add0aecad931", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898006269200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d0ff38f-ff4f-4e15-b5db-fa0bb0553327", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898006446800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e20078b5-09e5-4fd5-8141-58a74acb123a", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898008577400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8768ce9-d0c0-4867-998e-4fe1c891b235", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898012819400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38caae29-0a2d-4af7-9dfb-688e2471f4a3", "name": "entry : default@ProcessProfile cost memory 0.1291656494140625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898013035800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d392352-7de0-4ece-8253-1d2c9faa12e2", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898008548700, "endTime": 222898013132700}, "additional": {"logType": "info", "children": [], "durationId": "673f4362-871a-4216-9131-ac516a9858e1"}}, {"head": {"id": "df82f46a-e0d9-4785-8fd8-662ca5ff22f9", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898021709600, "endTime": 222898037732700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e5726ccc-37b9-4b76-85dd-e88af2c61c7d", "logId": "b49c28dc-76d0-43c9-a6a3-fe5ac6cd9c58"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5726ccc-37b9-4b76-85dd-e88af2c61c7d", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898015743200}, "additional": {"logType": "detail", "children": [], "durationId": "df82f46a-e0d9-4785-8fd8-662ca5ff22f9"}}, {"head": {"id": "284c6306-32c3-4cfe-aaa9-1fcae06630a9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898017103700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "555b059f-edc0-4073-876a-6d62baa6c8cd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898017256300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c848a944-5008-42b3-8d58-aa0c191263f9", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898023941000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "811f4ddf-3d38-4925-b4ff-b69c47b37e0d", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898037059500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60fb61bc-ac89-47b7-ae5a-91c1b6617e1e", "name": "entry : default@ProcessRouterMap cost memory -4.628028869628906", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898037541600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b49c28dc-76d0-43c9-a6a3-fe5ac6cd9c58", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898021709600, "endTime": 222898037732700}, "additional": {"logType": "info", "children": [], "durationId": "df82f46a-e0d9-4785-8fd8-662ca5ff22f9"}}, {"head": {"id": "ad072475-de99-426c-8361-18b02490a180", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898049455600, "endTime": 222898067497800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "3a4bc70d-ae0e-4271-b28c-0551aba7858b", "logId": "4ab7ec50-c204-4a0c-9a2d-cf75300d32fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a4bc70d-ae0e-4271-b28c-0551aba7858b", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898046832600}, "additional": {"logType": "detail", "children": [], "durationId": "ad072475-de99-426c-8361-18b02490a180"}}, {"head": {"id": "2b4a5ffc-548c-4f52-ae90-dbd241a3b762", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898048921100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08aedc6e-6ed9-4364-ac41-7c97997ead27", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898049132200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f94ac96f-9f0b-4337-a1ca-3f5929e68cd0", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898049468600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "067abe7c-2837-41f6-b8c8-0e47a4f0e09f", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898049732400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "991c9cb3-455d-450a-b99e-3d64fa9f04a5", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898056930900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e451fd24-2def-4666-afd4-5f12ddd53420", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898057146000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d155f586-186e-43ec-83d9-1c0985b4c538", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898057308500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c844e69e-8b11-4150-b32e-a5fbe29dac6d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898057370100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b37e69f-17fe-4d0f-83f9-58143221d3ff", "name": "entry : default@ProcessStartupConfig cost memory -5.8138580322265625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898067157200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e21a596d-fc76-4811-833d-3e5788b6fcd5", "name": "runTaskFromQueue task cost before running: 816 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898067382000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ab7ec50-c204-4a0c-9a2d-cf75300d32fb", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898049455600, "endTime": 222898067497800, "totalTime": 17878800}, "additional": {"logType": "info", "children": [], "durationId": "ad072475-de99-426c-8361-18b02490a180"}}, {"head": {"id": "aa200d8f-d443-4676-ba67-0447e9036210", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898075985000, "endTime": 222898078582300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "2aec00a9-16b4-4a38-a6f9-2256d028b3b8", "logId": "a1d5f1d1-8760-404b-9bda-d26d14024fe0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2aec00a9-16b4-4a38-a6f9-2256d028b3b8", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898073092800}, "additional": {"logType": "detail", "children": [], "durationId": "aa200d8f-d443-4676-ba67-0447e9036210"}}, {"head": {"id": "6a9f3831-94cc-4df2-9ccf-d199038bea9d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898074575000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ea9e231-867a-4b85-88da-fb15ab686971", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898074746400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9ed40f6-b81b-43f8-883b-7b775483e1c6", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898076024500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bab149c0-4318-4b21-8a12-164c0b43be84", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898076399600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba309917-771c-4849-9ba9-20deb8bdc8c2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898076493700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b069b6b-7405-4d63-a21d-bcc366e801bf", "name": "entry : default@BuildNativeWithNinja cost memory 0.058319091796875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898078267800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22d3d497-e6a4-4340-9e22-9454b90c9fd8", "name": "runTaskFromQueue task cost before running: 828 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898078499100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1d5f1d1-8760-404b-9bda-d26d14024fe0", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898075985000, "endTime": 222898078582300, "totalTime": 2478400}, "additional": {"logType": "info", "children": [], "durationId": "aa200d8f-d443-4676-ba67-0447e9036210"}}, {"head": {"id": "a6c410d0-f80b-4704-8c1f-a8f7e77135b8", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898087716100, "endTime": 222898097150300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "a742ce6e-9758-47af-b50b-0c5d97489fcd", "logId": "86e27112-4b1c-4b1c-9692-3ec7793ccf9e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a742ce6e-9758-47af-b50b-0c5d97489fcd", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898082061000}, "additional": {"logType": "detail", "children": [], "durationId": "a6c410d0-f80b-4704-8c1f-a8f7e77135b8"}}, {"head": {"id": "c5d6d89a-258e-4267-9954-6a3b2c11c59b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898083342500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b303dce2-d414-4d55-a976-2f6cf45bbd1c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898083471600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f8d6faf-1b1f-4a17-90b6-a34cb401782e", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898085401400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c993c31-207d-4b54-a1b3-2a39aff340a0", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898089922200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56854a36-26ef-4478-945f-832a9760fdf3", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898092730100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14194784-c9ba-44b6-8fc6-9bd4b1287938", "name": "entry : default@ProcessResource cost memory 0.1630706787109375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898092998400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86e27112-4b1c-4b1c-9692-3ec7793ccf9e", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898087716100, "endTime": 222898097150300}, "additional": {"logType": "info", "children": [], "durationId": "a6c410d0-f80b-4704-8c1f-a8f7e77135b8"}}, {"head": {"id": "199578f1-85b1-414d-b62a-d97d144b821f", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898110066400, "endTime": 222898135493700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "04fafb66-a1fa-401c-baf5-dcc4dbb8288a", "logId": "b8071736-5423-4a41-9e86-aa0bfea0ee59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "04fafb66-a1fa-401c-baf5-dcc4dbb8288a", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898103751000}, "additional": {"logType": "detail", "children": [], "durationId": "199578f1-85b1-414d-b62a-d97d144b821f"}}, {"head": {"id": "7ed5150f-30b9-443c-ac65-6808b1f65ce6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898105139800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "534c6d9d-beb3-46bd-ad52-093dabc5de12", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898105289700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "772b0240-a953-4113-94cf-ea6db15cff51", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898110095500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88bea647-cc27-4075-afd0-645646741561", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898135153800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73739b6b-301a-4203-b01e-b241eedce3d8", "name": "entry : default@GenerateLoaderJson cost memory 0.9037551879882812", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898135404300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8071736-5423-4a41-9e86-aa0bfea0ee59", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898110066400, "endTime": 222898135493700}, "additional": {"logType": "info", "children": [], "durationId": "199578f1-85b1-414d-b62a-d97d144b821f"}}, {"head": {"id": "927da95e-33fb-460b-ae57-13bf8b0c06f0", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898151070000, "endTime": 222898158237300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "0cbd2ca9-1f59-4a60-beb1-203ae17b7785", "logId": "3bbf35ec-6816-4b5f-bca9-0a0b3b1b743c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0cbd2ca9-1f59-4a60-beb1-203ae17b7785", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898148722200}, "additional": {"logType": "detail", "children": [], "durationId": "927da95e-33fb-460b-ae57-13bf8b0c06f0"}}, {"head": {"id": "8ff9071e-c22d-4a9b-bd96-65dcbb0ce44d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898149967500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1ac7d43-d04b-4b12-af86-bdf66156003a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898150100100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6171b17d-4477-47fd-9dfd-0e31fdb52a7e", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898151091900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c81ab480-27f8-47ee-8925-79d142a02211", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898158002600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "349bee33-0a3b-42b0-9498-b32d5a56e487", "name": "entry : default@ProcessLibs cost memory 0.1769561767578125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898158168000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bbf35ec-6816-4b5f-bca9-0a0b3b1b743c", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898151070000, "endTime": 222898158237300}, "additional": {"logType": "info", "children": [], "durationId": "927da95e-33fb-460b-ae57-13bf8b0c06f0"}}, {"head": {"id": "c8209afc-3950-4fea-b22a-bde2c82423a4", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898169986800, "endTime": 222898208609400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "793c4b6e-c740-42bb-b7e7-05dbb7e0dfb1", "logId": "4f937ef7-2866-4aa5-8f24-78fca4fc58a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "793c4b6e-c740-42bb-b7e7-05dbb7e0dfb1", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898162230400}, "additional": {"logType": "detail", "children": [], "durationId": "c8209afc-3950-4fea-b22a-bde2c82423a4"}}, {"head": {"id": "bdf96537-23cd-412e-b465-5accbe32bdeb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898163501900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70ffa352-9818-456c-8fad-b9d518ab3c30", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898163665400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9404952-097a-46df-9970-37b3df2a39f8", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898165228100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d68aa767-44fa-40a2-843d-2aa64f8e2832", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898170033000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d831b6f6-4faa-4ebe-939a-65bd3c8997f1", "name": "Incremental task entry:default@CompileResource pre-execution cost: 37 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898208212500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0de9233a-addd-4024-a712-9fe88642a71d", "name": "entry : default@CompileResource cost memory 1.3174514770507812", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898208446000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f937ef7-2866-4aa5-8f24-78fca4fc58a9", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898169986800, "endTime": 222898208609400}, "additional": {"logType": "info", "children": [], "durationId": "c8209afc-3950-4fea-b22a-bde2c82423a4"}}, {"head": {"id": "839e42bd-4142-43e7-9e0d-fe5197395a46", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898220062600, "endTime": 222898222812100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "f8129243-e78f-4f05-ac26-bb25f0105a95", "logId": "f3f7fbed-2b3a-4e4c-ba00-d415af35a668"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f8129243-e78f-4f05-ac26-bb25f0105a95", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898214887600}, "additional": {"logType": "detail", "children": [], "durationId": "839e42bd-4142-43e7-9e0d-fe5197395a46"}}, {"head": {"id": "7edde730-817a-437c-88da-e81b388d4553", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898216345600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a06d35ba-6e43-49f8-ad7e-cabe04fb0692", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898216504300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfbe07a9-59e0-4dbe-880e-9601fd4695c5", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898220079300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1eed2732-8e34-4b00-a320-6d572b94670e", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898220749300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "895dae48-84fa-4cb0-9b3a-b4332fed380a", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898222604100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b834948-8e57-4a22-8f39-9e48dbb06928", "name": "entry : default@DoNativeStrip cost memory 0.08349609375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898222745000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3f7fbed-2b3a-4e4c-ba00-d415af35a668", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898220062600, "endTime": 222898222812100}, "additional": {"logType": "info", "children": [], "durationId": "839e42bd-4142-43e7-9e0d-fe5197395a46"}}, {"head": {"id": "84a21d14-23a2-417f-9f06-633e344b2d26", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898232556500, "endTime": 222914438476800}, "additional": {"children": ["3dec7769-ff52-48db-aa03-25467a30adb5", "a873672c-aa7e-442e-be02-1c0e883b40f5"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "cdb83e66-b50f-4c31-abee-0fa1c7fdc133", "logId": "1680b03f-3948-46b7-927d-e05f0e975767"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cdb83e66-b50f-4c31-abee-0fa1c7fdc133", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898225046500}, "additional": {"logType": "detail", "children": [], "durationId": "84a21d14-23a2-417f-9f06-633e344b2d26"}}, {"head": {"id": "11cedf5c-4a8d-49d8-a09f-a5825adb4b51", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898226380000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec251c44-c53f-4e36-a3b1-e6d0f8971444", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898226527700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df4e23cb-36a4-4982-b293-89305d08a49c", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898232572300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaadcc33-0d69-4a40-9db5-4e965f6a03ec", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898232805500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1266fb9f-9d29-4dbf-802e-e02a2eb5eccd", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898273563300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e36199ec-a07d-4665-bc03-a072fcfb8ddf", "name": "default@CompileArkTS work[8] is submitted.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898276478300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dec7769-ff52-48db-aa03-25467a30adb5", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 222899140699200, "endTime": 222914438239200}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "84a21d14-23a2-417f-9f06-633e344b2d26", "logId": "a4a83112-5164-4e18-aa5a-230b1fa36d72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "37512efa-d6a5-470b-8725-2bd20cc1a240", "name": "default@CompileArkTS work[8] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898278099500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ed4a05e-d09b-4d68-ac9f-fcf53097645d", "name": "default@CompileArkTS work[8] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898278298000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdc60635-2c65-4530-9132-79897f7f10a1", "name": "CopyResources startTime: 222898278385200", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898278388600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9e0623f-be2a-4460-bda0-3ea0aadee7cb", "name": "default@CompileArkTS work[9] is submitted.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898278481000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a873672c-aa7e-442e-be02-1c0e883b40f5", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker18", "startTime": 222899643925500, "endTime": 222899658824400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "84a21d14-23a2-417f-9f06-633e344b2d26", "logId": "ddda2fd2-8b18-4d8e-b72f-e38349548ac3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a861b40-1f4c-4c56-a684-aff5c61d28a7", "name": "default@CompileArkTS work[9] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898279567700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "113bf639-c847-4c37-9e5f-ca263847d781", "name": "default@CompileArkTS work[9] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898279690300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1edb3320-9b0e-4306-85d3-48827341f250", "name": "entry : default@CompileArkTS cost memory 1.4430084228515625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898280084300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f5372d6-d7e5-4cab-9c13-491071756a3c", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898288026100, "endTime": 222898296720100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "e791e6a9-cb30-42f7-9719-4fb539c30d73", "logId": "f45d0a1a-abcd-4a18-8d2a-d1d18ffb6123"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e791e6a9-cb30-42f7-9719-4fb539c30d73", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898282098600}, "additional": {"logType": "detail", "children": [], "durationId": "0f5372d6-d7e5-4cab-9c13-491071756a3c"}}, {"head": {"id": "9ec41b32-8d49-42de-bb84-284bf3fd9599", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898283291600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b0f30ca-a9a7-4ff6-b4e6-ea20b64d64e0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898283418700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77545243-efac-4b9f-bfe0-1e23c616d8f4", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898288040500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c76d820c-8dfd-4b29-a537-9b5cdbb694af", "name": "entry : default@BuildJS cost memory 0.35943603515625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898296461700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c63960aa-c8f6-47f9-8d65-8ac975a90455", "name": "runTaskFromQueue task cost before running: 1 s 46 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898296649800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f45d0a1a-abcd-4a18-8d2a-d1d18ffb6123", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898288026100, "endTime": 222898296720100, "totalTime": 8586700}, "additional": {"logType": "info", "children": [], "durationId": "0f5372d6-d7e5-4cab-9c13-491071756a3c"}}, {"head": {"id": "6b1ea775-709f-400d-9ee5-0887cfe0eed8", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898303009400, "endTime": 222898306432700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "c94f1207-9f82-401c-b9fc-8f288f2d2de1", "logId": "36b14a0f-ed81-419b-97b1-f81da1478221"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c94f1207-9f82-401c-b9fc-8f288f2d2de1", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898298663700}, "additional": {"logType": "detail", "children": [], "durationId": "6b1ea775-709f-400d-9ee5-0887cfe0eed8"}}, {"head": {"id": "3d8927fe-2a4b-4edf-8d83-106d19504c87", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898299894900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e167041c-6832-4172-b0d0-caf7f7c45383", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898300032100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2d6c0be-408d-4004-9249-da563adb5705", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898303026000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae276819-20a3-4573-9307-867f78bf2118", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898303942600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26447ad5-4835-4765-b29f-abbd734ea0b2", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898306199400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "979af60d-e457-4ffe-af50-aa299a8da044", "name": "entry : default@CacheNativeLibs cost memory 0.09906768798828125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898306353800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36b14a0f-ed81-419b-97b1-f81da1478221", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898303009400, "endTime": 222898306432700}, "additional": {"logType": "info", "children": [], "durationId": "6b1ea775-709f-400d-9ee5-0887cfe0eed8"}}, {"head": {"id": "200321c5-89d4-41ce-a3f0-2fbe104af48e", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898491520900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5432b2be-7827-4011-8ea6-6337d9db4072", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898491745100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d786929-8c37-431e-914a-36391613adc1", "name": "default@CompileArkTS work[9] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898492938800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0677f800-4c1c-40df-840f-922a18c29e26", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222899138247100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10ce3fb7-15ca-4c8d-8077-4fa8f889d379", "name": "A work dispatched to worker[18] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222899138541500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5a953d1-0861-43f0-a1c4-45f7a53b9d47", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222899138697200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a756e9d-41f1-4d34-9f66-b91c4d4cd325", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222899138740100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76e8486c-187c-4023-aa45-295823f9af46", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222899138768900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c5a0256-a013-4e1f-b583-90e7e8ff842a", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222899138795600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e074a1b-5334-4309-afb0-9667e1cbbbbe", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222899138821200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc75204f-402a-41cf-98c1-13b2de9c73a2", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222899138846100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "559f76b6-6e9b-4009-9b5a-f7051d5ca353", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222899138994300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7822f53-8403-45fb-a48f-be73696d9f47", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222899139101700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1de56cd-644f-4d9d-b8b5-a93fc16ca772", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222899139177500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "714bb561-e908-4ed7-a870-23f0d7e6e665", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222899139250500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8c125c7-e3fc-497a-bd26-76957b6aa07b", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222899139318000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e361104-4302-4f63-9f50-bd3f43705a9f", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222899139388400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "743950e9-cb3a-4938-80a7-3ef0a767eb07", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222899139469800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a48ed3d-c496-4ef6-b3fe-d378c50a1279", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222899139554900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31302ece-4069-41dc-80de-43bff4c06ad4", "name": "default@CompileArkTS work[8] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222899140710300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71059472-8a1a-4ced-ad3a-cb3d52b6f3ff", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222899659081700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb925464-937b-4429-a10a-34b5715a0e16", "name": "CopyResources is end, endTime: 222899659356700", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222899659366900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a488178-8e9a-4682-90b0-dc03c9b3b6b7", "name": "default@CompileArkTS work[9] done.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222899659625500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddda2fd2-8b18-4d8e-b72f-e38349548ac3", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Worker18", "startTime": 222899643925500, "endTime": 222899658824400}, "additional": {"logType": "info", "children": [], "durationId": "a873672c-aa7e-442e-be02-1c0e883b40f5", "parent": "1680b03f-3948-46b7-927d-e05f0e975767"}}, {"head": {"id": "638fd4a0-27ad-4f95-959b-e5a20246d39f", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222900197918100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b90183a3-9997-442b-a318-8638dc98b9d1", "name": "worker[4] has one work error.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914438075400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "328560f6-9a32-4378-9c3e-ce227025e166", "name": "default@CompileArkTS work[8] failed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914438355400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4a83112-5164-4e18-aa5a-230b1fa36d72", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 222899140699200, "endTime": 222914438239200}, "additional": {"logType": "error", "children": [], "durationId": "3dec7769-ff52-48db-aa03-25467a30adb5", "parent": "1680b03f-3948-46b7-927d-e05f0e975767"}}, {"head": {"id": "1680b03f-3948-46b7-927d-e05f0e975767", "name": "Failed :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222898232556500, "endTime": 222914438476800}, "additional": {"logType": "error", "children": ["a4a83112-5164-4e18-aa5a-230b1fa36d72", "ddda2fd2-8b18-4d8e-b72f-e38349548ac3"], "durationId": "84a21d14-23a2-417f-9f06-633e344b2d26"}}, {"head": {"id": "7a3c3a3f-25f9-4883-86d0-2049e847b4d4", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914438566700}, "additional": {"logType": "debug", "children": [], "durationId": "84a21d14-23a2-417f-9f06-633e344b2d26"}}, {"head": {"id": "01d01104-4843-40a3-9276-a965e18d3377", "name": "ERROR: stacktrace = Error: ArkTS Compiler Error\r\n\u001b[33m1 WARN: \u001b[33m\u001b[33mArkTS:WARN: For details about ArkTS syntax errors, see FAQs\u001b[39m\u001b[39m\r\n\u001b[33m2 WARN: \u001b[33m\u001b[33mArkTS:WARN File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/WelcomePage.ets:29:64\n 'getContext' has been deprecated.\n\u001b[39m\u001b[39m\r\n\u001b[33m3 WARN: \u001b[33m\u001b[33mArkTS:WARN File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/WelcomePage.ets:66:12\n 'pushUrl' has been deprecated.\n\u001b[39m\u001b[39m\r\n\u001b[31m1 ERROR: \u001b[31m10605999 ArkTS Compiler Error\r\nError Message: No overload matches this call.\n  Overload 1 of 2, '(...items: ConcatArray<AppModel>[]): AppModel[]', gave the following error.\n    Argument of type 'AppModel[] | undefined' is not assignable to parameter of type 'ConcatArray<AppModel>'.\n      Type 'undefined' is not assignable to type 'ConcatArray<AppModel>'.\n  Overload 2 of 2, '(...items: (AppModel | ConcatArray<AppModel>)[]): AppModel[]', gave the following error.\n    Argument of type 'AppModel[] | undefined' is not assignable to parameter of type 'AppModel | ConcatArray<AppModel>'.\n      Type 'undefined' is not assignable to type 'AppModel | ConcatArray<AppModel>'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/SearchPage.ets:159:58\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m2 ERROR: \u001b[31m10605999 ArkTS Compiler Error\r\nError Message: Type 'AppModel[] | undefined' is not assignable to type 'AppModel[]'.\n  Type 'undefined' is not assignable to type 'AppModel[]'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/SearchPage.ets:162:11\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m3 ERROR: \u001b[31m10605999 ArkTS Compiler Error\r\nError Message: Type 'AppModel[] | undefined' must have a '[Symbol.iterator]()' method that returns an iterator. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/AppListPage.ets:128:41\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m4 ERROR: \u001b[31m10605999 ArkTS Compiler Error\r\nError Message: Type 'AppModel[] | undefined' is not assignable to type 'AppModel[]'.\n  Type 'undefined' is not assignable to type 'AppModel[]'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/AppListPage.ets:131:11\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m5 ERROR: \u001b[31m10605099 ArkTS Compiler Error\r\nError Message: It is possible to spread only arrays or classes derived from arrays into the rest parameter or array literals (arkts-no-spread) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/AppListPage.ets:128:38\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m6 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Type '{ list: undefined[]; pagination: { page: number; page_size: number; total: number; total_pages: number; }; }' is missing the following properties from type 'AppListData': data, total, page, page_size At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/services/ApiService.ets:204:10\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m7 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Type '{ list: undefined[]; pagination: { page: number; page_size: number; total: number; total_pages: number; }; }' is missing the following properties from type 'AppListData': data, total, page, page_size At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/services/ApiService.ets:295:10\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m8 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Type '{ list: undefined[]; pagination: { page: number; page_size: number; total: number; total_pages: number; }; }' is missing the following properties from type 'AppListData': data, total, page, page_size At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/services/ApiService.ets:542:9\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31mCOMPILE RESULT:FAIL {ERROR:9 WARN:3}\u001b[39m\n    at runArkPack (C:\\command-line-tools\\hvigor\\hvigor-ohos-plugin\\node_modules\\@ohos\\hvigor-arkts-compose\\dist\\src\\arkts-pack.js:1:5479)", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914439560100}, "additional": {"logType": "debug", "children": [], "durationId": "84a21d14-23a2-417f-9f06-633e344b2d26"}}, {"head": {"id": "61948c76-4f56-489b-b32b-f264752d6d07", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914463419800, "endTime": 222914463686600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e0546b02-98c3-4c2f-a733-f9dcc1972e69", "logId": "be668117-8f85-436f-93f7-2abd426bdabe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be668117-8f85-436f-93f7-2abd426bdabe", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914463419800, "endTime": 222914463686600}, "additional": {"logType": "info", "children": [], "durationId": "61948c76-4f56-489b-b32b-f264752d6d07"}}, {"head": {"id": "433ef6b6-f4d1-4e60-9388-88c0e547fd21", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222897251375200, "endTime": 222914464078800}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 19, "minute": 52, "second": 24}, "completeCommand": "{\"prop\":[\"product=default\"],\"mode\":\"module\",\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "eb32bb11-f6c5-4771-b239-bec9b19eab24", "name": "BUILD FAILED in 17 s 213 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914464191800}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "dc2ba7fc-8267-4e27-96f3-0c8541596821", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914464535000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fa72f43-38f9-47a7-a556-c3a0d56e15fa", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914464719900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61d6c0c8-d973-4cf3-9846-25e25e741e56", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914465285500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7092ca3b-82fc-4710-bcf6-5b864bc39a18", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914465449600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e2baf4c-f8a0-4e4b-9d31-9567c0427727", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914465669900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "058bf935-52d3-438d-99d4-ef9fb3168a92", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914465788100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03de1683-3b53-4864-9d25-02b922314e5b", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914465884600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef24ec73-73d6-4cf1-ade7-7ecc0633fd52", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914467206700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fc39f2b-c80a-4464-bbdb-619240a96d7e", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914467681400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8d9672c-8260-4722-9328-149262c87a89", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914467916000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2595c5dd-31de-404c-b619-6c5ae3812198", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914468033400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a53167c-8893-4173-a79e-bb7d14d4c669", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914468127600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4242f19e-f88e-4a28-a9b6-3cc5c276f4c7", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914468227700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db8e2674-6e5d-44ae-bcea-85f9d31164c1", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914470294500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3ef34fc-e835-43ff-9db2-3714ba944e1b", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914470751800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6dcfa70-cec3-4c6a-8c25-1fca9ff826ea", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914471079200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0f856fd-39ab-4df3-930f-25f082236067", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914471167100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e125e23-ee05-4324-be71-412db33139d8", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914471210800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f80d73a-e458-4da5-aeb5-54fd6c9cc52f", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914471244800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99490426-853d-4f24-b53e-8872261f0426", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914471319800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69e3feb2-476c-4c86-9195-f22f00f551d4", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914471351900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0a30da9-5282-4218-83cf-26dd5f968e05", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914476224800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23812ec8-4f78-4785-baf6-138ac7231b42", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914477409000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8ecdfa1-2a81-4e28-ba01-8f8480bb788f", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914478528400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2d83b3a-25c2-4801-8c91-47b8f9d55467", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914479095100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8019d1b2-982c-4b0b-a67d-1fe0c0df28da", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914479530700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb1f7f43-bdee-461b-91e8-30fc43a21122", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914480799400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4bd0278-fc2c-4273-9770-05cf9188b759", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914492113900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20e0408a-ca75-49b3-9545-fa755292f5b0", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914492556400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "935a09b8-2537-4089-811a-f9506388a822", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914493071300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac7c526e-de18-4587-8652-408f22b36291", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914494683100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9c4fa77-1f5b-4b24-8f07-4cc53791c545", "name": "Incremental task entry:default@CompileArkTS post-execution cost:25 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914495552400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1df7511e-97cb-46af-bfb0-a41d6045ebfa", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914498012500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cc064d5-248e-488f-8973-173e65017bbf", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914498872500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11cc816b-01b0-4ddc-9550-62cf54be30d7", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914499347600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a2fb052-5572-42f7-99b3-06b6a9313ef1", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914499628800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "068a672b-2f71-4214-a458-e171bd54a10b", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914499934900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58f28203-d367-4ed9-b049-a1f938ad86bf", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914500939300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a19550d3-dcf3-46c5-8acf-d14a597e1807", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914502128100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f55b04d6-1eff-4ba5-90ad-def41fec97d7", "name": "Incremental task entry:default@BuildJS post-execution cost:7 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914502617500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2654c2e8-e177-4c74-83cc-b2e8555ee797", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914502704100}, "additional": {"logType": "debug", "children": []}}], "workLog": []}