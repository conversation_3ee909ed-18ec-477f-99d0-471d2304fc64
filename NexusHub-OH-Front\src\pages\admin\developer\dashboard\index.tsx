import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Tag,
  Button,
  Space,
  Typography,
  Progress,
  List,
  Avatar,
  Spin,
  message,
} from 'antd';
import {
  UserOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  TrophyOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Column } from '@ant-design/plots';
import dayjs from 'dayjs';
import { history } from 'umi';
import { getAdminDevelopersStats, getAdminDevelopersRecent } from '@/services/ant-design-pro/kaifazhe';
import styles from './index.less';

const { Title, Text } = Typography;



const DeveloperDashboard: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<API.DeveloperStatsResponse | null>(null);
  const [recentApplications, setRecentApplications] = useState<API.RecentApplicationResponse[]>([]);
  const [chartData, setChartData] = useState<any[]>([]);

  // 获取统计数据
  const fetchStats = async () => {
    setLoading(true);
    try {
      // 获取统计数据
      const statsResponse = await getAdminDevelopersStats();
      console.log('API Response (getAdminDevelopersStats):', statsResponse);

      // 修改判断条件，检查code是否为200
      if (statsResponse && statsResponse.code === 200 && statsResponse.data) {
        console.log('Data to be set for stats:', statsResponse.data);
        setStats(statsResponse.data);
        
        // 生成图表数据
        const chartData = [
          { status: '已通过', count: statsResponse.data.approved_count || 0, color: '#52c41a' },
          { status: '待审核', count: statsResponse.data.pending_count || 0, color: '#faad14' },
          { status: '已拒绝', count: statsResponse.data.rejected_count || 0, color: '#ff4d4f' },
        ];
        setChartData(chartData);
      } else {
        console.warn('Failed to fetch stats or data is empty:', statsResponse);
      }
      
      // 获取最近申请数据
      const recentResponse = await getAdminDevelopersRecent({ limit: 5 });
      console.log('API Response (getAdminDevelopersRecent):', recentResponse);
      // 修改判断条件，检查code是否为200
      if (recentResponse && recentResponse.code === 200 && recentResponse.data) {
        setRecentApplications(recentResponse.data);
      } else {
        console.warn('Failed to fetch recent applications or data is empty:', recentResponse);
      }
      
    } catch (error) {
      console.error('获取统计数据失败:', error);
      message.error('获取统计数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  console.log('Current stats state before render:', stats); // 新增日志
  console.log('Current chartData state before render:', chartData); // 新增日志
  console.log('Current recentApplications state before render:', recentApplications); // 新增日志

  // 渲染状态标签
  const renderStatusTag = (status: string) => {
    const statusMap = {
      pending: { color: 'orange', text: '待审核' },
      approved: { color: 'green', text: '已通过' },
      rejected: { color: 'red', text: '已拒绝' },
    };
    
    const config = statusMap[status as keyof typeof statusMap];
    return config ? <Tag color={config.color}>{config.text}</Tag> : null;
  };

  // 跳转到详细页面
  const goToVerifyPage = () => {
    history.push('/user-management/developer-verify');
  };

  // 图表配置
  const chartConfig = {
    data: chartData,
    xField: 'status',
    yField: 'count',
    colorField: 'status',
    color: ({ status }: any) => {
      const colorMap: Record<string, string> = {
        '已通过': '#52c41a',
        '待审核': '#faad14',
        '已拒绝': '#ff4d4f',
      };
      return colorMap[status];
    },
    label: {
      position: 'middle' as const,
      style: {
        fill: '#FFFFFF',
        opacity: 0.6,
      },
    },
    meta: {
      status: {
        alias: '状态',
      },
      count: {
        alias: '数量',
      },
    },
  };

  if (loading) {
    return (
      <PageContainer title="开发者认证管理">
        <Card>
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>正在加载数据...</div>
          </div>
        </Card>
      </PageContainer>
    );
  }

  return (
    <PageContainer
      title="开发者认证数据统计"
      subTitle="统计开发者认证相关数据"
      extra={[
        <Button key="verify" type="primary" onClick={goToVerifyPage}>
          审核申请
        </Button>,
      ]}
    >
      <div className={styles.dashboard}>
        {/* 统计卡片 */}
        <Row gutter={[16, 16]} className={styles.statsRow}>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="总申请数"
                value={stats?.total_applications || 0}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="待审核"
                value={stats?.pending_count || 0}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="已通过"
                value={stats?.approved_count || 0}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="今日新申请"
                value={stats?.today_applications || 0}
                prefix={<TrophyOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          {/* 审核统计图表 */}
          <Col xs={24} lg={12}>
            <Card title="审核状态分布" className={styles.chartCard}>
              <Column {...chartConfig} height={300} />
              <div className={styles.approvalRate}>
                <Title level={4}>通过率</Title>
                <Progress
                  type="circle"
                  percent={parseFloat(stats?.approval_rate?.replace('%', '') || '0')}
                  format={(percent) => `${percent}%`}
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                />
              </div>
            </Card>
          </Col>

          {/* 最近申请 */}
          <Col xs={24} lg={12}>
            <Card
              title="最近申请"
              extra={
                <Button type="link" onClick={goToVerifyPage}>
                  查看全部
                </Button>
              }
              className={styles.recentCard}
            >
              <List
                itemLayout="horizontal"
                dataSource={recentApplications}
                renderItem={(item) => (
                  <List.Item
                    actions={[
                      <Button
                        key="view"
                        type="link"
                        icon={<EyeOutlined />}
                        onClick={goToVerifyPage}
                      >
                        查看
                      </Button>,
                    ]}
                  >
                    <List.Item.Meta
                      avatar={
                        <Avatar
                          src={item.developer_avatar}
                          icon={<UserOutlined />}
                        />
                      }
                      title={
                        <Space>
                          <span>{item.developer_name}</span>
                          {renderStatusTag(item.verify_status)}
                        </Space>
                      }
                      description={
                        <div>
                          <div>用户名: {item.username}</div>
                          {item.company_name && <div>公司: {item.company_name}</div>}
                          <Text type="secondary">
                            申请时间: {dayjs(item.submitted_at).format('MM-DD HH:mm')}
                          </Text>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </Card>
          </Col>
        </Row>
      </div>
    </PageContainer>
  );
};

export default DeveloperDashboard;