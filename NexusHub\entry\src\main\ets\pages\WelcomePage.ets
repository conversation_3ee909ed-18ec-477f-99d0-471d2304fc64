import { hilog } from '@kit.PerformanceAnalysisKit';
import { router } from '@kit.ArkUI';
import { preferences } from '@kit.ArkData';
import { Constants } from '../utils/Constants';

/**
 * 欢迎页面
 * 首次启动应用时显示，后续启动直接跳转到主页
 */
@Entry
@Component
struct WelcomePage {
  @State private isLoading: boolean = true;
  @State private showContent: boolean = false;
  private readonly DOMAIN = 0x0000;
  private readonly TAG = 'WelcomePage';
  private readonly FIRST_LAUNCH_KEY = 'is_first_launch';
  
  aboutToAppear(): void {
    hilog.info(this.DOMAIN, this.TAG, '欢迎页面加载');
    this.checkFirstLaunch();
  }

  /**
   * 检查是否为首次启动
   */
  private async checkFirstLaunch(): Promise<void> {
    try {
      const dataPreferences = await preferences.getPreferences(getContext(this), 'app_settings');
      const isFirstLaunch = await dataPreferences.get(this.FIRST_LAUNCH_KEY, true) as boolean;
      
      if (isFirstLaunch) {
        // 首次启动，显示欢迎页面
        hilog.info(this.DOMAIN, this.TAG, '首次启动，显示欢迎页面');
        this.showWelcomeContent();
        // 标记为非首次启动
        await dataPreferences.put(this.FIRST_LAUNCH_KEY, false);
        await dataPreferences.flush();
      } else {
        // 非首次启动，直接跳转到主页
        hilog.info(this.DOMAIN, this.TAG, '非首次启动，跳转到主页');
        this.navigateToHome();
      }
    } catch (error) {
      hilog.error(this.DOMAIN, this.TAG, '检查首次启动状态失败: %{public}s', JSON.stringify(error));
      // 出错时显示欢迎页面
      this.showWelcomeContent();
    }
  }

  /**
   * 显示欢迎页面内容
   */
  private showWelcomeContent(): void {
    this.isLoading = false;
    // 添加渐入动画效果
    setTimeout(() => {
      this.showContent = true;
    }, 100);
  }

  /**
   * 跳转到主页
   */
  private navigateToHome(): void {
    router.pushUrl({
      url: 'pages/Index'
    }).catch((error: Error) => {
      hilog.error(this.DOMAIN, this.TAG, '跳转到主页失败: %{public}s', error.message);
    });
  }

  /**
   * 开始使用按钮点击事件
   */
  private onStartClick(): void {
    hilog.info(this.DOMAIN, this.TAG, '用户点击开始使用');
    this.navigateToHome();
  }

  /**
   * 构建欢迎页面内容
   */
  @Builder
  private WelcomeContent() {
    Column({ space: Constants.SPACING.LARGE }) {
      // 应用Logo和名称
      Column({ space: Constants.SPACING.MEDIUM }) {
        Image($r('app.media.app_icon'))
          .width(120)
          .height(120)
          .borderRadius(24)
          .shadow({
            radius: 16,
            color: '#1F000000',
            offsetX: 0,
            offsetY: 4
          })

        Text('NexusHub')
          .fontSize(Constants.FONT_SIZE.XXLARGE)
          .fontWeight(FontWeight.Bold)
          .fontColor($r('sys.color.ohos_id_color_text_primary'))

        Text('OpenHarmony应用商店')
          .fontSize(Constants.FONT_SIZE.MEDIUM)
          .fontColor($r('sys.color.ohos_id_color_text_secondary'))
      }
      .margin({ top: 80 })

      // 功能介绍
      Column({ space: Constants.SPACING.LARGE }) {
        this.FeatureItem(
          $r('app.media.ic_featured'),
          '精选应用',
          '发现优质OpenHarmony应用'
        )
        
        this.FeatureItem(
          $r('app.media.ic_category'),
          '分类浏览',
          '按类别快速找到所需应用'
        )
        
        this.FeatureItem(
          $r('app.media.ic_download'),
          '安全下载',
          '安全可靠的应用下载体验'
        )
      }
      .margin({ top: 60 })

      // 开始使用按钮
      Button('开始使用')
        .width('80%')
        .height(48)
        .fontSize(Constants.FONT_SIZE.MEDIUM)
        .fontColor(Color.White)
        .backgroundColor($r('sys.color.ohos_id_color_emphasize'))
        .borderRadius(24)
        .margin({ top: 60, bottom: 40 })
        .onClick(() => this.onStartClick())
        .shadow({
          radius: 12,
          color: '#33007DFF',
          offsetX: 0,
          offsetY: 4
        })
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .padding({ left: 32, right: 32 })
    .opacity(this.showContent ? 1 : 0)
    .animation({
      duration: Constants.ANIMATION_DURATION.SLOW,
      curve: Curve.EaseOut
    })
  }

  /**
   * 功能介绍项
   */
  @Builder
  private FeatureItem(icon: Resource, title: string, description: string) {
    Row({ space: Constants.SPACING.MEDIUM }) {
      Image(icon)
        .width(32)
        .height(32)
        .fillColor($r('sys.color.ohos_id_color_emphasize'))

      Column({ space: 4 }) {
        Text(title)
          .fontSize(Constants.FONT_SIZE.MEDIUM)
          .fontWeight(FontWeight.Medium)
          .fontColor($r('sys.color.ohos_id_color_text_primary'))
          .alignSelf(ItemAlign.Start)

        Text(description)
          .fontSize(Constants.FONT_SIZE.SMALL)
          .fontColor($r('sys.color.ohos_id_color_text_secondary'))
          .alignSelf(ItemAlign.Start)
      }
      .alignItems(HorizontalAlign.Start)
      .layoutWeight(1)
    }
    .width('100%')
    .padding({ left: 16, right: 16 })
    .alignItems(VerticalAlign.Center)
  }

  /**
   * 加载指示器
   */
  @Builder
  private LoadingIndicator() {
    Column({ space: Constants.SPACING.MEDIUM }) {
      LoadingProgress()
        .width(48)
        .height(48)
        .color($r('sys.color.ohos_id_color_emphasize'))

      Text('正在加载...')
        .fontSize(Constants.FONT_SIZE.SMALL)
        .fontColor($r('sys.color.ohos_id_color_text_secondary'))
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
  }

  build() {
    Column() {
      if (this.isLoading) {
        this.LoadingIndicator()
      } else {
        this.WelcomeContent()
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor($r('sys.color.ohos_id_color_background'))
  }
}