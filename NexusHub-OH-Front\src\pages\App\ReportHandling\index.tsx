import { PageContainer } from '@ant-design/pro-components';
import { Card, Table, Button, Space, Tag, Input, Select, DatePicker, message, Modal, Form, Radio, Descriptions } from 'antd';
import { SearchOutlined, EyeOutlined, CheckCircleOutlined, StopOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { useRequest } from '@umijs/max';
import type { ColumnsType } from 'antd/es/table';

interface ReportItem {
  id: string;
  appName: string;
  reportType: 'inappropriate' | 'illegal' | 'copyright' | 'other';
  reportTime: string;
  reporter: string;
  status: 'pending' | 'processing' | 'resolved' | 'rejected';
  description: string;
  evidence: string[];
}

// 模拟数据获取函数
const fetchReportList = async (params: any) => {
  // 实际项目中应该调用API
  console.log('Fetching with params:', params);
  
  // 模拟数据
  const mockData: ReportItem[] = [
    {
      id: '1',
      appName: '社交聊天',
      reportType: 'inappropriate',
      reportTime: '2023-05-01 10:00:00',
      reporter: 'user123',
      status: 'pending',
      description: '应用包含不适当内容，违反社区规范',
      evidence: ['https://example.com/evidence1.jpg'],
    },
    {
      id: '2',
      appName: '视频编辑器',
      reportType: 'copyright',
      reportTime: '2023-05-02 14:30:00',
      reporter: 'user456',
      status: 'processing',
      description: '应用侵犯了我的版权内容',
      evidence: ['https://example.com/evidence2.pdf', 'https://example.com/evidence3.jpg'],
    },
    {
      id: '3',
      appName: '游戏应用',
      reportType: 'illegal',
      reportTime: '2023-05-03 09:15:00',
      reporter: 'user789',
      status: 'resolved',
      description: '应用包含非法内容',
      evidence: [],
    },
  ];

  return { data: mockData, total: mockData.length };
};

const ReportHandling: React.FC = () => {
  const [searchParams, setSearchParams] = useState({});
  const [modalVisible, setModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [currentItem, setCurrentItem] = useState<ReportItem | null>(null);
  const [form] = Form.useForm();
  
  const { data, loading, run } = useRequest(() => fetchReportList(searchParams), {
    refreshDeps: [searchParams],
  });

  const handleSearch = (values: any) => {
    setSearchParams(values);
  };

  const handleView = (item: ReportItem) => {
    setCurrentItem(item);
    setDetailModalVisible(true);
  };

  const handleProcess = (item: ReportItem) => {
    setCurrentItem(item);
    setModalVisible(true);
    form.resetFields();
  };

  const handleModalSubmit = () => {
    form.validateFields().then(values => {
      message.success(`举报 ${currentItem?.id} 处理完成，处理结果: ${values.result === 'resolve' ? '已解决' : '已拒绝'}`);
      form.resetFields();
      setModalVisible(false);
      // 实际项目中应该调用API
    });
  };

  const getReportTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      inappropriate: '不适当内容',
      illegal: '非法内容',
      copyright: '版权问题',
      other: '其他问题',
    };
    return typeMap[type] || type;
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'pending':
        return <Tag color="blue">待处理</Tag>;
      case 'processing':
        return <Tag color="orange">处理中</Tag>;
      case 'resolved':
        return <Tag color="green">已解决</Tag>;
      case 'rejected':
        return <Tag color="red">已拒绝</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };

  const columns: ColumnsType<ReportItem> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '应用名称',
      dataIndex: 'appName',
      key: 'appName',
    },
    {
      title: '举报类型',
      dataIndex: 'reportType',
      key: 'reportType',
      render: (type) => getReportTypeText(type),
    },
    {
      title: '举报时间',
      dataIndex: 'reportTime',
      key: 'reportTime',
      sorter: (a, b) => a.reportTime.localeCompare(b.reportTime),
    },
    {
      title: '举报者',
      dataIndex: 'reporter',
      key: 'reporter',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button 
            type="primary" 
            icon={<EyeOutlined />} 
            size="small"
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          {(record.status === 'pending' || record.status === 'processing') && (
            <Button 
              type="primary" 
              icon={<CheckCircleOutlined />} 
              size="small"
              onClick={() => handleProcess(record)}
            >
              处理
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <PageContainer>
      <Card title="用户举报处理" bordered={false}>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Input placeholder="应用名称" prefix={<SearchOutlined />} />
            <Select
              placeholder="举报类型"
              style={{ width: 120 }}
              options={[
                { value: 'inappropriate', label: '不适当内容' },
                { value: 'illegal', label: '非法内容' },
                { value: 'copyright', label: '版权问题' },
                { value: 'other', label: '其他问题' },
              ]}
            />
            <Select
              placeholder="状态"
              style={{ width: 120 }}
              options={[
                { value: 'pending', label: '待处理' },
                { value: 'processing', label: '处理中' },
                { value: 'resolved', label: '已解决' },
                { value: 'rejected', label: '已拒绝' },
              ]}
            />
            <DatePicker.RangePicker placeholder={['开始日期', '结束日期']} />
            <Button type="primary" onClick={() => handleSearch({})}>搜索</Button>
            <Button onClick={() => setSearchParams({})}>重置</Button>
          </Space>
        </div>
        <Table 
          columns={columns} 
          dataSource={data?.data} 
          rowKey="id" 
          loading={loading}
          pagination={{
            total: data?.total,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
        />
      </Card>

      {/* 处理举报的模态框 */}
      <Modal
        title="处理举报"
        open={modalVisible}
        onOk={handleModalSubmit}
        onCancel={() => setModalVisible(false)}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="result"
            label="处理结果"
            rules={[{ required: true, message: '请选择处理结果' }]}
          >
            <Radio.Group>
              <Radio value="resolve">解决举报</Radio>
              <Radio value="reject">拒绝举报</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item
            name="comment"
            label="处理说明"
            rules={[{ required: true, message: '请输入处理说明' }]}
          >
            <Input.TextArea rows={4} placeholder="请输入处理说明" />
          </Form.Item>
          <Form.Item
            name="notifyUser"
            valuePropName="checked"
          >
            <Radio.Group>
              <Radio value={true}>通知用户</Radio>
              <Radio value={false}>不通知用户</Radio>
            </Radio.Group>
          </Form.Item>
        </Form>
      </Modal>

      {/* 查看举报详情的模态框 */}
      <Modal
        title="举报详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
          currentItem && (currentItem.status === 'pending' || currentItem.status === 'processing') && (
            <Button 
              key="process" 
              type="primary" 
              onClick={() => {
                setDetailModalVisible(false);
                handleProcess(currentItem);
              }}
            >
              处理
            </Button>
          ),
        ]}
        width={700}
      >
        {currentItem && (
          <Descriptions bordered column={1}>
            <Descriptions.Item label="ID">{currentItem.id}</Descriptions.Item>
            <Descriptions.Item label="应用名称">{currentItem.appName}</Descriptions.Item>
            <Descriptions.Item label="举报类型">{getReportTypeText(currentItem.reportType)}</Descriptions.Item>
            <Descriptions.Item label="举报时间">{currentItem.reportTime}</Descriptions.Item>
            <Descriptions.Item label="举报者">{currentItem.reporter}</Descriptions.Item>
            <Descriptions.Item label="状态">{getStatusTag(currentItem.status)}</Descriptions.Item>
            <Descriptions.Item label="举报描述">{currentItem.description}</Descriptions.Item>
            <Descriptions.Item label="证据材料">
              {currentItem.evidence.length > 0 ? (
                <ul>
                  {currentItem.evidence.map((item, index) => (
                    <li key={index}>
                      <a href={item} target="_blank" rel="noopener noreferrer">
                        证据 {index + 1}
                      </a>
                    </li>
                  ))}
                </ul>
              ) : (
                '无证据材料'
              )}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </PageContainer>
  );
};

export default ReportHandling;