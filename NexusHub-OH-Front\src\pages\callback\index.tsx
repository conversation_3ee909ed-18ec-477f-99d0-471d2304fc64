import React, { useEffect } from 'react';
import { useLogto } from '@logto/react';
import { Spin, Result } from 'antd';
import { history } from '@umijs/max';
import { useModel } from '@umijs/max';

/**
 * Logto 认证回调页面
 * 处理从 Logto 服务器返回的认证结果
 */
const CallbackPage: React.FC = () => {
  const { handleSignInCallback, isLoading, error } = useLogto();
  const { setInitialState } = useModel('@@initialState');

  useEffect(() => {
    const handleCallback = async () => {
      try {
        // 处理登录回调
        await handleSignInCallback(window.location.href);
        
        // 更新应用状态
        setInitialState((s) => ({
          ...s,
          currentUser: null, // 这里会在后续通过 fetchUserInfo 更新
        }));
        
        // 获取重定向URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const redirectTo = urlParams.get('redirect') || '/';
        
        // 跳转到目标页面
        history.replace(redirectTo);
      } catch (error) {
        console.error('处理登录回调失败:', error);
      }
    };

    handleCallback();
  }, [handleSignInCallback, setInitialState]);

  // 显示加载状态
  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column'
      }}>
        <Spin size="large" />
        <div style={{ marginTop: 16, fontSize: 16 }}>
          正在处理登录信息...
        </div>
      </div>
    );
  }

  // 显示错误状态
  if (error) {
    return (
      <Result
        status="error"
        title="登录失败"
        subTitle={`认证过程中发生错误: ${error.message}`}
        extra={[
          <a key="retry" href="/user/login">
            返回登录页面
          </a>
        ]}
      />
    );
  }

  // 默认显示加载状态
  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      flexDirection: 'column'
    }}>
      <Spin size="large" />
      <div style={{ marginTop: 16, fontSize: 16 }}>
        正在跳转...
      </div>
    </div>
  );
};

export default CallbackPage;