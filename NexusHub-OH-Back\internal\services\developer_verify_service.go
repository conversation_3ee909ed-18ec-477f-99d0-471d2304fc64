package services

import (
	"context"
	"fmt"
	"time"

	"nexushub-oh-back/internal/models"
	"nexushub-oh-back/pkg/logger"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// DeveloperVerifyService 开发者认证服务
type DeveloperVerifyService struct {
	DB                  *gorm.DB
	MessageService      *MessageService
	NotificationService *NotificationService
}

// NewDeveloperVerifyService 创建开发者认证服务
func NewDeveloperVerifyService(db *gorm.DB, messageService *MessageService, notificationService *NotificationService) *DeveloperVerifyService {
	return &DeveloperVerifyService{
		DB:                  db,
		MessageService:      messageService,
		NotificationService: notificationService,
	}
}

// DeveloperVerifyMessage 开发者认证消息
type DeveloperVerifyMessage struct {
	UserID       uint   `json:"user_id"`
	Username     string `json:"username"`
	DeveloperName string `json:"developer_name"`
	Action       string `json:"action"` // submit, approve, reject
	Reason       string `json:"reason,omitempty"`
}

// PublishDeveloperVerifyMessage 发布开发者认证消息
func (s *DeveloperVerifyService) PublishDeveloperVerifyMessage(ctx context.Context, userID uint, username, developerName, action, reason string) error {
	msgData := DeveloperVerifyMessage{
		UserID:        userID,
		Username:      username,
		DeveloperName: developerName,
		Action:        action,
		Reason:        reason,
	}

	var msgType MessageType
	switch action {
	case "submit":
		msgType = "developer_verify_submitted"
	case "approve":
		msgType = "developer_verify_approved"
	case "reject":
		msgType = "developer_verify_rejected"
	default:
		msgType = "developer_verify_submitted"
	}

	return s.MessageService.PublishMessage(ctx, QueueNotification, msgType, msgData)
}

// SendVerifyResultNotification 发送认证结果通知
func (s *DeveloperVerifyService) SendVerifyResultNotification(ctx context.Context, userID uint, verifyStatus models.VerifyStatus, reason string) error {
	var title, content string
	var notificationType models.NotificationType
	switch verifyStatus {
	case models.VerifyStatusApproved:
		title = "开发者认证审核通过"
		content = "恭喜！您的开发者认证申请已通过审核，现在您可以上传和管理应用了。"
		notificationType = models.NotificationTypeSuccess
	case models.VerifyStatusRejected:
		title = "开发者认证审核未通过"
		content = fmt.Sprintf("很抱歉，您的开发者认证申请未通过审核。原因：%s。您可以重新提交申请。", reason)
		notificationType = models.NotificationTypeWarning
	default:
		return fmt.Errorf("无效的认证状态: %s", verifyStatus)
	}

	// 发送通知
	if s.NotificationService != nil {
		req := &NotificationRequest{
			UserID:   userID,
			Title:    title,
			Content:  content,
			Type:     notificationType,
			Category: "developer_verification",
			Priority: models.PriorityHigh,
			SendEmail: true,
		}
		_, err := s.NotificationService.CreateNotification(ctx, req)
		if err != nil {
			logger.Error("发送开发者认证通知失败", zap.Error(err), zap.Uint("user_id", userID))
			// 不返回错误，避免影响主流程
		}
	}

	// 发布消息到队列
	return s.MessageService.PublishNotificationMessage(ctx, userID, title, content, string(notificationType))
}

// GetVerifyStatistics 获取认证统计信息
func (s *DeveloperVerifyService) GetVerifyStatistics(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总申请数
	var totalApplications int64
	if err := s.DB.Model(&models.User{}).Where("verify_status != ?", "").Count(&totalApplications).Error; err != nil {
		logger.Error("获取总申请数失败", zap.Error(err))
		return nil, err
	}
	stats["total_applications"] = totalApplications

	// 待审核数
	var pendingCount int64
	if err := s.DB.Model(&models.User{}).Where("verify_status = ?", models.VerifyStatusPending).Count(&pendingCount).Error; err != nil {
		logger.Error("获取待审核数失败", zap.Error(err))
		return nil, err
	}
	stats["pending_count"] = pendingCount

	// 已通过数
	var approvedCount int64
	if err := s.DB.Model(&models.User{}).Where("verify_status = ?", models.VerifyStatusApproved).Count(&approvedCount).Error; err != nil {
		logger.Error("获取已通过数失败", zap.Error(err))
		return nil, err
	}
	stats["approved_count"] = approvedCount

	// 已拒绝数
	var rejectedCount int64
	if err := s.DB.Model(&models.User{}).Where("verify_status = ?", models.VerifyStatusRejected).Count(&rejectedCount).Error; err != nil {
		logger.Error("获取已拒绝数失败", zap.Error(err))
		return nil, err
	}
	stats["rejected_count"] = rejectedCount

	// 今日新申请数
	today := time.Now().Format("2006-01-02")
	var todayApplications int64
	if err := s.DB.Model(&models.User{}).Where("DATE(submitted_at) = ?", today).Count(&todayApplications).Error; err != nil {
		logger.Error("获取今日新申请数失败", zap.Error(err))
		return nil, err
	}
	stats["today_applications"] = todayApplications

	// 通过率
	if totalApplications > 0 {
		approvalRate := float64(approvedCount) / float64(totalApplications) * 100
		stats["approval_rate"] = fmt.Sprintf("%.1f%%", approvalRate)
	} else {
		stats["approval_rate"] = "0.0%"
	}

	return stats, nil
}

// GetRecentApplications 获取最近的申请记录
func (s *DeveloperVerifyService) GetRecentApplications(ctx context.Context, limit int) ([]models.User, error) {
	var users []models.User
	err := s.DB.Where("verify_status != ?", "").
		Order("submitted_at DESC").
		Limit(limit).
		Find(&users).Error

	if err != nil {
		logger.Error("获取最近申请记录失败", zap.Error(err))
		return nil, err
	}

	return users, nil
}

// ValidateVerifyData 验证认证数据
func (s *DeveloperVerifyService) ValidateVerifyData(data map[string]interface{}) error {
	// 检查必填字段
	requiredFields := []string{"developer_name", "contact_email", "contact_phone", "description", "developer_address", "identity_card"}
	for _, field := range requiredFields {
		if value, exists := data[field]; !exists || value == "" {
			return fmt.Errorf("字段 %s 不能为空", field)
		}
	}

	// 验证邮箱格式
	if email, ok := data["contact_email"].(string); ok {
		if !isValidEmail(email) {
			return fmt.Errorf("邮箱格式不正确")
		}
	}

	// 验证手机号格式
	if phone, ok := data["contact_phone"].(string); ok {
		if !isValidPhone(phone) {
			return fmt.Errorf("手机号格式不正确")
		}
	}

	return nil
}

// isValidEmail 验证邮箱格式
func isValidEmail(email string) bool {
	// 简单的邮箱格式验证
	return len(email) > 0 && len(email) <= 100 && 
		contains(email, "@") && contains(email, ".")
}

// isValidPhone 验证手机号格式
func isValidPhone(phone string) bool {
	// 简单的手机号格式验证（中国大陆）
	return len(phone) == 11 && phone[0] == '1'
}

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}