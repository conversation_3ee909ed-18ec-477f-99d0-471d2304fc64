import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';
import { ApiService } from '../services/ApiService';
import { LoadingView, LoadingState, LoadMoreView } from '../components/LoadingView';
import { AppCard } from '../components/AppCard';
import { AppModel, AppSearchParams } from '../models/App';
import { router } from '@kit.ArkUI';
import { promptAction } from '@kit.ArkUI';
import { preferences } from '@kit.ArkData';
// getContext is deprecated, use this.getUIContext().getHostContext() instead
import { hilog } from '@kit.PerformanceAnalysisKit';

interface SortItem {
  key: string;
  label: string;
}

interface FilterOptions {
  isFree?: boolean;
  minRating?: number;
  maxSize?: number;
}

interface AppListPageParams {
  title?: string;
  searchParams?: string;
}

/**
 * 应用列表页面
 */
@Entry
@Component
struct AppListPage {
  @State apps: AppModel[] = [];
  @State loadingState: LoadingState = LoadingState.LOADING;
  @State currentPage: number = 1;
  @State hasMore: boolean = true;
  @State isLoadingMore: boolean = false;
  @State refreshing: boolean = false;
  @State viewMode: 'list' | 'grid' = 'list';
  @State showFilter: boolean = false;
  @State sortBy: string = 'downloadCount';
  @State filterOptions: FilterOptions = {};

  private pageTitle: string = '应用列表';
  private searchParams: AppSearchParams = {
    page: 1,
    page_size: 20
  };
  private deviceUtils = DeviceUtils.getInstance();
  private apiService = ApiService.getInstance();

  aboutToAppear() {
    const params = this.getUIContext().getRouter().getParams() as AppListPageParams;
    
    this.pageTitle = params?.title || '应用列表';
    
    if (params?.searchParams) {
      try {
        const parsedParams = JSON.parse(params.searchParams) as AppSearchParams;
        this.searchParams = {
          page: parsedParams.page ?? this.searchParams.page,
          page_size: parsedParams.page_size ?? this.searchParams.page_size,
          category: parsedParams.category ?? this.searchParams.category,
          sort: parsedParams.sort ?? this.searchParams.sort,
          keyword: parsedParams.keyword ?? this.searchParams.keyword
        };
      } catch (error) {
        hilog.error(0x0000, 'AppListPage', '解析搜索参数失败: %{public}s', JSON.stringify(error));
      }
    }
    
    this.loadApps();
  }

  /**
   * 检查并设置认证token
   */
  private async checkAndSetAuthToken(): Promise<void> {
    try {
      const context = this.getUIContext().getHostContext();
      const options: preferences.Options = { name: 'user_data' };
      const dataPreferences = preferences.getPreferencesSync(context, options);
      const token = dataPreferences.getSync('token', '') as string;
      
      if (token) {
        this.apiService.setAuthToken(token);
      }
    } catch (error) {
      hilog.error(0x0000, 'AppListPage', '检查登录状态失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 加载应用列表
   */
  private async loadApps(loadMore: boolean = false) {
    try {
      if (!loadMore) {
        this.loadingState = LoadingState.LOADING;
        this.currentPage = 1;
        this.apps = [];
        
        // 检查登录状态并设置token
        await this.checkAndSetAuthToken();
      } else {
        this.isLoadingMore = true;
      }

      const additionalParams: AppSearchParams = {
        sort: this.sortBy,
        page: loadMore ? this.currentPage + 1 : 1,
        page_size: 20
      };
      const params: AppSearchParams = {
        page: additionalParams.page ?? this.searchParams.page,
        page_size: additionalParams.page_size ?? this.searchParams.page_size,
        category: this.searchParams.category,
        sort: additionalParams.sort ?? this.searchParams.sort,
        keyword: this.searchParams.keyword
      };

      const response = await this.apiService.getAppList(params);
      
      if (response.code === 200 && response.data) {
        if (loadMore) {
          // 使用concat方法替代spread操作符，并处理可能的undefined
          const newApps = response.data.list || response.data.data || [];
          this.apps = this.apps.concat(newApps);
          this.currentPage++;
        } else {
          this.apps = response.data.list || response.data.data || [];
          this.currentPage = 1;
        }
        
        this.hasMore = response.data.pagination?.hasNext ?? false;
        
        if (!this.apps || this.apps.length === 0) {
          this.loadingState = LoadingState.EMPTY;
        } else {
          this.loadingState = LoadingState.SUCCESS;
        }
      } else {
        this.loadingState = LoadingState.ERROR;
      }
    } catch (error) {
      hilog.error(0x0000, 'AppListPage', '加载应用列表失败: %{public}s', JSON.stringify(error));
      this.loadingState = LoadingState.ERROR;
    } finally {
      this.isLoadingMore = false;
    }
  }

  /**
   * 刷新数据
   */
  private async refreshData() {
    this.refreshing = true;
    await this.loadApps();
    this.refreshing = false;
  }

  /**
   * 跳转到应用详情
   */
  private navigateToDetail(appId: string): void {
    this.getUIContext().getRouter().pushUrl({
      url: 'pages/AppDetailPage',
      params: { appId }
    });
  }

  /**
   * 应用筛选器
   */
  @Builder
  private FilterPanel() {
    if (this.showFilter) {
      Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE) }) {
        // 标题
        Row() {
          Text('筛选条件')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
            .fontWeight(FontWeight.Bold)
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .layoutWeight(1)

          Text('×')
            .fontSize(24)
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .onClick((): void => {
              this.showFilter = false;
            })
        }
        .width('100%')

        // 排序方式
        Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.SMALL) }) {
          Text('排序方式')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontWeight(FontWeight.Medium)
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .alignSelf(ItemAlign.Start)

          Flex({ wrap: FlexWrap.Wrap }) {
            ForEach([
              { key: 'downloadCount', label: '下载量' },
              { key: 'rating', label: '评分' },
              { key: 'updatedAt', label: '更新时间' },
              { key: 'createdAt', label: '发布时间' },
              { key: 'name', label: '名称' },
              { key: 'size', label: '大小' }
            ], (sort: SortItem) => {
              Text(sort.label)
                .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
                .fontColor(this.sortBy === sort.key ? Constants.COLORS.WHITE : Constants.COLORS.TEXT_PRIMARY)
                .backgroundColor(this.sortBy === sort.key ? Constants.COLORS.PRIMARY : Constants.COLORS.BACKGROUND_LIGHT)
                .padding({ left: '12vp', right: '12vp', top: '6vp', bottom: '6vp' })
                .borderRadius(Constants.BORDER_RADIUS.SMALL)
                .onClick((): void => {
                  this.sortBy = sort.key;
                })
            })
          }
        }

        Divider().color(Constants.COLORS.BORDER)

        // 价格筛选
        Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.SMALL) }) {
          Text('价格')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontWeight(FontWeight.Medium)
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .alignSelf(ItemAlign.Start)

          Row({ space: 12 }) {
            Text('全部')
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
              .fontColor(this.filterOptions.isFree === undefined ? Constants.COLORS.WHITE : Constants.COLORS.TEXT_PRIMARY)
              .backgroundColor(this.filterOptions.isFree === undefined ? Constants.COLORS.PRIMARY : Constants.COLORS.BACKGROUND_LIGHT)
              .padding({ left: '12vp', right: '12vp', top: '6vp', bottom: '6vp' })
              .borderRadius(Constants.BORDER_RADIUS.SMALL)
              .onClick((): void => {
                this.filterOptions.isFree = undefined;
              })

            Text('免费')
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
              .fontColor(this.filterOptions.isFree === true ? Constants.COLORS.WHITE : Constants.COLORS.TEXT_PRIMARY)
              .backgroundColor(this.filterOptions.isFree === true ? Constants.COLORS.PRIMARY : Constants.COLORS.BACKGROUND_LIGHT)
              .padding({ left: '12vp', right: '12vp', top: '6vp', bottom: '6vp' })
              .borderRadius(Constants.BORDER_RADIUS.SMALL)
              .onClick((): void => {
                this.filterOptions.isFree = true;
              })

            Text('付费')
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
              .fontColor(this.filterOptions.isFree === false ? Constants.COLORS.WHITE : Constants.COLORS.TEXT_PRIMARY)
              .backgroundColor(this.filterOptions.isFree === false ? Constants.COLORS.PRIMARY : Constants.COLORS.BACKGROUND_LIGHT)
              .padding({ left: '12vp', right: '12vp', top: '6vp', bottom: '6vp' })
              .borderRadius(Constants.BORDER_RADIUS.SMALL)
              .onClick((): void => {
                this.filterOptions.isFree = false;
              })
          }
        }

        Divider().color(Constants.COLORS.BORDER)

        // 评分筛选
        Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.SMALL) }) {
          Text('最低评分')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontWeight(FontWeight.Medium)
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .alignSelf(ItemAlign.Start)

          Row({ space: 8 }) {
            ForEach([0, 1, 2, 3, 4] as number[], (rating: number) => {
              Row({ space: 4 }) {
                ForEach([1, 2, 3, 4, 5], (star: number) => {
                  Text('★')
                    .fontSize(12)
                    .fontColor(star <= rating + 1 ? Constants.COLORS.WARNING : Constants.COLORS.BORDER)
                }, (star: number) => star.toString())
              }
              .padding({ left: '8vp', right: '8vp', top: '4vp', bottom: '4vp' })
              .backgroundColor(this.filterOptions.minRating === rating + 1 ? Constants.COLORS.PRIMARY : Constants.COLORS.BACKGROUND_LIGHT)
              .borderRadius(Constants.BORDER_RADIUS.SMALL)
              .onClick(() => {
                this.filterOptions.minRating = this.filterOptions.minRating === rating + 1 ? undefined : rating + 1;
              })
            }, (rating: number) => rating.toString())
          }
        }

        // 操作按钮
        Row({ space: 12 }) {
          Button('重置')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .backgroundColor(Constants.COLORS.BACKGROUND_LIGHT)
            .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
            .layoutWeight(1)
            .onClick(() => {
              this.filterOptions = {};
              this.sortBy = 'downloadCount';
            })

          Button('确定')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor(Constants.COLORS.WHITE)
            .backgroundColor(Constants.COLORS.PRIMARY)
            .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
            .layoutWeight(1)
            .onClick(() => {
              this.showFilter = false;
              this.loadApps();
            })
        }
        .width('100%')
      }
      .width('100%')
      .padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE))
      .backgroundColor(Constants.COLORS.WHITE)
      .borderRadius({ topLeft: Constants.BORDER_RADIUS.LARGE, topRight: Constants.BORDER_RADIUS.LARGE })
      .animation({ duration: 300, curve: Curve.EaseInOut })
    }
  }

  /**
   * 应用网格（平板设备）
   */
  @Builder
  private AppGrid() {
    Grid() {
      ForEach(this.apps, (app: AppModel) => {
        GridItem() {
          AppCard({
            app: app,
            cardType: 'grid',
            showDownloadButton: true,
            onAppClick: (app: AppModel): void => this.navigateToDetail(app.id.toString())
          })
        }
      })

      // 加载更多
      if (this.hasMore || this.isLoadingMore) {
        GridItem() {
          LoadMoreView({
            isLoading: this.isLoadingMore,
            hasMore: this.hasMore,
            onLoadMore: () => {
              if (!this.isLoadingMore && this.hasMore) {
                this.loadApps(true);
              }
            }
          })
        }
        .columnStart(0)
        .columnEnd(this.deviceUtils.isTablet() ? 2 : 1)
      }
    }
    .columnsTemplate(this.deviceUtils.isTablet() ? '1fr 1fr 1fr' : '1fr 1fr')
    .rowsGap(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
    .columnsGap(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
    .padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
    .scrollBar(BarState.Auto)
  }

  /**
   * 应用列表（手机设备）
   */
  @Builder
  private AppList() {
    List({ space: 8 }) {
      ForEach(this.apps, (app: AppModel) => {
        ListItem() {
          AppCard({
            app: app,
            cardType: 'list',
            showDownloadButton: true,
            onAppClick: (app: AppModel): void => this.navigateToDetail(app.id.toString())
          })
            .margin({ left: 16, right: 16 })
        }
      })

      // 加载更多
      if (this.hasMore || this.isLoadingMore) {
        ListItem() {
          LoadMoreView({
            isLoading: this.isLoadingMore,
            hasMore: this.hasMore,
            onLoadMore: () => {
              if (!this.isLoadingMore && this.hasMore) {
                this.loadApps(true);
              }
            }
          })
        }
      }
    }
    .scrollBar(BarState.Auto)
  }

  /**
   * 获取排序标签
   */
  private getSortLabel(): string {
    switch (this.sortBy) {
      case 'downloadCount':
        return '下载量';
      case 'rating':
        return '评分';
      case 'updatedAt':
        return '更新时间';
      case 'createdAt':
        return '发布时间';
      case 'name':
        return '名称';
      case 'size':
        return '大小';
      default:
        return '排序';
    }
  }

  build() {
    Stack({ alignContent: Alignment.Bottom }) {
      Column() {
        // 顶部导航栏
        Row() {
          Text('←')
            .fontSize(24)
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .onClick((): void => {
              this.getUIContext().getRouter().back();
            })

          Text(this.pageTitle)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
            .fontWeight(FontWeight.Medium)
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .layoutWeight(1)
            .textAlign(TextAlign.Center)
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })

          Row({ space: 12 }) {
            // 视图切换（仅平板显示）
            if (this.deviceUtils.isTablet()) {
              Text(this.viewMode === 'list' ? '☰' : '⊞')
                .fontSize(20)
                .fontColor(Constants.COLORS.TEXT_PRIMARY)
                .onClick(() => {
                  this.viewMode = this.viewMode === 'list' ? 'grid' : 'list';
                })
            }

            // 筛选按钮
            Text('⚙')
              .fontSize(20)
              .fontColor(this.showFilter ? Constants.COLORS.PRIMARY : Constants.COLORS.TEXT_PRIMARY)
              .onClick((): void => {
                this.showFilter = !this.showFilter;
              })
          }
        }
        .width('100%')
        .height(56)
        .padding({ left: '16vp', right: '16vp' })
        .justifyContent(FlexAlign.SpaceBetween)
        .alignItems(VerticalAlign.Center)
        .backgroundColor(Constants.COLORS.WHITE)

        // 排序信息
        if (this.loadingState === LoadingState.SUCCESS && this.apps && this.apps.length > 0) {
          Row() {
            Text(`共 ${this.apps.length} 个应用`)
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
              .fontColor(Constants.COLORS.TEXT_SECONDARY)
              .layoutWeight(1)

            Text(`按${this.getSortLabel()}排序`)
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
              .fontColor(Constants.COLORS.TEXT_HINT)
          }
          .width('100%')
          .padding({ left: '16vp', right: '16vp', top: '8vp', bottom: '8vp' })
          .backgroundColor(Constants.COLORS.BACKGROUND_LIGHT)
        }

        if (this.loadingState === LoadingState.LOADING) {
          LoadingView({ state: LoadingState.LOADING })
            .layoutWeight(1)
        } else if (this.loadingState === LoadingState.ERROR) {
          LoadingView({ 
            state: LoadingState.ERROR,
            onRetry: () => { this.loadApps(); }
          })
            .layoutWeight(1)
        } else if (this.loadingState === LoadingState.EMPTY) {
          LoadingView({ 
            state: LoadingState.EMPTY,
            message: '暂无应用数据'
          })
            .layoutWeight(1)
        } else {
          // 应用列表
          Refresh({ refreshing: this.refreshing, offset: 64, friction: 100 }) {
            if (this.deviceUtils.isTablet() && this.viewMode === 'grid') {
              this.AppGrid()
            } else {
              this.AppList()
            }
          }
          .onStateChange((refreshStatus: RefreshStatus) => {
            if (refreshStatus === RefreshStatus.Refresh) {
              this.refreshData();
            }
          })
          .layoutWeight(1)
        }
      }
      .width('100%')
      .height('100%')
      .backgroundColor(Constants.COLORS.BACKGROUND)

      // 筛选面板
      this.FilterPanel()
    }
    .width('100%')
    .height('100%')
    .onClick(() => {
      if (this.showFilter) {
        this.showFilter = false;
      }
    })
  }
}