package models

import (
	"time"

	"gorm.io/gorm"
)

// OpenHarmonyVersion OpenHarmonyOS版本模型
type OpenHarmonyVersion struct {
	ID          uint           `gorm:"primarykey" json:"id"`
	VersionName string         `gorm:"type:varchar(50);not null" json:"version_name"` // 版本名称，如 "OpenHarmonyOS 4.0"
	VersionCode string         `gorm:"type:varchar(20);uniqueIndex;not null" json:"version_code"` // 版本代码，如 "4.0.0"
	Description string         `gorm:"type:varchar(255)" json:"description"` // 版本描述
	IsActive    bool           `gorm:"default:true" json:"is_active"` // 是否启用
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (OpenHarmonyVersion) TableName() string {
	return "open_harmony_versions"
}

// CreateOpenHarmonyVersion 创建OpenHarmonyOS版本
func CreateOpenHarmonyVersion(db *gorm.DB, version *OpenHarmonyVersion) error {
	return db.Create(version).Error
}

// GetOpenHarmonyVersionByID 通过ID获取版本
func GetOpenHarmonyVersionByID(db *gorm.DB, id uint) (*OpenHarmonyVersion, error) {
	var version OpenHarmonyVersion
	err := db.First(&version, id).Error
	return &version, err
}

// GetOpenHarmonyVersionByCode 通过版本代码获取版本
func GetOpenHarmonyVersionByCode(db *gorm.DB, code string) (*OpenHarmonyVersion, error) {
	var version OpenHarmonyVersion
	err := db.Where("version_code = ?", code).First(&version).Error
	return &version, err
}

// GetOpenHarmonyVersions 获取版本列表
func GetOpenHarmonyVersions(db *gorm.DB, isActive *bool, offset, limit int) ([]OpenHarmonyVersion, int64, error) {
	var versions []OpenHarmonyVersion
	var total int64

	query := db.Model(&OpenHarmonyVersion{})
	if isActive != nil {
		query = query.Where("is_active = ?", *isActive)
	}

	// 获取总数
	query.Count(&total)

	// 获取数据
	err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&versions).Error
	return versions, total, err
}

// GetActiveOpenHarmonyVersions 获取启用的版本列表
func GetActiveOpenHarmonyVersions(db *gorm.DB) ([]OpenHarmonyVersion, error) {
	var versions []OpenHarmonyVersion
	err := db.Where("is_active = ?", true).Order("version_code DESC").Find(&versions).Error
	return versions, err
}

// UpdateOpenHarmonyVersion 更新版本信息
func UpdateOpenHarmonyVersion(db *gorm.DB, version *OpenHarmonyVersion) error {
	return db.Save(version).Error
}

// DeleteOpenHarmonyVersion 删除版本
func DeleteOpenHarmonyVersion(db *gorm.DB, id uint) error {
	return db.Delete(&OpenHarmonyVersion{}, id).Error
}

// IsVersionCodeExists 检查版本代码是否存在
func IsVersionCodeExists(db *gorm.DB, code string, excludeID ...uint) bool {
	query := db.Model(&OpenHarmonyVersion{}).Where("version_code = ?", code)
	if len(excludeID) > 0 {
		query = query.Where("id != ?", excludeID[0])
	}

	var count int64
	query.Count(&count)
	return count > 0
}

// GetVersionUsageCount 获取版本使用次数
func GetVersionUsageCount(db *gorm.DB, versionCode string) (int64, error) {
	var count int64
	err := db.Model(&Application{}).Where("min_open_harmony_os_ver = ?", versionCode).Count(&count).Error
	return count, err
}