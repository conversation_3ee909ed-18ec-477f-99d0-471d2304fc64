import { <PERSON>Container } from '@ant-design/pro-components';
import { Card, Row, Col, Statistic, Table, DatePicker, Button, Tabs, Select, Radio } from 'antd';
import { Line, Column, Pie } from '@ant-design/plots';
import React, { useState } from 'react';
import { useRequest } from '@umijs/max';
import { 
  UserOutlined, 
  TeamOutlined, 
  RiseOutlined, 
  FallOutlined,
  AppstoreOutlined,
  MobileOutlined,
  DesktopOutlined,
  GlobalOutlined
} from '@ant-design/icons';

const { RangePicker } = DatePicker;
const { TabPane } = Tabs;
const { Option } = Select;

// 模拟获取用户统计数据
const fetchUserStatistics = async (params: any) => {
  console.log('Fetching user statistics with params:', params);
  
  // 模拟数据
  const overviewData = {
    totalUsers: 125680,
    newUsers: 1256,
    activeUsers: 45890,
    retentionRate: 68.5,
    conversionRate: 12.3,
    averageSessionTime: 15.6,
    userGrowthRate: 5.2,
  };
  
  // 模拟趋势数据
  const trendData = [
    { date: '2023-01-01', users: 100000, newUsers: 1000, activeUsers: 40000 },
    { date: '2023-02-01', users: 105000, newUsers: 1100, activeUsers: 41000 },
    { date: '2023-03-01', users: 110000, newUsers: 1200, activeUsers: 42000 },
    { date: '2023-04-01', users: 112000, newUsers: 900, activeUsers: 41500 },
    { date: '2023-05-01', users: 115000, newUsers: 950, activeUsers: 42500 },
    { date: '2023-06-01', users: 118000, newUsers: 1050, activeUsers: 43000 },
    { date: '2023-07-01', users: 120000, newUsers: 1100, activeUsers: 43500 },
    { date: '2023-08-01', users: 122000, newUsers: 1150, activeUsers: 44000 },
    { date: '2023-09-01', users: 123500, newUsers: 1200, activeUsers: 44500 },
    { date: '2023-10-01', users: 124800, newUsers: 1250, activeUsers: 45000 },
    { date: '2023-11-01', users: 125680, newUsers: 1256, activeUsers: 45890 },
  ];
  
  // 模拟用户分布数据
  const distributionData = {
    byAge: [
      { type: '18岁以下', value: 15 },
      { type: '18-24岁', value: 25 },
      { type: '25-34岁', value: 35 },
      { type: '35-44岁', value: 15 },
      { type: '45岁以上', value: 10 },
    ],
    byGender: [
      { type: '男', value: 60 },
      { type: '女', value: 38 },
      { type: '其他', value: 2 },
    ],
    byPlatform: [
      { type: 'iOS', value: 45 },
      { type: 'Android', value: 50 },
      { type: 'Web', value: 5 },
    ],
    byRegion: [
      { type: '华东', value: 30 },
      { type: '华南', value: 20 },
      { type: '华北', value: 25 },
      { type: '西南', value: 10 },
      { type: '西北', value: 5 },
      { type: '东北', value: 8 },
      { type: '海外', value: 2 },
    ],
  };
  
  // 模拟用户行为数据
  const behaviorData = {
    topFeatures: [
      { feature: '应用下载', count: 25680 },
      { feature: '应用评分', count: 18450 },
      { feature: '应用评论', count: 12340 },
      { feature: '应用分享', count: 9870 },
      { feature: '应用收藏', count: 7650 },
    ],
    userRetention: [
      { day: '次日', rate: 65 },
      { day: '7日', rate: 45 },
      { day: '15日', rate: 35 },
      { day: '30日', rate: 28 },
    ],
    userActivity: [
      { hour: '0-2', count: 1200 },
      { hour: '2-4', count: 800 },
      { hour: '4-6', count: 600 },
      { hour: '6-8', count: 2500 },
      { hour: '8-10', count: 5800 },
      { hour: '10-12', count: 8900 },
      { hour: '12-14', count: 7600 },
      { hour: '14-16', count: 8200 },
      { hour: '16-18', count: 9500 },
      { hour: '18-20', count: 12500 },
      { hour: '20-22', count: 10800 },
      { hour: '22-24', count: 6500 },
    ],
  };

  return {
    overview: overviewData,
    trend: trendData,
    distribution: distributionData,
    behavior: behaviorData,
  };
};

const UserStatistics: React.FC = () => {
  const [timeRange, setTimeRange] = useState<[string, string]>(['2023-01-01', '2023-11-01']);
  const [distributionType, setDistributionType] = useState<string>('byAge');
  const [chartType, setChartType] = useState<string>('total');

  const { data, loading, refresh } = useRequest(() => fetchUserStatistics({ timeRange }), {
    refreshDeps: [timeRange],
  });

  const handleTimeRangeChange = (dates: any, dateStrings: [string, string]) => {
    setTimeRange(dateStrings);
  };

  // 用户趋势图配置
  const trendConfig = {
    data: data?.trend || [],
    xField: 'date',
    yField: chartType === 'total' ? 'users' : chartType === 'new' ? 'newUsers' : 'activeUsers',
    smooth: true,
    meta: {
      date: {
        alias: '日期',
      },
      users: {
        alias: '用户总数',
      },
      newUsers: {
        alias: '新增用户',
      },
      activeUsers: {
        alias: '活跃用户',
      },
    },
  };

  // 用户分布图配置
  const distributionConfig = {
    data: data?.distribution[distributionType] || [],
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name}: {percentage}',
    },
    interactions: [{ type: 'element-active' }],
  };

  // 用户活跃时段图配置
  const activityConfig = {
    data: data?.behavior.userActivity || [],
    xField: 'hour',
    yField: 'count',
    meta: {
      hour: {
        alias: '时段',
      },
      count: {
        alias: '活跃用户数',
      },
    },
  };

  // 用户留存率图配置
  const retentionConfig = {
    data: data?.behavior.userRetention || [],
    xField: 'day',
    yField: 'rate',
    meta: {
      day: {
        alias: '留存天数',
      },
      rate: {
        alias: '留存率(%)',
      },
    },
  };

  return (
    <PageContainer
      header={{
        title: '用户数据统计',
        subTitle: '分析用户增长、活跃度和行为特征',
      }}
    >
      {/* 时间范围选择器 */}
      <div style={{ marginBottom: 16 }}>
        <RangePicker 
          value={[moment(timeRange[0]), moment(timeRange[1])]} 
          onChange={handleTimeRangeChange} 
          style={{ width: 300 }}
        />
        <Button 
          type="primary" 
          onClick={refresh} 
          style={{ marginLeft: 16 }}
        >
          刷新数据
        </Button>
      </div>

      {/* 用户概览 */}
      <Card title="用户概览" style={{ marginBottom: 16 }} loading={loading}>
        <Row gutter={16}>
          <Col span={6}>
            <Statistic 
              title="用户总数" 
              value={data?.overview.totalUsers} 
              prefix={<UserOutlined />} 
              suffix="人"
            />
          </Col>
          <Col span={6}>
            <Statistic 
              title="新增用户" 
              value={data?.overview.newUsers} 
              prefix={<TeamOutlined />}
              suffix={
                <span style={{ fontSize: 14, color: '#52c41a' }}>
                  <RiseOutlined /> {data?.overview.userGrowthRate}%
                </span>
              }
            />
          </Col>
          <Col span={6}>
            <Statistic 
              title="活跃用户" 
              value={data?.overview.activeUsers} 
              prefix={<UserOutlined />}
              suffix="人"
            />
          </Col>
          <Col span={6}>
            <Statistic 
              title="用户留存率" 
              value={data?.overview.retentionRate} 
              suffix="%" 
            />
          </Col>
        </Row>
        <Row gutter={16} style={{ marginTop: 16 }}>
          <Col span={6}>
            <Statistic 
              title="转化率" 
              value={data?.overview.conversionRate} 
              suffix="%" 
            />
          </Col>
          <Col span={6}>
            <Statistic 
              title="平均会话时长" 
              value={data?.overview.averageSessionTime} 
              suffix="分钟" 
            />
          </Col>
        </Row>
      </Card>

      {/* 用户趋势 */}
      <Card title="用户趋势" style={{ marginBottom: 16 }} loading={loading}>
        <div style={{ marginBottom: 16 }}>
          <Radio.Group value={chartType} onChange={e => setChartType(e.target.value)}>
            <Radio.Button value="total">用户总数</Radio.Button>
            <Radio.Button value="new">新增用户</Radio.Button>
            <Radio.Button value="active">活跃用户</Radio.Button>
          </Radio.Group>
        </div>
        <Line {...trendConfig} height={300} />
      </Card>

      {/* 用户分布 */}
      <Card title="用户分布" style={{ marginBottom: 16 }} loading={loading}>
        <div style={{ marginBottom: 16 }}>
          <Radio.Group value={distributionType} onChange={e => setDistributionType(e.target.value)}>
            <Radio.Button value="byAge">年龄分布</Radio.Button>
            <Radio.Button value="byGender">性别分布</Radio.Button>
            <Radio.Button value="byPlatform">平台分布</Radio.Button>
            <Radio.Button value="byRegion">地区分布</Radio.Button>
          </Radio.Group>
        </div>
        <Pie {...distributionConfig} height={300} />
      </Card>

      {/* 用户行为 */}
      <Tabs defaultActiveKey="features" style={{ marginBottom: 16 }}>
        <TabPane tab="热门功能" key="features">
          <Card loading={loading}>
            <Table 
              dataSource={data?.behavior.topFeatures} 
              rowKey="feature"
              pagination={false}
            >
              <Table.Column title="功能" dataIndex="feature" />
              <Table.Column title="使用次数" dataIndex="count" />
            </Table>
          </Card>
        </TabPane>
        <TabPane tab="用户留存" key="retention">
          <Card loading={loading}>
            <Column {...retentionConfig} height={300} />
          </Card>
        </TabPane>
        <TabPane tab="活跃时段" key="activity">
          <Card loading={loading}>
            <Column {...activityConfig} height={300} />
          </Card>
        </TabPane>
      </Tabs>
    </PageContainer>
  );
};

export default UserStatistics;