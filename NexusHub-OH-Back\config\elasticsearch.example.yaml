# Elasticsearch 配置示例
# 复制此文件为 elasticsearch.yaml 并根据实际环境修改配置

elasticsearch:
  # Elasticsearch 服务器地址
  host: "localhost"
  
  # Elasticsearch 服务器端口
  port: 9200
  
  # 是否启用认证
  use_auth: false
  
  # 用户名（当 use_auth 为 true 时需要）
  username: "elastic"
  
  # 密码（当 use_auth 为 true 时需要）
  password: "your_password_here"
  
  # 连接超时时间（秒）
  timeout: 30
  
  # 最大重试次数
  max_retries: 3
  
  # 索引设置
  indices:
    # 应用索引名称
    app_index: "nexushub_apps"
    
    # 用户索引名称
    user_index: "nexushub_users"
    
    # 评论索引名称
    review_index: "nexushub_reviews"
    
    # 标签索引名称
    tag_index: "nexushub_tags"
    
    # 分片数量
    number_of_shards: 1
    
    # 副本数量
    number_of_replicas: 0
    
    # 刷新间隔
    refresh_interval: "1s"

# 搜索配置
search:
  # 默认搜索结果数量
  default_size: 20
  
  # 最大搜索结果数量
  max_size: 100
  
  # 搜索超时时间（毫秒）
  timeout: 5000
  
  # 高亮设置
  highlight:
    enabled: true
    pre_tags: ["<mark>"]
    post_tags: ["</mark>"]
    fragment_size: 150
    number_of_fragments: 3

# 同步配置
sync:
  # 批量同步大小
  batch_size: 1000
  
  # 同步超时时间（秒）
  timeout: 300
  
  # 是否在启动时自动同步
  auto_sync_on_startup: false
  
  # 定时同步间隔（小时，0表示禁用）
  sync_interval_hours: 0