import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';
import { router } from '@kit.ArkUI';
import { preferences } from '@kit.ArkData';
import { bundleManager } from '@kit.AbilityKit';
import { BusinessError } from '@kit.BasicServicesKit';
// getContext is deprecated, use this.getUIContext().getHostContext() instead
import { hilog } from '@kit.PerformanceAnalysisKit';
import { promptAction } from '@kit.ArkUI';
import { ConfigurationConstant } from '@kit.AbilityKit';

interface SettingItem {
  icon: Resource;
  title: string;
  subtitle?: string;
  value?: string;
  type: 'navigation' | 'switch' | 'selection' | 'info';
  switchValue?: boolean;
  options?: string[];
  selectedIndex?: number;
  onClick?: () => void;
  onSwitchChange?: (value: boolean) => void;
  onSelectionChange?: (index: number) => void;
}

/**
 * 设置页面
 */
@Entry
@Component
struct SettingsPage {
  @State autoUpdate: boolean = true;
  @State wifiOnlyDownload: boolean = true;
  @State pushNotifications: boolean = true;
  @State downloadNotifications: boolean = true;
  @State updateNotifications: boolean = true;
  @State darkModeFollowSystem: boolean = true;
  @State darkModeManual: boolean = false;
  @State language: string = '简体中文';
  @State downloadPath: string = '/storage/emulated/0/Download';
  @State cacheSize: string = '计算中...';
  @State appVersion: string = '1.0.0';
  @State showLanguageDialog: boolean = false;
  @State showClearCacheDialog: boolean = false;

  private deviceUtils = DeviceUtils.getInstance();
  private languageOptions: string[] = ['简体中文', 'English', '繁體中文', '日本語'];

  aboutToAppear() {
    this.loadSettings();
    this.calculateCacheSize();
    this.getAppVersion();
  }

  /**
   * 加载设置
   */
  private async loadSettings() {
    try {
      const context = this.getUIContext().getHostContext();
      const dataPreferences = preferences.getPreferencesSync(context, { name: 'app_settings' });
      
      const autoUpdateValue: preferences.ValueType = dataPreferences.getSync('auto_update', true);
      this.autoUpdate = typeof autoUpdateValue === 'boolean' ? autoUpdateValue : true;
      
      const wifiOnlyValue: preferences.ValueType = dataPreferences.getSync('wifi_only_download', true);
      this.wifiOnlyDownload = typeof wifiOnlyValue === 'boolean' ? wifiOnlyValue : true;
      
      const pushNotificationsValue: preferences.ValueType = dataPreferences.getSync('push_notifications', true);
      this.pushNotifications = typeof pushNotificationsValue === 'boolean' ? pushNotificationsValue : true;
      
      const downloadNotificationsValue: preferences.ValueType = dataPreferences.getSync('download_notifications', true);
      this.downloadNotifications = typeof downloadNotificationsValue === 'boolean' ? downloadNotificationsValue : true;
      
      const updateNotificationsValue: preferences.ValueType = dataPreferences.getSync('update_notifications', true);
      this.updateNotifications = typeof updateNotificationsValue === 'boolean' ? updateNotificationsValue : true;
      
      const darkModeFollowSystemValue: preferences.ValueType = dataPreferences.getSync(Constants.STORAGE_KEYS.DARK_MODE_FOLLOW_SYSTEM, true);
      this.darkModeFollowSystem = typeof darkModeFollowSystemValue === 'boolean' ? darkModeFollowSystemValue : true;
      
      const darkModeManualValue: preferences.ValueType = dataPreferences.getSync(Constants.STORAGE_KEYS.DARK_MODE_MANUAL, false);
      this.darkModeManual = typeof darkModeManualValue === 'boolean' ? darkModeManualValue : false;
      
      const languageValue: preferences.ValueType = dataPreferences.getSync('language', '简体中文');
      this.language = typeof languageValue === 'string' ? languageValue : '简体中文';
      
      const downloadPathValue: preferences.ValueType = dataPreferences.getSync('download_path', '/storage/emulated/0/Download');
      this.downloadPath = typeof downloadPathValue === 'string' ? downloadPathValue : '/storage/emulated/0/Download';
    } catch (error) {
      hilog.error(0x0000, 'SettingsPage', '加载设置失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 保存设置
   */
  private async saveSetting(key: string, value: preferences.ValueType) {
    try {
      const context = this.getUIContext().getHostContext();
      const dataPreferences = preferences.getPreferencesSync(context, { name: 'app_settings' });
      dataPreferences.putSync(key, value);
      dataPreferences.flush();
    } catch (error) {
      hilog.error(0x0000, 'SettingsPage', '保存设置失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 计算缓存大小
   */
  private async calculateCacheSize() {
    try {
      // 这里应该计算实际的缓存大小
      // 暂时使用模拟数据
      setTimeout(() => {
        this.cacheSize = '156.8 MB';
      }, 1000);
    } catch (error) {
      hilog.error(0x0000, 'SettingsPage', '计算缓存大小失败: %{public}s', JSON.stringify(error));
      this.cacheSize = '未知';
    }
  }

  /**
   * 获取应用版本
   */
  private async getAppVersion() {
    try {
      const context = this.getUIContext().getHostContext();
      const bundleInfo = await bundleManager.getBundleInfoForSelf(bundleManager.BundleFlag.GET_BUNDLE_INFO_DEFAULT);
      this.appVersion = bundleInfo.versionName || '1.0.0';
    } catch (error) {
      hilog.error(0x0000, 'SettingsPage', '获取应用版本失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 清除缓存
   */
  private async clearCache() {
    try {
      // 这里应该实现实际的缓存清理逻辑
      this.getUIContext().getPromptAction().showToast({ message: '缓存清理中...' });
      
      setTimeout(() => {
        this.cacheSize = '0 MB';
        this.getUIContext().getPromptAction().showToast({ message: '缓存清理完成' });
      }, 2000);
    } catch (error) {
      hilog.error(0x0000, 'SettingsPage', '清除缓存失败: %{public}s', JSON.stringify(error));
      this.getUIContext().getPromptAction().showToast({ message: '清除缓存失败' });
    }
  }

  /**
   * 检查更新
   */
  private checkUpdate(): void {
    this.getUIContext().getPromptAction().showToast({ message: '检查更新中...' });
    
    setTimeout(() => {
      this.getUIContext().getPromptAction().showToast({ message: '当前已是最新版本' });
    }, 2000);
  }

  /**
   * 应用颜色模式设置
   */
  private applyColorMode(): void {
    try {
      const context = this.getUIContext().getHostContext();
      
      if (!context) {
        hilog.error(0x0000, 'SettingsPage', '获取context失败');
        this.getUIContext().getPromptAction().showToast({ message: '设置失败，请重试' });
        return;
      }
      
      if (this.darkModeFollowSystem) {
        // 跟随系统设置
        context.getApplicationContext().setColorMode(ConfigurationConstant.ColorMode.COLOR_MODE_NOT_SET);
        this.getUIContext().getPromptAction().showToast({ message: '已设置为跟随系统深色模式' });
      } else {
        // 手动设置
        if (this.darkModeManual) {
          context.getApplicationContext().setColorMode(ConfigurationConstant.ColorMode.COLOR_MODE_DARK);
          this.getUIContext().getPromptAction().showToast({ message: '已开启深色模式' });
        } else {
          context.getApplicationContext().setColorMode(ConfigurationConstant.ColorMode.COLOR_MODE_LIGHT);
          this.getUIContext().getPromptAction().showToast({ message: '已开启浅色模式' });
        }
      }
    } catch (error) {
      hilog.error(0x0000, 'SettingsPage', '应用颜色模式失败: %{public}s', JSON.stringify(error));
      this.getUIContext().getPromptAction().showToast({ message: '设置失败，请重试' });
    }
  }

  /**
   * 设置项组件
   */
  @Builder
  private SettingItemBuilder(item: SettingItem) {
    Row({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) }) {
      Image(item.icon)
        .width(24)
        .height(24)

      Column({ space: 2 }) {
        Text(item.title)
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .fontWeight(FontWeight.Medium)

        if (item.subtitle) {
          Text(item.subtitle)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.TEXT_HINT)
        }
      }
      .alignItems(HorizontalAlign.Start)
      .layoutWeight(1)

      if (item.type === 'switch') {
        Toggle({ type: ToggleType.Switch, isOn: item.switchValue || false })
          .selectedColor(Constants.COLORS.PRIMARY)
          .onChange((isOn: boolean) => {
            item.onSwitchChange?.(isOn);
          })
      } else if (item.type === 'selection') {
        Row({ space: 4 }) {
          Text(item.value || '')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.TEXT_SECONDARY)
          
          Image($r('app.media.ic_arrow_right'))
            .width(16)
            .height(16)
        }
      } else if (item.type === 'info') {
        Text(item.value || '')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
      } else {
        Image($r('app.media.ic_arrow_right'))
          .width(16)
          .height(16)
      }
    }
    .width('100%')
    .height(this.deviceUtils.isTablet() ? 64 : 56)
    .padding({ left: '16vp', right: '16vp' })
    .onClick(() => {
      if (item.type !== 'switch' && item.type !== 'info') {
        item.onClick?.();
      }
    })
  }

  /**
   * 设置分组
   */
  @Builder
  private SettingGroup(title: string, items: SettingItem[]) {
    Column() {
      // 分组标题
      Text(title)
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
        .fontColor(Constants.COLORS.TEXT_HINT)
        .fontWeight(FontWeight.Medium)
        .width('100%')
        .padding({ left: '16vp', right: '16vp', top: '16vp', bottom: '8vp' })

      // 设置项
      Column() {
        ForEach(items, (item: SettingItem, index: number) => {
          this.SettingItemBuilder(item)
          
          if (index < items.length - 1) {
            Divider()
              .color(Constants.COLORS.BORDER)
              .margin({ left: 56 })
          }
        })
      }
      .backgroundColor(Constants.COLORS.WHITE)
      .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
      .margin({ left: 16, right: 16 })
    }
  }

  /**
   * 语言选择对话框
   */
  @Builder
  private LanguageDialog() {
    Column() {
      Text('选择语言')
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
        .fontWeight(FontWeight.Bold)
        .margin({ bottom: 16 })

      ForEach(this.languageOptions, (option: string, index: number) => {
        Row() {
          Text(option)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .layoutWeight(1)

          if (option === this.language) {
            Image($r('app.media.ic_check'))
              .width(20)
              .height(20)
          }
        }
        .width('100%')
        .height(48)
        .padding({ left: '16vp', right: '16vp' })
        .onClick(() => {
          this.language = option;
          this.saveSetting('language', option);
          this.showLanguageDialog = false;
        })

        if (index < this.languageOptions.length - 1) {
          Divider().color(Constants.COLORS.BORDER)
        }
      })

      Row({ space: 16 }) {
        Button('取消')
          .layoutWeight(1)
          .backgroundColor(Constants.COLORS.BACKGROUND_LIGHT)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .onClick(() => this.showLanguageDialog = false)

        Button('确定')
          .layoutWeight(1)
          .backgroundColor(Constants.COLORS.PRIMARY)
          .fontColor(Constants.COLORS.WHITE)
          .onClick(() => this.showLanguageDialog = false)
      }
      .width('100%')
      .margin({ top: 16 })
    }
    .padding('24vp')
    .backgroundColor(Constants.COLORS.WHITE)
    .borderRadius(Constants.BORDER_RADIUS.LARGE)
  }

  /**
   * 清除缓存确认对话框
   */
  @Builder
  private ClearCacheDialog() {
    Column() {
      Text('清除缓存')
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
        .fontWeight(FontWeight.Bold)
        .margin({ bottom: 16 })

      Text('确定要清除所有缓存数据吗？此操作不可撤销。')
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
        .fontColor(Constants.COLORS.TEXT_SECONDARY)
        .textAlign(TextAlign.Center)
        .margin({ bottom: 24 })

      Row({ space: 16 }) {
        Button('取消')
          .layoutWeight(1)
          .backgroundColor(Constants.COLORS.BACKGROUND_LIGHT)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .onClick(() => this.showClearCacheDialog = false)

        Button('确定')
          .layoutWeight(1)
          .backgroundColor(Constants.COLORS.ERROR)
          .fontColor(Constants.COLORS.WHITE)
          .onClick(() => {
            this.showClearCacheDialog = false;
            this.clearCache();
          })
      }
      .width('100%')
    }
    .padding('24vp')
    .backgroundColor(Constants.COLORS.WHITE)
    .borderRadius(Constants.BORDER_RADIUS.LARGE)
  }

  build() {
    Stack() {
      Column() {
        // 顶部导航
        Row() {
          Image($r('app.media.ic_back'))
            .width(24)
            .height(24)
            .onClick(() => this.getUIContext().getRouter().back())

          Text('设置')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
            .fontWeight(FontWeight.Bold)
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .layoutWeight(1)
            .textAlign(TextAlign.Center)

          // 占位，保持标题居中
          Column()
            .width(24)
            .height(24)
        }
        .width('100%')
        .height(56)
        .padding({ left: '16vp', right: '16vp' })
        .backgroundColor(Constants.COLORS.WHITE)

        Scroll() {
          Column() {
            // 下载设置
            this.SettingGroup('下载设置', [
              {
                icon: $r('app.media.ic_download'),
                title: '自动更新应用',
                subtitle: '在WiFi环境下自动更新已安装的应用',
                type: 'switch',
                switchValue: this.autoUpdate,
                onSwitchChange: (value: boolean) => {
                  this.autoUpdate = value;
                  this.saveSetting('auto_update', value);
                }
              },
              {
                icon: $r('app.media.ic_wifi'),
                title: '仅WiFi下载',
                subtitle: '仅在WiFi网络下下载应用和更新',
                type: 'switch',
                switchValue: this.wifiOnlyDownload,
                onSwitchChange: (value: boolean) => {
                  this.wifiOnlyDownload = value;
                  this.saveSetting('wifi_only_download', value);
                }
              },
              {
                icon: $r('app.media.ic_folder'),
                title: '下载路径',
                subtitle: '设置应用下载保存位置',
                value: this.downloadPath,
                type: 'navigation',
                onClick: () => {
                  this.getUIContext().getPromptAction().showToast({ message: '下载路径设置功能开发中' });
                }
              }
            ])

            // 通知设置
            this.SettingGroup('通知设置', [
              {
                icon: $r('app.media.ic_notification'),
                title: '通知设置',
                subtitle: '管理所有通知偏好设置',
                type: 'navigation',
                onClick: () => {
                  this.getUIContext().getRouter().pushUrl({ url: 'pages/NotificationSettingsPage' });
                }
              },
              {
                icon: $r('app.media.ic_notification'),
                title: '推送通知',
                subtitle: '接收应用推荐和活动通知',
                type: 'switch',
                switchValue: this.pushNotifications,
                onSwitchChange: (value: boolean) => {
                  this.pushNotifications = value;
                  this.saveSetting('push_notifications', value);
                }
              },
              {
                icon: $r('app.media.ic_download'),
                title: '下载通知',
                subtitle: '显示应用下载进度通知',
                type: 'switch',
                switchValue: this.downloadNotifications,
                onSwitchChange: (value: boolean) => {
                  this.downloadNotifications = value;
                  this.saveSetting('download_notifications', value);
                }
              },
              {
                icon: $r('app.media.ic_update'),
                title: '更新通知',
                subtitle: '应用有新版本时通知',
                type: 'switch',
                switchValue: this.updateNotifications,
                onSwitchChange: (value: boolean) => {
                  this.updateNotifications = value;
                  this.saveSetting('update_notifications', value);
                }
              }
            ])

            // 界面设置
            this.SettingGroup('界面设置', [
              {
                icon: $r('app.media.ic_dark_mode'),
                title: '跟随系统深色模式',
                subtitle: '自动跟随系统的深色模式设置',
                type: 'switch',
                switchValue: this.darkModeFollowSystem,
                onSwitchChange: (value: boolean) => {
                  this.darkModeFollowSystem = value;
                  this.saveSetting(Constants.STORAGE_KEYS.DARK_MODE_FOLLOW_SYSTEM, value);
                  this.applyColorMode();
                }
              },
              {
                icon: $r('app.media.ic_dark_mode'),
                title: '手动深色模式',
                subtitle: this.darkModeFollowSystem ? '当前跟随系统设置' : '手动控制深色模式开关',
                type: 'switch',
                switchValue: this.darkModeFollowSystem ? false : this.darkModeManual,
                onSwitchChange: (value: boolean) => {
                  if (!this.darkModeFollowSystem) {
                    this.darkModeManual = value;
                    this.saveSetting(Constants.STORAGE_KEYS.DARK_MODE_MANUAL, value);
                    this.applyColorMode();
                  }
                }
              },
              {
                icon: $r('app.media.ic_info'),
                title: '深色模式测试',
                subtitle: '测试深色模式是否正常工作',
                type: 'navigation',
                onClick: () => {
                  this.getUIContext().getRouter().pushUrl({ url: 'pages/DarkModeTestPage' });
                }
              },
              {
                icon: $r('app.media.ic_language'),
                title: '语言',
                subtitle: '选择应用显示语言',
                value: this.language,
                type: 'selection',
                onClick: () => this.showLanguageDialog = true
              }
            ])

            // 存储设置
            this.SettingGroup('存储设置', [
              {
                icon: $r('app.media.ic_storage'),
                title: '缓存大小',
                subtitle: '应用缓存占用的存储空间',
                value: this.cacheSize,
                type: 'navigation',
                onClick: () => this.showClearCacheDialog = true
              }
            ])

            // 关于
            this.SettingGroup('关于', [
              {
                icon: $r('app.media.ic_update'),
                title: '检查更新',
                subtitle: '检查应用是否有新版本',
                type: 'navigation',
                onClick: () => this.checkUpdate()
              },
              {
                icon: $r('app.media.ic_info'),
                title: '版本信息',
                subtitle: '当前应用版本号',
                value: this.appVersion,
                type: 'info'
              },
              {
                icon: $r('app.media.ic_feedback'),
                title: '意见反馈',
                subtitle: '提交问题和建议',
                type: 'navigation',
                onClick: () => {
                  this.getUIContext().getRouter().pushUrl({ url: 'pages/FeedbackPage' });
                }
              },
              {
                icon: $r('app.media.ic_about'),
                title: '关于我们',
                subtitle: '了解更多应用信息',
                type: 'navigation',
                onClick: () => {
                  this.getUIContext().getRouter().pushUrl({ url: 'pages/AboutPage' });
                }
              }
            ])

            // 底部间距
            Column()
              .height(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE))
          }
        }
        .layoutWeight(1)
        .scrollable(ScrollDirection.Vertical)
        .scrollBar(BarState.Auto)
      }
      .width('100%')
      .height('100%')
      .backgroundColor(Constants.COLORS.BACKGROUND)

      // 对话框
      if (this.showLanguageDialog) {
        Column() {
          this.LanguageDialog()
        }
        .width('100%')
        .height('100%')
        .backgroundColor($r('app.color.overlay_dark'))
        .justifyContent(FlexAlign.Center)
        .padding('24vp')
        .onClick(() => this.showLanguageDialog = false)
      }

      if (this.showClearCacheDialog) {
        Column() {
          this.ClearCacheDialog()
        }
        .width('100%')
        .height('100%')
        .backgroundColor('rgba(0,0,0,0.5)')
        .justifyContent(FlexAlign.Center)
        .padding('24vp')
        .onClick(() => this.showClearCacheDialog = false)
      }
    }
  }
}