/**
 * 精选集数据模型
 * 直接匹配后端API返回的数据结构
 */

// 导入分页模型
import { PaginationModel } from './App';

/**
 * 精选集模型（直接匹配后端API文档）
 */
export interface FeaturedCollectionModel {
  id: number;
  name: string;
  description: string;
  cover_image?: string;
  app_count: number;
  display_order: number;
  status: string;
  created_at: string;
  updated_at: string;
}

/**
 * 精选集中的应用模型（直接匹配后端API文档）
 */
export interface FeaturedCollectionAppModel {
  id: number;
  name: string;
  package_name: string;
  description: string;
  short_description: string;
  icon: string;
  category_id: number;
  category_name: string;
  developer_id: number;
  developer_name: string;
  version: string;
  size: number;
  download_count: number;
  rating: number;
  review_count: number;
  tags: string[];
  status: string;
  published_at: string;
  added_at: string;
  display_order: number;
}

/**
 * 精选集列表数据模型（直接匹配后端API文档）
 */
export interface FeaturedCollectionListData {
  list: FeaturedCollectionModel[];  // 后端使用 list
  pagination: PaginationModel;
}

/**
 * 精选集列表响应模型（直接匹配后端API文档）
 */
export interface FeaturedCollectionListResponse {
  code: number;
  message: string;
  data: FeaturedCollectionListData;
}

/**
 * 精选集详情响应模型（直接匹配后端API文档）
 */
export interface FeaturedCollectionDetailResponse {
  code: number;
  message: string;
  data: FeaturedCollectionModel;
}

/**
 * 精选集应用列表数据模型（直接匹配后端API文档）
 */
export interface FeaturedCollectionAppsData {
  list: FeaturedCollectionAppModel[];  // 后端使用 list
  pagination: PaginationModel;
}

/**
 * 精选集应用列表响应模型（直接匹配后端API文档）
 */
export interface FeaturedCollectionAppsResponse {
  code: number;
  message: string;
  data: FeaturedCollectionAppsData;
}

