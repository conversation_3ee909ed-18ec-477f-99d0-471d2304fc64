import {
  PageContainer,
  ProForm,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
  StepsForm,
} from '@ant-design/pro-components';
import type { FormInstance } from 'antd';
import { Alert, Button, Card, Descriptions, Divider, Result, Statistic, Steps, Input } from 'antd';
import React, { useRef, useState } from 'react';
import type { StepDataType } from './data.d';
import useStyles from './style.style';

const StepDescriptions: React.FC<{
  stepData: StepDataType;
  bordered?: boolean;
}> = ({ stepData, bordered }) => {
  const { payAccount, receiverAccount, receiverName, amount, receiverMode } = stepData;
  return (
    <Descriptions column={1} bordered={bordered}>
      <Descriptions.Item label="付款账户"> {payAccount}</Descriptions.Item>
      <Descriptions.Item label="收款账户"> {receiverAccount}</Descriptions.Item>
      <Descriptions.Item label="收款人姓名"> {receiverName}</Descriptions.Item>
      <Descriptions.Item label="转账金额">
        <span style={{ fontWeight: 'bold' }}>{amount}</span>
        <span> 元</span>
      </Descriptions.Item>
      <Descriptions.Item label="转账方式">
        {receiverMode === 'alipay' ? '银行转账' :
          receiverMode === 'bank' ? '银行转账' :
            '其他方式'}
      </Descriptions.Item>
    </Descriptions>
  );
};

const StepResult: React.FC<{
  onFinish: () => Promise<void>;
  children?: React.ReactNode;
}> = (props) => {
  const { styles } = useStyles();
  return (
    <Result
      status="success"
      title="操作成功"
      subTitle="预计两小时内到账"
      extra={
        <>
          <Button type="primary" onClick={props.onFinish}>
            再转一笔
          </Button>
          <Button>查看账单</Button>
        </>
      }
      className={styles.result}
    >
      {props.children}
    </Result>
  );
};

const StepForm: React.FC = () => {
  const { styles } = useStyles();
  const [currentStep, setCurrentStep] = useState(0);
  const [stepData, setStepData] = useState<StepDataType>({
    payAccount: '',
    receiverAccount: '',
    receiverName: '',
    amount: '',
    receiverMode: 'bank',
  });
  const [form] = Form.useForm();

  const handleStep1Submit = (values: any) => {
    setStepData({ ...stepData, ...values });
    setCurrentStep(1);
  };

  const handleStep2Submit = (values: any) => {
    message.success('转账成功');
    setCurrentStep(2);
  };

  const handlePrev = () => {
    setCurrentStep(0);
  };

  const handleReset = () => {
    setCurrentStep(0);
    setStepData({
      payAccount: '',
      receiverAccount: '',
      receiverName: '',
      amount: '',
      receiverMode: 'bank',
    });
    form.resetFields();
  };

  const renderContent = () => {
    if (currentStep === 0) {
      return (
        <Form
          form={form}
          layout="horizontal"
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 14 }}
          onFinish={handleStep1Submit}
          initialValues={{
            payAccount: stepData.payAccount,
            receiverAccount: stepData.receiverAccount,
            receiverName: stepData.receiverName,
            amount: stepData.amount,
            receiverMode: stepData.receiverMode || 'bank',
          }}
        >
          <Form.Item
            label="付款账户"
            name="payAccount"
            rules={[{ required: true, message: '请输入付款账户' }]}
          >
            <Input placeholder="请输入付款账户" />
          </Form.Item>
          
          <Form.Item
            label="收款账户"
            name="receiverAccount"
            rules={[{ required: true, message: '请输入收款账户' }]}
          >
            <Input placeholder="请输入收款账户" />
          </Form.Item>
          
          <Form.Item
            label="收款人姓名"
            name="receiverName"
            rules={[{ required: true, message: '请输入收款人姓名' }]}
          >
            <Input placeholder="请输入收款人姓名" />
          </Form.Item>
          
          <Form.Item
            label="转账金额"
            name="amount"
            rules={[
              { required: true, message: '请输入转账金额' },
              { pattern: /^(\d+)((?:\.\d+)?)$/, message: '请输入合法金额数字' },
            ]}
          >
            <Input prefix="￥" placeholder="请输入金额" />
          </Form.Item>
          
          <Form.Item
            label="转账方式"
            name="receiverMode"
            rules={[{ required: true, message: '请选择转账方式' }]}
          >
            <Select>
              <Select.Option value="bank">银行转账</Select.Option>
              <Select.Option value="transfer">转账</Select.Option>
            </Select>
          </Form.Item>
          
          <Form.Item wrapperCol={{ offset: 4 }}>
            <Button type="primary" htmlType="submit">
              下一步
            </Button>
          </Form.Item>
        </Form>
      );
    }
    
    if (currentStep === 1) {
      return (
        <>
          <Alert
            closable
            showIcon
            message="确认转账后，资金将直接打入对方账户，无法退回。"
            style={{ marginBottom: 24 }}
          />
          
          <StepDescriptions stepData={stepData} bordered />
          
          <Divider />
          
          <Form
            layout="horizontal"
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 14 }}
            onFinish={handleStep2Submit}
          >
            <Form.Item
              label="验证码"
              name="verificationCode"
              rules={[
                { required: true, message: '需要验证码才能进行转账' },
                { pattern: /^\d{6}$/, message: '验证码格式不正确' },
              ]}
            >
              <Input placeholder="请输入验证码" />
            </Form.Item>
            
            <Form.Item wrapperCol={{ offset: 4 }}>
              <Button type="primary" htmlType="submit" style={{ marginRight: 8 }}>
                确认转账
              </Button>
              <Button onClick={handlePrev}>上一步</Button>
            </Form.Item>
          </Form>
        </>
      );
    }
    
    if (currentStep === 2) {
      return (
        <Result
          status="success"
          title="转账成功"
          subTitle={`预计两小时内到账，订单号：${Date.now()}`}
          extra={
            <Button type="primary" onClick={handleReset}>
              再转一笔
            </Button>
          }
        >
          <StepDescriptions stepData={stepData} />
        </Result>
      );
    }
    
    return null;
  };

  const renderFooter = () => {
    if (currentStep === 2) {
      return null;
    }
    
    const stepsItems = [
      { title: '填写转账信息' },
      { title: '确认转账信息' },
      { title: '完成' },
    ];
    
    return (
      <Steps size="small" current={currentStep} items={stepsItems} />
    );
  };

  return (
    <PageContainer content="将一个冗长或复杂的表单任务分成多个步骤，指导用户完成。">
      <Card bordered={false}>
        {renderFooter()}
        <Divider style={{ margin: '24px 0' }} />
        {renderContent()}
      </Card>
    </PageContainer>
  );
};

export default StepForm;
