// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 获取应用评论 获取应用的评论和评分 GET /apps/${param0}/reviews */
export async function getAppsIdReviews(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAppsIdReviewsParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.PageResponse & { data?: API.ReviewResponse[] }>(`/apps/${param0}/reviews`, {
    method: 'GET',
    params: {
      // page has a default value: 1
      page: '1',
      // page_size has a default value: 20
      page_size: '20',
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 发表应用评论 用户为应用发表评价和评分 POST /apps/${param0}/reviews */
export async function postAppsIdReviews(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postAppsIdReviewsParams,
  body: API.CreateReviewRequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Response & { data?: API.ReviewResponse }>(`/apps/${param0}/reviews`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 点赞评论 给应用评论点赞 POST /apps/${param0}/reviews/${param1}/like */
export async function postAppsIdReviewsReviewIdLike(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postAppsIdReviewsReviewIdLikeParams,
  options?: { [key: string]: any },
) {
  const { id: param0, review_id: param1, ...queryParams } = params;
  return request<API.Response>(`/apps/${param0}/reviews/${param1}/like`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 开发者回复评论 应用开发者回复用户评论 POST /apps/${param0}/reviews/${param1}/respond */
export async function postAppsIdReviewsReviewIdRespond(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postAppsIdReviewsReviewIdRespondParams,
  body: API.DevResponseRequest,
  options?: { [key: string]: any },
) {
  const { id: param0, review_id: param1, ...queryParams } = params;
  return request<API.Response & { data?: API.ReviewResponse }>(
    `/apps/${param0}/reviews/${param1}/respond`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      params: { ...queryParams },
      data: body,
      ...(options || {}),
    },
  );
}

/** 取消点赞评论 取消对应用评论的点赞 POST /apps/${param0}/reviews/${param1}/unlike */
export async function postAppsIdReviewsReviewIdUnlike(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postAppsIdReviewsReviewIdUnlikeParams,
  options?: { [key: string]: any },
) {
  const { id: param0, review_id: param1, ...queryParams } = params;
  return request<API.Response>(`/apps/${param0}/reviews/${param1}/unlike`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}
