// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 用户登录 登录获取认证令牌 POST /users/login */
export async function postUsersLogin(body: API.LoginRequest, options?: { [key: string]: any }) {
  return request<API.Response & { data?: Record }>('/users/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 用户退出登录 使当前用户的JWT令牌失效 POST /users/logout */
export async function postUsersLogout(options?: { [key: string]: any }) {
  return request<API.Response>('/users/logout', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 获取用户资料 获取当前登录用户的详细资料 GET /users/profile */
export async function getUsersProfile(options?: { [key: string]: any }) {
  return request<API.Response & { data?: API.UserResponse }>('/users/profile', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 更新用户资料 更新当前登录用户的资料信息 PUT /users/profile */
export async function putUsersProfile(
  body: API.UpdateProfileRequest,
  options?: { [key: string]: any },
) {
  return request<API.Response & { data?: API.UserResponse }>('/users/profile', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 用户注册 创建新用户账号 POST /users/register */
export async function postUsersRegister(
  body: API.RegisterRequest,
  options?: { [key: string]: any },
) {
  return request<API.Response>('/users/register', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
