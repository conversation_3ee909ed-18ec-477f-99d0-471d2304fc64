{"version": "2.0", "ppid": 32784, "events": [{"head": {"id": "384b466d-b733-4622-a15c-4b0c3ca9a965", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914528959700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "caee9e76-4dfc-4656-b8f7-ecdb637e3646", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914529138600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9fee5de-8c91-4abe-a198-7420f5778f7e", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914530739400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f478b74c-e4f4-43c8-a844-ea3b1cb3dc88", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914530997100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6684d269-0f8a-4346-bf44-d794d041f384", "name": "worker[18] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914535429500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "539fe81d-3896-4187-9cf3-585a8466656b", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914537838000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e849d89-4a87-4a02-ad8f-d25bd844ba1f", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914538464800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8ded06b-81f3-4326-8772-b6db2175f0f9", "name": "worker[4] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222914579325100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "559fb8bd-2bf7-418d-aab7-49c0ee91e260", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043260707300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "effbf164-437b-4845-9ee5-ff9f79a10a5d", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043273155100, "endTime": 223043515877800}, "additional": {"children": ["5834a625-182f-429e-a88e-d9d380d7e509", "6157ae88-704f-4e4f-a74b-b1ab35e95c2b", "a4f12c11-b6d0-486c-99da-09bb3a1a99f3", "a7d3601f-7e0f-47f7-ae46-cfdcd4053d53", "f68007a5-5802-48ff-bc97-4c8b5c0172cf", "e58ad9e2-9b19-4597-9d81-63b006fb1747", "11906f7f-4725-43a9-ada2-30e8d04dbb5a"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "037dfc86-12e1-4b67-9611-b2948df0268d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5834a625-182f-429e-a88e-d9d380d7e509", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043273157400, "endTime": 223043291779900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "effbf164-437b-4845-9ee5-ff9f79a10a5d", "logId": "e86f13d6-89cd-4858-b7fe-558c85405b3a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6157ae88-704f-4e4f-a74b-b1ab35e95c2b", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043291806900, "endTime": 223043514343400}, "additional": {"children": ["173707c2-0bb4-491f-aa26-91885240caca", "5d274064-6513-410a-9db1-c81fd429bd21", "339af672-af53-40ec-b237-f3823f8eb37d", "ca9fac1e-0ac7-4c6c-8fbc-c1d82fe1259f", "1eb1fe9a-74b8-4594-93b9-2ca1e422ba97", "93562fb3-51cd-4b38-9996-88002cbff98e", "3787ecf8-3b9d-4dc1-87dd-3133cfe4d6e1", "0aaca5bd-df2d-4dce-b267-2c806e6ac29d", "ed86fd21-e2c8-4789-a97c-96be3ad7cf47"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "effbf164-437b-4845-9ee5-ff9f79a10a5d", "logId": "a2012df9-8e42-4ea0-8dbd-27a4d6444fc6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4f12c11-b6d0-486c-99da-09bb3a1a99f3", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043514375200, "endTime": 223043515857700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "effbf164-437b-4845-9ee5-ff9f79a10a5d", "logId": "b4daf4e6-6f0f-41e9-a7fb-26a40c44b00e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7d3601f-7e0f-47f7-ae46-cfdcd4053d53", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043515864300, "endTime": 223043515872700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "effbf164-437b-4845-9ee5-ff9f79a10a5d", "logId": "25cb6118-e7bb-4419-bc83-4f95fc998040"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f68007a5-5802-48ff-bc97-4c8b5c0172cf", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043278631600, "endTime": 223043278699100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "effbf164-437b-4845-9ee5-ff9f79a10a5d", "logId": "df20a2f7-59d2-48f9-963e-776bd4c481c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df20a2f7-59d2-48f9-963e-776bd4c481c2", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043278631600, "endTime": 223043278699100}, "additional": {"logType": "info", "children": [], "durationId": "f68007a5-5802-48ff-bc97-4c8b5c0172cf", "parent": "037dfc86-12e1-4b67-9611-b2948df0268d"}}, {"head": {"id": "e58ad9e2-9b19-4597-9d81-63b006fb1747", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043284250800, "endTime": 223043284382300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "effbf164-437b-4845-9ee5-ff9f79a10a5d", "logId": "16d76d4b-6521-4950-bc75-23df3c00556d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16d76d4b-6521-4950-bc75-23df3c00556d", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043284250800, "endTime": 223043284382300}, "additional": {"logType": "info", "children": [], "durationId": "e58ad9e2-9b19-4597-9d81-63b006fb1747", "parent": "037dfc86-12e1-4b67-9611-b2948df0268d"}}, {"head": {"id": "f92cbef9-aa02-4494-bd3e-718f75c423b7", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043284495000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc055ab6-963a-4bd2-abba-461953d4e74b", "name": "Cache service initialization finished in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043291616800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e86f13d6-89cd-4858-b7fe-558c85405b3a", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043273157400, "endTime": 223043291779900}, "additional": {"logType": "info", "children": [], "durationId": "5834a625-182f-429e-a88e-d9d380d7e509", "parent": "037dfc86-12e1-4b67-9611-b2948df0268d"}}, {"head": {"id": "173707c2-0bb4-491f-aa26-91885240caca", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043299736600, "endTime": 223043299749900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6157ae88-704f-4e4f-a74b-b1ab35e95c2b", "logId": "9f8941bb-59dc-4588-a37f-72e2cd17816c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5d274064-6513-410a-9db1-c81fd429bd21", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043299770700, "endTime": 223043305173900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6157ae88-704f-4e4f-a74b-b1ab35e95c2b", "logId": "ab07d868-a7c8-4cda-a618-d47a039b038c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "339af672-af53-40ec-b237-f3823f8eb37d", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043305190600, "endTime": 223043397572700}, "additional": {"children": ["319e32e5-8ab7-45ae-8c3d-2594d0664987", "cb39ea63-4167-4208-80b8-549292ec1569", "7e1c1582-dd31-41f0-ac79-6837b25be8b4"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6157ae88-704f-4e4f-a74b-b1ab35e95c2b", "logId": "5b4190fa-e4af-4b4c-af02-91b586b7a3e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca9fac1e-0ac7-4c6c-8fbc-c1d82fe1259f", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043397589700, "endTime": 223043422314300}, "additional": {"children": ["c0f950ba-c26b-45a0-afe7-e00965338f1b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6157ae88-704f-4e4f-a74b-b1ab35e95c2b", "logId": "0c20dd5c-af7a-42af-a71c-313d766cc739"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1eb1fe9a-74b8-4594-93b9-2ca1e422ba97", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043422323300, "endTime": 223043480097200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6157ae88-704f-4e4f-a74b-b1ab35e95c2b", "logId": "efb7b832-9c62-4156-93da-92a55d25c333"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "93562fb3-51cd-4b38-9996-88002cbff98e", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043481474700, "endTime": 223043494194800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6157ae88-704f-4e4f-a74b-b1ab35e95c2b", "logId": "8484bf4e-8b47-4e7a-90db-d49402e6ce57"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3787ecf8-3b9d-4dc1-87dd-3133cfe4d6e1", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043494230800, "endTime": 223043514041600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6157ae88-704f-4e4f-a74b-b1ab35e95c2b", "logId": "885c6b2c-d546-4adc-bd33-0bd905330e90"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0aaca5bd-df2d-4dce-b267-2c806e6ac29d", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043514103400, "endTime": 223043514323600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6157ae88-704f-4e4f-a74b-b1ab35e95c2b", "logId": "165cd9cf-75d3-4664-a797-2507277320a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f8941bb-59dc-4588-a37f-72e2cd17816c", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043299736600, "endTime": 223043299749900}, "additional": {"logType": "info", "children": [], "durationId": "173707c2-0bb4-491f-aa26-91885240caca", "parent": "a2012df9-8e42-4ea0-8dbd-27a4d6444fc6"}}, {"head": {"id": "ab07d868-a7c8-4cda-a618-d47a039b038c", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043299770700, "endTime": 223043305173900}, "additional": {"logType": "info", "children": [], "durationId": "5d274064-6513-410a-9db1-c81fd429bd21", "parent": "a2012df9-8e42-4ea0-8dbd-27a4d6444fc6"}}, {"head": {"id": "319e32e5-8ab7-45ae-8c3d-2594d0664987", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043305817700, "endTime": 223043305839400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "339af672-af53-40ec-b237-f3823f8eb37d", "logId": "fda2ffbb-d243-4270-bdd0-0cf93fb1f997"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fda2ffbb-d243-4270-bdd0-0cf93fb1f997", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043305817700, "endTime": 223043305839400}, "additional": {"logType": "info", "children": [], "durationId": "319e32e5-8ab7-45ae-8c3d-2594d0664987", "parent": "5b4190fa-e4af-4b4c-af02-91b586b7a3e4"}}, {"head": {"id": "cb39ea63-4167-4208-80b8-549292ec1569", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043308014500, "endTime": 223043396848600}, "additional": {"children": ["ac49ef80-ed6a-4397-8eec-5320cd4c1f5e", "0cf5019e-0cad-4f49-9966-4bdae61d2138"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "339af672-af53-40ec-b237-f3823f8eb37d", "logId": "c1e3407b-9d3f-48a5-b5cb-2c504ff933f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac49ef80-ed6a-4397-8eec-5320cd4c1f5e", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043308016200, "endTime": 223043314722700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cb39ea63-4167-4208-80b8-549292ec1569", "logId": "2faf3636-432d-4122-85b4-38b2377fa9f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0cf5019e-0cad-4f49-9966-4bdae61d2138", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043314742000, "endTime": 223043396833100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cb39ea63-4167-4208-80b8-549292ec1569", "logId": "35015c5a-2dc3-4be9-9bba-2148c7fd7f2a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7b72b4a-308d-4a7c-aee3-bae63723346e", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043308024400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "277aaef6-520a-40ed-8a69-871ebfb4ceb4", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043314571200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2faf3636-432d-4122-85b4-38b2377fa9f8", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043308016200, "endTime": 223043314722700}, "additional": {"logType": "info", "children": [], "durationId": "ac49ef80-ed6a-4397-8eec-5320cd4c1f5e", "parent": "c1e3407b-9d3f-48a5-b5cb-2c504ff933f8"}}, {"head": {"id": "82bbf451-8cf6-4a1f-9b56-fc6d11a24a56", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043314761800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e4f1157-3c5c-4b86-b982-0ceba38b6129", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043322973500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5157027a-f143-4712-ab3c-574c6caeb3d7", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043324041300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3769ef2-8ea4-4139-8fd4-db1e23ec384d", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043324269600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcbf602b-6e49-42c4-8d3d-7effa0d10c9d", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043324399200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2fb524c-ac55-4571-83eb-e56c821d9846", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043327086000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a69aade0-d2dd-458a-840f-8f73f4ef89ee", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043345657100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "990788ee-d6e5-47fe-bdb4-2a50de39407d", "name": "Sdk init in 38 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043371476000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86bbd2e6-eca9-4caf-bf76-71cea0ccf386", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043371686400}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 19, "minute": 54, "second": 33}, "markType": "other"}}, {"head": {"id": "4829e293-f21e-4314-bcd4-85a7ceb031ca", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043371702900}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 19, "minute": 54, "second": 33}, "markType": "other"}}, {"head": {"id": "6bd398c4-5a71-4ec6-b4fd-41a9be97c3f3", "name": "Project task initialization takes 24 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043396528600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0eccdd6-5709-4e5d-8a72-ad854f561509", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043396676600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bcbb325-3fef-4f57-9959-b4ffc643be36", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043396747800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ffa99c2-08e9-43a4-81bb-98d6e73c0532", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043396792400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35015c5a-2dc3-4be9-9bba-2148c7fd7f2a", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043314742000, "endTime": 223043396833100}, "additional": {"logType": "info", "children": [], "durationId": "0cf5019e-0cad-4f49-9966-4bdae61d2138", "parent": "c1e3407b-9d3f-48a5-b5cb-2c504ff933f8"}}, {"head": {"id": "c1e3407b-9d3f-48a5-b5cb-2c504ff933f8", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043308014500, "endTime": 223043396848600}, "additional": {"logType": "info", "children": ["2faf3636-432d-4122-85b4-38b2377fa9f8", "35015c5a-2dc3-4be9-9bba-2148c7fd7f2a"], "durationId": "cb39ea63-4167-4208-80b8-549292ec1569", "parent": "5b4190fa-e4af-4b4c-af02-91b586b7a3e4"}}, {"head": {"id": "7e1c1582-dd31-41f0-ac79-6837b25be8b4", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043397535800, "endTime": 223043397554600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "339af672-af53-40ec-b237-f3823f8eb37d", "logId": "d744e612-d478-45cb-b7d2-353f9d4b3c47"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d744e612-d478-45cb-b7d2-353f9d4b3c47", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043397535800, "endTime": 223043397554600}, "additional": {"logType": "info", "children": [], "durationId": "7e1c1582-dd31-41f0-ac79-6837b25be8b4", "parent": "5b4190fa-e4af-4b4c-af02-91b586b7a3e4"}}, {"head": {"id": "5b4190fa-e4af-4b4c-af02-91b586b7a3e4", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043305190600, "endTime": 223043397572700}, "additional": {"logType": "info", "children": ["fda2ffbb-d243-4270-bdd0-0cf93fb1f997", "c1e3407b-9d3f-48a5-b5cb-2c504ff933f8", "d744e612-d478-45cb-b7d2-353f9d4b3c47"], "durationId": "339af672-af53-40ec-b237-f3823f8eb37d", "parent": "a2012df9-8e42-4ea0-8dbd-27a4d6444fc6"}}, {"head": {"id": "c0f950ba-c26b-45a0-afe7-e00965338f1b", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043398170800, "endTime": 223043422299500}, "additional": {"children": ["4f49311b-04dc-4382-8082-6d14d0818a26", "c4c0c6e1-6048-459e-93e6-4daace038e2c", "b0a40b51-ac2e-40ed-bea8-e1a50ea89f5d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca9fac1e-0ac7-4c6c-8fbc-c1d82fe1259f", "logId": "9fe2134d-f2fc-4ed6-96cd-755ae9d9b8e6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f49311b-04dc-4382-8082-6d14d0818a26", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043402408000, "endTime": 223043402436000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c0f950ba-c26b-45a0-afe7-e00965338f1b", "logId": "29d85ad0-b713-483e-98c8-9deb9cea94a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29d85ad0-b713-483e-98c8-9deb9cea94a9", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043402408000, "endTime": 223043402436000}, "additional": {"logType": "info", "children": [], "durationId": "4f49311b-04dc-4382-8082-6d14d0818a26", "parent": "9fe2134d-f2fc-4ed6-96cd-755ae9d9b8e6"}}, {"head": {"id": "c4c0c6e1-6048-459e-93e6-4daace038e2c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043404466600, "endTime": 223043420593400}, "additional": {"children": ["08123ce9-524e-4648-873f-4ba0983d9821", "28098c21-1c3d-4d76-b40f-2b9bb7225c72"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c0f950ba-c26b-45a0-afe7-e00965338f1b", "logId": "bb4ce3ce-3348-48c7-951e-f2dcff81c568"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "08123ce9-524e-4648-873f-4ba0983d9821", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043404468400, "endTime": 223043407438000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c4c0c6e1-6048-459e-93e6-4daace038e2c", "logId": "4a7ab253-35b5-4ef3-912e-2563c66476fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28098c21-1c3d-4d76-b40f-2b9bb7225c72", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043407456500, "endTime": 223043420580100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c4c0c6e1-6048-459e-93e6-4daace038e2c", "logId": "57de5de0-6de7-4402-b5ba-bb8afbda4f8a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bd5dd891-9879-4f5d-b58c-9da3e80dba40", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043404474000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc9f7e99-77f3-47a6-888a-fdbea052f0a4", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043407315900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a7ab253-35b5-4ef3-912e-2563c66476fb", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043404468400, "endTime": 223043407438000}, "additional": {"logType": "info", "children": [], "durationId": "08123ce9-524e-4648-873f-4ba0983d9821", "parent": "bb4ce3ce-3348-48c7-951e-f2dcff81c568"}}, {"head": {"id": "d81a24a6-a313-4f3a-b26c-f8dfeaea7425", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043407471200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f63c9c7e-f67f-488a-b2b2-20a34af9a710", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043414781900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d477fede-5965-41fd-90cf-e3849ee188fa", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043414919100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b713aff-51a9-44d0-94b9-a5e4bbfd117d", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043415095000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e78b388f-065f-4b80-893b-a901cfff259e", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043415193200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ed69b11-5acb-444d-a377-9608a2d73d86", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043415235600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3657ac14-bc57-423f-b2cd-a6536897b519", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043415282400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8f323ef-2203-490a-b48d-580c3765b7e4", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043415328300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cdce8f2-ae58-4d66-b5b5-ed6ab38796cf", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043415363700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aebc4d51-e3b4-41ab-b29a-0102885a0f71", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043415533300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3576791-057b-4794-baf2-7f3f1e658568", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043415615700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0fade3e-a4cf-4cff-aed5-7abcb0583131", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043415655400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bce1fac-77cd-43c5-a032-e9fd38cf001c", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043415685700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5223bb98-9469-4809-86ba-5900ba45a5ee", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043415727600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51514462-6aaa-4bcd-949a-2a46a8160154", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043415759900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaa71c42-deb5-4c57-9aaa-6f5f48d7e2b8", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043415854200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f975a63-a26f-428c-946e-cf25deb414d2", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043416674100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c53c372d-8ef3-44a5-9cc4-624dc9777473", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043416762900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8ce6e9d-67c3-4e10-9de2-a732cc5dfcb9", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043416804100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13c70a70-d534-4f2c-90bf-d08abe0848d4", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043416851100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03f2b1eb-a04c-4eba-bd11-4dfef6031377", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043420238800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad759bc7-32e9-4202-b1b5-ad85f70021ef", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043420448500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2421303-cf67-40d8-8cd4-109eeca2d86d", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043420509300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a111c755-2302-4852-b5d8-96c78f52645c", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043420545200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57de5de0-6de7-4402-b5ba-bb8afbda4f8a", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043407456500, "endTime": 223043420580100}, "additional": {"logType": "info", "children": [], "durationId": "28098c21-1c3d-4d76-b40f-2b9bb7225c72", "parent": "bb4ce3ce-3348-48c7-951e-f2dcff81c568"}}, {"head": {"id": "bb4ce3ce-3348-48c7-951e-f2dcff81c568", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043404466600, "endTime": 223043420593400}, "additional": {"logType": "info", "children": ["4a7ab253-35b5-4ef3-912e-2563c66476fb", "57de5de0-6de7-4402-b5ba-bb8afbda4f8a"], "durationId": "c4c0c6e1-6048-459e-93e6-4daace038e2c", "parent": "9fe2134d-f2fc-4ed6-96cd-755ae9d9b8e6"}}, {"head": {"id": "b0a40b51-ac2e-40ed-bea8-e1a50ea89f5d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043422243100, "endTime": 223043422261600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c0f950ba-c26b-45a0-afe7-e00965338f1b", "logId": "23a771cf-986c-453b-80cf-81c50e27c808"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "23a771cf-986c-453b-80cf-81c50e27c808", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043422243100, "endTime": 223043422261600}, "additional": {"logType": "info", "children": [], "durationId": "b0a40b51-ac2e-40ed-bea8-e1a50ea89f5d", "parent": "9fe2134d-f2fc-4ed6-96cd-755ae9d9b8e6"}}, {"head": {"id": "9fe2134d-f2fc-4ed6-96cd-755ae9d9b8e6", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043398170800, "endTime": 223043422299500}, "additional": {"logType": "info", "children": ["29d85ad0-b713-483e-98c8-9deb9cea94a9", "bb4ce3ce-3348-48c7-951e-f2dcff81c568", "23a771cf-986c-453b-80cf-81c50e27c808"], "durationId": "c0f950ba-c26b-45a0-afe7-e00965338f1b", "parent": "0c20dd5c-af7a-42af-a71c-313d766cc739"}}, {"head": {"id": "0c20dd5c-af7a-42af-a71c-313d766cc739", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043397589700, "endTime": 223043422314300}, "additional": {"logType": "info", "children": ["9fe2134d-f2fc-4ed6-96cd-755ae9d9b8e6"], "durationId": "ca9fac1e-0ac7-4c6c-8fbc-c1d82fe1259f", "parent": "a2012df9-8e42-4ea0-8dbd-27a4d6444fc6"}}, {"head": {"id": "4d2d4c94-d610-4ea4-9807-bf4fef112bad", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043437948300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b9cd653-6e00-452f-bb45-404b08a7d805", "name": "hvigorfile, resolve hvigorfile dependencies in 58 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043479919400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efb7b832-9c62-4156-93da-92a55d25c333", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043422323300, "endTime": 223043480097200}, "additional": {"logType": "info", "children": [], "durationId": "1eb1fe9a-74b8-4594-93b9-2ca1e422ba97", "parent": "a2012df9-8e42-4ea0-8dbd-27a4d6444fc6"}}, {"head": {"id": "ed86fd21-e2c8-4789-a97c-96be3ad7cf47", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043481219000, "endTime": 223043481456500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6157ae88-704f-4e4f-a74b-b1ab35e95c2b", "logId": "c7cb0b3c-2d29-4fed-abc0-eaba3c69693a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e5077cc-9bf7-4083-af72-36a7ceffa060", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043481258400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7cb0b3c-2d29-4fed-abc0-eaba3c69693a", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043481219000, "endTime": 223043481456500}, "additional": {"logType": "info", "children": [], "durationId": "ed86fd21-e2c8-4789-a97c-96be3ad7cf47", "parent": "a2012df9-8e42-4ea0-8dbd-27a4d6444fc6"}}, {"head": {"id": "cbaa0191-1be7-4f9d-8452-7a4a6e032c8e", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043483474900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8563c19c-52df-44aa-9306-885359d5960c", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043492408900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8484bf4e-8b47-4e7a-90db-d49402e6ce57", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043481474700, "endTime": 223043494194800}, "additional": {"logType": "info", "children": [], "durationId": "93562fb3-51cd-4b38-9996-88002cbff98e", "parent": "a2012df9-8e42-4ea0-8dbd-27a4d6444fc6"}}, {"head": {"id": "1a47f113-0359-42c4-ba5d-1afed9762557", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043494257300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18d7451e-a0a8-45cb-be31-008f86544956", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043505027000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2455a0bf-8d14-4aff-9937-5c8f2e161a90", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043505180600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e191840-7931-4a03-8e44-81871c707ee1", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043505429700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f39156a-5798-4b4d-bb87-de6a7d0b723a", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043509216300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15bb5618-0b82-4389-8a96-5ff380444a64", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043509349100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "885c6b2c-d546-4adc-bd33-0bd905330e90", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043494230800, "endTime": 223043514041600}, "additional": {"logType": "info", "children": [], "durationId": "3787ecf8-3b9d-4dc1-87dd-3133cfe4d6e1", "parent": "a2012df9-8e42-4ea0-8dbd-27a4d6444fc6"}}, {"head": {"id": "d39687d3-ef4e-4b41-ad52-c7b21136ab3e", "name": "Configuration phase cost:215 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043514194200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "165cd9cf-75d3-4664-a797-2507277320a9", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043514103400, "endTime": 223043514323600}, "additional": {"logType": "info", "children": [], "durationId": "0aaca5bd-df2d-4dce-b267-2c806e6ac29d", "parent": "a2012df9-8e42-4ea0-8dbd-27a4d6444fc6"}}, {"head": {"id": "a2012df9-8e42-4ea0-8dbd-27a4d6444fc6", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043291806900, "endTime": 223043514343400}, "additional": {"logType": "info", "children": ["9f8941bb-59dc-4588-a37f-72e2cd17816c", "ab07d868-a7c8-4cda-a618-d47a039b038c", "5b4190fa-e4af-4b4c-af02-91b586b7a3e4", "0c20dd5c-af7a-42af-a71c-313d766cc739", "efb7b832-9c62-4156-93da-92a55d25c333", "8484bf4e-8b47-4e7a-90db-d49402e6ce57", "885c6b2c-d546-4adc-bd33-0bd905330e90", "165cd9cf-75d3-4664-a797-2507277320a9", "c7cb0b3c-2d29-4fed-abc0-eaba3c69693a"], "durationId": "6157ae88-704f-4e4f-a74b-b1ab35e95c2b", "parent": "037dfc86-12e1-4b67-9611-b2948df0268d"}}, {"head": {"id": "11906f7f-4725-43a9-ada2-30e8d04dbb5a", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043515824800, "endTime": 223043515843700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "effbf164-437b-4845-9ee5-ff9f79a10a5d", "logId": "fe9ad7bb-3cec-488c-b996-4b9049a3628f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe9ad7bb-3cec-488c-b996-4b9049a3628f", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043515824800, "endTime": 223043515843700}, "additional": {"logType": "info", "children": [], "durationId": "11906f7f-4725-43a9-ada2-30e8d04dbb5a", "parent": "037dfc86-12e1-4b67-9611-b2948df0268d"}}, {"head": {"id": "b4daf4e6-6f0f-41e9-a7fb-26a40c44b00e", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043514375200, "endTime": 223043515857700}, "additional": {"logType": "info", "children": [], "durationId": "a4f12c11-b6d0-486c-99da-09bb3a1a99f3", "parent": "037dfc86-12e1-4b67-9611-b2948df0268d"}}, {"head": {"id": "25cb6118-e7bb-4419-bc83-4f95fc998040", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043515864300, "endTime": 223043515872700}, "additional": {"logType": "info", "children": [], "durationId": "a7d3601f-7e0f-47f7-ae46-cfdcd4053d53", "parent": "037dfc86-12e1-4b67-9611-b2948df0268d"}}, {"head": {"id": "037dfc86-12e1-4b67-9611-b2948df0268d", "name": "init", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043273155100, "endTime": 223043515877800}, "additional": {"logType": "info", "children": ["e86f13d6-89cd-4858-b7fe-558c85405b3a", "a2012df9-8e42-4ea0-8dbd-27a4d6444fc6", "b4daf4e6-6f0f-41e9-a7fb-26a40c44b00e", "25cb6118-e7bb-4419-bc83-4f95fc998040", "df20a2f7-59d2-48f9-963e-776bd4c481c2", "16d76d4b-6521-4950-bc75-23df3c00556d", "fe9ad7bb-3cec-488c-b996-4b9049a3628f"], "durationId": "effbf164-437b-4845-9ee5-ff9f79a10a5d"}}, {"head": {"id": "150bc421-3395-400e-99c9-910c76ddabe6", "name": "Configuration task cost before running: 250 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043516050100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3263f125-52d4-4f27-b0dd-3ba6981f346d", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043528503200, "endTime": 223043541746100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "3fe45cdb-5e51-4e3b-a891-3be17365a319", "logId": "5ea06919-87d0-4fe4-99d0-b0a19922a584"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3fe45cdb-5e51-4e3b-a891-3be17365a319", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043517987300}, "additional": {"logType": "detail", "children": [], "durationId": "3263f125-52d4-4f27-b0dd-3ba6981f346d"}}, {"head": {"id": "e205bc1a-6702-4db4-a82e-3e3f81f826f7", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043519370600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6588d92-e44b-4142-bdf6-72586956c272", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043519802200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "162a9b93-996d-4a84-b9fa-d23b2758082c", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043521022200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b1bfeef-0f16-4c6d-b916-a2020189e305", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043521862300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "361dc1db-b150-4bbb-893d-daa5128f660e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043523012600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97ca1946-67d9-4994-9b7c-d3883c747c6c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043523112600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7711ca2-11cc-4f3d-bd9b-9a03bc279df9", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043528527100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d89b7369-5441-4159-b458-feb9f5c76378", "name": "Incremental task entry:default@PreBuild pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043541500800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3aa09e8-b4a0-4041-bf18-7a8ce3230e7e", "name": "entry : default@PreBuild cost memory 0.31781768798828125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043541674200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ea06919-87d0-4fe4-99d0-b0a19922a584", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043528503200, "endTime": 223043541746100}, "additional": {"logType": "info", "children": [], "durationId": "3263f125-52d4-4f27-b0dd-3ba6981f346d"}}, {"head": {"id": "569c7029-2dea-432c-83db-a1c6ea6477e5", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043551180700, "endTime": 223043554378100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "daa6cf60-74c1-4774-b215-2fc11c42935d", "logId": "5b940eb7-3184-407c-b26d-e22743d3e6fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "daa6cf60-74c1-4774-b215-2fc11c42935d", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043548777600}, "additional": {"logType": "detail", "children": [], "durationId": "569c7029-2dea-432c-83db-a1c6ea6477e5"}}, {"head": {"id": "959da24a-f31e-442f-9f3c-87f2f0720550", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043550083700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83a7cbec-54d0-4cb1-b996-30d9053ad0a3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043550210400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34bd3716-414f-4156-8e82-ee3898bb2a05", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043551217600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ffb61ae-f9c9-466b-a133-8238de73e022", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043552808600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e431a984-db80-4790-af42-efe0115354f3", "name": "entry : default@CreateModuleInfo cost memory 0.06098175048828125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043554131000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45799181-bda5-4e81-a470-f946c684def5", "name": "runTaskFromQueue task cost before running: 288 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043554313800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b940eb7-3184-407c-b26d-e22743d3e6fe", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043551180700, "endTime": 223043554378100, "totalTime": 3125000}, "additional": {"logType": "info", "children": [], "durationId": "569c7029-2dea-432c-83db-a1c6ea6477e5"}}, {"head": {"id": "4630c931-5cbc-4366-b2c7-f53672153a17", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043567060200, "endTime": 223043570971700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "16605df5-12b0-485c-bf8e-7c01f60ccd75", "logId": "6bbf1388-b08d-45ee-9ec0-f3b948817925"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16605df5-12b0-485c-bf8e-7c01f60ccd75", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043556988100}, "additional": {"logType": "detail", "children": [], "durationId": "4630c931-5cbc-4366-b2c7-f53672153a17"}}, {"head": {"id": "bb20ad4a-e170-480e-880a-f4e6ce062e23", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043558196800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80325545-5ce4-47e6-9734-3d47fa4c2bef", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043558333800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0a2b88c-ea82-41f2-ac60-23f74062f966", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043567080000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "769bdfb6-0af7-43a4-a384-a69425a61bae", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043568764900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f195ebbe-22ae-471e-beec-5e18656b0ee7", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043570712700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd7b2ea6-72aa-462d-852a-099d4431cc36", "name": "entry : default@GenerateMetadata cost memory 0.1026458740234375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043570885200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bbf1388-b08d-45ee-9ec0-f3b948817925", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043567060200, "endTime": 223043570971700}, "additional": {"logType": "info", "children": [], "durationId": "4630c931-5cbc-4366-b2c7-f53672153a17"}}, {"head": {"id": "b35284d3-6327-4e01-bd32-5522a96e2d53", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043574426200, "endTime": 223043574749000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "0e00036c-c763-47d8-982d-bec0525f1a54", "logId": "23f48415-9d9b-4a7b-bfac-f200595d8901"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e00036c-c763-47d8-982d-bec0525f1a54", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043572807500}, "additional": {"logType": "detail", "children": [], "durationId": "b35284d3-6327-4e01-bd32-5522a96e2d53"}}, {"head": {"id": "8224b765-aafe-44bb-972d-f9ae43a9ee80", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043574133700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17ad9771-dda8-4c59-b302-3999224cbdc8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043574262800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b626c8a-4275-491b-b933-65c211858bb6", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043574436800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e15bcd68-1455-46af-a762-02d97f60895d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043574536200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b0553ea-97ba-4066-8b68-0e4246568915", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043574573600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c89cf903-8d97-4141-a82c-3bead482dc56", "name": "entry : default@ConfigureCmake cost memory 0.0380096435546875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043574633800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43ab9ccf-b8ba-484e-b9e4-5b6dfda82de9", "name": "runTaskFromQueue task cost before running: 308 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043574707000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23f48415-9d9b-4a7b-bfac-f200595d8901", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043574426200, "endTime": 223043574749000, "totalTime": 263400}, "additional": {"logType": "info", "children": [], "durationId": "b35284d3-6327-4e01-bd32-5522a96e2d53"}}, {"head": {"id": "aa80785b-2c70-4950-9099-20e841a7602d", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043580414000, "endTime": 223043583108600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "36c55aae-a7bf-41a6-93aa-7bad7b836b4f", "logId": "0a23902a-c98f-4932-bced-6180ae684f46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "36c55aae-a7bf-41a6-93aa-7bad7b836b4f", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043577305900}, "additional": {"logType": "detail", "children": [], "durationId": "aa80785b-2c70-4950-9099-20e841a7602d"}}, {"head": {"id": "4267c0eb-0b9b-44c1-903c-dfa1507cabe2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043579217100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "576da567-b54b-493a-8f0f-56e35adf39bb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043579370100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22a3e98b-8b00-41b3-a974-53bc417d105f", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043580430200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fbfc563-702a-431d-8700-496b70acaba3", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043582871900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "556b4f7a-10f6-4692-bd84-4a224aa6697a", "name": "entry : default@MergeProfile cost memory 0.11822509765625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043583036800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a23902a-c98f-4932-bced-6180ae684f46", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043580414000, "endTime": 223043583108600}, "additional": {"logType": "info", "children": [], "durationId": "aa80785b-2c70-4950-9099-20e841a7602d"}}, {"head": {"id": "85c5c86b-10d3-49f2-826f-95acc3edba94", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043589492800, "endTime": 223043594049200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3a6d8bd9-579e-497a-b09e-7d60a76c9c08", "logId": "84adc73d-5ec6-48b3-8a7d-dd6e75906be1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a6d8bd9-579e-497a-b09e-7d60a76c9c08", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043586173900}, "additional": {"logType": "detail", "children": [], "durationId": "85c5c86b-10d3-49f2-826f-95acc3edba94"}}, {"head": {"id": "3bd04d8d-cdcc-4323-af70-98400a80ff75", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043588111100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ac1f9dc-cabf-4e82-b401-04a3b879635c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043588265500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bdbda07-5420-4974-bb9a-28cfb3d7ace7", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043589507000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bdd9522-7230-45bc-933b-957c4b27dd4d", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043590777400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8142807e-0dc2-4235-990d-dbd8c2c85263", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043593563100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "838ed439-70f4-4eab-beed-3ec5d3ea47f0", "name": "entry : default@CreateBuildProfile cost memory 0.10784912109375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043593892300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84adc73d-5ec6-48b3-8a7d-dd6e75906be1", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043589492800, "endTime": 223043594049200}, "additional": {"logType": "info", "children": [], "durationId": "85c5c86b-10d3-49f2-826f-95acc3edba94"}}, {"head": {"id": "27f572be-0c90-4145-8e33-8ea45a111e34", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043599123400, "endTime": 223043599761500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "5ed091b4-c914-456c-af6c-83bf6fc7501c", "logId": "1fabdece-fa9b-4933-a3e0-a502e2c6f6f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ed091b4-c914-456c-af6c-83bf6fc7501c", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043596673600}, "additional": {"logType": "detail", "children": [], "durationId": "27f572be-0c90-4145-8e33-8ea45a111e34"}}, {"head": {"id": "6d2dc00b-1fb2-4a10-a11c-c75d88d71d7f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043597976400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d617e26-2599-48bb-9e15-def17159d615", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043598125000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fe35e7c-2017-4809-ab14-b89e03ec36c8", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043599138000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b36917b-7209-40d2-bae4-4b515613ff24", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043599307300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "479b41c4-1644-4e91-9b46-9c14c88ea6cd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043599366900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e228d5b7-aad5-49e8-9250-b1b9c3a7d38a", "name": "entry : default@PreCheckSyscap cost memory 0.041107177734375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043599602300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d036382a-6c0d-40d4-b61b-44d8f42ad6e3", "name": "runTaskFromQueue task cost before running: 333 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043599712200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fabdece-fa9b-4933-a3e0-a502e2c6f6f7", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043599123400, "endTime": 223043599761500, "totalTime": 569400}, "additional": {"logType": "info", "children": [], "durationId": "27f572be-0c90-4145-8e33-8ea45a111e34"}}, {"head": {"id": "c8dca433-3558-43a6-ab0c-a2e407c79a1c", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043605940800, "endTime": 223043612623900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "aaa3e861-552c-4f6a-ac33-4deeb085e12b", "logId": "5d1a9a2b-4bea-469e-afbd-e3a720da4cab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aaa3e861-552c-4f6a-ac33-4deeb085e12b", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043602283400}, "additional": {"logType": "detail", "children": [], "durationId": "c8dca433-3558-43a6-ab0c-a2e407c79a1c"}}, {"head": {"id": "c40fc78c-1f53-4231-96ed-82b80956fd83", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043603918500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b8e55aa-2b62-4e54-ab5e-aa0c1b4187d9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043604073600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4895f49a-a33f-4018-b963-c32c60cf890c", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043605958500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "799f6afa-9085-4bb4-9c25-946d919e0c5d", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043611548900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b91dfe7-9ba4-4800-921e-84f90859bee0", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043612399400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b8ce467-9da3-40c3-936a-e2944d98e32c", "name": "entry : default@GeneratePkgContextInfo cost memory 0.24840545654296875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043612547100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d1a9a2b-4bea-469e-afbd-e3a720da4cab", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043605940800, "endTime": 223043612623900}, "additional": {"logType": "info", "children": [], "durationId": "c8dca433-3558-43a6-ab0c-a2e407c79a1c"}}, {"head": {"id": "dc7841e5-464b-49fe-801d-ba49824dd966", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043622460300, "endTime": 223043625239600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "cc3fa158-40ca-4fea-8c5a-4b6a5e3f1607", "logId": "bf76ee31-f00a-4b9b-bef9-56bcc9466ef1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc3fa158-40ca-4fea-8c5a-4b6a5e3f1607", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043614534500}, "additional": {"logType": "detail", "children": [], "durationId": "dc7841e5-464b-49fe-801d-ba49824dd966"}}, {"head": {"id": "77f217da-4263-4788-9bd3-ac55cb0955e5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043615893000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1848c4c6-a6ef-45e9-a8f7-5c2da7bd7b50", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043616033100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b02603e7-0cca-4e0b-9393-655e6e85866d", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043622485200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee5dc081-25e3-4115-aabb-327be59f5cf8", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043624684600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cd0ebd6-2c27-4fa5-bd76-33977d27466f", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043624882000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ce2b343-9050-44fe-b361-4f1ce5dff12f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043624984800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b408c254-40e5-43d0-9384-ee943298ff81", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043625040100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b8acbab-f704-4c85-a742-dd667797ba43", "name": "entry : default@ProcessIntegratedHsp cost memory 0.12073516845703125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043625120600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d152a780-3223-4c19-adc3-12584b609553", "name": "runTaskFromQueue task cost before running: 359 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043625193200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf76ee31-f00a-4b9b-bef9-56bcc9466ef1", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043622460300, "endTime": 223043625239600, "totalTime": 2720900}, "additional": {"logType": "info", "children": [], "durationId": "dc7841e5-464b-49fe-801d-ba49824dd966"}}, {"head": {"id": "fe0e5938-9943-4d37-877a-dabb34b52772", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043631826100, "endTime": 223043632272200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "3e945319-e166-441b-9168-2c0812e62eb9", "logId": "9e415611-4dde-404c-b0b8-29b687a63934"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e945319-e166-441b-9168-2c0812e62eb9", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043629158200}, "additional": {"logType": "detail", "children": [], "durationId": "fe0e5938-9943-4d37-877a-dabb34b52772"}}, {"head": {"id": "53a54b13-ee22-4bbf-862d-13381f57fb4c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043630720200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e8746e4-0648-415a-9771-267ec0f2ab8a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043630895000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8eb3c2d-0f54-4c43-bf05-d18c51559b2e", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043631838400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3804867-dd64-4c6c-b1f9-da94ef60e4c7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043632002400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d1b31a5-a827-49eb-9497-6c1d3be657b4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043632066700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e33917ba-b70b-404f-a389-eb8dd2eae9a2", "name": "entry : default@BuildNativeWithCmake cost memory 0.03852081298828125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043632139000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8c32997-7a24-4893-9e6f-579cfe3f766b", "name": "runTaskFromQueue task cost before running: 366 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043632220100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e415611-4dde-404c-b0b8-29b687a63934", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043631826100, "endTime": 223043632272200, "totalTime": 376100}, "additional": {"logType": "info", "children": [], "durationId": "fe0e5938-9943-4d37-877a-dabb34b52772"}}, {"head": {"id": "4086ce74-b27f-4e32-85dc-2a61f58aba3d", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043636973300, "endTime": 223043641566100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9e04f078-18bb-4c15-9199-10171bcbdeb8", "logId": "a83f9f93-f5f4-4cea-8c35-2d6839c23624"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e04f078-18bb-4c15-9199-10171bcbdeb8", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043634093900}, "additional": {"logType": "detail", "children": [], "durationId": "4086ce74-b27f-4e32-85dc-2a61f58aba3d"}}, {"head": {"id": "4824d6e4-d31e-4734-b2b3-e814de08f994", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043635687300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52f35721-50e4-409f-9fa5-1c691895737a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043635886500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f965845-963b-401f-b982-9d6f3c69e08d", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043636988500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d063ca5-a844-449a-b323-0e7a980e9d0e", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043641308800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "804ad16d-a5c6-4392-bb8d-0af993f8c656", "name": "entry : default@MakePackInfo cost memory 0.16367340087890625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043641469600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a83f9f93-f5f4-4cea-8c35-2d6839c23624", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043636973300, "endTime": 223043641566100}, "additional": {"logType": "info", "children": [], "durationId": "4086ce74-b27f-4e32-85dc-2a61f58aba3d"}}, {"head": {"id": "a43cee53-c88b-4307-80c3-3d8b99ae8a71", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043647774700, "endTime": 223043652572000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7d915ee1-5f9e-4abb-8e9d-688620088fc8", "logId": "57bd65b4-4264-4e85-b786-8a30dd525943"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d915ee1-5f9e-4abb-8e9d-688620088fc8", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043644908800}, "additional": {"logType": "detail", "children": [], "durationId": "a43cee53-c88b-4307-80c3-3d8b99ae8a71"}}, {"head": {"id": "d78e44d3-9712-4e83-8644-a9dec71c88e9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043646187700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "239a86c3-a323-4e49-9d44-5fb041a2f73b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043646334700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6016c422-827a-44fd-9ac5-58d1456e3c12", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043647788800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33c4e949-167b-4232-85fb-eb1d90459aaf", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043648046900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "530ae503-7f6c-4d83-a53b-6162d0324311", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043648897700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "862b1ac9-f94f-4b2d-a924-7af9feaa60b8", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043652202600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d42a4a7a-00c0-4b8d-99f1-8844053df31c", "name": "entry : default@SyscapTransform cost memory 0.1499481201171875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043652452900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57bd65b4-4264-4e85-b786-8a30dd525943", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043647774700, "endTime": 223043652572000}, "additional": {"logType": "info", "children": [], "durationId": "a43cee53-c88b-4307-80c3-3d8b99ae8a71"}}, {"head": {"id": "f866f494-23aa-4222-8ee6-557f393bf271", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043657628000, "endTime": 223043660687200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "6291035f-0444-498f-a26e-87438c1dba22", "logId": "ee68cc44-a4b9-4961-9722-a247472b7d64"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6291035f-0444-498f-a26e-87438c1dba22", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043654698200}, "additional": {"logType": "detail", "children": [], "durationId": "f866f494-23aa-4222-8ee6-557f393bf271"}}, {"head": {"id": "85abc669-38af-47f4-bf22-c26684<PERSON><PERSON>da", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043656153300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d021603f-fa80-4c63-a25f-ce9727d3d9fb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043656294400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bff24914-ef4b-4474-811f-cdbfc9c50df6", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043657644800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a96bccee-47d8-4d5d-a8f9-9404a6770b1c", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043660326900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4c9a3b9-7564-45e0-95a8-cb858830b049", "name": "entry : default@ProcessProfile cost memory 0.12326812744140625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043660577600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee68cc44-a4b9-4961-9722-a247472b7d64", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043657628000, "endTime": 223043660687200}, "additional": {"logType": "info", "children": [], "durationId": "f866f494-23aa-4222-8ee6-557f393bf271"}}, {"head": {"id": "4fbf91b6-7d80-4cb1-9f80-48f1d6b29dd0", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043666218500, "endTime": 223043675282500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "13e87f69-5e24-4ebd-ac84-a4b101f619d2", "logId": "d997b165-0ca9-42d4-b100-6409aa8215e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13e87f69-5e24-4ebd-ac84-a4b101f619d2", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043662842700}, "additional": {"logType": "detail", "children": [], "durationId": "4fbf91b6-7d80-4cb1-9f80-48f1d6b29dd0"}}, {"head": {"id": "cbb82349-706e-4e16-b9f6-9eedfeedfbac", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043664083700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f01f4188-2ef6-47d2-bdb1-2d947a7cbedb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043664213900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e47d2927-896b-4786-acfd-30f8b1f45a46", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043666234100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "131ca4e1-7239-49df-8e06-4c8262f634cc", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043675018300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3d0c55f-ce8a-434e-b83a-41dd30de5d4c", "name": "entry : default@ProcessRouterMap cost memory 0.23328399658203125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043675196700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d997b165-0ca9-42d4-b100-6409aa8215e2", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043666218500, "endTime": 223043675282500}, "additional": {"logType": "info", "children": [], "durationId": "4fbf91b6-7d80-4cb1-9f80-48f1d6b29dd0"}}, {"head": {"id": "b7d6845e-cefa-4850-9ee7-ff6a06be4596", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043680830100, "endTime": 223043688250800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "2391627f-df8a-4a27-9ca4-f56e8f02ab4d", "logId": "74294e34-89a2-496d-9fa8-1e16ce6c7c22"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2391627f-df8a-4a27-9ca4-f56e8f02ab4d", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043679428300}, "additional": {"logType": "detail", "children": [], "durationId": "b7d6845e-cefa-4850-9ee7-ff6a06be4596"}}, {"head": {"id": "669a901d-ce92-48f7-9b28-8e68c3c12b28", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043680583900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6305fee6-3827-41af-a16f-98cbd2d46270", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043680720800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3d2760a-74c3-4944-b025-aac790d61e9c", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043680837900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0139c690-4ad9-4c47-8ce5-3928f78558fa", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043680941000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "996be0db-ae6d-4b23-a02a-31564a35685b", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043685058100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d5c4126-a1da-446f-b6b4-b0407a6e6af0", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043685426400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d87d9d78-019e-4423-8a50-621939ad3214", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043685663400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0933b31-bb5e-43c4-a04f-3b1a94e795f7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043685751400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcd24535-1581-49ae-9f47-61a4cc1527c6", "name": "entry : default@ProcessStartupConfig cost memory 0.2591400146484375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043687967800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67117884-322a-4287-9249-cf978016b32e", "name": "runTaskFromQueue task cost before running: 422 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043688168000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74294e34-89a2-496d-9fa8-1e16ce6c7c22", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043680830100, "endTime": 223043688250800, "totalTime": 7301200}, "additional": {"logType": "info", "children": [], "durationId": "b7d6845e-cefa-4850-9ee7-ff6a06be4596"}}, {"head": {"id": "069bae43-c7a4-4970-b787-33518fa07146", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043696621300, "endTime": 223043698150500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "10e9eb2a-2886-4ed6-b2a9-31c63fe5e55d", "logId": "ef3404b7-a864-4cac-9164-bc5b3a70ccbb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "10e9eb2a-2886-4ed6-b2a9-31c63fe5e55d", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043693169300}, "additional": {"logType": "detail", "children": [], "durationId": "069bae43-c7a4-4970-b787-33518fa07146"}}, {"head": {"id": "d8aa4ea8-6f73-472a-878f-57e2ee216cf8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043695356300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1ac4aa6-d888-4e8a-98b6-a903e90ba8b2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043695543700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7b56e7e-f1fb-4cb3-ab9a-75c680d9ccfe", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043696635400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd1fd67c-a707-4adc-beaa-3b9ac9ac3417", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043696792400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16ebc89d-4fe8-4b4e-93e4-4556fbdbb8ee", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043696843200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84d09d85-3552-4311-a499-c48decc43546", "name": "entry : default@BuildNativeWithNinja cost memory 0.05826568603515625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043697931600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d869c5b2-8a76-4295-8eb8-a73cd2e6d0d9", "name": "runTaskFromQueue task cost before running: 432 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043698090000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef3404b7-a864-4cac-9164-bc5b3a70ccbb", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043696621300, "endTime": 223043698150500, "totalTime": 1440700}, "additional": {"logType": "info", "children": [], "durationId": "069bae43-c7a4-4970-b787-33518fa07146"}}, {"head": {"id": "d1b254a2-09ad-4010-912b-05460908a7d3", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043706329700, "endTime": 223043713786700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "5765352a-3490-4afb-bc38-253557b9c1b6", "logId": "ba0db3c6-8449-4b83-870a-ca2f4f326ea5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5765352a-3490-4afb-bc38-253557b9c1b6", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043701519300}, "additional": {"logType": "detail", "children": [], "durationId": "d1b254a2-09ad-4010-912b-05460908a7d3"}}, {"head": {"id": "5e6123d1-ae5d-4d87-be11-ffc4ae9244e2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043703269200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6bd1158-8916-4898-9ecd-6017d1dcdec4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043703427400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c1e19fc-b02e-406d-91dd-ccc78a2bc988", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043704741300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d7f7929-62ce-47af-80d7-b94a9d592f35", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043708354900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f417d0e8-94ca-4032-8b0f-f4edf2dd18b2", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043711699000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1741544-8020-4f6f-baa7-875b9116d6d7", "name": "entry : default@ProcessResource cost memory 0.162017822265625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043711870200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba0db3c6-8449-4b83-870a-ca2f4f326ea5", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043706329700, "endTime": 223043713786700}, "additional": {"logType": "info", "children": [], "durationId": "d1b254a2-09ad-4010-912b-05460908a7d3"}}, {"head": {"id": "e915947b-a84c-4eee-8872-2303f00d03cf", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043723764600, "endTime": 223043747811000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4fba63e2-6bbb-4853-802b-22b64df5c20b", "logId": "1feefec3-3c65-41ef-883a-b3bd86f9c112"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4fba63e2-6bbb-4853-802b-22b64df5c20b", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043718135900}, "additional": {"logType": "detail", "children": [], "durationId": "e915947b-a84c-4eee-8872-2303f00d03cf"}}, {"head": {"id": "e3e1c4b1-3069-4233-bbcd-025219e6e1ca", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043719850800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9a5bd60-a17a-4880-b9b1-0d1139a6de21", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043720009400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d89e746-d275-4203-8e92-c4592a49e0f8", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043723781400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb7ffac1-a00c-4ec0-a051-a6a9ea54ffb6", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043747573400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "313477d0-3453-4ca5-b9ea-ee31c9e172ba", "name": "entry : default@GenerateLoaderJson cost memory 0.8762664794921875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043747738200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1feefec3-3c65-41ef-883a-b3bd86f9c112", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043723764600, "endTime": 223043747811000}, "additional": {"logType": "info", "children": [], "durationId": "e915947b-a84c-4eee-8872-2303f00d03cf"}}, {"head": {"id": "6a90668b-57f3-427a-9f0c-35b0db5b6a5e", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043761691500, "endTime": 223043766979900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "4e490728-7ad2-4c44-995f-95e2a1cfa055", "logId": "b183197b-1b03-410d-a3cc-eacb98cca145"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e490728-7ad2-4c44-995f-95e2a1cfa055", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043758830700}, "additional": {"logType": "detail", "children": [], "durationId": "6a90668b-57f3-427a-9f0c-35b0db5b6a5e"}}, {"head": {"id": "c92078af-3230-414a-a206-e9959588794f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043760237900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd7df455-3d75-4cc2-a5e3-9b42de541830", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043760440700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fca70c8-386d-4f21-8588-86ea2f61549c", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043761704800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbf7dc08-83b8-4335-8702-45ef95e582b1", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043766751100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50757897-c2b0-4a4f-8748-8b01799c3be4", "name": "entry : default@ProcessLibs cost memory 0.1420745849609375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043766913400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b183197b-1b03-410d-a3cc-eacb98cca145", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043761691500, "endTime": 223043766979900}, "additional": {"logType": "info", "children": [], "durationId": "6a90668b-57f3-427a-9f0c-35b0db5b6a5e"}}, {"head": {"id": "f4c31085-dba1-45f0-b7c4-df928d920823", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043775471800, "endTime": 223043810797500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "20640394-6c06-4b5f-a11a-c3554edc6fa5", "logId": "460ef17d-1f27-456c-aca5-b54eef234e39"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "20640394-6c06-4b5f-a11a-c3554edc6fa5", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043769837200}, "additional": {"logType": "detail", "children": [], "durationId": "f4c31085-dba1-45f0-b7c4-df928d920823"}}, {"head": {"id": "c57c7674-0691-49a9-9f9a-8e0642bd75ac", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043770931100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56a729c2-aac2-48e7-a771-ad0429a6d926", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043771086900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0b09c0b-ce30-4428-8c17-334d2e498786", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043772175000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5700cade-4959-4a9a-afb0-7460c87ce1df", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043775498400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c42f7956-8c05-47d0-bec9-72521c892cd7", "name": "Incremental task entry:default@CompileResource pre-execution cost: 33 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043810172900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1506b519-8bc5-4506-8e37-e996424bb0f3", "name": "entry : default@CompileResource cost memory 1.3160858154296875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043810596500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "460ef17d-1f27-456c-aca5-b54eef234e39", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043775471800, "endTime": 223043810797500}, "additional": {"logType": "info", "children": [], "durationId": "f4c31085-dba1-45f0-b7c4-df928d920823"}}, {"head": {"id": "fc063569-6cfc-417e-8827-e52ba9a27cb3", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043819179400, "endTime": 223043822613200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "a3eaff6d-10fe-4143-8c6b-bfd856ff76c9", "logId": "6f56c148-2539-42c7-8ff3-7c1d663b6362"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a3eaff6d-10fe-4143-8c6b-bfd856ff76c9", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043814986800}, "additional": {"logType": "detail", "children": [], "durationId": "fc063569-6cfc-417e-8827-e52ba9a27cb3"}}, {"head": {"id": "db04a1f7-e567-4c5b-91df-2165a5bd03a3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043816138100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f3a16cd-3d2c-4a93-8478-3f7a5b28c203", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043816256400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b83d02c2-6de5-4c0d-94e6-ef3128f142f5", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043819197900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a778c19-6b7a-42d3-8e22-8997e53eaba9", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043820169300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5f1f3f5-45fb-406f-85b7-78a1e6023a1d", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043822353900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1db704a7-2597-4e0d-81ed-45a22e680d0d", "name": "entry : default@DoNativeStrip cost memory 0.07946014404296875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043822531800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f56c148-2539-42c7-8ff3-7c1d663b6362", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043819179400, "endTime": 223043822613200}, "additional": {"logType": "info", "children": [], "durationId": "fc063569-6cfc-417e-8827-e52ba9a27cb3"}}, {"head": {"id": "38f01484-e1e3-44fb-98fb-7ce34e17f2ba", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043836151000, "endTime": 223062602548300}, "additional": {"children": ["db29c472-f14f-4f27-86ae-6fbfc3ba289e", "0055d0ee-9087-4db4-a473-5aac9319c320"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "bced2df7-edfc-4521-bec1-f0fa70ac2648", "logId": "b8700b20-7f4f-42ca-a57a-9d1865aca828"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bced2df7-edfc-4521-bec1-f0fa70ac2648", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043824346800}, "additional": {"logType": "detail", "children": [], "durationId": "38f01484-e1e3-44fb-98fb-7ce34e17f2ba"}}, {"head": {"id": "bfa4aba9-87dc-4299-9559-cf5f0e89d935", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043825468100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6399bcf5-7196-4dca-b236-af9c4abb1505", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043825616600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e088202-f3f9-468e-bf1c-54a6c8b28491", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043836166800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa41f49b-871b-485c-aa7c-09655b3652b4", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043836374300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1053640-f9bb-451a-83da-d34fffc5c95e", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043878071100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23dfa929-3750-48a9-b767-0872b484fa0b", "name": "default@CompileArkTS work[10] is submitted.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043880629900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db29c472-f14f-4f27-86ae-6fbfc3ba289e", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 223048615813700, "endTime": 223062588598000}, "additional": {"children": ["80625825-3a8b-4d85-818a-5f2749e742b4", "f60d945f-cff2-4482-ac33-67d2c47353ec", "d5db89bb-4664-4530-af70-52e30bcd1d29", "f6eda074-59e7-47b4-90c3-3e25a8262d8b", "0ff9043c-5e36-4c6e-8e2c-676c5335646d", "1f9d530d-ca0d-4625-8437-01cb2a32ec61", "0c4b159f-93fa-409f-a11a-aabf5c4028c1"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "38f01484-e1e3-44fb-98fb-7ce34e17f2ba", "logId": "567b01c6-096d-4d39-b910-4ef9d64061d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7282a85-2c01-4829-a259-d7474d70a14c", "name": "default@CompileArkTS work[10] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043881696600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed6ceac2-9ce7-45d0-bfd6-e0692fc41ae9", "name": "default@CompileArkTS work[10] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043881826300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2597a4c3-25ce-4ffe-bb31-bb89f032c112", "name": "CopyResources startTime: 223043881883600", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043881886700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af04e7c9-9399-4bf3-9661-079ed13a7df9", "name": "default@CompileArkTS work[11] is submitted.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043881949300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0055d0ee-9087-4db4-a473-5aac9319c320", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker18", "startTime": 223045155135400, "endTime": 223045183494400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "38f01484-e1e3-44fb-98fb-7ce34e17f2ba", "logId": "3a76b4c7-3f78-4acc-87e0-6e2f7449edd2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "26956bad-f736-4134-b2a7-89ae979131e4", "name": "default@CompileArkTS work[11] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043882827400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32bc6e0d-d182-42a4-b3ad-c23e2f872311", "name": "default@CompileArkTS work[11] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043882936000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c98fdd2a-7256-4d6b-8e24-8cee49c395f7", "name": "entry : default@CompileArkTS cost memory 1.401763916015625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043883031800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0ebf99f-f907-4f76-b83f-cf4e0385c1bf", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043892143700, "endTime": 223043903431600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "de0d1f0f-b18c-43f0-b55c-49c450c02ac3", "logId": "04884119-3832-4b0d-838b-214c2345e9f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de0d1f0f-b18c-43f0-b55c-49c450c02ac3", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043885213100}, "additional": {"logType": "detail", "children": [], "durationId": "d0ebf99f-f907-4f76-b83f-cf4e0385c1bf"}}, {"head": {"id": "cded1c04-e951-492a-8578-d72d02f7fc62", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043887121100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e87bd301-8846-4458-85cd-3e83860c94c9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043887324100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "440bba8f-e0da-4308-820f-0a2250eab271", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043892157900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c52181b-d65c-413b-b7a2-54479c51d4f2", "name": "entry : default@BuildJS cost memory -6.7765655517578125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043903172100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dc42797-8dc0-4535-9ad2-1867fbc68988", "name": "runTaskFromQueue task cost before running: 637 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043903360900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04884119-3832-4b0d-838b-214c2345e9f2", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043892143700, "endTime": 223043903431600, "totalTime": 11185400}, "additional": {"logType": "info", "children": [], "durationId": "d0ebf99f-f907-4f76-b83f-cf4e0385c1bf"}}, {"head": {"id": "3df765f0-899c-404e-89e4-ebd0a6d89fb9", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043910157200, "endTime": 223043914484700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "87d9258c-4143-4bdc-9c8f-113df7094e52", "logId": "1289f4b8-cdf7-414c-bdb2-926c101c094a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87d9258c-4143-4bdc-9c8f-113df7094e52", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043905425400}, "additional": {"logType": "detail", "children": [], "durationId": "3df765f0-899c-404e-89e4-ebd0a6d89fb9"}}, {"head": {"id": "2edf6a6d-8b58-4ef4-8d17-d35f1948d0be", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043906831100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48379088-e29a-4afa-88d3-bdb31dd23f2f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043906966100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce0770fe-477a-4b38-b74a-de395e7319a6", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043910175200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a49d80ca-608c-491f-ae55-b7dbd68e29a5", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043911339700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9ea91d6-7d1f-4a04-8ef0-ed5c21f79a36", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043914234200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ddb674e-584c-47e2-9f86-49785617cf4c", "name": "entry : default@CacheNativeLibs cost memory 0.0947113037109375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043914401200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1289f4b8-cdf7-414c-bdb2-926c101c094a", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043910157200, "endTime": 223043914484700}, "additional": {"logType": "info", "children": [], "durationId": "3df765f0-899c-404e-89e4-ebd0a6d89fb9"}}, {"head": {"id": "dc69ebd2-1da2-45d4-9699-ef3dd21f4a1d", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223044087120200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fa62d02-5cc7-4a14-a711-abc4ded6e029", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223044087371700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a395a9b1-5ff1-470e-aa9f-838e69d1ec6c", "name": "default@CompileArkTS work[11] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223044088175000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c694f16-abfc-4284-8809-7c2ecc69ce12", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223044739911600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fc3bfbc-9702-4ca6-b6a9-6f38f0b6d4bd", "name": "A work dispatched to worker[18] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223044740069200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baa815c4-35e4-49f4-8d65-1f37a591e447", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223044740133800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5df7450-5c57-465c-ba13-68d64e796fb3", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223044740166300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "527116f4-b980-43e8-a30f-a5b997b5e6fe", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223044740195500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f74e9761-7fe5-4384-b746-c9aa0fc444c0", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223044740223100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc50699f-7853-4ce4-96d4-80a4fc6cb38d", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223044740250600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5273426b-745c-4751-b388-e2e0bbf50e8b", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223044740280300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8565a9dd-4122-4b8a-9adb-2fb584c98e6c", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223044740306700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8afa1ad-089a-43e2-acd3-c9d0246af681", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223044740333600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b54f4c5-2a91-4c24-9c1c-2b175e057e45", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223044740359500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92eb1f03-5c07-4d35-b0f0-f93171bf39c5", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223044740410200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "901ca8d0-ffbe-4ccc-a0c7-c6d44c9ce4a8", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223044740441000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9699eb78-74aa-41c0-8c3f-8f9fb5ef9e08", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223044740469300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2374390f-ad2f-49e4-890e-7684162c886d", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223044740495800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fffc7a9-057e-418e-8b06-0aff997182d1", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223044740530500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f67fe2e-f6a2-4b89-8c68-7d6d4a789a62", "name": "default@CompileArkTS work[10] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223044741334000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "612cfd39-acbe-4025-a6ea-3bc651ae2b7b", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223045183831100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ee7d83e-a328-4eaa-aef8-9c483cd0da5b", "name": "CopyResources is end, endTime: 223045184071000", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223045184080700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a425af02-2610-4235-badb-4916b7aac469", "name": "default@CompileArkTS work[11] done.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223045185539300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a76b4c7-3f78-4acc-87e0-6e2f7449edd2", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Worker18", "startTime": 223045155135400, "endTime": 223045183494400}, "additional": {"logType": "info", "children": [], "durationId": "0055d0ee-9087-4db4-a473-5aac9319c320", "parent": "b8700b20-7f4f-42ca-a57a-9d1865aca828"}}, {"head": {"id": "12bed023-0ddc-4511-a818-d20bcd22322f", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223045732028500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "199fd408-e04c-4f5a-9f58-f8da89340954", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062591726700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80625825-3a8b-4d85-818a-5f2749e742b4", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 223048617204200, "endTime": 223049896370900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "db29c472-f14f-4f27-86ae-6fbfc3ba289e", "logId": "e94b0395-1e25-441d-a28a-e61a569659e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e94b0395-1e25-441d-a28a-e61a569659e4", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223048617204200, "endTime": 223049896370900}, "additional": {"logType": "info", "children": [], "durationId": "80625825-3a8b-4d85-818a-5f2749e742b4", "parent": "567b01c6-096d-4d39-b910-4ef9d64061d7"}}, {"head": {"id": "f60d945f-cff2-4482-ac33-67d2c47353ec", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 223049898170100, "endTime": 223049955085800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "db29c472-f14f-4f27-86ae-6fbfc3ba289e", "logId": "4df3d859-8635-412e-8ff7-881a056e3c84"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4df3d859-8635-412e-8ff7-881a056e3c84", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223049898170100, "endTime": 223049955085800}, "additional": {"logType": "info", "children": [], "durationId": "f60d945f-cff2-4482-ac33-67d2c47353ec", "parent": "567b01c6-096d-4d39-b910-4ef9d64061d7"}}, {"head": {"id": "d5db89bb-4664-4530-af70-52e30bcd1d29", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 223049955212400, "endTime": 223049955519900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "db29c472-f14f-4f27-86ae-6fbfc3ba289e", "logId": "07db4e31-86c9-4b61-b2a2-64db6543d1ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "07db4e31-86c9-4b61-b2a2-64db6543d1ac", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223049955212400, "endTime": 223049955519900}, "additional": {"logType": "info", "children": [], "durationId": "d5db89bb-4664-4530-af70-52e30bcd1d29", "parent": "567b01c6-096d-4d39-b910-4ef9d64061d7"}}, {"head": {"id": "f6eda074-59e7-47b4-90c3-3e25a8262d8b", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 223049955629300, "endTime": 223062154644100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "db29c472-f14f-4f27-86ae-6fbfc3ba289e", "logId": "2b39b472-e766-41f3-8b49-7e3ec44dc179"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b39b472-e766-41f3-8b49-7e3ec44dc179", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223049955629300, "endTime": 223062154644100}, "additional": {"logType": "info", "children": [], "durationId": "f6eda074-59e7-47b4-90c3-3e25a8262d8b", "parent": "567b01c6-096d-4d39-b910-4ef9d64061d7"}}, {"head": {"id": "0ff9043c-5e36-4c6e-8e2c-676c5335646d", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 223062155271600, "endTime": 223062193970000}, "additional": {"children": ["0ca8564c-7e3f-4190-bf24-153bcd82f658", "7200dce0-4f5c-43f3-83e2-680608be419b", "b8c60cf6-1c0a-44ac-9826-107701e97f3f"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "db29c472-f14f-4f27-86ae-6fbfc3ba289e", "logId": "35c1ca0c-a526-46fc-9843-e3892f6d49cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35c1ca0c-a526-46fc-9843-e3892f6d49cc", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062155271600, "endTime": 223062193970000}, "additional": {"logType": "info", "children": ["017198bd-43dd-46c1-b087-d8b00624b491", "e27c2a07-a996-4ca3-8227-1d98fd300a61", "32a7fca1-a175-4c3d-96da-b5c0e23c4beb"], "durationId": "0ff9043c-5e36-4c6e-8e2c-676c5335646d", "parent": "567b01c6-096d-4d39-b910-4ef9d64061d7"}}, {"head": {"id": "0ca8564c-7e3f-4190-bf24-153bcd82f658", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 223062155533600, "endTime": 223062155569700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "0ff9043c-5e36-4c6e-8e2c-676c5335646d", "logId": "017198bd-43dd-46c1-b087-d8b00624b491"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "017198bd-43dd-46c1-b087-d8b00624b491", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062155533600, "endTime": 223062155569700}, "additional": {"logType": "info", "children": [], "durationId": "0ca8564c-7e3f-4190-bf24-153bcd82f658", "parent": "35c1ca0c-a526-46fc-9843-e3892f6d49cc"}}, {"head": {"id": "7200dce0-4f5c-43f3-83e2-680608be419b", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 223062155583400, "endTime": 223062181481000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "0ff9043c-5e36-4c6e-8e2c-676c5335646d", "logId": "e27c2a07-a996-4ca3-8227-1d98fd300a61"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e27c2a07-a996-4ca3-8227-1d98fd300a61", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062155583400, "endTime": 223062181481000}, "additional": {"logType": "info", "children": [], "durationId": "7200dce0-4f5c-43f3-83e2-680608be419b", "parent": "35c1ca0c-a526-46fc-9843-e3892f6d49cc"}}, {"head": {"id": "b8c60cf6-1c0a-44ac-9826-107701e97f3f", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 223062181494000, "endTime": 223062193863800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "0ff9043c-5e36-4c6e-8e2c-676c5335646d", "logId": "32a7fca1-a175-4c3d-96da-b5c0e23c4beb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "32a7fca1-a175-4c3d-96da-b5c0e23c4beb", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062181494000, "endTime": 223062193863800}, "additional": {"logType": "info", "children": [], "durationId": "b8c60cf6-1c0a-44ac-9826-107701e97f3f", "parent": "35c1ca0c-a526-46fc-9843-e3892f6d49cc"}}, {"head": {"id": "1f9d530d-ca0d-4625-8437-01cb2a32ec61", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 223062194000300, "endTime": 223062579431000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "db29c472-f14f-4f27-86ae-6fbfc3ba289e", "logId": "fb642185-3c17-4123-9dc1-0058c020319c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb642185-3c17-4123-9dc1-0058c020319c", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062194000300, "endTime": 223062579431000}, "additional": {"logType": "info", "children": [], "durationId": "1f9d530d-ca0d-4625-8437-01cb2a32ec61", "parent": "567b01c6-096d-4d39-b910-4ef9d64061d7"}}, {"head": {"id": "0c4b159f-93fa-409f-a11a-aabf5c4028c1", "name": "load compilation dependencies", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 223045875500700, "endTime": 223048613250000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "db29c472-f14f-4f27-86ae-6fbfc3ba289e", "logId": "110d3d76-e40e-47c3-978b-d2097017466e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "110d3d76-e40e-47c3-978b-d2097017466e", "name": "load compilation dependencies", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223045875500700, "endTime": 223048613250000}, "additional": {"logType": "info", "children": [], "durationId": "0c4b159f-93fa-409f-a11a-aabf5c4028c1", "parent": "567b01c6-096d-4d39-b910-4ef9d64061d7"}}, {"head": {"id": "98844d55-5a28-4765-be3f-f11e3f7b865c", "name": "default@CompileArkTS work[10] done.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062602104100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "567b01c6-096d-4d39-b910-4ef9d64061d7", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 223048615813700, "endTime": 223062588598000}, "additional": {"logType": "info", "children": ["e94b0395-1e25-441d-a28a-e61a569659e4", "4df3d859-8635-412e-8ff7-881a056e3c84", "07db4e31-86c9-4b61-b2a2-64db6543d1ac", "2b39b472-e766-41f3-8b49-7e3ec44dc179", "35c1ca0c-a526-46fc-9843-e3892f6d49cc", "fb642185-3c17-4123-9dc1-0058c020319c", "110d3d76-e40e-47c3-978b-d2097017466e"], "durationId": "db29c472-f14f-4f27-86ae-6fbfc3ba289e", "parent": "b8700b20-7f4f-42ca-a57a-9d1865aca828"}}, {"head": {"id": "b8700b20-7f4f-42ca-a57a-9d1865aca828", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043836151000, "endTime": 223062602548300, "totalTime": 14048080200}, "additional": {"logType": "info", "children": ["567b01c6-096d-4d39-b910-4ef9d64061d7", "3a76b4c7-3f78-4acc-87e0-6e2f7449edd2"], "durationId": "38f01484-e1e3-44fb-98fb-7ce34e17f2ba"}}, {"head": {"id": "413e53ae-bacc-42bc-89dc-ae0f4fd95032", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062613027200, "endTime": 223062614831100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "32463400-1cb7-4495-8acb-978e2ce59b23", "logId": "7834d2f2-befe-4c4a-90ed-662e23bcfe44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "32463400-1cb7-4495-8acb-978e2ce59b23", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062610392500}, "additional": {"logType": "detail", "children": [], "durationId": "413e53ae-bacc-42bc-89dc-ae0f4fd95032"}}, {"head": {"id": "fb548a7c-68b5-401e-955d-85d3278657c6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062611636800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5a5bf82-8830-4071-9e3d-f8841495e2f3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062611796000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1da5bf9f-7a7d-4b61-8c25-73a2a1a12422", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062613041700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d6d7186-6d2e-432f-b7df-fcc17afbbbc5", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062613479700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11a575d9-09ff-4d20-af56-96eb2a11280c", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062614597000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58e01a89-2163-497d-8352-157918d15677", "name": "entry : default@GeneratePkgModuleJson cost memory 0.075408935546875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062614738600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7834d2f2-befe-4c4a-90ed-662e23bcfe44", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062613027200, "endTime": 223062614831100}, "additional": {"logType": "info", "children": [], "durationId": "413e53ae-bacc-42bc-89dc-ae0f4fd95032"}}, {"head": {"id": "85b0736e-c262-43b7-8280-400d5d04d772", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062632035900, "endTime": 223062980324100}, "additional": {"children": ["c5737e5a-4422-4ff2-a847-9bc5ba7fbf70", "3900a95b-f151-47d1-8e8b-fc6f8146185f"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "397d63fb-10fe-4e9b-b5e3-6dc611218c5c", "logId": "a5105e9c-4dc1-4ad5-b3b3-de83a0357cd6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "397d63fb-10fe-4e9b-b5e3-6dc611218c5c", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062620140800}, "additional": {"logType": "detail", "children": [], "durationId": "85b0736e-c262-43b7-8280-400d5d04d772"}}, {"head": {"id": "e4f7c155-b967-43f6-8efa-49a27b317ff0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062621355500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e15ac77-a7ce-4a7b-9832-614132b2355d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062621502700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78350200-fb0d-47ce-af4c-9bf7439429f1", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062632053900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12c029aa-8c31-4bf2-a2cf-2865c2f50ddd", "name": "entry:default@PackageHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062664390400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1aad2a37-d27a-4623-87c5-c66cdc8e0ae5", "name": "Incremental task entry:default@PackageHap pre-execution cost: 28 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062664613500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2790503-6af9-472a-92d9-80d40c090c54", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062664717300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b108279-170d-4c7e-be8b-853a260353a6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062664765400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5737e5a-4422-4ff2-a847-9bc5ba7fbf70", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062668555800, "endTime": 223062672596400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "85b0736e-c262-43b7-8280-400d5d04d772", "logId": "65cc14d7-2351-4356-8996-c6a066b82f54"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f2b79e6-a73a-406c-b922-db5695c0ca0a", "name": "Use tool [C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Huawei\\\\Sdk\\\\OpenHarmony\\\\Sdk\\\\18\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--rpcid-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\syscap\\\\default\\\\rpcid.sc',\n  '--ets-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets',\n  '--pkg-context-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader\\\\default\\\\pkgContextInfo.json'\n]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062672312800}, "additional": {"logType": "debug", "children": [], "durationId": "85b0736e-c262-43b7-8280-400d5d04d772"}}, {"head": {"id": "65cc14d7-2351-4356-8996-c6a066b82f54", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062668555800, "endTime": 223062672596400}, "additional": {"logType": "info", "children": [], "durationId": "c5737e5a-4422-4ff2-a847-9bc5ba7fbf70", "parent": "a5105e9c-4dc1-4ad5-b3b3-de83a0357cd6"}}, {"head": {"id": "3900a95b-f151-47d1-8e8b-fc6f8146185f", "name": "submit HAP packaging task to java daemon server", "description": "Pack HAP in java daemon", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062673511700, "endTime": 223062973392300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "85b0736e-c262-43b7-8280-400d5d04d772", "logId": "9f783fa8-2513-48a8-a1b5-efc62da91df7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66c705ad-e538-4bed-a09c-d0371ec14d48", "name": "java daemon socket received message:{\"code\":0,\"message\":\"pack success\"}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062972537400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f783fa8-2513-48a8-a1b5-efc62da91df7", "name": "submit HAP packaging task to java daemon server", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062673511700, "endTime": 223062973384400}, "additional": {"logType": "info", "children": [], "durationId": "3900a95b-f151-47d1-8e8b-fc6f8146185f", "parent": "a5105e9c-4dc1-4ad5-b3b3-de83a0357cd6"}}, {"head": {"id": "0f13da10-dd09-4ae8-9991-c6815a341e53", "name": "entry : default@PackageHap cost memory -0.19759368896484375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062980006000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a76b5c78-6e81-4c3c-acd7-62510a0f0971", "name": "runTaskFromQueue task cost before running: 19 s 714 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062980253900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5105e9c-4dc1-4ad5-b3b3-de83a0357cd6", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062632035900, "endTime": 223062980324100, "totalTime": 348180400}, "additional": {"logType": "info", "children": ["65cc14d7-2351-4356-8996-c6a066b82f54", "9f783fa8-2513-48a8-a1b5-efc62da91df7"], "durationId": "85b0736e-c262-43b7-8280-400d5d04d772"}}, {"head": {"id": "3a90c310-d453-40d9-bc43-6604bd14b767", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062992174500, "endTime": 223063373767100}, "additional": {"children": ["13134639-106a-42f8-bae6-18a4ca798e42", "587618b4-791c-4e39-91d6-8ef521590cb4"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "c6d663ab-d711-483a-a70b-f3e28eb8ee05", "logId": "a068a043-7d51-428a-b39d-d90cb300f5b0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c6d663ab-d711-483a-a70b-f3e28eb8ee05", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062985376500}, "additional": {"logType": "detail", "children": [], "durationId": "3a90c310-d453-40d9-bc43-6604bd14b767"}}, {"head": {"id": "50d186d7-1ae0-4d86-a7c5-b641ed987d82", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062986951200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc858eee-42c5-4f73-b359-e67c690a4758", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062987136600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61453a2c-694d-42db-b799-181a21f12696", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062992199900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7829bfc-9bc1-47f7-a714-fc59bbc7b63f", "name": "entry:default@SignHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062998520200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "603e478d-a7bc-460c-b0e4-eb7b805596c5", "name": "Incremental task entry:default@SignHap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062998747200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "decb4342-cb5f-4865-bd6c-fb87e2f8f5f4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062998871500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ae4b284-fc6e-4940-9670-de16174e7d8f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062998928300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13134639-106a-42f8-bae6-18a4ca798e42", "name": "generate hap signing command", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063001842000, "endTime": 223063125973300}, "additional": {"children": ["ed399387-dfd4-4f35-9df0-00562268e111"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3a90c310-d453-40d9-bc43-6604bd14b767", "logId": "70d94e5d-7ec5-456b-9982-76f446d4b6da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed399387-dfd4-4f35-9df0-00562268e111", "name": "verify signing configuration and get bundle name", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063031319700, "endTime": 223063124138300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "13134639-106a-42f8-bae6-18a4ca798e42", "logId": "82a0c635-8014-462a-bb9f-0fcee951d9b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7dd9806f-8c41-414d-9bb3-1d189d0caef7", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063048964100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83ae6003-cc02-487b-b4e3-bb343d5dc509", "name": "java daemon socket received message:{\"code\":0,\"message\":\"verify profile success\"}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063123679700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82a0c635-8014-462a-bb9f-0fcee951d9b3", "name": "verify signing configuration and get bundle name", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063031319700, "endTime": 223063124138300}, "additional": {"logType": "info", "children": [], "durationId": "ed399387-dfd4-4f35-9df0-00562268e111", "parent": "70d94e5d-7ec5-456b-9982-76f446d4b6da"}}, {"head": {"id": "70d94e5d-7ec5-456b-9982-76f446d4b6da", "name": "generate hap signing command", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063001842000, "endTime": 223063125973300}, "additional": {"logType": "info", "children": ["82a0c635-8014-462a-bb9f-0fcee951d9b3"], "durationId": "13134639-106a-42f8-bae6-18a4ca798e42", "parent": "a068a043-7d51-428a-b39d-d90cb300f5b0"}}, {"head": {"id": "587618b4-791c-4e39-91d6-8ef521590cb4", "name": "execute hap signing command", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063127094100, "endTime": 223063373235800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3a90c310-d453-40d9-bc43-6604bd14b767", "logId": "b388a8aa-e798-4963-90c0-3f07bb89f2f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dfb45aeb-420f-4762-acb7-0278b11dbe49", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063129518000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02c99040-24cc-4113-bdfc-2c8aca3c1045", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063133116400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0b99be9-654b-4c70-99a2-f9082d5dd601", "name": "java daemon socket received message:{\"code\":0,\"message\":\"sign app success\"}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063372784500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b388a8aa-e798-4963-90c0-3f07bb89f2f8", "name": "execute hap signing command", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063127094100, "endTime": 223063373235800}, "additional": {"logType": "info", "children": [], "durationId": "587618b4-791c-4e39-91d6-8ef521590cb4", "parent": "a068a043-7d51-428a-b39d-d90cb300f5b0"}}, {"head": {"id": "711f5c5c-ba46-4098-8efe-287449c2ac7a", "name": "entry : default@SignHap cost memory 0.17962646484375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063373578200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac3cbcc5-3312-438a-a878-2f8d57acfb7c", "name": "runTaskFromQueue task cost before running: 20 s 107 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063373713700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a068a043-7d51-428a-b39d-d90cb300f5b0", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223062992174500, "endTime": 223063373767100, "totalTime": 381521800}, "additional": {"logType": "info", "children": ["70d94e5d-7ec5-456b-9982-76f446d4b6da", "b388a8aa-e798-4963-90c0-3f07bb89f2f8"], "durationId": "3a90c310-d453-40d9-bc43-6604bd14b767"}}, {"head": {"id": "cce42405-815e-42ec-a9d1-b13c88e19a3f", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063378264800, "endTime": 223063384023000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0dc93f3e-2a0f-4b50-a83d-471868280c1a", "logId": "5fa32685-e4a3-4dac-806d-813bfd8d9ad7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0dc93f3e-2a0f-4b50-a83d-471868280c1a", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063375844100}, "additional": {"logType": "detail", "children": [], "durationId": "cce42405-815e-42ec-a9d1-b13c88e19a3f"}}, {"head": {"id": "fbef1079-9955-4002-9200-a8d22d12ede0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063376770500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cc788b0-a79a-48f1-8223-fe2fdc9e56ef", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063376881300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc5922ae-cc9c-45ca-a1ad-0297ca92eba2", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063378277300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4defb52d-0ea0-4894-b056-0887bb1f1c44", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063383595100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "864f9f49-078b-47b9-b9cb-e0938383c3e7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063383743500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9fd9d77-5073-469e-8438-d6564dfe077b", "name": "entry : default@CollectDebugSymbol cost memory 0.24261474609375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063383881900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7589eb5-daa6-4846-9825-2390a018eb18", "name": "runTaskFromQueue task cost before running: 20 s 118 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063383975300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fa32685-e4a3-4dac-806d-813bfd8d9ad7", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063378264800, "endTime": 223063384023000, "totalTime": 5686900}, "additional": {"logType": "info", "children": [], "durationId": "cce42405-815e-42ec-a9d1-b13c88e19a3f"}}, {"head": {"id": "b6dac16a-8fe9-4656-a237-8265f10e7b9f", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063385824500, "endTime": 223063386165100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "c620687e-bca3-43bb-bcc8-ad898767861f", "logId": "246fe168-ed63-4b58-b338-c5f092c6da65"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c620687e-bca3-43bb-bcc8-ad898767861f", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063385764300}, "additional": {"logType": "detail", "children": [], "durationId": "b6dac16a-8fe9-4656-a237-8265f10e7b9f"}}, {"head": {"id": "e36e6e95-984e-436b-bc5a-ad9c19ef2d20", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063385832400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5791fc6-a91a-4700-8d02-3523faaa0260", "name": "entry : assembleHap cost memory 0.011749267578125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063386025900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9508411b-eb95-4580-9956-24def3cb8b04", "name": "runTaskFromQueue task cost before running: 20 s 120 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063386114900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "246fe168-ed63-4b58-b338-c5f092c6da65", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063385824500, "endTime": 223063386165100, "totalTime": 270200}, "additional": {"logType": "info", "children": [], "durationId": "b6dac16a-8fe9-4656-a237-8265f10e7b9f"}}, {"head": {"id": "cd0aff89-5f42-452e-bb27-959ee51479a7", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063400314600, "endTime": 223063400368900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "20978674-ce6b-4254-8cbb-cbcfd1bc80e0", "logId": "73077fe3-f565-4c40-8e5b-466c67400204"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73077fe3-f565-4c40-8e5b-466c67400204", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063400314600, "endTime": 223063400368900}, "additional": {"logType": "info", "children": [], "durationId": "cd0aff89-5f42-452e-bb27-959ee51479a7"}}, {"head": {"id": "42df87ba-3b4b-478f-a415-42aa82ece73f", "name": "BUILD SUCCESSFUL in 20 s 134 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063400651200}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "5718c1dc-a1a0-473b-bbf2-3763008a292b", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223043266822900, "endTime": 223063401206700}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 19, "minute": 54, "second": 53}, "completeCommand": "{\"prop\":[\"product=default\"],\"mode\":\"module\",\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "b648415d-c817-48b5-9284-a042ce8fd27f", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063401279200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1f42320-54a6-4df7-bc5c-d41d9e97933d", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063401414700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c16f2ba6-7cee-4ca1-86dc-637c5bfe2e94", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063402292100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b65c3ea-45c3-4ef3-b20b-c192fa101fd9", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063402490600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8f15552-d79f-4d99-84e0-0df4d529cee8", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063402545800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfd62b5a-9c7a-4f23-a00a-71a3f0d54944", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063402649500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "040aa39a-877c-40c2-9ad0-cf90c27cda52", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063402685100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de7d9945-9cc5-41cf-8c98-c6dc4cea5187", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063403398500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae2a0313-1e1d-4cf0-b66e-17e05a8c8b06", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063403655100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3385e4d-4a9e-4d10-82f5-9b5cb5e5c082", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063403716000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bd6d0a8-2b71-4d05-97fc-73636d59213e", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063403753100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe4ab7ff-43f6-4b86-b880-cc2e05b16185", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063403783300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "858837d4-ad81-403a-851f-6499c343c5bf", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063403816500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1d723fb-58b0-4d24-b50a-bd7ce8a7110f", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063405350600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0b64db0-09c6-4c24-9771-ca1930bf6040", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063405702600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f077367f-f623-4c80-a909-91983eb4873c", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063405949600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a82d7c6e-8d20-4cd8-90c8-07d51febd6b7", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063406023100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c799257b-9e03-4205-a5e3-76336d04d96e", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063406065500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1c5df2e-95ef-49fc-aa84-f723aef0c1b8", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063406111400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd6aa1ec-b8b1-4076-bc2a-0ca81b97c24c", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063406147700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c914315-baef-4426-ab7e-49996fcd4a7b", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063406182600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fcd640c-d48f-4d97-94f0-e4af4abf6b87", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063410215100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19b05c41-02e7-41af-881e-ae42811c28d0", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063411150700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55fdf601-e426-42a5-80f5-0b892e57b70c", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063411782800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f201b5e0-5cef-4013-8083-350eed62d4c1", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063412075700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddae45d8-e182-442e-894a-b9c35710e27e", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063412316500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b786f59-36ae-4901-8640-e1122994c463", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063412976400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd25c8c8-1dec-4c94-b5cf-1d69eaf0301e", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063421076200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af7b4939-5d8b-423c-b924-4a93e0231073", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063421425000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2beeee4-6cb6-4672-845d-d09d7a2a2074", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063421885100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be8b69c9-a9d8-4948-bfc8-052c237b84a9", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063423317700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6301a92d-5025-4257-b0f2-1d1a598d1b34", "name": "Incremental task entry:default@CompileArkTS post-execution cost:18 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063424041600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1553efa-9a9c-45bb-bbfd-417d72c50ed5", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063426913100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81ba76d8-319b-465d-90a4-20de0d460016", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063427937500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edb66a4e-a6c5-43ce-8a24-a65e0f4e0e1a", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063428414600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63e1ea79-ce0f-40a9-906a-a7e7a706da82", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063428696800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f827f66-ac32-491a-9812-7822c03b5e37", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063428910500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f40ea4cf-f4f6-4704-8ba6-ed0e519bdf66", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063429574200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d947b134-825a-42a1-bbe5-bb1d82f69364", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063430457200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fcbd70d-35af-4f1c-9a5c-941a9e953ac5", "name": "Incremental task entry:default@BuildJS post-execution cost:7 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063430745400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8ebbe33-51c9-4b97-b077-bc7c6b6dc31c", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063430813300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "302a875f-4df8-461f-933e-0b57f4d2f0e4", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063430850300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be629bf7-6bb7-4cf7-84a2-8dd9686d34b7", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063432212600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b587366c-351d-4953-9970-0560471b0ada", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063432691500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "348119e6-8227-41cf-a6db-bd6ca9f4283f", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063432956200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f33a746-a20a-405b-aa52-d7196dedc713", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063441729100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2eaacf0-350d-4564-b0ba-8d4e14a9ad1a", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063442655500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cfa69b1-456c-40e3-8990-cbc4eab5fc73", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063443388700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee9405ce-1da5-49aa-bb68-12b94be23a26", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063443772700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ba6316e-8394-424f-b393-60c73b86d0ee", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063443852100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53334fe6-88eb-4786-8bd1-8e19ea99723a", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063444084100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69efe7ba-6086-4260-b67f-ca46ed4b3e55", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063444324400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c7f1c2c-dc45-47e3-8509-16805de02f05", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063445354900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbe12264-7ad7-440b-91eb-a5227fabacbf", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063445632000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99dd3482-1a14-46d4-af56-26ff985e5f06", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063445872000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fbd5401-7914-40bd-93b1-0b73ba22416b", "name": "Incremental task entry:default@PackageHap post-execution cost:16 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063446145800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2090ab96-4fce-404c-94be-5a2984a375c3", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.cer cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063446388500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9adb682a-b6ac-40cf-954e-faa1d94ae0c6", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p7b cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063446643500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0793e8d0-cffa-45a7-ac03-5fbce48f376c", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p12 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063446859400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26095213-246a-430f-975d-a851cef19f56", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063447105900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b893ca8-d5b7-42f3-a1bf-f29c08a062e9", "name": "Update task entry:default@SignHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063447175700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a913d372-464e-4469-8f20-da3b45012c6e", "name": "Incremental task entry:default@SignHap post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063447419200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ac591c2-a9f5-472a-aa14-77fa37fb18aa", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063450961200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06791cfb-a412-4be5-80e4-d15d063cfc43", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063451617300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54afeb4a-7b21-4183-afd9-5f69d7759c4b", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063452331300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13439733-a3d5-4974-8337-bccb30863466", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063452733400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}