import { EllipsisOutlined } from '@ant-design/icons';
import { GridContent } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Col, Dropdown, Row, DatePicker } from 'antd';
import type { RangePickerProps } from 'antd/es/date-picker';
import type { RadioChangeEvent } from 'antd/es/radio';
import type dayjs from 'dayjs';
import type { FC } from 'react';
import { Suspense, useState, useEffect } from 'react';
import IntroduceRow from './components/IntroduceRow';
import OfflineData from './components/OfflineData';
import PageLoading from './components/PageLoading';
import ProportionSales from './components/ProportionSales';
import type { TimeType } from './components/SalesCard';
import SalesCard from './components/SalesCard';
import TopSearch from './components/TopSearch';
import type { AnalysisData } from './data.d';
import { getSummaryData, getTrendData, getCategoriesData, getPopularApps } from './service';
import useStyles from './style.style';
import { getTimeDistance } from './utils/utils';

type RangePickerValue = RangePickerProps['value'];

type AnalysisProps = {
  dashboardAndanalysis: AnalysisData;
  loading: boolean;
};

type SalesType = 'all' | 'online';

const Analysis: FC<AnalysisProps> = () => {
  const { styles } = useStyles();
  const [salesType, setSalesType] = useState<SalesType>('all');
  const [currentTabKey, setCurrentTabKey] = useState<string>('');
  const [rangePickerValue, setRangePickerValue] = useState<RangePickerValue>(
    getTimeDistance('year'),
  );
  const [analyticsData, setAnalyticsData] = useState<any>({});
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const [summaryResult, trendResult, categoriesResult, appsResult] = await Promise.all([
          getSummaryData(),
          getTrendData(),
          getCategoriesData(),
          getPopularApps(),
        ]);
        
        // 处理分类数据
        const categoryData = categoriesResult.data || [];
        const salesTypeData = categoryData.map((item: any) => ({
          x: item.category_name,
          y: item.app_count,
        }));
        
        // 处理下载量数据为分类维度
        const downloadTypeData = categoryData.map((item: any) => ({
          x: item.category_name,
          y: item.download_count,
        }));
        
        // 合并summary和trend数据
        const combinedSummaryData = {
          ...summaryResult.data,
          ...trendResult.data,
        };
        
        // 设置应用数据
        setAnalyticsData({
          // 摘要数据
          summaryData: combinedSummaryData,
          
          // 趋势数据
          visitData: trendResult.data?.user_trend || [],
          salesData: trendResult.data?.download_trend || [],
          
          // 热门应用数据 - 直接使用API返回的数据结构
          searchData: appsResult.data || [],
          
          // 分类数据
          offlineData: categoryData || [],
          salesTypeData: salesTypeData || [],
          downloadTypeData: downloadTypeData || [],
        });
      } catch (error) {
        console.error('获取分析数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);
  
  const selectDate = (type: TimeType) => {
    setRangePickerValue(getTimeDistance(type));
  };
  const handleRangePickerChange = (value: RangePickerValue) => {
    setRangePickerValue(value);
  };
  const isActive = (type: TimeType) => {
    if (!rangePickerValue) {
      return '';
    }
    const value = getTimeDistance(type);
    if (!value) {
      return '';
    }
    if (!rangePickerValue[0] || !rangePickerValue[1]) {
      return '';
    }
    if (
      rangePickerValue[0].isSame(value[0] as dayjs.Dayjs, 'day') &&
      rangePickerValue[1].isSame(value[1] as dayjs.Dayjs, 'day')
    ) {
      return styles.currentDate;
    }
    return '';
  };

  // 根据选择的类型切换数据
  let salesPieData = salesType === 'all' 
    ? analyticsData.salesTypeData || [] 
    : analyticsData.downloadTypeData || [];

  const dropdownGroup = (
    <span className={styles.iconGroup}>
      <Dropdown
        menu={{
          items: [
            {
              key: '1',
              label: '导出数据',
            },
            {
              key: '2',
              label: '刷新',
            },
          ],
        }}
        placement="bottomRight"
      >
        <EllipsisOutlined />
      </Dropdown>
    </span>
  );
  const handleChangeSalesType = (e: RadioChangeEvent) => {
    setSalesType(e.target.value);
  };
  const handleTabChange = (key: string) => {
    setCurrentTabKey(key);
  };
  const activeKey = currentTabKey || (analyticsData?.offlineData?.[0] && analyticsData?.offlineData[0].category_name) || '';
  return (
    <GridContent>
      <>
        <Suspense fallback={<PageLoading />}>
          <IntroduceRow loading={loading} summaryData={analyticsData.summaryData} />
        </Suspense>

        <Suspense fallback={null}>
          <SalesCard
            rangePickerValue={rangePickerValue}
            salesData={analyticsData?.salesData || []}
            userData={analyticsData?.visitData || []}
            isActive={isActive}
            handleRangePickerChange={handleRangePickerChange}
            loading={loading}
            selectDate={selectDate}
          />
        </Suspense>

        <Row
          gutter={24}
          style={{
            marginTop: 24,
          }}
        >
          <Col xl={12} lg={24} md={24} sm={24} xs={24}>
            <Suspense fallback={null}>
              <TopSearch
                loading={loading}
                visitData2={analyticsData?.visitData || []}
                searchData={analyticsData?.searchData || []}
                dropdownGroup={dropdownGroup}
              />
            </Suspense>
          </Col>
          <Col xl={12} lg={24} md={24} sm={24} xs={24}>
            <Suspense fallback={null}>
              <ProportionSales
                dropdownGroup={dropdownGroup}
                salesType={salesType}
                loading={loading}
                salesPieData={salesPieData || []}
                handleChangeSalesType={handleChangeSalesType}
              />
            </Suspense>
          </Col>
        </Row>

        <Suspense fallback={null}>
          <OfflineData
            activeKey={activeKey}
            loading={loading}
            offlineData={analyticsData?.offlineData || []}
            offlineChartData={analyticsData?.offlineChartData || []}
            handleTabChange={handleTabChange}
          />
        </Suspense>
      </>
    </GridContent>
  );
};

export default Analysis;
