import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';
import { ApiService } from '../services/ApiService';
import { LoadingView, LoadingState } from '../components/LoadingView';
import { router } from '@kit.ArkUI';
import { hilog } from '@kit.PerformanceAnalysisKit';'@ohos.router';

/**
 * 评论模型
 */
interface ReviewModel {
  id: number;
  appId: number;
  appName: string;
  appIcon: string;
  rating: number;
  content: string;
  createTime: string;
  updateTime?: string;
  likeCount: number;
  replyCount: number;
  isLiked: boolean;
}

/**
 * 我的评论页面
 */
@Entry
@Component
struct MyReviewsPage {
  @State reviews: ReviewModel[] = [];
  @State loadingState: LoadingState = LoadingState.LOADING;
  @State selectedTab: number = 0; // 0: 全部, 1: 最新, 2: 最热

  private deviceUtils = DeviceUtils.getInstance();
  private apiService = ApiService.getInstance();

  aboutToAppear() {
    this.loadMyReviews();
  }

  /**
   * 加载我的评论
   */
  private async loadMyReviews() {
    try {
      this.loadingState = LoadingState.LOADING;
      // 这里应该调用API获取用户的评论
      // 暂时使用模拟数据
      await this.simulateApiCall();
      this.reviews = this.getMockReviews();
      this.loadingState = LoadingState.SUCCESS;
    } catch (error) {
      hilog.error(0x0000, 'MyReviewsPage', '加载评论失败: %{public}s', JSON.stringify(error));
      this.loadingState = LoadingState.ERROR;
    }
  }

  /**
   * 模拟API调用
   */
  private async simulateApiCall(): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 1000);
    });
  }

  /**
   * 获取模拟评论数据
   */
  private getMockReviews(): ReviewModel[] {
    return [
      {
        id: 1,
        appId: 1,
        appName: '微信',
        appIcon: Constants.PLACEHOLDER_IMAGE,
        rating: 5,
        content: '非常好用的社交软件，界面简洁，功能强大，是日常生活中不可缺少的应用。',
        createTime: '2024-01-20 14:30:00',
        likeCount: 12,
        replyCount: 3,
        isLiked: false
      },
      {
        id: 2,
        appId: 2,
        appName: '支付宝',
        appIcon: Constants.PLACEHOLDER_IMAGE,
        rating: 4,
        content: '支付很方便，但是启动速度有点慢，希望能优化一下性能。',
        createTime: '2024-01-18 09:15:00',
        updateTime: '2024-01-18 10:20:00',
        likeCount: 8,
        replyCount: 1,
        isLiked: true
      },
      {
        id: 3,
        appId: 3,
        appName: '王者荣耀',
        appIcon: Constants.PLACEHOLDER_IMAGE,
        rating: 3,
        content: '游戏画质不错，但是匹配机制需要改进，经常遇到实力差距很大的队友。',
        createTime: '2024-01-15 20:45:00',
        likeCount: 25,
        replyCount: 7,
        isLiked: false
      },
      {
        id: 4,
        appId: 4,
        appName: '抖音',
        appIcon: Constants.PLACEHOLDER_IMAGE,
        rating: 5,
        content: '内容丰富多彩，推荐算法很精准，总能刷到感兴趣的视频。',
        createTime: '2024-01-12 16:20:00',
        likeCount: 18,
        replyCount: 2,
        isLiked: true
      }
    ];
  }

  /**
   * 获取排序后的评论列表
   */
  private getSortedReviews(): ReviewModel[] {
    let sortedReviews = [...this.reviews];
    
    switch (this.selectedTab) {
      case 1: // 最新
        sortedReviews.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime());
        break;
      case 2: // 最热
        sortedReviews.sort((a, b) => (b.likeCount + b.replyCount) - (a.likeCount + a.replyCount));
        break;
      default: // 全部
        break;
    }
    
    return sortedReviews;
  }

  /**
   * 跳转到应用详情页面
   */
  private navigateToAppDetail(review: ReviewModel) {
    this.getUIContext().getRouter().pushUrl({
      url: 'pages/AppDetailPage',
      params: { appId: review.appId }
    });
  }

  /**
   * 点赞/取消点赞
   */
  private async toggleLike(review: ReviewModel) {
    try {
      // 这里应该调用API进行点赞操作
      // 暂时直接修改本地状态
      const index = this.reviews.findIndex(item => item.id === review.id);
      if (index !== -1) {
        this.reviews[index].isLiked = !this.reviews[index].isLiked;
        this.reviews[index].likeCount += this.reviews[index].isLiked ? 1 : -1;
      }
    } catch (error) {
      hilog.error(0x0000, 'MyReviewsPage', '点赞操作失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 删除评论
   */
  private async deleteReview(review: ReviewModel) {
    try {
      // 这里应该调用API删除评论
      // 暂时直接从列表中移除
      const index = this.reviews.findIndex(item => item.id === review.id);
      if (index !== -1) {
        this.reviews.splice(index, 1);
      }
    } catch (error) {
      hilog.error(0x0000, 'MyReviewsPage', '删除评论失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 标签栏
   */
  @Builder
  private TabBar() {
    Row() {
      ForEach(['全部', '最新', '最热'], (title: string, index: number) => {
        Text(title)
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontColor(this.selectedTab === index ? Constants.COLORS.PRIMARY : Constants.COLORS.TEXT_SECONDARY)
          .fontWeight(this.selectedTab === index ? FontWeight.Bold : FontWeight.Normal)
          .padding({ left: 16, right: 16, top: 12, bottom: 12 })
          .onClick(() => {
            this.selectedTab = index;
          })
      }, (title: string) => title)
    }
    .width('100%')
    .backgroundColor(Constants.COLORS.WHITE)
    .justifyContent(FlexAlign.SpaceAround)
  }

  /**
   * 星级评分
   */
  @Builder
  private StarRating(rating: number) {
    Row({ space: 2 }) {
      ForEach([1, 2, 3, 4, 5], (i: number) => {
        Text(i <= rating ? '⭐' : '☆')
          .fontSize(14)
          .fontColor(i <= rating ? $r('app.color.star_active') : Constants.COLORS.TEXT_HINT)
      }, (i: number) => `star_${i}`)
    }
  }

  /**
   * 评论项
   */
  @Builder
  private ReviewItem(review: ReviewModel) {
    Column({ space: 12 }) {
      // 应用信息
      Row({ space: 12 }) {
        Image(review.appIcon)
          .width(40)
          .height(40)
          .borderRadius(Constants.BORDER_RADIUS.SMALL)
          .objectFit(ImageFit.Cover)
          .backgroundColor(Constants.COLORS.BACKGROUND_LIGHT)

        Column({ space: 4 }) {
          Text(review.appName)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .fontWeight(FontWeight.Medium)

          this.StarRating(review.rating)
        }
        .alignItems(HorizontalAlign.Start)
        .layoutWeight(1)

        Text('🗑️')
          .fontSize(16)
          .fontColor(Constants.COLORS.TEXT_HINT)
          .padding(8)
          .onClick(() => {
            this.deleteReview(review);
          })
      }
      .width('100%')
      .onClick(() => this.navigateToAppDetail(review))

      // 评论内容
      Text(review.content)
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
        .fontColor(Constants.COLORS.TEXT_PRIMARY)
        .lineHeight(20)
        .width('100%')

      // 时间和互动信息
      Row() {
        Text(review.createTime.split(' ')[0])
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.TEXT_HINT)

        if (review.updateTime && review.updateTime !== review.createTime) {
          Text('(已编辑)')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.TEXT_HINT)
        }

        Blank()

        Row({ space: 16 }) {
          Row({ space: 4 }) {
            Text(review.isLiked ? '👍' : '👍🏻')
              .fontSize(14)
              .onClick(() => {
                this.toggleLike(review);
              })
            Text(review.likeCount.toString())
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
              .fontColor(Constants.COLORS.TEXT_HINT)
          }

          Row({ space: 4 }) {
            Text('💬')
              .fontSize(14)
            Text(review.replyCount.toString())
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
              .fontColor(Constants.COLORS.TEXT_HINT)
          }
        }
      }
      .width('100%')
    }
    .width('100%')
    .padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
    .backgroundColor(Constants.COLORS.WHITE)
    .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
    .margin({ left: 16, right: 16, bottom: 8 })
  }

  /**
   * 评论列表
   */
  @Builder
  private ReviewList() {
    if (this.getSortedReviews().length === 0) {
      Column({ space: 16 }) {
        Text('💬')
          .fontSize(48)
          .fontColor(Constants.COLORS.TEXT_HINT)
        
        Text('暂无评论记录')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
        
        Text('去应用商店体验应用并发表评论')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.TEXT_HINT)
      }
      .width('100%')
      .height(200)
      .justifyContent(FlexAlign.Center)
      .alignItems(HorizontalAlign.Center)
    } else {
      Column() {
        ForEach(this.getSortedReviews(), (review: ReviewModel) => {
          this.ReviewItem(review)
        }, (review: ReviewModel) => review.id.toString())
      }
      .width('100%')
    }
  }

  build() {
    Column() {
      // 标题栏
      Row() {
        Text('←')
          .fontSize(24)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .onClick(() => {
            this.getUIContext().getRouter().back();
          })

        Text('我的评论')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .fontWeight(FontWeight.Bold)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('')
          .width(24)
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor(Constants.COLORS.WHITE)
      .alignItems(VerticalAlign.Center)

      if (this.loadingState === LoadingState.LOADING) {
        LoadingView({ state: LoadingState.LOADING })
          .layoutWeight(1)
      } else if (this.loadingState === LoadingState.ERROR) {
        LoadingView({ 
          state: LoadingState.ERROR,
          onRetry: (): Promise<void> => this.loadMyReviews()
        })
          .layoutWeight(1)
      } else {
        Column() {
          // 标签栏
          this.TabBar()
          
          Divider()
            .color(Constants.COLORS.BORDER)

          // 评论列表
          Scroll() {
            Column() {
              this.ReviewList()
              
              // 底部间距
              Column()
                .height(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE))
            }
          }
          .scrollable(ScrollDirection.Vertical)
          .scrollBar(BarState.Auto)
          .layoutWeight(1)
        }
        .layoutWeight(1)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Constants.COLORS.BACKGROUND)
  }
}

export { MyReviewsPage };