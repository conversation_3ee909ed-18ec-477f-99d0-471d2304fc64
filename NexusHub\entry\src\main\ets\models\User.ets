/**
 * 用户模型
 */
export interface UserModel {
  id: number;
  username: string;
  email: string;
  phone: string;
  role: string;
  status: string;
  avatar: string;
  created_at: string;
  updated_at: string;
  last_login_at: string;
  login_count: number;
  is_developer: boolean;
  developer_name: string;
  company_name: string;
  website: string;
  description: string;
  contact_email: string;
  contact_phone: string;
  business_license: string;
  identity_card: string;
  developer_avatar: string;
  developer_address: string;
  submitted_at: string;
  verified_at?: string;
  verify_reason: string;
  verify_status: string;
}

/**
 * 登录请求模型
 */
export interface LoginRequest {
  username_or_email: string;
  password: string;
}

/**
 * 登录响应数据模型
 */
export interface LoginResponseData {
  token: string;
  user: UserModel;
}

/**
 * 登录响应模型
 */
export interface LoginResponse {
  code: number;
  message: string;
  data: LoginResponseData;
}

/**
 * 注册请求模型
 */
export interface RegisterRequest {
  username: string;
  email: string;
  phone?: string;
  password: string;
  country?: string;
  province?: string;
  city?: string;
  district?: string;
  street?: string;
}

/**
 * 注册响应模型
 */
export interface RegisterResponse {
  code: number;
  message: string;
  data: null;
}

/**
 * 用户资料响应模型
 */
export interface UserProfileResponse {
  code: number;
  message: string;
  data: UserModel;
}

/**
 * 开发者信息
 */
export interface DeveloperInfo {
  developer_name: string;
  company_name?: string;
  website?: string;
  description?: string;
}

/**
 * 更新用户资料请求模型
 */
export interface UpdateProfileRequest {
  username?: string;
  email?: string;
  phone?: string;
  avatar?: string;
  old_password?: string;
  new_password?: string;
  is_developer?: boolean;
  developer_info?: DeveloperInfo;
}

/**
 * 用户角色枚举
 */
export enum UserRole {
  USER = 'user',
  DEVELOPER = 'developer',
  OPERATOR = 'operator',
  REVIEWER = 'reviewer',
  ADMIN = 'admin'
}

/**
 * 用户状态枚举
 */
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  BANNED = 'banned'
}

/**
 * 开发者验证状态枚举
 */
export enum DeveloperVerifyStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

/**
 * 通知设置
 */
export interface NotificationSettings {
  app_updates: boolean;
  new_releases: boolean;
  promotions: boolean;
  security_alerts: boolean;
}

/**
 * 隐私设置
 */
export interface PrivacySettings {
  show_download_history: boolean;
  show_review_history: boolean;
  allow_recommendations: boolean;
}

/**
 * 下载设置
 */
export interface DownloadSettings {
  auto_update: boolean;
  wifi_only: boolean;
  parallel_downloads: number;
}

/**
 * 用户偏好设置
 */
export interface UserPreferences {
  language: string;
  theme: string;
  notifications: NotificationSettings;
  privacy: PrivacySettings;
  download: DownloadSettings;
}

/**
 * 用户统计信息
 */
export interface UserStats {
  total_downloads: number;
  total_reviews: number;
  average_rating_given: number;
  favorite_categories: string[];
  last_active: string;
  account_age_days: number;
}