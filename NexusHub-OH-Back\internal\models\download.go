package models

import (
	"time"

	"gorm.io/gorm"
)

// DownloadRecord 下载记录模型
type DownloadRecord struct {
	ID          uint      `gorm:"primarykey" json:"id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   *time.Time `gorm:"index" json:"deleted_at,omitempty"`
	UserID        uint   `gorm:"index" json:"user_id"`
	ApplicationID uint   `gorm:"index" json:"application_id"`
	AppVersionID  uint   `gorm:"index" json:"app_version_id"`
	VersionName   string `gorm:"type:varchar(50)" json:"version_name"`
	IP            string `gorm:"type:varchar(50)" json:"ip"`
	UserAgent     string `gorm:"type:varchar(255)" json:"user_agent"`
	DeviceType    string `gorm:"type:varchar(50)" json:"device_type"`
	DeviceOS      string `gorm:"type:varchar(50)" json:"device_os"`
	DeviceModel   string `gorm:"type:varchar(100)" json:"device_model"`
	Status        string `gorm:"type:varchar(20);default:'success'" json:"status"` // success, failed
}

// TableName 指定表名
func (DownloadRecord) TableName() string {
	return "download_records"
}

// CreateDownloadRecord 创建下载记录
func CreateDownloadRecord(db *gorm.DB, record *DownloadRecord) error {
	return db.Transaction(func(tx *gorm.DB) error {
		// 保存下载记录
		if err := tx.Create(record).Error; err != nil {
			return err
		}

		// 更新应用下载次数
		if err := tx.Model(&Application{}).Where("id = ?", record.ApplicationID).
			UpdateColumn("download_count", gorm.Expr("download_count + 1")).Error; err != nil {
			return err
		}

		// 更新版本下载次数
		return tx.Model(&AppVersion{}).Where("id = ?", record.AppVersionID).
			UpdateColumn("download_count", gorm.Expr("download_count + 1")).Error
	})
}

// GetUserDownloadRecords 获取用户下载记录
func GetUserDownloadRecords(db *gorm.DB, userID uint, page, pageSize int) ([]DownloadRecord, int64, error) {
	var records []DownloadRecord
	var count int64

	offset := (page - 1) * pageSize

	query := db.Model(&DownloadRecord{}).Where("user_id = ?", userID)

	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	if err := query.Order("created_at desc").Offset(offset).Limit(pageSize).Find(&records).Error; err != nil {
		return nil, 0, err
	}

	return records, count, nil
}

// GetAppDownloadStats 获取应用下载统计
type AppDownloadStats struct {
	Date      string `json:"date"`
	Downloads int64  `json:"downloads"`
}

// GetDailyDownloadStats 获取每日下载统计
func GetDailyDownloadStats(db *gorm.DB, appID uint, days int) ([]AppDownloadStats, error) {
	var stats []AppDownloadStats

	// 根据数据库类型选择适当的日期格式化方法
	// 以下示例适用于PostgreSQL
	query := `
		SELECT 
			TO_CHAR(created_at, 'YYYY-MM-DD') as date,
			COUNT(*) as downloads
		FROM 
			download_records
		WHERE 
			application_id = ?
			AND created_at >= NOW() - INTERVAL '? days'
		GROUP BY 
			date
		ORDER BY 
			date DESC
	`

	if err := db.Raw(query, appID, days).Scan(&stats).Error; err != nil {
		return nil, err
	}

	return stats, nil
}

// GetDownloadsByDevice 获取设备下载统计
type DeviceDownloadStats struct {
	DeviceType string `json:"device_type"`
	Downloads  int64  `json:"downloads"`
}

// GetDeviceTypeStats 获取设备类型统计
func GetDeviceTypeStats(db *gorm.DB, appID uint) ([]DeviceDownloadStats, error) {
	var stats []DeviceDownloadStats

	query := `
		SELECT 
			device_type,
			COUNT(*) as downloads
		FROM 
			download_records
		WHERE 
			application_id = ?
		GROUP BY 
			device_type
		ORDER BY 
			downloads DESC
	`

	if err := db.Raw(query, appID).Scan(&stats).Error; err != nil {
		return nil, err
	}

	return stats, nil
}
