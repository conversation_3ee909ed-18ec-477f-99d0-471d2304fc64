{"version": "2.0", "ppid": 32784, "events": [{"head": {"id": "e3e3fb4d-b50e-44ca-b3fb-52801741714b", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724653177300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "394fd9cd-6f23-4929-997e-f9a38e7ad261", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724653381000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab5bbe37-ca08-4332-b65b-5de9d7754723", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724653606100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d28ecda-2587-4407-9ff9-0921403c08d2", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724655152600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efe16749-9792-49bb-8795-1b11313fd289", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724655550600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd7d4d75-9b8e-44d4-920d-c6ba898d5739", "name": "worker[18] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724659657400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15164e29-3861-428a-8708-dcc6fa8369ec", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724661795200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10de8219-0839-4076-9d43-883dd525ef40", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724662159700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80b1b10e-ae07-44e4-b94c-c402fbbcb1c7", "name": "worker[4] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724704060400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "540f0504-a04d-4604-9f42-21727a4db3d8", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785872494100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10f3d8e8-2ee8-4171-b787-d335f55861ab", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785890701700, "endTime": 219786396528900}, "additional": {"children": ["6f3a8409-a182-48f4-acbf-8585328623d8", "a76109e3-1afc-4418-8707-a7e75ea2e4c4", "f439afae-5af4-4c5f-929d-ff6299cc2f1c", "699c7e26-f6f0-49b2-b153-92f620625322", "df363a90-5687-4395-8378-821b5f30ecc3", "29e31a64-58ec-473d-a665-fd0198808d93", "57240eff-7468-4e8e-ac0e-8b0b29ce2b62"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "86457560-8d4b-4057-adf1-3ec84a08f906"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f3a8409-a182-48f4-acbf-8585328623d8", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785890705700, "endTime": 219785924649600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "10f3d8e8-2ee8-4171-b787-d335f55861ab", "logId": "db22c633-5bf0-4750-935c-3bc5bd68229c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a76109e3-1afc-4418-8707-a7e75ea2e4c4", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785924675000, "endTime": 219786393535600}, "additional": {"children": ["f185d6f0-2f62-4093-b01e-98889bbf4f49", "448f2705-8b09-4b30-8103-6bf319538110", "7b8e7aa0-706a-4443-9809-835cd434ea73", "f866a901-b585-43e2-ba12-8954c4ca7d23", "f19096a0-80ce-4b78-b03b-965b07abb109", "6fb62c39-fbf8-4c35-9707-296a9f744419", "3abc4e0a-dc3d-4b16-9a1f-592896059ca6", "9b08a419-aa80-44dc-bbc9-1e3f6405733f", "aff990ec-3fd2-4f4b-b8b1-8cc20805c9c0"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "10f3d8e8-2ee8-4171-b787-d335f55861ab", "logId": "96339b83-f378-497f-a754-232508bade89"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f439afae-5af4-4c5f-929d-ff6299cc2f1c", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786393592000, "endTime": 219786396478700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "10f3d8e8-2ee8-4171-b787-d335f55861ab", "logId": "8e9139c4-57a3-4dbd-9e21-c8eac12fa444"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "699c7e26-f6f0-49b2-b153-92f620625322", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786396493500, "endTime": 219786396515000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "10f3d8e8-2ee8-4171-b787-d335f55861ab", "logId": "8ea3ce3b-e305-487e-8618-ae2d9acabd82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df363a90-5687-4395-8378-821b5f30ecc3", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785898992500, "endTime": 219785899064900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "10f3d8e8-2ee8-4171-b787-d335f55861ab", "logId": "1949417d-8a6b-4b2e-a5c5-fa6496dcabf5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1949417d-8a6b-4b2e-a5c5-fa6496dcabf5", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785898992500, "endTime": 219785899064900}, "additional": {"logType": "info", "children": [], "durationId": "df363a90-5687-4395-8378-821b5f30ecc3", "parent": "86457560-8d4b-4057-adf1-3ec84a08f906"}}, {"head": {"id": "29e31a64-58ec-473d-a665-fd0198808d93", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785916030700, "endTime": 219785916092300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "10f3d8e8-2ee8-4171-b787-d335f55861ab", "logId": "05e8d394-fdd6-45c2-a804-7ada9292f3ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "05e8d394-fdd6-45c2-a804-7ada9292f3ba", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785916030700, "endTime": 219785916092300}, "additional": {"logType": "info", "children": [], "durationId": "29e31a64-58ec-473d-a665-fd0198808d93", "parent": "86457560-8d4b-4057-adf1-3ec84a08f906"}}, {"head": {"id": "5529f042-4493-481e-ba0a-b54eeab9e5a2", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785916202100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "897f84ec-c447-477c-b20d-2f2ea2e7ed87", "name": "Cache service initialization finished in 8 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785924456600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db22c633-5bf0-4750-935c-3bc5bd68229c", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785890705700, "endTime": 219785924649600}, "additional": {"logType": "info", "children": [], "durationId": "6f3a8409-a182-48f4-acbf-8585328623d8", "parent": "86457560-8d4b-4057-adf1-3ec84a08f906"}}, {"head": {"id": "f185d6f0-2f62-4093-b01e-98889bbf4f49", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785937014900, "endTime": 219785937036800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a76109e3-1afc-4418-8707-a7e75ea2e4c4", "logId": "3880f19c-8738-4bac-af17-952e9f57ad5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "448f2705-8b09-4b30-8103-6bf319538110", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785937069400, "endTime": 219785948777100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a76109e3-1afc-4418-8707-a7e75ea2e4c4", "logId": "5539af2a-aea1-4490-b295-d7d1fe6a09b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b8e7aa0-706a-4443-9809-835cd434ea73", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785948807000, "endTime": 219786146930900}, "additional": {"children": ["9d702402-2547-49eb-9bf3-c065ad9c27d9", "5daeccf3-ca06-4236-b2a4-b059b3e2e197", "fb4cf7cd-cc53-4745-940d-0900090686da"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a76109e3-1afc-4418-8707-a7e75ea2e4c4", "logId": "d2c719f0-f4cf-4586-b66b-e8123acda5a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f866a901-b585-43e2-ba12-8954c4ca7d23", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786146961700, "endTime": 219786216700200}, "additional": {"children": ["a99fe8a0-750c-42d7-837d-e61ca74ade8c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a76109e3-1afc-4418-8707-a7e75ea2e4c4", "logId": "df115dcf-1e2d-41ff-8507-2d1485b52353"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f19096a0-80ce-4b78-b03b-965b07abb109", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786216715100, "endTime": 219786327387200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a76109e3-1afc-4418-8707-a7e75ea2e4c4", "logId": "279a3673-cd2f-4ab0-9bea-dde8bd9789c7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6fb62c39-fbf8-4c35-9707-296a9f744419", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786330598000, "endTime": 219786356666100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a76109e3-1afc-4418-8707-a7e75ea2e4c4", "logId": "e4ec5c39-c50b-4a76-83a7-03f9dddc3558"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3abc4e0a-dc3d-4b16-9a1f-592896059ca6", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786356737100, "endTime": 219786393185900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a76109e3-1afc-4418-8707-a7e75ea2e4c4", "logId": "02e548fb-9e88-4dea-a052-b7001e8af3a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b08a419-aa80-44dc-bbc9-1e3f6405733f", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786393234300, "endTime": 219786393504600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a76109e3-1afc-4418-8707-a7e75ea2e4c4", "logId": "baa3877d-1182-4311-a3ec-e6579ad27f9a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3880f19c-8738-4bac-af17-952e9f57ad5f", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785937014900, "endTime": 219785937036800}, "additional": {"logType": "info", "children": [], "durationId": "f185d6f0-2f62-4093-b01e-98889bbf4f49", "parent": "96339b83-f378-497f-a754-232508bade89"}}, {"head": {"id": "5539af2a-aea1-4490-b295-d7d1fe6a09b6", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785937069400, "endTime": 219785948777100}, "additional": {"logType": "info", "children": [], "durationId": "448f2705-8b09-4b30-8103-6bf319538110", "parent": "96339b83-f378-497f-a754-232508bade89"}}, {"head": {"id": "9d702402-2547-49eb-9bf3-c065ad9c27d9", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785950755300, "endTime": 219785950795000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7b8e7aa0-706a-4443-9809-835cd434ea73", "logId": "ae90dc8c-6735-4523-9c7a-91d98fb3a636"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae90dc8c-6735-4523-9c7a-91d98fb3a636", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785950755300, "endTime": 219785950795000}, "additional": {"logType": "info", "children": [], "durationId": "9d702402-2547-49eb-9bf3-c065ad9c27d9", "parent": "d2c719f0-f4cf-4586-b66b-e8123acda5a3"}}, {"head": {"id": "5daeccf3-ca06-4236-b2a4-b059b3e2e197", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785956716200, "endTime": 219786144863300}, "additional": {"children": ["88d22dcb-3e1c-422d-b0b5-8b40690ad94c", "90a80948-8685-4d7a-8d2a-976f732189d1"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7b8e7aa0-706a-4443-9809-835cd434ea73", "logId": "4b45891a-604a-404f-b183-6363ecfce6d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88d22dcb-3e1c-422d-b0b5-8b40690ad94c", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785956720000, "endTime": 219785969860200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5daeccf3-ca06-4236-b2a4-b059b3e2e197", "logId": "703d6adb-cea6-4c3e-814d-bbaf5a0e458e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "90a80948-8685-4d7a-8d2a-976f732189d1", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785969899200, "endTime": 219786144836400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5daeccf3-ca06-4236-b2a4-b059b3e2e197", "logId": "2cd7e312-0b00-47bc-8826-63db5914e8d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1516c0c0-89b2-43db-889c-404b2e8846ed", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785956736900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4654e082-1a30-41ba-9ce5-bbf2c277deab", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785969580900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "703d6adb-cea6-4c3e-814d-bbaf5a0e458e", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785956720000, "endTime": 219785969860200}, "additional": {"logType": "info", "children": [], "durationId": "88d22dcb-3e1c-422d-b0b5-8b40690ad94c", "parent": "4b45891a-604a-404f-b183-6363ecfce6d9"}}, {"head": {"id": "e8ee4d39-a609-4496-9092-b8d12555c1d3", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785969929100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "429c23cd-5334-4399-aacf-7a9eb910e646", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785993096600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c44c2553-16d9-47c0-b702-3a2471026748", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785993331600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a195b1e2-2ae9-4e96-8557-dc59fb3c6e74", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785993567900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9557b777-c6ef-4d47-a1b5-f26f3dca8f1c", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785993781200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "551b9c86-2ffe-4f52-a769-d0cc5e8567fa", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785998570700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "263a5840-f92d-4c2e-ad51-bc26a95843f6", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786030656100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c0aaa33-ec06-4003-98a6-ffc4c39f2cb0", "name": "Sdk init in 68 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786081079100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "119d4213-848d-432c-a792-7dd3c6749db2", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786081408700}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 19, "minute": 0, "second": 15}, "markType": "other"}}, {"head": {"id": "e47aed67-847d-4d4d-a1d8-b602aece9a11", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786081445000}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 19, "minute": 0, "second": 15}, "markType": "other"}}, {"head": {"id": "741626f6-244c-4af4-8fc3-acb998f3c1e8", "name": "Project task initialization takes 60 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786144299300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a43d56f2-4f77-4100-8e68-9d0cbe85adf0", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786144573000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92d61322-162f-4f41-89df-7b023ba6aad8", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786144689400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b874429-25ba-4122-953b-b672e275999b", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786144767600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cd7e312-0b00-47bc-8826-63db5914e8d6", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785969899200, "endTime": 219786144836400}, "additional": {"logType": "info", "children": [], "durationId": "90a80948-8685-4d7a-8d2a-976f732189d1", "parent": "4b45891a-604a-404f-b183-6363ecfce6d9"}}, {"head": {"id": "4b45891a-604a-404f-b183-6363ecfce6d9", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785956716200, "endTime": 219786144863300}, "additional": {"logType": "info", "children": ["703d6adb-cea6-4c3e-814d-bbaf5a0e458e", "2cd7e312-0b00-47bc-8826-63db5914e8d6"], "durationId": "5daeccf3-ca06-4236-b2a4-b059b3e2e197", "parent": "d2c719f0-f4cf-4586-b66b-e8123acda5a3"}}, {"head": {"id": "fb4cf7cd-cc53-4745-940d-0900090686da", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786146853300, "endTime": 219786146890900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7b8e7aa0-706a-4443-9809-835cd434ea73", "logId": "cea3189b-985e-4a08-b180-6e2832b36373"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cea3189b-985e-4a08-b180-6e2832b36373", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786146853300, "endTime": 219786146890900}, "additional": {"logType": "info", "children": [], "durationId": "fb4cf7cd-cc53-4745-940d-0900090686da", "parent": "d2c719f0-f4cf-4586-b66b-e8123acda5a3"}}, {"head": {"id": "d2c719f0-f4cf-4586-b66b-e8123acda5a3", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785948807000, "endTime": 219786146930900}, "additional": {"logType": "info", "children": ["ae90dc8c-6735-4523-9c7a-91d98fb3a636", "4b45891a-604a-404f-b183-6363ecfce6d9", "cea3189b-985e-4a08-b180-6e2832b36373"], "durationId": "7b8e7aa0-706a-4443-9809-835cd434ea73", "parent": "96339b83-f378-497f-a754-232508bade89"}}, {"head": {"id": "a99fe8a0-750c-42d7-837d-e61ca74ade8c", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786148600200, "endTime": 219786216671800}, "additional": {"children": ["e7649acd-9c57-4e01-a5f8-8baac7d2e3f5", "b5cd9296-d40d-4401-9e39-5dc010575605", "fba93ce8-ec60-4c25-b3b7-0eaefdbecc7f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f866a901-b585-43e2-ba12-8954c4ca7d23", "logId": "4f553371-d8f3-49a8-a33a-a253f593ba1e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7649acd-9c57-4e01-a5f8-8baac7d2e3f5", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786158488400, "endTime": 219786158531200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a99fe8a0-750c-42d7-837d-e61ca74ade8c", "logId": "9d0eb115-be3b-45f4-8c2c-ee2a3cb0d18c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d0eb115-be3b-45f4-8c2c-ee2a3cb0d18c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786158488400, "endTime": 219786158531200}, "additional": {"logType": "info", "children": [], "durationId": "e7649acd-9c57-4e01-a5f8-8baac7d2e3f5", "parent": "4f553371-d8f3-49a8-a33a-a253f593ba1e"}}, {"head": {"id": "b5cd9296-d40d-4401-9e39-5dc010575605", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786163770000, "endTime": 219786213674500}, "additional": {"children": ["cbea43e4-5488-46e4-aae3-1cd23d4838cc", "a385dd37-36b4-4e24-b4c8-db58f54be86a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a99fe8a0-750c-42d7-837d-e61ca74ade8c", "logId": "cb6f0666-f4d2-4fc4-915d-d52240464bb8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cbea43e4-5488-46e4-aae3-1cd23d4838cc", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786163773200, "endTime": 219786176653200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b5cd9296-d40d-4401-9e39-5dc010575605", "logId": "cd229db8-7348-44a7-8f1c-93928e542967"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a385dd37-36b4-4e24-b4c8-db58f54be86a", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786176685900, "endTime": 219786213656600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b5cd9296-d40d-4401-9e39-5dc010575605", "logId": "cc91cf56-4403-4e1b-a75c-33f28ecbfd68"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "83442e76-bdf6-4493-81ae-4349ee301eca", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786163789100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a056dc3f-ef91-441f-8ebf-7d24bd4d6b4d", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786176408500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd229db8-7348-44a7-8f1c-93928e542967", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786163773200, "endTime": 219786176653200}, "additional": {"logType": "info", "children": [], "durationId": "cbea43e4-5488-46e4-aae3-1cd23d4838cc", "parent": "cb6f0666-f4d2-4fc4-915d-d52240464bb8"}}, {"head": {"id": "a61bf63b-0dc8-4edd-98f5-5e9e9346d952", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786176711800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fe02c6b-93ed-48a1-bfa5-53b89485380a", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786198606600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d139066-4ad5-45b9-bbc3-7d461c739188", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786198898600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb4c4501-05b8-4673-95b4-e99f24c6905d", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786199436800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9da16a42-556b-4b35-9c60-ae353b28e329", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786199724700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cebcf6a-ecc0-49c4-9e71-eeda360cc1d7", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786199847100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "356655a4-2fe5-4d0a-9ac7-bb88e1d3eba5", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786199940200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "889f87f7-b36b-4c8a-98f1-bce02df94080", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786200085200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb1a47c2-9b72-4be0-ac13-6d7913dab255", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786200196300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70607d83-2699-4991-a3a3-a44f6f0dda72", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786202064500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f8e0e02-eace-4b3b-976f-f374e03e2933", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786202872300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e3a96fc-bee9-4369-a37e-2d26373462ef", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786203680600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2fdcc43-f0f9-4ccb-84e0-8a59483a5e82", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786204024100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edb90f0e-ab16-46a4-8271-7aba46604ea2", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786204683400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38f48795-5458-46a0-b39c-7ac828df3592", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786205105500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f42fa2d5-f93f-4dac-aa90-ef8c31360f33", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786205514100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93a59cf6-723b-405f-ae3e-631300347cd2", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786205988300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66487be1-1cd2-400e-8c04-b62f4d938615", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786206136900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "971deebd-69b0-4aed-9e73-4c6fd8be89fb", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786206243800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "620b7695-a202-4ff6-9231-9d56ebdda9c6", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786206361100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cbecb2d-877f-4481-a587-2d104c2d58f5", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786212883400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ad0e31f-0316-4bd7-ac89-c95039da8d52", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786213268100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8423b2ef-3903-40ce-8cf3-67a339381b98", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786213483100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43161f72-d61f-4822-9a21-2b365af1bc1a", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786213590600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc91cf56-4403-4e1b-a75c-33f28ecbfd68", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786176685900, "endTime": 219786213656600}, "additional": {"logType": "info", "children": [], "durationId": "a385dd37-36b4-4e24-b4c8-db58f54be86a", "parent": "cb6f0666-f4d2-4fc4-915d-d52240464bb8"}}, {"head": {"id": "cb6f0666-f4d2-4fc4-915d-d52240464bb8", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786163770000, "endTime": 219786213674500}, "additional": {"logType": "info", "children": ["cd229db8-7348-44a7-8f1c-93928e542967", "cc91cf56-4403-4e1b-a75c-33f28ecbfd68"], "durationId": "b5cd9296-d40d-4401-9e39-5dc010575605", "parent": "4f553371-d8f3-49a8-a33a-a253f593ba1e"}}, {"head": {"id": "fba93ce8-ec60-4c25-b3b7-0eaefdbecc7f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786216602600, "endTime": 219786216635400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a99fe8a0-750c-42d7-837d-e61ca74ade8c", "logId": "12065196-23d9-40b1-9b43-be477e1e1782"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12065196-23d9-40b1-9b43-be477e1e1782", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786216602600, "endTime": 219786216635400}, "additional": {"logType": "info", "children": [], "durationId": "fba93ce8-ec60-4c25-b3b7-0eaefdbecc7f", "parent": "4f553371-d8f3-49a8-a33a-a253f593ba1e"}}, {"head": {"id": "4f553371-d8f3-49a8-a33a-a253f593ba1e", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786148600200, "endTime": 219786216671800}, "additional": {"logType": "info", "children": ["9d0eb115-be3b-45f4-8c2c-ee2a3cb0d18c", "cb6f0666-f4d2-4fc4-915d-d52240464bb8", "12065196-23d9-40b1-9b43-be477e1e1782"], "durationId": "a99fe8a0-750c-42d7-837d-e61ca74ade8c", "parent": "df115dcf-1e2d-41ff-8507-2d1485b52353"}}, {"head": {"id": "df115dcf-1e2d-41ff-8507-2d1485b52353", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786146961700, "endTime": 219786216700200}, "additional": {"logType": "info", "children": ["4f553371-d8f3-49a8-a33a-a253f593ba1e"], "durationId": "f866a901-b585-43e2-ba12-8954c4ca7d23", "parent": "96339b83-f378-497f-a754-232508bade89"}}, {"head": {"id": "0b50df0d-b0a1-49d8-8029-d3cb279a6a4c", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786252880000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5678e6d0-3bfe-499e-b9b9-b9b2075f66ee", "name": "hvigorfile, resolve hvigorfile dependencies in 111 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786327151800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "279a3673-cd2f-4ab0-9bea-dde8bd9789c7", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786216715100, "endTime": 219786327387200}, "additional": {"logType": "info", "children": [], "durationId": "f19096a0-80ce-4b78-b03b-965b07abb109", "parent": "96339b83-f378-497f-a754-232508bade89"}}, {"head": {"id": "aff990ec-3fd2-4f4b-b8b1-8cc20805c9c0", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786329534100, "endTime": 219786330517400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a76109e3-1afc-4418-8707-a7e75ea2e4c4", "logId": "706b866a-f2d7-4b08-a61d-3df23b2db8f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "21aed658-4019-4dcd-94eb-608cac55ae41", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786329624200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "706b866a-f2d7-4b08-a61d-3df23b2db8f5", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786329534100, "endTime": 219786330517400}, "additional": {"logType": "info", "children": [], "durationId": "aff990ec-3fd2-4f4b-b8b1-8cc20805c9c0", "parent": "96339b83-f378-497f-a754-232508bade89"}}, {"head": {"id": "8186920b-c792-4425-b643-d3fcee95c63b", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786333644500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a6e83e7-8c33-43ee-b43a-ac7943e6bf97", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786353339200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4ec5c39-c50b-4a76-83a7-03f9dddc3558", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786330598000, "endTime": 219786356666100}, "additional": {"logType": "info", "children": [], "durationId": "6fb62c39-fbf8-4c35-9707-296a9f744419", "parent": "96339b83-f378-497f-a754-232508bade89"}}, {"head": {"id": "283321d7-83df-4e01-a9ed-5b48df5c82aa", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786356795400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a230b59a-64bb-4e3f-8344-13dab956682b", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786377776200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68e5b45e-d9db-465e-a1e2-50d350a2d12d", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786378011200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2defdf85-8223-4625-bf1e-947d3e991c0b", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786378431200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85589528-ec7c-43c3-95f1-0622182cce0a", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786384170400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02978381-d678-465f-a67b-f1d093f2310b", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786384389200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02e548fb-9e88-4dea-a052-b7001e8af3a5", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786356737100, "endTime": 219786393185900}, "additional": {"logType": "info", "children": [], "durationId": "3abc4e0a-dc3d-4b16-9a1f-592896059ca6", "parent": "96339b83-f378-497f-a754-232508bade89"}}, {"head": {"id": "503ee7d3-7c66-4807-953a-7a937f8e27c1", "name": "Configuration phase cost:457 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786393289700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baa3877d-1182-4311-a3ec-e6579ad27f9a", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786393234300, "endTime": 219786393504600}, "additional": {"logType": "info", "children": [], "durationId": "9b08a419-aa80-44dc-bbc9-1e3f6405733f", "parent": "96339b83-f378-497f-a754-232508bade89"}}, {"head": {"id": "96339b83-f378-497f-a754-232508bade89", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785924675000, "endTime": 219786393535600}, "additional": {"logType": "info", "children": ["3880f19c-8738-4bac-af17-952e9f57ad5f", "5539af2a-aea1-4490-b295-d7d1fe6a09b6", "d2c719f0-f4cf-4586-b66b-e8123acda5a3", "df115dcf-1e2d-41ff-8507-2d1485b52353", "279a3673-cd2f-4ab0-9bea-dde8bd9789c7", "e4ec5c39-c50b-4a76-83a7-03f9dddc3558", "02e548fb-9e88-4dea-a052-b7001e8af3a5", "baa3877d-1182-4311-a3ec-e6579ad27f9a", "706b866a-f2d7-4b08-a61d-3df23b2db8f5"], "durationId": "a76109e3-1afc-4418-8707-a7e75ea2e4c4", "parent": "86457560-8d4b-4057-adf1-3ec84a08f906"}}, {"head": {"id": "57240eff-7468-4e8e-ac0e-8b0b29ce2b62", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786396365000, "endTime": 219786396423700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "10f3d8e8-2ee8-4171-b787-d335f55861ab", "logId": "3974d769-f2d1-4360-88c0-31522acee863"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3974d769-f2d1-4360-88c0-31522acee863", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786396365000, "endTime": 219786396423700}, "additional": {"logType": "info", "children": [], "durationId": "57240eff-7468-4e8e-ac0e-8b0b29ce2b62", "parent": "86457560-8d4b-4057-adf1-3ec84a08f906"}}, {"head": {"id": "8e9139c4-57a3-4dbd-9e21-c8eac12fa444", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786393592000, "endTime": 219786396478700}, "additional": {"logType": "info", "children": [], "durationId": "f439afae-5af4-4c5f-929d-ff6299cc2f1c", "parent": "86457560-8d4b-4057-adf1-3ec84a08f906"}}, {"head": {"id": "8ea3ce3b-e305-487e-8618-ae2d9acabd82", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786396493500, "endTime": 219786396515000}, "additional": {"logType": "info", "children": [], "durationId": "699c7e26-f6f0-49b2-b153-92f620625322", "parent": "86457560-8d4b-4057-adf1-3ec84a08f906"}}, {"head": {"id": "86457560-8d4b-4057-adf1-3ec84a08f906", "name": "init", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785890701700, "endTime": 219786396528900}, "additional": {"logType": "info", "children": ["db22c633-5bf0-4750-935c-3bc5bd68229c", "96339b83-f378-497f-a754-232508bade89", "8e9139c4-57a3-4dbd-9e21-c8eac12fa444", "8ea3ce3b-e305-487e-8618-ae2d9acabd82", "1949417d-8a6b-4b2e-a5c5-fa6496dcabf5", "05e8d394-fdd6-45c2-a804-7ada9292f3ba", "3974d769-f2d1-4360-88c0-31522acee863"], "durationId": "10f3d8e8-2ee8-4171-b787-d335f55861ab"}}, {"head": {"id": "713675dc-5967-45ba-becf-25cda3c42453", "name": "Configuration task cost before running: 516 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786397107600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ff0ead6-56b5-4ca5-807b-27b145bc2a8b", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786416805900, "endTime": 219786441562500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "6bb22432-bfd7-48d2-a1a5-40d73cdd8fe3", "logId": "241ebba0-caf5-4711-bd4d-3abab4f8924a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6bb22432-bfd7-48d2-a1a5-40d73cdd8fe3", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786400149600}, "additional": {"logType": "detail", "children": [], "durationId": "0ff0ead6-56b5-4ca5-807b-27b145bc2a8b"}}, {"head": {"id": "b36c3755-f7c9-496e-ac3f-f3ec29d7d69a", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786401589100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4708588a-0e06-4775-b131-eda87b9e11c4", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786401822100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6151e9f-045f-4e97-beed-36cd05a29fa2", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786403077300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7aa4554-b366-4385-a9cd-5ce29f17b9a9", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786404902200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e17de77-ba59-4657-a142-0bd6d20e92fb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786407876300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "576e51a0-ab0b-47a8-a9ce-f180a3775005", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786408144700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc5b7676-7bfb-448f-94ea-c39c1985ed28", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786416830500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a25b579-d1bb-4cea-bf7b-513711f3857b", "name": "Incremental task entry:default@PreBuild pre-execution cost: 16 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786441184600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7855ebf1-3d10-4dfe-b4ed-99867e509c42", "name": "entry : default@PreBuild cost memory 0.3230133056640625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786441439000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "241ebba0-caf5-4711-bd4d-3abab4f8924a", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786416805900, "endTime": 219786441562500}, "additional": {"logType": "info", "children": [], "durationId": "0ff0ead6-56b5-4ca5-807b-27b145bc2a8b"}}, {"head": {"id": "a8f78a8a-1fc3-40c0-8898-d74ec1f3b0b9", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786457963200, "endTime": 219786461135900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1dede9e7-1308-4e3f-a774-fe30c09bf5da", "logId": "b9b7cb84-7afe-43b3-a968-bdfc930770e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1dede9e7-1308-4e3f-a774-fe30c09bf5da", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786454455800}, "additional": {"logType": "detail", "children": [], "durationId": "a8f78a8a-1fc3-40c0-8898-d74ec1f3b0b9"}}, {"head": {"id": "92af2b09-5866-4829-b889-69c299d5d839", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786456595100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8c5b142-6e0f-4b45-94d7-7d644128e91e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786456801600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ffee352-b383-4e9e-b806-099088b70349", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786457986400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd0fd37f-76df-4d5c-9d6a-697bfd496516", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786459263000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59947ee8-e284-4133-a2f8-a593495a8f2f", "name": "entry : default@CreateModuleInfo cost memory 0.0615386962890625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786460796000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cbadfb9-c352-456b-9ef0-dd8c26e6a31e", "name": "runTaskFromQueue task cost before running: 579 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786461022700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9b7cb84-7afe-43b3-a968-bdfc930770e9", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786457963200, "endTime": 219786461135900, "totalTime": 3013300}, "additional": {"logType": "info", "children": [], "durationId": "a8f78a8a-1fc3-40c0-8898-d74ec1f3b0b9"}}, {"head": {"id": "8179d3cb-7fb6-4132-ada2-eeb197ed05b4", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786476959900, "endTime": 219786481939600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8be106eb-6429-4558-8d43-fd075d88e5ae", "logId": "4f7bfd1f-074d-4350-80ac-715e8998b0f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8be106eb-6429-4558-8d43-fd075d88e5ae", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786466722800}, "additional": {"logType": "detail", "children": [], "durationId": "8179d3cb-7fb6-4132-ada2-eeb197ed05b4"}}, {"head": {"id": "c260ba89-e264-46b6-a1d1-6457ba57f88b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786469046800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3631ea01-ffb8-4f66-9555-4c8064a0bed2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786469260200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2be1dc3e-2b19-4967-b52b-5fb8a4664bb8", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786476986800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "727be432-f0f6-4e52-9de7-1998e987146b", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786478871300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54e5511a-6ab8-45cc-9039-9acd81799e45", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786481515100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "560368b1-4940-4036-bc77-1e9511fbd13d", "name": "entry : default@GenerateMetadata cost memory 0.1033782958984375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786481787800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f7bfd1f-074d-4350-80ac-715e8998b0f4", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786476959900, "endTime": 219786481939600}, "additional": {"logType": "info", "children": [], "durationId": "8179d3cb-7fb6-4132-ada2-eeb197ed05b4"}}, {"head": {"id": "be43596a-5986-42b1-aabb-28f08a7b94f1", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786487822500, "endTime": 219786488456900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "c45a5390-830e-4470-bb79-151a3b054083", "logId": "b16b3884-6efb-435d-a35d-836189f0807a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c45a5390-830e-4470-bb79-151a3b054083", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786485141700}, "additional": {"logType": "detail", "children": [], "durationId": "be43596a-5986-42b1-aabb-28f08a7b94f1"}}, {"head": {"id": "5ddd4f7f-0456-4fa6-8142-d8004d8416ca", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786487338600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9112ed00-b37a-40b2-9d4b-0e168af0e23d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786487543600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47f877f9-1bb9-40fd-8d7d-ac00fa4571d5", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786487839900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d1f430d-a5b6-4478-8395-7941f4924a4d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786488021500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f20059ae-fb1b-405f-9fb4-ca19242bd4d4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786488102400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea9d7696-d86e-4a07-b382-54a74fc9ee78", "name": "entry : default@ConfigureCmake cost memory 0.03753662109375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786488227900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a8e682b-a1ac-4477-867b-1df820e19544", "name": "runTaskFromQueue task cost before running: 607 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786488353800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b16b3884-6efb-435d-a35d-836189f0807a", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786487822500, "endTime": 219786488456900, "totalTime": 492600}, "additional": {"logType": "info", "children": [], "durationId": "be43596a-5986-42b1-aabb-28f08a7b94f1"}}, {"head": {"id": "872c0d73-a64d-473f-9bd1-83397811edb0", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786495448300, "endTime": 219786498718800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "b83275c0-8e99-451c-a02a-88b039d850e3", "logId": "51455349-4631-4df6-a4c3-f5d7625d072e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b83275c0-8e99-451c-a02a-88b039d850e3", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786491597900}, "additional": {"logType": "detail", "children": [], "durationId": "872c0d73-a64d-473f-9bd1-83397811edb0"}}, {"head": {"id": "7bbeb667-30fd-4e98-9962-10faf379d63a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786493729700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dc5f269-5986-42e2-942e-2946ba04cfc9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786493925000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5963c374-0d15-4636-af0b-ec1bcf0ee456", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786495472000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "331944d0-c442-43fd-ac26-96161cdd3b92", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786498414900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "137ff5eb-d1c1-475f-b7b1-efa27bd2ca61", "name": "entry : default@MergeProfile cost memory 0.1185302734375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786498617500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51455349-4631-4df6-a4c3-f5d7625d072e", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786495448300, "endTime": 219786498718800}, "additional": {"logType": "info", "children": [], "durationId": "872c0d73-a64d-473f-9bd1-83397811edb0"}}, {"head": {"id": "1f224801-9f67-46a3-8e60-6b11e2bd87c5", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786505297300, "endTime": 219786510148700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "fab94725-0e38-4219-b1e2-30d719997009", "logId": "88767da5-1256-4b0e-85ae-18c829a1ce65"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fab94725-0e38-4219-b1e2-30d719997009", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786501345300}, "additional": {"logType": "detail", "children": [], "durationId": "1f224801-9f67-46a3-8e60-6b11e2bd87c5"}}, {"head": {"id": "36e0f460-7431-4de5-87e8-688ed81865db", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786502829100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74c27e49-16ce-4f2d-ab24-e5c319e290bf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786503086200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17aec174-4f8d-42e8-a26e-3565fb5b97aa", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786505328400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d413851d-1992-4e0d-ab84-b95676869f53", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786507203100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5ba12b5-281b-49c9-893e-53594e58eef5", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786509743400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebcb6ae7-6065-41bd-8ea9-e716ab2ffe6f", "name": "entry : default@CreateBuildProfile cost memory 0.108673095703125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786510024200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88767da5-1256-4b0e-85ae-18c829a1ce65", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786505297300, "endTime": 219786510148700}, "additional": {"logType": "info", "children": [], "durationId": "1f224801-9f67-46a3-8e60-6b11e2bd87c5"}}, {"head": {"id": "f02c6eb8-89ba-4d6f-9d52-cf6af6862d88", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786517001000, "endTime": 219786518175600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "26e2d3b9-e348-4024-9644-28fb5c7c7b0e", "logId": "ac68d367-e93b-48cd-bd52-19a7abce753c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "26e2d3b9-e348-4024-9644-28fb5c7c7b0e", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786513189500}, "additional": {"logType": "detail", "children": [], "durationId": "f02c6eb8-89ba-4d6f-9d52-cf6af6862d88"}}, {"head": {"id": "f2700298-af5d-4c8d-bda7-650171652644", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786515135700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d3e0eaf-d9f7-4d9a-9610-de18f3e9d875", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786515306900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "445366be-367c-4467-9042-a906eee78334", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786517026600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "868e4e56-13e5-48bc-a167-1bf37fac<PERSON>ad", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786517293200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60e1a928-a36e-492d-9298-aca271c6d5fb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786517410800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f85c7641-9530-412f-9a55-6a639459859c", "name": "entry : default@PreCheckSyscap cost memory 0.04117584228515625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786517858100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd178bf2-a4d0-4c9b-9b70-d4463309e8ae", "name": "runTaskFromQueue task cost before running: 637 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786518061200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac68d367-e93b-48cd-bd52-19a7abce753c", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786517001000, "endTime": 219786518175600, "totalTime": 1005200}, "additional": {"logType": "info", "children": [], "durationId": "f02c6eb8-89ba-4d6f-9d52-cf6af6862d88"}}, {"head": {"id": "8ec9cf13-b17d-4acd-a402-b59768cffa23", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786526503900, "endTime": 219786540633700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "95dd0b37-e6fc-40a6-84c3-c2daf0f954d2", "logId": "e5685de5-e7a6-4814-b826-d89b199efb8f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95dd0b37-e6fc-40a6-84c3-c2daf0f954d2", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786521129400}, "additional": {"logType": "detail", "children": [], "durationId": "8ec9cf13-b17d-4acd-a402-b59768cffa23"}}, {"head": {"id": "9113cade-8356-4d6b-9b9f-851ec561c9ed", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786522789100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b511aebc-a16f-46cb-a51a-0848dc3c43d6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786522944100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16fbca6d-ab18-41dc-a141-2d7189a4ea47", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786526535600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e216667f-75ed-48b9-bbc6-f64f31bb3704", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786538194000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3fa8abc-17ee-4da5-9f0d-ce629f23d918", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786540038200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebb56880-409d-434f-83e3-08ee4a5dcf43", "name": "entry : default@GeneratePkgContextInfo cost memory 0.33692169189453125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786540492300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5685de5-e7a6-4814-b826-d89b199efb8f", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786526503900, "endTime": 219786540633700}, "additional": {"logType": "info", "children": [], "durationId": "8ec9cf13-b17d-4acd-a402-b59768cffa23"}}, {"head": {"id": "7b0f8f0b-1560-4fe7-bf4d-00cee1d51064", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786555680200, "endTime": 219786561999500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "5895330e-a607-4cc6-9dc3-326149052046", "logId": "e239f40c-8bdf-49cd-ae85-2f43aeeb5e5a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5895330e-a607-4cc6-9dc3-326149052046", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786543504100}, "additional": {"logType": "detail", "children": [], "durationId": "7b0f8f0b-1560-4fe7-bf4d-00cee1d51064"}}, {"head": {"id": "1fa5e3da-7d8b-4b9d-b728-c54484d414d8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786545632200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "559d7e37-f9e0-4a00-b47b-8d374c7ce40a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786545841500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc9bf83e-118c-44e6-b750-7752916bccd7", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786555710900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7957dc8-4c7a-420b-9aa8-cc62b25a783a", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786560888400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "542b4db5-8fac-4c5f-a3a4-e6f9526845ad", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786561259800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a5cbd13-d552-44f7-a2d9-68c45ebf5560", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786561499600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f747bf0-58b8-48f8-a641-e1045a872026", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786561669200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e20f1c2d-80f1-499a-925d-59cb6122d760", "name": "entry : default@ProcessIntegratedHsp cost memory 0.12830352783203125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786561843400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1707c56-3f00-4f6a-8106-f964a2500971", "name": "runTaskFromQueue task cost before running: 680 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786561937700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e239f40c-8bdf-49cd-ae85-2f43aeeb5e5a", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786555680200, "endTime": 219786561999500, "totalTime": 6235100}, "additional": {"logType": "info", "children": [], "durationId": "7b0f8f0b-1560-4fe7-bf4d-00cee1d51064"}}, {"head": {"id": "899c818c-9cbd-4fe3-8f57-dbfcf05dfefd", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786572474900, "endTime": 219786573507600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "7c777f8c-3fc1-40f4-9551-ab15effc88e4", "logId": "f63055ce-015d-4d34-ad51-5c67c8fb667d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c777f8c-3fc1-40f4-9551-ab15effc88e4", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786567138300}, "additional": {"logType": "detail", "children": [], "durationId": "899c818c-9cbd-4fe3-8f57-dbfcf05dfefd"}}, {"head": {"id": "b679747d-a19c-4bd2-8798-3868002c2597", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786570120600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8e6a5ad-d0ac-4d57-8592-e01bcb2bf582", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786570366900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d456f7b-25cb-479c-a834-e8b2551f8aeb", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786572541800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8887487-eede-4540-9139-00d9eb51529d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786572986600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a6cda95-16b0-4ed4-bf64-2a993a83be58", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786573150000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96dde1cf-fb14-44e4-bcd6-0a4b3a3eb5a0", "name": "entry : default@BuildNativeWithCmake cost memory 0.03852081298828125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786573298700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce92ca3c-5c40-418e-8399-d388fd1382f0", "name": "runTaskFromQueue task cost before running: 692 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786573423700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f63055ce-015d-4d34-ad51-5c67c8fb667d", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786572474900, "endTime": 219786573507600, "totalTime": 914400}, "additional": {"logType": "info", "children": [], "durationId": "899c818c-9cbd-4fe3-8f57-dbfcf05dfefd"}}, {"head": {"id": "92638e1d-13b6-49ac-8c27-601e0635dfb1", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786583504100, "endTime": 219786594876500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "eb2cb013-e274-40f3-be4a-53ed74baba89", "logId": "458ebfca-81bc-4e76-ba27-36793e494822"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb2cb013-e274-40f3-be4a-53ed74baba89", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786577182700}, "additional": {"logType": "detail", "children": [], "durationId": "92638e1d-13b6-49ac-8c27-601e0635dfb1"}}, {"head": {"id": "66d295c8-e3dc-4c78-abe2-3f5751baa73d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786581078700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc081078-7b1f-45c3-a28d-b7a25da71b06", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786581324900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c49a4ef-6b01-4de5-8958-dfead6505770", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786583529200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b80fbcb5-6c27-4d3c-92d4-c3baf6163e97", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786594419900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2352e1cc-8624-4a1e-89c8-dc8917928823", "name": "entry : default@MakePackInfo cost memory -5.35150146484375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786594683600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "458ebfca-81bc-4e76-ba27-36793e494822", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786583504100, "endTime": 219786594876500}, "additional": {"logType": "info", "children": [], "durationId": "92638e1d-13b6-49ac-8c27-601e0635dfb1"}}, {"head": {"id": "839d3402-c22e-403e-bfa0-d780a75770f9", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786607800200, "endTime": 219786614902500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "854f420d-bcf7-4965-bf07-fca8a866401c", "logId": "f194681f-bb01-4668-a4be-0fd0488bd15b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "854f420d-bcf7-4965-bf07-fca8a866401c", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786600268300}, "additional": {"logType": "detail", "children": [], "durationId": "839d3402-c22e-403e-bfa0-d780a75770f9"}}, {"head": {"id": "9605c77e-0290-4a0b-b58b-6ed02e57ae43", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786602883600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "341a4b41-9b78-4af7-81b1-4d2b2796f233", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786603363400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a73d1e6-4ba7-44c3-822c-8787439d9529", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786607828500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fbc0d24-5622-4bcd-934b-a2728775dea7", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786608281000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8afa8da-3406-4344-b079-756b078271b8", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786609691300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c8918c4-24cb-4dcf-a251-8dbe90d99a75", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786614473000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "968649c2-4b4c-4616-9f51-dbe2b6460969", "name": "entry : default@SyscapTransform cost memory 0.1514892578125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786614758400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f194681f-bb01-4668-a4be-0fd0488bd15b", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786607800200, "endTime": 219786614902500}, "additional": {"logType": "info", "children": [], "durationId": "839d3402-c22e-403e-bfa0-d780a75770f9"}}, {"head": {"id": "fc77de13-03be-4a3c-bccf-a80156d8276c", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786624144300, "endTime": 219786630088500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "087287c7-49bc-4e4c-a3fe-a28b73cb8168", "logId": "6ef353a7-57eb-4b7f-9b1b-eb063622d009"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "087287c7-49bc-4e4c-a3fe-a28b73cb8168", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786618559200}, "additional": {"logType": "detail", "children": [], "durationId": "fc77de13-03be-4a3c-bccf-a80156d8276c"}}, {"head": {"id": "cc2b7194-e272-4e5b-a63f-33923c<PERSON>bba7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786620997000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "518f7b39-e624-4c26-9db5-5e1aa67ff83f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786621240200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d11d2372-ad31-4fa5-a69f-149c2faed118", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786624175200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0377c0c9-2691-418f-91f4-ad96121f464d", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786629626400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "330ac1e3-69c0-45c5-867c-43fdba8854e9", "name": "entry : default@ProcessProfile cost memory 0.12358856201171875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786629922500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ef353a7-57eb-4b7f-9b1b-eb063622d009", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786624144300, "endTime": 219786630088500}, "additional": {"logType": "info", "children": [], "durationId": "fc77de13-03be-4a3c-bccf-a80156d8276c"}}, {"head": {"id": "1b8fd52c-ef01-4fcb-ae47-e6cc73312c8f", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786639728800, "endTime": 219786655514300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "844e03dc-c90f-4280-a91d-0d80e86c26ad", "logId": "8e20b55f-8007-442a-b7e5-68b19042f270"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "844e03dc-c90f-4280-a91d-0d80e86c26ad", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786633349800}, "additional": {"logType": "detail", "children": [], "durationId": "1b8fd52c-ef01-4fcb-ae47-e6cc73312c8f"}}, {"head": {"id": "cd6e272b-9a7b-48d8-8e7e-9b6778e33445", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786635452300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04f75f62-59aa-4493-8049-5792dc812765", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786635676500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a32cc0c-21c2-44fa-a179-5badb69bcf54", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786639758000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48674cbb-3f20-47cc-9f02-120c47696b14", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786655075700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e4dc509-b072-4390-be0f-ff22c17780d3", "name": "entry : default@ProcessRouterMap cost memory 0.2352142333984375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786655363200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e20b55f-8007-442a-b7e5-68b19042f270", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786639728800, "endTime": 219786655514300}, "additional": {"logType": "info", "children": [], "durationId": "1b8fd52c-ef01-4fcb-ae47-e6cc73312c8f"}}, {"head": {"id": "0741472e-5e25-49ff-a7a5-db9f0c8155a2", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786665854200, "endTime": 219786680066000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "f3473bd8-d72b-493d-994b-2f4a816cc4dc", "logId": "a6f13b33-f89e-4fc9-b2f2-856432b6b8d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f3473bd8-d72b-493d-994b-2f4a816cc4dc", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786663178800}, "additional": {"logType": "detail", "children": [], "durationId": "0741472e-5e25-49ff-a7a5-db9f0c8155a2"}}, {"head": {"id": "dc4cf535-6ab0-4364-bb96-b6bbbd115a4b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786665491700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3c53477-83ec-49a3-8190-82f1aa1b1b46", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786665694700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c18b4f8-c481-4509-a6af-a2515664f49b", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786665867100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3618f51-4ee0-4b6b-94ff-08897afd3cb8", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786666048100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "084a225b-2e9f-405d-8394-423a362d8aa1", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786676392600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da873ade-e5e7-4296-a256-4dbfa8dd7489", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786676691600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f5a928f-4c35-4e73-80fa-00ee7e1471c3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786676914300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e51606d-af22-4c2b-8783-5fbf68f81191", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786677046600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6aba692d-676d-4c95-8335-1d05df46f219", "name": "entry : default@ProcessStartupConfig cost memory 0.26092529296875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786679594300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e5da382-64b5-49c5-9904-2c50a99ac3ad", "name": "runTaskFromQueue task cost before running: 798 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786679927300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6f13b33-f89e-4fc9-b2f2-856432b6b8d9", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786665854200, "endTime": 219786680066000, "totalTime": 14005400}, "additional": {"logType": "info", "children": [], "durationId": "0741472e-5e25-49ff-a7a5-db9f0c8155a2"}}, {"head": {"id": "1b3b68ed-88f5-4051-a34f-f08f59afe170", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786690668000, "endTime": 219786693399600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "20069511-2402-4e0d-ad77-fe135a3acf2c", "logId": "9bd93987-dead-49e2-80d7-473270d31887"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "20069511-2402-4e0d-ad77-fe135a3acf2c", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786684991300}, "additional": {"logType": "detail", "children": [], "durationId": "1b3b68ed-88f5-4051-a34f-f08f59afe170"}}, {"head": {"id": "a9d86a45-5e2d-494f-82ce-b41456fd8df6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786688434500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e3400f9-7795-476e-9e5b-783df601e59d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786688651300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5715acfa-4453-45ea-99e4-abe12f696dfc", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786690692700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e6bdd3f-efd9-4e27-bec3-057235b1415f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786690949600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62f65f70-5468-44e8-be8e-28f05c845146", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786691054000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3fbd8b4-709b-4567-acbc-5f229c56e1b5", "name": "entry : default@BuildNativeWithNinja cost memory 0.0585479736328125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786693081300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c3c88b4-6d10-42a7-aa7a-b28c13bf88ad", "name": "runTaskFromQueue task cost before running: 812 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786693285100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bd93987-dead-49e2-80d7-473270d31887", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786690668000, "endTime": 219786693399600, "totalTime": 2582300}, "additional": {"logType": "info", "children": [], "durationId": "1b3b68ed-88f5-4051-a34f-f08f59afe170"}}, {"head": {"id": "7541de19-ab40-4f44-9912-6269a590c6aa", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786707367600, "endTime": 219786722061700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "9f930760-f0ff-4ac3-bcbe-8f9b948eef3b", "logId": "82913467-cc22-4873-ac60-3cf137a28c6a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f930760-f0ff-4ac3-bcbe-8f9b948eef3b", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786699836900}, "additional": {"logType": "detail", "children": [], "durationId": "7541de19-ab40-4f44-9912-6269a590c6aa"}}, {"head": {"id": "cc18f2d8-9ca6-434e-aa88-8484f6227b12", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786702646000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30b3bb58-0cb2-4e0a-9aee-0021817302e7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786702843400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37173b0c-1621-4533-abec-f69fd75ead98", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786705042800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd2b59a4-9eb5-4900-8f00-d19aa6d7eb70", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786711704100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "294d8856-1194-4c26-96a5-00c7f47c7ca2", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786717028100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84393dbb-0f07-4ba9-86eb-4c529e5f6989", "name": "entry : default@ProcessResource cost memory 0.163421630859375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786717310100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82913467-cc22-4873-ac60-3cf137a28c6a", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786707367600, "endTime": 219786722061700}, "additional": {"logType": "info", "children": [], "durationId": "7541de19-ab40-4f44-9912-6269a590c6aa"}}, {"head": {"id": "ef2f653e-76b6-4862-b3d0-c030cd9c4e49", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786740846600, "endTime": 219786784769800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "01dca484-ff90-4158-828f-d9c0631bf105", "logId": "18d85d8a-9274-4166-ae05-d92dc54bc51a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "01dca484-ff90-4158-828f-d9c0631bf105", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786730058100}, "additional": {"logType": "detail", "children": [], "durationId": "ef2f653e-76b6-4862-b3d0-c030cd9c4e49"}}, {"head": {"id": "5feaa876-05aa-4d4d-bc56-991687ca5b00", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786732925000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f088101a-9b7a-434f-b95d-0f7f6129f702", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786733139400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03fc874d-e44a-4493-9115-2c45b2a04b44", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786740864800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e95f4cdf-9c7b-40fc-9d3f-0ad93af8b779", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 28 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786784356200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad300cbb-135c-4755-a98c-5b3104cebdef", "name": "entry : default@GenerateLoaderJson cost memory 0.8843154907226562", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786784636000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18d85d8a-9274-4166-ae05-d92dc54bc51a", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786740846600, "endTime": 219786784769800}, "additional": {"logType": "info", "children": [], "durationId": "ef2f653e-76b6-4862-b3d0-c030cd9c4e49"}}, {"head": {"id": "c6d678f7-ab90-4789-acf0-8b69d461ec85", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786811113200, "endTime": 219786820492300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "f4de8e2f-fcaa-49d4-935c-d5293c3c429f", "logId": "dbd66b90-570d-4b7f-abb0-5b87cbd7428c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4de8e2f-fcaa-49d4-935c-d5293c3c429f", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786806976400}, "additional": {"logType": "detail", "children": [], "durationId": "c6d678f7-ab90-4789-acf0-8b69d461ec85"}}, {"head": {"id": "52480ad4-5e5c-41b3-b5cd-c59a8cbf2979", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786809274600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12e3c8ae-75ae-480c-9ba2-abffbeaf799e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786809517600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ec264f9-dfd2-4ac9-831d-a437acc75704", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786811133700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee728980-fb8e-4cb4-823a-36112552f29b", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786820036100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73b61c18-e890-40be-ae49-4f4868569947", "name": "entry : default@ProcessLibs cost memory 0.14306640625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786820347500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbd66b90-570d-4b7f-abb0-5b87cbd7428c", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786811113200, "endTime": 219786820492300}, "additional": {"logType": "info", "children": [], "durationId": "c6d678f7-ab90-4789-acf0-8b69d461ec85"}}, {"head": {"id": "5acc7428-3fa5-4bee-9693-f227a2884d6e", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786833701700, "endTime": 219786895142200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "48f66fd2-dcf8-4f9e-8494-4361ad1af2b8", "logId": "1fe33cbb-edf4-49d6-aca9-9ba08d2edc59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48f66fd2-dcf8-4f9e-8494-4361ad1af2b8", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786825019600}, "additional": {"logType": "detail", "children": [], "durationId": "5acc7428-3fa5-4bee-9693-f227a2884d6e"}}, {"head": {"id": "409e93aa-c9d6-4a16-b245-f5072604aba1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786826734100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41c0c230-cde9-4f95-b195-1ce26e901fb4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786826898100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0296205a-e779-4a5e-8890-af1cb8bdba0a", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786828184800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c64cc89b-76ae-43ac-ae4e-d572162b5c5c", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786833752800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ac20c64-76e3-4e47-91ab-32c7b34e760b", "name": "Incremental task entry:default@CompileResource pre-execution cost: 58 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786894578300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0b27c3f-c035-4bbb-b84f-1f9943ebb7bb", "name": "entry : default@CompileResource cost memory 1.3282012939453125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786894913200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fe33cbb-edf4-49d6-aca9-9ba08d2edc59", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786833701700, "endTime": 219786895142200}, "additional": {"logType": "info", "children": [], "durationId": "5acc7428-3fa5-4bee-9693-f227a2884d6e"}}, {"head": {"id": "b187355a-2d46-4888-8909-dc197f14ee4f", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786916333400, "endTime": 219786921368600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "ac082fa5-0e64-427e-9889-74b210b8b0cb", "logId": "4c176b06-6ace-4794-8238-9ff8569d3458"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac082fa5-0e64-427e-9889-74b210b8b0cb", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786905821200}, "additional": {"logType": "detail", "children": [], "durationId": "b187355a-2d46-4888-8909-dc197f14ee4f"}}, {"head": {"id": "49986728-c0a6-42f2-ac8d-28b630113e7e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786909010900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b35ddbd4-4a66-42f0-81f0-a8f2dbfd3473", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786909224700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bd0742f-09a6-41d8-9b30-c36d88dc22b0", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786916358300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a83f6a39-ae34-430b-b3cb-bdde4feddf38", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786917460000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb49538a-eb31-472a-be0d-13969db29bb2", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786921007700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "374276c9-ad98-4ace-8437-5cd2d4975808", "name": "entry : default@DoNativeStrip cost memory 0.08029937744140625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786921222400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c176b06-6ace-4794-8238-9ff8569d3458", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786916333400, "endTime": 219786921368600}, "additional": {"logType": "info", "children": [], "durationId": "b187355a-2d46-4888-8909-dc197f14ee4f"}}, {"head": {"id": "0d73765c-5d2e-483c-873b-f688f3d4118d", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786933016100, "endTime": 219786979664200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "3bcdc93d-4efd-4d83-b002-cb88f2e1a155", "logId": "e8856305-ddfa-46a5-952a-82328d685376"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3bcdc93d-4efd-4d83-b002-cb88f2e1a155", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786923756000}, "additional": {"logType": "detail", "children": [], "durationId": "0d73765c-5d2e-483c-873b-f688f3d4118d"}}, {"head": {"id": "3f5ebae8-e502-450e-9a6e-5c5b22858bbd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786925093800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43a793f9-670f-4ee5-baf6-61b2f169d374", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786925228400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e93c06ba-0dd1-407d-b7d0-6cb53<PERSON>ceb9b", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786933037800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b88c632-df1f-4739-a207-05d00dbc94ee", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786933286300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bbb15f5-1e57-4f50-8565-c256c475a0f4", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 34 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786979439400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41de58d1-af1f-4121-9622-dda7f6a1a3fc", "name": "entry : default@CompileArkTS cost memory 1.1972503662109375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786979596300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8856305-ddfa-46a5-952a-82328d685376", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786933016100, "endTime": 219786979664200}, "additional": {"logType": "info", "children": [], "durationId": "0d73765c-5d2e-483c-873b-f688f3d4118d"}}, {"head": {"id": "73dfcfb7-42ce-4a9c-88d5-1c0ce63bea66", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786998605100, "endTime": 219787013412600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "544e9146-dd8b-47a1-aa17-ebe0f5c829e5", "logId": "c2bd4b10-ca99-4331-bcc1-2abda715576b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "544e9146-dd8b-47a1-aa17-ebe0f5c829e5", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786986755100}, "additional": {"logType": "detail", "children": [], "durationId": "73dfcfb7-42ce-4a9c-88d5-1c0ce63bea66"}}, {"head": {"id": "9827aaa3-0da9-49fc-9a41-4f20c7018079", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786989464500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b176b96-aa4d-4aee-a3de-f467b8adccbe", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786989643900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86764980-809d-4301-9a4b-b001f75b0981", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786998627000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cb373dc-212d-4ca6-9ea0-712c55f6e1d7", "name": "entry : default@BuildJS cost memory 0.34572601318359375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787013203400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "447cf233-9323-4984-bc12-5f37f8accec4", "name": "runTaskFromQueue task cost before running: 1 s 132 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787013355700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2bd4b10-ca99-4331-bcc1-2abda715576b", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219786998605100, "endTime": 219787013412600, "totalTime": 14726200}, "additional": {"logType": "info", "children": [], "durationId": "73dfcfb7-42ce-4a9c-88d5-1c0ce63bea66"}}, {"head": {"id": "05c6ee53-6af3-487a-a056-e1d257d6d403", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787023994000, "endTime": 219787030397500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "ad28bb95-ba92-4da6-9a0d-539ad761331a", "logId": "3f40a718-0ad2-4e28-a9fa-52f60c7c9416"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad28bb95-ba92-4da6-9a0d-539ad761331a", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787015849500}, "additional": {"logType": "detail", "children": [], "durationId": "05c6ee53-6af3-487a-a056-e1d257d6d403"}}, {"head": {"id": "a2fda786-9cb4-4e62-9429-80a2bb722936", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787017887100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "278e5dea-341a-4cfc-b7aa-b501deac21cf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787018046100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b8052b4-55ac-4f4a-a06c-e5ccfdcdd7ad", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787024050500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24a00cdf-0c99-4536-afca-639f968e6262", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787025412600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "947f1104-be1d-41d1-8d32-7ad70d36ba12", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787030034100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8ba31e5-6dde-4e37-b4f5-bcbd4a2588f7", "name": "entry : default@CacheNativeLibs cost memory 0.09613037109375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787030268100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f40a718-0ad2-4e28-a9fa-52f60c7c9416", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787023994000, "endTime": 219787030397500}, "additional": {"logType": "info", "children": [], "durationId": "05c6ee53-6af3-487a-a056-e1d257d6d403"}}, {"head": {"id": "cec32680-effa-4d4b-85e4-30b40f1e875a", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787036773100, "endTime": 219787038142300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "70e7d330-cc8a-49e6-b97d-76e88e6df135", "logId": "764eef48-7a2e-40a3-83ad-607ed4f1e483"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "70e7d330-cc8a-49e6-b97d-76e88e6df135", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787033109800}, "additional": {"logType": "detail", "children": [], "durationId": "cec32680-effa-4d4b-85e4-30b40f1e875a"}}, {"head": {"id": "e9d34575-4869-47b1-be23-75a9e51f8e78", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787034573600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e857c2c4-273d-4a2a-918f-1e9a19aa2756", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787034744400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d98efa6e-52e3-4541-9ba7-48700bd51bb3", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787036783900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67997be5-1e32-48d7-afd1-3562e8679fba", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787037118700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd129382-ff4b-4f8b-9723-bf97a0a9b627", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787037980800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e5c6706-a3db-42d1-8aa0-30e4a8a9320a", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07523345947265625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787038086900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "764eef48-7a2e-40a3-83ad-607ed4f1e483", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787036773100, "endTime": 219787038142300}, "additional": {"logType": "info", "children": [], "durationId": "cec32680-effa-4d4b-85e4-30b40f1e875a"}}, {"head": {"id": "30190bf3-e8ce-46ad-b2a1-bb1888d564a6", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787048212900, "endTime": 219787077734000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "723bbc0c-0589-4a0a-9556-9aef27800874", "logId": "719a31e8-c0b7-4541-9af9-cebb721094bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "723bbc0c-0589-4a0a-9556-9aef27800874", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787040161300}, "additional": {"logType": "detail", "children": [], "durationId": "30190bf3-e8ce-46ad-b2a1-bb1888d564a6"}}, {"head": {"id": "8a297043-da51-4a57-99e3-dfec808c3d9d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787041030900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77f27f5f-2c1c-4b38-830a-3d13a8076177", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787041125100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6376f786-3aa2-4c88-b78c-38fa70017e82", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787048232400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a45b9f66-537b-419a-b230-0d2fa4b4a9d6", "name": "Incremental task entry:default@PackageHap pre-execution cost: 28 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787077515500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a87a5a4-7c18-48b3-9277-b403d1879e2f", "name": "entry : default@PackageHap cost memory 0.9492568969726562", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787077668800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "719a31e8-c0b7-4541-9af9-cebb721094bf", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787048212900, "endTime": 219787077734000}, "additional": {"logType": "info", "children": [], "durationId": "30190bf3-e8ce-46ad-b2a1-bb1888d564a6"}}, {"head": {"id": "4f13faa8-c85a-4cb7-9b14-101f3a1a37f9", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787091249100, "endTime": 219787096470300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": [], "detailId": "06c48e4d-1d5e-4a5a-b385-950c97bd1b6e", "logId": "7ffa071f-c26d-413f-9987-fa50cba7833f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06c48e4d-1d5e-4a5a-b385-950c97bd1b6e", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787084441600}, "additional": {"logType": "detail", "children": [], "durationId": "4f13faa8-c85a-4cb7-9b14-101f3a1a37f9"}}, {"head": {"id": "e4d81ca6-06f9-4841-87fb-b882382937f6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787086881800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e2d6a16-628f-4c11-a5bc-16938454fd29", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787087066400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fc84d01-b473-41d7-a2d4-18ec47c6f940", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787091274200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac99ee28-1e12-42ca-b27f-0c1177b2f142", "name": "Incremental task entry:default@SignHap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787095964300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d2e1219-1a2d-4fd6-af6a-9a8828335837", "name": "entry : default@SignHap cost memory 0.10556793212890625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787096281700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ffa071f-c26d-413f-9987-fa50cba7833f", "name": "UP-TO-DATE :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787091249100, "endTime": 219787096470300}, "additional": {"logType": "info", "children": [], "durationId": "4f13faa8-c85a-4cb7-9b14-101f3a1a37f9"}}, {"head": {"id": "ecfef912-518f-4c33-98b2-04ba31004f5c", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787104037600, "endTime": 219787113645900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "60b83208-0b6e-49f5-8071-46692abdfdc2", "logId": "514ae0ee-5b43-48b9-a7ba-1aa49097babb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "60b83208-0b6e-49f5-8071-46692abdfdc2", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787100632800}, "additional": {"logType": "detail", "children": [], "durationId": "ecfef912-518f-4c33-98b2-04ba31004f5c"}}, {"head": {"id": "7503de07-fcee-4de6-9a89-71ee82054541", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787102785900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e6b317f-5756-4018-974a-c88e2343b4ac", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787102960400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cf7394c-cd9d-4216-9835-9ee8b25cd250", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787104051700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbbd08f3-0b45-4b06-b894-798e0c53947a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787113266400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e24a205-e2a7-43c4-86c9-4a0fcb11f39d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787113420700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b98ff037-8a38-459b-aa0a-59392369de14", "name": "entry : default@CollectDebugSymbol cost memory 0.24493408203125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787113508200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "305a7287-ce82-4aab-b784-2c19253ee381", "name": "runTaskFromQueue task cost before running: 1 s 232 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787113590400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "514ae0ee-5b43-48b9-a7ba-1aa49097babb", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787104037600, "endTime": 219787113645900, "totalTime": 9524000}, "additional": {"logType": "info", "children": [], "durationId": "ecfef912-518f-4c33-98b2-04ba31004f5c"}}, {"head": {"id": "05746aba-dd30-48e4-bf1e-620fcfa57805", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787115421000, "endTime": 219787115692000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "7ef91a80-ba77-4003-8bdb-ebf90912566b", "logId": "086a3b8f-edb7-45dd-9723-332a5d4ffdc5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ef91a80-ba77-4003-8bdb-ebf90912566b", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787115370700}, "additional": {"logType": "detail", "children": [], "durationId": "05746aba-dd30-48e4-bf1e-620fcfa57805"}}, {"head": {"id": "e1cf3b70-324b-480f-b293-36e7e7c0654c", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787115429400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee6c970a-8037-4cc2-af75-50e7684d0a93", "name": "entry : assembleHap cost memory 0.011749267578125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787115561900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3e9a2c2-f1e0-4e68-afd3-32c8f113a62e", "name": "runTaskFromQueue task cost before running: 1 s 234 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787115650600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "086a3b8f-edb7-45dd-9723-332a5d4ffdc5", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787115421000, "endTime": 219787115692000, "totalTime": 206100}, "additional": {"logType": "info", "children": [], "durationId": "05746aba-dd30-48e4-bf1e-620fcfa57805"}}, {"head": {"id": "6869935a-1807-470f-808c-552d6aabd2b7", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787134311300, "endTime": 219787134357500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "61a35ef5-5d41-4616-adec-1c438ae2f88c", "logId": "06084143-cb43-4d93-b8c9-d5557cfeed1d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06084143-cb43-4d93-b8c9-d5557cfeed1d", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787134311300, "endTime": 219787134357500}, "additional": {"logType": "info", "children": [], "durationId": "6869935a-1807-470f-808c-552d6aabd2b7"}}, {"head": {"id": "dabc7c4c-03a1-4905-a967-004530d841c8", "name": "BUILD SUCCESSFUL in 1 s 253 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787134442800}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "a4dc87e2-0fc5-46e4-881f-1e453ed91d0a", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219785881999900, "endTime": 219787135127400}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 19, "minute": 0, "second": 16}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "4611ad32-c217-4b4e-b926-cee9e7f9f5c7", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787135168800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0208f122-5f9e-4a74-947a-f47fc595fe89", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787135316400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb7cf43f-8f17-4b80-af63-ab4bf0f5622e", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787136137600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d9a3fde-3637-43b5-a7b5-ad0147062b70", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787136295900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cd8b46c-d709-445d-be66-3d6751e0c2f2", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787136402400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4a7d402-b4d6-4b4c-a648-7c7f775c152d", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787136727500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6989c6b2-9b6f-42ae-917e-8a8b13fb0db7", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787136810900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6c29365-9a86-47b9-a62e-dda6b20206a7", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787138441200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85752ea6-d4d9-41a7-ab46-00fcca1f5eb9", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787138949200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41da99e4-b3aa-4e3a-b614-51b6e03aa9d2", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787139083100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e8bb07f-51d6-4143-b425-162c34e51d5f", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787139149900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e8850a2-0841-4071-971c-9f8904465cfb", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787139203700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "289eafc1-6abb-44d3-a512-03db1cfeea65", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787139252200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e076ff51-a8f0-433f-b276-0c5df423199f", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787142335900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5028490e-5eed-4492-84ee-c95d8c853d72", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787142853800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "567a7ba0-cf71-410c-81b8-e656c4abd69e", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787143220600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35dd1f7a-8a95-4371-9d9a-59c2e3849d1e", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787143530700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "199d023f-3a90-43af-8083-70afe9d51bd9", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787143630900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e90d5d1-f1c3-4c52-a3d9-88e2d31fedfc", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787143711400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4346fec-78d0-4f4a-95bb-ffcb06aef155", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787143826000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "598f6482-b10a-4bdd-a9e3-e35e15b739e0", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787143908200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9986e42-425c-45ed-8c2a-068507ad865f", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787143981700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "173ae160-43b3-42ca-8fca-43ea7a237263", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787149301900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47503405-76b9-47de-8eed-9d1e30884573", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787151515600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bedf853f-d40f-48da-94f3-e3c291ddad5f", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787152805500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd49b202-d73e-478a-a194-5088794c5ffd", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787153645600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f876581c-76c5-46bd-863a-5474b09e898b", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787154345000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df89b947-287b-4c7d-9ab9-f473f20226eb", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787157309000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44b80472-9754-45c2-9327-95e0e588b292", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787159841500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf4ae4d9-39b4-4094-997d-cfb705ba339c", "name": "Incremental task entry:default@BuildJS post-execution cost:17 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787160485300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd1b4e28-9f4f-438b-a708-950b163cb6c1", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787160652800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b45d9aa3-4169-4b80-adb7-6495474fd1db", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787160761000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c75e90d-7265-4947-89b3-f8f198249158", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787160868200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d48e6e61-dec8-4334-84aa-a47cb82c7ffa", "name": "There is no need to refresh cache, since the incremental task entry:default@SignHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787160958400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93f6f4f0-554c-4c44-b32c-fa7fc17797e4", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787168565500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38d28fb0-4a7c-4253-a5fb-d3cdb312ba23", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787171300800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c07f7675-cc59-484a-aa4f-51210364924e", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787172998300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11282eeb-b224-4344-b0bf-c37f98ec8ad6", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:13 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787173643700}, "additional": {"logType": "debug", "children": []}}], "workLog": []}