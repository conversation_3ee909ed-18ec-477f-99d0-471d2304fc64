06-18 19:58:10.513   2731-2731     C03915/com.exa...hub/AceSwiper  com.example.nexushub  I     [(100000:100000:scope)] Swiper ShowNext, id:496
06-18 19:58:12.955   2731-2954     C015B0/com.exa...shub/NETSTACK  com.example.nexushub  E     [epoll_multi_driver.cpp:75] epoll wait event 0 err: 113
06-18 19:58:13.433   2731-2899     C015B0/com.exa...shub/NETSTACK  com.example.nexushub  E     [epoll_multi_driver.cpp:75] epoll wait event 0 err: 0
06-18 19:58:13.514   2731-2731     C03915/com.exa...hub/AceSwiper  com.example.nexushub  I     [(100000:100000:scope)] Swiper ShowNext, id:496
06-18 19:58:13.542   2731-2731     C01406/com.exa...shub/OHOS::RS  com.example.nexushub  E     FlushImplicitTransaction return, [renderServiceClient_:1, transactionData empty:1]
06-18 19:58:14.058   2731-2867     C03914/com.exam...AceTextField  com.example.nexushub  I     [(-1:100000:singleton)] Handle function key 6
06-18 19:58:14.059   2731-2731     C03914/com.exam...AceTextField  com.example.nexushub  I     [(100000:100000:scope)] TextField PerformAction  6
06-18 19:58:14.059   2731-2731     C03914/com.exam...AceTextField  com.example.nexushub  I     [(100000:100000:scope)] On submit <private>
06-18 19:58:14.059   2731-2731     A00000/com.exa...ub/HttpClient  com.example.nexushub  I     Built URL: http://*************:8080/api/v1/search/apps
06-18 19:58:14.059   2731-2731     A00000/com.exa...ub/HttpClient  com.example.nexushub  I     GET Request - URL: http://*************:8080/api/v1/search/apps?keyword=14511&page=1&page_size=20
06-18 19:58:14.059   2731-2731     A00000/com.exa...ub/HttpClient  com.example.nexushub  I     GET Request - Headers: {"Content-Type":"application/json","Accept":"application/json","Authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxMSwidXNlcm5hbWUiOiJhZG1pbjEzIiwicm9sZSI6InVzZXIiLCJpc3MiOiJuZXh1c2h1YiIsInN1YiI6IjExIiwiZXhwIjoxNzUwMjcyNTEyLCJuYmYiOjE3NTAxODYxMTIsImlhdCI6MTc1MDE4NjExMn0.NKpKgLA_Dj0QNHEis3j1MirVlEYwOTbAMcvSKtriZ6A"}
06-18 19:58:14.061   2731-2731     C03914/com.exam...AceTextField  com.example.nexushub  I     [(100000:100000:scope)] textfield 5642 Will Close Soft keyboard.
06-18 19:58:14.061   2731-2731     C01C10/com.exa...ushub/ImsaKit  com.example.nexushub  I     line: 379, function: Close,start.
06-18 19:58:14.061   2731-2731     C03914/com.exam...AceTextField  com.example.nexushub  I     [(100000:100000:scope)] OnDetach
06-18 19:58:14.062   2731-2867     C03914/com.exam...AceTextField  com.example.nexushub  I     [(-1:100000:singleton)] SendKeyboardStatus status: 1
06-18 19:58:14.062   2731-2867     C03914/com.exam...AceTextField  com.example.nexushub  I     [(-1:100000:singleton)] SetKeyboardStatus status: 0
06-18 19:58:14.064   2731-2731     C03914/com.exam...AceTextField  com.example.nexushub  I     [(100000:100000:scope)] FinishTextPreview
06-18 19:58:14.064   2731-2731     C03914/com.exam...AceTextField  com.example.nexushub  I     [(100000:100000:scope)] SendKeyboardStatus status: 1
06-18 19:58:14.064   2731-2731     C03914/com.exam...AceTextField  com.example.nexushub  I     [(100000:100000:scope)] SetKeyboardStatus status: 0
06-18 19:58:14.064   2731-2731     C03914/com.exam...AceTextField  com.example.nexushub  I     [(100000:100000:scope)] FinishTextPreview
06-18 19:58:14.064   2731-2731     C03914/com.exam...AceTextField  com.example.nexushub  I     [(100000:100000:scope)] SendKeyboardStatus status: 1
06-18 19:58:14.064   2731-2731     C03914/com.exam...AceTextField  com.example.nexushub  I     [(100000:100000:scope)] SetKeyboardStatus status: 0
06-18 19:58:14.064   2731-2731     C0391C/com.exa...shub/AceFocus  com.example.nexushub  I     [(100000:100000:scope)] Lost focus to view root: Stack/5628
06-18 19:58:14.064   2731-2731     C03914/com.exam...AceTextField  com.example.nexushub  I     [(100000:100000:scope)] TextField 5642 OnBlur
06-18 19:58:14.064   2731-2731     C0391C/com.exa...shub/AceFocus  com.example.nexushub  I     [(100000:100000:scope)] FocusSwitch end, TextInput/secure_field onBlur, Stack/secure_field onFocus, start: 3, end: 1, update: 0
06-18 19:58:14.064   2731-2731     C03933/com.exa...b/AceKeyboard  com.example.nexushub  I     [(100000:100000:scope)] current focus node info : (Stack/5628).
06-18 19:58:14.064   2731-2731     C03933/com.exa...b/AceKeyboard  com.example.nexushub  I     [(100000:100000:scope)] FrameNode(Stack/5628) notNeedSoftKeyboard.
06-18 19:58:14.064   2731-2731     C03933/com.exa...b/AceKeyboard  com.example.nexushub  I     [(100000:100000:scope)] PageChange CloseKeyboard FrameNode notNeedSoftKeyboard.
06-18 19:58:14.064   2731-2731     C03933/com.exa...b/AceKeyboard  com.example.nexushub  I     [(100000:100000:scope)] PageChange CloseKeyboard SoftKeyboard Closes Successfully.
06-18 19:58:14.066   2731-2731     C03914/com.exam...AceTextField  com.example.nexushub  I     [(100000:100000:scope)] input state now is not at previewing text
06-18 19:58:14.066   2731-2731     C03914/com.exam...AceTextField  com.example.nexushub  I     [(100000:100000:scope)] input state now is not at previewing text
06-18 19:58:14.077   2731-2867     C03914/com.exam...AceTextField  com.example.nexushub  I     [(-1:100000:singleton)] NotifyPanelStatusInfo soft keyboard is closed by user, trigger=1
06-18 19:58:14.077   2731-2731     C03914/com.exam...AceTextField  com.example.nexushub  I     [(100000:100000:scope)] NotifyKeyboardClosed
06-18 19:58:14.077   2731-2731     C03914/com.exam...AceTextField  com.example.nexushub  I     [(-1:100000:singleton)] NotifyPanelStatusInfo SetImeShow:0
06-18 19:58:14.091   2731-2867     C0420B/com.exa...b/WMSKeyboard  com.example.nexushub  I     [] NotifyOccupiedAreaChangeInfo(4262): hasRSTransaction: 1, safeHeight: 0, occupied rect: x 0, y 0, w 0, h 0
06-18 19:58:14.091   2731-2731     C01406/com.exa...shub/OHOS::RS  com.example.nexushub  I     BeginSyncTransaction
06-18 19:58:14.091   2731-2731     C03900/com.exa....nexushub/Ace  com.example.nexushub  I     [(-2:100000:singleton)] OccupiedAreaChange rect:Rect (0.00, 0.00) - [0.00 x 0.00] type: 0, positionY:175.000000, height:176.000000, instanceId_ 100000
06-18 19:58:14.091   2731-2731     C0390D/com.exa...ub/AceOverlay  com.example.nexushub  W     [(100000:100000:scope)] failed to get foldCreaseRegion
06-18 19:58:14.091   2731-2731     C03903/com.exam...AceSubWindow  com.example.nexushub  W     [(100000:100000:scope)] Fail to find subwindow in subwindowMap_, instanceId is 100000.
06-18 19:58:14.091   2731-2731     C03933/com.exa...b/AceKeyboard  com.example.nexushub  I     [(100000:100000:scope)] origin positionY: 175.000000, height 176.000000
06-18 19:58:14.091   2731-2731     C03933/com.exa...b/AceKeyboard  com.example.nexushub  I     [(100000:100000:scope)] use origin arg from the window
06-18 19:58:14.091   2731-2731     C03933/com.exa...b/AceKeyboard  com.example.nexushub  I     [(100000:100000:scope)] keyboardHeight: 0.000000, positionY: 175.000000, textHeight: 176.000000, rootSize.Height() 2776.000000 final calculate keyboard offset is 0.000000
06-18 19:58:14.091   2731-2731     C03903/com.exam...AceSubWindow  com.example.nexushub  W     [(100000:100000:scope)] Fail to find subwindow in subwindowMap_, instanceId is 100000.
06-18 19:58:14.091   2731-2731     C03903/com.exam...AceSubWindow  com.example.nexushub  W     [(100000:100000:scope)] Fail to find subwindow in toastWindowMap_, instanceId is 100000.
06-18 19:58:14.091   2731-2731     C01406/com.exa...shub/OHOS::RS  com.example.nexushub  I     CommitSyncTransaction syncId: 12330851107049 syncCount: 0 parentPid: 2871
06-18 19:58:14.099   2731-2899     C015B0/com.exa...shub/NETSTACK  com.example.nexushub  I     [http_exec.cpp:418] taskid=-2147483617, size:2662, dns:0.069, connect:0.000, tls:0.000, firstSend:0.145, firstRecv:37.575, total:37.825, redirect:0.000, errCode:0, RespCode:200, httpVer:2, method:GET, osErr:0
06-18 19:58:14.102   2731-2731     A00000/com.exa...ub/HttpClient  com.example.nexushub  I     GET Response - Code: 200, Result: "{\"code\":200,\"message\":\"success\",\"data\":{\"apps\":[{\"id\":26,\"name\":\"待审核应用\",\"package\":\"com.example.pending\",\"description\":\"这是一个待审核的应用\",\"short_desc\":\"待审核应用\",\"category\":\"工具\",\"developer_id\":1,\"developer_name\":\"\",\"tags\":\"测试,待审核\",\"current_version\":\"1.0.0\",\"download_count\":0,\"average_rating\":0,\"rating_count\":0,\"min_open_harmony_os_ver\":\"\",\"is_verified\":false,\"is_featured\":false,\"is_editor\":false,\"is_top\":false,\"status\":\"approved\",\"keywords\":[\"待审核应用\",\"com.example.pending\",\"待审核应用\",\"\",\"工具\",\"测试\",\"待审核\",\"这是一个待审核的应用\"]},{\"id\":27,\"name\":\"14511\",\"package\":\"aaa1.hap\",\"description\":\"26541651532168411104010\",\"short_desc\":\"1111\",\"category\":\"金融\",\"developer_id\":7,\"developer_name\":\"111111111\",\"tags\":\",新品,推荐,免费\",\"current_version\":\"1.0.1\",\"download_count\":0,\"average_rating\":0,\"rating_count\":0,\"min_open_harmony_os_ver\":\"\",\"is_verified\":false,\"is_featured\":false,\"is_editor\":false,\"is_top\":false,\"status\":\"approved\",\"keywords\":[\"14511\",\"aaa1.hap\",\"1111\",\"111111111\",\"金融\",\"新品\",\"推荐\",\"免费\",\"26541651532168411104010\"]},{\"id\":32,\"name\":\"啊啊啊啊啊1111111aa01\",\"package\":\"hapaaaa01.hap\",\"description\":\"00450、\\n10\",\"short_desc\":\"1111\",\"category\":\"社交\",\"developer_id\":1,\"developer_name\":\"\",\"tags\":\",实用\",\"current_version\":\"\",\"download_count\":0,\"average_rating\":0,\"rating_count\":0,\"min_open_harmony_os_ver\":\"\",\"is_verified\":false,\"is_featured\":false,\"is_editor\":false,\"is_top\":false,\"status\":\"approved\",\"keywords\":[\"啊啊啊啊啊1111111aa01\",\"hapaaaa01.hap\",\"1111\",\"\",\"社交\",\"实用\",\"00450、\"]},{\"id\":29,\"name\":\"啊啊啊啊啊111\",\"package\":\"aaa11544.hap\",\"description\":\"5130.750\",\"short_desc\":\"1111\",\"category\":\"社交\",\"developer_id\":7,\"developer_name\":\"111111111\",\"tags\":\"\",\"current_version\":\"\",\"download_count\":0,\"average_rating\":0,\"rating_count\":0,\"min_open_harmony_os_ver\":\"\",\"is_verified\":false,\"is_featured\":false,\"is_editor\":false,\"is_top\":false,\"status\":\"approved\",\"keywords\":[\"啊啊啊啊啊111\",\"aaa11544.hap\",\"1111\",\"111111111\",\"社交\",\"5130.750\"]},{\"id\":33,\"name\":\"啊啊啊啊啊1111111aa0111\",\"package\":\"hapaaaa1101.hap\",\"description\":\"11111\",\"short_desc\":\"1111\",\"category\":\"钱钱钱\",\"developer_id\":1,\"developer_name\":\"\",\"tags\":\"免费,钱钱钱\",\"current_version\":\"\",\"download_count\":0,\"average_rating\":0,\"rating_count\":0,\"min_open_harmony_os_ver\":\"\",\"is_verified\":false,\"is_featured\":false,\"is_editor\":false,\"is_top\":false,\"status\":\"approved\",\"keywords\":[\"啊啊啊啊啊1111111aa0111\",\"hapaaaa1101.hap\",\"1111\",\"\",\"钱钱钱\",\"免费\",\"钱钱钱\",\"11111\"]}],\"total\":5,\"page\":1,\"page_size\":20,\"total_pages\":1}}"
06-18 19:58:14.102   2731-2731     A00000/com.exa...ub/HttpClient  com.example.nexushub  I     HTTP Response - Code: 200, URL: unknown
06-18 19:58:14.103   2731-2731     C04209/com.exa...ushub/WMSImms  com.example.nexushub  I     [] NotifyAvoidAreaChange(3450): window [406, nexushub0] type 3 area empty
06-18 19:58:14.103   2731-2731     C04208/com.exa...hub/WMSLayout  com.example.nexushub  I     [] UpdateViewportConfig(1208): [rotation,deviceRotation,transformHint,virtualPixelRatio]:[0,0,0,3.375000]
06-18 19:58:14.103   2731-2731     C03959/com.exa...hub/AceLayout  com.example.nexushub  I     [(-2:100000:singleton)] [com.example.nexushub][entry][100000]: UpdateViewportConfig Viewport config: size: (1224, 2776) orientation: 0 density: 3.375000 position: (0, 0) transformHint: 0, windowSizeChangeReason 23, is rsTransaction nullptr 1, updateAvoidAreas size 5, [(0,top [0 0 1224 148] )(1,top [475 48 274 86] )(2,empty)(3,empty)(4,bottom [397 2681 430 95] )]
06-18 19:58:14.103   2731-2731     C0394E/com.exa...hub/AceWindow  com.example.nexushub  I     [(-2:100000:singleton)] Update orientation to : 0
06-18 19:58:14.103   2731-2731     C04208/com.exa...hub/WMSLayout  com.example.nexushub  I     [] UpdateViewportConfig(1225): Id:406 reason:23 windowRect:[0,0,1224,2776] displayOrientation:0
06-18 19:58:14.103   2731-5531     C01653/com.exa...vePreferences  com.example.nexushub  I     WriteToDiskFile: No data to update persistent file
06-18 19:58:16.516   2731-2731     C03915/com.exa...hub/AceSwiper  com.example.nexushub  I     [(100000:100000:scope)] Swiper ShowNext, id:496
06-18 19:58:17.955   2731-2954     C015B0/com.exa...shub/NETSTACK  com.example.nexushub  E     [epoll_multi_driver.cpp:75] epoll wait event 0 err: 113
06-18 19:58:19.100   2731-2899     C015B0/com.exa...shub/NETSTACK  com.example.nexushub  E     [epoll_multi_driver.cpp:75] epoll wait event 0 err: 0
06-18 19:58:19.104   2731-5531     C01719/com.exa...nexushub/ffrt  com.example.nexushub  W     66:~WorkerThread:72 to exit, qos[3]
06-18 19:58:19.516   2731-2731     C03915/com.exa...hub/AceSwiper  com.example.nexushub  I     [(100000:100000:scope)] Swiper ShowNext, id:496
06-18 19:58:21.192   2731-2954     C015B0/com.exa...shub/NETSTACK  com.example.nexushub  I     taskid=117, size:0, dns:0.079, connect:0.000, tls:0.000, firstSend:0.000, firstRecv:0.000, total:60000.866, redirect:0.000, errCode:2300028, RespCode:0, httpVer:0, method:GET, osErr:0
06-18 19:58:21.193   2731-2954     C0393A/com.exa...wnloadManager  com.example.nexushub  I     [209]Async http task of url [<private>] failed, response code 0, msg from netStack: [Timeout was reached]
06-18 19:58:22.518   2731-2731     C03915/com.exa...hub/AceSwiper  com.example.nexushub  I     [(100000:100000:scope)] Swiper ShowNext, id:496