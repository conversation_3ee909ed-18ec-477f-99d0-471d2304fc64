{"version": "2.0", "ppid": 32784, "events": [{"head": {"id": "f6cfd031-76b9-46fb-8116-77b2343b2b72", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584865654100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21ae99a3-ec64-4fc5-a0f8-72706b652687", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584883133400, "endTime": 219589924098000}, "additional": {"children": ["54d7903b-0c40-411c-8e5e-3a51accd5aef", "59dd96aa-4782-4cc2-ad7f-8f66050d80be", "0d60a873-814f-4086-ade1-072caf38246f", "3ec6c265-72d3-4c71-9b86-6b317ff54a57", "2122c52d-f4e4-4897-86d0-07aa12e8550e", "18cf012a-6493-49d5-b23b-5d5fd9572832", "ee3b5b0c-f7da-435b-b0fd-782e40e7cefe"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "8e1b12f7-7f0b-4150-a762-4f253bce5b7e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "54d7903b-0c40-411c-8e5e-3a51accd5aef", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584883139000, "endTime": 219584911031300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "21ae99a3-ec64-4fc5-a0f8-72706b652687", "logId": "b76b6ba7-5ef0-4c6d-9c59-723a9b336351"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59dd96aa-4782-4cc2-ad7f-8f66050d80be", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584911060200, "endTime": 219589919834500}, "additional": {"children": ["e46f0666-fd7b-4cf2-a830-2696c45d93f2", "b221ea30-359d-43b1-bdf3-f45a89a2d5e6", "11454d74-bda2-4e2e-8399-7ccd4c8dd9d0", "c55b650a-8610-4fb8-a4ae-321c67c0d7cc", "1f8169b6-ccd3-48d4-8ec6-d1201556e789", "f158c5d9-db19-4538-9788-64826a8c4862", "c719e4ce-6650-4111-b587-9a479a4a192c", "95f1e7d5-f508-4282-b80f-78b737bc3016", "e87dcd36-2c72-4f02-96d7-4df8c66d0732"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "21ae99a3-ec64-4fc5-a0f8-72706b652687", "logId": "909966b7-63e0-4205-b5a5-352b20bb67ad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d60a873-814f-4086-ade1-072caf38246f", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589919891300, "endTime": 219589924016100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "21ae99a3-ec64-4fc5-a0f8-72706b652687", "logId": "599d1341-7138-45dc-b317-5e90a0b17cfb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ec6c265-72d3-4c71-9b86-6b317ff54a57", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589924028100, "endTime": 219589924073200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "21ae99a3-ec64-4fc5-a0f8-72706b652687", "logId": "ff8bce5a-a026-44c2-877a-5b761cdcf06a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2122c52d-f4e4-4897-86d0-07aa12e8550e", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584891608700, "endTime": 219584891902600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "21ae99a3-ec64-4fc5-a0f8-72706b652687", "logId": "6f8b1336-36d0-45be-bcbe-49ab5d20843e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f8b1336-36d0-45be-bcbe-49ab5d20843e", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584891608700, "endTime": 219584891902600}, "additional": {"logType": "info", "children": [], "durationId": "2122c52d-f4e4-4897-86d0-07aa12e8550e", "parent": "8e1b12f7-7f0b-4150-a762-4f253bce5b7e"}}, {"head": {"id": "18cf012a-6493-49d5-b23b-5d5fd9572832", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584900571900, "endTime": 219584900652100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "21ae99a3-ec64-4fc5-a0f8-72706b652687", "logId": "47d5cde0-f3a3-45dc-9601-606f6e35f5ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "47d5cde0-f3a3-45dc-9601-606f6e35f5ba", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584900571900, "endTime": 219584900652100}, "additional": {"logType": "info", "children": [], "durationId": "18cf012a-6493-49d5-b23b-5d5fd9572832", "parent": "8e1b12f7-7f0b-4150-a762-4f253bce5b7e"}}, {"head": {"id": "0086cc49-8d06-43ca-a455-5bcb7c8f7f12", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584901291100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f8ba8d7-5cf3-40d4-b7b0-ac5967695c4b", "name": "Cache service initialization finished in 9 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584910818700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b76b6ba7-5ef0-4c6d-9c59-723a9b336351", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584883139000, "endTime": 219584911031300}, "additional": {"logType": "info", "children": [], "durationId": "54d7903b-0c40-411c-8e5e-3a51accd5aef", "parent": "8e1b12f7-7f0b-4150-a762-4f253bce5b7e"}}, {"head": {"id": "e46f0666-fd7b-4cf2-a830-2696c45d93f2", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584918795800, "endTime": 219584918868100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "59dd96aa-4782-4cc2-ad7f-8f66050d80be", "logId": "a5a64a35-8762-4047-aff6-027d643b9c0c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b221ea30-359d-43b1-bdf3-f45a89a2d5e6", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584918925400, "endTime": 219584925010800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "59dd96aa-4782-4cc2-ad7f-8f66050d80be", "logId": "1267c8d5-eaa7-4c2e-8176-252ff2fd3e53"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11454d74-bda2-4e2e-8399-7ccd4c8dd9d0", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584925181600, "endTime": 219589719164200}, "additional": {"children": ["67ba46f5-1e14-46fe-8b6a-fbd1a34a6154", "35fdd483-495a-4b72-990c-20e0db0f7704", "5cad2c5e-947e-4f67-bc05-559573d3b6b6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "59dd96aa-4782-4cc2-ad7f-8f66050d80be", "logId": "9517c32d-ea2b-4af8-a75e-e26500493829"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c55b650a-8610-4fb8-a4ae-321c67c0d7cc", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589719346000, "endTime": 219589772125200}, "additional": {"children": ["54c0fdba-c606-44c7-ab73-51c105201c21"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "59dd96aa-4782-4cc2-ad7f-8f66050d80be", "logId": "f70daee1-00c2-4c6e-9324-7e39fa9d3600"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1f8169b6-ccd3-48d4-8ec6-d1201556e789", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589772363800, "endTime": 219589861264600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "59dd96aa-4782-4cc2-ad7f-8f66050d80be", "logId": "0070b026-4d89-487a-a8b1-6a0340f988d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f158c5d9-db19-4538-9788-64826a8c4862", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589863379200, "endTime": 219589881397500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "59dd96aa-4782-4cc2-ad7f-8f66050d80be", "logId": "752af8ff-9446-4891-8e4b-a75282254d60"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c719e4ce-6650-4111-b587-9a479a4a192c", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589881434400, "endTime": 219589919529900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "59dd96aa-4782-4cc2-ad7f-8f66050d80be", "logId": "338860a3-2312-4b0b-a6d7-6ba9f8cdd331"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95f1e7d5-f508-4282-b80f-78b737bc3016", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589919567400, "endTime": 219589919811400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "59dd96aa-4782-4cc2-ad7f-8f66050d80be", "logId": "b4f4dab9-acb8-4e32-a266-07d62fb9d1c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5a64a35-8762-4047-aff6-027d643b9c0c", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584918795800, "endTime": 219584918868100}, "additional": {"logType": "info", "children": [], "durationId": "e46f0666-fd7b-4cf2-a830-2696c45d93f2", "parent": "909966b7-63e0-4205-b5a5-352b20bb67ad"}}, {"head": {"id": "1267c8d5-eaa7-4c2e-8176-252ff2fd3e53", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584918925400, "endTime": 219584925010800}, "additional": {"logType": "info", "children": [], "durationId": "b221ea30-359d-43b1-bdf3-f45a89a2d5e6", "parent": "909966b7-63e0-4205-b5a5-352b20bb67ad"}}, {"head": {"id": "67ba46f5-1e14-46fe-8b6a-fbd1a34a6154", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584926265100, "endTime": 219584926339000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "11454d74-bda2-4e2e-8399-7ccd4c8dd9d0", "logId": "58ee6f9e-2773-4d5d-83d1-c41ede7541ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "58ee6f9e-2773-4d5d-83d1-c41ede7541ba", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584926265100, "endTime": 219584926339000}, "additional": {"logType": "info", "children": [], "durationId": "67ba46f5-1e14-46fe-8b6a-fbd1a34a6154", "parent": "9517c32d-ea2b-4af8-a75e-e26500493829"}}, {"head": {"id": "35fdd483-495a-4b72-990c-20e0db0f7704", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584929597100, "endTime": 219589717765200}, "additional": {"children": ["d233b221-c100-4e68-95a0-2c998ee5100c", "6bc6313d-705d-48b7-99a0-1452ea335124"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "11454d74-bda2-4e2e-8399-7ccd4c8dd9d0", "logId": "4f6d3e78-4b4f-4ed2-8bb3-e788795057b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d233b221-c100-4e68-95a0-2c998ee5100c", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584929598800, "endTime": 219589363149800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35fdd483-495a-4b72-990c-20e0db0f7704", "logId": "2b100531-82b9-4616-87b1-cd4ccd596f0f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6bc6313d-705d-48b7-99a0-1452ea335124", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589363179100, "endTime": 219589717745500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35fdd483-495a-4b72-990c-20e0db0f7704", "logId": "384316c0-01d9-494e-92a2-7f9485f84cf3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0dc4683e-0cc8-4e75-8aaf-3eac5b1e3c68", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584929609200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94008fc4-2ed5-45bc-8b7b-7752d27539ca", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589362949200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b100531-82b9-4616-87b1-cd4ccd596f0f", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584929598800, "endTime": 219589363149800}, "additional": {"logType": "info", "children": [], "durationId": "d233b221-c100-4e68-95a0-2c998ee5100c", "parent": "4f6d3e78-4b4f-4ed2-8bb3-e788795057b4"}}, {"head": {"id": "4575dc44-8df1-44a5-934a-72e4aa82c8aa", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589363314400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04e057ac-ea20-44ec-aec7-c6c63ede7cc6", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589598466000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30e9de2d-86b6-4eda-b8c9-79f29105bdb9", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589598801000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7a6be22-ee37-4260-b750-66e8dff96c0a", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589599423300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45b82fae-3ca8-44df-a691-21ae037700ff", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589599686900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cdee44b-ae3a-4f1d-9691-5d3745e748b4", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589604983700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e44e30d-2fbe-4e60-9ed5-531c814bbbe9", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589635037600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95f1743b-9853-4be6-bff8-680bfefc5e5c", "name": "Sdk init in 54 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589673095100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12d701b7-1118-4706-9f1e-6631c83b7e71", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589673806200}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 18, "minute": 56, "second": 59}, "markType": "other"}}, {"head": {"id": "1259e493-e2ca-4248-9b3c-95a4eb41df95", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589673932400}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 18, "minute": 56, "second": 59}, "markType": "other"}}, {"head": {"id": "652d2639-0305-4cae-80ce-ce0b7dcb4e85", "name": "Project task initialization takes 34 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589716848500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddac9988-f805-416e-961c-cd70a6bbf201", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589717401900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28dd42c7-66ea-4303-b301-52a754d86adc", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589717530900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a796a5db-0771-4ee9-b7db-dee4d1c99971", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589717595300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "384316c0-01d9-494e-92a2-7f9485f84cf3", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589363179100, "endTime": 219589717745500}, "additional": {"logType": "info", "children": [], "durationId": "6bc6313d-705d-48b7-99a0-1452ea335124", "parent": "4f6d3e78-4b4f-4ed2-8bb3-e788795057b4"}}, {"head": {"id": "4f6d3e78-4b4f-4ed2-8bb3-e788795057b4", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584929597100, "endTime": 219589717765200}, "additional": {"logType": "info", "children": ["2b100531-82b9-4616-87b1-cd4ccd596f0f", "384316c0-01d9-494e-92a2-7f9485f84cf3"], "durationId": "35fdd483-495a-4b72-990c-20e0db0f7704", "parent": "9517c32d-ea2b-4af8-a75e-e26500493829"}}, {"head": {"id": "5cad2c5e-947e-4f67-bc05-559573d3b6b6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589719057900, "endTime": 219589719130100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "11454d74-bda2-4e2e-8399-7ccd4c8dd9d0", "logId": "c8d0743d-a997-42d3-a06f-f7a969f4c2b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8d0743d-a997-42d3-a06f-f7a969f4c2b9", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589719057900, "endTime": 219589719130100}, "additional": {"logType": "info", "children": [], "durationId": "5cad2c5e-947e-4f67-bc05-559573d3b6b6", "parent": "9517c32d-ea2b-4af8-a75e-e26500493829"}}, {"head": {"id": "9517c32d-ea2b-4af8-a75e-e26500493829", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584925181600, "endTime": 219589719164200}, "additional": {"logType": "info", "children": ["58ee6f9e-2773-4d5d-83d1-c41ede7541ba", "4f6d3e78-4b4f-4ed2-8bb3-e788795057b4", "c8d0743d-a997-42d3-a06f-f7a969f4c2b9"], "durationId": "11454d74-bda2-4e2e-8399-7ccd4c8dd9d0", "parent": "909966b7-63e0-4205-b5a5-352b20bb67ad"}}, {"head": {"id": "54c0fdba-c606-44c7-ab73-51c105201c21", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589720215100, "endTime": 219589772087300}, "additional": {"children": ["8ec71631-cb8c-4f1b-a1e9-057bea36f375", "330d10f3-98ff-4521-9639-6a58f263c3c2", "ce164050-7f48-4814-85fe-2fc0138c0387"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c55b650a-8610-4fb8-a4ae-321c67c0d7cc", "logId": "7aafe4ae-b499-4c28-8b9e-8c61f3c6d882"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ec71631-cb8c-4f1b-a1e9-057bea36f375", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589725089200, "endTime": 219589725122500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "54c0fdba-c606-44c7-ab73-51c105201c21", "logId": "f381d549-faa7-4528-a253-88a8982d2a26"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f381d549-faa7-4528-a253-88a8982d2a26", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589725089200, "endTime": 219589725122500}, "additional": {"logType": "info", "children": [], "durationId": "8ec71631-cb8c-4f1b-a1e9-057bea36f375", "parent": "7aafe4ae-b499-4c28-8b9e-8c61f3c6d882"}}, {"head": {"id": "330d10f3-98ff-4521-9639-6a58f263c3c2", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589727861300, "endTime": 219589769732000}, "additional": {"children": ["b00109f9-6ea6-45ee-94f0-e31d891b0b6d", "a049c3f2-e3c6-486d-a670-b88737e647ca"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "54c0fdba-c606-44c7-ab73-51c105201c21", "logId": "65a059a4-816b-47db-bf74-5ed219512d18"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b00109f9-6ea6-45ee-94f0-e31d891b0b6d", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589727862600, "endTime": 219589736474700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "330d10f3-98ff-4521-9639-6a58f263c3c2", "logId": "80a70df4-9c0e-4101-9cea-ab6c6dd9743a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a049c3f2-e3c6-486d-a670-b88737e647ca", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589736500500, "endTime": 219589769719700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "330d10f3-98ff-4521-9639-6a58f263c3c2", "logId": "c34a4468-80cf-48db-9216-1ec13a97a017"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e32ad051-1134-43ce-befe-59081b9621d7", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589727870800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47ed8a75-a94e-406e-8817-e98342a45ca7", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589736299600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80a70df4-9c0e-4101-9cea-ab6c6dd9743a", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589727862600, "endTime": 219589736474700}, "additional": {"logType": "info", "children": [], "durationId": "b00109f9-6ea6-45ee-94f0-e31d891b0b6d", "parent": "65a059a4-816b-47db-bf74-5ed219512d18"}}, {"head": {"id": "2e5ef35b-4f72-4e86-ba46-c6cd178c878b", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589736671500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7529cc6-8df8-42fc-8889-e1c63c6aae05", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589754474700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91f01820-5ac0-42eb-a8aa-62bf69937294", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589754859900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba0ac12c-6942-4219-90e8-7d360e28b57f", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589755484000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e356b7a-b534-4a0c-80a7-d749e1cc9c9c", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589756380400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19b9f7af-0da2-422b-9d47-c02a29de01fd", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589756635000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12a6ed24-6256-4faa-8ea6-4994369e6c61", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589756722900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49de7fbd-f73d-40ee-aaab-e69f662f777e", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589756834100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce544e0c-23d6-4a38-b76c-7cb0cd339371", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589756927500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "709530d6-9c2f-47ba-b5bd-04b50989640c", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589757376300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ceb44a47-aaf0-4591-840c-d2b7409f1aa6", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589757561700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af1d83be-3f40-4458-ba16-e70a9a9b3ea1", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589757642200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c0b7c46-40bc-4a76-a404-baf611da9f04", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589757687300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30313246-152c-446b-9646-c94cad7071ff", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589757948100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ce1e08b-9f53-4a70-b573-7536486b16d9", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589758045900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35b225fa-b0c7-4652-b193-b7237faed13d", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589758319300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0f46b60-38b9-40dc-841a-bb3249dc0eb0", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589758468400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6054848e-950f-4247-aba8-a47c9c494cf5", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589758530900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2eb95597-3e2d-4276-a480-660c048a4dfa", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589758684500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2fc3457-f4c8-4903-8c27-6a8e8008d288", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589759073200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49e8496a-8740-42f9-a845-7cc3b73a8f21", "name": "Module entry task initialization takes 6 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589769229700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1948fae7-b0b2-447b-81d1-cfa67efcac78", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589769510500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1da01e85-5217-4f6d-880f-5154ea5d1ec8", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589769630600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cccfefbe-6d9c-4c2b-9342-1994755659cc", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589769680000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c34a4468-80cf-48db-9216-1ec13a97a017", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589736500500, "endTime": 219589769719700}, "additional": {"logType": "info", "children": [], "durationId": "a049c3f2-e3c6-486d-a670-b88737e647ca", "parent": "65a059a4-816b-47db-bf74-5ed219512d18"}}, {"head": {"id": "65a059a4-816b-47db-bf74-5ed219512d18", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589727861300, "endTime": 219589769732000}, "additional": {"logType": "info", "children": ["80a70df4-9c0e-4101-9cea-ab6c6dd9743a", "c34a4468-80cf-48db-9216-1ec13a97a017"], "durationId": "330d10f3-98ff-4521-9639-6a58f263c3c2", "parent": "7aafe4ae-b499-4c28-8b9e-8c61f3c6d882"}}, {"head": {"id": "ce164050-7f48-4814-85fe-2fc0138c0387", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589772038300, "endTime": 219589772060700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "54c0fdba-c606-44c7-ab73-51c105201c21", "logId": "a35df951-871b-4b38-ad71-75f08f16eea2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a35df951-871b-4b38-ad71-75f08f16eea2", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589772038300, "endTime": 219589772060700}, "additional": {"logType": "info", "children": [], "durationId": "ce164050-7f48-4814-85fe-2fc0138c0387", "parent": "7aafe4ae-b499-4c28-8b9e-8c61f3c6d882"}}, {"head": {"id": "7aafe4ae-b499-4c28-8b9e-8c61f3c6d882", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589720215100, "endTime": 219589772087300}, "additional": {"logType": "info", "children": ["f381d549-faa7-4528-a253-88a8982d2a26", "65a059a4-816b-47db-bf74-5ed219512d18", "a35df951-871b-4b38-ad71-75f08f16eea2"], "durationId": "54c0fdba-c606-44c7-ab73-51c105201c21", "parent": "f70daee1-00c2-4c6e-9324-7e39fa9d3600"}}, {"head": {"id": "f70daee1-00c2-4c6e-9324-7e39fa9d3600", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589719346000, "endTime": 219589772125200}, "additional": {"logType": "info", "children": ["7aafe4ae-b499-4c28-8b9e-8c61f3c6d882"], "durationId": "c55b650a-8610-4fb8-a4ae-321c67c0d7cc", "parent": "909966b7-63e0-4205-b5a5-352b20bb67ad"}}, {"head": {"id": "bc53c673-ba9f-4a9b-ac37-d309f25c7e77", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589799106400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44dc4d76-66d5-4cc0-8407-98116581ea83", "name": "hvigorfile, resolve hvigorfile dependencies in 89 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589861084800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0070b026-4d89-487a-a8b1-6a0340f988d4", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589772363800, "endTime": 219589861264600}, "additional": {"logType": "info", "children": [], "durationId": "1f8169b6-ccd3-48d4-8ec6-d1201556e789", "parent": "909966b7-63e0-4205-b5a5-352b20bb67ad"}}, {"head": {"id": "e87dcd36-2c72-4f02-96d7-4df8c66d0732", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589862551000, "endTime": 219589863312800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "59dd96aa-4782-4cc2-ad7f-8f66050d80be", "logId": "20943c60-dd97-4daa-8e5a-6812dfce8e2d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7a7b4025-f940-4f3d-9d5d-9f59223eeae5", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589862738300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20943c60-dd97-4daa-8e5a-6812dfce8e2d", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589862551000, "endTime": 219589863312800}, "additional": {"logType": "info", "children": [], "durationId": "e87dcd36-2c72-4f02-96d7-4df8c66d0732", "parent": "909966b7-63e0-4205-b5a5-352b20bb67ad"}}, {"head": {"id": "a695bee3-f881-4fac-afd8-581a6d40903c", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589866801300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1df2c366-cecb-4cff-9f3f-4b5cf48d3109", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589879694100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "752af8ff-9446-4891-8e4b-a75282254d60", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589863379200, "endTime": 219589881397500}, "additional": {"logType": "info", "children": [], "durationId": "f158c5d9-db19-4538-9788-64826a8c4862", "parent": "909966b7-63e0-4205-b5a5-352b20bb67ad"}}, {"head": {"id": "09a8edb9-b738-48da-b91d-58305e4e5e59", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589881593100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32968eaf-0f21-4a06-b812-0b9216de0766", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589902080000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49a8a6dd-1cd0-46df-b865-f54ae2f0bb43", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589902267600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2f54331-3158-46c3-a290-c462e27482bc", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589903152400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e769fe3b-66cb-4159-97fe-1ccc4f28fce3", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589910211500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d11d9526-1f87-4322-b9bc-5a384a9fd3b8", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589910396000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "338860a3-2312-4b0b-a6d7-6ba9f8cdd331", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589881434400, "endTime": 219589919529900}, "additional": {"logType": "info", "children": [], "durationId": "c719e4ce-6650-4111-b587-9a479a4a192c", "parent": "909966b7-63e0-4205-b5a5-352b20bb67ad"}}, {"head": {"id": "74ad895c-95af-4dc2-8de5-fe971e484256", "name": "Configuration phase cost:5 s ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589919626500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4f4dab9-acb8-4e32-a266-07d62fb9d1c6", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589919567400, "endTime": 219589919811400}, "additional": {"logType": "info", "children": [], "durationId": "95f1e7d5-f508-4282-b80f-78b737bc3016", "parent": "909966b7-63e0-4205-b5a5-352b20bb67ad"}}, {"head": {"id": "909966b7-63e0-4205-b5a5-352b20bb67ad", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584911060200, "endTime": 219589919834500}, "additional": {"logType": "info", "children": ["a5a64a35-8762-4047-aff6-027d643b9c0c", "1267c8d5-eaa7-4c2e-8176-252ff2fd3e53", "9517c32d-ea2b-4af8-a75e-e26500493829", "f70daee1-00c2-4c6e-9324-7e39fa9d3600", "0070b026-4d89-487a-a8b1-6a0340f988d4", "752af8ff-9446-4891-8e4b-a75282254d60", "338860a3-2312-4b0b-a6d7-6ba9f8cdd331", "b4f4dab9-acb8-4e32-a266-07d62fb9d1c6", "20943c60-dd97-4daa-8e5a-6812dfce8e2d"], "durationId": "59dd96aa-4782-4cc2-ad7f-8f66050d80be", "parent": "8e1b12f7-7f0b-4150-a762-4f253bce5b7e"}}, {"head": {"id": "ee3b5b0c-f7da-435b-b0fd-782e40e7cefe", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589923845600, "endTime": 219589923985300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "21ae99a3-ec64-4fc5-a0f8-72706b652687", "logId": "bf17f756-01e3-4f8b-bfd9-3107aab1a4da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf17f756-01e3-4f8b-bfd9-3107aab1a4da", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589923845600, "endTime": 219589923985300}, "additional": {"logType": "info", "children": [], "durationId": "ee3b5b0c-f7da-435b-b0fd-782e40e7cefe", "parent": "8e1b12f7-7f0b-4150-a762-4f253bce5b7e"}}, {"head": {"id": "599d1341-7138-45dc-b317-5e90a0b17cfb", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589919891300, "endTime": 219589924016100}, "additional": {"logType": "info", "children": [], "durationId": "0d60a873-814f-4086-ade1-072caf38246f", "parent": "8e1b12f7-7f0b-4150-a762-4f253bce5b7e"}}, {"head": {"id": "ff8bce5a-a026-44c2-877a-5b761cdcf06a", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589924028100, "endTime": 219589924073200}, "additional": {"logType": "info", "children": [], "durationId": "3ec6c265-72d3-4c71-9b86-6b317ff54a57", "parent": "8e1b12f7-7f0b-4150-a762-4f253bce5b7e"}}, {"head": {"id": "8e1b12f7-7f0b-4150-a762-4f253bce5b7e", "name": "init", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584883133400, "endTime": 219589924098000}, "additional": {"logType": "info", "children": ["b76b6ba7-5ef0-4c6d-9c59-723a9b336351", "909966b7-63e0-4205-b5a5-352b20bb67ad", "599d1341-7138-45dc-b317-5e90a0b17cfb", "ff8bce5a-a026-44c2-877a-5b761cdcf06a", "6f8b1336-36d0-45be-bcbe-49ab5d20843e", "47d5cde0-f3a3-45dc-9601-606f6e35f5ba", "bf17f756-01e3-4f8b-bfd9-3107aab1a4da"], "durationId": "21ae99a3-ec64-4fc5-a0f8-72706b652687"}}, {"head": {"id": "8db57e4d-e37f-45a2-b8d7-4674f289cd50", "name": "Configuration task cost before running: 5 s 49 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589925376600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03a96e58-b468-4175-b739-4eed7425f2dc", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589954806400, "endTime": 219589981273100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "9bb12ac1-cf8b-4257-b0d0-bd5057c42352", "logId": "9e84cfbf-8a39-430f-a332-6b94129910c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9bb12ac1-cf8b-4257-b0d0-bd5057c42352", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589928738000}, "additional": {"logType": "detail", "children": [], "durationId": "03a96e58-b468-4175-b739-4eed7425f2dc"}}, {"head": {"id": "51e00883-bc61-43ab-b7bb-f59309e6559a", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589931182300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bac17d3-4f65-4bf2-9cd8-69dac835a40c", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589931503700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83ffbfbc-77b9-4eca-bfdc-b622cc771f0a", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589933871300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dba64355-ea1c-4e88-a2c4-e2b866e9ef5b", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589937319500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7400398d-6727-4025-bcff-36873d6eaf24", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589942438200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "421d8557-24bd-4c18-9664-5771f8410cb3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589942662400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06bdf43c-138b-410b-8448-bad59819e601", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589954863600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f01f6aa-4fba-459e-8906-bbf5d311b363", "name": "Incremental task entry:default@PreBuild pre-execution cost: 15 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589980562100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03d331c7-343a-494c-ada1-8a3bb9ec217c", "name": "entry : default@PreBuild cost memory 0.8227996826171875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589980927200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e84cfbf-8a39-430f-a332-6b94129910c0", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589954806400, "endTime": 219589981273100}, "additional": {"logType": "info", "children": [], "durationId": "03a96e58-b468-4175-b739-4eed7425f2dc"}}, {"head": {"id": "c5337d5c-e630-4d0c-83a7-ea174117b854", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589993822000, "endTime": 219589998215200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "61333eb5-c1fb-440d-80ac-cec8e72456dd", "logId": "13b3b248-d0b8-47b1-9aba-7a149ae25071"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61333eb5-c1fb-440d-80ac-cec8e72456dd", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589988716300}, "additional": {"logType": "detail", "children": [], "durationId": "c5337d5c-e630-4d0c-83a7-ea174117b854"}}, {"head": {"id": "1f4d42e0-29fd-4709-b9bd-a79d74a9a537", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589992457500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df7d46ed-e70f-4ed5-95b1-e70e04247817", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589992642700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da4c765f-ff0c-4376-a7b9-22fe8487321a", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589993849100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d315235f-8cc5-4ca3-9314-1204839ec18d", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589995487600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "794f71fb-9037-4d92-927b-f0dfeefa33cc", "name": "entry : default@CreateModuleInfo cost memory 0.06461334228515625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589997580000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1be04f25-15c2-4320-93f0-cc880c18ef9c", "name": "runTaskFromQueue task cost before running: 5 s 122 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589997880600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13b3b248-d0b8-47b1-9aba-7a149ae25071", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219589993822000, "endTime": 219589998215200, "totalTime": 3999000}, "additional": {"logType": "info", "children": [], "durationId": "c5337d5c-e630-4d0c-83a7-ea174117b854"}}, {"head": {"id": "923c111c-42ba-4b73-8401-83346ff85a85", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590012111500, "endTime": 219590019621800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c0bc373b-a7d8-409f-a395-cb80b45759f0", "logId": "57a9d823-db31-4f5d-9851-f90c566cf9db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0bc373b-a7d8-409f-a395-cb80b45759f0", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590002004400}, "additional": {"logType": "detail", "children": [], "durationId": "923c111c-42ba-4b73-8401-83346ff85a85"}}, {"head": {"id": "425dacd7-0b1d-4082-9911-1a2c821fe85f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590003912300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0db56d60-e147-4a3c-bc4d-a7ccc32f0e7a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590004080100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7303870-c327-4b54-8390-275c19da2c1c", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590012137900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02e5266a-6dcd-46aa-bf83-90c2776b10a5", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 3 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590014710500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14516598-e5db-4a75-af1f-735d361ba871", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590019117100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fad0fbd7-126e-4e51-9480-ea957982952d", "name": "entry : default@GenerateMetadata cost memory 0.10826873779296875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590019450500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57a9d823-db31-4f5d-9851-f90c566cf9db", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590012111500, "endTime": 219590019621800}, "additional": {"logType": "info", "children": [], "durationId": "923c111c-42ba-4b73-8401-83346ff85a85"}}, {"head": {"id": "9b9ebe63-d452-4eb3-b970-0fe1014885fd", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590028156500, "endTime": 219590029490100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "fcd0e51f-42aa-4619-8e66-355ca70828e6", "logId": "0edfb8ce-51d2-4e04-9be1-b99a4835450b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fcd0e51f-42aa-4619-8e66-355ca70828e6", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590024018900}, "additional": {"logType": "detail", "children": [], "durationId": "9b9ebe63-d452-4eb3-b970-0fe1014885fd"}}, {"head": {"id": "77d4a625-6066-42b4-b58f-a7e5e65ccbf0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590026941700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8a8fdbf-9f71-4ec2-8825-7e2ca717b82d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590027375700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f998c97-3f42-439e-92c5-fa14d10be578", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590028201200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5efe23c-af86-4945-a52c-327732a4e53c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590028786100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b9453d1-a3d6-4c03-8033-d9c572c4ac7a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590028955700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c97ed181-b1ea-4775-a366-81da63196810", "name": "entry : default@ConfigureCmake cost memory 0.037750244140625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590029168900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59bafd76-b23c-4aa7-865c-6aa88fb12e32", "name": "runTaskFromQueue task cost before running: 5 s 153 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590029365900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0edfb8ce-51d2-4e04-9be1-b99a4835450b", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590028156500, "endTime": 219590029490100, "totalTime": 1156000}, "additional": {"logType": "info", "children": [], "durationId": "9b9ebe63-d452-4eb3-b970-0fe1014885fd"}}, {"head": {"id": "9ed5f1b2-4883-497b-896f-333acc0158bc", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590038267800, "endTime": 219590047869500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "8ebec1f7-fb2e-448a-b05b-3f39594f626e", "logId": "d022f980-de7f-450f-b8dd-d850492161a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ebec1f7-fb2e-448a-b05b-3f39594f626e", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590033543700}, "additional": {"logType": "detail", "children": [], "durationId": "9ed5f1b2-4883-497b-896f-333acc0158bc"}}, {"head": {"id": "aa4a5c56-25fb-4825-8a6c-eb3318413cb7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590036011800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbf628f4-7ea4-465d-a1ed-f877e2fdca37", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590036262900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "084874ea-1b05-44b6-af2f-895144a6715d", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590038327200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d8dc48a-9cd8-4bdb-aa3d-a85daebc8394", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590046871100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "181696f7-4e49-4cef-b8de-817977609e1c", "name": "entry : default@MergeProfile cost memory 0.124725341796875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590047510300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d022f980-de7f-450f-b8dd-d850492161a4", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590038267800, "endTime": 219590047869500}, "additional": {"logType": "info", "children": [], "durationId": "9ed5f1b2-4883-497b-896f-333acc0158bc"}}, {"head": {"id": "cfb78d55-fdc0-4a3b-9b8a-1b553ced0ebf", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590060865100, "endTime": 219590071599400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "bebc6cc7-7bc9-4acb-ab2b-d9fe2e6881cf", "logId": "1ec24849-96ca-4191-8d3f-7ca8236d2466"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bebc6cc7-7bc9-4acb-ab2b-d9fe2e6881cf", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590053371200}, "additional": {"logType": "detail", "children": [], "durationId": "cfb78d55-fdc0-4a3b-9b8a-1b553ced0ebf"}}, {"head": {"id": "17b2e688-266f-47f7-a691-0070cdc3e16e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590057622400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e0995f4-e5dc-460b-b34f-eb78c39179f2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590057901500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb633877-69e3-4149-81e3-9f08813e0960", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590060915900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a254312e-109c-4f79-9af5-f6af23d575aa", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 4 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590065033200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75142967-2b92-491d-aced-eb41eb915c76", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590071009500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c570096-a85a-4a42-ba6b-ae1897e6e772", "name": "entry : default@CreateBuildProfile cost memory 0.1144561767578125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590071352800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ec24849-96ca-4191-8d3f-7ca8236d2466", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590060865100, "endTime": 219590071599400}, "additional": {"logType": "info", "children": [], "durationId": "cfb78d55-fdc0-4a3b-9b8a-1b553ced0ebf"}}, {"head": {"id": "0227c8d4-9fa4-4344-aa98-882f45a32bad", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590082945600, "endTime": 219590084825600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "e60a6385-b55c-47e1-b4d4-3a7999f5a137", "logId": "7165aeec-1a7d-42e7-9bf1-bf3ff71b2dcb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e60a6385-b55c-47e1-b4d4-3a7999f5a137", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590077503100}, "additional": {"logType": "detail", "children": [], "durationId": "0227c8d4-9fa4-4344-aa98-882f45a32bad"}}, {"head": {"id": "7f4ddec3-3d86-47be-8cd8-9d099c1fa6b8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590080346300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12242101-9697-401f-9145-e5691b99b931", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590080669300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f9cdb2f-2b6c-493c-bd7c-5261b74db7d6", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590082988500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2214caad-6f5a-43d7-ba67-114f3a26ba68", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590083344500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eeb0c71a-e8c4-422f-befe-73419d3aec1c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590083504500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3142d115-2f6d-4333-b94a-e2b9a9e665fe", "name": "entry : default@PreCheckSyscap cost memory 0.04160308837890625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590084359800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55892048-b63e-4c4a-84d3-da175fc1abd0", "name": "runTaskFromQueue task cost before running: 5 s 208 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590084662100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7165aeec-1a7d-42e7-9bf1-bf3ff71b2dcb", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590082945600, "endTime": 219590084825600, "totalTime": 1660700}, "additional": {"logType": "info", "children": [], "durationId": "0227c8d4-9fa4-4344-aa98-882f45a32bad"}}, {"head": {"id": "7a40e414-130d-4b7f-96ef-56daf78b01d8", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590102884900, "endTime": 219590125832200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c73d26f4-739f-4615-a516-b724516dc1f4", "logId": "3d254ff4-b550-4936-bf37-7e0b23229377"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c73d26f4-739f-4615-a516-b724516dc1f4", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590091256300}, "additional": {"logType": "detail", "children": [], "durationId": "7a40e414-130d-4b7f-96ef-56daf78b01d8"}}, {"head": {"id": "4083d437-83c3-4371-a295-97da858d0004", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590095914100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "401a3879-71c4-4429-bf66-8b7ee6db9b66", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590096339000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57724908-2f48-4f9e-92e7-13d086692b3a", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590102923100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c6553be-6352-4f85-a008-aa68f538a0d7", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590122485400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "765ad903-b0e4-42f7-ab0f-4b9dcea5ccb5", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590125394500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "851ceb9e-9977-4220-95bb-77c7d1849d89", "name": "entry : default@GeneratePkgContextInfo cost memory 0.48706817626953125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590125687800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d254ff4-b550-4936-bf37-7e0b23229377", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590102884900, "endTime": 219590125832200}, "additional": {"logType": "info", "children": [], "durationId": "7a40e414-130d-4b7f-96ef-56daf78b01d8"}}, {"head": {"id": "27d30ab9-4b14-4ef4-9aad-c1c0087bc182", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590144365500, "endTime": 219590153387800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "859a8ac3-4b69-4818-8401-3408680c610e", "logId": "36970906-eb83-404d-950c-e872e2c24c3c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "859a8ac3-4b69-4818-8401-3408680c610e", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590130139900}, "additional": {"logType": "detail", "children": [], "durationId": "27d30ab9-4b14-4ef4-9aad-c1c0087bc182"}}, {"head": {"id": "13beac41-c829-4c93-b4f0-f431a6c77037", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590132766500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "679ff617-36b7-4fde-b6d0-7f9f4b82954d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590133000300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b2124e8-8624-472b-8fb4-d339b2e53f2a", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590144396000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "250d86dc-36b7-47f8-9946-7d1d03e5e257", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590151748600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9baeba8a-ce48-4023-be5c-b965509212a7", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590152190000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c20ec44a-dfbf-43bf-acc4-04077c528259", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590152602600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6095ffcf-04b3-4f30-b84a-880033dcba29", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590152730500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d693c22-af20-4eba-aedf-78a4bd606118", "name": "entry : default@ProcessIntegratedHsp cost memory 0.12368011474609375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590153068100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6e163c8-0ebf-49e8-afaf-2af1717b64e6", "name": "runTaskFromQueue task cost before running: 5 s 277 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590153282000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36970906-eb83-404d-950c-e872e2c24c3c", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590144365500, "endTime": 219590153387800, "totalTime": 8858300}, "additional": {"logType": "info", "children": [], "durationId": "27d30ab9-4b14-4ef4-9aad-c1c0087bc182"}}, {"head": {"id": "2a6e8aaf-7e9f-4527-ab5c-996e97d8548b", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590164562600, "endTime": 219590165732800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "770c51bf-e0e4-4021-b45b-d09acdbb611a", "logId": "4c0a5d7e-9ae6-42bf-9590-9e1deb6a4879"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "770c51bf-e0e4-4021-b45b-d09acdbb611a", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590159391200}, "additional": {"logType": "detail", "children": [], "durationId": "2a6e8aaf-7e9f-4527-ab5c-996e97d8548b"}}, {"head": {"id": "a09a6070-6030-4d2c-b36d-bb2f90d8466f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590162244900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ca82a6a-b3e3-4820-b15b-7aa8782a14ca", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590162500400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25f7b5ec-910a-4c14-9385-489a0aa676df", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590164596000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2db7a0ed-c498-4b1d-9f78-777763f6af71", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590164967400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da86d5e3-1cb4-42b7-89a6-43970db81174", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590165119200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "424afca5-3906-4dec-8330-4cfd4d1a108c", "name": "entry : default@BuildNativeWithCmake cost memory 0.03899383544921875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590165370600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6eb14e43-0b4f-4cc5-bd4f-2d6ae40452ad", "name": "runTaskFromQueue task cost before running: 5 s 289 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590165582800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c0a5d7e-9ae6-42bf-9590-9e1deb6a4879", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590164562600, "endTime": 219590165732800, "totalTime": 960300}, "additional": {"logType": "info", "children": [], "durationId": "2a6e8aaf-7e9f-4527-ab5c-996e97d8548b"}}, {"head": {"id": "4907cefd-f323-49b1-a568-83c6bd39625c", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590175318700, "endTime": 219590189621900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "aa889884-43ec-4877-adca-74e75f3b0ce5", "logId": "40afccfb-9684-42b9-9df9-298190ebd3df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa889884-43ec-4877-adca-74e75f3b0ce5", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590170384900}, "additional": {"logType": "detail", "children": [], "durationId": "4907cefd-f323-49b1-a568-83c6bd39625c"}}, {"head": {"id": "40c5ad1f-a3e3-4f68-b8fa-406405180295", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590173114800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "621bf8df-237a-431d-baf5-1ccb08ba5836", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590173368800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90acee04-7487-436f-b13f-748dc8512c88", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590175351100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02977191-c821-41d7-8061-4caede0e0f64", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590188193400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d1c5a09-3141-47e6-82a7-f8f25329959f", "name": "entry : default@MakePackInfo cost memory 0.1709442138671875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590188903900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40afccfb-9684-42b9-9df9-298190ebd3df", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590175318700, "endTime": 219590189621900}, "additional": {"logType": "info", "children": [], "durationId": "4907cefd-f323-49b1-a568-83c6bd39625c"}}, {"head": {"id": "85572472-fa49-4b4b-bfee-a7b6f7d84b6e", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590202259100, "endTime": 219590253852600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ac382006-95ef-4b96-9e32-eb95f3e94f26", "logId": "6cff36a7-3f66-488b-8839-f270a7476e3a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac382006-95ef-4b96-9e32-eb95f3e94f26", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590195668700}, "additional": {"logType": "detail", "children": [], "durationId": "85572472-fa49-4b4b-bfee-a7b6f7d84b6e"}}, {"head": {"id": "6ba7c2d0-24d0-4d33-a419-b579acf037c0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590198464400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23f35e5e-9f1d-4347-9358-641b2a0d7a24", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590198748700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf9e2eb1-b0c7-417d-9af3-18c0dc0e20f5", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590202328200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5badd9bb-6cc8-440a-a893-010bf6f79fca", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590202895700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "096ece9f-87b4-46e2-9a20-30904c11f3ba", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 3 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590205361000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaa66fce-ff41-48de-b9a9-65543aef4c7a", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 48 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590253505300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abdba634-5116-497d-bfc6-adb61748c33d", "name": "entry : default@SyscapTransform cost memory 0.1600494384765625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590253731500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cff36a7-3f66-488b-8839-f270a7476e3a", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590202259100, "endTime": 219590253852600}, "additional": {"logType": "info", "children": [], "durationId": "85572472-fa49-4b4b-bfee-a7b6f7d84b6e"}}, {"head": {"id": "fc91cb65-7072-44de-8250-e50747e04ac7", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590264553800, "endTime": 219590272804800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "146b1328-36c7-4b1d-89eb-a05c2e2de284", "logId": "827324b9-0546-49bc-b92f-55fe76372905"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "146b1328-36c7-4b1d-89eb-a05c2e2de284", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590258505100}, "additional": {"logType": "detail", "children": [], "durationId": "fc91cb65-7072-44de-8250-e50747e04ac7"}}, {"head": {"id": "4e78d90e-d821-474b-92f5-be5bb906d98e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590261106300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb8db454-f8e9-46aa-9688-b34f90d6e6af", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590261292800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bdcb7d7-2b98-4c74-91bb-2c47fb55bc0d", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590264588000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d3c21cb-01c4-41c9-b714-41f9c4ad9f08", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590272344300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04a01435-a4a0-4f63-8cf9-0a657923c5f6", "name": "entry : default@ProcessProfile cost memory 0.12863922119140625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590272643700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "827324b9-0546-49bc-b92f-55fe76372905", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590264553800, "endTime": 219590272804800}, "additional": {"logType": "info", "children": [], "durationId": "fc91cb65-7072-44de-8250-e50747e04ac7"}}, {"head": {"id": "db487ffd-3f78-46ab-b286-4879efdf03cc", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590284803500, "endTime": 219590304355600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3c7a17e5-68d0-4c86-a8b9-169a3cbe2ceb", "logId": "1e165d44-4098-4268-b5c7-4954fb4de756"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c7a17e5-68d0-4c86-a8b9-169a3cbe2ceb", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590276931300}, "additional": {"logType": "detail", "children": [], "durationId": "db487ffd-3f78-46ab-b286-4879efdf03cc"}}, {"head": {"id": "656acdeb-1659-47ae-8d53-0e4175456667", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590279553700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98d1dabe-f44c-4b88-9434-7a26725d9242", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590279791500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fc4b630-ef88-4efb-b9ef-0d455fd20d25", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590284841600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a7b116b-4729-4ee6-8d71-37d26aa5e67d", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590304028600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3ad4f5a-03dd-43e7-aebf-1aa65e1b35ec", "name": "entry : default@ProcessRouterMap cost memory 0.243896484375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590304257600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e165d44-4098-4268-b5c7-4954fb4de756", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590284803500, "endTime": 219590304355600}, "additional": {"logType": "info", "children": [], "durationId": "db487ffd-3f78-46ab-b286-4879efdf03cc"}}, {"head": {"id": "8ef7abad-3f73-43c9-8ca5-d9ad4d00a626", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590320007300, "endTime": 219590337861100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "0d4493fe-166b-4e6d-8fef-227da60a4120", "logId": "4f20b765-8014-4859-b479-f0206c23418c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d4493fe-166b-4e6d-8fef-227da60a4120", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590316089000}, "additional": {"logType": "detail", "children": [], "durationId": "8ef7abad-3f73-43c9-8ca5-d9ad4d00a626"}}, {"head": {"id": "d62336cc-ef48-4138-9f69-5278f0910427", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590319322500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bf9de28-1725-42cc-aead-203b39526fc5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590319764100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b50cbe96-c1cc-4f5a-90e6-e9b5e490f211", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590320027100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3519659-b06c-4199-8186-ba32d639453b", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590320360400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6da61e6c-e2c0-4dca-9a39-4410fffa09cc", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590332783100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a63abfbd-fb27-4385-9b62-b626ebde2dc2", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590333116400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd937e3c-8a43-4969-a2d7-b199293c5d1c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590333350300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdba8a63-ea70-418b-be36-15f629c587bb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590333481900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "badb5208-5763-4ee1-afdb-cc53e239de8b", "name": "entry : default@ProcessStartupConfig cost memory 0.26639556884765625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590337412300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73790a65-f4f9-47a8-8dab-b7d5266469ce", "name": "runTaskFromQueue task cost before running: 5 s 462 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590337727000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f20b765-8014-4859-b479-f0206c23418c", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590320007300, "endTime": 219590337861100, "totalTime": 17653100}, "additional": {"logType": "info", "children": [], "durationId": "8ef7abad-3f73-43c9-8ca5-d9ad4d00a626"}}, {"head": {"id": "9617eb11-1347-440e-976c-221d269ff924", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590350296500, "endTime": 219590354440800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "20aa74f2-c17c-4143-b73f-a4d4b2251cb0", "logId": "51c9a578-10a3-4b4d-be0f-477e8b340c3c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "20aa74f2-c17c-4143-b73f-a4d4b2251cb0", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590345229600}, "additional": {"logType": "detail", "children": [], "durationId": "9617eb11-1347-440e-976c-221d269ff924"}}, {"head": {"id": "9c6566a5-5707-446f-844f-7edb6caff49c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590347913100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d4cf888-894d-4413-84d5-71f97f3b4aa6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590348154800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbc9cd69-4524-4df0-9100-a6e0d583740f", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590350345400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4943a239-121b-47ab-9b9b-c15253acc756", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590350748900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef36de71-e04f-4d71-9919-72a0d9a60a62", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590350911200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3454c93f-11b7-4826-a248-ecdfca70a79a", "name": "entry : default@BuildNativeWithNinja cost memory 0.05879974365234375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590353945600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ab52470-1ee4-447d-8d51-13435de622a8", "name": "runTaskFromQueue task cost before running: 5 s 478 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590354290600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51c9a578-10a3-4b4d-be0f-477e8b340c3c", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590350296500, "endTime": 219590354440800, "totalTime": 3936300}, "additional": {"logType": "info", "children": [], "durationId": "9617eb11-1347-440e-976c-221d269ff924"}}, {"head": {"id": "4a6ae67e-e50b-4d5d-a577-4d40279519b8", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590370272000, "endTime": 219590392565800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "eab3709b-1419-4e99-8426-2d20155a7762", "logId": "5c5ef9b9-faf9-4118-b117-62b883bfd2e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eab3709b-1419-4e99-8426-2d20155a7762", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590360628500}, "additional": {"logType": "detail", "children": [], "durationId": "4a6ae67e-e50b-4d5d-a577-4d40279519b8"}}, {"head": {"id": "e78d4dc7-ef9f-4c8f-9248-42a3a841dea8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590362834700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6ad3b5a-4fe9-4af6-a809-9db43ee3b95e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590363010600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "248fe387-a9e1-45c3-b9b8-616c2609ceec", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590366748200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86a87d18-de22-4a95-9635-f62854e1bcd1", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590376938100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cba9c12-0958-4eea-ac49-3b5aebe5b918", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590386237700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09e88f97-fbdf-4a07-b875-4c2d28d12e34", "name": "entry : default@ProcessResource cost memory 0.171905517578125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590386640800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c5ef9b9-faf9-4118-b117-62b883bfd2e2", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590370272000, "endTime": 219590392565800}, "additional": {"logType": "info", "children": [], "durationId": "4a6ae67e-e50b-4d5d-a577-4d40279519b8"}}, {"head": {"id": "6be24089-d89b-4514-b033-21376448ab5c", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590410189900, "endTime": 219590455410500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "da8b12ec-4249-479a-a438-6fa0c8f28fa4", "logId": "18472c4a-0e4a-4968-9ae7-03b35feddd50"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da8b12ec-4249-479a-a438-6fa0c8f28fa4", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590401592400}, "additional": {"logType": "detail", "children": [], "durationId": "6be24089-d89b-4514-b033-21376448ab5c"}}, {"head": {"id": "1048b7cd-3110-480f-a780-9042a98c5db7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590403347300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a06793b3-01b0-43fb-a419-f45e7d032fe3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590403524500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebeb66d6-9f90-4de9-ad04-c28229662a4a", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590410217900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6f5dfd1-1f29-4eda-8478-404fde7d97cc", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 24 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590454922900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31756d92-bfe5-4fdd-bb13-22bc6b0796cd", "name": "entry : default@GenerateLoaderJson cost memory 1.1310272216796875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590455231100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18472c4a-0e4a-4968-9ae7-03b35feddd50", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590410189900, "endTime": 219590455410500}, "additional": {"logType": "info", "children": [], "durationId": "6be24089-d89b-4514-b033-21376448ab5c"}}, {"head": {"id": "b410df6d-c2fc-4110-9d2c-6dc335940567", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590480113400, "endTime": 219590491841800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "b5f9c271-56db-47c1-9e16-f3a2ed5d7da0", "logId": "0b3c8558-9f25-4421-bc7b-9f4eaa6a5036"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b5f9c271-56db-47c1-9e16-f3a2ed5d7da0", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590476480500}, "additional": {"logType": "detail", "children": [], "durationId": "b410df6d-c2fc-4110-9d2c-6dc335940567"}}, {"head": {"id": "a5b1c270-8e6f-4203-8c2a-bc72fe2e29ec", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590478335700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69565016-bc76-4c8d-8e03-c2497bb7578f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590478561500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e09be1c-65e5-45d4-9a16-d3ca0aacda46", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590480135300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d6bd72b-4bbc-445f-a0a6-0d63938d843e", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590491365100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebe34da9-9d69-4a29-9d60-845b7ad815b5", "name": "entry : default@ProcessLibs cost memory 0.1792144775390625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590491650100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b3c8558-9f25-4421-bc7b-9f4eaa6a5036", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590480113400, "endTime": 219590491841800}, "additional": {"logType": "info", "children": [], "durationId": "b410df6d-c2fc-4110-9d2c-6dc335940567"}}, {"head": {"id": "78208297-e733-4e5b-8f2c-56c8ef6ba385", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590509305400, "endTime": 219590564491000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "0742c376-5eb1-472e-8550-af823ab20137", "logId": "80306d39-eec1-400f-92cf-f4a774add775"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0742c376-5eb1-472e-8550-af823ab20137", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590498132300}, "additional": {"logType": "detail", "children": [], "durationId": "78208297-e733-4e5b-8f2c-56c8ef6ba385"}}, {"head": {"id": "2f8700d4-bcef-40dd-9b01-8542c2c62581", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590501013100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cdae809-b502-4f4d-ae88-b5ac2602a0cb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590501266300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4cf3441-ec6c-435e-80a2-7028eaf3b309", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590503659600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "321ad0e2-c2cc-4cab-865c-5423f063591b", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590509442300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af03c968-33ce-4658-b7a4-3c16ad018f1c", "name": "Incremental task entry:default@CompileResource pre-execution cost: 52 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590563689400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5742b2b1-8c05-48c8-951a-b3fbea0bb81d", "name": "entry : default@CompileResource cost memory 1.4482192993164062", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590564251700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80306d39-eec1-400f-92cf-f4a774add775", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590509305400, "endTime": 219590564491000}, "additional": {"logType": "info", "children": [], "durationId": "78208297-e733-4e5b-8f2c-56c8ef6ba385"}}, {"head": {"id": "7c714e80-3655-4a71-a5e9-29fd0601b6e8", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590578242500, "endTime": 219590584168700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "0b24cc09-b36d-4839-b8d4-eaa1b1c1d56d", "logId": "f1330592-dfbc-495d-8d3b-81cf800600f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b24cc09-b36d-4839-b8d4-eaa1b1c1d56d", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590570100600}, "additional": {"logType": "detail", "children": [], "durationId": "7c714e80-3655-4a71-a5e9-29fd0601b6e8"}}, {"head": {"id": "944bafcf-a0b8-445b-8b1c-aa6de6a613fd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590571759500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66db80f3-cf5a-46be-a5aa-e00592b3cd71", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590571955800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08745e8a-2de9-434e-bbb7-68ec131e7a8f", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590578272100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05185e87-659f-4446-814b-3ca2c578315c", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590579353200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "568cf07b-7016-4d16-b979-e22d8366445b", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590583636100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7977afe1-4e15-4e8b-b982-8d72eba3b44a", "name": "entry : default@DoNativeStrip cost memory 0.08417510986328125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590583958300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1330592-dfbc-495d-8d3b-81cf800600f0", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590578242500, "endTime": 219590584168700}, "additional": {"logType": "info", "children": [], "durationId": "7c714e80-3655-4a71-a5e9-29fd0601b6e8"}}, {"head": {"id": "bc7bbcd0-1f80-41f7-b848-add256eee508", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590602742800, "endTime": 219613191039800}, "additional": {"children": ["0321bbed-c0cb-48af-87e7-4725e01dc6ab", "e4456796-9a4f-4446-a15b-37328a5b9cae"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed."], "detailId": "360a2656-ff0e-4eaf-8b2f-84b84718c887", "logId": "2a75c9a6-da0a-405a-ba85-9bea473fe42a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "360a2656-ff0e-4eaf-8b2f-84b84718c887", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590588630700}, "additional": {"logType": "detail", "children": [], "durationId": "bc7bbcd0-1f80-41f7-b848-add256eee508"}}, {"head": {"id": "c88673b2-70da-4ea1-9042-3132cdcd4d54", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590591595100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2580c7e9-3b56-4220-83d5-ea105b04c663", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590591925700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ef3c216-9b2c-4d42-8969-e26d73447d87", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590602763800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77504ba4-7977-4a60-a784-277494fa0418", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590603501500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbb9f421-689b-4f8d-b012-a3482f6e9157", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590657372300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bc54a7f-fde8-41f7-9f93-421a479640fb", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 36 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590657658400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b17fa6cb-2992-478b-a68c-ae856faaada9", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590694284700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28503ff5-79b5-4e68-a618-10c387a37b08", "name": "default@CompileArkTS work[0] is submitted.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590697916600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0321bbed-c0cb-48af-87e7-4725e01dc6ab", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219596327570700, "endTime": 219613177208300}, "additional": {"children": ["0bc8ad4c-4dc6-4d92-9b4c-99f874e45e46", "09ded7d8-2d44-41d4-a96d-2a5427da93c9", "0c4b7bff-83d4-45e1-871f-10715b9315b5", "5cfcd9b0-7363-468f-83b5-7cab81b50231", "f02b9ec9-a37e-47c5-91bd-a2eda65e1ac7", "c84516f1-6713-4c50-9d61-2b88bff31880", "619beb26-8854-47fc-923b-c22ca8a48ec2"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "bc7bbcd0-1f80-41f7-b848-add256eee508", "logId": "dbcbda6c-ab63-4878-992c-a80645716649"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "70202199-37ae-4723-820d-b9a537532659", "name": "default@CompileArkTS work[0] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590699911600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "953da5f7-1cf6-4332-bf7c-3b8002c38b5c", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590700330900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed563e56-e352-4d2f-b840-f17a39a42e76", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590700435800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e9291b8-bc9f-4478-9961-91dbeb1401af", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590700481300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61cb5ec7-e1aa-4fdb-aee9-184119618932", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590700521900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc0fa3dd-dbe8-4682-8681-77dc0518d3e2", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590700575200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07cc14a3-7c44-476d-88f4-ffb3c21b355b", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590700615400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86471ffa-40f9-421c-8210-67c1e61c3962", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590700653800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d412eded-4f76-45a6-8f5c-5a530c05b013", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590700703200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "957945f6-e750-4d6b-88a6-f8e7f690fc95", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590700750600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2e525b2-cb77-4d26-a295-ee9b246d1e15", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590700787200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5c604cf-6dc1-4732-9b47-8bad4f94a064", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590700820200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc113a39-9eb6-4310-b866-ba17cc5447ef", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590700852700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb6054aa-be40-483a-b1f9-298670c795d7", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590700889800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44327069-5044-4081-b3ca-691537e60d57", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590700925300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acb1dad8-03f6-426d-9d9a-36233c10ad8f", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590700958100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd49bba2-65a3-4e7b-ad67-e3c9d45c033e", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590701127300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abf75146-4715-4f6f-ae23-078f8a8fa115", "name": "default@CompileArkTS work[0] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590702621700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2086532-9f3c-443e-b636-da34139be861", "name": "default@CompileArkTS work[0] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590702839500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c99745f-0ed7-4191-9c1e-3620cc853cb4", "name": "CopyResources startTime: 219590703017200", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590703023200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22a842d3-2104-4f9e-a013-5487994df5a0", "name": "default@CompileArkTS work[1] is submitted.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590703141300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4456796-9a4f-4446-a15b-37328a5b9cae", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker18", "startTime": 219592638437600, "endTime": 219592694941700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "bc7bbcd0-1f80-41f7-b848-add256eee508", "logId": "00285ac6-1dbf-474f-b0c3-f009b70b698c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "085fbdb8-fbf4-4e38-af66-ab8c1717e2f6", "name": "default@CompileArkTS work[1] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590704309000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca5720ac-015d-4edf-9b32-c53d69bcb632", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590704426100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24714347-8689-4d4f-8495-5f6e3051319d", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590704494900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33e270b6-0512-4675-9931-f5d3244389d0", "name": "default@CompileArkTS work[1] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590705644400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eba8c7bf-b7e4-4f23-a4d6-f9de5c9b9520", "name": "default@CompileArkTS work[1] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590705778500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cce93ecb-5ac8-4833-9e12-4a1009baa3bb", "name": "entry : default@CompileArkTS cost memory 2.7605514526367188", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590706028500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab13f0f7-fab8-4ba3-919b-bfbfc9b9e95c", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590715687300, "endTime": 219590728389000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "9bf05945-89f6-4d75-86e7-ba0f7dd6a4c3", "logId": "82a31000-485e-4c57-919c-f09d9f77c5f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9bf05945-89f6-4d75-86e7-ba0f7dd6a4c3", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590708482900}, "additional": {"logType": "detail", "children": [], "durationId": "ab13f0f7-fab8-4ba3-919b-bfbfc9b9e95c"}}, {"head": {"id": "50896732-3d30-4afa-a2b8-cd84f478b6c8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590709790900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c82aa4f6-a018-4ab9-8b90-4457391ad52c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590709935900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "049cc634-0b00-456d-9c99-8ffd9e8577bf", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590715713300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a18818c1-831c-48d9-878b-f42a179b3bd6", "name": "entry : default@BuildJS cost memory 0.35507965087890625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590728095700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a71c3e41-d6b1-4df9-8f85-95ef2ee18234", "name": "runTaskFromQueue task cost before running: 5 s 852 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590728313200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82a31000-485e-4c57-919c-f09d9f77c5f8", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590715687300, "endTime": 219590728389000, "totalTime": 12571400}, "additional": {"logType": "info", "children": [], "durationId": "ab13f0f7-fab8-4ba3-919b-bfbfc9b9e95c"}}, {"head": {"id": "fbe15627-5f48-49bc-917e-c3f320ca7ec1", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590736893400, "endTime": 219590742084300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "17334590-84b1-471e-ac6b-9ddbd5f1eba3", "logId": "47fc6a9d-9689-46d1-b8e1-7b58fcff4ffd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17334590-84b1-471e-ac6b-9ddbd5f1eba3", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590731553400}, "additional": {"logType": "detail", "children": [], "durationId": "fbe15627-5f48-49bc-917e-c3f320ca7ec1"}}, {"head": {"id": "58be4861-ddd8-4f83-88f7-5594edd6252f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590733207500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37b932af-4f45-4829-b26c-4cf4d9609dff", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590733372500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd83cfac-7d4e-4f57-b917-621c4f0b027a", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590736910200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6091fcc6-f7b0-47cb-b144-06763263b11a", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590738093200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "420bf2e7-3661-40a2-a348-efd9cb69f14b", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590741797900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce16f8b0-eed0-45b3-b475-135171b53a5d", "name": "entry : default@CacheNativeLibs cost memory 0.10028839111328125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590741991000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47fc6a9d-9689-46d1-b8e1-7b58fcff4ffd", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590736893400, "endTime": 219590742084300}, "additional": {"logType": "info", "children": [], "durationId": "fbe15627-5f48-49bc-917e-c3f320ca7ec1"}}, {"head": {"id": "cf452ed8-b0e5-41d7-8e99-4489ec5df47a", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219592695882500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ffb538a-a290-4853-9d31-ed0d4cdbf3b5", "name": "CopyResources is end, endTime: 219592696391800", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219592696412900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c174123f-a688-4c86-8247-e99c9f81b1bf", "name": "default@CompileArkTS work[1] done.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219592696865900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00285ac6-1dbf-474f-b0c3-f009b70b698c", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Worker18", "startTime": 219592638437600, "endTime": 219592694941700}, "additional": {"logType": "info", "children": [], "durationId": "e4456796-9a4f-4446-a15b-37328a5b9cae", "parent": "2a75c9a6-da0a-405a-ba85-9bea473fe42a"}}, {"head": {"id": "767f1833-1c6b-4b4e-930d-f1c289928e89", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219593227234900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad06729c-7d39-49c4-819b-75ced15d0f1f", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613178568300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bc8ad4c-4dc6-4d92-9b4c-99f874e45e46", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219596329009700, "endTime": 219598370707100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "0321bbed-c0cb-48af-87e7-4725e01dc6ab", "logId": "cf977ccd-b324-4dea-b1d0-e5046275d736"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf977ccd-b324-4dea-b1d0-e5046275d736", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219596329009700, "endTime": 219598370707100}, "additional": {"logType": "info", "children": [], "durationId": "0bc8ad4c-4dc6-4d92-9b4c-99f874e45e46", "parent": "dbcbda6c-ab63-4878-992c-a80645716649"}}, {"head": {"id": "09ded7d8-2d44-41d4-a96d-2a5427da93c9", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219598373934400, "endTime": 219598520082900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "0321bbed-c0cb-48af-87e7-4725e01dc6ab", "logId": "d3d778b6-3a4a-47ac-acd4-4d6aba2d61dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3d778b6-3a4a-47ac-acd4-4d6aba2d61dc", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219598373934400, "endTime": 219598520082900}, "additional": {"logType": "info", "children": [], "durationId": "09ded7d8-2d44-41d4-a96d-2a5427da93c9", "parent": "dbcbda6c-ab63-4878-992c-a80645716649"}}, {"head": {"id": "0c4b7bff-83d4-45e1-871f-10715b9315b5", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219598520259100, "endTime": 219598520628800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "0321bbed-c0cb-48af-87e7-4725e01dc6ab", "logId": "e7296e25-605d-4fa3-a37e-1199b37e1f56"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7296e25-605d-4fa3-a37e-1199b37e1f56", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219598520259100, "endTime": 219598520628800}, "additional": {"logType": "info", "children": [], "durationId": "0c4b7bff-83d4-45e1-871f-10715b9315b5", "parent": "dbcbda6c-ab63-4878-992c-a80645716649"}}, {"head": {"id": "5cfcd9b0-7363-468f-83b5-7cab81b50231", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219598520730600, "endTime": 219612783132300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "0321bbed-c0cb-48af-87e7-4725e01dc6ab", "logId": "5da0414a-63bd-4057-a4f1-0e4abbf3ba8e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5da0414a-63bd-4057-a4f1-0e4abbf3ba8e", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219598520730600, "endTime": 219612783132300}, "additional": {"logType": "info", "children": [], "durationId": "5cfcd9b0-7363-468f-83b5-7cab81b50231", "parent": "dbcbda6c-ab63-4878-992c-a80645716649"}}, {"head": {"id": "f02b9ec9-a37e-47c5-91bd-a2eda65e1ac7", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219612783492900, "endTime": 219612810460300}, "additional": {"children": ["80bb1e2b-4267-43ce-99c0-5098669bd21d", "dfd95308-9f67-4015-bacf-e5b638df3b46", "7263455a-59a3-49c7-8c66-d0b3c2019b91"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "0321bbed-c0cb-48af-87e7-4725e01dc6ab", "logId": "57a1be80-8a3d-4d12-a286-c5a7134e4ab9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "57a1be80-8a3d-4d12-a286-c5a7134e4ab9", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219612783492900, "endTime": 219612810460300}, "additional": {"logType": "info", "children": ["91e4cd5d-d02f-442d-ada4-cece975594ed", "222b45dc-74f6-442d-b4ea-40974634d629", "b7e2ba70-b556-472d-aca9-ee72771ca8d3"], "durationId": "f02b9ec9-a37e-47c5-91bd-a2eda65e1ac7", "parent": "dbcbda6c-ab63-4878-992c-a80645716649"}}, {"head": {"id": "80bb1e2b-4267-43ce-99c0-5098669bd21d", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219612783697300, "endTime": 219612783726100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f02b9ec9-a37e-47c5-91bd-a2eda65e1ac7", "logId": "91e4cd5d-d02f-442d-ada4-cece975594ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "91e4cd5d-d02f-442d-ada4-cece975594ed", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219612783697300, "endTime": 219612783726100}, "additional": {"logType": "info", "children": [], "durationId": "80bb1e2b-4267-43ce-99c0-5098669bd21d", "parent": "57a1be80-8a3d-4d12-a286-c5a7134e4ab9"}}, {"head": {"id": "dfd95308-9f67-4015-bacf-e5b638df3b46", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219612783741400, "endTime": 219612804639100}, "additional": {"children": ["f93bbe67-fdf9-42b4-858f-a034608a6beb"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f02b9ec9-a37e-47c5-91bd-a2eda65e1ac7", "logId": "222b45dc-74f6-442d-b4ea-40974634d629"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "222b45dc-74f6-442d-b4ea-40974634d629", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219612783741400, "endTime": 219612804639100}, "additional": {"logType": "info", "children": ["bb7bf01a-d013-4f96-b3db-eaf39fd02550"], "durationId": "dfd95308-9f67-4015-bacf-e5b638df3b46", "parent": "57a1be80-8a3d-4d12-a286-c5a7134e4ab9"}}, {"head": {"id": "f93bbe67-fdf9-42b4-858f-a034608a6beb", "name": "module 'ApiService.ets' pack", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219612789106600, "endTime": 219612802593200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "dfd95308-9f67-4015-bacf-e5b638df3b46", "logId": "bb7bf01a-d013-4f96-b3db-eaf39fd02550"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bb7bf01a-d013-4f96-b3db-eaf39fd02550", "name": "module 'ApiService.ets' pack", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219612789106600, "endTime": 219612802593200}, "additional": {"logType": "info", "children": [], "durationId": "f93bbe67-fdf9-42b4-858f-a034608a6beb", "parent": "222b45dc-74f6-442d-b4ea-40974634d629"}}, {"head": {"id": "7263455a-59a3-49c7-8c66-d0b3c2019b91", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219612804653800, "endTime": 219612810417100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f02b9ec9-a37e-47c5-91bd-a2eda65e1ac7", "logId": "b7e2ba70-b556-472d-aca9-ee72771ca8d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b7e2ba70-b556-472d-aca9-ee72771ca8d3", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219612804653800, "endTime": 219612810417100}, "additional": {"logType": "info", "children": [], "durationId": "7263455a-59a3-49c7-8c66-d0b3c2019b91", "parent": "57a1be80-8a3d-4d12-a286-c5a7134e4ab9"}}, {"head": {"id": "c84516f1-6713-4c50-9d61-2b88bff31880", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219612810693500, "endTime": 219613172126700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "0321bbed-c0cb-48af-87e7-4725e01dc6ab", "logId": "d98b5c82-c2db-4ff7-af4b-129fb764bdca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d98b5c82-c2db-4ff7-af4b-129fb764bdca", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219612810693500, "endTime": 219613172126700}, "additional": {"logType": "info", "children": [], "durationId": "c84516f1-6713-4c50-9d61-2b88bff31880", "parent": "dbcbda6c-ab63-4878-992c-a80645716649"}}, {"head": {"id": "619beb26-8854-47fc-923b-c22ca8a48ec2", "name": "load compilation dependencies", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219592346163600, "endTime": 219596324835700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "0321bbed-c0cb-48af-87e7-4725e01dc6ab", "logId": "769fce50-55a8-43d1-a0ff-73a1e155d968"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "769fce50-55a8-43d1-a0ff-73a1e155d968", "name": "load compilation dependencies", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219592346163600, "endTime": 219596324835700}, "additional": {"logType": "info", "children": [], "durationId": "619beb26-8854-47fc-923b-c22ca8a48ec2", "parent": "dbcbda6c-ab63-4878-992c-a80645716649"}}, {"head": {"id": "65ca1d51-aa8d-4142-ab63-1838f436816c", "name": "default@CompileArkTS work[0] done.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613190285500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbcbda6c-ab63-4878-992c-a80645716649", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219596327570700, "endTime": 219613177208300}, "additional": {"logType": "info", "children": ["cf977ccd-b324-4dea-b1d0-e5046275d736", "d3d778b6-3a4a-47ac-acd4-4d6aba2d61dc", "e7296e25-605d-4fa3-a37e-1199b37e1f56", "5da0414a-63bd-4057-a4f1-0e4abbf3ba8e", "57a1be80-8a3d-4d12-a286-c5a7134e4ab9", "d98b5c82-c2db-4ff7-af4b-129fb764bdca", "769fce50-55a8-43d1-a0ff-73a1e155d968"], "durationId": "0321bbed-c0cb-48af-87e7-4725e01dc6ab", "parent": "2a75c9a6-da0a-405a-ba85-9bea473fe42a"}}, {"head": {"id": "9e9889e8-b0bd-421c-860b-16abd0fee948", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613190857500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a75c9a6-da0a-405a-ba85-9bea473fe42a", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219590602742800, "endTime": 219613191039800, "totalTime": 17009521700}, "additional": {"logType": "info", "children": ["dbcbda6c-ab63-4878-992c-a80645716649", "00285ac6-1dbf-474f-b0c3-f009b70b698c"], "durationId": "bc7bbcd0-1f80-41f7-b848-add256eee508"}}, {"head": {"id": "7de7d126-d28b-4180-aa4d-e751dfc8b111", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613208513300, "endTime": 219613211488700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "d63f657d-9071-4242-b39b-a563e0688640", "logId": "35370e33-22fc-4b52-9b3b-da05ef19e6cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d63f657d-9071-4242-b39b-a563e0688640", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613202050000}, "additional": {"logType": "detail", "children": [], "durationId": "7de7d126-d28b-4180-aa4d-e751dfc8b111"}}, {"head": {"id": "76128dbd-45ad-4dc9-ab13-f6f1ba4fa37a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613204265000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "666851cd-2ffb-450e-a331-b08ec41087cf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613204471600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd272f04-34a6-46c9-9eb6-d82373aa3e0f", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613208553100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "479fd3de-3e75-4c12-a55c-a6e89273ea1b", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613209498700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60474c21-6341-437c-82e7-0e32165765f5", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613211214100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cffdddbc-89a8-4b3e-a7fd-30775544b45f", "name": "entry : default@GeneratePkgModuleJson cost memory 0.077239990234375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613211407000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35370e33-22fc-4b52-9b3b-da05ef19e6cd", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613208513300, "endTime": 219613211488700}, "additional": {"logType": "info", "children": [], "durationId": "7de7d126-d28b-4180-aa4d-e751dfc8b111"}}, {"head": {"id": "a4a39e60-245f-459a-9df0-5a7428cadf35", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613237168100, "endTime": 219613621022900}, "additional": {"children": ["5cc4187b-4409-43c1-9b73-512bffa73f08", "eef8ccc4-09be-4641-83ba-1631a488ad20"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "9285d1c9-f15a-4228-a310-f583837c1bbc", "logId": "049bec6c-60d5-4351-b9a8-bc84d8d2b79f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9285d1c9-f15a-4228-a310-f583837c1bbc", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613216579100}, "additional": {"logType": "detail", "children": [], "durationId": "a4a39e60-245f-459a-9df0-5a7428cadf35"}}, {"head": {"id": "8e41cb8d-0673-4955-a8d0-5e1826fca06d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613218254400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a58be007-bea3-49c5-8db7-b84c8d9488ce", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613218466700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42e43a82-9935-46f7-9689-64754f1cb37a", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613237194900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99eaa71b-8451-4605-b4de-9a05c4b8da5b", "name": "entry:default@PackageHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613276746200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5673bb5e-19f0-4da7-a586-8b2ffe0313b9", "name": "Incremental task entry:default@PackageHap pre-execution cost: 34 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613276986600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff0ce9d3-f20f-4038-bd65-e72c6e6b8064", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613277129700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "117c0187-d15e-41af-9d57-ca781b76e4ce", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613277196500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cc4187b-4409-43c1-9b73-512bffa73f08", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613279679700, "endTime": 219613285416900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a4a39e60-245f-459a-9df0-5a7428cadf35", "logId": "21ebfbae-e930-4eb4-8527-a1ab4763e954"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "049b8a9d-7cfe-4fae-8e77-51602f5563e1", "name": "Use tool [C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Huawei\\\\Sdk\\\\OpenHarmony\\\\Sdk\\\\18\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--rpcid-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\syscap\\\\default\\\\rpcid.sc',\n  '--ets-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets',\n  '--pkg-context-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader\\\\default\\\\pkgContextInfo.json'\n]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613285133500}, "additional": {"logType": "debug", "children": [], "durationId": "a4a39e60-245f-459a-9df0-5a7428cadf35"}}, {"head": {"id": "21ebfbae-e930-4eb4-8527-a1ab4763e954", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613279679700, "endTime": 219613285416900}, "additional": {"logType": "info", "children": [], "durationId": "5cc4187b-4409-43c1-9b73-512bffa73f08", "parent": "049bec6c-60d5-4351-b9a8-bc84d8d2b79f"}}, {"head": {"id": "eef8ccc4-09be-4641-83ba-1631a488ad20", "name": "submit HAP packaging task to java daemon server", "description": "Pack HAP in java daemon", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613286643200, "endTime": 219613607425100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a4a39e60-245f-459a-9df0-5a7428cadf35", "logId": "aab95b48-a696-41a0-8739-1f8f38c5b023"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "93fa29c1-0ea9-4178-94ac-a88f73e3ae70", "name": "java daemon socket received message:{\"code\":0,\"message\":\"pack success\"}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613605652000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aab95b48-a696-41a0-8739-1f8f38c5b023", "name": "submit HAP packaging task to java daemon server", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613286643200, "endTime": 219613607407800}, "additional": {"logType": "info", "children": [], "durationId": "eef8ccc4-09be-4641-83ba-1631a488ad20", "parent": "049bec6c-60d5-4351-b9a8-bc84d8d2b79f"}}, {"head": {"id": "d0925f18-b031-43c0-b828-02519cb36eee", "name": "entry : default@PackageHap cost memory 0.9845657348632812", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613620310600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de42afc9-b76e-4198-b90e-21e3dd0fa651", "name": "runTaskFromQueue task cost before running: 28 s 745 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613620731200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "049bec6c-60d5-4351-b9a8-bc84d8d2b79f", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613237168100, "endTime": 219613621022900, "totalTime": 383472100}, "additional": {"logType": "info", "children": ["21ebfbae-e930-4eb4-8527-a1ab4763e954", "aab95b48-a696-41a0-8739-1f8f38c5b023"], "durationId": "a4a39e60-245f-459a-9df0-5a7428cadf35"}}, {"head": {"id": "2591e32e-3c15-41fb-adaa-fa8f2e7c87d4", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613641659000, "endTime": 219614046930900}, "additional": {"children": ["d4180107-b59b-4f14-a289-516619a300cf", "a1481393-4a58-4794-b7fa-f2f09ad25c4c"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "2188a3ab-997d-44a8-bf61-b1360058ba8b", "logId": "f05819c3-6f9c-496a-b361-2a8d62cff5dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2188a3ab-997d-44a8-bf61-b1360058ba8b", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613631442000}, "additional": {"logType": "detail", "children": [], "durationId": "2591e32e-3c15-41fb-adaa-fa8f2e7c87d4"}}, {"head": {"id": "9ab76cfd-c768-40cc-a361-812cb8818e1b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613633955500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3620c7b8-50f7-4b52-bde9-a6a63cb8c266", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613634220600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e63670a-b906-43ed-9bbd-54a6049f6d39", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613641694200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a085d170-a355-4e08-b2d7-740c2c4d1290", "name": "entry:default@SignHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613649078900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09d08e97-0b20-4dd1-aa59-4526235fe489", "name": "Incremental task entry:default@SignHap pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613649388000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b5a7ba6-c7c5-4f6d-9e7d-f7224a291139", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613649559300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "627c44b3-0b6a-4c9f-880c-bb2ebee3cf64", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613649653900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4180107-b59b-4f14-a289-516619a300cf", "name": "generate hap signing command", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613652435700, "endTime": 219613769656700}, "additional": {"children": ["a0d01a21-c8d5-4082-a7cd-96d69d25cff5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2591e32e-3c15-41fb-adaa-fa8f2e7c87d4", "logId": "575268fa-5620-46cf-a6bc-a911fe646921"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0d01a21-c8d5-4082-a7cd-96d69d25cff5", "name": "verify signing configuration and get bundle name", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613685251700, "endTime": 219613767779700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d4180107-b59b-4f14-a289-516619a300cf", "logId": "639a5da0-2d5e-4930-bcdc-760abd1fc410"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fca51475-c9bd-40fe-a356-6cb8e86a3174", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613690890500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19f43e53-abb8-4334-8f9c-cfef85bd97f1", "name": "java daemon socket received message:{\"code\":0,\"message\":\"verify profile success\"}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613767183000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "639a5da0-2d5e-4930-bcdc-760abd1fc410", "name": "verify signing configuration and get bundle name", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613685251700, "endTime": 219613767779700}, "additional": {"logType": "info", "children": [], "durationId": "a0d01a21-c8d5-4082-a7cd-96d69d25cff5", "parent": "575268fa-5620-46cf-a6bc-a911fe646921"}}, {"head": {"id": "575268fa-5620-46cf-a6bc-a911fe646921", "name": "generate hap signing command", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613652435700, "endTime": 219613769656700}, "additional": {"logType": "info", "children": ["639a5da0-2d5e-4930-bcdc-760abd1fc410"], "durationId": "d4180107-b59b-4f14-a289-516619a300cf", "parent": "f05819c3-6f9c-496a-b361-2a8d62cff5dd"}}, {"head": {"id": "a1481393-4a58-4794-b7fa-f2f09ad25c4c", "name": "execute hap signing command", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613770744800, "endTime": 219614045572000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2591e32e-3c15-41fb-adaa-fa8f2e7c87d4", "logId": "8c21593b-1957-4be1-b144-a80b600794f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4be7b9e1-6083-45c1-85b3-0a3e02d3b445", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613774448200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e72f17cd-91ed-4c92-a7e2-31a9d1f6acc1", "name": "java daemon socket received message:{\"code\":0,\"message\":\"sign app success\"}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614044391700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c21593b-1957-4be1-b144-a80b600794f5", "name": "execute hap signing command", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613770744800, "endTime": 219614045572000}, "additional": {"logType": "info", "children": [], "durationId": "a1481393-4a58-4794-b7fa-f2f09ad25c4c", "parent": "f05819c3-6f9c-496a-b361-2a8d62cff5dd"}}, {"head": {"id": "8558a435-2486-4c42-9367-190023261e84", "name": "entry : default@SignHap cost memory 0.07697296142578125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614046328700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69c10456-07bc-4c0c-882e-3a51aadf5102", "name": "runTaskFromQueue task cost before running: 29 s 171 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614046757200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f05819c3-6f9c-496a-b361-2a8d62cff5dd", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219613641659000, "endTime": 219614046930900, "totalTime": 405026500}, "additional": {"logType": "info", "children": ["575268fa-5620-46cf-a6bc-a911fe646921", "8c21593b-1957-4be1-b144-a80b600794f5"], "durationId": "2591e32e-3c15-41fb-adaa-fa8f2e7c87d4"}}, {"head": {"id": "5aa131f7-8f3a-43a8-9ccb-3e2055638b86", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614057142200, "endTime": 219614078389800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e33675b6-da50-47b9-af2c-4768e5b17a81", "logId": "b48dfd76-8dab-4a29-aa43-44b72df04cf4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e33675b6-da50-47b9-af2c-4768e5b17a81", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614051805800}, "additional": {"logType": "detail", "children": [], "durationId": "5aa131f7-8f3a-43a8-9ccb-3e2055638b86"}}, {"head": {"id": "a9502e3c-0f65-4494-ac3a-35d8fe42cc9a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614054455100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0403c902-240f-44d6-b70a-8f24496cc65c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614054877500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "369d4866-0e3b-4e4c-b93b-6d17849f966d", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614057172200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "618c53fa-d29b-4a79-bed7-b23fb39cfe67", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614077394600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d86a21fd-763c-44f3-8a21-5e94536acb4f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614077642700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25e42301-f115-489f-a007-7da6d2c66780", "name": "entry : default@CollectDebugSymbol cost memory -0.6028289794921875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614078002700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b7b28b6-79eb-4e8f-a8e7-438a44d71907", "name": "runTaskFromQueue task cost before running: 29 s 202 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614078205800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b48dfd76-8dab-4a29-aa43-44b72df04cf4", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614057142200, "endTime": 219614078389800, "totalTime": 21013100}, "additional": {"logType": "info", "children": [], "durationId": "5aa131f7-8f3a-43a8-9ccb-3e2055638b86"}}, {"head": {"id": "29ab766e-53b7-4fd4-a341-cf3c8dcf9c34", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614083976700, "endTime": 219614084810700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "4cc6a278-7d9f-4748-9a44-1152b6c59a56", "logId": "9944f7ec-f7d3-442f-9b50-ea370b4f80c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4cc6a278-7d9f-4748-9a44-1152b6c59a56", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614083843900}, "additional": {"logType": "detail", "children": [], "durationId": "29ab766e-53b7-4fd4-a341-cf3c8dcf9c34"}}, {"head": {"id": "7974ef65-8e0d-4d0c-8f33-c85e9b6f6bf6", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614083999600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7fe6a41-451c-46ed-9191-9f26f360fcda", "name": "entry : assembleHap cost memory 0.012054443359375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614084411400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11f8ff2e-9069-4df2-9cb7-705b3c897445", "name": "runTaskFromQueue task cost before running: 29 s 208 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614084673900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9944f7ec-f7d3-442f-9b50-ea370b4f80c4", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614083976700, "endTime": 219614084810700, "totalTime": 645500}, "additional": {"logType": "info", "children": [], "durationId": "29ab766e-53b7-4fd4-a341-cf3c8dcf9c34"}}, {"head": {"id": "3e72a7f7-6d3d-45ac-9803-0309f85215af", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614119510000, "endTime": 219614119845100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c6e0b37a-62db-4363-9335-4da480e88b86", "logId": "9796deb3-6daa-40ea-ba23-f032dded0c4f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9796deb3-6daa-40ea-ba23-f032dded0c4f", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614119510000, "endTime": 219614119845100}, "additional": {"logType": "info", "children": [], "durationId": "3e72a7f7-6d3d-45ac-9803-0309f85215af"}}, {"head": {"id": "27705509-571c-4295-b6a5-07ee0cdbd353", "name": "BUILD SUCCESSFUL in 29 s 244 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614120010800}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "e3fc76e1-a30d-4697-a970-850a10d7b7cf", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219584876645000, "endTime": 219614122148600}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 18, "minute": 57, "second": 23}, "completeCommand": "{\"prop\":[\"product=default\"],\"mode\":\"module\",\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "9d00aaea-120f-4c37-9938-a95c9463b012", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614122657900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcf57ac2-dc45-4733-8c91-b32ae6e0799a", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614123657200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "959c336f-8112-4bb2-8d10-71fc3e04f7ba", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614124887400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d1e8f60-e9c9-4ebb-bb4e-3350cec0a75b", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614125103700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dcaeccb-a002-4e77-942f-cfbc650dc2f8", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614125247000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d593caa-1268-449a-a034-3af1023cde3b", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614125377700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c96d8009-b80b-47b6-b24a-5f602fbd880a", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614125467100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3616b70b-08f8-4b8d-b172-fda90a0d34b0", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614126969900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a70678d-cafd-420e-ab0e-b0ed108c8e00", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614127573300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d75162d9-f790-4a3c-b2b1-276942eec9eb", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614127806100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e7f8d54-9a79-4145-b586-719bdaa3f929", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614127919400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ea13503-139e-4307-bb40-95b17de8f2e8", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614128016600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc5824ad-47a9-47a7-b8fc-f7a02a3f9f97", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614128097800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4afe3520-4d5e-4e8f-9928-a61d3da453da", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614131703900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fb24975-037d-4daf-a8a6-364716808ed4", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614132912900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91b1042e-d58a-4a76-b45a-b7319edd9c33", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614133722100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "387fc2a9-1ae9-4e4a-937d-f0d32192fa2c", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614133925100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9c085e9-6a50-4708-a5a7-117e009ba9b5", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614134089200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d39c74c2-95b4-466c-9cb8-b6f1308fe862", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614134252800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfdbac20-daeb-410f-b00c-aa0e5b535fb5", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614134481200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7af4f64c-1a73-40dc-93fe-02feffeeede5", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614134675700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "579b9561-4496-4406-863a-5a314c3f2ad4", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614147500600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e08a791-10eb-4dbe-b759-d8a0c265b8a4", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614149297400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "260aa86f-b6ea-4ab1-89cc-c96c970299fd", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614150757500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19050243-452b-4d4b-8b98-cd5b3349d125", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614151881900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f9ef16b-5e7d-4609-abd0-19c975374b80", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614152549100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92936a7b-d02f-45db-927f-8be25e15600c", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614155864000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6141cf30-5154-4c04-9f48-da581736274b", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614156217300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fc26d0d-9eaa-4d03-ba12-377646ea1de2", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614157182500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98d95898-391d-4c4c-847b-79f22d36aed6", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614158587200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f70942b2-37a4-43d1-aeea-c8a4632cf204", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614161875300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbe50289-4f77-40f2-96b0-2e9e22a9c07d", "name": "Incremental task entry:default@CompileArkTS post-execution cost:31 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614164999400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "015bcb21-2123-4067-b612-60c23de22b04", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614173257300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a06a02a9-1144-48f0-a645-94a499bde3e4", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614176303000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6b3a044-98a7-4eba-922f-337ca48c2824", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614177288900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e55bde0f-5899-4521-8e3e-dccf882f7a0a", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614178015500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08e775bf-92d6-4af0-b09e-fb25d1caf859", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614178577700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "965b8cd8-4f0b-409f-ac65-ff4e71b120e7", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614181086200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be39641f-03e7-46b3-941c-b15a90c5e08d", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614183513300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15d1a568-4a88-4229-94d2-c589215b2747", "name": "Incremental task entry:default@BuildJS post-execution cost:19 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614184203200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d8e2e24-b77d-462b-be6b-83d1188a53b3", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614184411200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5a72f47-71fe-4d55-ab48-f70c1058dd5e", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614184541900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62a084df-290c-4c60-a876-4d45925d9130", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614189019300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef3c1c5a-9ef0-42bb-8ea9-d2006fb28140", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614190959400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a636cc6d-76cb-46c6-9da7-2c08effe3cde", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614191710800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5d233d8-54c3-450d-8c88-74e12b822ce1", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614214802900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50456da2-6936-439a-b5c8-abfff8bc6f36", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614215611500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f80661c-a544-494b-9489-5d6cae1b36da", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614216575600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11b94814-e9e7-4698-91f1-b48d4bd1cdff", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614217746100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d7d2767-79ca-464d-8cec-1355c129b1b7", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614218015500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4bf4d13-4b33-4ea7-b02a-9f4a74a73fbb", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614219033500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf65654e-7acf-43c5-a192-953bcf389a3b", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614220090200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fa26888-150f-4321-9192-ce3a50a2c140", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614223063200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc56c8b8-f196-4dd7-88a2-9432b0ec8cfa", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614223885100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1080965d-c57c-45d6-a791-84e9976a4e46", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614224600400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a57f94ff-a4c6-4440-a365-53470736259c", "name": "Incremental task entry:default@PackageHap post-execution cost:41 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614225359800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55834264-89bf-4d35-8e8d-a91855039a2b", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.cer cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614226052000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e3290f3-2219-4d5b-aa3c-266d51b290c7", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p7b cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614226633300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a64ea8e5-2ad5-46ad-aefe-35b8a653d3b3", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p12 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614227122100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "328a75c6-5091-4b8d-9fa2-dd8db50e380f", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614227612400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b29e2731-74c9-467b-87fd-fcf0ac2992f0", "name": "Update task entry:default@SignHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614227794200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66333760-c46d-4d48-a671-76154f5dcefd", "name": "Incremental task entry:default@SignHap post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614228365400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e614753-4451-4844-99b5-6a2f56b32940", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614235716600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bd2ee37-c339-4f2f-9d06-26f843781705", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614236505400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a30757b0-df17-4390-a949-ee2dc6ace5c2", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614239374000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d93c8456-79d5-4352-8e96-e04594efffbb", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:12 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614240310600}, "additional": {"logType": "debug", "children": []}}], "workLog": []}