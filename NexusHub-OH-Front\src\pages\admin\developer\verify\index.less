.filterBar {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.detailModal {
  .ant-descriptions-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
  }
  
  .ant-descriptions-item-label {
    font-weight: 500;
    background: #fafafa;
  }
  
  .imageItem {
    text-align: center;
    
    .ant-typography-title {
      margin-bottom: 12px;
      font-size: 14px;
    }
    
    .ant-image {
      border: 1px solid #d9d9d9;
      border-radius: 6px;
    }
  }
}

.ant-table-wrapper {
  .ant-table-thead > tr > th {
    background: #fafafa;
    font-weight: 600;
  }
  
  .ant-table-tbody > tr:hover > td {
    background: #f5f5f5;
  }
}

@media (max-width: 768px) {
  .filterBar {
    padding: 12px;
    
    .ant-radio-group {
      .ant-radio-button-wrapper {
        font-size: 12px;
        padding: 0 8px;
      }
    }
  }
  
  .detailModal {
    .ant-descriptions {
      .ant-descriptions-item-label,
      .ant-descriptions-item-content {
        padding: 8px;
      }
    }
    
    .imageItem {
      margin-bottom: 16px;
      
      .ant-image {
        width: 100px !important;
        height: 100px !important;
      }
    }
  }
}