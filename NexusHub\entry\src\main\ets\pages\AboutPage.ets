import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';
import { LoadingView, LoadingState } from '../components/LoadingView';
import { router } from '@kit.ArkUI';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { LengthMetrics } from '@kit.ArkUI';

/**
 * 团队成员模型
 */
interface TeamMember {
  id: number;
  name: string;
  role: string;
  avatar: string;
  description: string;
}

/**
 * 更新日志模型
 */
interface UpdateLog {
  version: string;
  date: string;
  features: string[];
  fixes: string[];
}

/**
 * 应用信息接口
 */
interface AppInfo {
  name: string;
  version: string;
  buildNumber: string;
  description: string;
  developer: string;
  website: string;
  email: string;
  privacy: string;
  terms: string;
  license: string;
  copyright: string;
  features: string[];
  technologies: string[];
}

/**
 * 关于页面
 */
@Entry
@Component
struct AboutPage {
  @State selectedTab: number = 0; // 0: 应用信息, 1: 团队介绍, 2: 更新日志
  @State appInfo: AppInfo = {} as AppInfo;
  @State teamMembers: TeamMember[] = [];
  @State updateLogs: UpdateLog[] = [];
  @State loadingState: LoadingState = LoadingState.SUCCESS;

  private deviceUtils = DeviceUtils.getInstance();

  aboutToAppear() {
    this.loadAppInfo();
    this.loadTeamMembers();
    this.loadUpdateLogs();
  }

  /**
   * 加载应用信息
   */
  private loadAppInfo() {
    this.appInfo = {
      name: 'NexusHub',
      version: '1.0.0',
      buildNumber: '100',
      description: 'NexusHub是一个专为HarmonyOS设计的应用商店客户端，提供丰富的应用资源和优质的用户体验。',
      developer: 'NexusHub团队',
      website: 'https://nexushub.com',
      email: '<EMAIL>',
      privacy: 'https://nexushub.com/privacy',
      terms: 'https://nexushub.com/terms',
      license: 'MIT License',
      copyright: '© 2024 NexusHub团队. All rights reserved.',
      features: [
        '🔍 智能搜索和推荐',
        '📱 精美的应用界面',
        '⚡ 快速下载和安装',
        '❤️ 收藏和评价功能',
        '🔒 安全可靠的应用审核',
        '🌟 个性化推荐算法'
      ],
      technologies: [
        'HarmonyOS SDK',
        'ArkTS',
        'ArkUI',
        'TypeScript',
      ]
    };
  }

  /**
   * 加载团队成员
   */
  private loadTeamMembers() {
    this.teamMembers = [
      {
        id: 1,
        name: '张三',
        role: '产品经理',
        avatar: '👨‍💼',
        description: '负责产品规划和用户体验设计，拥有5年移动应用产品经验。'
      },
      {
        id: 2,
        name: '李四',
        role: '技术负责人',
        avatar: '👨‍💻',
        description: '资深HarmonyOS开发工程师，专注于应用架构和性能优化。'
      },
      {
        id: 3,
        name: '王五',
        avatar: '👩‍💻',
        role: '前端开发',
        description: '专业的UI/UX开发工程师，致力于打造优秀的用户界面。'
      },
      {
        id: 4,
        name: '赵六',
        role: '后端开发',
        avatar: '👨‍🔧',
        description: '后端架构师，负责服务器端开发和数据库设计。'
      },
      {
        id: 5,
        name: '钱七',
        role: 'UI设计师',
        avatar: '👩‍🎨',
        description: '创意设计师，为应用提供美观且实用的视觉设计。'
      },
      {
        id: 6,
        name: '孙八',
        role: '测试工程师',
        avatar: '👨‍🔬',
        description: '质量保证专家，确保应用的稳定性和可靠性。'
      }
    ];
  }

  /**
   * 加载更新日志
   */
  private loadUpdateLogs() {
    this.updateLogs = [
      {
        version: '1.0.0',
        date: '2024-01-15',
        features: [
          '🎉 NexusHub正式发布',
          '📱 支持应用搜索和浏览',
          '⬇️ 应用下载和安装功能',
          '❤️ 收藏和评价系统',
          '👤 用户账号管理',
          '🔔 消息通知功能'
        ],
        fixes: []
      },
      {
        version: '0.9.5',
        date: '2024-01-10',
        features: [
          '🔍 优化搜索算法',
          '🎨 更新UI设计',
          '⚡ 提升应用启动速度'
        ],
        fixes: [
          '修复下载进度显示问题',
          '解决部分设备兼容性问题',
          '优化内存使用'
        ]
      },
      {
        version: '0.9.0',
        date: '2024-01-05',
        features: [
          '📊 添加应用统计功能',
          '🌙 支持深色模式',
          '🔄 自动更新检查'
        ],
        fixes: [
          '修复登录状态异常',
          '解决图片加载失败问题',
          '优化网络请求性能'
        ]
      }
    ];
  }

  /**
   * 打开外部链接
   */
  private openExternalLink(url: string) {
    // 这里应该调用系统浏览器打开链接
    hilog.info(0x0000, 'AboutPage', '打开链接: %{public}s', url);
  }

  /**
   * 标签栏
   */
  @Builder
  private TabBar() {
    Row() {
      ForEach(['应用信息', '团队介绍', '更新日志'], (title: string, index: number) => {
        Text(title)
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontColor(this.selectedTab === index ? Constants.COLORS.PRIMARY : Constants.COLORS.TEXT_SECONDARY)
          .fontWeight(this.selectedTab === index ? FontWeight.Bold : FontWeight.Normal)
          .padding({ left: 16, right: 16, top: 12, bottom: 12 })
          .onClick(() => {
            this.selectedTab = index;
          })
      }, (title: string) => title)
    }
    .width('100%')
    .backgroundColor(Constants.COLORS.WHITE)
    .justifyContent(FlexAlign.SpaceAround)
  }

  /**
   * 应用信息内容
   */
  @Builder
  private AppInfoContent() {
    Scroll() {
      Column({ space: 16 }) {
        // 应用图标和基本信息
        Column({ space: 12 }) {
          Text('📱')
            .fontSize(64)
            .fontColor(Constants.COLORS.PRIMARY)

          Text(this.appInfo.name)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.EXTRA_LARGE))
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .fontWeight(FontWeight.Bold)

          Text(`版本 ${this.appInfo.version} (${this.appInfo.buildNumber})`)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor(Constants.COLORS.TEXT_SECONDARY)

          Text(this.appInfo.description)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor(Constants.COLORS.TEXT_SECONDARY)
            .textAlign(TextAlign.Center)
            .lineHeight(20)
            .margin({ left: 16, right: 16 })
        }
        .width('100%')
        .padding(16)
        .backgroundColor(Constants.COLORS.WHITE)
        .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
        .alignItems(HorizontalAlign.Center)

        // 核心功能
        Column({ space: 12 }) {
          Text('✨ 核心功能')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .fontWeight(FontWeight.Bold)
            .width('100%')
            .textAlign(TextAlign.Start)

          Column({ space: 8 }) {
            ForEach(this.appInfo.features, (feature: string) => {
              Text(feature)
                .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
                .fontColor(Constants.COLORS.TEXT_SECONDARY)
                .width('100%')
                .textAlign(TextAlign.Start)
            }, (feature: string) => feature)
          }
          .width('100%')
        }
        .width('100%')
        .padding(16)
        .backgroundColor(Constants.COLORS.WHITE)
        .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
        .alignItems(HorizontalAlign.Start)

        // 技术栈
        Column({ space: 12 }) {
          Text('🛠️ 技术栈')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .fontWeight(FontWeight.Bold)
            .width('100%')
            .textAlign(TextAlign.Start)

          Flex({ wrap: FlexWrap.Wrap, space: { main: LengthMetrics.vp(8), cross: LengthMetrics.vp(8) } }) {
            ForEach(this.appInfo.technologies, (tech: string) => {
              Text(tech)
                .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
                .fontColor(Constants.COLORS.PRIMARY)
                .backgroundColor($r('app.color.overlay_medium'))
                .padding({ left: '12vp', right: '12vp', top: '6vp', bottom: '6vp' })
                .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
            }, (tech: string) => tech)
          }
          .width('100%')
        }
        .width('100%')
        .padding(16)
        .backgroundColor(Constants.COLORS.WHITE)
        .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
        .alignItems(HorizontalAlign.Start)

        // 联系信息
        Column({ space: 12 }) {
          Text('📞 联系我们')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .fontWeight(FontWeight.Bold)
            .width('100%')
            .textAlign(TextAlign.Start)

          Column({ space: 8 }) {
            Row({ space: 12 }) {
              Text('🌐')
                .fontSize(16)
              Text('官方网站')
                .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
                .fontColor(Constants.COLORS.TEXT_SECONDARY)
                .layoutWeight(1)
              Text(this.appInfo.website)
                .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
                .fontColor(Constants.COLORS.PRIMARY)
                .onClick(() => {
                  this.openExternalLink(this.appInfo.website);
                })
            }
            .width('100%')
            .alignItems(VerticalAlign.Center)

            Row({ space: 12 }) {
              Text('📧')
                .fontSize(16)
              Text('客服邮箱')
                .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
                .fontColor(Constants.COLORS.TEXT_SECONDARY)
                .layoutWeight(1)
              Text(this.appInfo.email)
                .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
                .fontColor(Constants.COLORS.PRIMARY)
                .onClick(() => {
                  this.openExternalLink(`mailto:${this.appInfo.email}`);
                })
            }
            .width('100%')
            .alignItems(VerticalAlign.Center)

            Row({ space: 12 }) {
              Text('🔒')
                .fontSize(16)
              Text('隐私政策')
                .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
                .fontColor(Constants.COLORS.TEXT_SECONDARY)
                .layoutWeight(1)
              Text('查看详情')
                .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
                .fontColor(Constants.COLORS.PRIMARY)
                .onClick(() => {
                  this.openExternalLink(this.appInfo.privacy);
                })
            }
            .width('100%')
            .alignItems(VerticalAlign.Center)

            Row({ space: 12 }) {
              Text('📋')
                .fontSize(16)
              Text('服务条款')
                .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
                .fontColor(Constants.COLORS.TEXT_SECONDARY)
                .layoutWeight(1)
              Text('查看详情')
                .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
                .fontColor(Constants.COLORS.PRIMARY)
                .onClick(() => {
                  this.openExternalLink(this.appInfo.terms);
                })
            }
            .width('100%')
            .alignItems(VerticalAlign.Center)
          }
          .width('100%')
        }
        .width('100%')
        .padding(16)
        .backgroundColor(Constants.COLORS.WHITE)
        .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
        .alignItems(HorizontalAlign.Start)

        // 版权信息
        Text(this.appInfo.copyright)
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.TEXT_HINT)
          .textAlign(TextAlign.Center)
          .width('100%')
          .margin({ top: 8, bottom: 16 })

        // 底部间距
        Column()
          .height(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE))
      }
      .padding(16)
    }
    .scrollable(ScrollDirection.Vertical)
    .scrollBar(BarState.Auto)
    .layoutWeight(1)
  }

  /**
   * 团队成员卡片
   */
  @Builder
  private TeamMemberCard(member: TeamMember) {
    Column({ space: 12 }) {
      Text(member.avatar)
        .fontSize(48)

      Text(member.name)
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
        .fontColor(Constants.COLORS.TEXT_PRIMARY)
        .fontWeight(FontWeight.Bold)

      Text(member.role)
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
        .fontColor(Constants.COLORS.PRIMARY)
        .backgroundColor('rgba(33, 150, 243, 0.1)')
        .padding({ left: 12, right: 12, top: 4, bottom: 4 })
        .borderRadius(Constants.BORDER_RADIUS.MEDIUM)

      Text(member.description)
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
        .fontColor(Constants.COLORS.TEXT_SECONDARY)
        .textAlign(TextAlign.Center)
        .lineHeight(18)
    }
    .width('100%')
    .padding(16)
    .backgroundColor(Constants.COLORS.WHITE)
    .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
    .alignItems(HorizontalAlign.Center)
  }

  /**
   * 团队介绍内容
   */
  @Builder
  private TeamContent() {
    Scroll() {
      Column({ space: 16 }) {
        // 团队介绍
        Column({ space: 12 }) {
          Text('👥 我们的团队')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.EXTRA_LARGE))
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .fontWeight(FontWeight.Bold)
            .width('100%')
            .textAlign(TextAlign.Center)

          Text('NexusHub团队由一群热爱技术、追求卓越的开发者组成。我们致力于为HarmonyOS生态提供优质的应用商店体验，让每一位用户都能轻松发现和使用优秀的应用。')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor(Constants.COLORS.TEXT_SECONDARY)
            .textAlign(TextAlign.Center)
            .lineHeight(20)
            .margin({ left: 16, right: 16 })
        }
        .width('100%')
        .padding(16)
        .backgroundColor(Constants.COLORS.WHITE)
        .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
        .alignItems(HorizontalAlign.Center)

        // 团队成员
        Grid() {
          ForEach(this.teamMembers, (member: TeamMember) => {
            GridItem() {
              this.TeamMemberCard(member)
            }
          })
        }
        .columnsTemplate('1fr 1fr')
        .rowsGap(16)
        .columnsGap(16)
        .width('100%')

        // 底部间距
        Column()
          .height(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE))
      }
      .padding(16)
    }
    .scrollable(ScrollDirection.Vertical)
    .scrollBar(BarState.Auto)
    .layoutWeight(1)
  }

  /**
   * 更新日志项
   */
  @Builder
  private UpdateLogItem(log: UpdateLog) {
    Column({ space: 12 }) {
      Row({ space: 12 }) {
        Text('🚀')
          .fontSize(20)
        
        Column({ space: 4 }) {
          Text(`版本 ${log.version}`)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .fontWeight(FontWeight.Bold)
            .width('100%')
            .textAlign(TextAlign.Start)
          
          Text(log.date)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.TEXT_HINT)
            .width('100%')
            .textAlign(TextAlign.Start)
        }
        .alignItems(HorizontalAlign.Start)
        .layoutWeight(1)
      }
      .width('100%')
      .alignItems(VerticalAlign.Top)

      if (log.features.length > 0) {
        Column({ space: 8 }) {
          Text('✨ 新功能')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor(Constants.COLORS.PRIMARY)
            .fontWeight(FontWeight.Medium)
            .width('100%')
            .textAlign(TextAlign.Start)

          Column({ space: 4 }) {
            ForEach(log.features, (feature: string) => {
              Text(`• ${feature}`)
                .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
                .fontColor(Constants.COLORS.TEXT_SECONDARY)
                .width('100%')
                .textAlign(TextAlign.Start)
            }, (feature: string) => feature)
          }
          .width('100%')
        }
        .width('100%')
        .alignItems(HorizontalAlign.Start)
      }

      if (log.fixes.length > 0) {
        Column({ space: 8 }) {
          Text('🔧 问题修复')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor(Constants.COLORS.SUCCESS)
            .fontWeight(FontWeight.Medium)
            .width('100%')
            .textAlign(TextAlign.Start)

          Column({ space: 4 }) {
            ForEach(log.fixes, (fix: string) => {
              Text(`• ${fix}`)
                .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
                .fontColor(Constants.COLORS.TEXT_SECONDARY)
                .width('100%')
                .textAlign(TextAlign.Start)
            }, (fix: string) => fix)
          }
          .width('100%')
        }
        .width('100%')
        .alignItems(HorizontalAlign.Start)
      }
    }
    .width('100%')
    .padding(16)
    .backgroundColor(Constants.COLORS.WHITE)
    .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
    .alignItems(HorizontalAlign.Start)
  }

  /**
   * 更新日志内容
   */
  @Builder
  private UpdateLogContent() {
    Scroll() {
      Column({ space: 16 }) {
        ForEach(this.updateLogs, (log: UpdateLog) => {
          this.UpdateLogItem(log)
        }, (log: UpdateLog) => log.version)

        // 底部间距
        Column()
          .height(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE))
      }
      .padding(16)
    }
    .scrollable(ScrollDirection.Vertical)
    .scrollBar(BarState.Auto)
    .layoutWeight(1)
  }

  build() {
    Column() {
      // 标题栏
      Row() {
        Text('←')
          .fontSize(24)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .onClick(() => {
            this.getUIContext().getRouter().back();
          })

        Text('关于我们')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .fontWeight(FontWeight.Bold)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('')
          .width(24)
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor(Constants.COLORS.WHITE)
      .alignItems(VerticalAlign.Center)

      if (this.loadingState === LoadingState.LOADING) {
        LoadingView({ state: LoadingState.LOADING })
          .layoutWeight(1)
      } else {
        Column() {
          // 标签栏
          this.TabBar()
          
          Divider()
            .color(Constants.COLORS.BORDER)

          // 内容区域
          if (this.selectedTab === 0) {
            this.AppInfoContent()
          } else if (this.selectedTab === 1) {
            this.TeamContent()
          } else {
            this.UpdateLogContent()
          }
        }
        .layoutWeight(1)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Constants.COLORS.BACKGROUND)
  }
}

export { AboutPage };