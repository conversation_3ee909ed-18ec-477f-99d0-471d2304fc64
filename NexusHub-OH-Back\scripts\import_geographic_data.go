package main

import (
	"encoding/csv"
	"log"
	"os"
	"strconv"
	"strings"

	"nexushub-oh-back/config"
	"nexushub-oh-back/pkg/database"
)

// GeographicData 地理位置数据结构
type GeographicData struct {
	ID       int    `json:"id" gorm:"column:id;primaryKey"`
	ParentID int    `json:"parent_id" gorm:"column:parent_id"`
	Deep     int    `json:"deep" gorm:"column:deep"` // 0-省份，1-城市，2-镇/区，3-街道
	Name     string `json:"name" gorm:"column:name"`
	Short    string `json:"short" gorm:"column:short"`
	Pinyin   string `json:"pinyin" gorm:"column:pinyin"`
	Code     string `json:"code" gorm:"column:code"`
	FullName string `json:"full_name" gorm:"column:full_name"`
}

// TableName 指定表名
func (GeographicData) TableName() string {
	return "geographic_data"
}

func main() {
	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 连接数据库
	db, err := database.NewPostgresDB(&cfg.Database)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	// 创建表
	if err := db.DB.AutoMigrate(&GeographicData{}); err != nil {
		log.Fatalf("创建表失败: %v", err)
	}

	// 读取CSV文件
	csvFile := "ok_data_level.csv"
	file, err := os.Open(csvFile)
	if err != nil {
		log.Fatalf("打开CSV文件失败: %v", err)
	}
	defer file.Close()

	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		log.Fatalf("读取CSV文件失败: %v", err)
	}

	// 批量插入数据
	batchSize := 1000
	totalRecords := len(records)
	log.Printf("开始导入 %d 条记录...", totalRecords)

	for i := 0; i < totalRecords; i += batchSize {
		end := i + batchSize
		if end > totalRecords {
			end = totalRecords
		}

		batch := make([]GeographicData, 0, end-i)
		for j := i; j < end; j++ {
			record := records[j]
			if len(record) < 8 {
				log.Printf("跳过无效记录: %v", record)
				continue
			}

			id, err := strconv.Atoi(record[0])
			if err != nil {
				log.Printf("跳过无效ID记录: %v", record)
				continue
			}

			parentID, err := strconv.Atoi(record[1])
			if err != nil {
				log.Printf("跳过无效父ID记录: %v", record)
				continue
			}

			deep, err := strconv.Atoi(record[2])
			if err != nil {
				log.Printf("跳过无效深度记录: %v", record)
				continue
			}

			// 清理字段中的引号
			name := strings.Trim(record[3], `"`)
			short := strings.Trim(record[4], `"`)
			pinyin := strings.Trim(record[5], `"`)
			code := strings.Trim(record[6], `"`)
			fullName := strings.Trim(record[7], `"`)

			data := GeographicData{
				ID:       id,
				ParentID: parentID,
				Deep:     deep,
				Name:     name,
				Short:    short,
				Pinyin:   pinyin,
				Code:     code,
				FullName: fullName,
			}

			batch = append(batch, data)
		}

		// 批量插入
		if len(batch) > 0 {
			if err := db.DB.CreateInBatches(batch, len(batch)).Error; err != nil {
				log.Printf("批量插入失败 (批次 %d-%d): %v", i, end-1, err)
				continue
			}
			log.Printf("成功导入批次 %d-%d (%d 条记录)", i, end-1, len(batch))
		}
	}

	// 创建索引以提高查询性能
	log.Println("创建索引...")
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_geographic_data_deep ON geographic_data(deep);",
		"CREATE INDEX IF NOT EXISTS idx_geographic_data_parent_id ON geographic_data(parent_id);",
		"CREATE INDEX IF NOT EXISTS idx_geographic_data_deep_parent ON geographic_data(deep, parent_id);",
		"CREATE INDEX IF NOT EXISTS idx_geographic_data_name ON geographic_data(name);",
	}

	for _, indexSQL := range indexes {
		if err := db.DB.Exec(indexSQL).Error; err != nil {
			log.Printf("创建索引失败: %v", err)
		} else {
			log.Printf("索引创建成功: %s", indexSQL)
		}
	}

	// 统计导入结果
	var count int64
	db.DB.Model(&GeographicData{}).Count(&count)
	log.Printf("数据导入完成，共导入 %d 条记录", count)

	// 按级别统计
	for level := 0; level <= 3; level++ {
		var levelCount int64
		db.DB.Model(&GeographicData{}).Where("deep = ?", level).Count(&levelCount)
		levelName := []string{"省份", "城市", "区/镇", "街道"}[level]
		log.Printf("级别 %d (%s): %d 条记录", level, levelName, levelCount)
	}
}