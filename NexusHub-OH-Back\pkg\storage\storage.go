package storage

import (
	"context"
	"io"
	"time"

	"nexushub-oh-back/config"
)

// StorageProvider 存储提供者接口
type StorageProvider interface {
	// UploadFile 上传文件
	UploadFile(ctx context.Context, bucketName, objectName string, reader io.Reader, objectSize int64, contentType string) error

	// GetFileURL 获取文件的URL
	GetFileURL(ctx context.Context, bucketName, objectName string, expiry time.Duration) (string, error)

	// DeleteFile 删除文件
	DeleteFile(ctx context.Context, bucketName, objectName string) error

	// CheckBucket 检查存储桶是否存在，不存在则创建
	CheckBucket(ctx context.Context, bucketName string) error
}

// StorageType 存储类型
type StorageType string

const (
	// MinioStorage Minio存储
	MinioStorage StorageType = "minio"

	// OSSStorage 阿里云OSS存储
	OSSStorage StorageType = "oss"
)

// NewStorageProvider 创建新的存储提供者
func NewStorageProvider(cfg *config.StorageConfig) (StorageProvider, error) {
	switch StorageType(cfg.Type) {
	case MinioStorage:
		return NewMinioProvider(cfg)
	case OSSStorage:
		// TODO: 实现阿里云OSS存储
		return nil, nil
	default:
		// 默认使用Minio
		return NewMinioProvider(cfg)
	}
}
