package controllers

import (
	"strconv"
	"strings"

	"nexushub-oh-back/internal/models"
	"nexushub-oh-back/pkg/response"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// HelpCenterController 帮助中心控制器
type HelpCenterController struct {
	DB *gorm.DB
}

// NewHelpCenterController 创建帮助中心控制器
func NewHelpCenterController(db *gorm.DB) *HelpCenterController {
	return &HelpCenterController{
		DB: db,
	}
}

// GetHelpCategories 获取帮助分类列表（公开接口）
// @Summary 获取帮助分类列表
// @Description 获取所有活跃的帮助分类列表（公开接口）
// @Tags 帮助中心
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]models.HelpCategoryResponse} "获取成功"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/help/categories [get]
func (c *HelpCenterController) GetHelpCategories(ctx *gin.Context) {
	var categories []models.HelpCategory
	if err := c.DB.Where("is_active = ?", true).Order("sort_order ASC, created_at ASC").Find(&categories).Error; err != nil {
		response.InternalServerError(ctx, "获取分类列表失败")
		return
	}

	var result []models.HelpCategoryResponse
	for _, category := range categories {
		result = append(result, convertToCategoryResponse(category))
	}

	response.Success(ctx, result)
}

// GetHelpArticles 获取帮助文章列表（公开接口）
// @Summary 获取帮助文章列表
// @Description 获取指定分类下的帮助文章列表（公开接口）
// @Tags 帮助中心
// @Accept json
// @Produce json
// @Param category_id query int false "分类ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} response.Response{data=PaginatedData} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/help/articles [get]
func (c *HelpCenterController) GetHelpArticles(ctx *gin.Context) {
	categoryIDStr := ctx.Query("category_id")
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	query := c.DB.Where("is_published = ?", true)
	if categoryIDStr != "" {
		categoryID, err := strconv.ParseUint(categoryIDStr, 10, 32)
		if err != nil {
			response.BadRequest(ctx, "无效的分类ID")
			return
		}
		query = query.Where("category_id = ?", uint(categoryID))
	}

	var total int64
	if err := query.Model(&models.HelpArticle{}).Count(&total).Error; err != nil {
		response.InternalServerError(ctx, "获取文章总数失败")
		return
	}

	var articles []models.HelpArticle
	offset := (page - 1) * pageSize
	if err := query.Preload("Category").Preload("Author").Order("sort_order ASC, created_at DESC").Offset(offset).Limit(pageSize).Find(&articles).Error; err != nil {
		response.InternalServerError(ctx, "获取文章列表失败")
		return
	}

	var result []models.HelpArticleResponse
	for _, article := range articles {
		result = append(result, convertToArticleResponse(article))
	}

	paginatedResult := PaginatedData{
		Items:      result,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: int((total + int64(pageSize) - 1) / int64(pageSize)),
	}

	response.Success(ctx, paginatedResult)
}

// GetHelpArticle 获取帮助文章详情（公开接口）
// @Summary 获取帮助文章详情
// @Description 获取指定帮助文章的详细信息（公开接口）
// @Tags 帮助中心
// @Accept json
// @Produce json
// @Param id path int true "文章ID"
// @Success 200 {object} response.Response{data=models.HelpArticleResponse} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "文章不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/help/articles/{id} [get]
func (c *HelpCenterController) GetHelpArticle(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(ctx, "无效的文章ID")
		return
	}

	var article models.HelpArticle
	if err := c.DB.Preload("Category").Preload("Author").Where("id = ? AND is_published = ?", uint(id), true).First(&article).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "文章不存在")
		} else {
			response.InternalServerError(ctx, "获取文章失败")
		}
		return
	}

	// 增加浏览次数
	c.DB.Model(&article).Update("view_count", gorm.Expr("view_count + ?", 1))

	result := convertToArticleResponse(article)
	response.Success(ctx, result)
}

// PaginatedData 分页数据结构体
type PaginatedData struct {
	Items      interface{} `json:"list"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	TotalPages int         `json:"total_pages"`
}

// ========== 分类管理 ==========

// CreateCategory 创建帮助分类
// @Summary 创建帮助分类
// @Description 创建新的帮助分类（管理员权限）
// @Tags 帮助中心管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param request body CreateCategoryRequest true "分类信息"
// @Success 200 {object} response.Response{data=models.HelpCategoryResponse} "创建成功"
// @Failure 400 {object} response.ErrorResponse "参数错误"
// @Failure 401 {object} response.ErrorResponse "未授权"
// @Failure 403 {object} response.ErrorResponse "无权限"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/admin/help/categories [post]
func (c *HelpCenterController) CreateCategory(ctx *gin.Context) {
	// 检查管理员权限
	if !c.checkAdminPermission(ctx) {
		return
	}

	var req CreateCategoryRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, "参数错误: "+err.Error())
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "用户信息获取失败")
		return
	}

	// 安全地转换用户ID为uint类型
	var createdBy uint
	switch v := userID.(type) {
	case uint:
		createdBy = v
	case string:
		if uid, err := strconv.ParseUint(v, 10, 32); err == nil {
			createdBy = uint(uid)
		} else {
			response.BadRequest(ctx, "无效的用户ID格式")
			return
		}
	default:
		response.BadRequest(ctx, "用户ID类型错误")
		return
	}

	// 检查分类名称是否已存在
	var existingCategory models.HelpCategory
	if err := c.DB.Where("name = ?", req.Name).First(&existingCategory).Error; err == nil {
		response.BadRequest(ctx, "分类名称已存在")
		return
	}

	category := models.HelpCategory{
		Name:        req.Name,
		Description: req.Description,
		Icon:        req.Icon,
		SortOrder:   req.SortOrder,
		IsActive:    req.IsActive,
		CreatedBy:   createdBy,
	}

	if err := c.DB.Create(&category).Error; err != nil {
		response.InternalServerError(ctx, "创建分类失败")
		return
	}

	resp := convertToCategoryResponse(category)
	response.Success(ctx, resp)
}

// UpdateCategory 更新帮助分类
// @Summary 更新帮助分类
// @Description 更新帮助分类信息（管理员权限）
// @Tags 帮助中心管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "分类ID"
// @Param request body UpdateCategoryRequest true "分类信息"
// @Success 200 {object} response.Response{data=models.HelpCategoryResponse} "更新成功"
// @Failure 400 {object} response.ErrorResponse "参数错误"
// @Failure 401 {object} response.ErrorResponse "未授权"
// @Failure 403 {object} response.ErrorResponse "无权限"
// @Failure 404 {object} response.ErrorResponse "分类不存在"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/admin/help/categories/{id} [put]
func (c *HelpCenterController) UpdateCategory(ctx *gin.Context) {
	// 检查管理员权限
	if !c.checkAdminPermission(ctx) {
		return
	}

	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.BadRequest(ctx, "无效的分类ID")
		return
	}

	var req UpdateCategoryRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, "参数错误: "+err.Error())
		return
	}

	var category models.HelpCategory
	if err := c.DB.First(&category, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "分类不存在")
			return
		}
		response.InternalServerError(ctx, "查询分类失败")
		return
	}

	// 检查分类名称是否已存在（排除当前分类）
	var existingCategory models.HelpCategory
	if err := c.DB.Where("name = ? AND id != ?", req.Name, id).First(&existingCategory).Error; err == nil {
		response.BadRequest(ctx, "分类名称已存在")
		return
	}

	// 更新分类信息
	category.Name = req.Name
	category.Description = req.Description
	category.Icon = req.Icon
	category.SortOrder = req.SortOrder
	category.IsActive = req.IsActive

	if err := c.DB.Save(&category).Error; err != nil {
		response.InternalServerError(ctx, "更新分类失败")
		return
	}

	resp := convertToCategoryResponse(category)
	response.Success(ctx, resp)
}

// DeleteCategory 删除帮助分类
// @Summary 删除帮助分类
// @Description 删除帮助分类（管理员权限）
// @Tags 帮助中心管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "分类ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.ErrorResponse "参数错误"
// @Failure 401 {object} response.ErrorResponse "未授权"
// @Failure 403 {object} response.ErrorResponse "无权限"
// @Failure 404 {object} response.ErrorResponse "分类不存在"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/admin/help/categories/{id} [delete]
func (c *HelpCenterController) DeleteCategory(ctx *gin.Context) {
	// 检查管理员权限
	if !c.checkAdminPermission(ctx) {
		return
	}

	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.BadRequest(ctx, "无效的分类ID")
		return
	}

	var category models.HelpCategory
	if err := c.DB.First(&category, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "分类不存在")
			return
		}
		response.InternalServerError(ctx, "查询分类失败")
		return
	}

	// 检查分类下是否有文章
	var articleCount int64
	c.DB.Model(&models.HelpArticle{}).Where("category_id = ?", id).Count(&articleCount)
	if articleCount > 0 {
		response.BadRequest(ctx, "该分类下还有文章，无法删除")
		return
	}

	if err := c.DB.Delete(&category).Error; err != nil {
		response.InternalServerError(ctx, "删除分类失败")
		return
	}

	response.Success(ctx, nil)
}

// GetCategories 获取帮助分类列表
// @Summary 获取帮助分类列表
// @Description 获取帮助分类列表，支持分页
// @Tags 帮助中心
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Param is_active query bool false "是否激活"
// @Success 200 {object} response.PageResponse{data=[]models.HelpCategoryResponse} "获取成功"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/help/categories [get]
func (c *HelpCenterController) GetCategories(ctx *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}
	offset := (page - 1) * limit

	// 构建查询条件
	query := c.DB.Model(&models.HelpCategory{})

	// 筛选激活状态
	if isActiveStr := ctx.Query("is_active"); isActiveStr != "" {
		if isActive, err := strconv.ParseBool(isActiveStr); err == nil {
			query = query.Where("is_active = ?", isActive)
		}
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		response.InternalServerError(ctx, "查询分类总数失败")
		return
	}

	// 获取数据
	var categories []models.HelpCategory
	if err := query.Order("sort_order ASC, id ASC").
		Offset(offset).Limit(limit).
		Find(&categories).Error; err != nil {
		response.InternalServerError(ctx, "查询分类失败")
		return
	}

	// 转换为响应格式
	var result []models.HelpCategoryResponse
	for _, category := range categories {
		result = append(result, convertToCategoryResponse(category))
	}

	response.SuccessWithPagination(ctx, result, total, page, limit)
}

// ========== 文章管理 ==========

// CreateArticle 创建帮助文章
// @Summary 创建帮助文章
// @Description 创建新的帮助文章（管理员权限）
// @Tags 帮助中心管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param request body CreateArticleRequest true "文章信息"
// @Success 200 {object} response.Response{data=models.HelpArticleResponse} "创建成功"
// @Failure 400 {object} response.ErrorResponse "参数错误"
// @Failure 401 {object} response.ErrorResponse "未授权"
// @Failure 403 {object} response.ErrorResponse "无权限"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/admin/help/articles [post]
func (c *HelpCenterController) CreateArticle(ctx *gin.Context) {
	// 检查管理员权限
	if !c.checkAdminPermission(ctx) {
		return
	}

	var req CreateArticleRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, "参数错误: "+err.Error())
		return
	}

	// 检查分类是否存在
	var category models.HelpCategory
	if err := c.DB.First(&category, req.CategoryID).Error; err != nil {
		response.BadRequest(ctx, "分类不存在")
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "用户信息获取失败")
		return
	}

	// 安全地转换用户ID为uint类型
	var createdBy uint
	switch v := userID.(type) {
	case uint:
		createdBy = v
	case string:
		if uid, err := strconv.ParseUint(v, 10, 32); err == nil {
			createdBy = uint(uid)
		} else {
			response.BadRequest(ctx, "无效的用户ID格式")
			return
		}
	default:
		response.BadRequest(ctx, "用户ID类型错误")
		return
	}

	article := models.HelpArticle{
		CategoryID:  req.CategoryID,
		Title:       req.Title,
		Content:     req.Content,
		Summary:     req.Summary,
		Tags:        req.Tags,
		SortOrder:   req.SortOrder,
		IsPublished: req.IsPublished,
		CreatedBy:   createdBy,
		UpdatedBy:   createdBy, // 创建时设置更新者为创建者
	}

	if err := c.DB.Create(&article).Error; err != nil {
		response.InternalServerError(ctx, "创建文章失败")
		return
	}

	// 预加载关联数据
	if err := c.DB.Preload("Category").Preload("Creator").First(&article, article.ID).Error; err != nil {
		response.InternalServerError(ctx, "查询文章失败")
		return
	}

	resp := convertToArticleResponse(article)
	response.Success(ctx, resp)
}

// UpdateArticle 更新帮助文章
// @Summary 更新帮助文章
// @Description 更新帮助文章信息（管理员权限）
// @Tags 帮助中心管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "文章ID"
// @Param request body UpdateArticleRequest true "文章信息"
// @Success 200 {object} response.Response{data=models.HelpArticleResponse} "更新成功"
// @Failure 400 {object} response.ErrorResponse "参数错误"
// @Failure 401 {object} response.ErrorResponse "未授权"
// @Failure 403 {object} response.ErrorResponse "无权限"
// @Failure 404 {object} response.ErrorResponse "文章不存在"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/admin/help/articles/{id} [put]
func (c *HelpCenterController) UpdateArticle(ctx *gin.Context) {
	// 检查管理员权限
	if !c.checkAdminPermission(ctx) {
		return
	}

	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.BadRequest(ctx, "无效的文章ID")
		return
	}

	var req UpdateArticleRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, "参数错误: "+err.Error())
		return
	}

	var article models.HelpArticle
	if err := c.DB.First(&article, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "文章不存在")
			return
		}
		response.InternalServerError(ctx, "查询文章失败")
		return
	}

	// 检查分类是否存在
	var category models.HelpCategory
	if err := c.DB.First(&category, req.CategoryID).Error; err != nil {
		response.BadRequest(ctx, "分类不存在")
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "用户信息获取失败")
		return
	}

	// 安全地转换用户ID为uint类型
	var updatedBy uint
	switch v := userID.(type) {
	case uint:
		updatedBy = v
	case string:
		if uid, err := strconv.ParseUint(v, 10, 32); err == nil {
			updatedBy = uint(uid)
		} else {
			response.BadRequest(ctx, "无效的用户ID格式")
			return
		}
	default:
		response.BadRequest(ctx, "用户ID类型错误")
		return
	}

	// 更新文章信息
	article.CategoryID = req.CategoryID
	article.Title = req.Title
	article.Content = req.Content
	article.Summary = req.Summary
	article.Tags = req.Tags
	article.SortOrder = req.SortOrder
	article.IsPublished = req.IsPublished
	article.UpdatedBy = updatedBy

	if err := c.DB.Save(&article).Error; err != nil {
		response.InternalServerError(ctx, "更新文章失败")
		return
	}

	// 预加载关联数据
	if err := c.DB.Preload("Category").Preload("Creator").First(&article, article.ID).Error; err != nil {
		response.InternalServerError(ctx, "查询文章失败")
		return
	}

	resp := convertToArticleResponse(article)
	response.Success(ctx, resp)
}

// DeleteArticle 删除帮助文章
// @Summary 删除帮助文章
// @Description 删除帮助文章（管理员权限）
// @Tags 帮助中心管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "文章ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.ErrorResponse "参数错误"
// @Failure 401 {object} response.ErrorResponse "未授权"
// @Failure 403 {object} response.ErrorResponse "无权限"
// @Failure 404 {object} response.ErrorResponse "文章不存在"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/admin/help/articles/{id} [delete]
func (c *HelpCenterController) DeleteArticle(ctx *gin.Context) {
	// 检查管理员权限
	if !c.checkAdminPermission(ctx) {
		return
	}

	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.BadRequest(ctx, "无效的文章ID")
		return
	}

	var article models.HelpArticle
	if err := c.DB.First(&article, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "文章不存在")
			return
		}
		response.InternalServerError(ctx, "查询文章失败")
		return
	}

	// 删除相关的浏览记录
	c.DB.Where("article_id = ?", id).Delete(&models.HelpArticleView{})

	// 删除文章
	if err := c.DB.Delete(&article).Error; err != nil {
		response.InternalServerError(ctx, "删除文章失败")
		return
	}

	response.Success(ctx, nil)
}

// GetArticles 获取帮助文章列表
// @Summary 获取帮助文章列表
// @Description 获取帮助文章列表，支持分页和筛选
// @Tags 帮助中心
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Param category_id query int false "分类ID"
// @Param is_published query bool false "是否发布"
// @Param keyword query string false "关键词搜索"
// @Success 200 {object} response.PageResponse{data=[]models.HelpArticleResponse} "获取成功"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/help/articles [get]
func (c *HelpCenterController) GetArticles(ctx *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}
	offset := (page - 1) * limit

	// 构建查询条件
	query := c.DB.Model(&models.HelpArticle{})

	// 筛选分类
	if categoryIDStr := ctx.Query("category_id"); categoryIDStr != "" {
		if categoryID, err := strconv.Atoi(categoryIDStr); err == nil {
			query = query.Where("category_id = ?", categoryID)
		}
	}

	// 筛选发布状态
	if isPublishedStr := ctx.Query("is_published"); isPublishedStr != "" {
		if isPublished, err := strconv.ParseBool(isPublishedStr); err == nil {
			query = query.Where("is_published = ?", isPublished)
		}
	} else {
		// 默认只显示已发布的文章
		query = query.Where("is_published = ?", true)
	}

	// 关键词搜索
	if keyword := ctx.Query("keyword"); keyword != "" {
		query = query.Where("title LIKE ? OR content LIKE ? OR summary LIKE ?",
			"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		response.InternalServerError(ctx, "查询文章总数失败")
		return
	}

	// 获取数据
	var articles []models.HelpArticle
	if err := query.Preload("Category").Preload("Creator").
		Order("sort_order ASC, created_at DESC").
		Offset(offset).Limit(limit).
		Find(&articles).Error; err != nil {
		response.InternalServerError(ctx, "查询文章失败")
		return
	}

	// 转换为响应格式
	var result []models.HelpArticleResponse
	for _, article := range articles {
		result = append(result, convertToArticleResponse(article))
	}

	response.SuccessWithPagination(ctx, result, total, page, limit)
}

// GetArticleDetail 获取帮助文章详情
// @Summary 获取帮助文章详情
// @Description 获取帮助文章详情，并记录浏览记录
// @Tags 帮助中心
// @Accept json
// @Produce json
// @Param id path int true "文章ID"
// @Success 200 {object} response.Response{data=models.HelpArticleResponse} "获取成功"
// @Failure 400 {object} response.ErrorResponse "参数错误"
// @Failure 404 {object} response.ErrorResponse "文章不存在"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/help/articles/{id} [get]
func (c *HelpCenterController) GetArticleDetail(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.BadRequest(ctx, "无效的文章ID")
		return
	}

	var article models.HelpArticle
	if err := c.DB.Preload("Category").Preload("Creator").
		Where("id = ? AND is_published = ?", id, true).
		First(&article).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "文章不存在")
			return
		}
		response.InternalServerError(ctx, "查询文章失败")
		return
	}

	// 记录浏览记录
	c.recordArticleView(ctx, uint(id))

	// 增加浏览次数
	c.DB.Model(&article).UpdateColumn("view_count", gorm.Expr("view_count + ?", 1))

	resp := convertToArticleResponse(article)
	response.Success(ctx, resp)
}

// ========== 辅助方法 ==========

// checkAdminPermission 检查管理员权限
func (c *HelpCenterController) checkAdminPermission(ctx *gin.Context) bool {
	userRole, exists := ctx.Get("role")
	if !exists || userRole != "admin" {
		response.Forbidden(ctx, "需要管理员权限")
		return false
	}
	return true
}

// recordArticleView 记录文章浏览
func (c *HelpCenterController) recordArticleView(ctx *gin.Context, articleID uint) {
	// 获取用户ID（可选）
	userID, _ := ctx.Get("user_id")

	// 获取IP地址
	ipAddress := ctx.ClientIP()

	// 获取User-Agent
	userAgent := ctx.GetHeader("User-Agent")

	view := models.HelpArticleView{
		ArticleID: articleID,
		IPAddress: ipAddress,
		UserAgent: userAgent,
	}

	if userID != nil {
		// 安全地转换用户ID为uint类型
		switch v := userID.(type) {
		case uint:
			view.UserID = &v
		case string:
			if uid, err := strconv.ParseUint(v, 10, 32); err == nil {
				uintUID := uint(uid)
				view.UserID = &uintUID
			}
			// 如果转换失败，保持UserID为nil
		}
	}

	// 异步记录，不影响主流程
	go func() {
		c.DB.Create(&view)
	}()
}

// 转换函数

// convertToCategoryResponse 转换为分类响应格式
func convertToCategoryResponse(category models.HelpCategory) models.HelpCategoryResponse {
	return models.HelpCategoryResponse{
		ID:          category.ID,
		Name:        category.Name,
		Description: category.Description,
		Icon:        category.Icon,
		SortOrder:   category.SortOrder,
		IsActive:    category.IsActive,
		CreatedAt:   category.CreatedAt,
	}
}

// convertToArticleResponse 转换为文章响应格式
func convertToArticleResponse(article models.HelpArticle) models.HelpArticleResponse {
	// 处理Tags字段，从字符串转换为字符串数组
	var tags []string
	if article.Tags != "" {
		tags = strings.Split(article.Tags, ",")
		for i := range tags {
			tags[i] = strings.TrimSpace(tags[i])
		}
	}

	// 构建分类信息
	var category models.HelpCategoryResponse
	if article.Category.ID != 0 {
		category = models.HelpCategoryResponse{
			ID:           article.Category.ID,
			Name:         article.Category.Name,
			Description:  article.Category.Description,
			Icon:         article.Category.Icon,
			SortOrder:    article.Category.SortOrder,
			IsActive:     article.Category.IsActive,
			CreatedAt:    article.Category.CreatedAt,
			ArticleCount: 0, // 这里不计算文章数量，避免额外查询
		}
	}

	// 构建创建者信息
	var creator struct {
		ID       uint   `json:"id"`
		Username string `json:"username"`
		Avatar   string `json:"avatar"`
	}
	if article.Creator.ID != 0 {
		creator.ID = article.Creator.ID
		creator.Username = article.Creator.Username
		creator.Avatar = article.Creator.Avatar
	}

	return models.HelpArticleResponse{
		ID:          article.ID,
		Title:       article.Title,
		Content:     article.Content,
		Summary:     article.Summary,
		Thumbnail:   article.Thumbnail,
		Tags:        tags,
		SortOrder:   article.SortOrder,
		ViewCount:   article.ViewCount,
		LikeCount:   article.LikeCount,
		IsPublished: article.IsPublished,
		IsFeatured:  article.IsFeatured,
		CreatedAt:   article.CreatedAt,
		UpdatedAt:   article.UpdatedAt,
		PublishedAt: article.PublishedAt,
		Category:    category,
		Creator:     creator,
	}
}

// 请求结构体

// CreateCategoryRequest 创建分类请求
type CreateCategoryRequest struct {
	Name        string `json:"name" binding:"required,max=100"`
	Description string `json:"description" binding:"max=500"`
	Icon        string `json:"icon" binding:"max=255"`
	SortOrder   int    `json:"sort_order"`
	IsActive    bool   `json:"is_active"`
}

// UpdateCategoryRequest 更新分类请求
type UpdateCategoryRequest struct {
	Name        string `json:"name" binding:"required,max=100"`
	Description string `json:"description" binding:"max=500"`
	Icon        string `json:"icon" binding:"max=255"`
	SortOrder   int    `json:"sort_order"`
	IsActive    bool   `json:"is_active"`
}

// CreateArticleRequest 创建文章请求
type CreateArticleRequest struct {
	CategoryID  uint   `json:"category_id" binding:"required"`
	Title       string `json:"title" binding:"required,max=200"`
	Content     string `json:"content" binding:"required"`
	Summary     string `json:"summary" binding:"max=500"`
	Tags        string `json:"tags" binding:"max=500"`
	SortOrder   int    `json:"sort_order"`
	IsPublished bool   `json:"is_published"`
}

// UpdateArticleRequest 更新文章请求
type UpdateArticleRequest struct {
	CategoryID  uint   `json:"category_id" binding:"required"`
	Title       string `json:"title" binding:"required,max=200"`
	Content     string `json:"content" binding:"required"`
	Summary     string `json:"summary" binding:"max=500"`
	Tags        string `json:"tags" binding:"max=500"`
	SortOrder   int    `json:"sort_order"`
	IsPublished bool   `json:"is_published"`
}
