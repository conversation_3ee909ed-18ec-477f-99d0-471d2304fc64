import { PageContainer } from '@ant-design/pro-components';
import { Card, Row, Col, Statistic, Table, DatePicker, Button, Radio, Tabs } from 'antd';
import { Line, Column } from '@ant-design/plots';
import { DownloadOutlined, ReloadOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { useRequest } from '@umijs/max';

// 定义数据接口
interface RevenueData {
  date: string;
  revenue: number;
}

interface CategoryData {
  category: string;
  revenue: number;
}

interface TopSellingApp {
  id: number;
  name: string;
  category: string;
  revenue: number;
  sales: number;
}

interface SalesOverview {
  totalRevenue: number;
  totalSales: number;
  conversionRate: number;
}

interface SalesStatisticsResponse {
  overview: SalesOverview;
  revenueData: RevenueData[];
  categoryData: CategoryData[];
  topSellingApps: TopSellingApp[];
}

// 模拟数据获取函数
const fetchSalesStatistics = async (params: any): Promise<SalesStatisticsResponse> => {
  console.log('Fetching sales statistics with params:', params);
  
  // 模拟数据
  const revenueData: RevenueData[] = [
    { date: '2023-01', revenue: 125000 },
    { date: '2023-02', revenue: 138000 },
    { date: '2023-03', revenue: 150000 },
    { date: '2023-04', revenue: 165000 },
    { date: '2023-05', revenue: 180000 },
    { date: '2023-06', revenue: 210000 },
    { date: '2023-07', revenue: 230000 },
    { date: '2023-08', revenue: 245000 },
    { date: '2023-09', revenue: 260000 },
    { date: '2023-10', revenue: 275000 },
    { date: '2023-11', revenue: 290000 },
    { date: '2023-12', revenue: 320000 },
  ];

  const categoryData: CategoryData[] = [
    { category: '游戏', revenue: 850000 },
    { category: '工具', revenue: 580000 },
    { category: '教育', revenue: 390000 },
    { category: '社交', revenue: 260000 },
    { category: '音乐', revenue: 220000 },
    { category: '其他', revenue: 180000 },
  ];

  const topSellingApps: TopSellingApp[] = [
    { id: 1, name: '超级工具箱Pro', category: '工具', revenue: 125000, sales: 25000 },
    { id: 2, name: '音乐播放器高级版', category: '音乐', revenue: 98000, sales: 19600 },
    { id: 3, name: '学习助手Plus', category: '教育', revenue: 87000, sales: 17400 },
    { id: 4, name: '休闲游戏包', category: '游戏', revenue: 76000, sales: 15200 },
    { id: 5, name: '社交聊天高级会员', category: '社交', revenue: 65000, sales: 13000 },
    { id: 6, name: '健康追踪专业版', category: '健康', revenue: 54000, sales: 10800 },
    { id: 7, name: '阅读器完整版', category: '工具', revenue: 43000, sales: 8600 },
    { id: 8, name: '视频编辑专业版', category: '工具', revenue: 32000, sales: 6400 },
    { id: 9, name: '天气预报高级版', category: '工具', revenue: 21000, sales: 4200 },
    { id: 10, name: '记事本专业版', category: '工具', revenue: 10000, sales: 2000 },
  ];

  return {
    overview: {
      totalRevenue: 2580000,
      totalSales: 516000,
      conversionRate: 0.08,
    },
    revenueData,
    categoryData,
    topSellingApps,
  };
};

const SalesStatistics: React.FC = () => {
  const [timeRange, setTimeRange] = useState('year');
  const [activeTab, setActiveTab] = useState('overview');

  const { data, loading, refresh } = useRequest(() => fetchSalesStatistics({
    timeRange,
  }), {
    refreshDeps: [timeRange],
  });

  // 确保data存在
  const salesData: SalesStatisticsResponse = data || {
    overview: { 
      totalRevenue: 0,
      totalSales: 0,
      conversionRate: 0
    },
    revenueData: [],
    categoryData: [],
    topSellingApps: []
  };

  const revenueConfig = {
    data: salesData.revenueData,
    xField: 'date',
    yField: 'revenue',
    smooth: true,
    point: {
      size: 5,
      shape: 'diamond',
    },
    label: {},
    color: '#52c41a',
  };

  const categoryConfig = {
    data: salesData.categoryData,
    xField: 'category',
    yField: 'revenue',
    label: {
      position: 'middle',
      style: {
        fill: '#FFFFFF',
        opacity: 0.6,
      },
    },
    color: '#52c41a',
  };

  const columns = [
    {
      title: '排名',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '应用名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: '收入(元)',
      dataIndex: 'revenue',
      key: 'revenue',
      sorter: (a: TopSellingApp, b: TopSellingApp) => a.revenue - b.revenue,
      render: (revenue: number) => `¥${revenue.toLocaleString()}`,
    },
    {
      title: '销量',
      dataIndex: 'sales',
      key: 'sales',
      sorter: (a: TopSellingApp, b: TopSellingApp) => a.sales - b.sales,
      render: (sales: number) => sales.toLocaleString(),
    },
  ];

  return (
    <PageContainer
      header={{
        title: '销售数据统计',
        subTitle: '查看应用销售和收入数据',
        extra: [
          <Button key="export" icon={<DownloadOutlined />}>导出数据</Button>,
          <Button key="refresh" icon={<ReloadOutlined />} onClick={refresh}>刷新</Button>,
        ],
      }}
    >
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        tabBarExtraContent={
          <Radio.Group value={timeRange} onChange={(e) => setTimeRange(e.target.value)}>
            <Radio.Button value="month">本月</Radio.Button>
            <Radio.Button value="quarter">本季度</Radio.Button>
            <Radio.Button value="year">本年</Radio.Button>
            <Radio.Button value="all">全部</Radio.Button>
          </Radio.Group>
        }
        items={[
          {
            key: 'overview',
            label: '概览',
            children: (
              <>
                <Row gutter={16} style={{ marginBottom: 24 }}>
                  <Col span={8}>
                    <Card>
                      <Statistic
                        title="总收入"
                        value={salesData.overview.totalRevenue}
                        loading={loading}
                        prefix="¥"
                        formatter={(value) => `${(Number(value) / 10000).toFixed(2)}万`}
                      />
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card>
                      <Statistic
                        title="总销量"
                        value={salesData.overview.totalSales}
                        loading={loading}
                        formatter={(value) => `${(Number(value) / 1000).toFixed(1)}k`}
                      />
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card>
                      <Statistic
                        title="转化率"
                        value={salesData.overview.conversionRate}
                        loading={loading}
                        precision={2}
                        suffix="%"
                        formatter={(value) => (Number(value) * 100).toFixed(2)}
                      />
                    </Card>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={24}>
                    <Card title="收入趋势" style={{ marginBottom: 24 }}>
                      <Line {...revenueConfig} loading={loading} />
                    </Card>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={24}>
                    <Card title="分类收入" style={{ marginBottom: 24 }}>
                      <Column {...categoryConfig} loading={loading} />
                    </Card>
                  </Col>
                </Row>
              </>
            ),
          },
          {
            key: 'topSelling',
            label: '热销应用',
            children: (
              <Card>
                <Table
                  columns={columns}
                  dataSource={salesData.topSellingApps}
                  rowKey="id"
                  loading={loading}
                  pagination={false}
                />
              </Card>
            ),
          },
        ]}
      />
    </PageContainer>
  );
};

export default SalesStatistics;