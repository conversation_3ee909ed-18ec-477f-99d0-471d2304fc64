package api

import (
	"strconv"

	"nexushub-oh-back/internal/models"
	"nexushub-oh-back/pkg/logger"
	"nexushub-oh-back/pkg/response"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ReviewController 评论控制器
type ReviewController struct {
	DB       *gorm.DB
	Validate *validator.Validate
}

// NewReviewController 创建评论控制器
func NewReviewController(db *gorm.DB) *ReviewController {
	return &ReviewController{
		DB:       db,
		Validate: validator.New(),
	}
}

// CreateReviewRequest 创建评论请求
type CreateReviewRequest struct {
	Title      string `json:"title" validate:"required,max=100"`
	Content    string `json:"content" validate:"required,min=1,max=1000"`
	Rating     int    `json:"rating" validate:"required,min=1,max=5"`
	AppVersion string `json:"app_version" validate:"required,max=50"`
}

// ReviewResponse 评论响应
type ReviewResponse struct {
	ID            uint   `json:"id"`
	UserID        uint   `json:"user_id"`
	Username      string `json:"username"`
	Avatar        string `json:"avatar"`
	ApplicationID uint   `json:"application_id"`
	AppName       string `json:"app_name"`
	AppVersion    string `json:"app_version"`
	Title         string `json:"title"`
	Content       string `json:"content"`
	Rating        int    `json:"rating"`
	LikeCount     int    `json:"like_count"`
	Status        string `json:"status"`
	DevResponse   string `json:"dev_response"`
	CreatedAt     string `json:"created_at"`
	UpdatedAt     string `json:"updated_at"`
}

// DevResponseRequest 开发者回复请求
type DevResponseRequest struct {
	Content string `json:"content" validate:"required,min=1,max=1000"`
}

// CreateReview 创建评论
//
//	@Summary		发表应用评论
//	@Description	用户为应用发表评价和评分
//	@Tags			评论
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			id		path		int								true	"应用ID"
//	@Param			data	body		CreateReviewRequest				true	"评论内容"
//	@Success		200		{object}	response.Response{data=ReviewResponse}	"创建成功，返回评论详情"
//	@Failure		400		{object}	response.Response						"参数错误"
//	@Failure		401		{object}	response.Response						"未授权"
//	@Failure		404		{object}	response.Response						"应用不存在"
//	@Failure		500		{object}	response.Response						"服务器错误"
//	@Router			/apps/{id}/reviews [post]
func (c *ReviewController) CreateReview(ctx *gin.Context) {
	appID, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		response.BadRequest(ctx, "无效的应用ID")
		return
	}

	var req CreateReviewRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, "无效的请求参数")
		return
	}

	// 验证请求参数
	if err := c.Validate.Struct(req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	// 获取用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "")
		return
	}

	// 检查应用是否存在
	var app models.Application
	if err := c.DB.First(&app, appID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "应用不存在")
		} else {
			response.InternalServerError(ctx, "获取应用信息失败")
		}
		return
	}

	// 检查用户是否已经发表过评论
	var count int64
	if err := c.DB.Model(&models.Review{}).Where("user_id = ? AND application_id = ?", userID, appID).Count(&count).Error; err != nil {
		logger.Error("检查用户评论失败", zap.Error(err))
		response.InternalServerError(ctx, "创建评论失败，请稍后重试")
		return
	}

	if count > 0 {
		response.BadRequest(ctx, "您已经评论过此应用，不能重复评论")
		return
	}

	// 创建评论
	review := &models.Review{
		UserID:        userID.(uint),
		ApplicationID: uint(appID),
		AppVersion:    req.AppVersion,
		Title:         req.Title,
		Content:       req.Content,
		Rating:        req.Rating,
		Status:        models.ReviewStatusPending, // 默认为待审核状态
	}

	if err := models.CreateReview(c.DB, review); err != nil {
		logger.Error("创建评论失败", zap.Error(err))
		response.InternalServerError(ctx, "创建评论失败，请稍后重试")
		return
	}

	// 获取用户信息以便返回
	var user models.User
	if err := c.DB.First(&user, userID).Error; err != nil {
		logger.Error("获取用户信息失败", zap.Error(err))
	}

	response.SuccessWithMessage(ctx, "评论成功，等待审核", ReviewResponse{
		ID:            review.ID,
		UserID:        review.UserID,
		Username:      user.Username,
		Avatar:        user.Avatar,
		ApplicationID: review.ApplicationID,
		AppName:       app.Name,
		AppVersion:    review.AppVersion,
		Title:         review.Title,
		Content:       review.Content,
		Rating:        review.Rating,
		LikeCount:     review.LikeCount,
		Status:        string(review.Status),
		DevResponse:   review.DevResponse,
		CreatedAt:     review.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:     review.UpdatedAt.Format("2006-01-02 15:04:05"),
	})
}

// GetAppReviews 获取应用评论
//
//	@Summary		获取应用评论
//	@Description	获取应用的评论和评分
//	@Tags			评论
//	@Accept			json
//	@Produce		json
//	@Param			id			path		int									true	"应用ID"
//	@Param			page		query		int									false	"页码，默认1"	default(1)
//	@Param			page_size	query		int									false	"每页数量，默认20"	default(20)
//	@Success		200			{object}	response.PageResponse{data=[]ReviewResponse}	"返回评论列表和分页信息"
//	@Failure		400			{object}	response.Response							"参数错误"
//	@Failure		404			{object}	response.Response							"应用不存在"
//	@Failure		500			{object}	response.Response							"服务器错误"
//	@Router			/apps/{id}/reviews [get]
func (c *ReviewController) GetAppReviews(ctx *gin.Context) {
	appID, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		response.BadRequest(ctx, "无效的应用ID")
		return
	}

	// 解析分页参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// 检查应用是否存在
	var app models.Application
	if err := c.DB.First(&app, appID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "应用不存在")
		} else {
			response.InternalServerError(ctx, "获取应用信息失败")
		}
		return
	}

	// 获取评论列表
	reviews, total, err := models.GetReviewsByAppID(c.DB, uint(appID), page, pageSize)
	if err != nil {
		logger.Error("获取评论失败", zap.Error(err))
		response.InternalServerError(ctx, "获取评论失败，请稍后重试")
		return
	}

	// 构建返回数据
	result := make([]ReviewResponse, 0, len(reviews))
	for _, review := range reviews {
		result = append(result, ReviewResponse{
			ID:            review.ID,
			UserID:        review.UserID,
			Username:      review.User.Username,
			Avatar:        review.User.Avatar,
			ApplicationID: review.ApplicationID,
			AppName:       app.Name,
			AppVersion:    review.AppVersion,
			Title:         review.Title,
			Content:       review.Content,
			Rating:        review.Rating,
			LikeCount:     review.LikeCount,
			Status:        string(review.Status),
			DevResponse:   review.DevResponse,
			CreatedAt:     review.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:     review.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	response.SuccessWithPagination(ctx, result, total, page, pageSize)
}

// LikeReview 点赞评论
//
//	@Summary		点赞评论
//	@Description	给应用评论点赞
//	@Tags			评论
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			id			path		int			true	"应用ID"
//	@Param			review_id	path		int			true	"评论ID"
//	@Success		200			{object}	response.Response	"点赞成功"
//	@Failure		400			{object}	response.Response	"参数错误"
//	@Failure		401			{object}	response.Response	"未授权"
//	@Failure		404			{object}	response.Response	"评论不存在"
//	@Failure		500			{object}	response.Response	"服务器错误"
//	@Router			/apps/{id}/reviews/{review_id}/like [post]
func (c *ReviewController) LikeReview(ctx *gin.Context) {
	reviewID, err := strconv.Atoi(ctx.Param("review_id"))
	if err != nil {
		response.BadRequest(ctx, "无效的评论ID")
		return
	}

	// 获取用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "")
		return
	}

	// 检查评论是否存在
	var review models.Review
	if err := c.DB.First(&review, reviewID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "评论不存在")
		} else {
			response.InternalServerError(ctx, "获取评论信息失败")
		}
		return
	}

	// 点赞评论
	if err := models.LikeReview(c.DB, userID.(uint), uint(reviewID)); err != nil {
		logger.Error("点赞评论失败", zap.Error(err))
		response.InternalServerError(ctx, "点赞失败，请稍后重试")
		return
	}

	response.SuccessWithMessage(ctx, "点赞成功", nil)
}

// UnlikeReview 取消点赞评论
//
//	@Summary		取消点赞评论
//	@Description	取消对应用评论的点赞
//	@Tags			评论
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			id			path		int			true	"应用ID"
//	@Param			review_id	path		int			true	"评论ID"
//	@Success		200			{object}	response.Response	"取消点赞成功"
//	@Failure		400			{object}	response.Response	"参数错误"
//	@Failure		401			{object}	response.Response	"未授权"
//	@Failure		404			{object}	response.Response	"评论不存在"
//	@Failure		500			{object}	response.Response	"服务器错误"
//	@Router			/apps/{id}/reviews/{review_id}/unlike [post]
func (c *ReviewController) UnlikeReview(ctx *gin.Context) {
	reviewID, err := strconv.Atoi(ctx.Param("review_id"))
	if err != nil {
		response.BadRequest(ctx, "无效的评论ID")
		return
	}

	// 获取用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "")
		return
	}

	// 检查评论是否存在
	var review models.Review
	if err := c.DB.First(&review, reviewID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "评论不存在")
		} else {
			response.InternalServerError(ctx, "获取评论信息失败")
		}
		return
	}

	// 取消点赞评论
	if err := models.UnlikeReview(c.DB, userID.(uint), uint(reviewID)); err != nil {
		logger.Error("取消点赞评论失败", zap.Error(err))
		response.InternalServerError(ctx, "取消点赞失败，请稍后重试")
		return
	}

	response.SuccessWithMessage(ctx, "取消点赞成功", nil)
}

// RespondToReview 开发者回复评论
//
//	@Summary		开发者回复评论
//	@Description	应用开发者回复用户评论
//	@Tags			评论
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			id			path		int								true	"应用ID"
//	@Param			review_id	path		int								true	"评论ID"
//	@Param			data		body		DevResponseRequest				true	"回复内容"
//	@Success		200			{object}	response.Response{data=ReviewResponse}	"回复成功，返回更新后的评论"
//	@Failure		400			{object}	response.Response						"参数错误"
//	@Failure		401			{object}	response.Response						"未授权"
//	@Failure		403			{object}	response.Response						"非应用开发者，无权回复"
//	@Failure		404			{object}	response.Response						"评论不存在"
//	@Failure		500			{object}	response.Response						"服务器错误"
//	@Router			/apps/{id}/reviews/{review_id}/respond [post]
func (c *ReviewController) RespondToReview(ctx *gin.Context) {
	appID, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		response.BadRequest(ctx, "无效的应用ID")
		return
	}

	reviewID, err := strconv.Atoi(ctx.Param("review_id"))
	if err != nil {
		response.BadRequest(ctx, "无效的评论ID")
		return
	}

	var req DevResponseRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, "无效的请求参数")
		return
	}

	// 验证请求参数
	if err := c.Validate.Struct(req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	// 获取用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "")
		return
	}

	// 检查应用是否存在并验证是否为应用开发者
	var app models.Application
	if err := c.DB.First(&app, appID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "应用不存在")
		} else {
			response.InternalServerError(ctx, "获取应用信息失败")
		}
		return
	}

	if app.DeveloperID != userID.(uint) {
		response.Forbidden(ctx, "您不是此应用的开发者，无权回复评论")
		return
	}

	// 检查评论是否存在
	var review models.Review
	if err := c.DB.First(&review, reviewID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "评论不存在")
		} else {
			response.InternalServerError(ctx, "获取评论信息失败")
		}
		return
	}

	// 确保评论属于当前应用
	if review.ApplicationID != uint(appID) {
		response.BadRequest(ctx, "评论不属于当前应用")
		return
	}

	// 更新评论回复
	now := gorm.DeletedAt{Time: app.UpdatedAt, Valid: true}
	review.DevResponse = req.Content
	review.DevResponseAt = &now

	if err := c.DB.Save(&review).Error; err != nil {
		logger.Error("回复评论失败", zap.Error(err))
		response.InternalServerError(ctx, "回复评论失败，请稍后重试")
		return
	}

	// 获取用户信息
	var user models.User
	if err := c.DB.First(&user, review.UserID).Error; err != nil {
		logger.Error("获取用户信息失败", zap.Error(err))
	}

	response.SuccessWithMessage(ctx, "回复成功", ReviewResponse{
		ID:            review.ID,
		UserID:        review.UserID,
		Username:      user.Username,
		Avatar:        user.Avatar,
		ApplicationID: review.ApplicationID,
		AppName:       app.Name,
		AppVersion:    review.AppVersion,
		Title:         review.Title,
		Content:       review.Content,
		Rating:        review.Rating,
		LikeCount:     review.LikeCount,
		Status:        string(review.Status),
		DevResponse:   review.DevResponse,
		CreatedAt:     review.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:     review.UpdatedAt.Format("2006-01-02 15:04:05"),
	})
}
