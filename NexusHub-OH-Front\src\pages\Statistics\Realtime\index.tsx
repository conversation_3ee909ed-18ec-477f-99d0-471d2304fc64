import { <PERSON>Container } from '@ant-design/pro-components';
import { Card, Row, Col, Statistic, Tabs, DatePicker, Radio, Spin } from 'antd';
import { Line, Column, Pie } from '@ant-design/plots';
import { ArrowUpOutlined, ArrowDownOutlined, UserOutlined, DownloadOutlined, DollarOutlined, AppstoreOutlined } from '@ant-design/icons';
import React, { useState, useEffect } from 'react';
import { useRequest } from '@umijs/max';
import dayjs from 'dayjs';

// 模拟数据获取函数
const fetchRealtimeData = async (params: any) => {
  // 实际项目中应该调用API
  console.log('Fetching realtime data with params:', params);
  
  // 模拟数据
  const mockData = {
    overview: {
      activeUsers: 12345,
      activeUsersChange: 5.2,
      downloads: 5678,
      downloadsChange: -2.3,
      sales: 98765,
      salesChange: 8.7,
      newApps: 42,
      newAppsChange: 12.5,
    },
    userTrend: Array.from({ length: 24 }, (_, i) => ({
      time: `${i}:00`,
      value: Math.floor(Math.random() * 5000) + 8000,
      type: '活跃用户',
    })),
    downloadTrend: Array.from({ length: 24 }, (_, i) => ({
      time: `${i}:00`,
      value: Math.floor(Math.random() * 2000) + 3000,
      type: '下载量',
    })),
    salesTrend: Array.from({ length: 24 }, (_, i) => ({
      time: `${i}:00`,
      value: Math.floor(Math.random() * 50000) + 70000,
      type: '销售额',
    })),
    categoryDistribution: [
      { type: '游戏', value: 35 },
      { type: '工具', value: 25 },
      { type: '社交', value: 15 },
      { type: '教育', value: 10 },
      { type: '音乐', value: 8 },
      { type: '其他', value: 7 },
    ],
    platformDistribution: [
      { type: 'Android', value: 55 },
      { type: 'iOS', value: 40 },
      { type: '其他', value: 5 },
    ],
  };

  // 模拟网络延迟
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({ data: mockData });
    }, 500);
  });
};

const RealtimeMonitor: React.FC = () => {
  const [timeRange, setTimeRange] = useState<string>('today');
  const [loading, setLoading] = useState<boolean>(false);
  const [refreshInterval, setRefreshInterval] = useState<number | null>(null);
  
  const { data, run } = useRequest(() => fetchRealtimeData({ timeRange }), {
    refreshDeps: [timeRange],
    onBefore: () => setLoading(true),
    onFinally: () => setLoading(false),
  });

  // 设置自动刷新
  useEffect(() => {
    if (refreshInterval) {
      const timer = setInterval(() => {
        run();
      }, refreshInterval * 1000);
      return () => clearInterval(timer);
    }
    return undefined;
  }, [refreshInterval, run]);

  const handleTimeRangeChange = (e: any) => {
    setTimeRange(e.target.value);
  };

  const handleRefreshChange = (e: any) => {
    setRefreshInterval(e.target.value);
  };

  // 图表配置
  const lineConfig = {
    data: data?.data?.userTrend || [],
    xField: 'time',
    yField: 'value',
    seriesField: 'type',
    smooth: true,
    animation: {
      appear: {
        animation: 'path-in',
        duration: 1000,
      },
    },
  };

  const columnConfig = {
    data: data?.data?.downloadTrend || [],
    xField: 'time',
    yField: 'value',
    seriesField: 'type',
    color: '#1890ff',
    animation: {
      appear: {
        animation: 'wave-in',
        duration: 1000,
      },
    },
  };

  const pieConfig = {
    data: data?.data?.categoryDistribution || [],
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name} {percentage}',
    },
    interactions: [{ type: 'pie-legend-active' }, { type: 'element-active' }],
  };

  const platformPieConfig = {
    data: data?.data?.platformDistribution || [],
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name} {percentage}',
    },
    interactions: [{ type: 'pie-legend-active' }, { type: 'element-active' }],
  };

  return (
    <PageContainer>
      <Card
        title="实时数据监控"
        bordered={false}
        extra={
          <div style={{ display: 'flex', gap: '16px' }}>
            <Radio.Group value={timeRange} onChange={handleTimeRangeChange}>
              <Radio.Button value="today">今日</Radio.Button>
              <Radio.Button value="yesterday">昨日</Radio.Button>
              <Radio.Button value="week">本周</Radio.Button>
              <Radio.Button value="month">本月</Radio.Button>
            </Radio.Group>
            <DatePicker />
            <Radio.Group value={refreshInterval} onChange={handleRefreshChange}>
              <Radio.Button value={null}>关闭刷新</Radio.Button>
              <Radio.Button value={30}>30秒</Radio.Button>
              <Radio.Button value={60}>1分钟</Radio.Button>
              <Radio.Button value={300}>5分钟</Radio.Button>
            </Radio.Group>
          </div>
        }
      >
        <Spin spinning={loading}>
          {/* 概览数据 */}
          <Row gutter={16} style={{ marginBottom: 24 }}>
            <Col span={6}>
              <Card>
                <Statistic
                  title="实时活跃用户"
                  value={data?.data?.overview.activeUsers}
                  prefix={<UserOutlined />}
                  suffix={
                    data?.data?.overview.activeUsersChange > 0 ? (
                      <span style={{ color: '#3f8600' }}>
                        <ArrowUpOutlined /> {data?.data?.overview.activeUsersChange}%
                      </span>
                    ) : (
                      <span style={{ color: '#cf1322' }}>
                        <ArrowDownOutlined /> {Math.abs(data?.data?.overview.activeUsersChange || 0)}%
                      </span>
                    )
                  }
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="今日下载量"
                  value={data?.data?.overview.downloads}
                  prefix={<DownloadOutlined />}
                  suffix={
                    data?.data?.overview.downloadsChange > 0 ? (
                      <span style={{ color: '#3f8600' }}>
                        <ArrowUpOutlined /> {data?.data?.overview.downloadsChange}%
                      </span>
                    ) : (
                      <span style={{ color: '#cf1322' }}>
                        <ArrowDownOutlined /> {Math.abs(data?.data?.overview.downloadsChange || 0)}%
                      </span>
                    )
                  }
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="今日销售额"
                  value={data?.data?.overview.sales}
                  prefix={<DollarOutlined />}
                  suffix={
                    data?.data?.overview.salesChange > 0 ? (
                      <span style={{ color: '#3f8600' }}>
                        <ArrowUpOutlined /> {data?.data?.overview.salesChange}%
                      </span>
                    ) : (
                      <span style={{ color: '#cf1322' }}>
                        <ArrowDownOutlined /> {Math.abs(data?.data?.overview.salesChange || 0)}%
                      </span>
                    )
                  }
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="新增应用"
                  value={data?.data?.overview.newApps}
                  prefix={<AppstoreOutlined />}
                  suffix={
                    data?.data?.overview.newAppsChange > 0 ? (
                      <span style={{ color: '#3f8600' }}>
                        <ArrowUpOutlined /> {data?.data?.overview.newAppsChange}%
                      </span>
                    ) : (
                      <span style={{ color: '#cf1322' }}>
                        <ArrowDownOutlined /> {Math.abs(data?.data?.overview.newAppsChange || 0)}%
                      </span>
                    )
                  }
                />
              </Card>
            </Col>
          </Row>

          {/* 趋势图表 */}
          <Tabs defaultActiveKey="users">
            <Tabs.TabPane tab="用户趋势" key="users">
              <Card title="24小时用户活跃趋势" bordered={false}>
                <Line {...lineConfig} />
              </Card>
            </Tabs.TabPane>
            <Tabs.TabPane tab="下载趋势" key="downloads">
              <Card title="24小时下载趋势" bordered={false}>
                <Column {...columnConfig} />
              </Card>
            </Tabs.TabPane>
            <Tabs.TabPane tab="销售趋势" key="sales">
              <Card title="24小时销售趋势" bordered={false}>
                <Line 
                  {...lineConfig} 
                  data={data?.data?.salesTrend || []} 
                  color="#52c41a"
                />
              </Card>
            </Tabs.TabPane>
          </Tabs>

          {/* 分布图表 */}
          <Row gutter={16} style={{ marginTop: 24 }}>
            <Col span={12}>
              <Card title="应用类别分布" bordered={false}>
                <Pie {...pieConfig} />
              </Card>
            </Col>
            <Col span={12}>
              <Card title="平台分布" bordered={false}>
                <Pie {...platformPieConfig} />
              </Card>
            </Col>
          </Row>
        </Spin>
      </Card>
    </PageContainer>
  );
};

export default RealtimeMonitor;