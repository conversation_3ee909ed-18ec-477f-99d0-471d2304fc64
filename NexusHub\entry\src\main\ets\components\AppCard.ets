import { AppModel } from '../models/App';
import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';

/**
 * 应用卡片组件
 */
@Component
export struct AppCard {
  @Prop app: AppModel;
  @Prop cardType: 'list' | 'grid' | 'featured' = 'grid';
  @Prop showDownloadButton: boolean = true;
  onAppClick?: (app: AppModel) => void;
  onDownloadClick?: (app: AppModel) => void;

  private deviceUtils = DeviceUtils.getInstance();

  /**
   * 格式化文件大小
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }

  /**
   * 格式化下载数量
   */
  private formatDownloadCount(count: number): string {
    if (count < 1000) return count.toString();
    if (count < 1000000) return (count / 1000).toFixed(1) + 'K';
    return (count / 1000000).toFixed(1) + 'M';
  }

  /**
   * 获取评分星星
   */
  @Builder
  private RatingStars(rating: number) {
    Row({ space: '2vp' }) {
      ForEach([1, 2, 3, 4, 5], (star: number) => {
        Text('★')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(12))
          .fontColor(star <= rating ? $r('app.color.star_active') : $r('app.color.star_inactive'))
      })
    }
  }

  /**
   * 列表样式卡片
   */
  @Builder
  private ListCard() {
    Row({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) }) {
      // 应用图标
      Image(this.app.icon || Constants.PLACEHOLDER_IMAGE)
        .width(60)
        .height(60)
        .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
        .objectFit(ImageFit.Cover)
        .onError(() => {
          console.error('AppCard ListCard: Failed to load app icon:', this.app.icon);
        })

      // 应用信息
      Column({ space: '4vp' }) {
        // 应用名称
        Text(this.app.name)
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.MEDIUM))
          .fontColor($r('sys.color.ohos_id_color_text_primary'))
          .fontWeight(FontWeight.Medium)
          .maxLines(1)
          .textOverflow({ overflow: TextOverflow.Ellipsis })

        // 开发者
        Text(this.app.developer_name)
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor($r('sys.color.ohos_id_color_text_secondary'))
          .maxLines(1)
          .textOverflow({ overflow: TextOverflow.Ellipsis })

        // 评分和下载量
        Row({ space: '8vp' }) {
          this.RatingStars(this.app.rating || 0)
          Text((this.app.rating || 0).toFixed(1))
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor($r('sys.color.ohos_id_color_text_secondary'))
          Text('•')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor($r('sys.color.ohos_id_color_text_hint'))
          Text(this.formatDownloadCount(this.app.download_count || 0))
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor($r('sys.color.ohos_id_color_text_secondary'))
        }

        // 应用大小
        Text(this.formatFileSize(this.app.size || 0))
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor($r('sys.color.ohos_id_color_text_hint'))
      }
      .alignItems(HorizontalAlign.Start)
      .layoutWeight(1)

      // 下载按钮
      if (this.showDownloadButton) {
        Button('下载')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .backgroundColor(Constants.COLORS.PRIMARY)
          .fontColor(Constants.COLORS.WHITE)
          .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
          .padding({ left: '16vp', right: '16vp', top: '8vp', bottom: '8vp' })
          .onClick(() => {
            this.onDownloadClick?.(this.app);
          })
      }
    }
    .width('100%')
    .padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
    .backgroundColor($r('sys.color.ohos_id_color_background'))
    .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
    .shadow({
      radius: 4,
      color: $r('sys.color.ohos_id_color_foreground_contrary'),
      offsetX: 0,
      offsetY: 2
    } as ShadowOptions)
    .onClick(() => {
      this.onAppClick?.(this.app);
    })
  }

  /**
   * 网格样式卡片
   */
  @Builder
  private GridCard() {
    Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.SMALL) }) {
      // 应用图标
      Image(this.app.icon || Constants.PLACEHOLDER_IMAGE)
        .width(64)
        .height(64)
        .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
        .objectFit(ImageFit.Cover)
        .onError(() => {
          console.error('AppCard GridCard: Failed to load app icon:', this.app.icon);
        })

      // 应用名称
      Text(this.app.name)
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
        .fontColor($r('sys.color.ohos_id_color_text_primary'))
        .fontWeight(FontWeight.Medium)
        .maxLines(2)
        .textOverflow({ overflow: TextOverflow.Ellipsis })
        .textAlign(TextAlign.Center)
        .width('100%')

      // 开发者
      Text(this.app.developer_name)
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
        .fontColor($r('sys.color.ohos_id_color_text_secondary'))
        .maxLines(1)
        .textOverflow({ overflow: TextOverflow.Ellipsis })
        .textAlign(TextAlign.Center)
        .width('100%')

      // 评分
      Row({ space: '4vp' }) {
        this.RatingStars(this.app.rating || 0)
        Text((this.app.rating || 0).toFixed(1))
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor($r('sys.color.ohos_id_color_text_secondary'))
      }
      .justifyContent(FlexAlign.Center)

      // 下载按钮
      if (this.showDownloadButton) {
        Button('下载')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .backgroundColor(Constants.COLORS.PRIMARY)
          .fontColor(Constants.COLORS.WHITE)
          .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
          .width('100%')
          .height(32)
          .onClick(() => {
            this.onDownloadClick?.(this.app);
          })
      }
    }
    .width('100%')
    .padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
    .backgroundColor($r('sys.color.ohos_id_color_background'))
    .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
    .shadow({
      radius: 4,
      color: $r('sys.color.ohos_id_color_foreground_contrary'),
      offsetX: 0,
      offsetY: 2
    } as ShadowOptions)
    .onClick(() => {
      this.onAppClick?.(this.app);
    })
  }

  /**
   * 精选样式卡片
   */
  @Builder
  private FeaturedCard() {
    Stack({ alignContent: Alignment.BottomStart }) {
      // 背景图片
      Image(this.app.screenshots?.[0] || this.app.icon || Constants.PLACEHOLDER_IMAGE)
        .width('100%')
        .height(200)
        .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
        .objectFit(ImageFit.Cover)
        .onError(() => {
          console.error('AppCard FeaturedCard: Failed to load background image:', this.app.screenshots?.[0] || this.app.icon);
        })

      // 渐变遮罩
      Column()
        .width('100%')
        .height(80)
        .linearGradient({
          direction: GradientDirection.Bottom,
          colors: [['rgba(0,0,0,0)', 0], [$r('app.color.gradient_overlay'), 1]]
        })
        .borderRadius({ bottomLeft: Constants.BORDER_RADIUS.MEDIUM, bottomRight: Constants.BORDER_RADIUS.MEDIUM })

      // 应用信息
      Row({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) }) {
        Image(this.app.icon || Constants.PLACEHOLDER_IMAGE)
          .width(48)
          .height(48)
          .borderRadius(Constants.BORDER_RADIUS.SMALL)
          .objectFit(ImageFit.Cover)
          .onError(() => {
            console.error('AppCard FeaturedCard: Failed to load app icon:', this.app.icon);
          })

        Column({ space: '4vp' }) {
          Text(this.app.name)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.MEDIUM))
            .fontColor(Constants.COLORS.WHITE)
            .fontWeight(FontWeight.Bold)
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })

          Text(this.app.short_description)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.WHITE)
            .opacity(0.9)
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })

          Row({ space: '8vp' }) {
            this.RatingStars(this.app.rating || 0)
            Text((this.app.rating || 0).toFixed(1))
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
              .fontColor(Constants.COLORS.WHITE)
              .opacity(0.9)
          }
        }
        .alignItems(HorizontalAlign.Start)
        .layoutWeight(1)

        if (this.showDownloadButton) {
          Button('下载')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .backgroundColor(Constants.COLORS.WHITE)
            .fontColor(Constants.COLORS.PRIMARY)
            .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
            .padding({ left: '16vp', right: '16vp', top: '8vp', bottom: '8vp' })
            .onClick(() => {
              this.onDownloadClick?.(this.app);
            })
        }
      }
      .width('100%')
      .padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
    }
    .width('100%')
    .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
    .shadow({
      radius: 8,
      color: Constants.COLORS.SHADOW,
      offsetX: 0,
      offsetY: 4
    } as ShadowOptions)
    .onClick(() => {
      this.onAppClick?.(this.app);
    })
  }

  build() {
    if (this.cardType === 'list') {
      this.ListCard()
    } else if (this.cardType === 'featured') {
      this.FeaturedCard()
    } else {
      this.GridCard()
    }
  }
}