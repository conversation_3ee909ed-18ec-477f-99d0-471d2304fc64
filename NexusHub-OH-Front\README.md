# NexusHub-OH 应用商店前端

NexusHub-OH 是为 OpenHarmony 系统开发的应用商店平台，本仓库包含了应用商店的前端实现，基于 React、Ant Design Pro 和 UmiJS 构建。

## 功能概述

NexusHub-OH 应用商店前端主要包含以下功能模块：

### 用户模块
- 用户注册/登录（支持传统认证和SSO）
- Logto SSO 单点登录集成
- 混合认证模式支持
- 用户资料设置和管理
- 开发者认证申请

### 仪表盘模块
- 数据分析页：展示用户、应用、下载等统计数据
- 系统监控页：监控系统运行状态
- 工作台：个人工作中心，提供任务管理等功能

### 应用管理模块
- 应用列表展示
- 应用详情查看
- 开发者应用管理

### 审核模块
- 应用审核功能
- 用户举报处理

### 标签和分类管理
- 应用标签管理
- 应用分类管理

### 评论模块
- 应用评论展示
- 评论点赞
- 开发者回复

### 统计分析模块
- 应用下载统计
- 用户增长趋势
- 分类统计

## 技术栈

- React 18
- Ant Design Pro
- UmiJS
- TypeScript
- Ant Design Charts

## API接入

本项目使用OpenAPI规范进行API接入，通过Ant Design Pro提供的OpenAPI插件自动生成API接口代码。

### OpenAPI配置

项目的OpenAPI配置位于`config/config.ts`文件中：

```typescript
openAPI: [
  {
    requestLibPath: "import { request } from '@umijs/max'",
    schemaPath: join(__dirname, 'oneapi.json'),
    mock: false,
  },
],
```

### 生成API服务

通过以下命令生成API服务代码：

```bash
npm run openapi
```

生成的API服务代码位于`src/services/ant-design-pro/`目录下，包括：

- `typings.d.ts`: API类型定义
- `index.ts`: API服务入口
- 各模块API文件：如`fenleiguanli.ts`(分类管理)、`biaoqianguanli.ts`(标签管理)等

### 模块与API对应关系

| 模块 | API文件 | 说明 |
|------|---------|------|
| 用户管理 | yonghu.ts | 用户注册、登录、资料管理等API |
| 分类管理 | fenleiguanli.ts | 应用分类的增删改查API |
| 标签管理 | biaoqianguanli.ts | 应用标签的增删改查API |
| 应用管理 | yingyong.ts | 应用的创建、更新、删除等API |
| 评论管理 | pinglun.ts | 评论的创建、审核等API |
| 审核管理 | shenheyuan.ts | 应用审核、评论审核等API |
| 仪表盘 | yibiaopan.ts | 数据分析、监控数据等API |

### 使用示例

```typescript
import { getCategories, postCategories } from '@/services/ant-design-pro/fenleiguanli';

// 获取分类列表
const fetchCategories = async () => {
  try {
    const response = await getCategories({});
    setCategories(response || []);
  } catch (error) {
    message.error('获取分类列表失败');
  }
};

// 创建新分类
const createCategory = async (values) => {
  try {
    await postCategories({
      name: values.name,
      description: values.description,
      parent_id: values.parentId,
    });
    message.success('创建分类成功');
  } catch (error) {
    message.error('创建分类失败');
  }
};
```

## 目录结构

```
├── config                # 项目配置文件
│   ├── config.ts         # 主配置
│   ├── routes.ts         # 路由配置
│   └── oneapi.json       # API文档
├── src                   # 源代码
│   ├── components        # 全局组件
│   ├── locales           # 国际化资源
│   ├── models            # 全局数据模型
│   ├── pages             # 页面组件
│   │   ├── App           # 应用相关页面
│   │   ├── Settings      # 设置相关页面
│   │   ├── Statistics    # 统计分析页面
│   │   ├── Review        # 评论相关页面
│   │   ├── UserManagement# 用户管理页面
│   │   ├── dashboard     # 仪表盘相关页面
│   │   │   ├── analysis  # 分析页
│   │   │   ├── monitor   # 监控页
│   │   │   └── workplace # 工作台
│   │   ├── user          # 用户认证页面
│   │   └── account       # 用户账户设置页面
│   ├── services          # API服务接口
│   │   ├── ant-design-pro # OpenAPI生成的接口
│   │   └── ...           # 其他手动编写的接口
│   └── utils             # 工具函数
└── public                # 静态资源
```

## 页面功能说明

### 仪表盘

1. **分析页（Analysis）**
   - 访问路径：`/dashboard/analysis`
   - 功能：展示应用商店的整体数据统计和趋势，包括用户增长、应用发布、下载量等数据分析

2. **监控页（Monitor）**
   - 访问路径：`/dashboard/monitor`
   - 功能：监控系统运行状态，展示服务器CPU、内存使用率，请求量，错误率等指标

3. **工作台（Workplace）**
   - 访问路径：`/dashboard/workplace`
   - 功能：个人工作中心，显示待办任务、最近活动、个人应用统计等

### 应用管理

1. **应用列表（App List）**
   - 访问路径：`/app/list`
   - 功能：显示应用列表，支持分类、搜索和筛选

2. **应用详情（App Detail）**
   - 访问路径：`/app/detail/:id`
   - 功能：展示应用的详细信息，包括版本历史、评论等

### 审核管理

1. **应用审核（App Audit）**
   - 访问路径：`/audit/app`
   - 功能：审核员审核新提交的应用

### 用户管理

1. **用户列表（User List）**
   - 访问路径：`/user-management/list`
   - 功能：管理员查看和管理用户

2. **开发者管理（Developer Management）**
   - 访问路径：`/user-management/developer`
   - 功能：审核开发者认证申请，管理开发者权限

### 设置

1. **标签管理（Tag Management）**
   - 访问路径：`/settings/tags`
   - 功能：管理应用标签

2. **分类管理（Category Management）**
   - 访问路径：`/settings/categories`
   - 功能：管理应用分类

## Logto SSO 集成说明

### 功能概述

NexusHub-OH 前端项目集成了 Logto SSO 单点登录功能，支持传统用户名密码登录和 SSO 登录两种认证方式。用户可以通过环境变量灵活切换认证模式。

### 认证模式

1. **传统认证模式**：用户名/密码登录
2. **SSO 认证模式**：通过 Logto 进行单点登录
3. **混合模式**：在登录页面同时提供两种登录方式

### 环境配置

#### 1. 环境变量设置

复制 `.env.example` 文件为 `.env.local` 并配置以下变量：

```bash
# 是否启用 Logto SSO 认证
REACT_APP_USE_LOGTO=false

# Logto 配置（启用 SSO 时需要）
REACT_APP_LOGTO_ENDPOINT=https://your-logto-endpoint
REACT_APP_LOGTO_APP_ID=your-app-id
REACT_APP_LOGTO_API_RESOURCE=https://your-api-resource

# 后端 API 地址
REACT_APP_API_BASE_URL=http://localhost:8080
```

#### 2. Logto 控制台配置

在 Logto 管理控制台中进行以下配置：

1. **创建应用**：选择 "Single Page Application" 类型
2. **配置重定向 URI**：
   - 开发环境：`http://localhost:8000/callback`
   - 生产环境：`https://your-domain.com/callback`
3. **配置登出后重定向 URI**：
   - 开发环境：`http://localhost:8000`
   - 生产环境：`https://your-domain.com`
4. **配置 API 资源**：设置后端 API 的资源标识符

### 核心组件说明

#### LogtoProvider (`src/components/LogtoProvider/index.tsx`)
- Logto 认证上下文提供者
- 管理全局认证状态
- 包装整个应用

#### AppWrapper (`src/components/AppWrapper/index.tsx`)
- 应用包装器组件
- 根据环境变量决定是否启用 Logto 认证
- 条件性渲染 LogtoProvider

#### HybridLogin (`src/components/HybridLogin/index.tsx`)
- 混合登录组件
- 在传统登录表单中添加 SSO 登录选项
- 处理 SSO 登录跳转

#### useLogtoAuth (`src/hooks/useLogtoAuth.ts`)
- Logto 认证自定义 Hook
- 封装认证相关方法和状态
- 提供用户信息获取、token 管理等功能

#### 回调页面 (`src/pages/callback/index.tsx`)
- 处理 Logto 认证回调
- 获取用户信息并存储认证状态
- 跳转到目标页面

### 认证流程

#### 传统认证流程
1. 用户访问登录页面 `/user/login`
2. 输入用户名和密码
3. 调用后端登录 API
4. 后端验证并返回 JWT token
5. 前端存储 token 到 localStorage
6. 跳转到主页面

#### SSO 认证流程
1. 用户访问登录页面 `/user/login`
2. 点击 "SSO 登录" 按钮
3. 跳转到 Logto 认证页面
4. 用户在 Logto 完成身份验证
5. Logto 重定向回应用的 `/callback` 页面
6. 回调页面处理认证结果
7. 获取用户信息和访问令牌
8. 存储认证信息到 localStorage
9. 跳转到目标页面（默认为 `/dashboard`）

### API 请求认证

项目的请求拦截器 (`src/requestConfig.ts`) 支持两种 token 认证：

1. **传统 token**：存储在 `localStorage.token`
2. **Logto token**：存储在 `localStorage.logto_access_token`

请求拦截器会自动选择合适的 token：
- 如果启用了 Logto 认证，优先使用 Logto token
- 否则使用传统 token
- 自动添加 `Authorization: Bearer <token>` 请求头

### 错误处理

- **401 未授权错误**：自动清除所有 token 并跳转到登录页
- **认证失败**：显示错误提示并允许重试
- **网络错误**：显示友好的错误消息

### 开发调试

#### 查看认证状态
```javascript
// 在浏览器控制台执行
console.log('传统 Token:', localStorage.getItem('token'));
console.log('Logto Token:', localStorage.getItem('logto_access_token'));
console.log('使用 Logto:', process.env.REACT_APP_USE_LOGTO);
```

#### 清除认证状态
```javascript
// 清除所有认证信息
localStorage.removeItem('token');
localStorage.removeItem('logto_access_token');
location.reload();
```

#### 切换认证模式
1. 修改 `.env.local` 文件中的 `REACT_APP_USE_LOGTO` 值
2. 重启开发服务器
3. 清除浏览器缓存和 localStorage

## 开发

### 环境准备

- Node.js >= 16
- pnpm >= 7 (推荐) 或 npm >= 7

### 安装依赖

```bash
pnpm install
# 或
npm install
```

### 启动开发服务器

```bash
pnpm start
# 或
npm start
```

### 构建生产环境

```bash
pnpm build
# 或
npm run build
```

### 生成API服务代码

```bash
pnpm run openapi
# 或
npm run openapi
```

## 开发指南

开发新页面或修改现有页面时，请遵循以下步骤：

1. 检查API接口文档(`config/oneapi.json`)，了解可用的API
2. 使用`npm run openapi`生成最新的API服务代码
3. 在页面组件中导入并使用相应的API服务
4. 开发页面UI和业务逻辑
5. 进行测试和调试

## API路径修复说明

### 问题描述
在开发过程中，前端API请求出现了404错误，主要出现在仪表盘相关页面。问题原因是API请求路径配置不一致，导致出现了路径重复前缀`/api/v1/api/v1/`的情况。

### 解决方案
1. 前端代码中，`src/utils/request.ts`文件已经配置了全局API前缀：
   ```javascript
   export const API_BASE_URL = process.env.NODE_ENV === 'production' 
     ? 'https://api.nexushub-oh.top/api/v1'  // 生产环境
     : '/api/v1';  // 开发环境（走代理）
   ```

2. 在request配置中，已经设置了默认前缀：
   ```javascript
   export const requestConfig: RequestConfig = {
     errorHandler,
     requestInterceptors: [requestInterceptor],
     // 默认前缀
     prefix: API_BASE_URL,
   };
   ```

3. 因此，在页面service文件中调用API时，不应该再重复添加`/api/v1/`前缀，只需要使用相对路径：
   ```javascript
   // 正确用法
   export async function getSummaryData(): Promise<{ data: any }> {
     return request('/dashboard/analytics/summary');
   }
   
   // 错误用法
   export async function getSummaryData(): Promise<{ data: any }> {
     return request('/api/v1/dashboard/analytics/summary');
   }
   ```

4. 已修复的文件：
   - `src/pages/dashboard/analysis/service.ts`
   - `src/pages/dashboard/monitor/service.ts`
   - `src/pages/dashboard/workplace/service.ts`

### 使用说明
在今后开发新的API请求时，请遵循以下规则：
1. 不要在请求URL中手动添加`/api/v1/`前缀，这会导致重复前缀问题
2. 请直接使用相对路径，例如`/dashboard/analytics/summary`
3. 全局前缀已在`src/utils/request.ts`中配置，会自动添加

## 开发指南

详细开发指南请参考 `/docs` 目录下的文档。

## 贡献指南

欢迎提交 Issue 或 Pull Request 来帮助我们改进项目。

## 许可证

[Apache-2.0 许可证](LICENSE)
