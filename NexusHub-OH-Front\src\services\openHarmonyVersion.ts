import { request } from '@umijs/max';

export interface OpenHarmonyVersion {
  id: number;
  version_name: string;
  version_code: number;
  description?: string;
  is_active: boolean;
  usage_count: number;
  created_at: string;
  updated_at: string;
}

export interface CreateOpenHarmonyVersionRequest {
  version_name: string;
  version_code: number;
  description?: string;
  is_active?: boolean;
}

export interface UpdateOpenHarmonyVersionRequest {
  version_name?: string;
  version_code?: number;
  description?: string;
  is_active?: boolean;
}

export interface GetOpenHarmonyVersionsParams {
  page?: number;
  page_size?: number;
  keyword?: string;
}

/**
 * 获取OpenHarmonyOS版本列表
 */
export async function getOpenHarmonyVersions(params?: GetOpenHarmonyVersionsParams) {
  return request<{
    code: number;
    data: OpenHarmonyVersion[];
    total: number;
    page: number;
    page_size: number;
    message?: string;
  }>('/admin/harmony-versions', {
    method: 'GET',
    params,
  });
}

/**
 * 获取单个OpenHarmonyOS版本详情
 */
export async function getOpenHarmonyVersion(id: number) {
  return request<{
    code: number;
    data: OpenHarmonyVersion;
    message?: string;
  }>(`/admin/harmony-versions/${id}`, {
    method: 'GET',
  });
}

/**
 * 创建OpenHarmonyOS版本
 */
export async function createOpenHarmonyVersion(data: CreateOpenHarmonyVersionRequest) {
  return request<{
    code: number;
    data: OpenHarmonyVersion;
    message?: string;
  }>('/admin/harmony-versions', {
    method: 'POST',
    data,
  });
}

/**
 * 更新OpenHarmonyOS版本
 */
export async function updateOpenHarmonyVersion(
  id: number,
  data: UpdateOpenHarmonyVersionRequest
) {
  return request<{
    code: number;
    data: OpenHarmonyVersion;
    message?: string;
  }>(`/admin/harmony-versions/${id}`, {
    method: 'PUT',
    data,
  });
}

/**
 * 删除OpenHarmonyOS版本
 */
export async function deleteOpenHarmonyVersion(id: number) {
  return request<{
    code: number;
    message?: string;
  }>(`/admin/harmony-versions/${id}`, {
    method: 'DELETE',
  });
}

/**
 * 检查版本代码是否存在
 */
export async function checkVersionCodeExists(versionCode: number, excludeId?: number) {
  return request<{
    code: number;
    data: { exists: boolean };
    message?: string;
  }>('/admin/harmony-versions/check-version-code', {
    method: 'POST',
    data: {
      version_code: versionCode,
      exclude_id: excludeId,
    },
  });
}

/**
 * 获取版本使用统计
 */
export async function getVersionUsageStats(id: number) {
  return request<{
    code: number;
    data: { usage_count: number };
    message?: string;
  }>(`/admin/harmony-versions/${id}/usage`, {
    method: 'GET',
  });
}