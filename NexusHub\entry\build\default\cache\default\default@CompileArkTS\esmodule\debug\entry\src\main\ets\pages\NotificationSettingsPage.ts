if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface NotificationSettingsPage_Params {
    settings?: NotificationSettings;
    loadingState?: LoadingState;
    isSaving?: boolean;
    apiService?;
}
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { ApiService } from "@normalized:N&&&entry/src/main/ets/services/ApiService&";
import { LoadingView, LoadingState } from "@normalized:N&&&entry/src/main/ets/components/LoadingView&";
import preferences from "@ohos:data.preferences";
import hilog from "@ohos:hilog";
// getContext is deprecated, use this.getUIContext().getHostContext() instead
/**
 * 通知设置数据模型
 */
interface NotificationSettings extends Record<string, boolean> {
    app_updates: boolean;
    security_alerts: boolean;
    promotional: boolean;
    reviews: boolean;
    downloads: boolean;
    system: boolean;
    email_notifications: boolean;
    push_notifications: boolean;
}
/**
 * 通知设置响应数据模型
 */
interface NotificationSettingsData {
    app_updates?: boolean;
    security_alerts?: boolean;
    promotional?: boolean;
    reviews?: boolean;
    downloads?: boolean;
    system?: boolean;
    email_notifications?: boolean;
    push_notifications?: boolean;
}
class NotificationSettingsPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__settings = new ObservedPropertyObjectPU({
            app_updates: true,
            security_alerts: true,
            promotional: false,
            reviews: true,
            downloads: true,
            system: true,
            email_notifications: false,
            push_notifications: true
        }, this, "settings");
        this.__loadingState = new ObservedPropertySimplePU(LoadingState.LOADING, this, "loadingState");
        this.__isSaving = new ObservedPropertySimplePU(false, this, "isSaving");
        this.apiService = ApiService.getInstance();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: NotificationSettingsPage_Params) {
        if (params.settings !== undefined) {
            this.settings = params.settings;
        }
        if (params.loadingState !== undefined) {
            this.loadingState = params.loadingState;
        }
        if (params.isSaving !== undefined) {
            this.isSaving = params.isSaving;
        }
        if (params.apiService !== undefined) {
            this.apiService = params.apiService;
        }
    }
    updateStateVars(params: NotificationSettingsPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__settings.purgeDependencyOnElmtId(rmElmtId);
        this.__loadingState.purgeDependencyOnElmtId(rmElmtId);
        this.__isSaving.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__settings.aboutToBeDeleted();
        this.__loadingState.aboutToBeDeleted();
        this.__isSaving.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __settings: ObservedPropertyObjectPU<NotificationSettings>;
    get settings() {
        return this.__settings.get();
    }
    set settings(newValue: NotificationSettings) {
        this.__settings.set(newValue);
    }
    private __loadingState: ObservedPropertySimplePU<LoadingState>;
    get loadingState() {
        return this.__loadingState.get();
    }
    set loadingState(newValue: LoadingState) {
        this.__loadingState.set(newValue);
    }
    private __isSaving: ObservedPropertySimplePU<boolean>;
    get isSaving() {
        return this.__isSaving.get();
    }
    set isSaving(newValue: boolean) {
        this.__isSaving.set(newValue);
    }
    private apiService;
    aboutToAppear() {
        this.checkAndSetAuthToken().then(() => {
            this.loadSettings();
        });
    }
    /**
     * 检查并设置认证token
     */
    private async checkAndSetAuthToken(): Promise<void> {
        try {
            const context = this.getUIContext().getHostContext();
            const dataPreferences = preferences.getPreferencesSync(context, { name: 'user_data' });
            const token: preferences.ValueType = dataPreferences.getSync('token', '');
            if (token && typeof token === 'string' && token.length > 0) {
                this.apiService.setAuthToken(token);
            }
            else {
                // 没有token，跳转到登录页面
                this.getUIContext().getRouter().pushUrl({
                    url: 'pages/LoginPage'
                });
            }
        }
        catch (error) {
            hilog.error(0x0000, 'NotificationSettings', '检查登录状态失败: %{public}s', JSON.stringify(error));
            this.getUIContext().getRouter().pushUrl({
                url: 'pages/LoginPage'
            });
        }
    }
    /**
     * 加载通知设置
     */
    private async loadSettings() {
        try {
            this.loadingState = LoadingState.LOADING;
            const response = await this.apiService.getNotificationSettings();
            if (response.code === 200 && response.data) {
                const responseData = response.data as NotificationSettingsData;
                const newSettings: NotificationSettings = {
                    app_updates: responseData.app_updates ?? this.settings.app_updates,
                    security_alerts: responseData.security_alerts ?? this.settings.security_alerts,
                    promotional: responseData.promotional ?? this.settings.promotional,
                    reviews: responseData.reviews ?? this.settings.reviews,
                    downloads: responseData.downloads ?? this.settings.downloads,
                    system: responseData.system ?? this.settings.system,
                    email_notifications: responseData.email_notifications ?? this.settings.email_notifications,
                    push_notifications: responseData.push_notifications ?? this.settings.push_notifications
                };
                this.settings = newSettings;
                this.loadingState = LoadingState.SUCCESS;
            }
            else {
                this.loadingState = LoadingState.ERROR;
            }
        }
        catch (error) {
            hilog.error(0x0000, 'NotificationSettings', '加载通知设置失败: %{public}s', JSON.stringify(error));
            this.loadingState = LoadingState.ERROR;
        }
    }
    /**
     * 保存通知设置
     */
    private async saveSettings() {
        try {
            this.isSaving = true;
            const response = await this.apiService.updateNotificationSettings(this.settings);
            if (response.code === 200) {
                // 显示成功提示
                hilog.info(0x0000, 'NotificationSettings', '通知设置保存成功');
            }
            else {
                hilog.error(0x0000, 'NotificationSettings', '保存通知设置失败: %{public}s', response.message);
            }
        }
        catch (error) {
            hilog.error(0x0000, 'NotificationSettings', '保存通知设置失败: %{public}s', JSON.stringify(error));
        }
        finally {
            this.isSaving = false;
        }
    }
    /**
     * 获取设置值
     */
    private getSettingValue(key: keyof NotificationSettings): boolean {
        switch (key) {
            case 'app_updates':
                return this.settings.app_updates;
            case 'security_alerts':
                return this.settings.security_alerts;
            case 'promotional':
                return this.settings.promotional;
            case 'reviews':
                return this.settings.reviews;
            case 'downloads':
                return this.settings.downloads;
            case 'system':
                return this.settings.system;
            case 'email_notifications':
                return this.settings.email_notifications;
            case 'push_notifications':
                return this.settings.push_notifications;
            default:
                return false;
        }
    }
    /**
     * 切换设置项
     */
    private toggleSetting(key: keyof NotificationSettings) {
        const newSettings: NotificationSettings = {
            app_updates: this.settings.app_updates,
            security_alerts: this.settings.security_alerts,
            promotional: this.settings.promotional,
            reviews: this.settings.reviews,
            downloads: this.settings.downloads,
            system: this.settings.system,
            email_notifications: this.settings.email_notifications,
            push_notifications: this.settings.push_notifications
        };
        // 使用switch语句替代索引访问
        switch (key) {
            case 'app_updates':
                newSettings.app_updates = !this.settings.app_updates;
                break;
            case 'security_alerts':
                newSettings.security_alerts = !this.settings.security_alerts;
                break;
            case 'promotional':
                newSettings.promotional = !this.settings.promotional;
                break;
            case 'reviews':
                newSettings.reviews = !this.settings.reviews;
                break;
            case 'downloads':
                newSettings.downloads = !this.settings.downloads;
                break;
            case 'system':
                newSettings.system = !this.settings.system;
                break;
            case 'email_notifications':
                newSettings.email_notifications = !this.settings.email_notifications;
                break;
            case 'push_notifications':
                newSettings.push_notifications = !this.settings.push_notifications;
                break;
        }
        this.settings = newSettings;
        // 自动保存
        this.saveSettings();
    }
    /**
     * 构建设置项
     */
    buildSettingItem(title: string, description: string, key: keyof NotificationSettings, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.width('100%');
            Row.padding(16);
            Row.backgroundColor(Constants.COLORS.WHITE);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(title);
            Text.fontSize(Constants.FONT_SIZE.NORMAL);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.fontWeight(FontWeight.Medium);
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(description);
            Text.fontSize(Constants.FONT_SIZE.SMALL);
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
            Text.maxLines(2);
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Toggle.create({ type: ToggleType.Switch, isOn: this.getSettingValue(key) });
            Toggle.selectedColor(Constants.COLORS.PRIMARY);
            Toggle.switchPointColor(Constants.COLORS.WHITE);
            Toggle.onChange((isOn: boolean) => {
                this.toggleSetting(key);
            });
        }, Toggle);
        Toggle.pop();
        Row.pop();
    }
    /**
     * 构建分组标题
     */
    buildGroupTitle(title: string, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(title);
            Text.fontSize(Constants.FONT_SIZE.SMALL);
            Text.fontColor(Constants.COLORS.TEXT_HINT);
            Text.fontWeight(FontWeight.Medium);
            Text.padding({ left: 16, right: 16, top: 16, bottom: 8 });
        }, Text);
        Text.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor(Constants.COLORS.BACKGROUND);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 标题栏
            Row.create();
            // 标题栏
            Row.width('100%');
            // 标题栏
            Row.height(56);
            // 标题栏
            Row.padding({ left: 16, right: 16 });
            // 标题栏
            Row.backgroundColor(Constants.COLORS.WHITE);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.width(40);
            Button.height(40);
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                this.getUIContext().getRouter().back();
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777246, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(24);
            Image.height(24);
            Image.fillColor(Constants.COLORS.TEXT_PRIMARY);
        }, Image);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('通知设置');
            Text.fontSize(Constants.FONT_SIZE.LARGE);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 占位符保持居中
            Row.create();
            // 占位符保持居中
            Row.width(40);
            // 占位符保持居中
            Row.height(40);
        }, Row);
        // 占位符保持居中
        Row.pop();
        // 标题栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 内容区域
            if (this.loadingState === LoadingState.LOADING) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/NotificationSettingsPage.ets", line: 298, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {};
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {});
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else if (this.loadingState === LoadingState.ERROR) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.layoutWeight(1);
                        Column.justifyContent(FlexAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Image.create({ "id": 16777254, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                        Image.width(64);
                        Image.height(64);
                        Image.fillColor(Constants.COLORS.TEXT_HINT);
                        Image.margin({ bottom: 16 });
                    }, Image);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('加载失败');
                        Text.fontSize(Constants.FONT_SIZE.MEDIUM);
                        Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                        Text.margin({ bottom: 16 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('重试');
                        Button.fontSize(Constants.FONT_SIZE.NORMAL);
                        Button.fontColor(Constants.COLORS.WHITE);
                        Button.backgroundColor(Constants.COLORS.PRIMARY);
                        Button.borderRadius(8);
                        Button.padding({ left: 24, right: 24, top: 8, bottom: 8 });
                        Button.onClick(() => {
                            this.loadSettings();
                        });
                    }, Button);
                    Button.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Scroll.create();
                        Scroll.layoutWeight(1);
                        Scroll.scrollBar(BarState.Off);
                    }, Scroll);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                    }, Column);
                    // 推送通知设置
                    this.buildGroupTitle.bind(this)('推送通知');
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.backgroundColor(Constants.COLORS.WHITE);
                        Column.borderRadius(12);
                        Column.margin({ left: 16, right: 16, bottom: 16 });
                    }, Column);
                    this.buildSettingItem.bind(this)('启用推送通知', '接收应用推送的通知消息', 'push_notifications');
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Divider.create();
                        Divider.color(Constants.COLORS.BORDER);
                        Divider.margin({ left: 16, right: 16 });
                    }, Divider);
                    this.buildSettingItem.bind(this)('邮件通知', '通过邮件接收重要通知', 'email_notifications');
                    Column.pop();
                    // 应用相关通知
                    this.buildGroupTitle.bind(this)('应用通知');
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.backgroundColor(Constants.COLORS.WHITE);
                        Column.borderRadius(12);
                        Column.margin({ left: 16, right: 16, bottom: 16 });
                    }, Column);
                    this.buildSettingItem.bind(this)('应用更新', '当有新版本发布时通知我', 'app_updates');
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Divider.create();
                        Divider.color(Constants.COLORS.BORDER);
                        Divider.margin({ left: 16, right: 16 });
                    }, Divider);
                    this.buildSettingItem.bind(this)('下载完成', '应用下载完成后通知我', 'downloads');
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Divider.create();
                        Divider.color(Constants.COLORS.BORDER);
                        Divider.margin({ left: 16, right: 16 });
                    }, Divider);
                    this.buildSettingItem.bind(this)('评论回复', '当有人回复我的评论时通知我', 'reviews');
                    Column.pop();
                    // 系统通知
                    this.buildGroupTitle.bind(this)('系统通知');
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.backgroundColor(Constants.COLORS.WHITE);
                        Column.borderRadius(12);
                        Column.margin({ left: 16, right: 16, bottom: 16 });
                    }, Column);
                    this.buildSettingItem.bind(this)('安全提醒', '账户安全相关的重要提醒', 'security_alerts');
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Divider.create();
                        Divider.color(Constants.COLORS.BORDER);
                        Divider.margin({ left: 16, right: 16 });
                    }, Divider);
                    this.buildSettingItem.bind(this)('系统消息', '系统维护、公告等消息', 'system');
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Divider.create();
                        Divider.color(Constants.COLORS.BORDER);
                        Divider.margin({ left: 16, right: 16 });
                    }, Divider);
                    this.buildSettingItem.bind(this)('推广信息', '新功能介绍、活动推广等信息', 'promotional');
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 说明文字
                        Text.create('关闭某些通知可能会影响您及时获取重要信息，请谨慎操作。');
                        // 说明文字
                        Text.fontSize(Constants.FONT_SIZE.SMALL);
                        // 说明文字
                        Text.fontColor(Constants.COLORS.TEXT_HINT);
                        // 说明文字
                        Text.textAlign(TextAlign.Center);
                        // 说明文字
                        Text.padding({ left: 32, right: 32, top: 16, bottom: 32 });
                    }, Text);
                    // 说明文字
                    Text.pop();
                    Column.pop();
                    Scroll.pop();
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 保存状态指示器
            if (this.isSaving) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.justifyContent(FlexAlign.Center);
                        Row.padding(8);
                        Row.backgroundColor(Constants.COLORS.BACKGROUND_LIGHT);
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.width(16);
                        LoadingProgress.height(16);
                        LoadingProgress.margin({ right: 8 });
                    }, LoadingProgress);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('保存中...');
                        Text.fontSize(Constants.FONT_SIZE.SMALL);
                        Text.fontColor(Constants.COLORS.TEXT_HINT);
                    }, Text);
                    Text.pop();
                    Row.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "NotificationSettingsPage";
    }
}
registerNamedRoute(() => new NotificationSettingsPage(undefined, {}), "", { bundleName: "com.example.nexushub", moduleName: "entry", pagePath: "pages/NotificationSettingsPage", pageFullPath: "entry/src/main/ets/pages/NotificationSettingsPage", integratedHsp: "false", moduleType: "followWithHap" });
