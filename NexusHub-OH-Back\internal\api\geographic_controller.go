package api

import (
	"strconv"

	"nexushub-oh-back/internal/models"
	"nexushub-oh-back/pkg/response"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// GeographicController 地理位置控制器
type GeographicController struct {
	db *gorm.DB
}

// NewGeographicController 创建地理位置控制器
func NewGeographicController(db *gorm.DB) *GeographicController {
	return &GeographicController{
		db: db,
	}
}

// CityRegion 城市区域结构体 - 原有数据表结构
type CityRegion struct {
	ID         int    `json:"id" gorm:"column:Fregion_id"`
	ParentID   *int   `json:"parent_id" gorm:"column:Fp_region_id"`
	Name       string `json:"name" gorm:"column:Fcr_name"`
	RegionType int    `json:"region_type" gorm:"column:Fregion_type"`
}

// TableName 指定表名
func (CityRegion) TableName() string {
	return "t_bb_city_region"
}

// 使用 models 包中的 GeographicData 结构体

// CountryResponse 国家响应结构
type CountryResponse struct {
	Key   string `json:"key"`
	Label string `json:"label"`
}

// ProvinceResponse 省份响应结构
type ProvinceResponse struct {
	Key   string `json:"key"`
	Label string `json:"label"`
}

// CityResponse 城市响应结构
type CityResponse struct {
	Key   string `json:"key"`
	Label string `json:"label"`
}

// DistrictResponse 区/镇响应结构
type DistrictResponse struct {
	Key   string `json:"key"`
	Label string `json:"label"`
}

// StreetResponse 街道响应结构
type StreetResponse struct {
	Key   string `json:"key"`
	Label string `json:"label"`
}

// GeographicResponse 通用地理位置响应结构
type GeographicResponse struct {
	Key   string `json:"key"`
	Label string `json:"label"`
}

// GetCountries 获取国家列表 (保持兼容性，返回中国)
//
//	@Summary		获取国家列表
//	@Description	获取所有国家数据
//	@Tags			地理位置
//	@Accept			json
//	@Produce		json
//	@Success		200	{array}	CountryResponse	"国家列表"
//	@Router			/api/v1/geographic/country [get]
func (gc *GeographicController) GetCountries(c *gin.Context) {
	// 由于CSV数据主要是中国的行政区划，这里返回中国作为默认国家
	result := []CountryResponse{
		{
			Key:   "CN",
			Label: "中国",
		},
	}

	response.Success(c, result)
}

// GetProvinces 获取省份列表
//
//	@Summary		获取省份列表
//	@Description	获取所有省份数据
//	@Tags			地理位置
//	@Accept			json
//	@Produce		json
//	@Success		200	{array}	ProvinceResponse	"省份列表"
//	@Router			/api/v1/geographic/province [get]
func (gc *GeographicController) GetProvinces(c *gin.Context) {
	var provinces []models.GeographicData

	// 查询所有省份（deep = 0 表示省份）
	if err := gc.DB.Where("deep = ?", 0).Find(&provinces).Error; err != nil {
		response.InternalServerError(c, "获取省份数据失败")
		return
	}

	// 转换为响应格式
	var result []ProvinceResponse
	for _, province := range provinces {
		result = append(result, ProvinceResponse{
			Key:   strconv.Itoa(province.ID),
			Label: province.Name,
		})
	}

	response.Success(c, result)
}

// GetCities 获取指定省份的城市列表
//
//	@Summary		获取城市列表
//	@Description	根据省份ID获取该省份下的所有城市
//	@Tags			地理位置
//	@Accept			json
//	@Produce		json
//	@Param			province	path	string			true	"省份ID"
//	@Success		200			{array}	CityResponse	"城市列表"
//	@Router			/api/v1/geographic/city/{province} [get]
func (gc *GeographicController) GetCities(c *gin.Context) {
	provinceParam := c.Param("province")

	// 转换省份ID
	provinceID, err := strconv.Atoi(provinceParam)
	if err != nil {
		response.BadRequest(c, "无效的省份ID")
		return
	}

	var cities []models.GeographicData

	// 查询指定省份下的所有城市（deep = 1 表示城市）
	if err := gc.DB.Where("deep = ? AND parent_id = ?", 1, provinceID).Find(&cities).Error; err != nil {
		response.InternalServerError(c, "获取城市数据失败")
		return
	}

	// 转换为响应格式
	var result []CityResponse
	for _, city := range cities {
		result = append(result, CityResponse{
			Key:   strconv.Itoa(city.ID),
			Label: city.Name,
		})
	}

	response.Success(c, result)
}

// GetDistricts 获取指定城市的区/镇列表
//
//	@Summary		获取区/镇列表
//	@Description	根据城市ID获取该城市下的所有区/镇
//	@Tags			地理位置
//	@Accept			json
//	@Produce		json
//	@Param			city	path	string				true	"城市ID"
//	@Success		200		{array}	DistrictResponse	"区/镇列表"
//	@Router			/api/v1/geographic/district/{city} [get]
func (gc *GeographicController) GetDistricts(c *gin.Context) {
	cityParam := c.Param("city")

	// 转换城市ID
	cityID, err := strconv.Atoi(cityParam)
	if err != nil {
		response.BadRequest(c, "无效的城市ID")
		return
	}

	var districts []models.GeographicData

	// 查询指定城市下的所有区/镇（deep = 2 表示区/镇）
	if err := gc.DB.Where("deep = ? AND parent_id = ?", 2, cityID).Find(&districts).Error; err != nil {
		response.InternalServerError(c, "获取区/镇数据失败")
		return
	}

	// 转换为响应格式
	var result []DistrictResponse
	for _, district := range districts {
		result = append(result, DistrictResponse{
			Key:   strconv.Itoa(district.ID),
			Label: district.Name,
		})
	}

	response.Success(c, result)
}

// GetStreets 获取指定区/镇的街道列表
//
//	@Summary		获取街道列表
//	@Description	根据区/镇ID获取该区/镇下的所有街道
//	@Tags			地理位置
//	@Accept			json
//	@Produce		json
//	@Param			district	path	string			true	"区/镇ID"
//	@Success		200			{array}	StreetResponse	"街道列表"
//	@Router			/api/v1/geographic/street/{district} [get]
func (gc *GeographicController) GetStreets(c *gin.Context) {
	districtParam := c.Param("district")

	// 转换区/镇ID
	districtID, err := strconv.Atoi(districtParam)
	if err != nil {
		response.BadRequest(c, "无效的区/镇ID")
		return
	}

	var streets []models.GeographicData

	// 查询指定区/镇下的所有街道（deep = 3 表示街道）
	if err := gc.DB.Where("deep = ? AND parent_id = ?", 3, districtID).Find(&streets).Error; err != nil {
		response.InternalServerError(c, "获取街道数据失败")
		return
	}

	// 转换为响应格式
	var result []StreetResponse
	for _, street := range streets {
		result = append(result, StreetResponse{
			Key:   strconv.Itoa(street.ID),
			Label: street.Name,
		})
	}

	response.Success(c, result)
}

// GetGeographicByLevel 根据级别和父ID获取地理位置数据
//
//	@Summary		根据级别获取地理位置数据
//	@Description	根据级别(0-省份,1-城市,2-区镇,3-街道)和父ID获取地理位置数据
//	@Tags			地理位置
//	@Accept			json
//	@Produce		json
//	@Param			level		query	int					true	"级别(0-省份,1-城市,2-区镇,3-街道)"
//	@Param			parent_id	query	int					false	"父级ID(获取省份时可不传)"
//	@Success		200			{array}	GeographicResponse	"地理位置列表"
//	@Router			/api/v1/geographic/level [get]
func (gc *GeographicController) GetGeographicByLevel(c *gin.Context) {
	levelParam := c.Query("level")
	parentIDParam := c.Query("parent_id")

	// 转换级别
	level, err := strconv.Atoi(levelParam)
	if err != nil || level < 0 || level > 3 {
		response.BadRequest(c, "无效的级别参数，必须为0-3之间的整数")
		return
	}

	var data []models.GeographicData
	query := gc.DB.Where("deep = ?", level)

	// 如果提供了父ID，则添加父ID条件
	if parentIDParam != "" {
		parentID, err := strconv.Atoi(parentIDParam)
		if err != nil {
			response.BadRequest(c, "无效的父ID参数")
			return
		}
		query = query.Where("parent_id = ?", parentID)
	} else if level > 0 {
		// 如果级别大于0但没有提供父ID，返回错误
		response.BadRequest(c, "获取非省份级别数据时必须提供父ID")
		return
	}

	if err := query.Find(&data).Error; err != nil {
		response.InternalServerError(c, "获取地理位置数据失败")
		return
	}

	// 转换为响应格式
	var result []GeographicResponse
	for _, item := range data {
		result = append(result, GeographicResponse{
			Key:   strconv.Itoa(item.ID),
			Label: item.Name,
		})
	}

	response.Success(c, result)
}
