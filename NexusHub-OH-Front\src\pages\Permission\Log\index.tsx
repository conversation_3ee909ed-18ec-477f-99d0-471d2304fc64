import { PageContainer } from '@ant-design/pro-components';
import { Card, Table, Button, Space, Input, DatePicker, Select, Tag, Tooltip, Drawer, Descriptions, Badge, Form, Popconfirm } from 'antd';
import { SearchOutlined, ReloadOutlined, EyeOutlined, DownloadOutlined, DeleteOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { useRequest } from '@umijs/max';
import type { ColumnsType } from 'antd/es/table';
import moment from 'moment';

const { RangePicker } = DatePicker;
const { Option } = Select;

interface LogItem {
  id: string;
  userId: string;
  username: string;
  module: string;
  action: string;
  ip: string;
  status: 'success' | 'fail';
  detail: string;
  createdAt: string;
  duration: number;
}

// 模拟数据获取函数
const fetchLogs = async (params: any) => {
  console.log('Fetching logs with params:', params);
  
  // 模拟数据
  const mockData: LogItem[] = [
    {
      id: 'log001',
      userId: 'user001',
      username: 'admin',
      module: '用户管理',
      action: '登录',
      ip: '***********',
      status: 'success',
      detail: '管理员登录系统',
      createdAt: '2023-11-20 10:00:00',
      duration: 120,
    },
    {
      id: 'log002',
      userId: 'user001',
      username: 'admin',
      module: '应用管理',
      action: '审核应用',
      ip: '***********',
      status: 'success',
      detail: '审核通过应用"微信"',
      createdAt: '2023-11-20 10:15:00',
      duration: 3500,
    },
    {
      id: 'log003',
      userId: 'user002',
      username: 'editor',
      module: '内容管理',
      action: '添加推荐',
      ip: '***********',
      status: 'success',
      detail: '添加推荐内容"热门游戏"',
      createdAt: '2023-11-20 11:00:00',
      duration: 2100,
    },
    {
      id: 'log004',
      userId: 'user003',
      username: 'reviewer',
      module: '评论管理',
      action: '删除评论',
      ip: '***********',
      status: 'success',
      detail: '删除违规评论',
      createdAt: '2023-11-20 11:30:00',
      duration: 800,
    },
    {
      id: 'log005',
      userId: 'user002',
      username: 'editor',
      module: '标签管理',
      action: '创建标签',
      ip: '***********',
      status: 'success',
      detail: '创建标签"热门"',
      createdAt: '2023-11-20 13:00:00',
      duration: 650,
    },
    {
      id: 'log006',
      userId: 'user004',
      username: 'operator',
      module: '用户管理',
      action: '禁用用户',
      ip: '***********',
      status: 'success',
      detail: '禁用违规用户"user123"',
      createdAt: '2023-11-20 14:00:00',
      duration: 1200,
    },
    {
      id: 'log007',
      userId: 'user001',
      username: 'admin',
      module: '系统设置',
      action: '修改配置',
      ip: '***********',
      status: 'success',
      detail: '修改系统参数配置',
      createdAt: '2023-11-20 15:00:00',
      duration: 1800,
    },
    {
      id: 'log008',
      userId: 'user005',
      username: 'guest',
      module: '用户管理',
      action: '登录',
      ip: '***********',
      status: 'fail',
      detail: '密码错误',
      createdAt: '2023-11-20 16:00:00',
      duration: 100,
    },
    {
      id: 'log009',
      userId: 'user003',
      username: 'reviewer',
      module: '应用管理',
      action: '审核应用',
      ip: '***********',
      status: 'success',
      detail: '审核拒绝应用"测试应用"',
      createdAt: '2023-11-20 16:30:00',
      duration: 4200,
    },
    {
      id: 'log010',
      userId: 'user001',
      username: 'admin',
      module: '权限管理',
      action: '分配角色',
      ip: '***********',
      status: 'success',
      detail: '为用户"editor"分配"内容管理员"角色',
      createdAt: '2023-11-20 17:00:00',
      duration: 1500,
    },
    {
      id: 'log011',
      userId: 'user006',
      username: 'developer',
      module: '应用管理',
      action: '创建应用',
      ip: '***********',
      status: 'success',
      detail: '创建应用"新游戏"',
      createdAt: '2023-11-21 09:00:00',
      duration: 2800,
    },
    {
      id: 'log012',
      userId: 'user002',
      username: 'editor',
      module: '活动管理',
      action: '创建活动',
      ip: '***********',
      status: 'success',
      detail: '创建活动"双十一促销"',
      createdAt: '2023-11-21 10:00:00',
      duration: 3200,
    },
    {
      id: 'log013',
      userId: 'user007',
      username: 'tester',
      module: '系统监控',
      action: '查看日志',
      ip: '***********',
      status: 'success',
      detail: '查看系统运行日志',
      createdAt: '2023-11-21 11:00:00',
      duration: 5000,
    },
    {
      id: 'log014',
      userId: 'user001',
      username: 'admin',
      module: '备份管理',
      action: '创建备份',
      ip: '***********',
      status: 'success',
      detail: '创建系统数据备份',
      createdAt: '2023-11-21 12:00:00',
      duration: 15000,
    },
    {
      id: 'log015',
      userId: 'user004',
      username: 'operator',
      module: '内容管理',
      action: '更新内容',
      ip: '***********',
      status: 'fail',
      detail: '更新内容失败，数据格式错误',
      createdAt: '2023-11-21 13:00:00',
      duration: 800,
    },
  ];

  // 根据时间范围过滤
  let timeFilteredData = mockData;
  if (params.timeRange && params.timeRange.length === 2) {
    const startTime = moment(params.timeRange[0]).startOf('day');
    const endTime = moment(params.timeRange[1]).endOf('day');
    timeFilteredData = mockData.filter(item => {
      const itemTime = moment(item.createdAt);
      return itemTime.isBetween(startTime, endTime, null, '[]');
    });
  }
  
  // 根据模块过滤
  const moduleFilteredData = params.module ? timeFilteredData.filter(item => item.module === params.module) : timeFilteredData;
  
  // 根据状态过滤
  const statusFilteredData = params.status ? moduleFilteredData.filter(item => item.status === params.status) : moduleFilteredData;
  
  // 根据关键词过滤
  const keywordFilteredData = params.keyword
    ? statusFilteredData.filter(item => 
        item.username.toLowerCase().includes(params.keyword.toLowerCase()) ||
        item.action.toLowerCase().includes(params.keyword.toLowerCase()) ||
        item.detail.toLowerCase().includes(params.keyword.toLowerCase())
      )
    : statusFilteredData;

  return { data: keywordFilteredData, total: keywordFilteredData.length };
};

const LogManagement: React.FC = () => {
  const [searchParams, setSearchParams] = useState({});
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [currentLog, setCurrentLog] = useState<LogItem | null>(null);

  const { data, loading, refresh } = useRequest(() => fetchLogs(searchParams), {
    refreshDeps: [searchParams],
  });

  const handleSearch = (values: any) => {
    setSearchParams(values);
  };

  const showLogDetail = (record: LogItem) => {
    setCurrentLog(record);
    setDrawerVisible(true);
  };

  const handleExport = () => {
    // 模拟导出功能
    message.success('日志导出成功');
  };

  const handleClearLogs = () => {
    // 模拟清空日志功能
    message.success('日志已清空');
    refresh();
  };

  const moduleOptions = [
    { label: '用户管理', value: '用户管理' },
    { label: '应用管理', value: '应用管理' },
    { label: '内容管理', value: '内容管理' },
    { label: '评论管理', value: '评论管理' },
    { label: '标签管理', value: '标签管理' },
    { label: '系统设置', value: '系统设置' },
    { label: '权限管理', value: '权限管理' },
    { label: '活动管理', value: '活动管理' },
    { label: '系统监控', value: '系统监控' },
    { label: '备份管理', value: '备份管理' },
  ];

  const columns: ColumnsType<LogItem> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      width: 120,
    },
    {
      title: '模块',
      dataIndex: 'module',
      key: 'module',
      width: 120,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 120,
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const color = status === 'success' ? 'success' : 'error';
        const text = status === 'success' ? '成功' : '失败';
        return <Badge status={color} text={text} />;
      },
    },
    {
      title: '操作详情',
      dataIndex: 'detail',
      key: 'detail',
      ellipsis: true,
    },
    {
      title: '耗时(ms)',
      dataIndex: 'duration',
      key: 'duration',
      width: 100,
      render: (duration: number) => {
        let color = 'green';
        if (duration > 5000) {
          color = 'red';
        } else if (duration > 2000) {
          color = 'orange';
        }
        return <Tag color={color}>{duration}</Tag>;
      },
    },
    {
      title: '操作时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              onClick={() => showLogDetail(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer
      header={{
        title: '操作日志',
        subTitle: '记录系统操作日志',
      }}
    >
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Form layout="inline" onFinish={handleSearch}>
            <Form.Item name="keyword" label="关键词">
              <Input 
                placeholder="搜索用户名/操作/详情" 
                prefix={<SearchOutlined />}
                style={{ width: 200 }}
              />
            </Form.Item>
            <Form.Item name="timeRange" label="时间范围">
              <RangePicker 
                style={{ width: 300 }}
              />
            </Form.Item>
            <Form.Item name="module" label="模块">
              <Select 
                placeholder="选择模块" 
                style={{ width: 150 }}
                allowClear
              >
                {moduleOptions.map(option => (
                  <Option key={option.value} value={option.value}>{option.label}</Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name="status" label="状态">
              <Select 
                placeholder="选择状态" 
                style={{ width: 120 }}
                allowClear
              >
                <Option value="success">成功</Option>
                <Option value="fail">失败</Option>
              </Select>
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                搜索
              </Button>
            </Form.Item>
            <Form.Item>
              <Button onClick={() => setSearchParams({})}>
                重置
              </Button>
            </Form.Item>
          </Form>
        </div>

        <div style={{ marginBottom: 16 }}>
          <Button 
            icon={<DownloadOutlined />} 
            onClick={handleExport}
            style={{ marginRight: 8 }}
          >
            导出日志
          </Button>
          <Popconfirm
            title="确定要清空所有日志吗？此操作不可恢复！"
            onConfirm={handleClearLogs}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              danger 
              icon={<DeleteOutlined />}
              style={{ marginRight: 8 }}
            >
              清空日志
            </Button>
          </Popconfirm>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={refresh}
          >
            刷新
          </Button>
        </div>

        <Table
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
          columns={columns}
          dataSource={data?.data}
          rowKey="id"
          loading={loading}
          pagination={{
            total: data?.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
          }}
        />
      </Card>

      {/* 日志详情抽屉 */}
      <Drawer
        title="日志详情"
        placement="right"
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
        width={500}
      >
        {currentLog && (
          <Descriptions column={1} bordered>
            <Descriptions.Item label="ID">{currentLog.id}</Descriptions.Item>
            <Descriptions.Item label="用户ID">{currentLog.userId}</Descriptions.Item>
            <Descriptions.Item label="用户名">{currentLog.username}</Descriptions.Item>
            <Descriptions.Item label="模块">{currentLog.module}</Descriptions.Item>
            <Descriptions.Item label="操作">{currentLog.action}</Descriptions.Item>
            <Descriptions.Item label="IP地址">{currentLog.ip}</Descriptions.Item>
            <Descriptions.Item label="状态">
              <Badge 
                status={currentLog.status === 'success' ? 'success' : 'error'} 
                text={currentLog.status === 'success' ? '成功' : '失败'} 
              />
            </Descriptions.Item>
            <Descriptions.Item label="操作详情">{currentLog.detail}</Descriptions.Item>
            <Descriptions.Item label="耗时(ms)">{currentLog.duration}</Descriptions.Item>
            <Descriptions.Item label="操作时间">{currentLog.createdAt}</Descriptions.Item>
          </Descriptions>
        )}
      </Drawer>
    </PageContainer>
  );
};

export default LogManagement;