# NexusHub - 鸿蒙应用商店

## 项目简介

NexusHub是一个基于华为鸿蒙操作系统(HarmonyOS)开发的应用商店项目，提供完整的应用发现、下载、管理功能。项目采用前后端分离架构，包含鸿蒙客户端、Web管理后台和Go语言后端服务。

## 项目结构

```
NexusHub-OH/
├── NexusHub/                    # 鸿蒙客户端应用
│   ├── entry/src/main/ets/      # 主要源码目录
│   │   ├── pages/               # 页面文件
│   │   ├── services/            # 服务层
│   │   ├── models/              # 数据模型
│   │   ├── utils/               # 工具类
│   │   └── components/          # 自定义组件
│   └── AppScope/                # 应用配置
├── NexusHub-OH-Back/            # Go后端服务
│   ├── cmd/server/              # 服务器启动入口
│   ├── internal/                # 内部业务逻辑
│   ├── pkg/                     # 公共包
│   └── config/                  # 配置文件
└── NexusHub-OH-Front/           # Web管理后台
    ├── src/                     # 前端源码
    └── config/                  # 前端配置
```

## 主要功能

### 鸿蒙客户端功能

#### 1. 用户认证系统
- **登录/注册页面** (`LoginPage.ets`)
  - 支持邮箱密码登录
  - 用户注册功能，包含地理位置选择
  - 第三方登录支持（微信、QQ、微博）
  - 密码显示/隐藏切换
  - 用户协议和隐私政策确认

#### 2. 应用搜索与发现
- **搜索页面** (`SearchPage.ets`)
  - 智能搜索功能，支持关键词搜索
  - 搜索历史记录
  - 热门搜索推荐
  - 分类筛选
  - 排序选项（相关度、下载量、评分、时间）
  - 分页加载
  - 公开搜索API支持（无需登录）

#### 3. 应用详情与管理
- **应用详情页面** (`AppDetailPage.ets`)
  - 应用基本信息展示
  - 截图和视频预览
  - 用户评价和评分
  - 下载和安装功能
  - 收藏和分享功能

#### 4. 用户个人中心
- **个人资料页面** (`ProfilePage.ets`)
  - 用户信息展示和编辑
  - 我的下载、收藏、评价
  - 浏览历史
  - 设置入口
  - **通知中心**（新增）
  - **系统状态**（新增）

#### 5. 通知系统（新增功能）
- **通知页面** (`NotificationPage.ets`)
  - 通知列表展示
  - 已读/未读状态管理
  - 通知删除功能
  - 点击通知跳转到相关页面
  - 分页加载
  - 未读通知计数

- **通知设置页面** (`NotificationSettingsPage.ets`)
  - 推送通知开关
  - 下载完成通知
  - 应用更新通知
  - 系统消息通知
  - 营销消息通知
  - 夜间免打扰设置

#### 6. 地理位置选择（新增功能）
- **位置选择页面** (`LocationPickerPage.ets`)
  - 支持国家、省份、城市、区县、街道五级选择
  - 搜索功能，支持按名称或代码搜索
  - 级联选择，上级选择后自动加载下级选项
  - 用于用户注册时填写地理位置信息

#### 7. 系统监控（新增功能）
- **系统状态页面** (`SystemStatusPage.ets`)
  - 系统健康状态监控
  - 服务状态检查
  - 系统指标展示（CPU、内存、磁盘使用率）
  - 系统配置信息
  - 自动刷新功能

#### 8. 应用设置
- **设置页面** (`SettingsPage.ets`)
  - 下载设置（自动更新、WiFi下载等）
  - 通知设置入口
  - 界面设置（深色模式、语言选择）
  - 存储设置（缓存管理）
  - 关于信息

### 后端API功能

#### 1. 用户管理
- 用户注册/登录
- 用户信息管理
- 地理位置信息存储

#### 2. 应用管理
- 应用信息CRUD
- 应用搜索（支持Elasticsearch）
- 应用分类管理
- 应用统计

#### 3. 通知系统
- 通知发送和管理
- 通知设置管理
- 推送服务集成

#### 4. 地理位置服务
- 国家、省份、城市、区县、街道数据管理
- 地理位置查询API
- 级联查询支持

#### 5. 系统监控
- 系统健康检查
- 服务状态监控
- 系统配置管理

## 技术栈

### 鸿蒙客户端
- **开发语言**: ArkTS
- **UI框架**: ArkUI
- **状态管理**: @State装饰器
- **路由管理**: @ohos.router
- **数据存储**: @ohos.data.preferences
- **网络请求**: @ohos.net.http

### 后端服务
- **开发语言**: Go
- **Web框架**: Gin
- **数据库**: PostgreSQL
- **搜索引擎**: Elasticsearch
- **消息队列**: RabbitMQ
- **API文档**: Swagger

### Web管理后台
- **开发语言**: TypeScript
- **前端框架**: React + Ant Design Pro
- **构建工具**: Umi
- **状态管理**: Redux

## 安装和运行

### 环境要求
- HarmonyOS SDK 4.0+
- DevEco Studio 4.0+
- Go 1.19+
- Node.js 16+
- PostgreSQL 13+
- Elasticsearch 8.0+

### 后端服务启动

1. 进入后端目录
```bash
cd NexusHub-OH-Back
```

2. 安装依赖
```bash
go mod download
```

3. 配置数据库和环境变量
```bash
cp config.yaml.example config.yaml
# 编辑config.yaml文件，配置数据库连接等信息
```

4. 运行服务
```bash
go run main.go
```

服务将在 http://localhost:8080 启动

### Web管理后台启动

1. 进入前端目录
```bash
cd NexusHub-OH-Front
```

2. 安装依赖
```bash
npm install
```

3. 启动开发服务器
```bash
npm run start:dev
```

前端将在 http://localhost:8000 启动

### 鸿蒙客户端运行

1. 使用DevEco Studio打开NexusHub目录
2. 配置签名和证书
3. 连接鸿蒙设备或启动模拟器
4. 点击运行按钮

## API接口说明

### 用户相关接口
- `POST /api/users/register` - 用户注册
- `POST /api/users/login` - 用户登录
- `GET /api/users/profile` - 获取用户信息
- `PUT /api/users/profile` - 更新用户信息

### 应用相关接口
- `GET /api/apps` - 获取应用列表
- `GET /api/apps/search` - 搜索应用
- `GET /api/apps/search/public` - 公开搜索（无需登录）
- `GET /api/apps/:id` - 获取应用详情
- `POST /api/apps/:id/download` - 下载应用

### 通知相关接口
- `GET /api/notifications` - 获取通知列表
- `GET /api/notifications/unread-count` - 获取未读通知数量
- `PUT /api/notifications/:id/read` - 标记通知为已读
- `DELETE /api/notifications/:id` - 删除通知
- `GET /api/notification-settings` - 获取通知设置
- `PUT /api/notification-settings` - 更新通知设置

### 地理位置相关接口
- `GET /api/geographic/countries` - 获取国家列表
- `GET /api/geographic/provinces` - 获取省份列表
- `GET /api/geographic/cities` - 获取城市列表
- `GET /api/geographic/districts` - 获取区县列表
- `GET /api/geographic/streets` - 获取街道列表

### 系统监控接口
- `GET /api/system/health` - 系统健康检查
- `GET /api/system/config` - 获取系统配置

## 数据模型

### 用户注册模型 (RegisterRequest)
```typescript
interface RegisterRequest {
  username: string;        // 用户名
  email: string;          // 邮箱
  phone?: string;         // 手机号（可选）
  password: string;       // 密码
  country?: string;       // 国家（可选）
  province?: string;      // 省份（可选）
  city?: string;         // 城市（可选）
  district?: string;     // 区县（可选）
  street?: string;       // 街道（可选）
}
```

### 通知模型 (NotificationModel)
```typescript
interface NotificationModel {
  id: number;            // 通知ID
  title: string;         // 通知标题
  content: string;       // 通知内容
  type: string;          // 通知类型
  isRead: boolean;       // 是否已读
  createdAt: string;     // 创建时间
  relatedId?: number;    // 关联ID
  relatedType?: string;  // 关联类型
}
```

### 地理位置模型 (LocationModel)
```typescript
interface LocationModel {
  id: number;           // 位置ID
  name: string;         // 位置名称
  code: string;         // 位置代码
  parentCode?: string;  // 父级代码
  level: number;        // 级别（1-5）
}
```

## 开发规范

### 代码规范
1. 使用TypeScript/ArkTS严格模式
2. 遵循ESLint和Prettier配置
3. 组件命名使用PascalCase
4. 方法命名使用camelCase
5. 常量命名使用UPPER_SNAKE_CASE

### 文件组织
1. 页面文件放在`pages/`目录
2. 组件文件放在`components/`目录
3. 服务文件放在`services/`目录
4. 工具类放在`utils/`目录
5. 数据模型放在`models/`目录

### Git提交规范
- `feat:` 新功能
- `fix:` 修复bug
- `docs:` 文档更新
- `style:` 代码格式调整
- `refactor:` 代码重构
- `test:` 测试相关
- `chore:` 构建过程或辅助工具的变动

## 更新日志

### v1.2.0 (最新)
- ✨ 新增通知系统，支持通知列表和设置管理
- ✨ 新增地理位置选择功能，支持五级地理位置选择
- ✨ 新增系统状态监控页面
- ✨ 用户注册支持地理位置信息填写
- 🔧 优化搜索功能，支持公开搜索API
- 🔧 完善个人中心功能入口
- 🔧 优化设置页面，新增通知设置入口

### v1.1.0
- ✨ 完善应用搜索功能
- ✨ 新增应用详情页面
- 🔧 优化用户认证流程
- 🔧 完善API接口设计

### v1.0.0
- 🎉 项目初始版本
- ✨ 基础用户认证系统
- ✨ 应用列表和搜索功能
- ✨ 个人中心基础功能

## 贡献指南

1. Fork本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目维护者：NexusHub团队
- 邮箱：<EMAIL>
- 项目地址：https://github.com/nexushub/nexushub-oh

## 致谢

感谢华为鸿蒙团队提供的优秀开发平台和工具链支持。