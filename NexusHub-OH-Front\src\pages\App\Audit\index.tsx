import { PageContainer } from '@ant-design/pro-components';
import { Card, Table, Button, Space, Tag, message, Modal, Form, Input } from 'antd';
import { CheckOutlined, CloseOutlined, EyeOutlined, ReloadOutlined } from '@ant-design/icons';
import React, { useState, useEffect } from 'react';
import type { ColumnsType } from 'antd/es/table';
import { getReviewerAppsPending, postReviewerAppsIdReview } from '@/services/ant-design-pro/shenheyuan';

interface AppItem {
  id: number;
  name: string;
  developer_name: string;
  current_version: string;
  category: string;
  created_at: string;
  status: 'pending' | 'approved' | 'rejected' | 'published';
  package: string;
  description: string;
  is_verified: boolean;
  is_featured: boolean;
  download_count: number;
  average_rating: number;
}

// 获取待审核应用列表数据
const fetchPendingAppList = async (params: any) => {
  try {
    console.log('Fetching pending apps with params:', params);
    
    // 调用审核API获取待审核应用数据
    const response = await getReviewerAppsPending({
      page: params.page || 1,
      page_size: params.page_size || 20,
    });
    
    console.log('API响应数据:', response);
    
    if (response && response.code === 200 && response.data) {
      // 转换数据格式以适配表格显示
      const apps = response.data.data?.map((app: any) => ({
        id: app.id,
        name: app.name,
        developer_name: app.developer?.username || app.developer_name || '未知开发者',
        current_version: app.current_version,
        category: app.category,
        created_at: app.created_at,
        status: app.status,
        package: app.package,
        description: app.description,
        is_verified: app.is_verified,
        is_featured: app.is_featured,
        download_count: app.download_count || 0,
        average_rating: app.average_rating || 0,
      })) || [];
      
      console.log('处理后的应用数据:', apps);
    console.log('应用数据详情:', JSON.stringify(apps, null, 2));
    console.log('数据长度:', apps.length);
      
      return {
      data: apps,
      total: response.data.total || apps.length,
      page: response.data.page || 1,
      page_size: response.data.page_size || 20,
    };
    }
    
    return { data: [], total: 0 };
  } catch (error) {
    console.error('获取待审核应用列表失败:', error);
    message.error('获取待审核应用列表失败，请稍后重试');
    return { data: [], total: 0 };
  }
};

const AppAudit: React.FC = () => {
  const [searchParams, setSearchParams] = useState<any>({});
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await fetchPendingAppList(searchParams);
      console.log('fetchData result:', result);
      setData(result);
    } catch (err) {
      console.error('fetchData error:', err);
      setError(err instanceof Error ? err.message : '获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  const run = () => {
    fetchData();
  };

  useEffect(() => {
    fetchData();
  }, [searchParams]);

  console.log('手动管理的data:', data);
  console.log('Table的dataSource:', data?.data);
  console.log('data是否为数组:', Array.isArray(data?.data));
  console.log('data的类型:', typeof data);
  console.log('total:', data?.total);

  const handleApprove = async (id: number) => {
    try {
      const response = await postReviewerAppsIdReview(
        { id: Number(id) },
        { status: 'approved', reason: '审核通过' }
      );
      
      if (response && response.code === 200) {
        message.success('应用审核通过');
        run(); // 刷新列表
      } else {
        message.error(response?.message || '审核失败');
      }
    } catch (error) {
      console.error('审核失败:', error);
      message.error('审核失败，请稍后重试');
    }
  };

  const [rejectModalVisible, setRejectModalVisible] = useState(false);
  const [currentRejectApp, setCurrentRejectApp] = useState<AppItem | null>(null);
  const [rejectForm] = Form.useForm();

  const handleReject = (app: AppItem) => {
    setCurrentRejectApp(app);
    setRejectModalVisible(true);
    rejectForm.resetFields();
  };

  const handleRejectSubmit = async () => {
    if (!currentRejectApp) return;
    
    try {
      const values = await rejectForm.validateFields();
      const response = await postReviewerAppsIdReview(
        { id: currentRejectApp.id },
        { status: 'rejected', reason: values.reason }
      );
      
      if (response && response.code === 200) {
        message.success('应用已拒绝');
        setRejectModalVisible(false);
        setCurrentRejectApp(null);
        rejectForm.resetFields();
        run(); // 刷新列表
      } else {
        message.error(response?.message || '操作失败');
      }
    } catch (error) {
      console.error('操作失败:', error);
      message.error('操作失败，请稍后重试');
    }
  };

  const handleRejectCancel = () => {
    setRejectModalVisible(false);
    setCurrentRejectApp(null);
    rejectForm.resetFields();
  };

  const handleView = (id: number) => {
    // 跳转到应用详情页
    window.open(`/app/detail/${id}`, '_blank');
  };

  const columns: ColumnsType<AppItem> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '应用名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      ellipsis: true,
    },
    {
      title: '开发者',
      dataIndex: 'developer_name',
      key: 'developer_name',
      width: 120,
      ellipsis: true,
    },
    {
      title: '版本',
      dataIndex: 'current_version',
      key: 'current_version',
      width: 100,
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 100,
    },
    {
      title: '包名',
      dataIndex: 'package',
      key: 'package',
      width: 150,
      ellipsis: true,
    },
    {
      title: '下载量',
      dataIndex: 'download_count',
      key: 'download_count',
      width: 100,
      render: (count: number) => count?.toLocaleString() || 0,
    },
    {
      title: '评分',
      dataIndex: 'average_rating',
      key: 'average_rating',
      width: 80,
      render: (rating: number) => rating ? rating.toFixed(1) : '暂无',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (time: string) => time ? new Date(time).toLocaleString() : '-',
      sorter: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        let color = 'blue';
        let text = '待审核';
        
        if (status === 'approved') {
          color = 'green';
          text = '已通过';
        } else if (status === 'rejected') {
          color = 'red';
          text = '已拒绝';
        }
        
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button 
            type="primary" 
            icon={<EyeOutlined />} 
            size="small"
            onClick={() => handleView(record.id)}
          >
            查看
          </Button>
          {record.status === 'pending' && (
            <>
              <Button 
                type="primary" 
                icon={<CheckOutlined />} 
                size="small"
                onClick={() => handleApprove(record.id)}
              >
                通过
              </Button>
              <Button 
                danger 
                icon={<CloseOutlined />} 
                size="small"
                onClick={() => handleReject(record)}
              >
                拒绝
              </Button>
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <PageContainer>
      <Card bordered={false}>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <span>应用审核管理</span>
            <Tag color="orange" style={{ marginLeft: 8 }}>待审核应用: {data?.total || 0}</Tag>
          </div>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={() => run()}
          >
            刷新
          </Button>
        </div>
        <Table 
          columns={columns} 
          dataSource={data?.data || []} 
          rowKey="id" 
          loading={loading}
          scroll={{ x: 1200 }}
          pagination={{
            current: data?.page || 1,
            pageSize: data?.page_size || 20,
            total: data?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => {
              setSearchParams({
                ...searchParams,
                page,
                page_size: pageSize,
              });
            }
          }}
      />
      </Card>
      
      {/* 拒绝原因填写模态框 */}
      <Modal
        title="拒绝应用审核"
        open={rejectModalVisible}
        onOk={handleRejectSubmit}
        onCancel={handleRejectCancel}
        okText="确认拒绝"
        cancelText="取消"
        okButtonProps={{ danger: true }}
      >
        <div style={{ marginBottom: 16 }}>
          <p><strong>应用名称：</strong>{currentRejectApp?.name}</p>
          <p><strong>开发者：</strong>{currentRejectApp?.developer_name}</p>
          <p><strong>版本：</strong>{currentRejectApp?.current_version}</p>
        </div>
        <Form form={rejectForm} layout="vertical">
          <Form.Item
            name="reason"
            label="拒绝原因"
            rules={[
              { required: true, message: '请填写拒绝原因' },
              { min: 5, message: '拒绝原因至少5个字符' },
              { max: 200, message: '拒绝原因不能超过200个字符' }
            ]}
          >
            <Input.TextArea
              rows={4}
              placeholder="请详细说明拒绝该应用的原因，这将帮助开发者改进应用..."
              showCount
              maxLength={200}
            />
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default AppAudit;