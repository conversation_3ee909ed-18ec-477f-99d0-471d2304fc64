import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';
import { ApiService } from '../services/ApiService';
import { LoadingView, LoadingState } from '../components/LoadingView';
import { router } from '@kit.ArkUI';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { AppModel } from '../models/App';

/**
 * 我的收藏页面
 */
@Entry
@Component
struct FavoritesPage {
  @State favoriteApps: AppModel[] = [];
  @State loadingState: LoadingState = LoadingState.LOADING;
  @State selectedCategory: string = '全部';
  @State categories: string[] = ['全部', '游戏', '社交', '工具', '娱乐', '教育'];

  private deviceUtils = DeviceUtils.getInstance();
  private apiService = ApiService.getInstance();

  aboutToAppear() {
    this.loadFavoriteApps();
  }

  /**
   * 加载收藏的应用
   */
  private async loadFavoriteApps() {
    try {
      this.loadingState = LoadingState.LOADING;
      // 这里应该调用API获取用户收藏的应用
      // 暂时使用模拟数据
      await this.simulateApiCall();
      this.favoriteApps = this.getMockFavoriteApps();
      this.loadingState = LoadingState.SUCCESS;
    } catch (error) {
      hilog.error(0x0000, 'FavoritesPage', '加载收藏应用失败: %{public}s', JSON.stringify(error));
      this.loadingState = LoadingState.ERROR;
    }
  }

  /**
   * 模拟API调用
   */
  private async simulateApiCall(): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 1000);
    });
  }

  /**
   * 获取模拟收藏应用数据
   */
  private getMockFavoriteApps(): AppModel[] {
    return [
      {
        id: 1,
        created_at: '2024-01-12T00:00:00Z',
        updated_at: '2024-01-12T00:00:00Z',
        name: '王者荣耀',
        package_name: 'com.tencent.tmgp.sgame',
        description: '5v5英雄公平对战手游',
        short_description: 'MOBA竞技游戏',
        icon: Constants.PLACEHOLDER_IMAGE,
        category_id: 3,
        category_name: '游戏',
        developer_id: 3,
        developer_name: '腾讯游戏',
        version: '********',
        version_code: 384018,
        min_sdk_version: 9,
        target_sdk_version: 12,
        size: 1887436800,
        download_url: '',
        download_count: 500000000,
        rating: 4.5,
        review_count: 100000,
        screenshots: [Constants.PLACEHOLDER_IMAGE],
        permissions: [],
        tags: ['MOBA', '竞技'],
        changelog: '新增英雄和皮肤',
        privacy_policy: '',
        support_email: '<EMAIL>',
        website: 'https://pvp.qq.com',
        status: 'published',
        is_featured: true,
        is_editor_choice: true,
        is_top: false,
        published_at: '2024-01-12T00:00:00Z',
        review_status: 'approved',
        review_reason: '',
        reviewed_at: '2024-01-12T00:00:00Z',
        reviewer_id: 1
      },
      {
        id: 2,
        created_at: '2024-01-08T00:00:00Z',
        updated_at: '2024-01-08T00:00:00Z',
        name: '抖音',
        package_name: 'com.ss.android.ugc.aweme',
        description: '记录美好生活',
        short_description: '短视频社交平台',
        icon: Constants.PLACEHOLDER_IMAGE,
        category_id: 4,
        category_name: '娱乐',
        developer_id: 4,
        developer_name: '北京微播视界科技有限公司',
        version: '28.7.0',
        version_code: 287000,
        min_sdk_version: 9,
        target_sdk_version: 12,
        size: 234881024,
        download_url: '',
        download_count: 800000000,
        rating: 4.6,
        review_count: 200000,
        screenshots: [Constants.PLACEHOLDER_IMAGE],
        permissions: [],
        tags: ['短视频', '娱乐'],
        changelog: '优化视频播放体验',
        privacy_policy: '',
        support_email: '<EMAIL>',
        website: 'https://www.douyin.com',
        status: 'published',
        is_featured: true,
        is_editor_choice: false,
        is_top: true,
        published_at: '2024-01-08T00:00:00Z',
        review_status: 'approved',
        review_reason: '',
        reviewed_at: '2024-01-08T00:00:00Z',
        reviewer_id: 1
      },
      {
        id: 3,
        created_at: '2024-01-05T00:00:00Z',
        updated_at: '2024-01-05T00:00:00Z',
        name: '学习强国',
        package_name: 'cn.xuexi.android',
        description: '学而时习之，不亦说乎',
        short_description: '学习教育平台',
        icon: Constants.PLACEHOLDER_IMAGE,
        category_id: 5,
        category_name: '教育',
        developer_id: 5,
        developer_name: '中央宣传部思想政治工作研究所',
        version: '2.45.0',
        version_code: 245000,
        min_sdk_version: 9,
        target_sdk_version: 12,
        size: 156672000,
        download_url: '',
        download_count: 200000000,
        rating: 4.8,
        review_count: 80000,
        screenshots: [Constants.PLACEHOLDER_IMAGE],
        permissions: [],
        tags: ['学习', '新闻'],
        changelog: '新增学习内容',
        privacy_policy: '',
        support_email: '<EMAIL>',
        website: 'https://www.xuexi.cn',
        status: 'published',
        is_featured: false,
        is_editor_choice: true,
        is_top: false,
        published_at: '2024-01-05T00:00:00Z',
        review_status: 'approved',
        review_reason: '',
        reviewed_at: '2024-01-05T00:00:00Z',
        reviewer_id: 1
      }
    ];
  }

  /**
   * 获取过滤后的应用列表
   */
  private getFilteredApps(): AppModel[] {
    if (this.selectedCategory === '全部') {
      return this.favoriteApps;
    }
    return this.favoriteApps.filter(app => app.tags && app.tags.includes(this.selectedCategory));
  }

  /**
   * 跳转到应用详情页面
   */
  private navigateToAppDetail(app: AppModel) {
    this.getUIContext().getRouter().pushUrl({
      url: 'pages/AppDetailPage',
      params: { appId: app.id.toString() }
    });
  }

  /**
   * 取消收藏
   */
  private async removeFavorite(app: AppModel) {
    try {
      // 这里应该调用API取消收藏
      // 暂时直接从列表中移除
      const index = this.favoriteApps.findIndex(item => item.id === app.id);
      if (index !== -1) {
        this.favoriteApps.splice(index, 1);
      }
    } catch (error) {
      hilog.error(0x0000, 'FavoritesPage', '取消收藏失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 分类标签栏
   */
  @Builder
  private CategoryTabs() {
    Scroll() {
      Row({ space: 8 }) {
        ForEach(this.categories, (category: string) => {
          Text(category)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(this.selectedCategory === category ? Constants.COLORS.WHITE : Constants.COLORS.TEXT_SECONDARY)
            .backgroundColor(this.selectedCategory === category ? Constants.COLORS.PRIMARY : $r('app.color.overlay_light'))
            .padding({ left: 12, right: 12, top: 6, bottom: 6 })
            .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
            .onClick(() => {
              this.selectedCategory = category;
            })
        }, (category: string) => category)
      }
      .padding({ left: 16, right: 16 })
    }
    .scrollable(ScrollDirection.Horizontal)
    .scrollBar(BarState.Off)
    .width('100%')
    .backgroundColor(Constants.COLORS.WHITE)
    .padding({ top: 12, bottom: 12 })
  }

  /**
   * 应用项
   */
  @Builder
  private AppItem(app: AppModel) {
    Row({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) }) {
      // 应用图标
      Image(app.icon)
        .width(this.deviceUtils.isTablet() ? 64 : 56)
        .height(this.deviceUtils.isTablet() ? 64 : 56)
        .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
        .objectFit(ImageFit.Cover)
        .backgroundColor(Constants.COLORS.BACKGROUND_LIGHT)

      // 应用信息
      Column({ space: 4 }) {
        Row() {
          Text(app.name)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .fontWeight(FontWeight.Medium)
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })
            .layoutWeight(1)

          Text(app.tags && app.tags.length > 0 ? app.tags[0] : '应用')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.PRIMARY)
            .backgroundColor($r('app.color.overlay_medium'))
            .padding({ left: 8, right: 8, top: 2, bottom: 2 })
            .borderRadius(Constants.BORDER_RADIUS.SMALL)
        }
        .width('100%')

        Text(app.description)
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
          .maxLines(1)
          .textOverflow({ overflow: TextOverflow.Ellipsis })

        Row({ space: 8 }) {
          Row({ space: 4 }) {
            Text('⭐')
              .fontSize(12)
            Text((app.rating || 0).toString())
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
              .fontColor(Constants.COLORS.TEXT_HINT)
          }

          Text(`${((app.size || 0) / 1024 / 1024).toFixed(1)}MB`)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.TEXT_HINT)

          Text(`更新时间: ${app.updated_at.split(' ')[0]}`)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.TEXT_HINT)
        }
      }
      .alignItems(HorizontalAlign.Start)
      .layoutWeight(1)

      // 取消收藏按钮
      Text('❤️')
        .fontSize(20)
        .fontColor(Constants.COLORS.ERROR)
        .padding(8)
        .onClick(() => {
          this.removeFavorite(app);
        })
    }
    .width('100%')
    .padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
    .backgroundColor(Constants.COLORS.WHITE)
    .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
    .margin({ left: 16, right: 16, bottom: 8 })
    .onClick(() => this.navigateToAppDetail(app))
  }

  /**
   * 应用列表
   */
  @Builder
  private AppList() {
    if (this.getFilteredApps().length === 0) {
      Column({ space: 16 }) {
        Text('❤️')
          .fontSize(48)
          .fontColor(Constants.COLORS.TEXT_HINT)
        
        Text(this.selectedCategory === '全部' ? '暂无收藏应用' : `暂无${this.selectedCategory}类收藏`)
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
        
        Text('去应用商店发现更多精彩应用')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.TEXT_HINT)
      }
      .width('100%')
      .height(200)
      .justifyContent(FlexAlign.Center)
      .alignItems(HorizontalAlign.Center)
    } else {
      Column() {
        ForEach(this.getFilteredApps(), (app: AppModel) => {
          this.AppItem(app)
        }, (app: AppModel) => app.id.toString())
      }
      .width('100%')
    }
  }

  build() {
    Column() {
      // 标题栏
      Row() {
        Text('←')
          .fontSize(24)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .onClick(() => {
            this.getUIContext().getRouter().back();
          })

        Text('我的收藏')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .fontWeight(FontWeight.Bold)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('')
          .width(24)
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor(Constants.COLORS.WHITE)
      .alignItems(VerticalAlign.Center)

      if (this.loadingState === LoadingState.LOADING) {
        LoadingView({ state: LoadingState.LOADING })
          .layoutWeight(1)
      } else if (this.loadingState === LoadingState.ERROR) {
        LoadingView({ 
          state: LoadingState.ERROR,
          onRetry: (): Promise<void> => this.loadFavoriteApps()
        })
          .layoutWeight(1)
      } else {
        Column() {
          // 分类标签栏
          this.CategoryTabs()
          
          Divider()
            .color(Constants.COLORS.BORDER)

          // 应用列表
          Scroll() {
            Column() {
              this.AppList()
              
              // 底部间距
              Column()
                .height(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE))
            }
          }
          .scrollable(ScrollDirection.Vertical)
          .scrollBar(BarState.Auto)
          .layoutWeight(1)
        }
        .layoutWeight(1)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Constants.COLORS.BACKGROUND)
  }
}

export { FavoritesPage };