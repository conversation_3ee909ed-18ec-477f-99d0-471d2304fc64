package api

import (
	"strconv"
	"time"

	"nexushub-oh-back/internal/models"
	"nexushub-oh-back/pkg/auth"
	"nexushub-oh-back/pkg/logger"
	"nexushub-oh-back/pkg/response"
	"nexushub-oh-back/pkg/utils"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// UserController 用户控制器
type UserController struct {
	DB         *gorm.DB
	JWTService *auth.JWTService
	Validate   *validator.Validate
}

// NewUserController 创建用户控制器
func NewUserController(db *gorm.DB, jwtService *auth.JWTService) *UserController {
	return &UserController{
		DB:         db,
		JWTService: jwtService,
		Validate:   validator.New(),
	}
}

// RegisterRequest 注册请求
type RegisterRequest struct {
	Username string `json:"username" validate:"required,min=3,max=50"`
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required,min=6"`
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" validate:"required"`
	Password string `json:"password" validate:"required"`
}

// UserResponse 用户响应
type UserResponse struct {
	ID       uint   `json:"id"`
	Username string `json:"username"`
	Email    string `json:"email"`
	Role     string `json:"role"`
	Avatar   string `json:"avatar"`
}

// UpdateProfileRequest 更新用户资料请求
type UpdateProfileRequest struct {
	Username string `json:"username" validate:"omitempty,min=3,max=50"`
	Email    string `json:"email" validate:"omitempty,email"`
	Avatar   string `json:"avatar"`
	Bio      string `json:"bio" validate:"max=500"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" validate:"required"`
	NewPassword string `json:"new_password" validate:"required,min=6"`
}

// CreateUserRequest 创建用户请求（管理员）
type CreateUserRequest struct {
	Username string `json:"username" validate:"required,min=3,max=50"`
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required,min=6"`
	Role     string `json:"role" validate:"required,oneof=user admin developer"`
}

// UpdateUserRequest 更新用户请求（管理员）
type UpdateUserRequest struct {
	Username string `json:"username" validate:"omitempty,min=3,max=50"`
	Email    string `json:"email" validate:"omitempty,email"`
	Role     string `json:"role" validate:"omitempty,oneof=user admin developer"`
	Status   string `json:"status" validate:"omitempty,oneof=active inactive banned"`
}

// Register 用户注册
//
//	@Summary		用户注册
//	@Description	用户注册接口
//	@Tags			用户
//	@Accept			json
//	@Produce		json
//	@Param			data	body		RegisterRequest		true	"注册信息"
//	@Success		200		{object}	response.Response		"注册成功"
//	@Failure		400		{object}	response.Response		"参数错误"
//	@Failure		409		{object}	response.Response		"用户名或邮箱已存在"
//	@Failure		500		{object}	response.Response		"服务器错误"
//	@Router			/auth/register [post]
func (c *UserController) Register(ctx *gin.Context) {
	// 检查数据库连接
	if c.DB == nil {
		response.InternalServerError(ctx, "服务器配置错误")
		return
	}

	var req RegisterRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, "无效的请求参数")
		return
	}

	// 验证请求参数
	if err := c.Validate.Struct(req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	// 检查用户名是否已存在
	var count int64
	c.DB.Model(&models.User{}).Where("username = ?", req.Username).Count(&count)
	if count > 0 {
		response.Conflict(ctx, "用户名已存在")
		return
	}

	// 检查邮箱是否已存在
	c.DB.Model(&models.User{}).Where("email = ?", req.Email).Count(&count)
	if count > 0 {
		response.Conflict(ctx, "邮箱已存在")
		return
	}

	// 加密密码
	hashedPassword, err := utils.HashPassword(req.Password)
	if err != nil {
		logger.Error("密码加密失败", zap.Error(err))
		response.InternalServerError(ctx, "服务器错误")
		return
	}

	// 创建用户
	user := models.User{
		Username: req.Username,
		Email:    req.Email,
		Password: hashedPassword,
		Role:     "user",
		Status:   "active",
	}

	if err := c.DB.Create(&user).Error; err != nil {
		logger.Error("创建用户失败", zap.Error(err))
		response.InternalServerError(ctx, "创建用户失败")
		return
	}

	response.Success(ctx, nil)
}

// ListUsers 获取用户列表
func (c *UserController) ListUsers(ctx *gin.Context) {
	// TODO: 实现获取用户列表功能
	response.Success(ctx, []interface{}{})
}

// AdminCreateUser 管理员创建用户
func (c *UserController) AdminCreateUser(ctx *gin.Context) {
	// TODO: 实现管理员创建用户功能
	response.Success(ctx, nil)
}

// GetUserDetail 获取用户详情
func (c *UserController) GetUserDetail(ctx *gin.Context) {
	// TODO: 实现获取用户详情功能
	response.Success(ctx, nil)
}

// GetUserLoginRecords 获取用户登录记录
func (c *UserController) GetUserLoginRecords(ctx *gin.Context) {
	// TODO: 实现获取用户登录记录功能
	response.Success(ctx, nil)
}

// GetUserAppRecords 获取用户应用记录
func (c *UserController) GetUserAppRecords(ctx *gin.Context) {
	// TODO: 实现获取用户应用记录功能
	response.Success(ctx, nil)
}

// UpdateUserRole 更新用户角色
func (c *UserController) UpdateUserRole(ctx *gin.Context) {
	// TODO: 实现更新用户角色功能
	response.Success(ctx, nil)
}

// UpdateUserStatus 更新用户状态
func (c *UserController) UpdateUserStatus(ctx *gin.Context) {
	// TODO: 实现更新用户状态功能
	response.Success(ctx, nil)
}

// Login 用户登录
//
//	@Summary		用户登录
//	@Description	用户登录接口
//	@Tags			用户
//	@Accept			json
//	@Produce		json
//	@Param			data	body		LoginRequest							true	"登录信息"
//	@Success		200		{object}	response.Response{data=map[string]interface{}}	"登录成功，返回token和用户信息"
//	@Failure		400		{object}	response.Response							"参数错误"
//	@Failure		401		{object}	response.Response							"用户名或密码错误"
//	@Failure		403		{object}	response.Response							"账户被禁用"
//	@Failure		500		{object}	response.Response							"服务器错误"
//	@Router			/auth/login [post]
func (c *UserController) Login(ctx *gin.Context) {
	var req LoginRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, "无效的请求参数")
		return
	}

	// 验证请求参数
	if err := c.Validate.Struct(req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	// 查找用户
	var user models.User
	if err := c.DB.Where("username = ? OR email = ?", req.Username, req.Username).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.Unauthorized(ctx, "用户名或密码错误")
			return
		}
		logger.Error("查询用户失败", zap.Error(err))
		response.InternalServerError(ctx, "服务器错误")
		return
	}

	// 检查账户状态
	if user.Status != "active" {
		response.Forbidden(ctx, "账户已被禁用")
		return
	}

	// 验证密码
	if err := utils.CheckPassword(user.Password, req.Password); err != nil {
		response.Unauthorized(ctx, "用户名或密码错误")
		return
	}

	// 生成JWT token
	token, err := c.JWTService.GenerateToken(user.ID, user.Username, user.Role)
	if err != nil {
		logger.Error("生成token失败", zap.Error(err))
		response.InternalServerError(ctx, "服务器错误")
		return
	}

	// 更新最后登录时间
	now := time.Now()
	user.LastLoginAt = &now
	c.DB.Save(&user)

	// 返回用户信息和token
	userResponse := UserResponse{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
		Role:     user.Role,
		Avatar:   user.Avatar,
	}

	response.Success(ctx, map[string]interface{}{
		"token": token,
		"user":  userResponse,
	})
}

// GetProfile 获取用户资料
//
//	@Summary		获取用户资料
//	@Description	获取当前登录用户的资料信息
//	@Tags			用户
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Success		200	{object}	response.Response{data=UserResponse}	"返回用户资料"
//	@Failure		401	{object}	response.Response					"未授权"
//	@Failure		404	{object}	response.Response					"用户不存在"
//	@Failure		500	{object}	response.Response					"服务器错误"
//	@Router			/user/profile [get]
func (c *UserController) GetProfile(ctx *gin.Context) {
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "未授权")
		return
	}

	var user models.User
	if err := c.DB.First(&user, userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "用户不存在")
			return
		}
		logger.Error("查询用户失败", zap.Error(err))
		response.InternalServerError(ctx, "服务器错误")
		return
	}

	userResponse := UserResponse{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
		Role:     user.Role,
		Avatar:   user.Avatar,
	}

	response.Success(ctx, userResponse)
}

// UpdateProfile 更新用户资料
//
//	@Summary		更新用户资料
//	@Description	更新当前登录用户的资料信息
//	@Tags			用户
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			data	body		UpdateProfileRequest				true	"更新的用户资料"
//	@Success		200		{object}	response.Response{data=UserResponse}	"更新成功，返回更新后的用户资料"
//	@Failure		400		{object}	response.Response						"参数错误"
//	@Failure		401		{object}	response.Response						"未授权"
//	@Failure		404		{object}	response.Response						"用户不存在"
//	@Failure		409		{object}	response.Response						"用户名或邮箱已存在"
//	@Failure		500		{object}	response.Response						"服务器错误"
//	@Router			/user/profile [put]
func (c *UserController) UpdateProfile(ctx *gin.Context) {
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "未授权")
		return
	}

	var req UpdateProfileRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, "无效的请求参数")
		return
	}

	// 验证请求参数
	if err := c.Validate.Struct(req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	// 查找用户
	var user models.User
	if err := c.DB.First(&user, userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "用户不存在")
			return
		}
		logger.Error("查询用户失败", zap.Error(err))
		response.InternalServerError(ctx, "服务器错误")
		return
	}

	// 检查用户名是否已被其他用户使用
	if req.Username != "" && req.Username != user.Username {
		var count int64
		c.DB.Model(&models.User{}).Where("username = ? AND id != ?", req.Username, userID).Count(&count)
		if count > 0 {
			response.Conflict(ctx, "用户名已存在")
			return
		}
		user.Username = req.Username
	}

	// 检查邮箱是否已被其他用户使用
	if req.Email != "" && req.Email != user.Email {
		var count int64
		c.DB.Model(&models.User{}).Where("email = ? AND id != ?", req.Email, userID).Count(&count)
		if count > 0 {
			response.Conflict(ctx, "邮箱已存在")
			return
		}
		user.Email = req.Email
	}

	// 更新其他字段
	if req.Avatar != "" {
		user.Avatar = req.Avatar
	}
	// Bio 字段暂时不支持，User 模型中没有此字段
	// if req.Bio != "" {
	//	user.Bio = req.Bio
	// }

	// 保存更新
	if err := c.DB.Save(&user).Error; err != nil {
		logger.Error("更新用户失败", zap.Error(err))
		response.InternalServerError(ctx, "更新用户失败")
		return
	}

	userResponse := UserResponse{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
		Role:     user.Role,
		Avatar:   user.Avatar,
	}

	response.Success(ctx, userResponse)
}

// ChangePassword 修改密码
//
//	@Summary		修改密码
//	@Description	修改当前登录用户的密码
//	@Tags			用户
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			data	body		ChangePasswordRequest		true	"密码信息"
//	@Success		200		{object}	response.Response				"修改成功"
//	@Failure		400		{object}	response.Response				"参数错误"
//	@Failure		401		{object}	response.Response				"未授权或原密码错误"
//	@Failure		404		{object}	response.Response				"用户不存在"
//	@Failure		500		{object}	response.Response				"服务器错误"
//	@Router			/user/password [put]
func (c *UserController) ChangePassword(ctx *gin.Context) {
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "未授权")
		return
	}

	var req ChangePasswordRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, "无效的请求参数")
		return
	}

	// 验证请求参数
	if err := c.Validate.Struct(req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	// 查找用户
	var user models.User
	if err := c.DB.First(&user, userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "用户不存在")
			return
		}
		logger.Error("查询用户失败", zap.Error(err))
		response.InternalServerError(ctx, "服务器错误")
		return
	}

	// 验证原密码
	if err := utils.CheckPassword(user.Password, req.OldPassword); err != nil {
		response.Unauthorized(ctx, "原密码错误")
		return
	}

	// 加密新密码
	hashedPassword, err := utils.HashPassword(req.NewPassword)
	if err != nil {
		logger.Error("密码加密失败", zap.Error(err))
		response.InternalServerError(ctx, "服务器错误")
		return
	}

	// 更新密码
	user.Password = hashedPassword
	if err := c.DB.Save(&user).Error; err != nil {
		logger.Error("更新密码失败", zap.Error(err))
		response.InternalServerError(ctx, "更新密码失败")
		return
	}

	response.Success(ctx, nil)
}

// CreateUser 创建用户（管理员）
//
//	@Summary		创建用户
//	@Description	管理员创建新用户
//	@Tags			用户管理
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			data	body		CreateUserRequest			true	"用户信息"
//	@Success		200		{object}	response.Response				"创建成功"
//	@Failure		400		{object}	response.Response				"参数错误"
//	@Failure		401		{object}	response.Response				"未授权"
//	@Failure		403		{object}	response.Response				"权限不足"
//	@Failure		409		{object}	response.Response				"用户名或邮箱已存在"
//	@Failure		500		{object}	response.Response				"服务器错误"
//	@Router			/admin/users [post]
func (c *UserController) CreateUser(ctx *gin.Context) {
	// 检查管理员权限
	userRole, exists := ctx.Get("role")
	if !exists || userRole != "admin" {
		response.Forbidden(ctx, "权限不足")
		return
	}

	var req CreateUserRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, "无效的请求参数")
		return
	}

	// 验证请求参数
	if err := c.Validate.Struct(req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	// 检查用户名是否已存在
	var count int64
	c.DB.Model(&models.User{}).Where("username = ?", req.Username).Count(&count)
	if count > 0 {
		response.Conflict(ctx, "用户名已存在")
		return
	}

	// 检查邮箱是否已存在
	c.DB.Model(&models.User{}).Where("email = ?", req.Email).Count(&count)
	if count > 0 {
		response.Conflict(ctx, "邮箱已存在")
		return
	}

	// 加密密码
	hashedPassword, err := utils.HashPassword(req.Password)
	if err != nil {
		logger.Error("密码加密失败", zap.Error(err))
		response.InternalServerError(ctx, "服务器错误")
		return
	}

	// 创建用户
	user := models.User{
		Username: req.Username,
		Email:    req.Email,
		Password: hashedPassword,
		Role:     req.Role,
		Status:   "active",
	}

	if err := c.DB.Create(&user).Error; err != nil {
		logger.Error("创建用户失败", zap.Error(err))
		response.InternalServerError(ctx, "创建用户失败")
		return
	}

	response.Success(ctx, nil)
}

// UpdateUser 更新用户（管理员）
//
//	@Summary		更新用户
//	@Description	管理员更新用户信息
//	@Tags			用户管理
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			id		path		int							true	"用户ID"
//	@Param			data	body		UpdateUserRequest			true	"更新的用户信息"
//	@Success		200		{object}	response.Response				"更新成功"
//	@Failure		400		{object}	response.Response				"参数错误"
//	@Failure		401		{object}	response.Response				"未授权"
//	@Failure		403		{object}	response.Response				"权限不足"
//	@Failure		404		{object}	response.Response				"用户不存在"
//	@Failure		409		{object}	response.Response				"用户名或邮箱已存在"
//	@Failure		500		{object}	response.Response				"服务器错误"
//	@Router			/admin/users/{id} [put]
func (c *UserController) UpdateUser(ctx *gin.Context) {
	// 检查管理员权限
	userRole, exists := ctx.Get("role")
	if !exists || userRole != "admin" {
		response.Forbidden(ctx, "权限不足")
		return
	}

	userID, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		response.BadRequest(ctx, "无效的用户ID")
		return
	}

	var req UpdateUserRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, "无效的请求参数")
		return
	}

	// 验证请求参数
	if err := c.Validate.Struct(req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	// 查找用户
	var user models.User
	if err := c.DB.First(&user, userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "用户不存在")
			return
		}
		logger.Error("查询用户失败", zap.Error(err))
		response.InternalServerError(ctx, "服务器错误")
		return
	}

	// 检查用户名是否已被其他用户使用
	if req.Username != "" && req.Username != user.Username {
		var count int64
		c.DB.Model(&models.User{}).Where("username = ? AND id != ?", req.Username, userID).Count(&count)
		if count > 0 {
			response.Conflict(ctx, "用户名已存在")
			return
		}
		user.Username = req.Username
	}

	// 检查邮箱是否已被其他用户使用
	if req.Email != "" && req.Email != user.Email {
		var count int64
		c.DB.Model(&models.User{}).Where("email = ? AND id != ?", req.Email, userID).Count(&count)
		if count > 0 {
			response.Conflict(ctx, "邮箱已存在")
			return
		}
		user.Email = req.Email
	}

	// 更新其他字段
	if req.Role != "" {
		user.Role = req.Role
	}
	if req.Status != "" {
		user.Status = models.UserStatus(req.Status)
	}

	// 保存更新
	if err := c.DB.Save(&user).Error; err != nil {
		logger.Error("更新用户失败", zap.Error(err))
		response.InternalServerError(ctx, "更新用户失败")
		return
	}

	response.Success(ctx, nil)
}

// GetUsers 获取用户列表（管理员）
//
//	@Summary		获取用户列表
//	@Description	管理员获取用户列表
//	@Tags			用户管理
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			page		query		int						false	"页码"				default(1)
//	@Param			limit		query		int						false	"每页数量"			default(10)
//	@Param			role		query		string					false	"角色筛选"
//	@Param			status		query		string					false	"状态筛选"
//	@Param			keyword		query		string					false	"关键词搜索"
//	@Success		200				{object}	response.Response{data=[]UserResponse}	"用户列表"
//	@Failure		401				{object}	response.Response						"未授权"
//	@Failure		403				{object}	response.Response						"权限不足"
//	@Failure		500				{object}	response.Response						"服务器错误"
//	@Router			/admin/users [get]
func (c *UserController) GetUsers(ctx *gin.Context) {
	// 检查管理员权限
	userRole, exists := ctx.Get("role")
	logger.Error("=== GetUsers函数开始执行 ===")
	logger.Error("GetUsers权限检查", zap.Bool("exists", exists), zap.Any("userRole", userRole))
	if !exists || userRole != "admin" {
		response.Error(ctx, 403, "权限不足")
		return
	}

	// 获取查询参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "10"))
	role := ctx.Query("role")
	status := ctx.Query("status")
	keyword := ctx.Query("keyword")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// 构建查询
	query := c.DB.Model(&models.User{})

	if role != "" {
		query = query.Where("role = ?", role)
	}
	if status != "" {
		query = query.Where("status = ?", status)
	}
	if keyword != "" {
		query = query.Where("username LIKE ? OR email LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 获取总数
	var total int64
	query.Count(&total)
	logger.Error("查询用户总数", zap.Int64("total", total))

	// 获取用户列表
	logger.Error("=== 开始查询用户列表 ===")
	var users []models.User
	offset := (page - 1) * limit
	logger.Error("查询参数", zap.Int("page", page), zap.Int("limit", limit), zap.Int("offset", offset))
	if err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&users).Error; err != nil {
		logger.Error("查询用户列表失败", zap.Error(err))
		response.InternalServerError(ctx, "服务器错误")
		return
	}
	logger.Error("查询到用户数量", zap.Int("count", len(users)))

	// 转换为响应格式
	logger.Error("=== 开始转换响应格式 ===")
	var userResponses []UserResponse
	for _, user := range users {
		userResponses = append(userResponses, UserResponse{
			ID:       user.ID,
			Username: user.Username,
			Email:    user.Email,
			Role:     user.Role,
			Avatar:   user.Avatar,
		})
	}
	logger.Error("准备返回响应", zap.Int("userResponses", len(userResponses)), zap.Int64("total", total))

	response.SuccessWithPagination(ctx, userResponses, total, page, limit)
}

// Logout 用户登出
//
//	@Summary		用户登出
//	@Description	用户登出接口
//	@Tags			用户
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Success		200	{object}	response.Response	"登出成功"
//	@Failure		401	{object}	response.Response	"未授权"
//	@Router			/auth/logout [post]
func (c *UserController) Logout(ctx *gin.Context) {
	// 这里可以实现token黑名单逻辑
	// 目前简单返回成功
	response.Success(ctx, nil)
}

// ResetPassword 重置密码（管理员）
//
//	@Summary		重置密码
//	@Description	管理员重置用户密码
//	@Tags			用户管理
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			id			path		int							true	"用户ID"
//	@Param			data		body		map[string]string			true	"新密码"
//	@Success		200		{object}	response.Response				"更新成功"
//	@Failure		400		{object}	response.Response				"参数错误"
//	@Failure		401		{object}	response.Response				"未授权"
//	@Failure		403		{object}	response.Response				"权限不足"
//	@Failure		404		{object}	response.Response				"用户不存在"
//	@Failure		500		{object}	response.Response				"服务器错误"
//	@Router			/admin/users/{id}/reset-password [put]
func (c *UserController) ResetPassword(ctx *gin.Context) {
	// 检查管理员权限
	userRole, exists := ctx.Get("role")
	if !exists || userRole != "admin" {
		response.Forbidden(ctx, "权限不足")
		return
	}

	userID, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		response.BadRequest(ctx, "无效的用户ID")
		return
	}

	var req map[string]string
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, "无效的请求参数")
		return
	}

	newPassword, exists := req["password"]
	if !exists || len(newPassword) < 6 {
		response.BadRequest(ctx, "密码长度至少6位")
		return
	}

	// 查找用户
	var user models.User
	if err := c.DB.First(&user, userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "用户不存在")
			return
		}
		logger.Error("查询用户失败", zap.Error(err))
		response.InternalServerError(ctx, "服务器错误")
		return
	}

	// 加密新密码
	hashedPassword, err := utils.HashPassword(newPassword)
	if err != nil {
		logger.Error("密码加密失败", zap.Error(err))
		response.InternalServerError(ctx, "服务器错误")
		return
	}

	// 更新密码
	user.Password = hashedPassword
	if err := c.DB.Save(&user).Error; err != nil {
		logger.Error("更新密码失败", zap.Error(err))
		response.InternalServerError(ctx, "更新密码失败")
		return
	}

	response.Success(ctx, nil)
}
