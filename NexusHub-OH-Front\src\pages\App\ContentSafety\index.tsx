import { PageContainer } from '@ant-design/pro-components';
import { Card, Table, Button, Space, Tag, Input, Select, DatePicker, message, Badge, Drawer, Descriptions, Typography } from 'antd';
import { SearchOutlined, EyeOutlined, CheckCircleOutlined, ExclamationCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { useRequest } from '@umijs/max';
import type { ColumnsType } from 'antd/es/table';

const { Text } = Typography;

interface ContentItem {
  id: string;
  appName: string;
  contentType: 'text' | 'image' | 'video';
  checkTime: string;
  riskLevel: 'low' | 'medium' | 'high';
  status: 'pending' | 'passed' | 'blocked';
  content: string;
  riskDetail: string;
}

// 模拟数据获取函数
const fetchContentList = async (params: any) => {
  // 实际项目中应该调用API
  console.log('Fetching with params:', params);
  
  // 模拟数据
  const mockData: ContentItem[] = [
    {
      id: '1',
      appName: '社交聊天',
      contentType: 'text',
      checkTime: '2023-05-01 10:00:00',
      riskLevel: 'high',
      status: 'pending',
      content: '包含敏感词汇的聊天内容...',
      riskDetail: '政治敏感、违规内容',
    },
    {
      id: '2',
      appName: '图片分享',
      contentType: 'image',
      checkTime: '2023-05-02 14:30:00',
      riskLevel: 'medium',
      status: 'blocked',
      content: 'https://example.com/image1.jpg',
      riskDetail: '不适宜内容、暴力倾向',
    },
    {
      id: '3',
      appName: '视频平台',
      contentType: 'video',
      checkTime: '2023-05-03 09:15:00',
      riskLevel: 'low',
      status: 'passed',
      content: 'https://example.com/video1.mp4',
      riskDetail: '轻微不适宜内容',
    },
  ];

  return { data: mockData, total: mockData.length };
};

const ContentSafety: React.FC = () => {
  const [searchParams, setSearchParams] = useState({});
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [currentItem, setCurrentItem] = useState<ContentItem | null>(null);
  
  const { data, loading, run } = useRequest(() => fetchContentList(searchParams), {
    refreshDeps: [searchParams],
  });

  const handleSearch = (values: any) => {
    setSearchParams(values);
  };

  const handleView = (item: ContentItem) => {
    setCurrentItem(item);
    setDrawerVisible(true);
  };

  const handlePass = (id: string) => {
    message.success(`内容 ${id} 已通过检测`);
    // 实际项目中应该调用API
  };

  const handleBlock = (id: string) => {
    message.error(`内容 ${id} 已被屏蔽`);
    // 实际项目中应该调用API
  };

  const getRiskLevelBadge = (level: string) => {
    switch (level) {
      case 'high':
        return <Badge status="error" text="高风险" />;
      case 'medium':
        return <Badge status="warning" text="中风险" />;
      case 'low':
        return <Badge status="success" text="低风险" />;
      default:
        return <Badge status="default" text="未知" />;
    }
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'pending':
        return <Tag color="blue">待处理</Tag>;
      case 'passed':
        return <Tag color="green">已通过</Tag>;
      case 'blocked':
        return <Tag color="red">已屏蔽</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };

  const columns: ColumnsType<ContentItem> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '应用名称',
      dataIndex: 'appName',
      key: 'appName',
    },
    {
      title: '内容类型',
      dataIndex: 'contentType',
      key: 'contentType',
      render: (type) => {
        const typeMap = {
          text: '文本',
          image: '图片',
          video: '视频',
        };
        return typeMap[type as keyof typeof typeMap] || type;
      },
    },
    {
      title: '检测时间',
      dataIndex: 'checkTime',
      key: 'checkTime',
      sorter: (a, b) => a.checkTime.localeCompare(b.checkTime),
    },
    {
      title: '风险等级',
      dataIndex: 'riskLevel',
      key: 'riskLevel',
      render: (level) => getRiskLevelBadge(level),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button 
            type="primary" 
            icon={<EyeOutlined />} 
            size="small"
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          {record.status === 'pending' && (
            <>
              <Button 
                type="primary" 
                icon={<CheckCircleOutlined />} 
                size="small"
                onClick={() => handlePass(record.id)}
              >
                通过
              </Button>
              <Button 
                danger 
                icon={<CloseCircleOutlined />} 
                size="small"
                onClick={() => handleBlock(record.id)}
              >
                屏蔽
              </Button>
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <PageContainer>
      <Card title="内容安全检测" bordered={false}>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Input placeholder="应用名称" prefix={<SearchOutlined />} />
            <Select
              placeholder="内容类型"
              style={{ width: 120 }}
              options={[
                { value: 'text', label: '文本' },
                { value: 'image', label: '图片' },
                { value: 'video', label: '视频' },
              ]}
            />
            <Select
              placeholder="风险等级"
              style={{ width: 120 }}
              options={[
                { value: 'high', label: '高风险' },
                { value: 'medium', label: '中风险' },
                { value: 'low', label: '低风险' },
              ]}
            />
            <Select
              placeholder="状态"
              style={{ width: 120 }}
              options={[
                { value: 'pending', label: '待处理' },
                { value: 'passed', label: '已通过' },
                { value: 'blocked', label: '已屏蔽' },
              ]}
            />
            <DatePicker.RangePicker placeholder={['开始日期', '结束日期']} />
            <Button type="primary" onClick={() => handleSearch({})}>搜索</Button>
            <Button onClick={() => setSearchParams({})}>重置</Button>
          </Space>
        </div>
        <Table 
          columns={columns} 
          dataSource={data?.data} 
          rowKey="id" 
          loading={loading}
          pagination={{
            total: data?.total,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
        />
      </Card>

      <Drawer
        title="内容详情"
        placement="right"
        width={600}
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
      >
        {currentItem && (
          <>
            <Descriptions bordered column={1}>
              <Descriptions.Item label="ID">{currentItem.id}</Descriptions.Item>
              <Descriptions.Item label="应用名称">{currentItem.appName}</Descriptions.Item>
              <Descriptions.Item label="内容类型">
                {{
                  text: '文本',
                  image: '图片',
                  video: '视频',
                }[currentItem.contentType] || currentItem.contentType}
              </Descriptions.Item>
              <Descriptions.Item label="检测时间">{currentItem.checkTime}</Descriptions.Item>
              <Descriptions.Item label="风险等级">{getRiskLevelBadge(currentItem.riskLevel)}</Descriptions.Item>
              <Descriptions.Item label="状态">{getStatusTag(currentItem.status)}</Descriptions.Item>
              <Descriptions.Item label="风险详情">
                <Text type="danger">{currentItem.riskDetail}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="内容预览">
                {currentItem.contentType === 'text' ? (
                  <Text>{currentItem.content}</Text>
                ) : currentItem.contentType === 'image' ? (
                  <img src={currentItem.content} alt="内容预览" style={{ maxWidth: '100%' }} />
                ) : (
                  <video src={currentItem.content} controls style={{ maxWidth: '100%' }} />
                )}
              </Descriptions.Item>
            </Descriptions>

            {currentItem.status === 'pending' && (
              <div style={{ marginTop: 24, textAlign: 'right' }}>
                <Space>
                  <Button 
                    type="primary" 
                    icon={<CheckCircleOutlined />}
                    onClick={() => {
                      handlePass(currentItem.id);
                      setDrawerVisible(false);
                    }}
                  >
                    通过
                  </Button>
                  <Button 
                    danger 
                    icon={<CloseCircleOutlined />}
                    onClick={() => {
                      handleBlock(currentItem.id);
                      setDrawerVisible(false);
                    }}
                  >
                    屏蔽
                  </Button>
                </Space>
              </div>
            )}
          </>
        )}
      </Drawer>
    </PageContainer>
  );
};

export default ContentSafety;