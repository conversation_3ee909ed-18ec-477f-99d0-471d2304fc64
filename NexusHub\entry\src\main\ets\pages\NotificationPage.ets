import { AppModel } from '../models/App';
import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';
import { ApiService } from '../services/ApiService';
import { LoadingView, LoadingState } from '../components/LoadingView';
import { router } from '@kit.ArkUI';
import { preferences } from '@kit.ArkData';
import { hilog } from '@kit.PerformanceAnalysisKit';
// getContext is deprecated, use this.getUIContext().getHostContext() instead

/**
 * 通知数据模型
 */
interface NotificationModel {
  id: number;
  title: string;
  content: string;
  type: string;
  is_read: boolean;
  created_at: string;
  data?: Record<string, Object>;
}

/**
 * 分页信息接口
 */
interface PaginationInfo {
  hasNext: boolean;
  page: number;
  total: number;
}

/**
 * 通知列表响应数据模型
 */
interface NotificationListData {
  notifications: NotificationModel[];
  pagination: PaginationInfo;
}

/**
 * 未读通知数量数据模型
 */
interface UnreadCountData {
  count: number;
}

/**
 * 通知页面
 */
@Entry
@Component
struct NotificationPage {
  @State notifications: NotificationModel[] = [];
  @State loadingState: LoadingState = LoadingState.LOADING;
  @State currentPage: number = 1;
  @State hasMore: boolean = true;
  @State isLoadingMore: boolean = false;
  @State unreadCount: number = 0;
  @State showSettings: boolean = false;

  private deviceUtils = DeviceUtils.getInstance();
  private apiService = ApiService.getInstance();
  private pageSize: number = 20;

  aboutToAppear() {
    this.checkAndSetAuthToken().then(() => {
      this.loadNotifications();
      this.loadUnreadCount();
    });
  }

  /**
   * 检查并设置认证token
   */
  private async checkAndSetAuthToken(): Promise<void> {
    try {
      const context = this.getUIContext().getHostContext();
      const dataPreferences = preferences.getPreferencesSync(context, { name: 'user_data' });
      const token: preferences.ValueType = dataPreferences.getSync('token', '');
      
      if (token && typeof token === 'string' && token.length > 0) {
        this.apiService.setAuthToken(token);
      } else {
        // 没有token，跳转到登录页面
        this.getUIContext().getRouter().pushUrl({
          url: 'pages/LoginPage'
        });
      }
    } catch (error) {
      hilog.error(0x0000, 'NotificationPage', '检查登录状态失败: %{public}s', JSON.stringify(error));
      this.getUIContext().getRouter().pushUrl({
        url: 'pages/LoginPage'
      });
    }
  }

  /**
   * 加载通知列表
   */
  private async loadNotifications(page: number = 1) {
    try {
      if (page === 1) {
        this.loadingState = LoadingState.LOADING;
      } else {
        this.isLoadingMore = true;
      }

      const response = await this.apiService.getNotifications(page, this.pageSize);
      if (response.code === 200 && response.data) {
        let newNotifications: NotificationModel[] = [];
        
        // 明确的类型检查和转换
        if (response.data && typeof response.data === 'object') {
          const data = response.data as NotificationListData;
          if (data.notifications && Array.isArray(data.notifications)) {
        newNotifications = data.notifications;
          } else if (Array.isArray(response.data)) {
            newNotifications = response.data as NotificationModel[];
          }
        }
        
        if (page === 1) {
          this.notifications = newNotifications;
        } else {
          const updatedNotifications: NotificationModel[] = [];
          this.notifications.forEach(item => updatedNotifications.push(item));
          newNotifications.forEach(item => updatedNotifications.push(item));
          this.notifications = updatedNotifications;
        }
        
        // 明确的分页信息获取
        let hasNext = false;
        if (response.data && typeof response.data === 'object') {
          const data = response.data as NotificationListData;
          if (data.pagination && typeof data.pagination === 'object') {
            hasNext = data.pagination.hasNext || false;
          }
        }
        this.hasMore = hasNext;
        this.currentPage = page;
        this.loadingState = LoadingState.SUCCESS;
      } else {
        this.loadingState = LoadingState.ERROR;
      }
    } catch (error) {
      hilog.error(0x0000, 'NotificationPage', '加载通知失败: %{public}s', JSON.stringify(error));
      this.loadingState = LoadingState.ERROR;
    } finally {
      this.isLoadingMore = false;
    }
  }

  /**
   * 加载未读通知数量
   */
  private async loadUnreadCount() {
    try {
      const response = await this.apiService.getUnreadNotificationCount();
      if (response.code === 200 && response.data) {
        this.unreadCount = (response.data as UnreadCountData).count || 0;
      }
    } catch (error) {
      hilog.error(0x0000, 'NotificationPage', '加载未读通知数量失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 标记通知为已读
   */
  private async markAsRead(notification: NotificationModel) {
    if (notification.is_read) {
      return;
    }

    try {
      await this.apiService.markNotificationAsRead(notification.id);
      
      // 更新本地状态
      const index = this.notifications.findIndex(n => n.id === notification.id);
      if (index !== -1) {
        this.notifications[index].is_read = true;
        this.unreadCount = Math.max(0, this.unreadCount - 1);
      }
    } catch (error) {
      hilog.error(0x0000, 'NotificationPage', '标记通知已读失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 删除通知
   */
  private async deleteNotification(notificationId: number) {
    try {
      await this.apiService.deleteNotification(notificationId);
      
      // 从列表中移除
      const filteredNotifications: NotificationModel[] = [];
      this.notifications.forEach(n => {
        if (n.id !== notificationId) {
          filteredNotifications.push(n);
        }
      });
      this.notifications = filteredNotifications;
    } catch (error) {
      hilog.error(0x0000, 'NotificationPage', '删除通知失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 处理通知点击
   */
  private handleNotificationClick(notification: NotificationModel) {
    // 标记为已读
    this.markAsRead(notification);
    
    // 根据通知类型进行跳转
    if (notification.data && notification.data['app_id']) {
      this.getUIContext().getRouter().pushUrl({
        url: 'pages/AppDetailPage',
        params: {
          appId: notification.data['app_id']
        }
      });
    }
  }

  /**
   * 格式化时间
   */
  private formatTime(timeStr: string): string {
    const time = new Date(timeStr);
    const now = new Date();
    const diff = now.getTime() - time.getTime();
    
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (minutes < 1) {
      return '刚刚';
    } else if (minutes < 60) {
      return `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return time.toLocaleDateString();
    }
  }

  build() {
    Column() {
      // 标题栏
      Row() {
        Button() {
          Image($r('app.media.ic_back'))
            .width(24)
            .height(24)
            .fillColor(Constants.COLORS.TEXT_PRIMARY)
        }
        .width(40)
        .height(40)
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          this.getUIContext().getRouter().back();
        })

        Text('通知')
          .fontSize(Constants.FONT_SIZE.LARGE)
          .fontWeight(FontWeight.Medium)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Button() {
          Image($r('app.media.ic_settings'))
            .width(24)
            .height(24)
            .fillColor(Constants.COLORS.TEXT_PRIMARY)
        }
        .width(40)
        .height(40)
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          this.showSettings = true;
        })
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor(Constants.COLORS.WHITE)

      // 内容区域
      if (this.loadingState === LoadingState.LOADING) {
        LoadingView()
          .layoutWeight(1)
      } else if (this.loadingState === LoadingState.ERROR) {
        Column() {
          Image($r('app.media.ic_error'))
            .width(64)
            .height(64)
            .fillColor(Constants.COLORS.TEXT_HINT)
            .margin({ bottom: 16 })
          
          Text('加载失败')
            .fontSize(Constants.FONT_SIZE.MEDIUM)
            .fontColor(Constants.COLORS.TEXT_SECONDARY)
            .margin({ bottom: 16 })
          
          Button('重试')
            .fontSize(Constants.FONT_SIZE.NORMAL)
            .fontColor(Constants.COLORS.WHITE)
            .backgroundColor(Constants.COLORS.PRIMARY)
            .borderRadius(8)
            .padding({ left: 24, right: 24, top: 8, bottom: 8 })
            .onClick(() => {
              this.loadNotifications();
            })
        }
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
      } else if (!this.notifications || this.notifications.length === 0) {
        Column() {
          Image($r('app.media.ic_notification_empty'))
            .width(120)
            .height(120)
            .fillColor(Constants.COLORS.TEXT_HINT)
            .margin({ bottom: 16 })
          
          Text('暂无通知')
            .fontSize(Constants.FONT_SIZE.MEDIUM)
            .fontColor(Constants.COLORS.TEXT_SECONDARY)
        }
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
      } else {
        List() {
          ForEach(this.notifications, (notification: NotificationModel) => {
            ListItem() {
              Row() {
                Column() {
                  Row() {
                    Text(notification.title)
                      .fontSize(Constants.FONT_SIZE.NORMAL)
                      .fontWeight(notification.is_read ? FontWeight.Normal : FontWeight.Medium)
                      .fontColor(notification.is_read ? Constants.COLORS.TEXT_SECONDARY : Constants.COLORS.TEXT_PRIMARY)
                      .layoutWeight(1)
                      .maxLines(1)
                      .textOverflow({ overflow: TextOverflow.Ellipsis })
                    
                    if (!notification.is_read) {
                      Circle()
                        .width(8)
                        .height(8)
                        .fill(Constants.COLORS.PRIMARY)
                        .margin({ left: 8 })
                    }
                  }
                  .width('100%')
                  .margin({ bottom: 4 })
                  
                  Text(notification.content)
                    .fontSize(Constants.FONT_SIZE.SMALL)
                    .fontColor(Constants.COLORS.TEXT_SECONDARY)
                    .maxLines(2)
                    .textOverflow({ overflow: TextOverflow.Ellipsis })
                    .margin({ bottom: 8 })
                  
                  Text(this.formatTime(notification.created_at))
                    .fontSize(Constants.FONT_SIZE.SMALL)
                    .fontColor(Constants.COLORS.TEXT_HINT)
                }
                .layoutWeight(1)
                .alignItems(HorizontalAlign.Start)
                
                Button() {
                  Image($r('app.media.ic_delete'))
                    .width(20)
                    .height(20)
                    .fillColor(Constants.COLORS.TEXT_HINT)
                }
                .width(32)
                .height(32)
                .backgroundColor(Color.Transparent)
                .onClick(() => {
                  this.deleteNotification(notification.id);
                })
              }
              .width('100%')
              .padding(16)
              .backgroundColor(notification.is_read ? Constants.COLORS.WHITE : Constants.COLORS.BACKGROUND_LIGHT)
            }
            .onClick(() => {
              this.handleNotificationClick(notification);
            })
          })
          
          if (this.hasMore) {
            ListItem() {
              if (this.isLoadingMore) {
                Row() {
                  LoadingProgress()
                    .width(20)
                    .height(20)
                    .margin({ right: 8 })
                  
                  Text('加载中...')
                    .fontSize(Constants.FONT_SIZE.SMALL)
                    .fontColor(Constants.COLORS.TEXT_HINT)
                }
                .justifyContent(FlexAlign.Center)
                .padding(16)
              } else {
                Text('点击加载更多')
                  .fontSize(Constants.FONT_SIZE.SMALL)
                  .fontColor(Constants.COLORS.PRIMARY)
                  .textAlign(TextAlign.Center)
                  .padding(16)
              }
            }
            .onClick(() => {
              if (!this.isLoadingMore) {
                this.loadNotifications(this.currentPage + 1);
              }
            })
          }
        }
        .layoutWeight(1)
        .divider({
          strokeWidth: 1,
          color: Constants.COLORS.BORDER,
          startMargin: 16,
          endMargin: 16
        })
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Constants.COLORS.BACKGROUND)
  }
}