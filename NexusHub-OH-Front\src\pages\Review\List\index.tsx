import { PageContainer } from '@ant-design/pro-components';
import { Card, Table, Button, Tag, Space, Input, Select, DatePicker, Modal, Form, Rate, message } from 'antd';
import { SearchOutlined, ReloadOutlined, EyeOutlined, DeleteOutlined, CheckOutlined, StopOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { useRequest } from '@umijs/max';
import type { ColumnsType } from 'antd/es/table';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;

interface ReviewItem {
  id: string;
  appId: string;
  appName: string;
  userId: string;
  username: string;
  content: string;
  rating: number;
  status: 'approved' | 'rejected' | 'pending';
  createdAt: string;
  updatedAt: string;
  likeCount: number;
  replyCount: number;
}

// 模拟数据获取函数
const fetchReviewList = async (params: any) => {
  console.log('Fetching review list with params:', params);
  
  // 模拟数据
  const mockData: ReviewItem[] = [
    {
      id: '1',
      appId: 'app001',
      appName: '微信',
      userId: 'user001',
      username: 'user12345',
      content: '非常好用的社交软件，功能丰富，界面简洁，推荐使用！',
      rating: 5,
      status: 'approved',
      createdAt: '2023-12-10 14:30:25',
      updatedAt: '2023-12-10 15:20:10',
      likeCount: 45,
      replyCount: 3,
    },
    {
      id: '2',
      appId: 'app002',
      appName: '支付宝',
      userId: 'user002',
      username: 'techfan',
      content: '支付功能很方便，但最近更新后偶尔会出现卡顿现象。',
      rating: 4,
      status: 'approved',
      createdAt: '2023-12-09 10:15:30',
      updatedAt: '2023-12-09 11:05:22',
      likeCount: 32,
      replyCount: 5,
    },
    {
      id: '3',
      appId: 'app003',
      appName: '抖音',
      userId: 'user003',
      username: 'videomaker',
      content: '短视频制作功能很强大，但广告太多了，希望能减少一些。',
      rating: 3,
      status: 'approved',
      createdAt: '2023-12-08 16:45:12',
      updatedAt: '2023-12-08 17:30:05',
      likeCount: 28,
      replyCount: 7,
    },
    {
      id: '4',
      appId: 'app004',
      appName: '淘宝',
      userId: 'user004',
      username: 'shopper',
      content: '购物体验很好，但客服响应有点慢。',
      rating: 4,
      status: 'approved',
      createdAt: '2023-12-07 09:20:45',
      updatedAt: '2023-12-07 10:10:33',
      likeCount: 19,
      replyCount: 2,
    },
    {
      id: '5',
      appId: 'app005',
      appName: '网易云音乐',
      userId: 'user005',
      username: 'musiclover',
      content: '音乐推荐算法很准确，发现了很多喜欢的歌曲！',
      rating: 5,
      status: 'approved',
      createdAt: '2023-12-06 20:30:18',
      updatedAt: '2023-12-06 21:15:27',
      likeCount: 56,
      replyCount: 4,
    },
    {
      id: '6',
      appId: 'app001',
      appName: '微信',
      userId: 'user006',
      username: 'chatmaster',
      content: '最近更新后，语音通话质量有所下降，希望能改进。',
      rating: 3,
      status: 'pending',
      createdAt: '2023-12-05 15:40:22',
      updatedAt: '2023-12-05 15:40:22',
      likeCount: 8,
      replyCount: 0,
    },
    {
      id: '7',
      appId: 'app006',
      appName: 'QQ',
      userId: 'user007',
      username: 'gamer2023',
      content: '聊天功能很稳定，但游戏中心的游戏质量参差不齐。',
      rating: 4,
      status: 'approved',
      createdAt: '2023-12-04 18:25:36',
      updatedAt: '2023-12-04 19:10:42',
      likeCount: 23,
      replyCount: 3,
    },
    {
      id: '8',
      appId: 'app007',
      appName: '美团',
      userId: 'user008',
      username: 'foodie',
      content: '外卖送达速度快，但有时候商家出餐慢影响体验。',
      rating: 4,
      status: 'approved',
      createdAt: '2023-12-03 12:50:15',
      updatedAt: '2023-12-03 13:35:28',
      likeCount: 17,
      replyCount: 2,
    },
    {
      id: '9',
      appId: 'app008',
      appName: '知乎',
      userId: 'user009',
      username: 'thinker',
      content: '内容质量高，但广告越来越多，影响阅读体验。',
      rating: 3,
      status: 'rejected',
      createdAt: '2023-12-02 14:15:33',
      updatedAt: '2023-12-02 15:05:47',
      likeCount: 12,
      replyCount: 1,
    },
    {
      id: '10',
      appId: 'app009',
      appName: 'Bilibili',
      userId: 'user010',
      username: 'animefan',
      content: '视频内容丰富，弹幕互动很有趣，但有时候缓冲速度慢。',
      rating: 4,
      status: 'approved',
      createdAt: '2023-12-01 19:30:27',
      updatedAt: '2023-12-01 20:20:38',
      likeCount: 41,
      replyCount: 6,
    },
  ];

  // 根据状态过滤
  const statusFilteredData = params.status ? mockData.filter(item => item.status === params.status) : mockData;
  
  // 根据评分过滤
  const ratingFilteredData = params.rating ? statusFilteredData.filter(item => item.rating === params.rating) : statusFilteredData;
  
  // 根据关键词过滤
  const keywordFilteredData = params.keyword
    ? ratingFilteredData.filter(item => 
        item.content.toLowerCase().includes(params.keyword.toLowerCase()) ||
        item.appName.toLowerCase().includes(params.keyword.toLowerCase()) ||
        item.username.toLowerCase().includes(params.keyword.toLowerCase())
      )
    : ratingFilteredData;

  return { data: keywordFilteredData, total: keywordFilteredData.length };
};

const ReviewList: React.FC = () => {
  const [searchParams, setSearchParams] = useState({});
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [currentReview, setCurrentReview] = useState<ReviewItem | null>(null);

  const { data, loading, refresh } = useRequest(() => fetchReviewList(searchParams), {
    refreshDeps: [searchParams],
  });

  const handleSearch = (values: any) => {
    setSearchParams(values);
  };

  const handleViewDetail = (record: ReviewItem) => {
    setCurrentReview(record);
    setDetailModalVisible(true);
  };

  const handleDelete = (id: string) => {
    message.success(`已删除评论 ID: ${id}`);
    refresh();
  };

  const handleBatchDelete = () => {
    message.success(`已批量删除 ${selectedRowKeys.length} 条评论`);
    setSelectedRowKeys([]);
    refresh();
  };

  const handleApprove = (id: string) => {
    message.success(`已通过评论 ID: ${id}`);
    refresh();
  };

  const handleReject = (id: string) => {
    message.success(`已拒绝评论 ID: ${id}`);
    refresh();
  };

  const columns: ColumnsType<ReviewItem> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '应用名称',
      dataIndex: 'appName',
      key: 'appName',
      width: 120,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      width: 120,
    },
    {
      title: '评分',
      dataIndex: 'rating',
      key: 'rating',
      width: 100,
      render: (rating: number) => <Rate disabled defaultValue={rating} />,
    },
    {
      title: '评论内容',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
      width: 300,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        let color = 'blue';
        let text = '待审核';
        
        if (status === 'approved') {
          color = 'green';
          text = '已通过';
        } else if (status === 'rejected') {
          color = 'red';
          text = '已拒绝';
        }
        
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '点赞数',
      dataIndex: 'likeCount',
      key: 'likeCount',
      width: 80,
    },
    {
      title: '回复数',
      dataIndex: 'replyCount',
      key: 'replyCount',
      width: 80,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="text" 
            icon={<EyeOutlined />} 
            onClick={() => handleViewDetail(record)}
          >
            查看
          </Button>
          {record.status === 'pending' && (
            <>
              <Button 
                type="text" 
                icon={<CheckOutlined />} 
                style={{ color: 'green' }}
                onClick={() => handleApprove(record.id)}
              >
                通过
              </Button>
              <Button 
                type="text" 
                icon={<StopOutlined />} 
                style={{ color: 'red' }}
                onClick={() => handleReject(record.id)}
              >
                拒绝
              </Button>
            </>
          )}
          <Button 
            type="text" 
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer
      header={{
        title: '评论列表',
        subTitle: '查看和管理所有应用评论',
      }}
    >
      <Card>
        <Form layout="inline" onFinish={handleSearch} style={{ marginBottom: 24 }}>
          <Form.Item name="keyword" label="关键词">
            <Input placeholder="评论内容/应用名称/用户名" prefix={<SearchOutlined />} />
          </Form.Item>
          <Form.Item name="status" label="状态">
            <Select style={{ width: 120 }} placeholder="全部状态" allowClear>
              <Option value="approved">已通过</Option>
              <Option value="rejected">已拒绝</Option>
              <Option value="pending">待审核</Option>
            </Select>
          </Form.Item>
          <Form.Item name="rating" label="评分">
            <Select style={{ width: 120 }} placeholder="全部评分" allowClear>
              <Option value={1}>1星</Option>
              <Option value={2}>2星</Option>
              <Option value={3}>3星</Option>
              <Option value={4}>4星</Option>
              <Option value={5}>5星</Option>
            </Select>
          </Form.Item>
          <Form.Item name="dateRange" label="时间范围">
            <RangePicker />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit">
              搜索
            </Button>
          </Form.Item>
          <Form.Item>
            <Button onClick={() => setSearchParams({})}>
              重置
            </Button>
          </Form.Item>
        </Form>

        <div style={{ marginBottom: 16 }}>
          <Button 
            danger 
            disabled={selectedRowKeys.length === 0}
            onClick={handleBatchDelete}
          >
            批量删除
          </Button>
          <Button 
            icon={<ReloadOutlined />} 
            style={{ marginLeft: 8 }}
            onClick={refresh}
          >
            刷新
          </Button>
          <span style={{ marginLeft: 8 }}>
            {selectedRowKeys.length > 0 ? `已选择 ${selectedRowKeys.length} 项` : ''}
          </span>
        </div>

        <Table
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
          columns={columns}
          dataSource={data?.data}
          rowKey="id"
          loading={loading}
          pagination={{
            total: data?.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
          }}
          scroll={{ x: 1500 }}
        />
      </Card>

      <Modal
        title="评论详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={600}
      >
        {currentReview && (
          <div>
            <p><strong>应用名称：</strong> {currentReview.appName}</p>
            <p><strong>用户名：</strong> {currentReview.username}</p>
            <p><strong>评分：</strong> <Rate disabled defaultValue={currentReview.rating} /></p>
            <p><strong>评论内容：</strong></p>
            <p style={{ background: '#f5f5f5', padding: 12, borderRadius: 4 }}>
              {currentReview.content}
            </p>
            <p><strong>创建时间：</strong> {currentReview.createdAt}</p>
            <p><strong>更新时间：</strong> {currentReview.updatedAt}</p>
            <p><strong>点赞数：</strong> {currentReview.likeCount}</p>
            <p><strong>回复数：</strong> {currentReview.replyCount}</p>
            <p>
              <strong>状态：</strong> 
              {currentReview.status === 'approved' && <Tag color="green">已通过</Tag>}
              {currentReview.status === 'rejected' && <Tag color="red">已拒绝</Tag>}
              {currentReview.status === 'pending' && <Tag color="blue">待审核</Tag>}
            </p>
          </div>
        )}
      </Modal>
    </PageContainer>
  );
};

export default ReviewList;