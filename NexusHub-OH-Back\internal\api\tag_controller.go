package api

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"nexushub-oh-back/internal/models"
	"nexushub-oh-back/pkg/response"
)

// TagController 标签控制器
type TagController struct {
	db *gorm.DB
}

// NewTagController 创建标签控制器
func NewTagController(db *gorm.DB) *TagController {
	return &TagController{db: db}
}

// CreateTagRequest 创建标签请求
type CreateTagRequest struct {
	Name        string `json:"name" binding:"required,max=50"`
	Description string `json:"description" binding:"max=1000"`
	Color       string `json:"color" binding:"max=20"`
}

// UpdateTagRequest 更新标签请求
type UpdateTagRequest struct {
	Name        string `json:"name" binding:"max=50"`
	Description string `json:"description" binding:"max=1000"`
	Color       string `json:"color" binding:"max=20"`
	IsActive    *bool  `json:"is_active"`
}

// TagResponse 标签响应结构体
type TagResponse struct {
	ID          uint       `json:"id"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	DeletedAt   *time.Time `json:"deleted_at,omitempty"`
	Name        string     `json:"name"`
	Description string     `json:"description"`
	Color       string     `json:"color"`
	IsActive    bool       `json:"is_active"`
}

// TagAppCountResponse 标签应用数量响应结构体
type TagAppCountResponse struct {
	TagResponse
	AppCount int `json:"app_count"`
}

type AppResponse struct {
	ID              uint       `json:"id"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
	DeletedAt       *time.Time `json:"deleted_at,omitempty"`
	Name            string     `json:"name"`
	PackageName     string     `json:"package_name"`
	Description     string     `json:"description"`
	Icon            string     `json:"icon"`
	DeveloperID     uint       `json:"developer_id"`
	CategoryID      int        `json:"category_id"`
	DownloadCount   int        `json:"download_count"`
	AverageRating   float64    `json:"average_rating"`
	ReviewCount     int        `json:"review_count"`
	MinAndroidSDK   int        `json:"min_android_sdk"`
	Status          string     `json:"status"`
	CurrentVersions string     `json:"current_versions"`
}

// CreateTag 创建标签
//
//	@Summary		创建应用标签
//	@Description	创建一个新的应用标签
//	@Tags			标签管理
//	@Accept			json
//	@Produce		json
//	@Param			Authorization	header		string				true	"Bearer 用户令牌"
//	@Param			tag				body		CreateTagRequest	true	"标签信息"
//	@Success		201				{object}	TagResponse			"创建成功"
//	@Failure		400				{object}	response.ErrorResponse		"请求参数错误"
//	@Failure		401				{object}	response.ErrorResponse		"未授权"
//	@Failure		403				{object}	response.ErrorResponse		"权限不足"
//	@Failure		500				{object}	response.ErrorResponse		"内部服务器错误"
//	@Router			/tags [post]
func (c *TagController) CreateTag(ctx *gin.Context) {
	var req CreateTagRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, "无效的请求参数")
		return
	}

	// 检查标签名称是否已存在
	existingTag, err := models.GetTagByName(c.DB, req.Name)
	if err == nil && existingTag.ID > 0 {
		response.BadRequest(ctx, "标签名称已存在")
		return
	}

	tag := models.Tag{
		Name:        req.Name,
		Description: req.Description,
		Color:       req.Color,
		IsActive:    true,
	}

	if err := models.CreateTag(c.DB, &tag); err != nil {
		response.InternalServerError(ctx, "创建标签失败")
		return
	}

	// 转换为响应结构体
	var deletedAt *time.Time
	if tag.DeletedAt != nil {
		deletedAt = tag.DeletedAt
	}

	result := TagResponse{
		ID:          tag.ID,
		CreatedAt:   tag.CreatedAt,
		UpdatedAt:   tag.UpdatedAt,
		DeletedAt:   deletedAt,
		Name:        tag.Name,
		Description: tag.Description,
		Color:       tag.Color,
		IsActive:    tag.IsActive,
	}

	ctx.JSON(http.StatusCreated, result)
}

// GetTag 获取标签
//
//	@Summary		获取应用标签
//	@Description	获取应用标签详情
//	@Tags			标签管理
//	@Accept			json
//	@Produce		json
//	@Param			id	path		int					true	"标签ID"
//	@Success		200	{object}	TagAppCountResponse	"标签详情"
//	@Failure		400	{object}	response.ErrorResponse		"请求参数错误"
//	@Failure		404	{object}	response.ErrorResponse		"标签不存在"
//	@Failure		500	{object}	response.ErrorResponse		"内部服务器错误"
//	@Router			/tags/{id} [get]
func (c *TagController) GetTag(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(ctx, "无效的标签ID")
		return
	}

	tag, err := models.GetTagByID(c.DB, uint(id))
	if err != nil {
		response.NotFound(ctx, "标签不存在")
		return
	}

	// 获取使用该标签的应用数量
	var appCount int64
	if err := c.DB.Model(&models.AppTag{}).Where("tag_id = ?", tag.ID).Count(&appCount).Error; err != nil {
		response.InternalServerError(ctx, "获取标签使用数量失败")
		return
	}

	// 转换为响应结构体
	var deletedAt *time.Time
	if tag.DeletedAt != nil {
		deletedAt = tag.DeletedAt
	}

	response := TagAppCountResponse{
		TagResponse: TagResponse{
			ID:          tag.ID,
			CreatedAt:   tag.CreatedAt,
			UpdatedAt:   tag.UpdatedAt,
			DeletedAt:   deletedAt,
			Name:        tag.Name,
			Description: tag.Description,
			Color:       tag.Color,
			IsActive:    tag.IsActive,
		},
		AppCount: int(appCount),
	}

	ctx.JSON(http.StatusOK, response)
}

// UpdateTag 更新标签
//
//	@Summary		更新应用标签
//	@Description	更新应用标签信息
//	@Tags			标签管理
//	@Accept			json
//	@Produce		json
//	@Param			Authorization	header		string				true	"Bearer 用户令牌"
//	@Param			id				path		int					true	"标签ID"
//	@Param			tag				body		UpdateTagRequest	true	"标签信息"
//	@Success		200				{object}	TagResponse			"更新成功"
//	@Failure		400				{object}	response.ErrorResponse		"请求参数错误"
//	@Failure		401				{object}	response.ErrorResponse		"未授权"
//	@Failure		403				{object}	response.ErrorResponse		"权限不足"
//	@Failure		404				{object}	response.ErrorResponse		"标签不存在"
//	@Failure		500				{object}	response.ErrorResponse		"内部服务器错误"
//	@Router			/tags/{id} [put]
func (c *TagController) UpdateTag(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(ctx, "无效的标签ID")
		return
	}

	tag, err := models.GetTagByID(c.DB, uint(id))
	if err != nil {
		response.NotFound(ctx, "标签不存在")
		return
	}

	var req UpdateTagRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, "无效的请求参数")
		return
	}

	// 如果更新了名称，检查是否与其他标签重名
	if req.Name != "" && req.Name != tag.Name {
		existingTag, err := models.GetTagByName(c.DB, req.Name)
		if err == nil && existingTag.ID > 0 && existingTag.ID != tag.ID {
			response.BadRequest(ctx, "标签名称已存在")
			return
		}
		tag.Name = req.Name
	}

	// 更新其他字段
	if req.Description != "" {
		tag.Description = req.Description
	}
	if req.Color != "" {
		tag.Color = req.Color
	}

	// 更新状态
	if req.IsActive != nil {
		tag.IsActive = *req.IsActive
	}

	if err := models.UpdateTag(c.DB, tag); err != nil {
		response.InternalServerError(ctx, "更新标签失败")
		return
	}

	// 转换为响应结构体
	var deletedAt *time.Time
	if tag.DeletedAt != nil {
		deletedAt = tag.DeletedAt
	}

	result := TagResponse{
		ID:          tag.ID,
		CreatedAt:   tag.CreatedAt,
		UpdatedAt:   tag.UpdatedAt,
		DeletedAt:   deletedAt,
		Name:        tag.Name,
		Description: tag.Description,
		Color:       tag.Color,
		IsActive:    tag.IsActive,
	}

	ctx.JSON(http.StatusOK, result)
}

// DeleteTag 删除标签
//
//	@Summary		删除应用标签
//	@Description	删除应用标签
//	@Tags			标签管理
//	@Accept			json
//	@Produce		json
//	@Param			Authorization	header		string			true	"Bearer 用户令牌"
//	@Param			id				path		int				true	"标签ID"
//	@Success		200				{object}	response.SuccessResponse	"删除成功"
//	@Failure		400				{object}	response.ErrorResponse	"请求参数错误"
//	@Failure		401				{object}	response.ErrorResponse	"未授权"
//	@Failure		403				{object}	response.ErrorResponse	"权限不足"
//	@Failure		404				{object}	response.ErrorResponse	"标签不存在"
//	@Failure		500				{object}	response.ErrorResponse	"内部服务器错误"
//	@Router			/tags/{id} [delete]
func (c *TagController) DeleteTag(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(ctx, "无效的标签ID")
		return
	}

	// 检查标签是否存在
	tag, err := models.GetTagByID(c.DB, uint(id))
	if err != nil {
		response.NotFound(ctx, "标签不存在")
		return
	}

	// 检查是否有应用使用该标签
	var count int64
	if err := c.DB.Model(&models.AppTag{}).Where("tag_id = ?", tag.ID).Count(&count).Error; err != nil {
		response.InternalServerError(ctx, "检查关联应用失败")
		return
	}

	if count > 0 {
		response.BadRequest(ctx, "该标签正在被应用使用，不能删除")
		return
	}

	if err := models.DeleteTag(c.DB, tag.ID); err != nil {
		response.InternalServerError(ctx, "删除标签失败")
		return
	}

	response.Success(ctx, gin.H{"message": "标签删除成功"})
}

// ListTags 获取标签列表
//
//	@Summary		获取应用标签列表
//	@Description	获取所有应用标签
//	@Tags			标签管理
//	@Accept			json
//	@Produce		json
//	@Param			include_inactive	query		bool			false	"是否包含未激活的标签"	default(false)
//	@Success		200					{array}		TagResponse		"标签列表"
//	@Failure		500					{object}	response.ErrorResponse	"内部服务器错误"
//	@Router			/tags [get]
func (c *TagController) ListTags(ctx *gin.Context) {
	includeInactive := ctx.Query("include_inactive") == "true"

	tags, err := models.ListTags(c.DB, includeInactive)
	if err != nil {
		response.InternalServerError(ctx, "获取标签列表失败")
		return
	}

	// 转换为响应结构体
	var responses []TagResponse
	for _, tag := range tags {
		var deletedAt *time.Time
		if tag.DeletedAt != nil {
			deletedAt = tag.DeletedAt
		}

		responses = append(responses, TagResponse{
			ID:          tag.ID,
			CreatedAt:   tag.CreatedAt,
			UpdatedAt:   tag.UpdatedAt,
			DeletedAt:   deletedAt,
			Name:        tag.Name,
			Description: tag.Description,
			Color:       tag.Color,
			IsActive:    tag.IsActive,
		})
	}

	ctx.JSON(http.StatusOK, responses)
}

// AppTagRequest 应用标签关联请求
type AppTagRequest struct {
	TagIDs []uint `json:"tag_ids" binding:"required"`
}

// AddAppTags 为应用添加标签
//
//	@Summary		为应用添加标签
//	@Description	为应用添加多个标签
//	@Tags			标签管理
//	@Accept			json
//	@Produce		json
//	@Param			Authorization	header		string			true	"Bearer 用户令牌"
//	@Param			id				path		int				true	"应用ID"
//	@Param			tags			body		AppTagRequest	true	"标签ID列表"
//	@Success		200				{object}	response.SuccessResponse	"添加成功"
//	@Failure		400				{object}	response.ErrorResponse	"请求参数错误"
//	@Failure		401				{object}	response.ErrorResponse	"未授权"
//	@Failure		403				{object}	response.ErrorResponse	"权限不足"
//	@Failure		404				{object}	response.ErrorResponse	"应用或标签不存在"
//	@Failure		500				{object}	response.ErrorResponse	"内部服务器错误"
//	@Router			/apps/{id}/tags [post]
func (c *TagController) AddAppTags(ctx *gin.Context) {
	appID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(ctx, "无效的应用ID")
		return
	}

	// 检查应用是否存在
	app, err := models.GetApplicationByID(c.DB, uint(appID))
	if err != nil {
		response.NotFound(ctx, "应用不存在")
		return
	}

	var req AppTagRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, "无效的请求参数")
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "未授权")
		return
	}

	// 获取用户角色
	userRole, exists := ctx.Get("role")
	if !exists {
		response.Unauthorized(ctx, "未授权")
		return
	}

	// 检查权限：只有开发者本人或管理员可以添加标签
	if app.DeveloperID != userID.(uint) && userRole.(string) != string(models.UserRoleAdmin) {
		response.Forbidden(ctx, "没有权限操作此应用")
		return
	}

	// 开始事务
	tx := c.DB.Begin()
	if tx.Error != nil {
		response.InternalServerError(ctx, "启动事务失败")
		return
	}

	// 为应用添加标签
	for _, tagID := range req.TagIDs {
		// 检查标签是否存在
		tag, err := models.GetTagByID(tx, tagID)
		if err != nil {
			tx.Rollback()
			response.NotFound(ctx, "标签ID "+strconv.FormatUint(uint64(tagID), 10)+" 不存在")
			return
		}

		// 如果标签未激活，跳过
		if !tag.IsActive {
			continue
		}

		// 添加标签关联
		if err := models.AddAppTag(tx, app.ID, tagID); err != nil {
			tx.Rollback()
			// 忽略唯一约束错误（标签已存在）
			if err.Error() != "UNIQUE constraint failed: app_tags.application_id, app_tags.tag_id" {
				response.InternalServerError(ctx, "添加标签失败")
				return
			}
		}
	}

	if err := tx.Commit().Error; err != nil {
		response.InternalServerError(ctx, "提交事务失败")
		return
	}

	response.Success(ctx, gin.H{"message": "标签添加成功"})
}

// RemoveAppTag 删除应用的标签
//
//	@Summary		删除应用的标签
//	@Description	删除应用的指定标签
//	@Tags			标签管理
//	@Accept			json
//	@Produce		json
//	@Param			Authorization	header		string			true	"Bearer 用户令牌"
//	@Param			id				path		int				true	"应用ID"
//	@Param			tag_id			path		int				true	"标签ID"
//	@Success		200				{object}	response.SuccessResponse	"删除成功"
//	@Failure		400				{object}	response.ErrorResponse	"请求参数错误"
//	@Failure		401				{object}	response.ErrorResponse	"未授权"
//	@Failure		403				{object}	response.ErrorResponse	"权限不足"
//	@Failure		404				{object}	response.ErrorResponse	"应用或标签不存在"
//	@Failure		500				{object}	response.ErrorResponse	"内部服务器错误"
//	@Router			/apps/{id}/tags/{tag_id} [delete]
func (c *TagController) RemoveAppTag(ctx *gin.Context) {
	appID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(ctx, "无效的应用ID")
		return
	}

	tagID, err := strconv.ParseUint(ctx.Param("tag_id"), 10, 32)
	if err != nil {
		response.BadRequest(ctx, "无效的标签ID")
		return
	}

	// 检查应用是否存在
	app, err := models.GetApplicationByID(c.DB, uint(appID))
	if err != nil {
		response.NotFound(ctx, "应用不存在")
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "未授权")
		return
	}

	// 获取用户角色
	userRole, exists := ctx.Get("role")
	if !exists {
		response.Unauthorized(ctx, "未授权")
		return
	}

	// 检查权限：只有开发者本人或管理员可以删除标签
	if app.DeveloperID != userID.(uint) && userRole.(string) != string(models.UserRoleAdmin) {
		response.Forbidden(ctx, "没有权限操作此应用")
		return
	}

	// 删除标签关联
	if err := models.RemoveAppTag(c.DB, app.ID, uint(tagID)); err != nil {
		response.InternalServerError(ctx, "删除标签失败")
		return
	}

	response.Success(ctx, gin.H{"message": "标签删除成功"})
}

// GetAppTags 获取应用的标签
//
//	@Summary		获取应用的标签
//	@Description	获取应用的所有标签
//	@Tags			标签管理
//	@Accept			json
//	@Produce		json
//	@Param			id	path		int				true	"应用ID"
//	@Success		200	{array}		TagResponse		"标签列表"
//	@Failure		400	{object}	response.ErrorResponse	"请求参数错误"
//	@Failure		404	{object}	response.ErrorResponse	"应用不存在"
//	@Failure		500	{object}	response.ErrorResponse	"内部服务器错误"
//	@Router			/apps/{id}/tags [get]
func (c *TagController) GetAppTags(ctx *gin.Context) {
	appID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(ctx, "无效的应用ID")
		return
	}

	// 检查应用是否存在
	_, err = models.GetApplicationByID(c.DB, uint(appID))
	if err != nil {
		response.NotFound(ctx, "应用不存在")
		return
	}

	// 获取应用的所有标签
	tags, err := models.GetAppTags(c.DB, uint(appID))
	if err != nil {
		response.InternalServerError(ctx, "获取应用标签失败")
		return
	}

	// 转换为响应结构体
	var responses []TagResponse
	for _, tag := range tags {
		var deletedAt *time.Time
		if tag.DeletedAt != nil {
			deletedAt = tag.DeletedAt
		}

		responses = append(responses, TagResponse{
			ID:          tag.ID,
			CreatedAt:   tag.CreatedAt,
			UpdatedAt:   tag.UpdatedAt,
			DeletedAt:   deletedAt,
			Name:        tag.Name,
			Description: tag.Description,
			Color:       tag.Color,
			IsActive:    tag.IsActive,
		})
	}

	ctx.JSON(http.StatusOK, responses)
}

// GetAppsByTag 获取标签下的应用
//
//	@Summary		获取指定标签的应用
//	@Description	获取包含指定标签的所有应用
//	@Tags			标签管理
//	@Accept			json
//	@Produce		json
//	@Param			id	path		int				true	"标签ID"
//	@Success		200	{array}		AppResponse		"应用列表"
//	@Failure		400	{object}	response.ErrorResponse	"请求参数错误"
//	@Failure		404	{object}	response.ErrorResponse	"标签不存在"
//	@Failure		500	{object}	response.ErrorResponse	"内部服务器错误"
//	@Router			/tags/{id}/apps [get]
func (c *TagController) GetAppsByTag(ctx *gin.Context) {
	tagID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(ctx, "无效的标签ID")
		return
	}

	// 检查标签是否存在
	_, err = models.GetTagByID(c.DB, uint(tagID))
	if err != nil {
		response.NotFound(ctx, "标签不存在")
		return
	}

	// 获取包含该标签的所有应用
	apps, err := models.GetAppsByTag(c.DB, uint(tagID))
	if err != nil {
		response.InternalServerError(ctx, "获取标签应用失败")
		return
	}

	// 转换为响应结构体
	var responses []AppResponse
	for _, app := range apps {
		var deletedAt *time.Time
		if app.DeletedAt != nil {
			deletedAt = app.DeletedAt
		}

		responses = append(responses, AppResponse{
			ID:              app.ID,
			CreatedAt:       app.CreatedAt,
			UpdatedAt:       app.UpdatedAt,
			DeletedAt:       deletedAt,
			Name:            app.Name,
			PackageName:     app.Package,
			Description:     app.Description,
			Icon:            app.Icon,
			DeveloperID:     app.DeveloperID,
			CategoryID:      0, // Application模型中使用Category字符串而非ID
			DownloadCount:   int(app.DownloadCount),
			AverageRating:   app.AverageRating,
			ReviewCount:     app.RatingCount,
			MinAndroidSDK:   0, // 不适用
			Status:          string(app.Status),
			CurrentVersions: app.CurrentVersion,
		})
	}

	ctx.JSON(http.StatusOK, responses)
}
