package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"nexushub-oh-back/internal/models"
	"nexushub-oh-back/pkg/elasticsearch"
	"nexushub-oh-back/pkg/logger"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// UserSearchService 用户搜索服务
type UserSearchService struct {
	DB       *gorm.DB
	ESClient *elasticsearch.ESClient
}

// NewUserSearchService 创建用户搜索服务
func NewUserSearchService(db *gorm.DB, esClient *elasticsearch.ESClient) *UserSearchService {
	return &UserSearchService{
		DB:       db,
		ESClient: esClient,
	}
}

// UserDocument Elasticsearch中的用户文档结构
type UserDocument struct {
	ID               uint      `json:"id"`
	Username         string    `json:"username"`
	Email            string    `json:"email"`
	Phone            string    `json:"phone"`
	Role             string    `json:"role"`
	Status           string    `json:"status"`
	IsDeveloper      bool      `json:"is_developer"`
	DeveloperName    string    `json:"developer_name"`
	CompanyName      string    `json:"company_name"`
	Website          string    `json:"website"`
	Description      string    `json:"description"`
	ContactEmail     string    `json:"contact_email"`
	ContactPhone     string    `json:"contact_phone"`
	DeveloperAddress string    `json:"developer_address"`
	VerifyStatus     string    `json:"verify_status"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
	LastLoginAt      time.Time `json:"last_login_at"`
	LoginCount       int       `json:"login_count"`
	Keywords         []string  `json:"keywords"` // 用于搜索的关键词
}

// UserSearchRequest 用户搜索请求
type UserSearchRequest struct {
	Keyword      string   `json:"keyword"`
	Role         string   `json:"role"`
	Status       string   `json:"status"`
	IsDeveloper  *bool    `json:"is_developer"`
	VerifyStatus string   `json:"verify_status"`
	SortBy       string   `json:"sort_by"`    // username, created_at, last_login_at, login_count
	SortOrder    string   `json:"sort_order"` // asc, desc
	Page         int      `json:"page"`
	PageSize     int      `json:"page_size"`
}

// UserSearchResponse 用户搜索响应
type UserSearchResponse struct {
	Users      []UserDocument `json:"users"`
	Total      int64          `json:"total"`
	Page       int            `json:"page"`
	PageSize   int            `json:"page_size"`
	TotalPages int            `json:"total_pages"`
}

const (
	UserIndexName = "nexushub_users"
)

// InitializeUserIndex 初始化用户Elasticsearch索引
func (s *UserSearchService) InitializeUserIndex(ctx context.Context) error {
	// 定义用户索引的映射
	mapping := `{
		"mappings": {
			"properties": {
				"id": {"type": "long"},
				"username": {
					"type": "text",
					"analyzer": "standard",
					"fields": {
						"keyword": {"type": "keyword"}
					}
				},
				"email": {
					"type": "text",
					"analyzer": "standard",
					"fields": {
						"keyword": {"type": "keyword"}
					}
				},
				"phone": {"type": "keyword"},
				"role": {"type": "keyword"},
				"status": {"type": "keyword"},
				"is_developer": {"type": "boolean"},
				"developer_name": {
					"type": "text",
					"analyzer": "standard",
					"fields": {
						"keyword": {"type": "keyword"}
					}
				},
				"company_name": {
					"type": "text",
					"analyzer": "standard",
					"fields": {
						"keyword": {"type": "keyword"}
					}
				},
				"website": {"type": "keyword"},
				"description": {
					"type": "text",
					"analyzer": "standard"
				},
				"contact_email": {"type": "keyword"},
				"contact_phone": {"type": "keyword"},
				"developer_address": {
					"type": "text",
					"analyzer": "standard"
				},
				"verify_status": {"type": "keyword"},
				"created_at": {"type": "date"},
				"updated_at": {"type": "date"},
				"last_login_at": {"type": "date"},
				"login_count": {"type": "integer"},
				"keywords": {
					"type": "text",
					"analyzer": "standard"
				}
			}
		}
	}`

	// 检查索引是否存在
	exists, err := s.ESClient.IndexExists(ctx, UserIndexName)
	if err != nil {
		return fmt.Errorf("检查用户索引是否存在失败: %w", err)
	}

	if !exists {
		if err := s.ESClient.CreateIndex(ctx, UserIndexName, mapping); err != nil {
			return fmt.Errorf("创建用户索引失败: %w", err)
		}
		logger.Info("用户索引创建成功", zap.String("index", UserIndexName))
	} else {
		logger.Info("用户索引已存在", zap.String("index", UserIndexName))
	}

	return nil
}

// IndexUser 索引用户到Elasticsearch
func (s *UserSearchService) IndexUser(ctx context.Context, user *models.User) error {
	// 构建关键词
	keywords := []string{
		user.Username,
		user.Email,
		user.DeveloperName,
		user.CompanyName,
		user.Description,
		user.DeveloperAddress,
	}

	// 过滤空关键词
	var filteredKeywords []string
	for _, keyword := range keywords {
		if strings.TrimSpace(keyword) != "" {
			filteredKeywords = append(filteredKeywords, keyword)
		}
	}

	// 构建用户文档
	userDoc := UserDocument{
		ID:               user.ID,
		Username:         user.Username,
		Email:            user.Email,
		Phone:            user.Phone,
		Role:             user.Role,
		Status:           string(user.Status),
		IsDeveloper:      user.IsDeveloper,
		DeveloperName:    user.DeveloperName,
		CompanyName:      user.CompanyName,
		Website:          user.Website,
		Description:      user.Description,
		ContactEmail:     user.ContactEmail,
		ContactPhone:     user.ContactPhone,
		DeveloperAddress: user.DeveloperAddress,
		VerifyStatus:     string(user.VerifyStatus),
		CreatedAt:        user.CreatedAt,
		UpdatedAt:        user.UpdatedAt,
		LoginCount:       user.LoginCount,
		Keywords:         filteredKeywords,
	}

	// 处理最后登录时间
	if user.LastLoginAt != nil {
		userDoc.LastLoginAt = *user.LastLoginAt
	}

	// 序列化文档
	docJSON, err := json.Marshal(userDoc)
	if err != nil {
		return fmt.Errorf("序列化用户文档失败: %w", err)
	}

	// 索引文档
	documentID := strconv.FormatUint(uint64(user.ID), 10)
	if err := s.ESClient.IndexDocument(ctx, UserIndexName, documentID, string(docJSON)); err != nil {
		return fmt.Errorf("索引用户文档失败: %w", err)
	}

	logger.Info("用户索引成功", zap.Uint("user_id", user.ID), zap.String("username", user.Username))
	return nil
}

// SearchUsers 搜索用户
func (s *UserSearchService) SearchUsers(ctx context.Context, req *UserSearchRequest) (*UserSearchResponse, error) {
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.SortBy == "" {
		req.SortBy = "created_at"
	}
	if req.SortOrder == "" {
		req.SortOrder = "desc"
	}

	// 构建查询
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": []interface{}{},
				"filter": []interface{}{},
			},
		},
		"sort": []interface{}{
			map[string]interface{}{
				req.SortBy: map[string]interface{}{
					"order": req.SortOrder,
				},
			},
		},
		"from": (req.Page - 1) * req.PageSize,
		"size": req.PageSize,
	}

	mustQueries := query["query"].(map[string]interface{})["bool"].(map[string]interface{})["must"].([]interface{})
	filterQueries := query["query"].(map[string]interface{})["bool"].(map[string]interface{})["filter"].([]interface{})

	// 关键词搜索
	if req.Keyword != "" {
		mustQueries = append(mustQueries, map[string]interface{}{
			"multi_match": map[string]interface{}{
				"query": req.Keyword,
				"fields": []string{
					"username^3",
					"developer_name^2",
					"company_name^2",
					"email",
					"description",
					"keywords",
				},
				"type": "best_fields",
				"fuzziness": "AUTO",
			},
		})
	}

	// 角色过滤
	if req.Role != "" {
		filterQueries = append(filterQueries, map[string]interface{}{
			"term": map[string]interface{}{
				"role": req.Role,
			},
		})
	}

	// 状态过滤
	if req.Status != "" {
		filterQueries = append(filterQueries, map[string]interface{}{
			"term": map[string]interface{}{
				"status": req.Status,
			},
		})
	}

	// 开发者过滤
	if req.IsDeveloper != nil {
		filterQueries = append(filterQueries, map[string]interface{}{
			"term": map[string]interface{}{
				"is_developer": *req.IsDeveloper,
			},
		})
	}

	// 认证状态过滤
	if req.VerifyStatus != "" {
		filterQueries = append(filterQueries, map[string]interface{}{
			"term": map[string]interface{}{
				"verify_status": req.VerifyStatus,
			},
		})
	}

	// 更新查询
	query["query"].(map[string]interface{})["bool"].(map[string]interface{})["must"] = mustQueries
	query["query"].(map[string]interface{})["bool"].(map[string]interface{})["filter"] = filterQueries

	// 序列化查询
	queryJSON, err := json.Marshal(query)
	if err != nil {
		return nil, fmt.Errorf("序列化查询失败: %w", err)
	}

	// 执行搜索
	result, err := s.ESClient.Search(ctx, UserIndexName, string(queryJSON))
	if err != nil {
		return nil, fmt.Errorf("搜索用户失败: %w", err)
	}

	// 解析结果
	hits := result["hits"].(map[string]interface{})
	total := int64(hits["total"].(map[string]interface{})["value"].(float64))
	hitsList := hits["hits"].([]interface{})

	var users []UserDocument
	for _, hit := range hitsList {
		hitMap := hit.(map[string]interface{})
		source := hitMap["_source"].(map[string]interface{})

		// 解析用户文档
		sourceJSON, err := json.Marshal(source)
		if err != nil {
			continue
		}

		var user UserDocument
		if err := json.Unmarshal(sourceJSON, &user); err != nil {
			continue
		}

		users = append(users, user)
	}

	// 计算总页数
	totalPages := int(total) / req.PageSize
	if int(total)%req.PageSize > 0 {
		totalPages++
	}

	return &UserSearchResponse{
		Users:      users,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// DeleteUser 从Elasticsearch删除用户
func (s *UserSearchService) DeleteUser(ctx context.Context, userID uint) error {
	documentID := strconv.FormatUint(uint64(userID), 10)
	if err := s.ESClient.DeleteDocument(ctx, UserIndexName, documentID); err != nil {
		return fmt.Errorf("删除用户文档失败: %w", err)
	}

	logger.Info("用户文档删除成功", zap.Uint("user_id", userID))
	return nil
}

// SyncAllUsers 同步所有用户到Elasticsearch
func (s *UserSearchService) SyncAllUsers(ctx context.Context) error {
	var users []models.User
	if err := s.DB.Find(&users).Error; err != nil {
		return fmt.Errorf("获取用户列表失败: %w", err)
	}

	for _, user := range users {
		if err := s.IndexUser(ctx, &user); err != nil {
			logger.Error("同步用户失败", zap.Uint("user_id", user.ID), zap.Error(err))
			continue
		}
	}

	logger.Info("用户同步完成", zap.Int("total", len(users)))
	return nil
}