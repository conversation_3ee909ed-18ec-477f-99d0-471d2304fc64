if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface FavoritesPage_Params {
    favoriteApps?: AppModel[];
    loadingState?: LoadingState;
    selectedCategory?: string;
    categories?: string[];
    deviceUtils?;
    apiService?;
}
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { DeviceUtils } from "@normalized:N&&&entry/src/main/ets/utils/DeviceUtils&";
import { ApiService } from "@normalized:N&&&entry/src/main/ets/services/ApiService&";
import { LoadingView, LoadingState } from "@normalized:N&&&entry/src/main/ets/components/LoadingView&";
import hilog from "@ohos:hilog";
import type { AppModel } from '../models/App';
class FavoritesPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__favoriteApps = new ObservedPropertyObjectPU([], this, "favoriteApps");
        this.__loadingState = new ObservedPropertySimplePU(LoadingState.LOADING, this, "loadingState");
        this.__selectedCategory = new ObservedPropertySimplePU('全部', this, "selectedCategory");
        this.__categories = new ObservedPropertyObjectPU(['全部', '游戏', '社交', '工具', '娱乐', '教育'], this, "categories");
        this.deviceUtils = DeviceUtils.getInstance();
        this.apiService = ApiService.getInstance();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: FavoritesPage_Params) {
        if (params.favoriteApps !== undefined) {
            this.favoriteApps = params.favoriteApps;
        }
        if (params.loadingState !== undefined) {
            this.loadingState = params.loadingState;
        }
        if (params.selectedCategory !== undefined) {
            this.selectedCategory = params.selectedCategory;
        }
        if (params.categories !== undefined) {
            this.categories = params.categories;
        }
        if (params.deviceUtils !== undefined) {
            this.deviceUtils = params.deviceUtils;
        }
        if (params.apiService !== undefined) {
            this.apiService = params.apiService;
        }
    }
    updateStateVars(params: FavoritesPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__favoriteApps.purgeDependencyOnElmtId(rmElmtId);
        this.__loadingState.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedCategory.purgeDependencyOnElmtId(rmElmtId);
        this.__categories.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__favoriteApps.aboutToBeDeleted();
        this.__loadingState.aboutToBeDeleted();
        this.__selectedCategory.aboutToBeDeleted();
        this.__categories.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __favoriteApps: ObservedPropertyObjectPU<AppModel[]>;
    get favoriteApps() {
        return this.__favoriteApps.get();
    }
    set favoriteApps(newValue: AppModel[]) {
        this.__favoriteApps.set(newValue);
    }
    private __loadingState: ObservedPropertySimplePU<LoadingState>;
    get loadingState() {
        return this.__loadingState.get();
    }
    set loadingState(newValue: LoadingState) {
        this.__loadingState.set(newValue);
    }
    private __selectedCategory: ObservedPropertySimplePU<string>;
    get selectedCategory() {
        return this.__selectedCategory.get();
    }
    set selectedCategory(newValue: string) {
        this.__selectedCategory.set(newValue);
    }
    private __categories: ObservedPropertyObjectPU<string[]>;
    get categories() {
        return this.__categories.get();
    }
    set categories(newValue: string[]) {
        this.__categories.set(newValue);
    }
    private deviceUtils;
    private apiService;
    aboutToAppear() {
        this.loadFavoriteApps();
    }
    /**
     * 加载收藏的应用
     */
    private async loadFavoriteApps() {
        try {
            this.loadingState = LoadingState.LOADING;
            // 这里应该调用API获取用户收藏的应用
            // 暂时使用模拟数据
            await this.simulateApiCall();
            this.favoriteApps = this.getMockFavoriteApps();
            this.loadingState = LoadingState.SUCCESS;
        }
        catch (error) {
            hilog.error(0x0000, 'FavoritesPage', '加载收藏应用失败: %{public}s', JSON.stringify(error));
            this.loadingState = LoadingState.ERROR;
        }
    }
    /**
     * 模拟API调用
     */
    private async simulateApiCall(): Promise<void> {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve();
            }, 1000);
        });
    }
    /**
     * 获取模拟收藏应用数据
     */
    private getMockFavoriteApps(): AppModel[] {
        return [
            {
                id: 1,
                created_at: '2024-01-12T00:00:00Z',
                updated_at: '2024-01-12T00:00:00Z',
                name: '王者荣耀',
                package_name: 'com.tencent.tmgp.sgame',
                description: '5v5英雄公平对战手游',
                short_description: 'MOBA竞技游戏',
                icon: Constants.PLACEHOLDER_IMAGE,
                category_id: 3,
                category_name: '游戏',
                developer_id: 3,
                developer_name: '腾讯游戏',
                version: '********',
                version_code: 384018,
                min_sdk_version: 9,
                target_sdk_version: 12,
                size: 1887436800,
                download_url: '',
                download_count: 500000000,
                rating: 4.5,
                review_count: 100000,
                screenshots: [Constants.PLACEHOLDER_IMAGE],
                permissions: [],
                tags: ['MOBA', '竞技'],
                changelog: '新增英雄和皮肤',
                privacy_policy: '',
                support_email: '<EMAIL>',
                website: 'https://pvp.qq.com',
                status: 'published',
                is_featured: true,
                is_editor_choice: true,
                is_top: false,
                published_at: '2024-01-12T00:00:00Z',
                review_status: 'approved',
                review_reason: '',
                reviewed_at: '2024-01-12T00:00:00Z',
                reviewer_id: 1
            },
            {
                id: 2,
                created_at: '2024-01-08T00:00:00Z',
                updated_at: '2024-01-08T00:00:00Z',
                name: '抖音',
                package_name: 'com.ss.android.ugc.aweme',
                description: '记录美好生活',
                short_description: '短视频社交平台',
                icon: Constants.PLACEHOLDER_IMAGE,
                category_id: 4,
                category_name: '娱乐',
                developer_id: 4,
                developer_name: '北京微播视界科技有限公司',
                version: '28.7.0',
                version_code: 287000,
                min_sdk_version: 9,
                target_sdk_version: 12,
                size: 234881024,
                download_url: '',
                download_count: 800000000,
                rating: 4.6,
                review_count: 200000,
                screenshots: [Constants.PLACEHOLDER_IMAGE],
                permissions: [],
                tags: ['短视频', '娱乐'],
                changelog: '优化视频播放体验',
                privacy_policy: '',
                support_email: '<EMAIL>',
                website: 'https://www.douyin.com',
                status: 'published',
                is_featured: true,
                is_editor_choice: false,
                is_top: true,
                published_at: '2024-01-08T00:00:00Z',
                review_status: 'approved',
                review_reason: '',
                reviewed_at: '2024-01-08T00:00:00Z',
                reviewer_id: 1
            },
            {
                id: 3,
                created_at: '2024-01-05T00:00:00Z',
                updated_at: '2024-01-05T00:00:00Z',
                name: '学习强国',
                package_name: 'cn.xuexi.android',
                description: '学而时习之，不亦说乎',
                short_description: '学习教育平台',
                icon: Constants.PLACEHOLDER_IMAGE,
                category_id: 5,
                category_name: '教育',
                developer_id: 5,
                developer_name: '中央宣传部思想政治工作研究所',
                version: '2.45.0',
                version_code: 245000,
                min_sdk_version: 9,
                target_sdk_version: 12,
                size: 156672000,
                download_url: '',
                download_count: 200000000,
                rating: 4.8,
                review_count: 80000,
                screenshots: [Constants.PLACEHOLDER_IMAGE],
                permissions: [],
                tags: ['学习', '新闻'],
                changelog: '新增学习内容',
                privacy_policy: '',
                support_email: '<EMAIL>',
                website: 'https://www.xuexi.cn',
                status: 'published',
                is_featured: false,
                is_editor_choice: true,
                is_top: false,
                published_at: '2024-01-05T00:00:00Z',
                review_status: 'approved',
                review_reason: '',
                reviewed_at: '2024-01-05T00:00:00Z',
                reviewer_id: 1
            }
        ];
    }
    /**
     * 获取过滤后的应用列表
     */
    private getFilteredApps(): AppModel[] {
        if (this.selectedCategory === '全部') {
            return this.favoriteApps;
        }
        return this.favoriteApps.filter(app => app.tags && app.tags.includes(this.selectedCategory));
    }
    /**
     * 跳转到应用详情页面
     */
    private navigateToAppDetail(app: AppModel) {
        this.getUIContext().getRouter().pushUrl({
            url: 'pages/AppDetailPage',
            params: { appId: app.id.toString() }
        });
    }
    /**
     * 取消收藏
     */
    private async removeFavorite(app: AppModel) {
        try {
            // 这里应该调用API取消收藏
            // 暂时直接从列表中移除
            const index = this.favoriteApps.findIndex(item => item.id === app.id);
            if (index !== -1) {
                this.favoriteApps.splice(index, 1);
            }
        }
        catch (error) {
            hilog.error(0x0000, 'FavoritesPage', '取消收藏失败: %{public}s', JSON.stringify(error));
        }
    }
    /**
     * 分类标签栏
     */
    private CategoryTabs(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.scrollable(ScrollDirection.Horizontal);
            Scroll.scrollBar(BarState.Off);
            Scroll.width('100%');
            Scroll.backgroundColor(Constants.COLORS.WHITE);
            Scroll.padding({ top: 12, bottom: 12 });
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 8 });
            Row.padding({ left: 16, right: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const category = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(category);
                    Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                    Text.fontColor(this.selectedCategory === category ? Constants.COLORS.WHITE : Constants.COLORS.TEXT_SECONDARY);
                    Text.backgroundColor(this.selectedCategory === category ? Constants.COLORS.PRIMARY : { "id": 16777231, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                    Text.padding({ left: 12, right: 12, top: 6, bottom: 6 });
                    Text.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
                    Text.onClick(() => {
                        this.selectedCategory = category;
                    });
                }, Text);
                Text.pop();
            };
            this.forEachUpdateFunction(elmtId, this.categories, forEachItemGenFunction, (category: string) => category, false, false);
        }, ForEach);
        ForEach.pop();
        Row.pop();
        Scroll.pop();
    }
    /**
     * 应用项
     */
    private AppItem(app: AppModel, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) });
            Row.width('100%');
            Row.padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM));
            Row.backgroundColor(Constants.COLORS.WHITE);
            Row.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            Row.margin({ left: 16, right: 16, bottom: 8 });
            Row.onClick(() => this.navigateToAppDetail(app));
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 应用图标
            Image.create(app.icon);
            // 应用图标
            Image.width(this.deviceUtils.isTablet() ? 64 : 56);
            // 应用图标
            Image.height(this.deviceUtils.isTablet() ? 64 : 56);
            // 应用图标
            Image.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            // 应用图标
            Image.objectFit(ImageFit.Cover);
            // 应用图标
            Image.backgroundColor(Constants.COLORS.BACKGROUND_LIGHT);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 应用信息
            Column.create({ space: 4 });
            // 应用信息
            Column.alignItems(HorizontalAlign.Start);
            // 应用信息
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(app.name);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.fontWeight(FontWeight.Medium);
            Text.maxLines(1);
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(app.tags && app.tags.length > 0 ? app.tags[0] : '应用');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.PRIMARY);
            Text.backgroundColor({ "id": 16777232, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Text.padding({ left: 8, right: 8, top: 2, bottom: 2 });
            Text.borderRadius(Constants.BORDER_RADIUS.SMALL);
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(app.description);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
            Text.maxLines(1);
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 4 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('⭐');
            Text.fontSize(12);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create((app.rating || 0).toString());
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_HINT);
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`${((app.size || 0) / 1024 / 1024).toFixed(1)}MB`);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_HINT);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`更新时间: ${app.updated_at.split(' ')[0]}`);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_HINT);
        }, Text);
        Text.pop();
        Row.pop();
        // 应用信息
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 取消收藏按钮
            Text.create('❤️');
            // 取消收藏按钮
            Text.fontSize(20);
            // 取消收藏按钮
            Text.fontColor(Constants.COLORS.ERROR);
            // 取消收藏按钮
            Text.padding(8);
            // 取消收藏按钮
            Text.onClick(() => {
                this.removeFavorite(app);
            });
        }, Text);
        // 取消收藏按钮
        Text.pop();
        Row.pop();
    }
    /**
     * 应用列表
     */
    private AppList(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.getFilteredApps().length === 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create({ space: 16 });
                        Column.width('100%');
                        Column.height(200);
                        Column.justifyContent(FlexAlign.Center);
                        Column.alignItems(HorizontalAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('❤️');
                        Text.fontSize(48);
                        Text.fontColor(Constants.COLORS.TEXT_HINT);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.selectedCategory === '全部' ? '暂无收藏应用' : `暂无${this.selectedCategory}类收藏`);
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('去应用商店发现更多精彩应用');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                        Text.fontColor(Constants.COLORS.TEXT_HINT);
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.width('100%');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const app = _item;
                            this.AppItem.bind(this)(app);
                        };
                        this.forEachUpdateFunction(elmtId, this.getFilteredApps(), forEachItemGenFunction, (app: AppModel) => app.id.toString(), false, false);
                    }, ForEach);
                    ForEach.pop();
                    Column.pop();
                });
            }
        }, If);
        If.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor(Constants.COLORS.BACKGROUND);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 标题栏
            Row.create();
            // 标题栏
            Row.width('100%');
            // 标题栏
            Row.height(56);
            // 标题栏
            Row.padding({ left: 16, right: 16 });
            // 标题栏
            Row.backgroundColor(Constants.COLORS.WHITE);
            // 标题栏
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('←');
            Text.fontSize(24);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.onClick(() => {
                this.getUIContext().getRouter().back();
            });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('我的收藏');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.fontWeight(FontWeight.Bold);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.width(24);
        }, Text);
        Text.pop();
        // 标题栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.loadingState === LoadingState.LOADING) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, { state: LoadingState.LOADING }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/FavoritesPage.ets", line: 383, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.LOADING
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.LOADING
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else if (this.loadingState === LoadingState.ERROR) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, {
                                    state: LoadingState.ERROR,
                                    onRetry: (): Promise<void> => this.loadFavoriteApps()
                                }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/FavoritesPage.ets", line: 386, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.ERROR,
                                        onRetry: (): Promise<void> => this.loadFavoriteApps()
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.ERROR
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.layoutWeight(1);
                    }, Column);
                    // 分类标签栏
                    this.CategoryTabs.bind(this)();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Divider.create();
                        Divider.color(Constants.COLORS.BORDER);
                    }, Divider);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 应用列表
                        Scroll.create();
                        // 应用列表
                        Scroll.scrollable(ScrollDirection.Vertical);
                        // 应用列表
                        Scroll.scrollBar(BarState.Auto);
                        // 应用列表
                        Scroll.layoutWeight(1);
                    }, Scroll);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                    }, Column);
                    this.AppList.bind(this)();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 底部间距
                        Column.create();
                        // 底部间距
                        Column.height(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE));
                    }, Column);
                    // 底部间距
                    Column.pop();
                    Column.pop();
                    // 应用列表
                    Scroll.pop();
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "FavoritesPage";
    }
}
export { FavoritesPage };
registerNamedRoute(() => new FavoritesPage(undefined, {}), "", { bundleName: "com.example.nexushub", moduleName: "entry", pagePath: "pages/FavoritesPage", pageFullPath: "entry/src/main/ets/pages/FavoritesPage", integratedHsp: "false", moduleType: "followWithHap" });
