import { PageContainer } from '@ant-design/pro-components';
import { Card, Table, Button, Space, Tag, message, Modal, Form, Input, Select } from 'antd';
import { CheckOutlined, CloseOutlined, EyeOutlined, ReloadOutlined } from '@ant-design/icons';
import React, { useState, useEffect } from 'react';
import { useRequest } from '@umijs/max';
import type { ColumnsType } from 'antd/es/table';
import { getPendingVersions, reviewAppVersion } from '@/services/version';
import type { AppVersion, VersionReviewRequest } from '@/services/version';

const { TextArea } = Input;
const { Option } = Select;

const VersionReview: React.FC = () => {
  const [searchParams, setSearchParams] = useState<any>({});
  const [reviewModalVisible, setReviewModalVisible] = useState(false);
  const [currentVersion, setCurrentVersion] = useState<AppVersion | null>(null);
  const [form] = Form.useForm();

  // 获取待审核版本列表
  const { data, loading, run } = useRequest(
    () => getPendingVersions({
      page: searchParams.page || 1,
      pageSize: searchParams.pageSize || 20,
    }),
    {
      refreshDeps: [searchParams],
    }
  );

  // 处理审核操作
  const handleReview = async (version: AppVersion, action: 'approve' | 'reject') => {
    if (action === 'reject') {
      setCurrentVersion(version);
      setReviewModalVisible(true);
      form.setFieldsValue({ action: 'reject' });
    } else {
      try {
        const reviewData: VersionReviewRequest = {
          status: 'approved',
          reason: '版本审核通过',
        };
        
        await reviewAppVersion(version.id, reviewData);
        message.success('版本审核通过');
        run(); // 刷新列表
      } catch (error) {
        console.error('审核失败:', error);
        message.error('审核失败，请稍后重试');
      }
    }
  };

  // 提交审核表单
  const handleReviewSubmit = async (values: any) => {
    if (!currentVersion) return;
    
    try {
      const reviewData: VersionReviewRequest = {
        status: values.action === 'approve' ? 'approved' : 'rejected',
        reason: values.comment || (values.action === 'approve' ? '版本审核通过' : '版本审核拒绝'),
      };
      
      await reviewAppVersion(currentVersion.id, reviewData);
      message.success(values.action === 'approve' ? '版本审核通过' : '版本已拒绝');
      setReviewModalVisible(false);
      setCurrentVersion(null);
      form.resetFields();
      run(); // 刷新列表
    } catch (error) {
      console.error('审核失败:', error);
      message.error('审核失败，请稍后重试');
    }
  };

  // 查看版本详情
  const handleViewDetail = (version: AppVersion) => {
    Modal.info({
      title: '版本详情',
      width: 600,
      content: (
        <div>
          <p><strong>应用名称:</strong> {version.appName}</p>
          <p><strong>版本号:</strong> {version.versionName}</p>
          <p><strong>版本代码:</strong> {version.versionCode}</p>
          <p><strong>开发者:</strong> {version.developerName}</p>
          <p><strong>文件大小:</strong> {version.fileSize ? `${(version.fileSize / 1024 / 1024).toFixed(2)} MB` : '未知'}</p>
          <p><strong>更新说明:</strong></p>
          <div style={{ background: '#f5f5f5', padding: '8px', borderRadius: '4px', maxHeight: '200px', overflow: 'auto' }}>
            {version.updateDescription || '无更新说明'}
          </div>
          <p><strong>提交时间:</strong> {(version.createdAt || version.created_at) ? new Date(version.createdAt || version.created_at).toLocaleString() : '-'}</p>
        </div>
      ),
    });
  };

  const columns: ColumnsType<AppVersion> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '应用名称',
      dataIndex: 'appName',
      key: 'appName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '版本号',
      dataIndex: 'versionName',
      key: 'versionName',
      width: 100,
    },
    {
      title: '版本代码',
      dataIndex: 'versionCode',
      key: 'versionCode',
      width: 100,
    },
    {
      title: '开发者',
      dataIndex: 'developerName',
      key: 'developerName',
      width: 120,
      ellipsis: true,
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
      width: 100,
      render: (size: number) => size ? `${(size / 1024 / 1024).toFixed(2)} MB` : '未知',
    },
    {
      title: '提交时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (time: string, record: AppVersion) => {
        const timeValue = time || record.created_at;
        return timeValue ? new Date(timeValue).toLocaleString() : '-';
      },
      sorter: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        let color = 'orange';
        let text = '待审核';
        
        if (status === 'approved') {
          color = 'green';
          text = '已通过';
        } else if (status === 'rejected') {
          color = 'red';
          text = '已拒绝';
        } else if (status === 'published') {
          color = 'blue';
          text = '已发布';
        }
        
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="link" 
            icon={<EyeOutlined />} 
            size="small"
            onClick={() => handleViewDetail(record)}
          >
            查看
          </Button>
          {record.status === 'pending' && (
            <>
              <Button 
                type="primary" 
                icon={<CheckOutlined />} 
                size="small"
                onClick={() => handleReview(record, 'approve')}
              >
                通过
              </Button>
              <Button 
                danger 
                icon={<CloseOutlined />} 
                size="small"
                onClick={() => handleReview(record, 'reject')}
              >
                拒绝
              </Button>
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <PageContainer>
      <Card bordered={false}>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <span>版本审核管理</span>
            <Tag color="orange" style={{ marginLeft: 8 }}>待审核版本: {data?.total || 0}</Tag>
          </div>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={() => run()}
          >
            刷新
          </Button>
        </div>
        
        <Table 
          columns={columns} 
          dataSource={data?.data} 
          rowKey="id" 
          loading={loading}
          scroll={{ x: 1200 }}
          pagination={{
            current: data?.page || 1,
            pageSize: data?.pageSize || 20,
            total: data?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => {
              setSearchParams({
                ...searchParams,
                page,
                pageSize,
              });
            },
          }}
        />
      </Card>

      {/* 审核模态框 */}
      <Modal
        title="版本审核"
        open={reviewModalVisible}
        onCancel={() => {
          setReviewModalVisible(false);
          setCurrentVersion(null);
          form.resetFields();
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleReviewSubmit}
        >
          <Form.Item
            name="action"
            label="审核操作"
            rules={[{ required: true, message: '请选择审核操作' }]}
          >
            <Select placeholder="请选择审核操作">
              <Option value="approve">通过</Option>
              <Option value="reject">拒绝</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="comment"
            label="审核意见"
            rules={[{ required: true, message: '请输入审核意见' }]}
          >
            <TextArea 
              rows={4} 
              placeholder="请输入审核意见..." 
            />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                提交审核
              </Button>
              <Button onClick={() => {
                setReviewModalVisible(false);
                setCurrentVersion(null);
                form.resetFields();
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default VersionReview;