<!--
 * @Author: xiaoxiaozhou <EMAIL>
 * @Date: 2025-06-18 21:57:32
 * @LastEditors: xiaoxiaozhou <EMAIL>
 * @LastEditTime: 2025-06-18 21:57:41
 * @FilePath: \NexusHub-OH\NexusHub-OH-Back\error.md
 * @Description: 
 * 技术生的工作日常
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
PS C:\Users\<USER>\Documents\NexusHub-OH\NexusHub-OH-Back> go run main.go
# nexushub-oh-back/internal/api
internal\api\app_controller.go:3044:31: undefined: models.VersionStatusPending
internal\api\app_controller.go:3196:10: undefined: UpdateAppStatusRequest
internal\api\app_controller.go:3235:33: undefined: models.AppStatusApproved
internal\api\app_controller.go:3269:10: undefined: SetFeaturedRequest
internal\api\category_controller.go:231:44: c.DB undefined (type *CategoryController has no field or method DB, but does have field db)
internal\api\category_controller.go:332:44: c.DB undefined (type *CategoryController has no field or method DB, but does have field db)
internal\api\developer_controller.go:649:23: undefined: models.DeveloperVerification
internal\api\developer_controller.go:724:23: undefined: models.DeveloperVerification
internal\api\developer_controller.go:816:23: undefined: models.DeveloperVerification
internal\api\featured_collection_controller.go:114:68: fc.DB undefined (type *FeaturedCollectionController has no field or method DB, but does have field db)        
internal\api\featured_collection_controller.go:114:68: too many errors