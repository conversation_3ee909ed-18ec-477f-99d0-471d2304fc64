import { PageContainer } from '@ant-design/pro-components';
import { Card, Table, Button, Tag, Space, Input, Select, DatePicker, Modal, Form, Radio, message } from 'antd';
import { SearchOutlined, ReloadOutlined, EyeOutlined, CheckOutlined, StopOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { useRequest } from '@umijs/max';
import type { ColumnsType } from 'antd/es/table';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;

interface ReviewItem {
  id: string;
  appId: string;
  appName: string;
  userId: string;
  username: string;
  content: string;
  rating: number;
  status: 'pending';
  createdAt: string;
  reportCount: number;
  sensitiveWords: string[];
}

// 模拟数据获取函数
const fetchPendingReviews = async (params: any) => {
  console.log('Fetching pending reviews with params:', params);
  
  // 模拟数据
  const mockData: ReviewItem[] = [
    {
      id: '1',
      appId: 'app001',
      appName: '微信',
      userId: 'user001',
      username: 'user12345',
      content: '这个应用太垃圾了，根本不好用，开发者是傻子吗？',
      rating: 1,
      status: 'pending',
      createdAt: '2023-12-15 14:30:25',
      reportCount: 3,
      sensitiveWords: ['垃圾', '傻子'],
    },
    {
      id: '2',
      appId: 'app002',
      appName: '支付宝',
      userId: 'user002',
      username: 'techfan',
      content: '这个应用有安全漏洞，我的账号被盗了，客服也不处理，太差劲了！',
      rating: 1,
      status: 'pending',
      createdAt: '2023-12-15 13:15:30',
      reportCount: 2,
      sensitiveWords: ['安全漏洞', '被盗'],
    },
    {
      id: '3',
      appId: 'app003',
      appName: '抖音',
      userId: 'user003',
      username: 'videomaker',
      content: '这个应用充斥着低俗内容，对青少年有害，应该被封禁！',
      rating: 2,
      status: 'pending',
      createdAt: '2023-12-15 12:45:12',
      reportCount: 5,
      sensitiveWords: ['低俗', '封禁'],
    },
    {
      id: '4',
      appId: 'app004',
      appName: '淘宝',
      userId: 'user004',
      username: 'shopper',
      content: '客服态度极差，商品质量有问题，退款困难，不推荐使用！',
      rating: 2,
      status: 'pending',
      createdAt: '2023-12-15 11:20:45',
      reportCount: 1,
      sensitiveWords: [],
    },
    {
      id: '5',
      appId: 'app005',
      appName: '网易云音乐',
      userId: 'user005',
      username: 'musiclover',
      content: '这个应用的会员太贵了，而且歌曲质量参差不齐，不值这个价！',
      rating: 2,
      status: 'pending',
      createdAt: '2023-12-15 10:30:18',
      reportCount: 0,
      sensitiveWords: [],
    },
    {
      id: '6',
      appId: 'app001',
      appName: '微信',
      userId: 'user006',
      username: 'chatmaster',
      content: '这个应用经常崩溃，开发者是不是不会写代码？垃圾软件！',
      rating: 1,
      status: 'pending',
      createdAt: '2023-12-15 09:40:22',
      reportCount: 2,
      sensitiveWords: ['垃圾'],
    },
    {
      id: '7',
      appId: 'app006',
      appName: 'QQ',
      userId: 'user007',
      username: 'gamer2023',
      content: '这个应用的游戏中心全是坑钱的游戏，太黑心了！',
      rating: 2,
      status: 'pending',
      createdAt: '2023-12-15 08:25:36',
      reportCount: 1,
      sensitiveWords: ['坑钱', '黑心'],
    },
    {
      id: '8',
      appId: 'app007',
      appName: '美团',
      userId: 'user008',
      username: 'foodie',
      content: '送餐员态度恶劣，投诉无门，平台包庇商家，太差劲了！',
      rating: 1,
      status: 'pending',
      createdAt: '2023-12-15 07:50:15',
      reportCount: 3,
      sensitiveWords: ['恶劣', '包庇'],
    },
    {
      id: '9',
      appId: 'app008',
      appName: '知乎',
      userId: 'user009',
      username: 'thinker',
      content: '内容审核不严，充斥着虚假信息，误导用户，应该被处罚！',
      rating: 2,
      status: 'pending',
      createdAt: '2023-12-15 06:15:33',
      reportCount: 4,
      sensitiveWords: ['虚假', '处罚'],
    },
    {
      id: '10',
      appId: 'app009',
      appName: 'Bilibili',
      userId: 'user010',
      username: 'animefan',
      content: '这个应用的广告太多了，而且很多视频内容低俗，不适合年轻人！',
      rating: 2,
      status: 'pending',
      createdAt: '2023-12-15 05:30:27',
      reportCount: 2,
      sensitiveWords: ['低俗'],
    },
  ];

  // 根据应用名称过滤
  const appFilteredData = params.appName ? mockData.filter(item => item.appName.includes(params.appName)) : mockData;
  
  // 根据评分过滤
  const ratingFilteredData = params.rating ? appFilteredData.filter(item => item.rating === params.rating) : appFilteredData;
  
  // 根据关键词过滤
  const keywordFilteredData = params.keyword
    ? ratingFilteredData.filter(item => 
        item.content.includes(params.keyword) ||
        item.username.includes(params.keyword)
      )
    : ratingFilteredData;

  // 根据举报次数过滤
  const reportFilteredData = params.minReportCount
    ? keywordFilteredData.filter(item => item.reportCount >= params.minReportCount)
    : keywordFilteredData;

  return { data: reportFilteredData, total: reportFilteredData.length };
};

const ReviewAudit: React.FC = () => {
  const [searchParams, setSearchParams] = useState({});
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [currentReview, setCurrentReview] = useState<ReviewItem | null>(null);
  const [rejectModalVisible, setRejectModalVisible] = useState(false);
  const [rejectForm] = Form.useForm();

  const { data, loading, refresh } = useRequest(() => fetchPendingReviews(searchParams), {
    refreshDeps: [searchParams],
  });

  const handleSearch = (values: any) => {
    setSearchParams(values);
  };

  const handleViewDetail = (record: ReviewItem) => {
    setCurrentReview(record);
    setDetailModalVisible(true);
  };

  const handleApprove = (id: string) => {
    message.success(`已通过评论 ID: ${id}`);
    refresh();
  };

  const handleReject = (id: string) => {
    setCurrentReview(data?.data.find(item => item.id === id) || null);
    setRejectModalVisible(true);
  };

  const handleRejectSubmit = () => {
    rejectForm.validateFields().then(values => {
      message.success(`已拒绝评论 ID: ${currentReview?.id}，原因: ${values.reason}`);
      setRejectModalVisible(false);
      rejectForm.resetFields();
      refresh();
    });
  };

  const handleBatchApprove = () => {
    message.success(`已批量通过 ${selectedRowKeys.length} 条评论`);
    setSelectedRowKeys([]);
    refresh();
  };

  const handleBatchReject = () => {
    message.success(`已批量拒绝 ${selectedRowKeys.length} 条评论`);
    setSelectedRowKeys([]);
    refresh();
  };

  const columns: ColumnsType<ReviewItem> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '应用名称',
      dataIndex: 'appName',
      key: 'appName',
      width: 120,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      width: 120,
    },
    {
      title: '评分',
      dataIndex: 'rating',
      key: 'rating',
      width: 80,
      render: (rating: number) => {
        return (
          <Tag color={rating <= 2 ? 'red' : 'green'}>
            {rating} 星
          </Tag>
        );
      },
    },
    {
      title: '评论内容',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
      width: 300,
      render: (content: string, record: ReviewItem) => {
        if (record.sensitiveWords.length > 0) {
          let highlightedContent = content;
          record.sensitiveWords.forEach(word => {
            highlightedContent = highlightedContent.replace(
              new RegExp(word, 'g'),
              `<span style="color: red; font-weight: bold;">${word}</span>`
            );
          });
          return <div dangerouslySetInnerHTML={{ __html: highlightedContent }} />;
        }
        return content;
      },
    },
    {
      title: '举报次数',
      dataIndex: 'reportCount',
      key: 'reportCount',
      width: 100,
      render: (count: number) => {
        let color = 'green';
        if (count > 3) {
          color = 'red';
        } else if (count > 0) {
          color = 'orange';
        }
        return <Tag color={color}>{count}</Tag>;
      },
    },
    {
      title: '敏感词',
      dataIndex: 'sensitiveWords',
      key: 'sensitiveWords',
      width: 150,
      render: (words: string[]) => (
        <>
          {words.map(word => (
            <Tag color="red" key={word}>
              {word}
            </Tag>
          ))}
        </>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="text" 
            icon={<EyeOutlined />} 
            onClick={() => handleViewDetail(record)}
          >
            查看
          </Button>
          <Button 
            type="text" 
            icon={<CheckOutlined />} 
            style={{ color: 'green' }}
            onClick={() => handleApprove(record.id)}
          >
            通过
          </Button>
          <Button 
            type="text" 
            icon={<StopOutlined />} 
            style={{ color: 'red' }}
            onClick={() => handleReject(record.id)}
          >
            拒绝
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer
      header={{
        title: '评论审核',
        subTitle: '审核待处理的用户评论',
      }}
    >
      <Card>
        <Form layout="inline" onFinish={handleSearch} style={{ marginBottom: 24 }}>
          <Form.Item name="keyword" label="关键词">
            <Input placeholder="评论内容/用户名" prefix={<SearchOutlined />} />
          </Form.Item>
          <Form.Item name="appName" label="应用名称">
            <Input placeholder="应用名称" />
          </Form.Item>
          <Form.Item name="rating" label="评分">
            <Select style={{ width: 120 }} placeholder="全部评分" allowClear>
              <Option value={1}>1星</Option>
              <Option value={2}>2星</Option>
            </Select>
          </Form.Item>
          <Form.Item name="minReportCount" label="最小举报次数">
            <Select style={{ width: 120 }} placeholder="全部" allowClear>
              <Option value={1}>≥ 1</Option>
              <Option value={3}>≥ 3</Option>
              <Option value={5}>≥ 5</Option>
            </Select>
          </Form.Item>
          <Form.Item name="dateRange" label="时间范围">
            <RangePicker />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit">
              搜索
            </Button>
          </Form.Item>
          <Form.Item>
            <Button onClick={() => setSearchParams({})}>
              重置
            </Button>
          </Form.Item>
        </Form>

        <div style={{ marginBottom: 16 }}>
          <Button 
            type="primary"
            style={{ marginRight: 8 }}
            disabled={selectedRowKeys.length === 0}
            onClick={handleBatchApprove}
          >
            批量通过
          </Button>
          <Button 
            danger
            disabled={selectedRowKeys.length === 0}
            onClick={handleBatchReject}
          >
            批量拒绝
          </Button>
          <Button 
            icon={<ReloadOutlined />} 
            style={{ marginLeft: 8 }}
            onClick={refresh}
          >
            刷新
          </Button>
          <span style={{ marginLeft: 8 }}>
            {selectedRowKeys.length > 0 ? `已选择 ${selectedRowKeys.length} 项` : ''}
          </span>
        </div>

        <Table
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
          columns={columns}
          dataSource={data?.data}
          rowKey="id"
          loading={loading}
          pagination={{
            total: data?.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
          }}
          scroll={{ x: 1500 }}
        />
      </Card>

      <Modal
        title="评论详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="approve" type="primary" onClick={() => {
            handleApprove(currentReview?.id || '');
            setDetailModalVisible(false);
          }}>
            通过
          </Button>,
          <Button key="reject" danger onClick={() => {
            setDetailModalVisible(false);
            handleReject(currentReview?.id || '');
          }}>
            拒绝
          </Button>,
          <Button key="cancel" onClick={() => setDetailModalVisible(false)}>
            取消
          </Button>,
        ]}
        width={600}
      >
        {currentReview && (
          <div>
            <p><strong>应用名称：</strong> {currentReview.appName}</p>
            <p><strong>用户名：</strong> {currentReview.username}</p>
            <p><strong>评分：</strong> {currentReview.rating} 星</p>
            <p><strong>评论内容：</strong></p>
            <p style={{ background: '#f5f5f5', padding: 12, borderRadius: 4 }}>
              {currentReview.content}
            </p>
            <p><strong>创建时间：</strong> {currentReview.createdAt}</p>
            <p><strong>举报次数：</strong> {currentReview.reportCount}</p>
            <p><strong>敏感词：</strong> 
              {currentReview.sensitiveWords.length > 0 ? (
                currentReview.sensitiveWords.map(word => (
                  <Tag color="red" key={word} style={{ marginRight: 4 }}>
                    {word}
                  </Tag>
                ))
              ) : '无'}
            </p>
          </div>
        )}
      </Modal>

      <Modal
        title="拒绝评论"
        open={rejectModalVisible}
        onOk={handleRejectSubmit}
        onCancel={() => setRejectModalVisible(false)}
        width={500}
      >
        <Form form={rejectForm} layout="vertical">
          <Form.Item
            name="reason"
            label="拒绝原因"
            rules={[{ required: true, message: '请选择拒绝原因' }]}
          >
            <Radio.Group>
              <Space direction="vertical">
                <Radio value="违反社区规范">违反社区规范</Radio>
                <Radio value="包含敏感词">包含敏感词</Radio>
                <Radio value="恶意攻击应用或开发者">恶意攻击应用或开发者</Radio>
                <Radio value="内容与应用无关">内容与应用无关</Radio>
                <Radio value="广告或垃圾内容">广告或垃圾内容</Radio>
                <Radio value="other">其他原因</Radio>
              </Space>
            </Radio.Group>
          </Form.Item>
          <Form.Item
            name="comment"
            label="备注"
            dependencies={['reason']}
            rules={[
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (getFieldValue('reason') === 'other' && !value) {
                    return Promise.reject(new Error('请填写其他原因'));
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <TextArea rows={4} placeholder="请输入备注信息" />
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default ReviewAudit;