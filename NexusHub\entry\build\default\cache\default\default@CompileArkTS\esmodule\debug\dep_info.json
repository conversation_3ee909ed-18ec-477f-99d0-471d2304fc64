{"resolveConflictMode": true, "depName2RootPath": {"@ohos/hypium": "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\oh_modules\\.ohpm\\@ohos+hypium@1.0.21\\oh_modules\\@ohos\\hypium", "@ohos/hamock": "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\oh_modules\\.ohpm\\@ohos+hamock@1.0.0\\oh_modules\\@ohos\\hamock"}, "depName2DepInfo": {"@ohos/hypium": {"pkgRootPath": "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\oh_modules\\.ohpm\\@ohos+hypium@1.0.21\\oh_modules\\@ohos\\hypium", "pkgName": "@ohos/hypium", "pkgVersion": "1.0.21"}, "@ohos/hamock": {"pkgRootPath": "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\oh_modules\\.ohpm\\@ohos+hamock@1.0.0\\oh_modules\\@ohos\\hamock", "pkgName": "@ohos/hamock", "pkgVersion": "1.0.0"}}}