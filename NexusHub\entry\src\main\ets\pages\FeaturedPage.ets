/**
 * 精选集页面
 * 展示精选集列表
 */
import { FeaturedCollectionModel, FeaturedCollectionListResponse } from '../models/FeaturedCollection';
import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';
import { ApiService } from '../services/ApiService';
import { LoadingView, LoadMoreView } from '../components/LoadingView';
import { router } from '@kit.ArkUI';
import { promptAction } from '@kit.ArkUI';
import { hilog } from '@kit.PerformanceAnalysisKit';

/**
 * 精选集页面组件
 */
@Component
export struct FeaturedPage {
  @State collections: FeaturedCollectionModel[] = [];
  @State isLoading: boolean = false;
  @State isLoadingMore: boolean = false;
  @State hasMore: boolean = true;
  @State currentPage: number = 1;
  @State pageSize: number = 20;
  @State errorMessage: string = '';
  @State showError: boolean = false;
  
  private apiService: ApiService = ApiService.getInstance();
  private deviceUtils: DeviceUtils = DeviceUtils.getInstance();
  
  /**
   * 页面即将出现时的回调
   */
  aboutToAppear() {
    this.loadFeaturedCollections();
  }
  
  /**
   * 加载精选集列表
   */
  private async loadFeaturedCollections() {
    if (this.isLoading) return;

    this.isLoading = true;
    this.showError = false;
    this.currentPage = 1;
    this.hasMore = true;

    try {
      hilog.info(0x0000, 'FeaturedPage', '开始加载精选集列表...');

      const response: FeaturedCollectionListResponse = await this.apiService.getFeaturedCollections(
        this.currentPage,
        this.pageSize,
        'active'
      );

      hilog.info(0x0000, 'FeaturedPage', '精选集列表响应: code=%{public}d, dataExists=%{public}s',
        response?.code || -1, response?.data ? 'true' : 'false');

      // 改进的数据验证逻辑 - 使用后端返回的list字段
      if (response && response.data && Array.isArray(response.data.list)) {
        this.collections = response.data.list;

        // 设置分页信息
        if (response.data.pagination) {
          this.hasMore = response.data.pagination.page < response.data.pagination.total_pages;
          hilog.info(0x0000, 'FeaturedPage', '分页信息: page=%{public}d, total_pages=%{public}d, hasMore=%{public}s',
            response.data.pagination.page, response.data.pagination.total_pages, this.hasMore ? 'true' : 'false');
        } else {
          this.hasMore = false;
        }

        // 根据数据长度设置状态
        if (this.collections.length > 0) {
          hilog.info(0x0000, 'FeaturedPage', '成功加载 %{public}d 个精选集', this.collections.length);
        } else {
          hilog.info(0x0000, 'FeaturedPage', '精选集数据为空');
        }
      } else {
        // 数据格式不正确或响应失败
        hilog.error(0x0000, 'FeaturedPage', '精选集数据格式错误或响应失败: code=%{public}d, message=%{public}s',
          response?.code || -1, response?.message || 'unknown');
        this.showError = true;
        this.errorMessage = response?.message || '数据加载失败，请重试';
      }
    } catch (error) {
      hilog.error(0x0000, 'FeaturedPage', '加载精选集失败: %{public}s', JSON.stringify(error));
      this.showError = true;
      this.errorMessage = '网络连接失败，请检查网络设置';
    } finally {
      this.isLoading = false;
    }
  }
  
  /**
   * 加载更多精选集
   */
  private async loadMoreCollections() {
    if (this.isLoadingMore || !this.hasMore) return;

    this.isLoadingMore = true;

    try {
      const nextPage = this.currentPage + 1;
      hilog.info(0x0000, 'FeaturedPage', '开始加载更多精选集，页码: %{public}d', nextPage);

      const response: FeaturedCollectionListResponse = await this.apiService.getFeaturedCollections(
        nextPage,
        this.pageSize,
        'active'
      );

      hilog.info(0x0000, 'FeaturedPage', '加载更多响应: code=%{public}d, dataExists=%{public}s',
        response?.code || -1, response?.data ? 'true' : 'false');

      if (response && response.data && Array.isArray(response.data.list)) {
        const newCollections: FeaturedCollectionModel[] = response.data.list;
        this.collections = this.collections.concat(newCollections);
        this.currentPage = nextPage;

        // 更新分页状态
        if (response.data.pagination) {
          this.hasMore = response.data.pagination.page < response.data.pagination.total_pages;
        } else {
          this.hasMore = false;
        }

        hilog.info(0x0000, 'FeaturedPage', '成功加载更多 %{public}d 个精选集，总数: %{public}d',
          newCollections.length, this.collections.length);
      } else {
        this.hasMore = false;
        hilog.error(0x0000, 'FeaturedPage', '加载更多数据格式错误: %{public}s', JSON.stringify(response));
        this.getUIContext().getPromptAction().showToast({
          message: response?.message || '加载失败',
          duration: 2000
        });
      }
    } catch (error) {
      hilog.error(0x0000, 'FeaturedPage', '加载更多精选集失败: %{public}s', JSON.stringify(error));
      this.hasMore = false;
      this.getUIContext().getPromptAction().showToast({
        message: '网络连接失败',
        duration: 2000
      });
    } finally {
      this.isLoadingMore = false;
    }
  }
  
  /**
   * 处理精选集卡片点击
   */
  private handleCollectionClick(collection: FeaturedCollectionModel) {
    this.getUIContext().getRouter().pushUrl({
      url: 'pages/FeaturedCollectionDetailPage',
      params: {
        collectionId: collection.id,
        collection: collection
      }
    }).catch((error: Error) => {
      hilog.error(0x0000, 'FeaturedPage', '导航到精选集详情页失败: %{public}s', JSON.stringify(error));
      this.getUIContext().getPromptAction().showToast({
        message: '页面跳转失败',
        duration: 2000
      });
    });
  }
  
  /**
   * 返回上一页
   */
  private goBack() {
    this.getUIContext().getRouter().back();
  }
  
  /**
   * 构建精选集卡片
   */
  @Builder
  private buildCollectionCard(collection: FeaturedCollectionModel) {
    Column() {
      // 封面图片
      Image(collection.cover_image || Constants.PLACEHOLDER_IMAGE)
        .width('100%')
        .height(120)
        .objectFit(ImageFit.Cover)
        .borderRadius({ topLeft: Constants.BORDER_RADIUS.MEDIUM, topRight: Constants.BORDER_RADIUS.MEDIUM })

      // 内容区域
      Column({ space: 8 }) {
        Text(collection.name)
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.MEDIUM))
          .fontWeight(FontWeight.Bold)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .maxLines(1)
          .textOverflow({ overflow: TextOverflow.Ellipsis })

        Text(collection.description)
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
          .maxLines(2)
          .textOverflow({ overflow: TextOverflow.Ellipsis })

        Row() {
          Text(`${collection.app_count}个应用`)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.TEXT_HINT)
        }
        .width('100%')
        .justifyContent(FlexAlign.SpaceBetween)
      }
      .alignItems(HorizontalAlign.Start)
      .padding(12)
    }
    .width('100%')
    .backgroundColor(Constants.COLORS.WHITE)
    .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
    .shadow({
      radius: 4,
      color: Constants.COLORS.SHADOW,
      offsetX: 0,
      offsetY: 2
    })
    .onClick(() => this.handleCollectionClick(collection))
  }
  
  /**
   * 构建页面UI
   */
  build() {
    Navigation() {
      Column() {
        

        
        // 内容区域
        if (this.isLoading) {
          LoadingView()
            .layoutWeight(1)
        } else if (this.showError) {
          Column() {
            Image($r('app.media.ic_error'))
              .width(64)
              .height(64)
              .fillColor($r('sys.color.ohos_id_color_text_secondary'))
              .margin({ bottom: 16 })
            
            Text(this.errorMessage)
              .fontSize(Constants.FONT_SIZE.NORMAL)
              .fontColor($r('sys.color.ohos_id_color_text_secondary'))
              .textAlign(TextAlign.Center)
              .margin({ bottom: 24 })
            
            Button('重试')
              .fontSize(Constants.FONT_SIZE.NORMAL)
              .fontColor($r('sys.color.ohos_id_color_primary'))
              .backgroundColor($r('sys.color.ohos_id_color_background'))
              .borderRadius(8)
              .padding({ left: 24, right: 24, top: 8, bottom: 8 })
              .onClick(() => this.loadFeaturedCollections())
          }
          .layoutWeight(1)
          .justifyContent(FlexAlign.Center)
          .alignItems(HorizontalAlign.Center)
        } else {
          // 精选集列表
          List() {
            ForEach(this.collections, (collection: FeaturedCollectionModel, index: number) => {
              ListItem() {
                this.buildCollectionCard(collection)
              }
              .margin({ left: 16, right: 16, bottom: 16 })
            }, (collection: FeaturedCollectionModel) => collection.id.toString())
            
            // 加载更多组件
            if (this.hasMore || this.isLoadingMore) {
              ListItem() {
                LoadMoreView({
                  isLoading: this.isLoadingMore,
                  hasMore: this.hasMore,
                  onLoadMore: () => {
                    if (!this.isLoadingMore && this.hasMore) {
                      this.loadMoreCollections();
                    }
                  }
                })
              }
              .padding({ top: 12, bottom: 12 })
            }
          }
          .layoutWeight(1)
          .scrollBar(BarState.Auto)
          .edgeEffect(EdgeEffect.Spring)
          .padding({ top: 16 })
          .onReachEnd(() => {
            if (this.hasMore && !this.isLoadingMore) {
              this.loadMoreCollections();
            }
          })
        }
      }
      .width('100%')
      .height('100%')
      .backgroundColor($r('sys.color.ohos_id_color_background'))
    }
    .title('精选集')
    .titleMode(NavigationTitleMode.Mini)
    .hideBackButton(true)
    .width('100%')
    .height('100%')
  }
}