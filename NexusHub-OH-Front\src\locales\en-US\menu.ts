/*
 * @Author: xiaoxiaozhou <EMAIL>
 * @Date: 2025-05-01 05:42:20
 * @LastEditors: xiaoxiaozhou <EMAIL>
 * @LastEditTime: 2025-06-12 03:20:33
 * @FilePath: \NexusHub-OH\NexusHub-OH-Front\src\locales\en-US\menu.ts
 * @Description: 
 * 技术生的工作日常
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
export default {
  'menu.welcome': 'Welcome',
  'menu.more-blocks': 'More Blocks',
  'menu.home': 'Home',
  'menu.admin': 'Admin',
  'menu.admin.sub-page': 'Sub-Page',
  'menu.login': 'Login',
  'menu.register': 'Register',
  'menu.register-result': 'Register Result',
  'menu.dashboard': 'Dashboard',
  'menu.dashboard.analysis': 'Analysis',
  'menu.dashboard.monitor': 'Monitor',
  'menu.dashboard.workplace': 'Workplace',
  
  'menu.audit': 'Audit Management',
  'menu.audit.app-audit': 'App Audit',
  'menu.audit.content-safety': 'Content Safety',
  'menu.audit.report-handling': 'Report Handling',
  'menu.audit.version-review': 'Version Review',
  
  'menu.statistics': 'Statistics',
  'menu.statistics.realtime-monitor': 'Realtime Monitor',
  'menu.statistics.report-generation': 'Report Generation',
  'menu.statistics.sales-statistics': 'Sales Statistics',
  'menu.statistics.app-statistics': 'App Statistics',
  'menu.statistics.user-statistics': 'User Statistics',
  'menu.statistics.developer-statistics': 'Developer Statistics',
  
  'menu.user-management': 'User Management',
  'menu.user-management.user-list': 'User List',
  'menu.user-management.user-detail': 'User Detail',


  
  'menu.content': 'Content Management',
  'menu.content.featured-content': 'Featured Content',
  'menu.content.activity-planning': 'Activity Planning',
  'menu.content.help-content': 'Help Content',
  
  'menu.review': 'Review Management',
  'menu.review.review-list': 'Review List',
  'menu.review.review-audit': 'Review Audit',
  
  'menu.app': 'App Management',
  'menu.app.app-list': 'App List',
  'menu.app.app-detail': 'App Detail',
  'menu.app-versions': 'App Versions',
  'menu.create-version': 'Create Version',
  
  'menu.settings': 'System Settings',
  'menu.settings.parameter-config': 'Parameter Config',
  'menu.settings.log-management': 'Log Management',
  'menu.settings.security-settings': 'Security Settings',
  'menu.settings.tag-management': 'Tag Management',
  'menu.settings.category-management': 'Category Management',
  'menu.settings.openharmony-versions': 'OpenHarmony Versions',
  
  'menu.permission': 'Permission Management',
  'menu.permission.role-management': 'Role Management',
  'menu.permission.permission-control': 'Permission Control',
  'menu.permission.operation-log': 'Operation Log',
  
  'menu.account': 'Account',

  'menu.account.settings': 'Account Settings',
  'menu.account.logout': 'Logout',
};
