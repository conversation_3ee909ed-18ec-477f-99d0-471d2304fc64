.dashboard {
  .statsRow {
    margin-bottom: 24px;
    
    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
      }
    }
    
    .ant-statistic {
      .ant-statistic-title {
        font-weight: 500;
        color: rgba(0, 0, 0, 0.65);
        margin-bottom: 8px;
      }
      
      .ant-statistic-content {
        font-size: 24px;
        font-weight: 600;
      }
    }
  }
  
  .chartCard {
    .ant-card-body {
      padding: 24px;
    }
    
    .approvalRate {
      margin-top: 24px;
      text-align: center;
      
      .ant-typography {
        margin-bottom: 16px;
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }
  
  .recentCard {
    .ant-list-item {
      padding: 16px 0;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .ant-list-item-meta {
        .ant-list-item-meta-title {
          margin-bottom: 4px;
          
          .ant-space {
            align-items: center;
          }
        }
        
        .ant-list-item-meta-description {
          color: rgba(0, 0, 0, 0.65);
          font-size: 12px;
          line-height: 1.4;
          
          div {
            margin-bottom: 2px;
            
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
      
      .ant-list-item-action {
        margin-left: 16px;
      }
    }
  }
  
  .quickActions {
    .ant-card-body {
      padding: 24px;
    }
    
    .ant-btn {
      height: 40px;
      border-radius: 6px;
      font-weight: 500;
      
      &.ant-btn-primary {
        background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
        border: none;
        
        &:hover {
          background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
      }
      
      &:not(.ant-btn-primary) {
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard {
    .statsRow {
      .ant-col {
        margin-bottom: 16px;
      }
    }
    
    .chartCard {
      .approvalRate {
        margin-top: 16px;
        
        .ant-progress {
          .ant-progress-circle {
            width: 80px !important;
            height: 80px !important;
          }
        }
      }
    }
    
    .quickActions {
      .ant-space {
        flex-direction: column;
        width: 100%;
        
        .ant-btn {
          width: 100%;
        }
      }
    }
  }
}

// 暗色主题适配
@media (prefers-color-scheme: dark) {
  .dashboard {
    .ant-card {
      background: #1f1f1f;
      border-color: #303030;
    }
    
    .statsRow {
      .ant-statistic {
        .ant-statistic-title {
          color: rgba(255, 255, 255, 0.65);
        }
        
        .ant-statistic-content {
          color: rgba(255, 255, 255, 0.85);
        }
      }
    }
    
    .recentCard {
      .ant-list-item {
        border-bottom-color: #303030;
        
        .ant-list-item-meta-description {
          color: rgba(255, 255, 255, 0.65);
        }
      }
    }
  }
}

// 动画效果
.dashboard {
  .ant-card {
    animation: fadeInUp 0.6s ease-out;
  }
  
  .statsRow {
    .ant-col:nth-child(1) .ant-card { animation-delay: 0.1s; }
    .ant-col:nth-child(2) .ant-card { animation-delay: 0.2s; }
    .ant-col:nth-child(3) .ant-card { animation-delay: 0.3s; }
    .ant-col:nth-child(4) .ant-card { animation-delay: 0.4s; }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}