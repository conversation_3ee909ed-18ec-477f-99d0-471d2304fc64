package main

import (
	"log"

	"nexushub-oh-back/config"
	"nexushub-oh-back/internal/models"
	"nexushub-oh-back/pkg/database"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 连接数据库
	db, err := database.NewPostgresDB(&cfg.Database)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	// 确保表已创建
	if err := db.DB.AutoMigrate(&models.GeographicData{}); err != nil {
		log.Fatalf("创建表失败: %v", err)
	}

	// 准备测试数据
	testData := []models.GeographicData{
		// 省份级别 (deep = 0)
		{ID: 11, ParentID: 0, Deep: 0, Name: "北京", Short: "b", Pinyin: "bei jing", Code: "110000000000", FullName: "北京市"},
		{ID: 12, ParentID: 0, Deep: 0, Name: "天津", Short: "t", Pinyin: "tian jin", Code: "120000000000", FullName: "天津市"},
		{ID: 13, ParentID: 0, Deep: 0, Name: "河北", Short: "h", Pinyin: "he bei", Code: "130000000000", FullName: "河北省"},
		{ID: 14, ParentID: 0, Deep: 0, Name: "山西", Short: "s", Pinyin: "shan xi", Code: "140000000000", FullName: "山西省"},
		{ID: 15, ParentID: 0, Deep: 0, Name: "内蒙古", Short: "n", Pinyin: "nei meng gu", Code: "150000000000", FullName: "内蒙古自治区"},

		// 城市级别 (deep = 1) - 直辖市下的城市
		{ID: 1101, ParentID: 11, Deep: 1, Name: "北京", Short: "b", Pinyin: "bei jing", Code: "110100000000", FullName: "北京市"},
		{ID: 1201, ParentID: 12, Deep: 1, Name: "天津", Short: "t", Pinyin: "tian jin", Code: "120100000000", FullName: "天津市"},

		// 区/镇级别 (deep = 2) - 北京市下的区
		{ID: 110101, ParentID: 1101, Deep: 2, Name: "东城", Short: "d", Pinyin: "dong cheng", Code: "110101000000", FullName: "东城区"},
		{ID: 110102, ParentID: 1101, Deep: 2, Name: "西城", Short: "x", Pinyin: "xi cheng", Code: "110102000000", FullName: "西城区"},
		{ID: 110105, ParentID: 1101, Deep: 2, Name: "朝阳", Short: "c", Pinyin: "chao yang", Code: "110105000000", FullName: "朝阳区"},
		{ID: 110106, ParentID: 1101, Deep: 2, Name: "丰台", Short: "f", Pinyin: "feng tai", Code: "110106000000", FullName: "丰台区"},

		// 区/镇级别 (deep = 2) - 天津市下的区
		{ID: 120101, ParentID: 1201, Deep: 2, Name: "和平", Short: "h", Pinyin: "he ping", Code: "120101000000", FullName: "和平区"},
		{ID: 120102, ParentID: 1201, Deep: 2, Name: "河东", Short: "h", Pinyin: "he dong", Code: "120102000000", FullName: "河东区"},
		{ID: 120103, ParentID: 1201, Deep: 2, Name: "河西", Short: "h", Pinyin: "he xi", Code: "120103000000", FullName: "河西区"},
		{ID: 120104, ParentID: 1201, Deep: 2, Name: "南开", Short: "n", Pinyin: "nan kai", Code: "120104000000", FullName: "南开区"},
		{ID: 120105, ParentID: 1201, Deep: 2, Name: "河北", Short: "h", Pinyin: "he bei", Code: "120105000000", FullName: "河北区"},
		{ID: 120106, ParentID: 1201, Deep: 2, Name: "红桥", Short: "h", Pinyin: "hong qiao", Code: "120106000000", FullName: "红桥区"},

		// 街道级别 (deep = 3) - 东城区下的街道
		{ID: 110101001, ParentID: 110101, Deep: 3, Name: "东华门", Short: "d", Pinyin: "dong hua men", Code: "110101001000", FullName: "东华门街道"},
		{ID: 110101002, ParentID: 110101, Deep: 3, Name: "景山", Short: "j", Pinyin: "jing shan", Code: "110101002000", FullName: "景山街道"},
		{ID: 110101003, ParentID: 110101, Deep: 3, Name: "交道口", Short: "j", Pinyin: "jiao dao kou", Code: "110101003000", FullName: "交道口街道"},
		{ID: 110101004, ParentID: 110101, Deep: 3, Name: "安定门", Short: "a", Pinyin: "an ding men", Code: "110101004000", FullName: "安定门街道"},

		// 街道级别 (deep = 3) - 天津和平区下的街道
		{ID: 120101001, ParentID: 120101, Deep: 3, Name: "劝业场", Short: "q", Pinyin: "quan ye chang", Code: "120101001000", FullName: "劝业场街道"},
		{ID: 120101002, ParentID: 120101, Deep: 3, Name: "小白楼", Short: "x", Pinyin: "xiao bai lou", Code: "120101002000", FullName: "小白楼街道"},
		{ID: 120101003, ParentID: 120101, Deep: 3, Name: "五大道", Short: "w", Pinyin: "wu da dao", Code: "120101003000", FullName: "五大道街道"},
		{ID: 120101004, ParentID: 120101, Deep: 3, Name: "新兴", Short: "x", Pinyin: "xin xing", Code: "120101004000", FullName: "新兴街道"},

		// 街道级别 (deep = 3) - 天津河东区下的街道
		{ID: 120102001, ParentID: 120102, Deep: 3, Name: "大王庄", Short: "d", Pinyin: "da wang zhuang", Code: "120102001000", FullName: "大王庄街道"},
		{ID: 120102002, ParentID: 120102, Deep: 3, Name: "大直沽", Short: "d", Pinyin: "da zhi gu", Code: "120102002000", FullName: "大直沽街道"},
		{ID: 120102003, ParentID: 120102, Deep: 3, Name: "中山门", Short: "z", Pinyin: "zhong shan men", Code: "120102003000", FullName: "中山门街道"},
		{ID: 120102004, ParentID: 120102, Deep: 3, Name: "富民路", Short: "f", Pinyin: "fu min lu", Code: "120102004000", FullName: "富民路街道"},

		// 街道级别 (deep = 3) - 天津河西区下的街道
		{ID: 120103001, ParentID: 120103, Deep: 3, Name: "尖山", Short: "j", Pinyin: "jian shan", Code: "120103001000", FullName: "尖山街道"},
		{ID: 120103002, ParentID: 120103, Deep: 3, Name: "友谊路", Short: "y", Pinyin: "you yi lu", Code: "120103002000", FullName: "友谊路街道"},
		{ID: 120103003, ParentID: 120103, Deep: 3, Name: "挂甲寺", Short: "g", Pinyin: "gua jia si", Code: "120103003000", FullName: "挂甲寺街道"},
		{ID: 120103004, ParentID: 120103, Deep: 3, Name: "马场", Short: "m", Pinyin: "ma chang", Code: "120103004000", FullName: "马场街道"},

		// 街道级别 (deep = 3) - 天津南开区下的街道
		{ID: 120104001, ParentID: 120104, Deep: 3, Name: "长虹", Short: "c", Pinyin: "chang hong", Code: "120104001000", FullName: "长虹街道"},
		{ID: 120104002, ParentID: 120104, Deep: 3, Name: "鼓楼", Short: "g", Pinyin: "gu lou", Code: "120104002000", FullName: "鼓楼街道"},
		{ID: 120104003, ParentID: 120104, Deep: 3, Name: "兴南", Short: "x", Pinyin: "xing nan", Code: "120104003000", FullName: "兴南街道"},
		{ID: 120104004, ParentID: 120104, Deep: 3, Name: "广开", Short: "g", Pinyin: "guang kai", Code: "120104004000", FullName: "广开街道"},

		// 街道级别 (deep = 3) - 天津河北区下的街道
		{ID: 120105001, ParentID: 120105, Deep: 3, Name: "光复道", Short: "g", Pinyin: "guang fu dao", Code: "120105001000", FullName: "光复道街道"},
		{ID: 120105002, ParentID: 120105, Deep: 3, Name: "望海楼", Short: "w", Pinyin: "wang hai lou", Code: "120105002000", FullName: "望海楼街道"},
		{ID: 120105003, ParentID: 120105, Deep: 3, Name: "鸿顺里", Short: "h", Pinyin: "hong shun li", Code: "120105003000", FullName: "鸿顺里街道"},
		{ID: 120105004, ParentID: 120105, Deep: 3, Name: "新开河", Short: "x", Pinyin: "xin kai he", Code: "120105004000", FullName: "新开河街道"},

		// 街道级别 (deep = 3) - 天津红桥区下的街道
		{ID: 120106001, ParentID: 120106, Deep: 3, Name: "西于庄", Short: "x", Pinyin: "xi yu zhuang", Code: "120106001000", FullName: "西于庄街道"},
		{ID: 120106002, ParentID: 120106, Deep: 3, Name: "丁字沽", Short: "d", Pinyin: "ding zi gu", Code: "120106002000", FullName: "丁字沽街道"},
		{ID: 120106003, ParentID: 120106, Deep: 3, Name: "西沽", Short: "x", Pinyin: "xi gu", Code: "120106003000", FullName: "西沽街道"},
		{ID: 120106004, ParentID: 120106, Deep: 3, Name: "三条石", Short: "s", Pinyin: "san tiao shi", Code: "120106004000", FullName: "三条石街道"},
	}

	// 批量插入数据
	log.Println("开始插入测试数据...")
	for _, data := range testData {
		// 使用 FirstOrCreate 避免重复插入
		var existing models.GeographicData
		result := db.DB.Where("id = ?", data.ID).First(&existing)
		if result.Error != nil {
			// 记录不存在，插入新记录
			if err := db.DB.Create(&data).Error; err != nil {
				log.Printf("插入数据失败 (ID: %d): %v", data.ID, err)
			} else {
				log.Printf("成功插入: %s (%s)", data.Name, data.GetLevelName())
			}
		} else {
			log.Printf("数据已存在: %s (%s)", data.Name, data.GetLevelName())
		}
	}

	// 统计插入结果
	var count int64
	db.DB.Model(&models.GeographicData{}).Count(&count)
	log.Printf("数据插入完成，当前总记录数: %d", count)

	// 按级别统计
	for level := 0; level <= 3; level++ {
		var levelCount int64
		db.DB.Model(&models.GeographicData{}).Where("deep = ?", level).Count(&levelCount)
		levelName := []string{"省份", "城市", "区/镇", "街道"}[level]
		log.Printf("级别 %d (%s): %d 条记录", level, levelName, levelCount)
	}

	log.Println("测试数据插入完成！")
}