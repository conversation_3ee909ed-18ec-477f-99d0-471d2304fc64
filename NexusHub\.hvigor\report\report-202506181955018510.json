{"version": "2.0", "ppid": 32784, "events": [{"head": {"id": "d0d3b4cd-543d-46d6-b9d8-12efb7e77aaf", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063482678000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44228266-3cd9-4178-9043-a56fca39a067", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063482871700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "828953ec-99f2-44c4-8128-1e0d37cc73f9", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063483084700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bbd4a42-33d4-4322-9bde-a2ebfd314ed0", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063485078000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "828a996b-64ba-4ee9-814b-4e86f7342c23", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063486521100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee0c29e2-6cf4-4905-98dd-470d17a537f2", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063488401100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a1317b0-32e8-4bea-beca-e73e06f32302", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063489355600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b36f872-abe9-45e2-9600-2868c689c289", "name": "worker[18] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063489719400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12f12f2a-e12e-4ec1-9344-0f3d79c1a09d", "name": "worker[4] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223063538769100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34d5f004-9b3b-402f-b5c5-2bae6ed4dc98", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072003683100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5497b0d9-eb77-4993-ac75-5b4873ef977d", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072017488800, "endTime": 223072387972600}, "additional": {"children": ["7fa70963-ad1f-4b23-8276-790b487bc901", "46ec6f6f-4a01-438d-9ba8-f72560817cda", "c46b7c52-60c1-4255-9406-d8504ccbf55f", "799663f5-8bd7-405d-a6bd-3eea1012e0b3", "24d1d16c-fb89-47bd-901c-013bae81362d", "da808020-0030-4e64-a51c-3aee80685f2a", "29144b9a-0697-4abe-8405-49eb5ad00de7"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "b2edbfcb-629a-4f00-8b7e-01966c28c39c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7fa70963-ad1f-4b23-8276-790b487bc901", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072017492500, "endTime": 223072047504300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5497b0d9-eb77-4993-ac75-5b4873ef977d", "logId": "2062d912-fdce-41b8-8763-5a3826846165"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "46ec6f6f-4a01-438d-9ba8-f72560817cda", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072047565800, "endTime": 223072385306000}, "additional": {"children": ["5b817a34-d8ff-4709-9f4a-20bb35b41f3d", "2175a27c-94a5-42c7-acbd-9dead1452178", "5fa60eb3-1929-4bfe-8fad-960854d0966a", "e367b859-12b3-4933-9655-c2587d3f7b24", "7a0a8479-7e96-4223-a290-b5dd6e32f2f3", "c65ffb19-6785-4764-8d44-da24cbd741f1", "6eb515b7-c02b-4218-87cf-82e519a18280", "645f89d4-dc54-4a8c-88ad-af678e336cea", "de31a613-5d6d-41c7-a063-318eff8de739"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5497b0d9-eb77-4993-ac75-5b4873ef977d", "logId": "70b053f2-565f-46ea-81c9-20f64619c245"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c46b7c52-60c1-4255-9406-d8504ccbf55f", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072385361800, "endTime": 223072387911400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5497b0d9-eb77-4993-ac75-5b4873ef977d", "logId": "36fea3ec-3700-48d8-be95-659a13aff604"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "799663f5-8bd7-405d-a6bd-3eea1012e0b3", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072387922900, "endTime": 223072387959200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5497b0d9-eb77-4993-ac75-5b4873ef977d", "logId": "317d5558-3fda-4199-a766-c5e7a61b75e8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24d1d16c-fb89-47bd-901c-013bae81362d", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072025527500, "endTime": 223072025712600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5497b0d9-eb77-4993-ac75-5b4873ef977d", "logId": "ed031e1a-f52c-4b3b-bb1f-d09166f3f5b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed031e1a-f52c-4b3b-bb1f-d09166f3f5b8", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072025527500, "endTime": 223072025712600}, "additional": {"logType": "info", "children": [], "durationId": "24d1d16c-fb89-47bd-901c-013bae81362d", "parent": "b2edbfcb-629a-4f00-8b7e-01966c28c39c"}}, {"head": {"id": "da808020-0030-4e64-a51c-3aee80685f2a", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072036962200, "endTime": 223072037017900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5497b0d9-eb77-4993-ac75-5b4873ef977d", "logId": "f41251ac-6faa-4064-ade7-475125a2c754"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f41251ac-6faa-4064-ade7-475125a2c754", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072036962200, "endTime": 223072037017900}, "additional": {"logType": "info", "children": [], "durationId": "da808020-0030-4e64-a51c-3aee80685f2a", "parent": "b2edbfcb-629a-4f00-8b7e-01966c28c39c"}}, {"head": {"id": "20e641d4-e41a-44cb-8c97-6ca398e1f60c", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072037116500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f380b41-fc1e-4f23-9735-52d24e4ad393", "name": "Cache service initialization finished in 10 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072046381200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2062d912-fdce-41b8-8763-5a3826846165", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072017492500, "endTime": 223072047504300}, "additional": {"logType": "info", "children": [], "durationId": "7fa70963-ad1f-4b23-8276-790b487bc901", "parent": "b2edbfcb-629a-4f00-8b7e-01966c28c39c"}}, {"head": {"id": "5b817a34-d8ff-4709-9f4a-20bb35b41f3d", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072059502900, "endTime": 223072059524200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "46ec6f6f-4a01-438d-9ba8-f72560817cda", "logId": "1d10ca25-dcbe-4241-88ce-164d0c0726ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2175a27c-94a5-42c7-acbd-9dead1452178", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072059555000, "endTime": 223072066003200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "46ec6f6f-4a01-438d-9ba8-f72560817cda", "logId": "0d75aa22-1f69-4a73-bd48-9d8edf3b7f67"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5fa60eb3-1929-4bfe-8fad-960854d0966a", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072066019300, "endTime": 223072179675700}, "additional": {"children": ["6f2e315d-fd01-4a19-befc-615496b667be", "708b5b99-8752-42b9-ace7-211c25354f37", "94f51924-238d-41b5-976e-1a2d176a15fc"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "46ec6f6f-4a01-438d-9ba8-f72560817cda", "logId": "41097ec5-9f14-43f6-8c2a-4d79213e1df1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e367b859-12b3-4933-9655-c2587d3f7b24", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072179695600, "endTime": 223072222032300}, "additional": {"children": ["6ec3ebaa-73dc-44b9-9211-586e68a0f504"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "46ec6f6f-4a01-438d-9ba8-f72560817cda", "logId": "19da6805-5834-47d3-896d-f9927ebed67d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7a0a8479-7e96-4223-a290-b5dd6e32f2f3", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072222044600, "endTime": 223072317897500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "46ec6f6f-4a01-438d-9ba8-f72560817cda", "logId": "7b365fa0-7d35-4394-802c-8a2e9170cda0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c65ffb19-6785-4764-8d44-da24cbd741f1", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072319465100, "endTime": 223072339205700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "46ec6f6f-4a01-438d-9ba8-f72560817cda", "logId": "b077ad9d-3b47-4654-954a-f805a58d6732"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6eb515b7-c02b-4218-87cf-82e519a18280", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072339250900, "endTime": 223072384958200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "46ec6f6f-4a01-438d-9ba8-f72560817cda", "logId": "75989431-61b0-4eae-b84e-c7cfe40c0e8a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "645f89d4-dc54-4a8c-88ad-af678e336cea", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072385008900, "endTime": 223072385274400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "46ec6f6f-4a01-438d-9ba8-f72560817cda", "logId": "14c784a9-49ad-468f-8d8b-4321a6525f78"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d10ca25-dcbe-4241-88ce-164d0c0726ea", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072059502900, "endTime": 223072059524200}, "additional": {"logType": "info", "children": [], "durationId": "5b817a34-d8ff-4709-9f4a-20bb35b41f3d", "parent": "70b053f2-565f-46ea-81c9-20f64619c245"}}, {"head": {"id": "0d75aa22-1f69-4a73-bd48-9d8edf3b7f67", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072059555000, "endTime": 223072066003200}, "additional": {"logType": "info", "children": [], "durationId": "2175a27c-94a5-42c7-acbd-9dead1452178", "parent": "70b053f2-565f-46ea-81c9-20f64619c245"}}, {"head": {"id": "6f2e315d-fd01-4a19-befc-615496b667be", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072066790100, "endTime": 223072066822800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5fa60eb3-1929-4bfe-8fad-960854d0966a", "logId": "b24ae943-bec9-4434-a0d4-ed9a094147b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b24ae943-bec9-4434-a0d4-ed9a094147b2", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072066790100, "endTime": 223072066822800}, "additional": {"logType": "info", "children": [], "durationId": "6f2e315d-fd01-4a19-befc-615496b667be", "parent": "41097ec5-9f14-43f6-8c2a-4d79213e1df1"}}, {"head": {"id": "708b5b99-8752-42b9-ace7-211c25354f37", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072069213500, "endTime": 223072178788700}, "additional": {"children": ["03563f9c-0b7f-4b56-929a-4d60fcb69641", "b38924fb-2288-4973-a8c2-e0bb62149c4a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5fa60eb3-1929-4bfe-8fad-960854d0966a", "logId": "7b1c4bb2-8fa6-488e-8040-b8e4b3849ff8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03563f9c-0b7f-4b56-929a-4d60fcb69641", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072069214900, "endTime": 223072077205900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "708b5b99-8752-42b9-ace7-211c25354f37", "logId": "5d5dbab7-149d-4acd-b1a1-8e8e74e1c017"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b38924fb-2288-4973-a8c2-e0bb62149c4a", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072077234000, "endTime": 223072178773600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "708b5b99-8752-42b9-ace7-211c25354f37", "logId": "aa576551-3c81-4e76-8adf-012c2f261480"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a421aaaf-c5bc-4168-8067-8d3f3539d027", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072069220300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a011c954-54ab-4335-a6c2-038cf0492caf", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072076990100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d5dbab7-149d-4acd-b1a1-8e8e74e1c017", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072069214900, "endTime": 223072077205900}, "additional": {"logType": "info", "children": [], "durationId": "03563f9c-0b7f-4b56-929a-4d60fcb69641", "parent": "7b1c4bb2-8fa6-488e-8040-b8e4b3849ff8"}}, {"head": {"id": "f714ff57-3879-44c2-b523-8e08972c5877", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072077256400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b315ae1b-d028-42a8-a7eb-6409ee0056e5", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072088179200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "728cf58e-beaa-47ba-ac3c-e567a118a7ac", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072088416900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c6a1716-7a7d-4147-ab19-c79abf9700d3", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072088660200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "992807d4-b134-45b2-bb1a-6b63c71a57e7", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072088839500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d5f4475-70e0-481b-8175-387ed66bd9de", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072092093600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3b96608-334c-4948-9166-7705ebb93931", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072107967700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2193423b-4a3a-41e6-9846-7ef7cf484997", "name": "Sdk init in 37 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072138185100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b17974e-83b5-4836-9040-68b3e4dc3632", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072138421700}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 19, "minute": 55, "second": 1}, "markType": "other"}}, {"head": {"id": "09a95fdb-e0a8-410e-bd73-6500448d9c8a", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072138441200}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 19, "minute": 55, "second": 1}, "markType": "other"}}, {"head": {"id": "e39e92c6-3f17-43af-90ac-5e017c4caebb", "name": "Project task initialization takes 38 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072178417100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc607cce-a37c-43c3-b61e-4f6d2ead6627", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072178584500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c26e33b8-62c7-426c-a03c-72aff36b4ab0", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072178664500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40278235-911d-4d42-a119-2020d797e219", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072178723800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa576551-3c81-4e76-8adf-012c2f261480", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072077234000, "endTime": 223072178773600}, "additional": {"logType": "info", "children": [], "durationId": "b38924fb-2288-4973-a8c2-e0bb62149c4a", "parent": "7b1c4bb2-8fa6-488e-8040-b8e4b3849ff8"}}, {"head": {"id": "7b1c4bb2-8fa6-488e-8040-b8e4b3849ff8", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072069213500, "endTime": 223072178788700}, "additional": {"logType": "info", "children": ["5d5dbab7-149d-4acd-b1a1-8e8e74e1c017", "aa576551-3c81-4e76-8adf-012c2f261480"], "durationId": "708b5b99-8752-42b9-ace7-211c25354f37", "parent": "41097ec5-9f14-43f6-8c2a-4d79213e1df1"}}, {"head": {"id": "94f51924-238d-41b5-976e-1a2d176a15fc", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072179635500, "endTime": 223072179655200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5fa60eb3-1929-4bfe-8fad-960854d0966a", "logId": "77c6d77f-d264-4156-abdd-30f993cd0d4d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "77c6d77f-d264-4156-abdd-30f993cd0d4d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072179635500, "endTime": 223072179655200}, "additional": {"logType": "info", "children": [], "durationId": "94f51924-238d-41b5-976e-1a2d176a15fc", "parent": "41097ec5-9f14-43f6-8c2a-4d79213e1df1"}}, {"head": {"id": "41097ec5-9f14-43f6-8c2a-4d79213e1df1", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072066019300, "endTime": 223072179675700}, "additional": {"logType": "info", "children": ["b24ae943-bec9-4434-a0d4-ed9a094147b2", "7b1c4bb2-8fa6-488e-8040-b8e4b3849ff8", "77c6d77f-d264-4156-abdd-30f993cd0d4d"], "durationId": "5fa60eb3-1929-4bfe-8fad-960854d0966a", "parent": "70b053f2-565f-46ea-81c9-20f64619c245"}}, {"head": {"id": "6ec3ebaa-73dc-44b9-9211-586e68a0f504", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072180375000, "endTime": 223072222011900}, "additional": {"children": ["b5c09ad9-e77f-49a5-9d4a-09827c238804", "0f8f8a64-f954-4b90-8f22-9904a5ba7b35", "59011abf-f3cf-4f33-803e-f2514a4a2097"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e367b859-12b3-4933-9655-c2587d3f7b24", "logId": "f1c02210-952c-49d7-b4f6-5979223b525d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b5c09ad9-e77f-49a5-9d4a-09827c238804", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072185224100, "endTime": 223072185245600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6ec3ebaa-73dc-44b9-9211-586e68a0f504", "logId": "86d93aa5-af37-463a-8c50-c10bc3589433"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "86d93aa5-af37-463a-8c50-c10bc3589433", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072185224100, "endTime": 223072185245600}, "additional": {"logType": "info", "children": [], "durationId": "b5c09ad9-e77f-49a5-9d4a-09827c238804", "parent": "f1c02210-952c-49d7-b4f6-5979223b525d"}}, {"head": {"id": "0f8f8a64-f954-4b90-8f22-9904a5ba7b35", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072187153600, "endTime": 223072218906000}, "additional": {"children": ["3c8f66d5-0dfc-4299-b7e1-663c00071971", "6f9fc63d-eaba-4bcd-b97e-04dfd520f5b8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6ec3ebaa-73dc-44b9-9211-586e68a0f504", "logId": "b86cdf8a-de06-47c1-9cf0-f32be0b08333"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c8f66d5-0dfc-4299-b7e1-663c00071971", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072187154700, "endTime": 223072191066000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0f8f8a64-f954-4b90-8f22-9904a5ba7b35", "logId": "853a281f-c99c-4f22-91ce-ee120fc7af54"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f9fc63d-eaba-4bcd-b97e-04dfd520f5b8", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072191090400, "endTime": 223072218876700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0f8f8a64-f954-4b90-8f22-9904a5ba7b35", "logId": "939db8df-593b-4451-9c67-849d9cb636c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7ad02c8-c352-4876-b0c7-06f396e052f9", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072187160000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad15f6d2-f0c5-4889-93f9-2572524a880d", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072190864500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "853a281f-c99c-4f22-91ce-ee120fc7af54", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072187154700, "endTime": 223072191066000}, "additional": {"logType": "info", "children": [], "durationId": "3c8f66d5-0dfc-4299-b7e1-663c00071971", "parent": "b86cdf8a-de06-47c1-9cf0-f32be0b08333"}}, {"head": {"id": "a249edc0-e6df-4cc7-94a2-e9cc07e8214a", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072191112200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdc6496e-f845-4ca6-a4bf-72da3abc7d2e", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072206993500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "077ee24a-b7f2-41b3-a020-b8a999603f9b", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072207225500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1c2146a-2a6b-4eae-966f-e9b0b10a89b4", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072207591100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9a8df65-8c44-4ce7-8d8d-1520a00a7783", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072208027100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f4d8eeb-515d-486c-af74-2880fb506450", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072208346700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eae7f447-8636-4809-8d09-c748600178e5", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072208593700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75371a77-339a-46c6-ace4-c1d0eb22a197", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072208949100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85d53b83-ebc5-4a5f-87f2-98b7c9e9090c", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072209124700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd4fc3ff-5759-4862-a922-4f62e5d3c7e8", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072209610800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a7cd39a-9d3d-4f9c-94bf-7c5d4d75ad10", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072209971400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c872d319-4e11-4977-b88a-d67ebc99c493", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072210174300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c1cfb00-7d40-471a-aaca-f9314fe8e19f", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072210331200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a25d53e1-6654-45a3-b756-96b4379dffa5", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072210527600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef953486-fba3-45f3-b354-62d038c114b5", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072210706200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb182d03-d732-45a9-b72f-3dc41811d27c", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072211043800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1536877-40cd-40c9-bd1c-fe0f5f97e99a", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072211285500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "605b1111-9006-4d37-bfbf-2efd31bc45d9", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072211393700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e045973-0198-4903-b447-26ba3eaf426c", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072211489200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5d1b0df-aa90-432d-825f-15b4508d1377", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072211614700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b908a1a8-46aa-4abb-8c9d-8d08000612d0", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072217612100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8908c3b-cc87-489a-bfd4-c8b667b210f9", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072218361400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d19f2ea-fc46-4459-bda1-91fbc97061b7", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072218584200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fc4e917-3358-4d70-a1cd-9e1a3c79b3dc", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072218741600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "939db8df-593b-4451-9c67-849d9cb636c4", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072191090400, "endTime": 223072218876700}, "additional": {"logType": "info", "children": [], "durationId": "6f9fc63d-eaba-4bcd-b97e-04dfd520f5b8", "parent": "b86cdf8a-de06-47c1-9cf0-f32be0b08333"}}, {"head": {"id": "b86cdf8a-de06-47c1-9cf0-f32be0b08333", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072187153600, "endTime": 223072218906000}, "additional": {"logType": "info", "children": ["853a281f-c99c-4f22-91ce-ee120fc7af54", "939db8df-593b-4451-9c67-849d9cb636c4"], "durationId": "0f8f8a64-f954-4b90-8f22-9904a5ba7b35", "parent": "f1c02210-952c-49d7-b4f6-5979223b525d"}}, {"head": {"id": "59011abf-f3cf-4f33-803e-f2514a4a2097", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072221969700, "endTime": 223072221988500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6ec3ebaa-73dc-44b9-9211-586e68a0f504", "logId": "fb33065a-de98-4656-adc1-0e242b054020"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb33065a-de98-4656-adc1-0e242b054020", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072221969700, "endTime": 223072221988500}, "additional": {"logType": "info", "children": [], "durationId": "59011abf-f3cf-4f33-803e-f2514a4a2097", "parent": "f1c02210-952c-49d7-b4f6-5979223b525d"}}, {"head": {"id": "f1c02210-952c-49d7-b4f6-5979223b525d", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072180375000, "endTime": 223072222011900}, "additional": {"logType": "info", "children": ["86d93aa5-af37-463a-8c50-c10bc3589433", "b86cdf8a-de06-47c1-9cf0-f32be0b08333", "fb33065a-de98-4656-adc1-0e242b054020"], "durationId": "6ec3ebaa-73dc-44b9-9211-586e68a0f504", "parent": "19da6805-5834-47d3-896d-f9927ebed67d"}}, {"head": {"id": "19da6805-5834-47d3-896d-f9927ebed67d", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072179695600, "endTime": 223072222032300}, "additional": {"logType": "info", "children": ["f1c02210-952c-49d7-b4f6-5979223b525d"], "durationId": "e367b859-12b3-4933-9655-c2587d3f7b24", "parent": "70b053f2-565f-46ea-81c9-20f64619c245"}}, {"head": {"id": "fe08f140-d675-4f7a-b41c-0ca06e20fdbe", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072257567800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f0c4969-e789-4e56-afd3-d54bcbb8f860", "name": "hvigorfile, resolve hvigorfile dependencies in 96 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072317500000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b365fa0-7d35-4394-802c-8a2e9170cda0", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072222044600, "endTime": 223072317897500}, "additional": {"logType": "info", "children": [], "durationId": "7a0a8479-7e96-4223-a290-b5dd6e32f2f3", "parent": "70b053f2-565f-46ea-81c9-20f64619c245"}}, {"head": {"id": "de31a613-5d6d-41c7-a063-318eff8de739", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072319154800, "endTime": 223072319443600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "46ec6f6f-4a01-438d-9ba8-f72560817cda", "logId": "cbfe8472-3304-4b1e-8400-a380da0d6192"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1f9757d8-88c4-4e32-9961-138855ff4d5b", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072319203500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbfe8472-3304-4b1e-8400-a380da0d6192", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072319154800, "endTime": 223072319443600}, "additional": {"logType": "info", "children": [], "durationId": "de31a613-5d6d-41c7-a063-318eff8de739", "parent": "70b053f2-565f-46ea-81c9-20f64619c245"}}, {"head": {"id": "7f302f2b-05bb-40b6-afcf-dac465aafd7b", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072323173900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47526d57-25be-4400-87a7-1c1a8ae49784", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072337606200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b077ad9d-3b47-4654-954a-f805a58d6732", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072319465100, "endTime": 223072339205700}, "additional": {"logType": "info", "children": [], "durationId": "c65ffb19-6785-4764-8d44-da24cbd741f1", "parent": "70b053f2-565f-46ea-81c9-20f64619c245"}}, {"head": {"id": "9083e021-3acc-4fb9-9e78-2e3dc8e5f24f", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072339279500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "515bc754-2e11-4274-9fa3-d403c4b00e18", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072365758300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3cf5e33-8631-41eb-9f1d-9ae35f915d1d", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072366018600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7980169-752d-43e8-9059-41c0cf22e8c1", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072366552100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe205d78-c0e6-4b57-9e8a-4b4c2c746c88", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072376682800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39007a14-b051-4f51-a9fb-3fb782fc26a1", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072376922500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75989431-61b0-4eae-b84e-c7cfe40c0e8a", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072339250900, "endTime": 223072384958200}, "additional": {"logType": "info", "children": [], "durationId": "6eb515b7-c02b-4218-87cf-82e519a18280", "parent": "70b053f2-565f-46ea-81c9-20f64619c245"}}, {"head": {"id": "b3525080-a492-4b88-9545-a0e04e8088be", "name": "Configuration phase cost:326 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072385057300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14c784a9-49ad-468f-8d8b-4321a6525f78", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072385008900, "endTime": 223072385274400}, "additional": {"logType": "info", "children": [], "durationId": "645f89d4-dc54-4a8c-88ad-af678e336cea", "parent": "70b053f2-565f-46ea-81c9-20f64619c245"}}, {"head": {"id": "70b053f2-565f-46ea-81c9-20f64619c245", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072047565800, "endTime": 223072385306000}, "additional": {"logType": "info", "children": ["1d10ca25-dcbe-4241-88ce-164d0c0726ea", "0d75aa22-1f69-4a73-bd48-9d8edf3b7f67", "41097ec5-9f14-43f6-8c2a-4d79213e1df1", "19da6805-5834-47d3-896d-f9927ebed67d", "7b365fa0-7d35-4394-802c-8a2e9170cda0", "b077ad9d-3b47-4654-954a-f805a58d6732", "75989431-61b0-4eae-b84e-c7cfe40c0e8a", "14c784a9-49ad-468f-8d8b-4321a6525f78", "cbfe8472-3304-4b1e-8400-a380da0d6192"], "durationId": "46ec6f6f-4a01-438d-9ba8-f72560817cda", "parent": "b2edbfcb-629a-4f00-8b7e-01966c28c39c"}}, {"head": {"id": "29144b9a-0697-4abe-8405-49eb5ad00de7", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072387861500, "endTime": 223072387889700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5497b0d9-eb77-4993-ac75-5b4873ef977d", "logId": "6a6d7ed3-5ba6-4154-8f0c-0f90967a8a1e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a6d7ed3-5ba6-4154-8f0c-0f90967a8a1e", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072387861500, "endTime": 223072387889700}, "additional": {"logType": "info", "children": [], "durationId": "29144b9a-0697-4abe-8405-49eb5ad00de7", "parent": "b2edbfcb-629a-4f00-8b7e-01966c28c39c"}}, {"head": {"id": "36fea3ec-3700-48d8-be95-659a13aff604", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072385361800, "endTime": 223072387911400}, "additional": {"logType": "info", "children": [], "durationId": "c46b7c52-60c1-4255-9406-d8504ccbf55f", "parent": "b2edbfcb-629a-4f00-8b7e-01966c28c39c"}}, {"head": {"id": "317d5558-3fda-4199-a766-c5e7a61b75e8", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072387922900, "endTime": 223072387959200}, "additional": {"logType": "info", "children": [], "durationId": "799663f5-8bd7-405d-a6bd-3eea1012e0b3", "parent": "b2edbfcb-629a-4f00-8b7e-01966c28c39c"}}, {"head": {"id": "b2edbfcb-629a-4f00-8b7e-01966c28c39c", "name": "init", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072017488800, "endTime": 223072387972600}, "additional": {"logType": "info", "children": ["2062d912-fdce-41b8-8763-5a3826846165", "70b053f2-565f-46ea-81c9-20f64619c245", "36fea3ec-3700-48d8-be95-659a13aff604", "317d5558-3fda-4199-a766-c5e7a61b75e8", "ed031e1a-f52c-4b3b-bb1f-d09166f3f5b8", "f41251ac-6faa-4064-ade7-475125a2c754", "6a6d7ed3-5ba6-4154-8f0c-0f90967a8a1e"], "durationId": "5497b0d9-eb77-4993-ac75-5b4873ef977d"}}, {"head": {"id": "d1190366-d65d-48f1-8ece-2037ac3df5c9", "name": "Configuration task cost before running: 378 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072388387700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05c78a73-c74c-4556-800f-76e61085bab9", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072409664000, "endTime": 223072436008600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "24195844-a6c8-47e2-aeef-3ea235cfe963", "logId": "d2212cbe-0b38-40c9-9fa2-c0a925a46ada"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24195844-a6c8-47e2-aeef-3ea235cfe963", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072392243800}, "additional": {"logType": "detail", "children": [], "durationId": "05c78a73-c74c-4556-800f-76e61085bab9"}}, {"head": {"id": "ecaa77a2-84fc-4387-a508-200d9d311728", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072394191900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa4f4bc3-db97-4d89-b44e-f43b4ee465a2", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072394475200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "837f10d7-ecb9-4738-89ab-706151f16571", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072396424400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c34cc7b5-d808-470f-bfd3-c4f3e3fa7d62", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072397940300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d709ab2f-ea5d-43a8-b0cb-baa22c22b3e7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072400849200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6972088e-e10f-4088-aa47-82305de8714b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072401184500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "150091e2-cfef-4373-8675-eb2108e79914", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072409694600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f81d146-905c-4bd7-9bba-a298a5916fa4", "name": "Incremental task entry:default@PreBuild pre-execution cost: 18 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072435617700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "399b0314-7738-412f-971d-cda1c9f2b57d", "name": "entry : default@PreBuild cost memory 0.440765380859375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072435856600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2212cbe-0b38-40c9-9fa2-c0a925a46ada", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072409664000, "endTime": 223072436008600}, "additional": {"logType": "info", "children": [], "durationId": "05c78a73-c74c-4556-800f-76e61085bab9"}}, {"head": {"id": "7c745180-bac3-4470-b48b-cc87fdf6c3b5", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072453158000, "endTime": 223072457405800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "87c4744e-94d5-4d2d-b7e5-c91dabaa01f5", "logId": "37bb8fc6-5374-4de9-8564-f2341b51b4de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87c4744e-94d5-4d2d-b7e5-c91dabaa01f5", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072447604800}, "additional": {"logType": "detail", "children": [], "durationId": "7c745180-bac3-4470-b48b-cc87fdf6c3b5"}}, {"head": {"id": "2edcc851-e48e-44ca-8c6c-4b539af268f4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072451096200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cef6da38-c0bb-4f77-a5cb-ecffebb365d2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072451363300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d7f8f67-eeab-4fab-8581-b88e4e25fb11", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072453188600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79346334-c719-496e-a876-b70a469f81cb", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072454997500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86e8e434-543b-4b7e-a498-2c74ab42db46", "name": "entry : default@CreateModuleInfo cost memory 0.061492919921875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072457015700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "127ebf54-a106-4afc-8b7d-fc56e42994ff", "name": "runTaskFromQueue task cost before running: 447 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072457280900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37bb8fc6-5374-4de9-8564-f2341b51b4de", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072453158000, "endTime": 223072457405800, "totalTime": 4081100}, "additional": {"logType": "info", "children": [], "durationId": "7c745180-bac3-4470-b48b-cc87fdf6c3b5"}}, {"head": {"id": "d3eeeb5d-9138-4fe3-b503-e2ccf64ff05e", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072474771100, "endTime": 223072479153800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "cf161c50-7b3c-4545-af97-d58121252b7a", "logId": "0ab8d7cd-785e-47ba-ab27-9224d4489ff6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf161c50-7b3c-4545-af97-d58121252b7a", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072463026100}, "additional": {"logType": "detail", "children": [], "durationId": "d3eeeb5d-9138-4fe3-b503-e2ccf64ff05e"}}, {"head": {"id": "89dbd11e-3e87-4397-82cd-4aea55eddd27", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072465991200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d653e40-8acc-4359-881c-322d89214fb3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072466355000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4186470-a03a-49ff-9d9a-473d48a663a7", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072474820400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "590a87e6-97de-4380-aa77-6732162c7624", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072476526000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b6eb9b3-a57b-4b5b-90d0-62d904557138", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072478831500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77a93a2c-0413-4669-9c0e-c05138282df3", "name": "entry : default@GenerateMetadata cost memory 0.103118896484375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072479011600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ab8d7cd-785e-47ba-ab27-9224d4489ff6", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072474771100, "endTime": 223072479153800}, "additional": {"logType": "info", "children": [], "durationId": "d3eeeb5d-9138-4fe3-b503-e2ccf64ff05e"}}, {"head": {"id": "dd56de10-0fe6-415e-9a14-dc5f6f97e529", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072484541900, "endTime": 223072485102600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "6ceca442-e390-4a2c-9395-7151e5ba286b", "logId": "dd2e1eb4-8ab2-4a2b-b7dc-81137d660454"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ceca442-e390-4a2c-9395-7151e5ba286b", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072482183100}, "additional": {"logType": "detail", "children": [], "durationId": "dd56de10-0fe6-415e-9a14-dc5f6f97e529"}}, {"head": {"id": "b1748293-e72d-4154-9b9b-38ac3d8554ec", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072484113000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee8f6409-602f-427d-8148-331decb6bad5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072484308400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66bcf5fe-7fb7-4d19-8e63-7ef191a20279", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072484556400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f579fef-2078-47e0-9d3d-994f7afa4881", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072484704800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd18415f-9ce5-4675-98d3-c96eb10adc28", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072484789600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44ce4b08-d3b3-49dc-b53e-bbc8419b8e36", "name": "entry : default@ConfigureCmake cost memory 0.0374755859375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072484904800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b5d78f9-3db1-4464-871f-b6d231194dde", "name": "runTaskFromQueue task cost before running: 475 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072485031700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd2e1eb4-8ab2-4a2b-b7dc-81137d660454", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072484541900, "endTime": 223072485102600, "totalTime": 461800}, "additional": {"logType": "info", "children": [], "durationId": "dd56de10-0fe6-415e-9a14-dc5f6f97e529"}}, {"head": {"id": "312d20cc-0f7c-4f07-9883-35501cd01cc9", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072491231200, "endTime": 223072495886900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "85f98385-9402-49b1-9b92-3787fdc1aa82", "logId": "41955741-3ecc-47f3-808d-83cd65c738e8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85f98385-9402-49b1-9b92-3787fdc1aa82", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072488014700}, "additional": {"logType": "detail", "children": [], "durationId": "312d20cc-0f7c-4f07-9883-35501cd01cc9"}}, {"head": {"id": "df94d56e-5cf1-4720-8fdd-266307571d2a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072489950100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb622199-8a37-4cc2-9fdb-a10e2a62de4a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072490136300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d6a2652-2ce4-4518-8016-aea26c269f04", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072491255500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a35d560-6789-47ba-a6cb-1b5435b8b1ce", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072495474700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c36e9dc-1fc0-4171-bd6e-8fe9b0f97f00", "name": "entry : default@MergeProfile cost memory 0.1182403564453125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072495740800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41955741-3ecc-47f3-808d-83cd65c738e8", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072491231200, "endTime": 223072495886900}, "additional": {"logType": "info", "children": [], "durationId": "312d20cc-0f7c-4f07-9883-35501cd01cc9"}}, {"head": {"id": "a60742ec-5fc2-4102-9fa1-7fe861e3e293", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072507089200, "endTime": 223072514278000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b9ad7c5b-8da0-4b69-a785-5f14ff38b60d", "logId": "de3b64c7-43cb-40bf-ba32-045005b13862"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b9ad7c5b-8da0-4b69-a785-5f14ff38b60d", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072502464800}, "additional": {"logType": "detail", "children": [], "durationId": "a60742ec-5fc2-4102-9fa1-7fe861e3e293"}}, {"head": {"id": "33d13769-b680-44cf-b790-ed5fedc25abf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072505165100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8afabf09-0eaf-4164-8586-2a631f4fa011", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072505368800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebb110b5-3198-45ce-92d2-bafb593a0ed3", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072507108800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cc2735b-bcb6-4c6a-b949-ec782da56666", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 3 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072509648100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0adbf002-2d3e-48aa-b711-3a7fb9b71858", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072513842000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "485223fe-baf6-45bf-bf12-4de735d029f2", "name": "entry : default@CreateBuildProfile cost memory 0.10800933837890625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072514096400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de3b64c7-43cb-40bf-ba32-045005b13862", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072507089200, "endTime": 223072514278000}, "additional": {"logType": "info", "children": [], "durationId": "a60742ec-5fc2-4102-9fa1-7fe861e3e293"}}, {"head": {"id": "3a86b1a3-e8ff-48b6-ace4-9e04f53a7d16", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072524008700, "endTime": 223072525358200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "cb08f150-c785-4382-9c85-b110d879e4bb", "logId": "c06b7c29-7809-43bf-a38b-91d785cd0cd5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cb08f150-c785-4382-9c85-b110d879e4bb", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072518923600}, "additional": {"logType": "detail", "children": [], "durationId": "3a86b1a3-e8ff-48b6-ace4-9e04f53a7d16"}}, {"head": {"id": "5f42e4af-2ea7-4763-8f64-9726db522bcd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072522207600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ec8a50c-c1d6-4fb3-8366-ff3676220e3d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072522418300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01886d4e-8541-46d9-84ad-e137652ba138", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072524029800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9264454-0dc7-4907-8219-62c7d5871e3f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072524267200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e256bdd-c880-4249-aee9-a31484dd3733", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072524407600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f015f14-21a4-432f-9192-cc40be33614c", "name": "entry : default@PreCheckSyscap cost memory 0.041107177734375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072524970800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "816acc87-5122-445c-b9ed-e6e3ddcf34a3", "name": "runTaskFromQueue task cost before running: 515 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072525222600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c06b7c29-7809-43bf-a38b-91d785cd0cd5", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072524008700, "endTime": 223072525358200, "totalTime": 1171200}, "additional": {"logType": "info", "children": [], "durationId": "3a86b1a3-e8ff-48b6-ace4-9e04f53a7d16"}}, {"head": {"id": "a93c9299-ee59-476b-900c-755fbfa9e30f", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072536543900, "endTime": 223072550023700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3a8fbc5e-c2f9-495c-b9ce-5e90c3811dd3", "logId": "f9af7aef-c537-4f8e-82ac-7ec1f64fb84b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a8fbc5e-c2f9-495c-b9ce-5e90c3811dd3", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072529366400}, "additional": {"logType": "detail", "children": [], "durationId": "a93c9299-ee59-476b-900c-755fbfa9e30f"}}, {"head": {"id": "3f42b9ed-5ce1-489e-8239-90da1064e25c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072533293400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec31db5e-dc75-43dc-bedf-9709c55df616", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072533570100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bee2010-ba72-4325-ac72-3de1caaf5ba6", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072536567600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "deb403bc-c8c2-4cd9-a88f-29f5391dccec", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072547936400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba5c5197-195d-43f4-9f61-8c6b22a2235c", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072549452100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed920425-cc38-4c2c-a830-2a2972d9bf11", "name": "entry : default@GeneratePkgContextInfo cost memory 0.2548065185546875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072549827400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9af7aef-c537-4f8e-82ac-7ec1f64fb84b", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072536543900, "endTime": 223072550023700}, "additional": {"logType": "info", "children": [], "durationId": "a93c9299-ee59-476b-900c-755fbfa9e30f"}}, {"head": {"id": "3eac164a-771d-40b0-ab0d-c76831966427", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072569313800, "endTime": 223072574087800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "98e1c0e0-72c6-4e16-bcda-3a8e6ff28520", "logId": "ef8192dc-29bf-46f6-9766-f7867823e00b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "98e1c0e0-72c6-4e16-bcda-3a8e6ff28520", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072553733400}, "additional": {"logType": "detail", "children": [], "durationId": "3eac164a-771d-40b0-ab0d-c76831966427"}}, {"head": {"id": "4ab1c61a-4015-4e6a-bdf3-eae097362e80", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072556341800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ace5a5d-b011-4cb7-b2ee-3fd6e26225d2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072556521700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb3ad795-4a0a-4547-9113-14640249a150", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072569347900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22e5efa6-0627-4496-beec-11bc9b4513b7", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072573142900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97ecb8d7-1af3-4ade-bfd6-5a08af27ffb0", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072573386400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cae8245-f85f-436a-be51-0f527a31c085", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072573560500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90371bf8-8bf0-4734-91fa-517a393dd235", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072573672300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36d8250e-7bf9-4d25-bfc8-3809f5a1987b", "name": "entry : default@ProcessIntegratedHsp cost memory 0.121185302734375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072573826000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbc5dfe0-24a5-4ceb-9c3c-10da916bcef8", "name": "runTaskFromQueue task cost before running: 564 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072573979400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef8192dc-29bf-46f6-9766-f7867823e00b", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072569313800, "endTime": 223072574087800, "totalTime": 4641700}, "additional": {"logType": "info", "children": [], "durationId": "3eac164a-771d-40b0-ab0d-c76831966427"}}, {"head": {"id": "8de2ea7d-b2de-4c5b-ac1f-f404bc5f758e", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072584326100, "endTime": 223072585166700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "ca15c927-1362-4738-aee4-73e84f4287f4", "logId": "40a7fe42-ddc2-433f-90ab-610b460b133e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca15c927-1362-4738-aee4-73e84f4287f4", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072579097200}, "additional": {"logType": "detail", "children": [], "durationId": "8de2ea7d-b2de-4c5b-ac1f-f404bc5f758e"}}, {"head": {"id": "a0300818-4f00-4a74-89f0-013fa3a2e5c1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072582018100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eeac3dc1-a356-495e-a581-7ddaa9e5c957", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072582254400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e634139a-37e6-4009-bc72-541fa965bf08", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072584349900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bab191dd-4ddd-4d2c-81bd-cfede21dcc56", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072584612600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "347810c3-d63f-46d4-a1a9-286b8255931b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072584728400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "890ac8f0-d3cd-4eed-8f19-73a41e4e7953", "name": "entry : default@BuildNativeWithCmake cost memory 0.03852081298828125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072584893400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd8f6959-e055-4e34-bd11-a10caf2e4b68", "name": "runTaskFromQueue task cost before running: 575 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072585055000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40a7fe42-ddc2-433f-90ab-610b460b133e", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072584326100, "endTime": 223072585166700, "totalTime": 693900}, "additional": {"logType": "info", "children": [], "durationId": "8de2ea7d-b2de-4c5b-ac1f-f404bc5f758e"}}, {"head": {"id": "2fc6e74b-7f0f-4091-bc23-3ab3548a911f", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072593861800, "endTime": 223072604244500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a5405e1c-f629-480c-9feb-df0c6dd67992", "logId": "be28d835-a519-4cd2-bf04-fd859ca16e2f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5405e1c-f629-480c-9feb-df0c6dd67992", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072588792900}, "additional": {"logType": "detail", "children": [], "durationId": "2fc6e74b-7f0f-4091-bc23-3ab3548a911f"}}, {"head": {"id": "6702ac74-39d6-43a1-b167-40471094a387", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072591762700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5007eac0-6757-4700-b9dc-60e414890b0b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072592011200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a33e58d5-1bb0-48df-a89f-29f3c4728cba", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072593906800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32f8b340-ef84-40bd-9531-73deabfbd77f", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072603837600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20e5ccc0-4ec0-4bbd-b113-9a853b510e80", "name": "entry : default@MakePackInfo cost memory 0.16445159912109375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072604089400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be28d835-a519-4cd2-bf04-fd859ca16e2f", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072593861800, "endTime": 223072604244500}, "additional": {"logType": "info", "children": [], "durationId": "2fc6e74b-7f0f-4091-bc23-3ab3548a911f"}}, {"head": {"id": "0fa6e9ea-a4fd-4d0b-9fb9-ba57e3e05162", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072616002600, "endTime": 223072623149200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f30f0b45-c2a3-4e0e-b35f-a39e848de54f", "logId": "0bd458df-98d5-4c53-8742-b2e6c73a707e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f30f0b45-c2a3-4e0e-b35f-a39e848de54f", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072610109500}, "additional": {"logType": "detail", "children": [], "durationId": "0fa6e9ea-a4fd-4d0b-9fb9-ba57e3e05162"}}, {"head": {"id": "be12f970-940f-44e9-a55d-c3282cb3714c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072612838700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30de15fb-b418-45a9-b535-7e4b50fd6927", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072613078100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffabfbce-6684-43cd-aa30-2006017ff2e7", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072616031100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "472f93d2-313f-441a-a127-564cd3773aa6", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072616535200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff10bafb-2557-42c9-b1f1-9ca3edc6019c", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072617665500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91dc5b5c-7eb8-4cbd-87b3-7fc84c1215a1", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072622745100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "574b9fc1-ca67-4be8-a223-2c02b4b530ff", "name": "entry : default@SyscapTransform cost memory 0.1506805419921875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072622976000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bd458df-98d5-4c53-8742-b2e6c73a707e", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072616002600, "endTime": 223072623149200}, "additional": {"logType": "info", "children": [], "durationId": "0fa6e9ea-a4fd-4d0b-9fb9-ba57e3e05162"}}, {"head": {"id": "36c71bbe-f784-445d-b8fb-90f9a8092007", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072633165700, "endTime": 223072638528300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "2664308d-08ce-470e-bed9-483776d7ab4c", "logId": "6e2fd8f7-8f1c-4c4b-a30e-b8cdeff694aa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2664308d-08ce-470e-bed9-483776d7ab4c", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072627610000}, "additional": {"logType": "detail", "children": [], "durationId": "36c71bbe-f784-445d-b8fb-90f9a8092007"}}, {"head": {"id": "457ab41e-6f6b-45cd-952d-cf7127e1a819", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072630218100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22db7775-4e56-4dd5-b926-2ed9928165f0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072630431800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "248c9609-8579-416b-a754-f0e7451be26a", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072633191700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81801adb-8474-430f-b275-4c3793da2625", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072638108900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "465ec8ab-d9f9-470d-8f10-66241f97544b", "name": "entry : default@ProcessProfile cost memory 0.12453460693359375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072638382300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e2fd8f7-8f1c-4c4b-a30e-b8cdeff694aa", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072633165700, "endTime": 223072638528300}, "additional": {"logType": "info", "children": [], "durationId": "36c71bbe-f784-445d-b8fb-90f9a8092007"}}, {"head": {"id": "080bbdc8-5a8a-4937-ac85-801ab85d5c62", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072648425900, "endTime": 223072661897900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3c2ad280-c4e2-4756-8598-b4db335d7628", "logId": "3ee516f9-7bcd-4b6b-93e3-20a125b4f6cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c2ad280-c4e2-4756-8598-b4db335d7628", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072641894000}, "additional": {"logType": "detail", "children": [], "durationId": "080bbdc8-5a8a-4937-ac85-801ab85d5c62"}}, {"head": {"id": "2445f330-1a57-4c46-b344-70c8fc0e9d35", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072644252600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdebcb36-3d72-4622-94f7-805aaf0141a4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072644437500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d7731ea-7342-42e1-b73d-08d4b127e330", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072648456400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0aa2c5c3-cef9-47e3-af1a-d1157b1b8825", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072661529200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41fb5d32-4ce3-4921-9359-8a037ffc5b6e", "name": "entry : default@ProcessRouterMap cost memory 0.23371124267578125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072661766400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ee516f9-7bcd-4b6b-93e3-20a125b4f6cf", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072648425900, "endTime": 223072661897900}, "additional": {"logType": "info", "children": [], "durationId": "080bbdc8-5a8a-4937-ac85-801ab85d5c62"}}, {"head": {"id": "28e9bb38-e385-43f5-b926-37d54b3d10cb", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072672166100, "endTime": 223072685682800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "5cf0ee05-5207-458d-baf7-17640a4f4f26", "logId": "672e4ef2-f867-461f-9960-2082ecc5bef5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5cf0ee05-5207-458d-baf7-17640a4f4f26", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072669305900}, "additional": {"logType": "detail", "children": [], "durationId": "28e9bb38-e385-43f5-b926-37d54b3d10cb"}}, {"head": {"id": "6e7db6d4-59ce-4355-a5ac-1bce4a553c6f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072671770900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09a938e4-805d-4898-9812-fa2d3b7b0fe7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072671973200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccf6339a-b7a9-41c7-a0fb-82bc6d545458", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072672183100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60416174-8ce0-425c-ac84-9df163908a2b", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072672483400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d5c8a62-6541-4b24-b651-cba9f91ee79b", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072682657300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0c7e27e-85e3-401a-886c-4d225b82c78d", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072683033300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef4c23d1-5e51-481b-ae4e-52a818caba10", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072683249900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6fd6f77-5e4f-4f28-9b7e-3cd7dba94fc7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072683335100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6f73e7b-bf93-4fb8-9f18-e28a5856106b", "name": "entry : default@ProcessStartupConfig cost memory 0.2599945068359375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072685264900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29f5b3da-85a3-4297-a87f-3236f4db273d", "name": "runTaskFromQueue task cost before running: 675 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072685545500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "672e4ef2-f867-461f-9960-2082ecc5bef5", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072672166100, "endTime": 223072685682800, "totalTime": 13329300}, "additional": {"logType": "info", "children": [], "durationId": "28e9bb38-e385-43f5-b926-37d54b3d10cb"}}, {"head": {"id": "f5ce7069-1f23-4972-8351-bd4ee8f84de6", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072696901300, "endTime": 223072699414900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "0db536cb-f2f8-4baf-bfd9-ad9f17ce9026", "logId": "0b875a86-8a84-4c75-b585-39723a08d42b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0db536cb-f2f8-4baf-bfd9-ad9f17ce9026", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072692446300}, "additional": {"logType": "detail", "children": [], "durationId": "f5ce7069-1f23-4972-8351-bd4ee8f84de6"}}, {"head": {"id": "ad34357a-7683-4fc2-a243-14f27f0ead90", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072695042400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb6867ab-935d-4ede-8551-5810c790decb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072695248500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d10f49d4-71f9-4d54-a4de-8115f9f8785f", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072696923000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccba5a99-14e4-4516-b871-562e2eb68cbd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072697128700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bddab7a6-a303-48de-b3a8-db5df190332f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072697231600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4880fbc-a870-47e6-8a0d-d985de307749", "name": "entry : default@BuildNativeWithNinja cost memory 0.05841827392578125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072699054200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b57b57f9-b884-45ab-b321-dde326601a95", "name": "runTaskFromQueue task cost before running: 689 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072699289600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b875a86-8a84-4c75-b585-39723a08d42b", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072696901300, "endTime": 223072699414900, "totalTime": 2349200}, "additional": {"logType": "info", "children": [], "durationId": "f5ce7069-1f23-4972-8351-bd4ee8f84de6"}}, {"head": {"id": "fefe33fb-f2ba-429c-9da1-130f29d3c891", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072709981300, "endTime": 223072720532700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "429c2a47-8f0a-4f2f-b11b-7c2abdd1004a", "logId": "f2d41bcb-90f8-45df-a4e3-477e768df7c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "429c2a47-8f0a-4f2f-b11b-7c2abdd1004a", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072704048700}, "additional": {"logType": "detail", "children": [], "durationId": "fefe33fb-f2ba-429c-9da1-130f29d3c891"}}, {"head": {"id": "8228ca72-27f3-421a-a7fa-10ed9cd4c399", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072705694100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89b5ff26-c4c0-4a48-b592-b01e37b63fc2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072705839800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2129e42-0259-47fd-a344-514e91807bfa", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072707618100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c1526fd-094a-4cfa-954a-2376568667ea", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072712569100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72cdbac5-6e30-4576-842e-df0af3d66159", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072717017100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07d48c2f-d8e2-4b6a-b3bb-faa59b8a98cc", "name": "entry : default@ProcessResource cost memory 0.162506103515625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072717186700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2d41bcb-90f8-45df-a4e3-477e768df7c6", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072709981300, "endTime": 223072720532700}, "additional": {"logType": "info", "children": [], "durationId": "fefe33fb-f2ba-429c-9da1-130f29d3c891"}}, {"head": {"id": "42387753-2e8f-45ec-b034-74e6505de6ae", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072738389700, "endTime": 223072782630400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ec59275b-8bdb-4f5e-ab9f-c6cd4654cf5d", "logId": "d3412a8f-ae6d-4e08-928a-c16752b6c9a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec59275b-8bdb-4f5e-ab9f-c6cd4654cf5d", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072728359200}, "additional": {"logType": "detail", "children": [], "durationId": "42387753-2e8f-45ec-b034-74e6505de6ae"}}, {"head": {"id": "eb6067cd-234b-44ed-877a-cb28882b5938", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072730728600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b760bde-6bca-4dd7-9962-c378d618a3bb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072730906200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3a1c4ca-b49f-4336-9530-d574cb2d6dfc", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072738415100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c2b1706-ba9c-4ed9-a7bc-8eb779cfd7bc", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 24 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072782271600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b816f27e-ed87-45e3-aaa7-2ffc84371942", "name": "entry : default@GenerateLoaderJson cost memory -4.723930358886719", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072782492100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3412a8f-ae6d-4e08-928a-c16752b6c9a9", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072738389700, "endTime": 223072782630400}, "additional": {"logType": "info", "children": [], "durationId": "42387753-2e8f-45ec-b034-74e6505de6ae"}}, {"head": {"id": "f89d73ca-3e8c-4074-9c0b-d62c764c84d2", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072802168300, "endTime": 223072806516000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "101f6cee-3828-4887-a32d-345d8e00cf27", "logId": "ef368f14-5625-44e3-8f5b-cdcbfd250556"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "101f6cee-3828-4887-a32d-345d8e00cf27", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072800231700}, "additional": {"logType": "detail", "children": [], "durationId": "f89d73ca-3e8c-4074-9c0b-d62c764c84d2"}}, {"head": {"id": "bd77f1b2-4877-45cf-bc25-329a80168274", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072801296900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83ad076c-82d9-4c3b-bbb5-de42ee4fb169", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072801440300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cc205ee-1b5b-491b-b902-2ae09bbd2ba1", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072802208900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60e9b62b-6d86-409c-999e-7bc5608c6b1a", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072806305800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8f4252b-f25e-47d3-9ce6-3ba8f5b1e563", "name": "entry : default@ProcessLibs cost memory 0.1424713134765625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072806443700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef368f14-5625-44e3-8f5b-cdcbfd250556", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072802168300, "endTime": 223072806516000}, "additional": {"logType": "info", "children": [], "durationId": "f89d73ca-3e8c-4074-9c0b-d62c764c84d2"}}, {"head": {"id": "82bb09b8-f381-4ff4-8f76-1a096fa004ac", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072817567800, "endTime": 223072896964100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "30faf13f-935e-40a5-8675-f008ba9357ab", "logId": "77b2d811-8b78-4b9f-8036-e6a9f9216086"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30faf13f-935e-40a5-8675-f008ba9357ab", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072809923100}, "additional": {"logType": "detail", "children": [], "durationId": "82bb09b8-f381-4ff4-8f76-1a096fa004ac"}}, {"head": {"id": "e699180b-413f-405b-b14d-f108638a4b16", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072810867400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a43b9125-d5d9-4215-b8db-bb037c54132a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072811000200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb745ad8-469d-470a-996c-c6ccf3b83f7a", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072812650900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7db4799-64d0-436a-a90b-c1615391f573", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072817603400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4f3e221-ec8f-45ba-8f5c-78b623bb4be1", "name": "Incremental task entry:default@CompileResource pre-execution cost: 77 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072896293200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01628f39-fdb3-407b-88f5-9f54b79bb5d3", "name": "entry : default@CompileResource cost memory 1.3060073852539062", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072896776500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77b2d811-8b78-4b9f-8036-e6a9f9216086", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072817567800, "endTime": 223072896964100}, "additional": {"logType": "info", "children": [], "durationId": "82bb09b8-f381-4ff4-8f76-1a096fa004ac"}}, {"head": {"id": "76f396a2-f9a1-4fab-a519-ee1fcc04b240", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072912945000, "endTime": 223072916656800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "49e014d7-6f72-4475-a87c-53d6142a0c5c", "logId": "b7e4c34f-4f42-405b-b6bb-66e8acec4ebc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49e014d7-6f72-4475-a87c-53d6142a0c5c", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072904982600}, "additional": {"logType": "detail", "children": [], "durationId": "76f396a2-f9a1-4fab-a519-ee1fcc04b240"}}, {"head": {"id": "ea6d2a16-a166-4691-9118-78bd18133f12", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072907404000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a68794c6-c299-4f31-bcdb-44cd08b85b27", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072907626400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa5263c5-785b-4dbc-a9af-919509113839", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072912967100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c4e4c37-1d38-458d-a874-e484d25f917a", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072913783500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b51dcf2-725b-4b53-b3b4-9a52fa97c8e5", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072916294800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf789d1c-341b-43d7-a9bb-0896cc3e329d", "name": "entry : default@DoNativeStrip cost memory 0.07949066162109375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072916505000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7e4c34f-4f42-405b-b6bb-66e8acec4ebc", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072912945000, "endTime": 223072916656800}, "additional": {"logType": "info", "children": [], "durationId": "76f396a2-f9a1-4fab-a519-ee1fcc04b240"}}, {"head": {"id": "567ccde0-25fb-4412-8548-c3a03856e745", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072930656100, "endTime": 223073006024300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "e6559f02-457b-4934-b065-b5a5594b1505", "logId": "75317c6d-f0c0-46a7-979b-526fa8b08797"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6559f02-457b-4934-b065-b5a5594b1505", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072919521100}, "additional": {"logType": "detail", "children": [], "durationId": "567ccde0-25fb-4412-8548-c3a03856e745"}}, {"head": {"id": "775371c6-c8ea-4759-916a-28a171fd45c6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072921163600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d0f8d25-937b-4b97-b9d4-c565ea93f038", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072921324900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5982ee71-d92c-4f27-9fff-aaeb7d219c8b", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072930681900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b70304eb-0964-4853-b2e3-8a5237c3d4d4", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072930964600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53deebc1-4fd8-496c-b800-bce8069af254", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 59 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073005680700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4916a72e-ea71-41c8-a46b-6a89437f3015", "name": "entry : default@CompileArkTS cost memory 1.1887359619140625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073005888300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75317c6d-f0c0-46a7-979b-526fa8b08797", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072930656100, "endTime": 223073006024300}, "additional": {"logType": "info", "children": [], "durationId": "567ccde0-25fb-4412-8548-c3a03856e745"}}, {"head": {"id": "58c606a1-e370-4297-9f42-582fa8dced62", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073033542900, "endTime": 223073046804200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "42577ad6-7167-418b-bbbd-f5013bf81f7d", "logId": "b45907f1-8af3-4a0d-a175-9456a8e2a381"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42577ad6-7167-418b-bbbd-f5013bf81f7d", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073019427500}, "additional": {"logType": "detail", "children": [], "durationId": "58c606a1-e370-4297-9f42-582fa8dced62"}}, {"head": {"id": "f7cc611c-91a2-47b3-9e86-fb6351aae308", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073021974300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8f66634-9e6a-439a-9ed5-cfa0f654f237", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073022179900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "595dd1d9-661f-4912-a7ff-254bce6d8814", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073033564700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77b49455-1313-4bb9-9c66-12a2530f9ca6", "name": "entry : default@BuildJS cost memory 0.3467254638671875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073046449900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7226aa1e-c41d-4df1-8b13-8e873c0b8802", "name": "runTaskFromQueue task cost before running: 1 s 36 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073046688200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b45907f1-8af3-4a0d-a175-9456a8e2a381", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073033542900, "endTime": 223073046804200, "totalTime": 13101600}, "additional": {"logType": "info", "children": [], "durationId": "58c606a1-e370-4297-9f42-582fa8dced62"}}, {"head": {"id": "a0cb20d2-fcdb-408d-a690-37f226d73d28", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073057009700, "endTime": 223073063663700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "6f9e87f1-6f09-4a33-87cb-0b107cef105c", "logId": "d8f6ed12-3348-4c3c-a463-e475c53de3f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f9e87f1-6f09-4a33-87cb-0b107cef105c", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073049802500}, "additional": {"logType": "detail", "children": [], "durationId": "a0cb20d2-fcdb-408d-a690-37f226d73d28"}}, {"head": {"id": "daedbc06-d28c-4ac2-9742-0d6bec5f87a1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073051076900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99c73555-2d30-4290-be1f-4f16d9ecb734", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073051213100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df0c44da-e727-4099-9175-108323c7e388", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073057032700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fee0bccd-f68e-4d40-83e9-cb4a8eca9fe4", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073058776400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08c5d7bf-165d-4e50-a146-1291c9749aa8", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073063336500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba1a7f70-382a-4ac8-bd2f-d4482b3ecc9a", "name": "entry : default@CacheNativeLibs cost memory 0.095367431640625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073063542800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8f6ed12-3348-4c3c-a463-e475c53de3f2", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073057009700, "endTime": 223073063663700}, "additional": {"logType": "info", "children": [], "durationId": "a0cb20d2-fcdb-408d-a690-37f226d73d28"}}, {"head": {"id": "235c0421-f5d0-4e91-ba0d-8b676b603d83", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073068948200, "endTime": 223073072087500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "da4a2c6b-ac2c-4aee-bfa6-7afec49001dc", "logId": "18c82ef0-4127-4338-b3a9-ac26419ac154"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da4a2c6b-ac2c-4aee-bfa6-7afec49001dc", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073066426500}, "additional": {"logType": "detail", "children": [], "durationId": "235c0421-f5d0-4e91-ba0d-8b676b603d83"}}, {"head": {"id": "30b32391-57cb-4c5b-a591-f0de4b92ca5d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073067536900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ecc343a-26f9-4813-acb1-184b51c41f6c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073067686500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bcfe566-56dc-4ae1-a187-8719bea46c79", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073068973200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da9ebb16-9335-41ef-9507-3ee8935d4914", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073069678000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "267da016-0cfe-45af-adf8-5536a35fea91", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073071770500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "356fd12d-e948-460b-8034-af80e4ceaf80", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07503509521484375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073071946500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18c82ef0-4127-4338-b3a9-ac26419ac154", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073068948200, "endTime": 223073072087500}, "additional": {"logType": "info", "children": [], "durationId": "235c0421-f5d0-4e91-ba0d-8b676b603d83"}}, {"head": {"id": "6a8f630a-4409-4bcd-9a55-4404e507ef77", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073098903300, "endTime": 223073168044500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "a1c3a77d-2b8b-4f4f-9a62-e0c205c30980", "logId": "f7675a0d-3c7f-45e1-9e15-a035d4c889a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1c3a77d-2b8b-4f4f-9a62-e0c205c30980", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073075857000}, "additional": {"logType": "detail", "children": [], "durationId": "6a8f630a-4409-4bcd-9a55-4404e507ef77"}}, {"head": {"id": "f1313302-74d8-42b6-9b4a-eb2161e40bce", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073076874600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "670d1441-cbaf-422a-b4e5-f8dcb5011e16", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073077000900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c15d41d1-9431-4ea1-a979-7c728d483e3c", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073098926800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ac5b8e2-d97b-4376-b175-b1a3466a99dc", "name": "Incremental task entry:default@PackageHap pre-execution cost: 63 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073167642100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e186a73-0218-465b-a9de-c7456e8fbb60", "name": "entry : default@PackageHap cost memory -4.915534973144531", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073167912000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7675a0d-3c7f-45e1-9e15-a035d4c889a2", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073098903300, "endTime": 223073168044500}, "additional": {"logType": "info", "children": [], "durationId": "6a8f630a-4409-4bcd-9a55-4404e507ef77"}}, {"head": {"id": "a3a1e9b9-d412-4aa9-8aa0-a265ab36943c", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073182833500, "endTime": 223073186952300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": [], "detailId": "893b8058-1960-4ea4-97c3-c3623f65674e", "logId": "c9471c6a-2934-4e38-8af4-9807d8c14f55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "893b8058-1960-4ea4-97c3-c3623f65674e", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073175727800}, "additional": {"logType": "detail", "children": [], "durationId": "a3a1e9b9-d412-4aa9-8aa0-a265ab36943c"}}, {"head": {"id": "1b91e24d-bd83-49d1-8a29-fa58f6c5ef94", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073177990700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f59e2b0-5f1b-407c-8fcd-e740a8f1d9b1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073178186200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0eb237d4-bc5c-41ca-892a-3a350ab07455", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073182859800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ef77d41-c66b-43d1-a043-045d1cd7c183", "name": "Incremental task entry:default@SignHap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073186586800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43ed6878-dc5b-4f44-a603-415aa4e8b5de", "name": "entry : default@SignHap cost memory 0.10596466064453125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073186817500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9471c6a-2934-4e38-8af4-9807d8c14f55", "name": "UP-TO-DATE :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073182833500, "endTime": 223073186952300}, "additional": {"logType": "info", "children": [], "durationId": "a3a1e9b9-d412-4aa9-8aa0-a265ab36943c"}}, {"head": {"id": "506987cd-6b29-478f-92cb-ac014e1443f6", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073195934200, "endTime": 223073210340900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "403a2c73-d03a-4d97-a896-14e7b693d4fd", "logId": "4ab07273-e3d1-4fdf-b45b-94e2a3544558"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "403a2c73-d03a-4d97-a896-14e7b693d4fd", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073191012400}, "additional": {"logType": "detail", "children": [], "durationId": "506987cd-6b29-478f-92cb-ac014e1443f6"}}, {"head": {"id": "7bf1fc38-ae85-4dd1-9534-764d53bb8ded", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073193952800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "635ac422-fbb0-48eb-9279-2e9e6a8bb5d1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073194159500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "606c29e6-9508-4407-bbeb-3909173d2f43", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073195953900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c2d7f82-d3b2-4d2a-97a6-33d21d4e128f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073209849600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24a7ed0f-c55c-4fc1-87a8-8664ef8510fc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073210005100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a15c468d-aefe-45d5-bf16-84a02d1da6b6", "name": "entry : default@CollectDebugSymbol cost memory 0.2442169189453125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073210139700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ef66a2a-a852-4520-b0b0-3f3945c3c492", "name": "runTaskFromQueue task cost before running: 1 s 200 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073210263300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ab07273-e3d1-4fdf-b45b-94e2a3544558", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073195934200, "endTime": 223073210340900, "totalTime": 14299100}, "additional": {"logType": "info", "children": [], "durationId": "506987cd-6b29-478f-92cb-ac014e1443f6"}}, {"head": {"id": "be6fa84e-23b0-4eb0-ac84-e1df7796546a", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073213067800, "endTime": 223073213557400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "d02a2939-3131-4df8-bda4-1bf739b4c5b0", "logId": "93af69d4-7240-4a96-a899-e60887e20b89"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d02a2939-3131-4df8-bda4-1bf739b4c5b0", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073212977000}, "additional": {"logType": "detail", "children": [], "durationId": "be6fa84e-23b0-4eb0-ac84-e1df7796546a"}}, {"head": {"id": "2fbca10b-449d-4ad0-ba66-413fdc39584a", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073213083900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03e980f8-f2d6-4eca-bf1a-222b1174b9a5", "name": "entry : assembleHap cost memory 0.011749267578125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073213291300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "995fc319-657f-491d-8527-c6cb7cb5bb5b", "name": "runTaskFromQueue task cost before running: 1 s 203 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073213443500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93af69d4-7240-4a96-a899-e60887e20b89", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073213067800, "endTime": 223073213557400, "totalTime": 342800}, "additional": {"logType": "info", "children": [], "durationId": "be6fa84e-23b0-4eb0-ac84-e1df7796546a"}}, {"head": {"id": "6d85781e-85c4-4e69-ac2d-19e66bb082e2", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073233385800, "endTime": 223073233436000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ba36d4f0-ceb1-4f1b-800f-7debfaa8123b", "logId": "f0553b24-bad9-4972-a694-a9017a83fc5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0553b24-bad9-4972-a694-a9017a83fc5e", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073233385800, "endTime": 223073233436000}, "additional": {"logType": "info", "children": [], "durationId": "6d85781e-85c4-4e69-ac2d-19e66bb082e2"}}, {"head": {"id": "3917938d-28b0-4498-9b13-677954be8aba", "name": "BUILD SUCCESSFUL in 1 s 223 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073233556700}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "d6ea5d7a-2be8-45a4-8851-1a9c1ab552eb", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223072010737000, "endTime": 223073234286300}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 19, "minute": 55, "second": 3}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "3ebcd05c-8949-4042-8ab0-ff6c3d1064bf", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073234323400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "766df390-060f-4418-8de6-1a685b36c46d", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073234632200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdfe84b3-8a5e-4381-9d13-577add92afdc", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073235414000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fb70962-13cd-4fdf-9dc5-e65774c2bbd8", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073235586000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55bdee18-61bd-460a-a6cd-aa5164b60502", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073235686200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42963e64-2c0b-4d87-94bf-39253191d7ad", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073235765000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c216ff2b-6568-46e2-94b5-91e7f8a3d287", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073235839200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ccc52b7-8bd0-4e94-94be-50cc7421b330", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073237150300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcc09473-b965-4b73-ace1-5564012d4e07", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073237793300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36393a2b-482b-4815-8675-c05bf7824e16", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073238004400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ee48514-4a63-4e81-a4cf-ddc621a3dfd3", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073238087100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5b7af7d-c279-4cd8-8d5a-1412ea349c62", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073238155300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d556ce69-8932-4156-b1ab-25b15598f478", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073238236300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18d44181-2370-48bf-b94a-95e4cbe7c6b4", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073240499200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1aab16ec-11d8-4fcf-817b-ee31b31b57d4", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073240996000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d0132b7-2573-4f91-8462-3e8b87d1f5fa", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073241460900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edd988d4-41c4-4492-a932-b1080d926f5e", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073241584900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b20d4ba-241a-4fb1-b0f1-572a354325ed", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073241685200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f2bb415-7dc7-452c-9601-71b382f807f6", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073241722300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f014c7d4-3b3b-48a1-8fbc-42f39bdd5bdd", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073241752400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46ff0dc0-c00a-42a4-93d2-bffe5baf5a77", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073241780500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cb87a39-f5c4-44b4-aa65-8e40d4613caa", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073241809100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3a35e17-8a7f-42f7-b4da-d970e50d0532", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073245035200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b966e16-7d1f-42fc-9570-134a24fe67e6", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073246547700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cdb9357-de5d-42e6-ae5d-e46c1439ee2e", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073247419000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17243947-f0bb-4df9-b3be-812036ab5b37", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073247943300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd10ff6c-e4df-4088-8b72-36e8400ba1d3", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073248444400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7174730d-f57d-46a9-99a2-0dc719087821", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073249914700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "869cbb49-1486-4c44-abb2-04e00c72a921", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073252221600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7863c9dd-dda5-4d24-b025-7a3ae1e80c7a", "name": "Incremental task entry:default@BuildJS post-execution cost:11 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073252813500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ecb46e5-df47-4e16-95e4-63871a32c6b3", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073252994900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f96f6404-dfda-46ba-9b67-5bdc922d8ca9", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073253107100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "867fdc69-d904-4a55-9eef-ebbe2a4a65e9", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073253215400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ae24336-c84c-4727-8e77-9e2c8aaabb7b", "name": "There is no need to refresh cache, since the incremental task entry:default@SignHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073253298700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e497d0f-7650-498c-9eb9-f7855f2d472e", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073259165000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7d66ef9-6419-4409-80b7-3550522fc95f", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073259716800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31db933a-3bcc-4ed4-8cac-505429a0c440", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073260860700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efdd94ed-b2f7-494a-a5e6-9db6733d0b16", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073261311700}, "additional": {"logType": "debug", "children": []}}], "workLog": []}