import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';
import { ApiService } from '../services/ApiService';
import { LoadingView, LoadingState } from '../components/LoadingView';
import { AppModel } from '../models/App';
import { router } from '@kit.ArkUI';
import { preferences } from '@kit.ArkData';
import { hilog } from '@kit.PerformanceAnalysisKit';

/**
 * 浏览历史项模型
 */
interface HistoryItem {
  app: AppModel;
  viewTime: string;
  viewCount: number;
}

/**
 * 浏览历史页面
 */
@Entry
@Component
struct HistoryPage {
  @State historyItems: HistoryItem[] = [];
  @State loadingState: LoadingState = LoadingState.LOADING;
  @State selectedPeriod: string = '全部';
  @State periods: string[] = ['全部', '今天', '昨天', '本周', '本月'];
  @State isEditMode: boolean = false;
  @State selectedItems: Set<number> = new Set();

  private deviceUtils = DeviceUtils.getInstance();
  private apiService = ApiService.getInstance();

  aboutToAppear() {
    this.loadBrowseHistory();
  }

  /**
   * 加载浏览历史
   */
  private async loadBrowseHistory() {
    try {
      this.loadingState = LoadingState.LOADING;
      // 这里应该从本地存储或API获取浏览历史
      // 暂时使用模拟数据
      await this.simulateApiCall();
      this.historyItems = this.getMockHistoryItems();
      this.loadingState = LoadingState.SUCCESS;
    } catch (error) {
      hilog.error(0x0000, 'HistoryPage', '加载浏览历史失败: %{public}s', JSON.stringify(error));
      this.loadingState = LoadingState.ERROR;
    }
  }

  /**
   * 模拟API调用
   */
  private async simulateApiCall(): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 1000);
    });
  }

  /**
   * 获取模拟历史数据
   */
  private getMockHistoryItems(): HistoryItem[] {
    const mockApps: AppModel[] = [
      {
        id: 1,
        created_at: '2024-01-15',
        updated_at: '2024-01-15',
        name: '微信',
        package_name: 'com.tencent.mm',
        description: '一个为智能终端提供即时通讯服务的免费应用程序',
        short_description: '即时通讯服务',
        icon: Constants.PLACEHOLDER_IMAGE,
        category_id: 1,
        category_name: '社交',
        developer_id: 1,
        developer_name: '腾讯科技',
        version: '8.0.32',
        version_code: 1,
        min_sdk_version: 21,
        target_sdk_version: 33,
        size: 245760000,
        download_url: '',
        download_count: 1000000000,
        rating: 4.8,
        review_count: 500000,
        screenshots: [Constants.PLACEHOLDER_IMAGE],
        permissions: [],
        tags: ['社交', '通讯'],
        changelog: '优化用户体验',
        privacy_policy: 'https://example.com/privacy',
        support_email: '<EMAIL>',
        website: 'https://example.com',
        status: 'published',
        is_featured: true,
        is_editor_choice: true,
        is_top: true,
        published_at: '2024-01-15',
        review_status: 'approved',
        review_reason: '',
        reviewed_at: '2024-01-15',
        reviewer_id: 1
      },
      {
        id: 2,
        name: '支付宝',
        description: '数字生活开放平台',
        icon: Constants.PLACEHOLDER_IMAGE,
        version: '10.3.20',
        size: 156672000,
        rating: 4.7,
        download_count: 800000000,
        category_id: 2,
        category_name: '金融',
        screenshots: [Constants.PLACEHOLDER_IMAGE],
        developer_id: 2,
        developer_name: '蚂蚁集团',
        created_at: '2024-01-10',
        updated_at: '2024-01-10',
        download_url: '',
        package_name: 'com.eg.android.AlipayGphone',
        version_code: 1,
        min_sdk_version: 21,
        target_sdk_version: 33,
        review_count: 500000,
        changelog: '优化用户体验',
        short_description: '数字生活开放平台',
        privacy_policy: 'https://example.com/privacy',
        support_email: '<EMAIL>',
        website: 'https://example.com',
        status: 'published',
        is_featured: true,
        is_editor_choice: true,
        is_top: true,
        published_at: '2024-01-10',
        review_status: 'approved',
        review_reason: '',
        reviewed_at: '2024-01-10',
        reviewer_id: 1,
        permissions: [],
        tags: ['支付', '金融']
      },
      {
        id: 3,
        name: '王者荣耀',
        description: '5v5英雄公平对战手游',
        icon: Constants.PLACEHOLDER_IMAGE,
        version: '********',
        size: 1887436800,
        rating: 4.5,
        download_count: 500000000,
        category_id: 1,
        category_name: '游戏',
        screenshots: [Constants.PLACEHOLDER_IMAGE],
        developer_id: 1,
        developer_name: '腾讯游戏',
        created_at: '2024-01-12',
        updated_at: '2024-01-12',
        download_url: '',
        package_name: 'com.tencent.tmgp.sgame',
        version_code: 1,
        min_sdk_version: 21,
        target_sdk_version: 33,
        review_count: 1000000,
        changelog: '修复已知问题',
        short_description: '5v5英雄公平对战手游',
        privacy_policy: 'https://example.com/privacy',
        support_email: '<EMAIL>',
        website: 'https://example.com',
        status: 'published',
        is_featured: true,
        is_editor_choice: false,
        is_top: true,
        published_at: '2024-01-12',
        review_status: 'approved',
        review_reason: '',
        reviewed_at: '2024-01-12',
        reviewer_id: 1,
        permissions: [],
        tags: ['MOBA', '竞技']
      }
    ];

    return [
      {
        app: mockApps[0],
        viewTime: '2024-01-22 16:45:00',
        viewCount: 3
      },
      {
        app: mockApps[1],
        viewTime: '2024-01-22 14:20:00',
        viewCount: 1
      },
      {
        app: mockApps[2],
        viewTime: '2024-01-21 20:30:00',
        viewCount: 5
      }
    ];
  }

  /**
   * 获取过滤后的历史记录
   */
  private getFilteredHistory(): HistoryItem[] {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    const weekStart = new Date(today.getTime() - (today.getDay() || 7) * 24 * 60 * 60 * 1000);
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

    return this.historyItems.filter(item => {
      const viewDate = new Date(item.viewTime);
      
      switch (this.selectedPeriod) {
        case '今天':
          return viewDate >= today;
        case '昨天':
          return viewDate >= yesterday && viewDate < today;
        case '本周':
          return viewDate >= weekStart;
        case '本月':
          return viewDate >= monthStart;
        default:
          return true;
      }
    });
  }

  /**
   * 跳转到应用详情页面
   */
  private navigateToAppDetail(app: AppModel) {
    if (!this.isEditMode) {
     this.getUIContext().getRouter().pushUrl({
          url: 'pages/AppDetailPage',
          params: { appId: app.id.toString() }
        });
    }
  }

  /**
   * 切换编辑模式
   */
  private toggleEditMode() {
    this.isEditMode = !this.isEditMode;
    if (!this.isEditMode) {
      this.selectedItems.clear();
    }
  }

  /**
   * 选择/取消选择项目
   */
  private toggleSelectItem(appId: number) {
    if (this.selectedItems.has(appId)) {
      this.selectedItems.delete(appId);
    } else {
      this.selectedItems.add(appId);
    }
  }

  /**
   * 全选/取消全选
   */
  private toggleSelectAll() {
    const filteredHistory = this.getFilteredHistory();
    if (this.selectedItems.size === filteredHistory.length) {
      this.selectedItems.clear();
    } else {
      this.selectedItems.clear();
      filteredHistory.forEach(item => {
        this.selectedItems.add(item.app.id);
      });
    }
  }

  /**
   * 删除选中的历史记录
   */
  private async deleteSelectedItems() {
    try {
      // 这里应该调用API删除历史记录
      // 暂时直接从列表中移除
      this.historyItems = this.historyItems.filter(item => !this.selectedItems.has(item.app.id));
      this.selectedItems.clear();
      this.isEditMode = false;
    } catch (error) {
      hilog.error(0x0000, 'HistoryPage', '删除历史记录失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 清空所有历史记录
   */
  private async clearAllHistory() {
    try {
      // 这里应该调用API清空历史记录
      // 暂时直接清空列表
      this.historyItems = [];
      this.selectedItems.clear();
      this.isEditMode = false;
    } catch (error) {
      hilog.error(0x0000, 'HistoryPage', '清空历史记录失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 时间段标签栏
   */
  @Builder
  private PeriodTabs() {
    Scroll() {
      Row({ space: 8 }) {
        ForEach(this.periods, (period: string) => {
          Text(period)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(this.selectedPeriod === period ? Constants.COLORS.WHITE : Constants.COLORS.TEXT_SECONDARY)
            .backgroundColor(this.selectedPeriod === period ? Constants.COLORS.PRIMARY : $r('app.color.overlay_light'))
            .padding({ left: 12, right: 12, top: 6, bottom: 6 })
            .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
            .onClick(() => {
              this.selectedPeriod = period;
            })
        }, (period: string) => period)
      }
      .padding({ left: 16, right: 16 })
    }
    .scrollable(ScrollDirection.Horizontal)
    .scrollBar(BarState.Off)
    .width('100%')
    .backgroundColor(Constants.COLORS.WHITE)
    .padding({ top: 12, bottom: 12 })
  }

  /**
   * 历史记录项
   */
  @Builder
  private HistoryItem(item: HistoryItem) {
    Row({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) }) {
      // 编辑模式下的选择框
      if (this.isEditMode) {
        Text(this.selectedItems.has(item.app.id) ? '☑️' : '☐')
          .fontSize(20)
          .onClick(() => {
            this.toggleSelectItem(item.app.id);
          })
      }

      // 应用图标
      Image(item.app.icon)
        .width(this.deviceUtils.isTablet() ? 64 : 56)
        .height(this.deviceUtils.isTablet() ? 64 : 56)
        .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
        .objectFit(ImageFit.Cover)
        .backgroundColor(Constants.COLORS.BACKGROUND_LIGHT)

      // 应用信息
      Column({ space: 4 }) {
        Row() {
          Text(item.app.name)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .fontWeight(FontWeight.Medium)
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })
            .layoutWeight(1)

          Text(item.app.category_name)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.PRIMARY)
            .backgroundColor($r('app.color.overlay_medium'))
            .padding({ left: 8, right: 8, top: 2, bottom: 2 })
            .borderRadius(Constants.BORDER_RADIUS.SMALL)
        }
        .width('100%')

        Text(item.app.description)
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
          .maxLines(1)
          .textOverflow({ overflow: TextOverflow.Ellipsis })

        Row({ space: 8 }) {
          Row({ space: 4 }) {
            Text('⭐')
              .fontSize(12)
            Text((item.app.rating || 0).toString())
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
              .fontColor(Constants.COLORS.TEXT_HINT)
          }

          Text(`${((item.app.size || 0) / 1024 / 1024).toFixed(1)}MB`)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.TEXT_HINT)

          Text(`浏览${item.viewCount}次`)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.TEXT_HINT)

          Text(this.formatViewTime(item.viewTime))
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.TEXT_HINT)
        }
      }
      .alignItems(HorizontalAlign.Start)
      .layoutWeight(1)
    }
    .width('100%')
    .padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
    .backgroundColor(Constants.COLORS.WHITE)
    .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
    .margin({ left: 16, right: 16, bottom: 8 })
    .onClick(() => this.navigateToAppDetail(item.app))
  }

  /**
   * 格式化浏览时间
   */
  private formatViewTime(viewTime: string): string {
    const now = new Date();
    const viewDate = new Date(viewTime);
    const diffMs = now.getTime() - viewDate.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffHours < 1) {
      return '刚刚';
    } else if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return viewTime.split(' ')[0];
    }
  }

  /**
   * 历史记录列表
   */
  @Builder
  private HistoryList() {
    if (this.getFilteredHistory().length === 0) {
      Column({ space: 16 }) {
        Text('🕒')
          .fontSize(48)
          .fontColor(Constants.COLORS.TEXT_HINT)
        
        Text(this.selectedPeriod === '全部' ? '暂无浏览记录' : `${this.selectedPeriod}暂无浏览记录`)
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
        
        Text('去应用商店发现更多精彩应用')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.TEXT_HINT)
      }
      .width('100%')
      .height(200)
      .justifyContent(FlexAlign.Center)
      .alignItems(HorizontalAlign.Center)
    } else {
      Column() {
        ForEach(this.getFilteredHistory(), (item: HistoryItem) => {
          this.HistoryItem(item)
        }, (item: HistoryItem) => item.app.id.toString())
      }
      .width('100%')
    }
  }

  build() {
    Column() {
      // 标题栏
      Row() {
        Text('←')
          .fontSize(24)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .onClick(() => {
            this.getUIContext().getRouter().back();
          })

        Text('浏览历史')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .fontWeight(FontWeight.Bold)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        if (this.historyItems.length > 0) {
          Text(this.isEditMode ? '完成' : '编辑')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor(Constants.COLORS.PRIMARY)
            .onClick(() => {
              this.toggleEditMode();
            })
        } else {
          Text('')
            .width(32)
        }
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor(Constants.COLORS.WHITE)
      .alignItems(VerticalAlign.Center)

      // 编辑模式下的操作栏
      if (this.isEditMode && this.historyItems.length > 0) {
        Row() {
          Text(this.selectedItems.size === this.getFilteredHistory().length ? '取消全选' : '全选')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor(Constants.COLORS.PRIMARY)
            .onClick(() => {
              this.toggleSelectAll();
            })

          Blank()

          Row({ space: 16 }) {
            if (this.selectedItems.size > 0) {
              Text('删除选中')
                .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
                .fontColor(Constants.COLORS.ERROR)
                .onClick(() => {
                  this.deleteSelectedItems();
                })
            }

            Text('清空全部')
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
              .fontColor(Constants.COLORS.ERROR)
              .onClick(() => {
                this.clearAllHistory();
              })
          }
        }
        .width('100%')
        .padding({ left: 16, right: 16, top: 8, bottom: 8 })
        .backgroundColor(Constants.COLORS.WHITE)
        .border({ width: { top: 1 }, color: Constants.COLORS.BORDER })
      }

      if (this.loadingState === LoadingState.LOADING) {
        LoadingView({ state: LoadingState.LOADING })
          .layoutWeight(1)
      } else if (this.loadingState === LoadingState.ERROR) {
        LoadingView({ 
          state: LoadingState.ERROR,
          onRetry: (): Promise<void> => this.loadBrowseHistory()
        })
          .layoutWeight(1)
      } else {
        Column() {
          // 时间段标签栏
          this.PeriodTabs()
          
          Divider()
            .color(Constants.COLORS.BORDER)

          // 历史记录列表
          Scroll() {
            Column() {
              this.HistoryList()
              
              // 底部间距
              Column()
                .height(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE))
            }
          }
          .scrollable(ScrollDirection.Vertical)
          .scrollBar(BarState.Auto)
          .layoutWeight(1)
        }
        .layoutWeight(1)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Constants.COLORS.BACKGROUND)
  }
}

export { HistoryPage };