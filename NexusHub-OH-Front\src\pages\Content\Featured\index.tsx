import { <PERSON>Container } from '@ant-design/pro-components';
import { Card, Table, Button, Tag, Switch, Space, Modal, Form, Input, Upload, Select, message, Tabs, Row, Col, Tooltip, Image } from 'antd';
import { PlusOutlined, UploadOutlined, DeleteOutlined, EditOutlined, EyeOutlined, DragOutlined, ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { useRequest } from '@umijs/max';
import type { ColumnsType } from 'antd/es/table';
import type { UploadFile } from 'antd/es/upload/interface';
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { arrayMove, SortableContext, sortableKeyboardCoordinates, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface FeaturedItem {
  id: string;
  title: string;
  subtitle?: string;
  imageUrl: string;
  targetType: 'app' | 'category' | 'url' | 'activity';
  targetId?: string;
  targetUrl?: string;
  position: number;
  startTime?: string;
  endTime?: string;
  status: 'active' | 'inactive' | 'scheduled';
  createdTime: string;
  createdBy: string;
  section: 'banner' | 'featured' | 'new' | 'popular';
}

// 模拟数据获取函数
const fetchFeaturedItems = async (params: any) => {
  // 实际项目中应该调用API
  console.log('Fetching featured items with params:', params);
  
  // 模拟数据
  const mockData: FeaturedItem[] = [
    {
      id: '1',
      title: '新游戏上线',
      subtitle: '体验全新3D游戏',
      imageUrl: 'https://gw.alipayobjects.com/zos/rmsportal/JiqGstEfoWAOHiTxclqi.png',
      targetType: 'app',
      targetId: 'app001',
      position: 1,
      startTime: '2023-05-01 00:00:00',
      endTime: '2023-06-01 00:00:00',
      status: 'active',
      createdTime: '2023-04-28 10:00:00',
      createdBy: 'admin',
      section: 'banner',
    },
    {
      id: '2',
      title: '热门游戏专区',
      subtitle: '本周最热门游戏',
      imageUrl: 'https://gw.alipayobjects.com/zos/rmsportal/JiqGstEfoWAOHiTxclqi.png',
      targetType: 'category',
      targetId: 'cat001',
      position: 2,
      status: 'active',
      createdTime: '2023-04-27 14:30:00',
      createdBy: 'admin',
      section: 'banner',
    },
    {
      id: '3',
      title: '618促销活动',
      subtitle: '限时折扣',
      imageUrl: 'https://gw.alipayobjects.com/zos/rmsportal/JiqGstEfoWAOHiTxclqi.png',
      targetType: 'activity',
      targetId: 'act001',
      position: 3,
      startTime: '2023-06-01 00:00:00',
      endTime: '2023-06-18 23:59:59',
      status: 'scheduled',
      createdTime: '2023-05-15 09:20:00',
      createdBy: 'admin',
      section: 'banner',
    },
    {
      id: '4',
      title: '超级游戏A',
      imageUrl: 'https://gw.alipayobjects.com/zos/rmsportal/JiqGstEfoWAOHiTxclqi.png',
      targetType: 'app',
      targetId: 'app002',
      position: 1,
      status: 'active',
      createdTime: '2023-05-10 11:30:00',
      createdBy: 'admin',
      section: 'featured',
    },
    {
      id: '5',
      title: '效率工具B',
      imageUrl: 'https://gw.alipayobjects.com/zos/rmsportal/JiqGstEfoWAOHiTxclqi.png',
      targetType: 'app',
      targetId: 'app003',
      position: 2,
      status: 'active',
      createdTime: '2023-05-09 16:45:00',
      createdBy: 'admin',
      section: 'featured',
    },
  ];

  // 根据section过滤
  const filteredData = params.section ? mockData.filter(item => item.section === params.section) : mockData;
  
  // 根据状态过滤
  const statusFilteredData = params.status ? filteredData.filter(item => item.status === params.status) : filteredData;
  
  // 排序
  const sortedData = statusFilteredData.sort((a, b) => a.position - b.position);

  return { data: sortedData, total: sortedData.length };
};

// 可拖拽的表格行组件
const SortableRow = ({ children, ...props }: any) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: props['data-row-key'],
  });
  
  const style = {
    ...props.style,
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    cursor: 'move',
    backgroundColor: isDragging ? '#f5f5f5' : undefined,
  };

  return <tr {...props} ref={setNodeRef} style={style} {...attributes} {...listeners}>{children}</tr>;
};

const FeaturedContent: React.FC = () => {
  const [activeTab, setActiveTab] = useState('banner');
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [currentItem, setCurrentItem] = useState<FeaturedItem | null>(null);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [editForm] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  // 传感器配置，用于拖拽功能
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const { data, loading, refresh } = useRequest(() => fetchFeaturedItems({
    section: activeTab,
  }), {
    refreshDeps: [activeTab],
  });

  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  const handleAdd = () => {
    setCurrentItem(null);
    editForm.resetFields();
    setFileList([]);
    setEditModalVisible(true);
  };

  const handleEdit = (record: FeaturedItem) => {
    setCurrentItem(record);
    editForm.setFieldsValue({
      title: record.title,
      subtitle: record.subtitle,
      targetType: record.targetType,
      targetId: record.targetId,
      targetUrl: record.targetUrl,
      startTime: record.startTime ? record.startTime : undefined,
      endTime: record.endTime ? record.endTime : undefined,
      status: record.status,
    });
    setFileList(record.imageUrl ? [{
      uid: '-1',
      name: 'image.png',
      status: 'done',
      url: record.imageUrl,
    }] : []);
    setEditModalVisible(true);
  };

  const handlePreview = (imageUrl: string) => {
    setPreviewImage(imageUrl);
    setPreviewVisible(true);
  };

  const handleStatusChange = (checked: boolean, record: FeaturedItem) => {
    console.log(`Change status of ${record.id} to ${checked ? 'active' : 'inactive'}`);
    // 实际项目中应该调用API
    message.success(`状态已更新为${checked ? '启用' : '禁用'}`);
    refresh();
  };

  const handleDelete = (record: FeaturedItem) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除「${record.title}」吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        console.log(`Delete item ${record.id}`);
        // 实际项目中应该调用API
        message.success('删除成功');
        refresh();
      },
    });
  };

  const handleEditSubmit = () => {
    editForm.validateFields().then(values => {
      console.log('Form values:', values);
      // 实际项目中应该调用API
      message.success(currentItem ? '更新成功' : '添加成功');
      setEditModalVisible(false);
      refresh();
    });
  };

  const handleDragEnd = (event: any) => {
    const { active, over } = event;
    
    if (active.id !== over.id) {
      const activeIndex = data?.data.findIndex((item: FeaturedItem) => item.id === active.id);
      const overIndex = data?.data.findIndex((item: FeaturedItem) => item.id === over.id);
      
      if (activeIndex !== -1 && overIndex !== -1) {
        console.log(`Move item from position ${activeIndex + 1} to ${overIndex + 1}`);
        // 实际项目中应该调用API更新位置
        message.success('排序已更新');
        refresh();
      }
    }
  };

  const handleMoveUp = (record: FeaturedItem) => {
    if (record.position <= 1) return;
    console.log(`Move item ${record.id} up`);
    // 实际项目中应该调用API
    message.success('已上移');
    refresh();
  };

  const handleMoveDown = (record: FeaturedItem) => {
    if (!data?.data || record.position >= data.data.length) return;
    console.log(`Move item ${record.id} down`);
    // 实际项目中应该调用API
    message.success('已下移');
    refresh();
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'active':
        return <Tag color="success">启用</Tag>;
      case 'inactive':
        return <Tag color="default">禁用</Tag>;
      case 'scheduled':
        return <Tag color="processing">定时</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  const columns: ColumnsType<FeaturedItem> = [
    {
      title: '排序',
      key: 'sort',
      width: 80,
      render: (_, record) => (
        <Space>
          <Tooltip title="上移">
            <Button 
              type="text" 
              icon={<ArrowUpOutlined />} 
              disabled={record.position <= 1}
              onClick={() => handleMoveUp(record)}
            />
          </Tooltip>
          <Tooltip title="下移">
            <Button 
              type="text" 
              icon={<ArrowDownOutlined />} 
              disabled={!data?.data || record.position >= data.data.length}
              onClick={() => handleMoveDown(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
    {
      title: '位置',
      dataIndex: 'position',
      key: 'position',
      width: 60,
    },
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: '副标题',
      dataIndex: 'subtitle',
      key: 'subtitle',
      render: (subtitle) => subtitle || '-',
    },
    {
      title: '图片',
      dataIndex: 'imageUrl',
      key: 'imageUrl',
      render: (imageUrl) => (
        <div style={{ width: 60, height: 60, overflow: 'hidden' }}>
          <img 
            src={imageUrl} 
            alt="预览图" 
            style={{ width: '100%', height: '100%', objectFit: 'cover', cursor: 'pointer' }}
            onClick={() => handlePreview(imageUrl)}
          />
        </div>
      ),
    },
    {
      title: '目标类型',
      dataIndex: 'targetType',
      key: 'targetType',
      render: (targetType) => {
        const typeMap = {
          app: '应用',
          category: '分类',
          url: '链接',
          activity: '活动',
        };
        return typeMap[targetType as keyof typeof typeMap] || targetType;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => (
        <Space>
          {getStatusTag(status)}
          <Switch 
            checked={status === 'active'} 
            onChange={(checked) => handleStatusChange(checked, record)}
            size="small"
          />
        </Space>
      ),
    },
    {
      title: '时间设置',
      key: 'time',
      render: (_, record) => (
        <div>
          {record.startTime ? (
            <div>开始: {record.startTime}</div>
          ) : (
            <div>开始: 无限制</div>
          )}
          {record.endTime ? (
            <div>结束: {record.endTime}</div>
          ) : (
            <div>结束: 无限制</div>
          )}
        </div>
      ),
    },
    {
      title: '创建信息',
      key: 'created',
      render: (_, record) => (
        <div>
          <div>创建人: {record.createdBy}</div>
          <div>创建时间: {record.createdTime}</div>
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <a onClick={() => handleEdit(record)}><EditOutlined /> 编辑</a>
          <a onClick={() => handleDelete(record)}><DeleteOutlined /> 删除</a>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer
      header={{
        title: '首页推荐设置',
      }}
    >
      <Card bordered={false}>
        <Tabs 
          activeKey={activeTab} 
          onChange={handleTabChange}
          tabBarExtraContent={
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              添加内容
            </Button>
          }
        >
          <Tabs.TabPane tab="轮播图" key="banner">
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={data?.data?.map((item: FeaturedItem) => item.id) || []}
                strategy={verticalListSortingStrategy}
              >
                <Table
                  columns={columns}
                  dataSource={data?.data}
                  rowKey="id"
                  loading={loading}
                  pagination={false}
                  components={{
                    body: {
                      row: SortableRow,
                    },
                  }}
                />
              </SortableContext>
            </DndContext>
          </Tabs.TabPane>
          <Tabs.TabPane tab="精选应用" key="featured">
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={data?.data?.map((item: FeaturedItem) => item.id) || []}
                strategy={verticalListSortingStrategy}
              >
                <Table
                  columns={columns}
                  dataSource={data?.data}
                  rowKey="id"
                  loading={loading}
                  pagination={false}
                  components={{
                    body: {
                      row: SortableRow,
                    },
                  }}
                />
              </SortableContext>
            </DndContext>
          </Tabs.TabPane>
          <Tabs.TabPane tab="新品推荐" key="new">
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={data?.data?.map((item: FeaturedItem) => item.id) || []}
                strategy={verticalListSortingStrategy}
              >
                <Table
                  columns={columns}
                  dataSource={data?.data}
                  rowKey="id"
                  loading={loading}
                  pagination={false}
                  components={{
                    body: {
                      row: SortableRow,
                    },
                  }}
                />
              </SortableContext>
            </DndContext>
          </Tabs.TabPane>
          <Tabs.TabPane tab="热门榜单" key="popular">
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={data?.data?.map((item: FeaturedItem) => item.id) || []}
                strategy={verticalListSortingStrategy}
              >
                <Table
                  columns={columns}
                  dataSource={data?.data}
                  rowKey="id"
                  loading={loading}
                  pagination={false}
                  components={{
                    body: {
                      row: SortableRow,
                    },
                  }}
                />
              </SortableContext>
            </DndContext>
          </Tabs.TabPane>
        </Tabs>
      </Card>

      {/* 编辑弹窗 */}
      <Modal
        title={currentItem ? '编辑推荐内容' : '添加推荐内容'}
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
        width={700}
      >
        <Form form={editForm} layout="vertical" onFinish={handleEditSubmit}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="title"
                label="标题"
                rules={[{ required: true, message: '请输入标题' }]}
              >
                <Input placeholder="请输入标题" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="subtitle"
                label="副标题"
              >
                <Input placeholder="请输入副标题" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="image"
            label="图片"
            rules={[{ required: true, message: '请上传图片' }]}
          >
            <Upload
              listType="picture-card"
              fileList={fileList}
              beforeUpload={() => false}
              onChange={({ fileList }) => setFileList(fileList)}
              onPreview={() => handlePreview(fileList[0]?.url || fileList[0]?.thumbUrl || '')}
              maxCount={1}
            >
              {fileList.length === 0 && (
                <div>
                  <UploadOutlined />
                  <div style={{ marginTop: 8 }}>上传图片</div>
                </div>
              )}
            </Upload>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="targetType"
                label="目标类型"
                rules={[{ required: true, message: '请选择目标类型' }]}
              >
                <Select placeholder="请选择目标类型">
                  <Select.Option value="app">应用</Select.Option>
                  <Select.Option value="category">分类</Select.Option>
                  <Select.Option value="url">链接</Select.Option>
                  <Select.Option value="activity">活动</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) => prevValues.targetType !== currentValues.targetType}
              >
                {({ getFieldValue }) => {
                  const targetType = getFieldValue('targetType');
                  if (targetType === 'app') {
                    return (
                      <Form.Item
                        name="targetId"
                        label="选择应用"
                        rules={[{ required: true, message: '请选择应用' }]}
                      >
                        <Select placeholder="请选择应用">
                          <Select.Option value="app001">超级游戏A</Select.Option>
                          <Select.Option value="app002">效率工具B</Select.Option>
                          <Select.Option value="app003">社交平台C</Select.Option>
                        </Select>
                      </Form.Item>
                    );
                  }
                  if (targetType === 'category') {
                    return (
                      <Form.Item
                        name="targetId"
                        label="选择分类"
                        rules={[{ required: true, message: '请选择分类' }]}
                      >
                        <Select placeholder="请选择分类">
                          <Select.Option value="cat001">游戏</Select.Option>
                          <Select.Option value="cat002">工具</Select.Option>
                          <Select.Option value="cat003">社交</Select.Option>
                        </Select>
                      </Form.Item>
                    );
                  }
                  if (targetType === 'url') {
                    return (
                      <Form.Item
                        name="targetUrl"
                        label="目标链接"
                        rules={[{ required: true, message: '请输入目标链接' }]}
                      >
                        <Input placeholder="请输入目标链接" />
                      </Form.Item>
                    );
                  }
                  if (targetType === 'activity') {
                    return (
                      <Form.Item
                        name="targetId"
                        label="选择活动"
                        rules={[{ required: true, message: '请选择活动' }]}
                      >
                        <Select placeholder="请选择活动">
                          <Select.Option value="act001">618促销活动</Select.Option>
                          <Select.Option value="act002">新品发布会</Select.Option>
                          <Select.Option value="act003">开发者大会</Select.Option>
                        </Select>
                      </Form.Item>
                    );
                  }
                  return null;
                }}
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="startTime"
                label="开始时间"
              >
                <Input placeholder="格式: YYYY-MM-DD HH:MM:SS" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="endTime"
                label="结束时间"
              >
                <Input placeholder="格式: YYYY-MM-DD HH:MM:SS" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select placeholder="请选择状态">
              <Select.Option value="active">启用</Select.Option>
              <Select.Option value="inactive">禁用</Select.Option>
              <Select.Option value="scheduled">定时</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="section"
            label="所属区域"
            rules={[{ required: true, message: '请选择所属区域' }]}
            initialValue={activeTab}
          >
            <Select placeholder="请选择所属区域">
              <Select.Option value="banner">轮播图</Select.Option>
              <Select.Option value="featured">精选应用</Select.Option>
              <Select.Option value="new">新品推荐</Select.Option>
              <Select.Option value="popular">热门榜单</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                提交
              </Button>
              <Button onClick={() => setEditModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 图片预览 */}
      <Modal
        open={previewVisible}
        footer={null}
        onCancel={() => setPreviewVisible(false)}
      >
        <img alt="预览" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </PageContainer>
  );
};

export default FeaturedContent;