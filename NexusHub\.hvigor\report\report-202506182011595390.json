{"version": "2.0", "ppid": 32784, "events": [{"head": {"id": "d138e38f-2577-4bc2-9e77-f6e3a5e9c7d5", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072819117000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "061f48fc-c0cd-4155-b069-4407e7b9e9ac", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072819533400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69d06a09-08c4-4710-a64d-593c0d4acae1", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072819711400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e253c42-c5c6-4273-9588-9666d63e47a2", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072822224700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb736960-466a-491a-aa3c-6509c9db6c20", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072822600300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2de1266-2332-490e-8790-08579d37d810", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072824556800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f228ad88-921d-4460-ad8b-f9e6ae47a507", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072824895200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46a98e37-08eb-4daa-a516-b8ca18d6c113", "name": "worker[18] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072826448500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f360994-e70b-4f86-8b86-637a3342925b", "name": "worker[4] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072876499500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8aed5d30-9d07-47b6-9283-fec22db350b7", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089692088700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2a43be1-0a1f-4b4f-a550-c4bc7a07f554", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089709392500, "endTime": 224090067545600}, "additional": {"children": ["41a111bc-044f-47c2-a4df-1af75a50aee1", "244905b8-ad9c-4d03-9bf6-064b71d8d2b0", "855257d2-fc52-4a0e-b144-208816f0a6c2", "2e27a94c-2a6d-44d2-9e81-b3b3f25deb25", "486c4519-bcdc-42fe-a78b-96e06ca4037d", "a619631f-3aeb-4656-bc34-06d543aa433e", "020998d4-29e2-406e-ba57-dde39fd91142"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "3acbd4b5-15ba-4a69-abd1-1ea2f601a807"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41a111bc-044f-47c2-a4df-1af75a50aee1", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089709396600, "endTime": 224089737249800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f2a43be1-0a1f-4b4f-a550-c4bc7a07f554", "logId": "b27df13d-0662-434a-aafc-4c725a9b1c4b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "244905b8-ad9c-4d03-9bf6-064b71d8d2b0", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089737289500, "endTime": 224090065806200}, "additional": {"children": ["6b6b8c14-56f3-45d9-a998-2f2194f7aa3c", "d0e663a4-e9a8-4be3-9617-d2be5f22b567", "3d399d9d-eae7-4e15-9ff5-8d8234a7f86e", "763c3a52-0f49-42d9-aff5-7fe2fea8ec0b", "59019db8-be6e-428d-b3a5-461b3690a21b", "bff0939b-6f3b-4c9e-981d-b6c1e3ddd360", "a8222050-9803-43f9-b689-dda93209422d", "dde209b0-d5c8-4131-9f02-ecbc23f6fbb6", "9a6faadd-34ae-4cc2-a21e-e79e9036217e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f2a43be1-0a1f-4b4f-a550-c4bc7a07f554", "logId": "ff056d80-3fae-476a-8854-02292c5a9619"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "855257d2-fc52-4a0e-b144-208816f0a6c2", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090065837500, "endTime": 224090067486500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f2a43be1-0a1f-4b4f-a550-c4bc7a07f554", "logId": "e9e5ab39-9f6f-44a0-9cda-b1cd866f128b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e27a94c-2a6d-44d2-9e81-b3b3f25deb25", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090067494400, "endTime": 224090067536000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f2a43be1-0a1f-4b4f-a550-c4bc7a07f554", "logId": "183890ed-4fbd-41be-8cfa-748bfa0fec18"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "486c4519-bcdc-42fe-a78b-96e06ca4037d", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089718095800, "endTime": 224089718132900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f2a43be1-0a1f-4b4f-a550-c4bc7a07f554", "logId": "9f2cab5d-d05a-4d3c-bb75-041ea737ebb9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f2cab5d-d05a-4d3c-bb75-041ea737ebb9", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089718095800, "endTime": 224089718132900}, "additional": {"logType": "info", "children": [], "durationId": "486c4519-bcdc-42fe-a78b-96e06ca4037d", "parent": "3acbd4b5-15ba-4a69-abd1-1ea2f601a807"}}, {"head": {"id": "a619631f-3aeb-4656-bc34-06d543aa433e", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089727769800, "endTime": 224089727796700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f2a43be1-0a1f-4b4f-a550-c4bc7a07f554", "logId": "17b0ef94-430e-4882-960b-b0b6208d8037"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17b0ef94-430e-4882-960b-b0b6208d8037", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089727769800, "endTime": 224089727796700}, "additional": {"logType": "info", "children": [], "durationId": "a619631f-3aeb-4656-bc34-06d543aa433e", "parent": "3acbd4b5-15ba-4a69-abd1-1ea2f601a807"}}, {"head": {"id": "faf4ce92-c5d3-4776-bc13-33f1f42f0928", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089728048000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6be4d32-a9be-4c58-9702-2a50b4a89bd4", "name": "Cache service initialization finished in 9 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089736860800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b27df13d-0662-434a-aafc-4c725a9b1c4b", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089709396600, "endTime": 224089737249800}, "additional": {"logType": "info", "children": [], "durationId": "41a111bc-044f-47c2-a4df-1af75a50aee1", "parent": "3acbd4b5-15ba-4a69-abd1-1ea2f601a807"}}, {"head": {"id": "6b6b8c14-56f3-45d9-a998-2f2194f7aa3c", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089743039800, "endTime": 224089743071100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "244905b8-ad9c-4d03-9bf6-064b71d8d2b0", "logId": "b0807a40-a81e-41d8-b2d4-4d7dca5216fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0e663a4-e9a8-4be3-9617-d2be5f22b567", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089743264100, "endTime": 224089750720100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "244905b8-ad9c-4d03-9bf6-064b71d8d2b0", "logId": "0f36290b-5dcf-4bc2-b13b-de48f849ed13"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d399d9d-eae7-4e15-9ff5-8d8234a7f86e", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089750736600, "endTime": 224089915112000}, "additional": {"children": ["566e01f0-e9a6-4cc1-923e-6a32aea7199e", "1b11a8c1-d76d-4c12-b7aa-62c8cc9ebaf4", "d521b194-ae78-41eb-9947-417b0a876952"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "244905b8-ad9c-4d03-9bf6-064b71d8d2b0", "logId": "9951ad88-38a4-4677-8136-7dfb48e044bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "763c3a52-0f49-42d9-aff5-7fe2fea8ec0b", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089915126300, "endTime": 224089955641400}, "additional": {"children": ["15a172ce-564c-4b95-9c11-1ee08d150661"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "244905b8-ad9c-4d03-9bf6-064b71d8d2b0", "logId": "e497d4b1-53d9-4488-b1ce-f7bd64310c67"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59019db8-be6e-428d-b3a5-461b3690a21b", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089955697200, "endTime": 224090013865600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "244905b8-ad9c-4d03-9bf6-064b71d8d2b0", "logId": "14b9d6b2-8ed0-40fd-aa59-5bb65d23884a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bff0939b-6f3b-4c9e-981d-b6c1e3ddd360", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090016095100, "endTime": 224090031831300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "244905b8-ad9c-4d03-9bf6-064b71d8d2b0", "logId": "874fdaf0-8a05-4282-bff5-796c359b3dd3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8222050-9803-43f9-b689-dda93209422d", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090031855100, "endTime": 224090065597100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "244905b8-ad9c-4d03-9bf6-064b71d8d2b0", "logId": "5862b173-57e4-42b2-b385-14c51d0df4c7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dde209b0-d5c8-4131-9f02-ecbc23f6fbb6", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090065621400, "endTime": 224090065790500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "244905b8-ad9c-4d03-9bf6-064b71d8d2b0", "logId": "1a90e6c2-1354-4fb4-8af3-b43a2ec24189"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b0807a40-a81e-41d8-b2d4-4d7dca5216fd", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089743039800, "endTime": 224089743071100}, "additional": {"logType": "info", "children": [], "durationId": "6b6b8c14-56f3-45d9-a998-2f2194f7aa3c", "parent": "ff056d80-3fae-476a-8854-02292c5a9619"}}, {"head": {"id": "0f36290b-5dcf-4bc2-b13b-de48f849ed13", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089743264100, "endTime": 224089750720100}, "additional": {"logType": "info", "children": [], "durationId": "d0e663a4-e9a8-4be3-9617-d2be5f22b567", "parent": "ff056d80-3fae-476a-8854-02292c5a9619"}}, {"head": {"id": "566e01f0-e9a6-4cc1-923e-6a32aea7199e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089751814500, "endTime": 224089751854800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d399d9d-eae7-4e15-9ff5-8d8234a7f86e", "logId": "6fae8581-496b-4551-9a85-2016119ec952"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6fae8581-496b-4551-9a85-2016119ec952", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089751814500, "endTime": 224089751854800}, "additional": {"logType": "info", "children": [], "durationId": "566e01f0-e9a6-4cc1-923e-6a32aea7199e", "parent": "9951ad88-38a4-4677-8136-7dfb48e044bd"}}, {"head": {"id": "1b11a8c1-d76d-4c12-b7aa-62c8cc9ebaf4", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089756922000, "endTime": 224089914508200}, "additional": {"children": ["c23992a3-d28b-4014-9d5b-91b6b6d855b2", "3bcd312c-593b-4a3c-a452-6147e01a61af"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d399d9d-eae7-4e15-9ff5-8d8234a7f86e", "logId": "91842824-408c-4b75-8934-7b4954ef460d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c23992a3-d28b-4014-9d5b-91b6b6d855b2", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089756924700, "endTime": 224089800746900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1b11a8c1-d76d-4c12-b7aa-62c8cc9ebaf4", "logId": "8267a521-2d01-4307-b9b1-272ae1767bbf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3bcd312c-593b-4a3c-a452-6147e01a61af", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089800771700, "endTime": 224089914496300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1b11a8c1-d76d-4c12-b7aa-62c8cc9ebaf4", "logId": "4bdbd2da-1e04-4a89-9d89-d515239f80d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df68b583-e3d0-4f55-9416-255737d7fd87", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089756933400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7e6ca8f-3354-4915-a8bd-2273cf5898f9", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089800520300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8267a521-2d01-4307-b9b1-272ae1767bbf", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089756924700, "endTime": 224089800746900}, "additional": {"logType": "info", "children": [], "durationId": "c23992a3-d28b-4014-9d5b-91b6b6d855b2", "parent": "91842824-408c-4b75-8934-7b4954ef460d"}}, {"head": {"id": "a673679d-5028-4d8f-be57-ab04cab496f7", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089800861800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "085bb2f2-287a-4954-8378-bb2752206f31", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089837430200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9482ea73-f2da-4d7b-bc86-2d6aae0e4726", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089837859300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37ca5b70-7151-4f4e-b641-8b99a59d87c1", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089838210900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0962e331-04f9-4ce7-a1f2-7f36ed783909", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089840529100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02bd4920-db06-4496-93d5-fd7cc9770903", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089845513200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28ceca07-ea11-48b5-8903-a96d7a8aa4ad", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089866337200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "405f9ed9-0584-4c57-aa99-80ddf88cc101", "name": "Sdk init in 32 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089887615800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6cdae22-3edf-4f41-bfcf-be6834b040dc", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089887888500}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 20, "minute": 11, "second": 59}, "markType": "other"}}, {"head": {"id": "fdcf5c93-fa54-4ca2-a0a4-3c398e2636e1", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089887903100}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 20, "minute": 11, "second": 59}, "markType": "other"}}, {"head": {"id": "edb7f8aa-0e20-4a26-be76-26db28ccda81", "name": "Project task initialization takes 25 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089914226700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90eb3a82-db3d-4bd5-a319-64abff0e61f8", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089914348000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4db0720c-2e8b-4564-b0e0-a3e15935a62c", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089914395700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cccf9504-cd35-496b-85d5-4dc11120806a", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089914438300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bdbd2da-1e04-4a89-9d89-d515239f80d6", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089800771700, "endTime": 224089914496300}, "additional": {"logType": "info", "children": [], "durationId": "3bcd312c-593b-4a3c-a452-6147e01a61af", "parent": "91842824-408c-4b75-8934-7b4954ef460d"}}, {"head": {"id": "91842824-408c-4b75-8934-7b4954ef460d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089756922000, "endTime": 224089914508200}, "additional": {"logType": "info", "children": ["8267a521-2d01-4307-b9b1-272ae1767bbf", "4bdbd2da-1e04-4a89-9d89-d515239f80d6"], "durationId": "1b11a8c1-d76d-4c12-b7aa-62c8cc9ebaf4", "parent": "9951ad88-38a4-4677-8136-7dfb48e044bd"}}, {"head": {"id": "d521b194-ae78-41eb-9947-417b0a876952", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089915084900, "endTime": 224089915099600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d399d9d-eae7-4e15-9ff5-8d8234a7f86e", "logId": "34ec2d47-82dd-4881-9012-20fcff69c482"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34ec2d47-82dd-4881-9012-20fcff69c482", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089915084900, "endTime": 224089915099600}, "additional": {"logType": "info", "children": [], "durationId": "d521b194-ae78-41eb-9947-417b0a876952", "parent": "9951ad88-38a4-4677-8136-7dfb48e044bd"}}, {"head": {"id": "9951ad88-38a4-4677-8136-7dfb48e044bd", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089750736600, "endTime": 224089915112000}, "additional": {"logType": "info", "children": ["6fae8581-496b-4551-9a85-2016119ec952", "91842824-408c-4b75-8934-7b4954ef460d", "34ec2d47-82dd-4881-9012-20fcff69c482"], "durationId": "3d399d9d-eae7-4e15-9ff5-8d8234a7f86e", "parent": "ff056d80-3fae-476a-8854-02292c5a9619"}}, {"head": {"id": "15a172ce-564c-4b95-9c11-1ee08d150661", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089915644400, "endTime": 224089955625200}, "additional": {"children": ["3288ab06-6e84-4818-b7e0-a09b2a8ccac9", "7fb7adbf-e1fc-4ac0-8bd2-b02473b8cb97", "598c6bb3-21c7-4082-a501-fe12a9f250ba"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "763c3a52-0f49-42d9-aff5-7fe2fea8ec0b", "logId": "863620fe-0a98-4b46-a08f-cf8ce378cb5b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3288ab06-6e84-4818-b7e0-a09b2a8ccac9", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089919814000, "endTime": 224089919828700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "15a172ce-564c-4b95-9c11-1ee08d150661", "logId": "c5c24e38-dba5-43ba-a666-12296d09ecbc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c5c24e38-dba5-43ba-a666-12296d09ecbc", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089919814000, "endTime": 224089919828700}, "additional": {"logType": "info", "children": [], "durationId": "3288ab06-6e84-4818-b7e0-a09b2a8ccac9", "parent": "863620fe-0a98-4b46-a08f-cf8ce378cb5b"}}, {"head": {"id": "7fb7adbf-e1fc-4ac0-8bd2-b02473b8cb97", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089921652100, "endTime": 224089953792500}, "additional": {"children": ["c89e39a8-6fa1-4964-a49a-de9d6ebe5924", "27064679-37bd-4393-b731-656b4185ca23"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "15a172ce-564c-4b95-9c11-1ee08d150661", "logId": "142349cf-382c-4122-ab16-4b9275de54fa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c89e39a8-6fa1-4964-a49a-de9d6ebe5924", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089921653100, "endTime": 224089924179300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7fb7adbf-e1fc-4ac0-8bd2-b02473b8cb97", "logId": "4da1f234-8a4b-4192-acde-b609c277f5ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "27064679-37bd-4393-b731-656b4185ca23", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089924193300, "endTime": 224089953780000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7fb7adbf-e1fc-4ac0-8bd2-b02473b8cb97", "logId": "5f176406-082e-4fda-bcd2-4fa573845c25"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "45d0ae3f-85c2-4f71-89eb-6e278a74efdc", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089921656700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39de849a-49f1-433b-be78-dcc718d761b3", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089924059700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4da1f234-8a4b-4192-acde-b609c277f5ce", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089921653100, "endTime": 224089924179300}, "additional": {"logType": "info", "children": [], "durationId": "c89e39a8-6fa1-4964-a49a-de9d6ebe5924", "parent": "142349cf-382c-4122-ab16-4b9275de54fa"}}, {"head": {"id": "ce05520d-1240-44ce-9bee-840b9f130784", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089924203700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9aaadeb9-a46b-4fb6-9b4a-4582ec820e9a", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089942637100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "829e6fd4-3a9f-4af0-aaa1-6af7609e8c52", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089942815300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74af7edf-631a-45db-bcd8-e9bcb0573fe6", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089943085600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adf2847d-c0c8-46b1-9414-d2bc403a280b", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089943283700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "131aa8e9-d0a5-4e24-8e2a-7125758faf6e", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089943376700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ad23ba9-2335-4320-a1ab-0922175f53ba", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089943456000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f92d4ab-e6cd-4ee8-81f2-191bfb36684d", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089943547900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b914d633-24b9-473c-b583-3548dd44c8c7", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089943634300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de2bfed2-879f-445d-9901-0548c62f1b31", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089943982000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c14e76ee-3b2c-4538-8e64-34dde71c3496", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089944171500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9568934-c714-4045-b1cd-8bb31d495f09", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089944264700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7884bf60-8718-4d6d-b4f6-8aecbc4b5301", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089944432800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33f5e3c3-e01f-49f3-ac24-08c420b3316c", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089944533600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df7ad761-b90a-4ea3-a56f-f8346f39b82c", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089944579200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51346853-0422-4b20-980c-221c90a64ab8", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089944679600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f575bf2-f9d7-44dc-b4e7-cf718e4fc906", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089944748700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97bee083-eb61-4ad0-b7f8-3fadb15d788d", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089944779000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f291b2a9-f4ca-4606-8ce0-6860fe177431", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089944858300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0123ed4e-db38-45d8-a70d-0742dcc8d7dd", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089945119200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48bebdf2-974a-49ef-a0da-2e69446e7256", "name": "Module entry task initialization takes 5 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089953443800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56f14ede-0cd8-4197-b241-6231a27ae787", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089953665100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7470d44d-01b2-4b78-8171-cecac7560e54", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089953722700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d43e5fb-2a0d-4a10-aa78-7bfe75087524", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089953752400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f176406-082e-4fda-bcd2-4fa573845c25", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089924193300, "endTime": 224089953780000}, "additional": {"logType": "info", "children": [], "durationId": "27064679-37bd-4393-b731-656b4185ca23", "parent": "142349cf-382c-4122-ab16-4b9275de54fa"}}, {"head": {"id": "142349cf-382c-4122-ab16-4b9275de54fa", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089921652100, "endTime": 224089953792500}, "additional": {"logType": "info", "children": ["4da1f234-8a4b-4192-acde-b609c277f5ce", "5f176406-082e-4fda-bcd2-4fa573845c25"], "durationId": "7fb7adbf-e1fc-4ac0-8bd2-b02473b8cb97", "parent": "863620fe-0a98-4b46-a08f-cf8ce378cb5b"}}, {"head": {"id": "598c6bb3-21c7-4082-a501-fe12a9f250ba", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089955482700, "endTime": 224089955498600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "15a172ce-564c-4b95-9c11-1ee08d150661", "logId": "5ac77097-59d9-4060-9935-e16028fba0a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ac77097-59d9-4060-9935-e16028fba0a4", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089955482700, "endTime": 224089955498600}, "additional": {"logType": "info", "children": [], "durationId": "598c6bb3-21c7-4082-a501-fe12a9f250ba", "parent": "863620fe-0a98-4b46-a08f-cf8ce378cb5b"}}, {"head": {"id": "863620fe-0a98-4b46-a08f-cf8ce378cb5b", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089915644400, "endTime": 224089955625200}, "additional": {"logType": "info", "children": ["c5c24e38-dba5-43ba-a666-12296d09ecbc", "142349cf-382c-4122-ab16-4b9275de54fa", "5ac77097-59d9-4060-9935-e16028fba0a4"], "durationId": "15a172ce-564c-4b95-9c11-1ee08d150661", "parent": "e497d4b1-53d9-4488-b1ce-f7bd64310c67"}}, {"head": {"id": "e497d4b1-53d9-4488-b1ce-f7bd64310c67", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089915126300, "endTime": 224089955641400}, "additional": {"logType": "info", "children": ["863620fe-0a98-4b46-a08f-cf8ce378cb5b"], "durationId": "763c3a52-0f49-42d9-aff5-7fe2fea8ec0b", "parent": "ff056d80-3fae-476a-8854-02292c5a9619"}}, {"head": {"id": "c3336859-e847-44f9-96a5-2e8a2a3b656e", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089973011300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a90aab32-faf3-433e-b91e-5c392ce273c3", "name": "hvigorfile, resolve hvigorfile dependencies in 58 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090013655200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14b9d6b2-8ed0-40fd-aa59-5bb65d23884a", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089955697200, "endTime": 224090013865600}, "additional": {"logType": "info", "children": [], "durationId": "59019db8-be6e-428d-b3a5-461b3690a21b", "parent": "ff056d80-3fae-476a-8854-02292c5a9619"}}, {"head": {"id": "9a6faadd-34ae-4cc2-a21e-e79e9036217e", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090015454600, "endTime": 224090016074500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "244905b8-ad9c-4d03-9bf6-064b71d8d2b0", "logId": "e96e890a-5672-404c-a82b-ca524c1e5ffb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "76307ca6-4c5d-4265-b91b-01c173221262", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090015629500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e96e890a-5672-404c-a82b-ca524c1e5ffb", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090015454600, "endTime": 224090016074500}, "additional": {"logType": "info", "children": [], "durationId": "9a6faadd-34ae-4cc2-a21e-e79e9036217e", "parent": "ff056d80-3fae-476a-8854-02292c5a9619"}}, {"head": {"id": "40028fcc-9906-4ad8-a1b5-bb084bde0c46", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090019314000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b54314e-7a52-492c-97e2-65ae3d3d733b", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090030039700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "874fdaf0-8a05-4282-bff5-796c359b3dd3", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090016095100, "endTime": 224090031831300}, "additional": {"logType": "info", "children": [], "durationId": "bff0939b-6f3b-4c9e-981d-b6c1e3ddd360", "parent": "ff056d80-3fae-476a-8854-02292c5a9619"}}, {"head": {"id": "eac4f222-c02b-4ef1-87b6-f4ee7b58dab9", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090031989500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58d3ebca-7588-4eca-b265-b73f77effbcb", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090044585400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d584622-f986-43b4-b0a8-c0b1fb292070", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090044885700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "805e8c01-47ac-4c37-bc3d-660c7197<PERSON>ea", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090046092900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01d18260-5ec5-47c7-a408-c93174204544", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090053770900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b4f072f-1350-4851-8c7d-e4c7ea4f9fb6", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090053994100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5862b173-57e4-42b2-b385-14c51d0df4c7", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090031855100, "endTime": 224090065597100}, "additional": {"logType": "info", "children": [], "durationId": "a8222050-9803-43f9-b689-dda93209422d", "parent": "ff056d80-3fae-476a-8854-02292c5a9619"}}, {"head": {"id": "ae5644e4-e94d-4ca9-b048-7fe6e2b631d9", "name": "Configuration phase cost:323 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090065655000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a90e6c2-1354-4fb4-8af3-b43a2ec24189", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090065621400, "endTime": 224090065790500}, "additional": {"logType": "info", "children": [], "durationId": "dde209b0-d5c8-4131-9f02-ecbc23f6fbb6", "parent": "ff056d80-3fae-476a-8854-02292c5a9619"}}, {"head": {"id": "ff056d80-3fae-476a-8854-02292c5a9619", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089737289500, "endTime": 224090065806200}, "additional": {"logType": "info", "children": ["b0807a40-a81e-41d8-b2d4-4d7dca5216fd", "0f36290b-5dcf-4bc2-b13b-de48f849ed13", "9951ad88-38a4-4677-8136-7dfb48e044bd", "e497d4b1-53d9-4488-b1ce-f7bd64310c67", "14b9d6b2-8ed0-40fd-aa59-5bb65d23884a", "874fdaf0-8a05-4282-bff5-796c359b3dd3", "5862b173-57e4-42b2-b385-14c51d0df4c7", "1a90e6c2-1354-4fb4-8af3-b43a2ec24189", "e96e890a-5672-404c-a82b-ca524c1e5ffb"], "durationId": "244905b8-ad9c-4d03-9bf6-064b71d8d2b0", "parent": "3acbd4b5-15ba-4a69-abd1-1ea2f601a807"}}, {"head": {"id": "020998d4-29e2-406e-ba57-dde39fd91142", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090067440300, "endTime": 224090067466600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f2a43be1-0a1f-4b4f-a550-c4bc7a07f554", "logId": "56a06615-57bf-49eb-9527-4db672422fe5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56a06615-57bf-49eb-9527-4db672422fe5", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090067440300, "endTime": 224090067466600}, "additional": {"logType": "info", "children": [], "durationId": "020998d4-29e2-406e-ba57-dde39fd91142", "parent": "3acbd4b5-15ba-4a69-abd1-1ea2f601a807"}}, {"head": {"id": "e9e5ab39-9f6f-44a0-9cda-b1cd866f128b", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090065837500, "endTime": 224090067486500}, "additional": {"logType": "info", "children": [], "durationId": "855257d2-fc52-4a0e-b144-208816f0a6c2", "parent": "3acbd4b5-15ba-4a69-abd1-1ea2f601a807"}}, {"head": {"id": "183890ed-4fbd-41be-8cfa-748bfa0fec18", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090067494400, "endTime": 224090067536000}, "additional": {"logType": "info", "children": [], "durationId": "2e27a94c-2a6d-44d2-9e81-b3b3f25deb25", "parent": "3acbd4b5-15ba-4a69-abd1-1ea2f601a807"}}, {"head": {"id": "3acbd4b5-15ba-4a69-abd1-1ea2f601a807", "name": "init", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089709392500, "endTime": 224090067545600}, "additional": {"logType": "info", "children": ["b27df13d-0662-434a-aafc-4c725a9b1c4b", "ff056d80-3fae-476a-8854-02292c5a9619", "e9e5ab39-9f6f-44a0-9cda-b1cd866f128b", "183890ed-4fbd-41be-8cfa-748bfa0fec18", "9f2cab5d-d05a-4d3c-bb75-041ea737ebb9", "17b0ef94-430e-4882-960b-b0b6208d8037", "56a06615-57bf-49eb-9527-4db672422fe5"], "durationId": "f2a43be1-0a1f-4b4f-a550-c4bc7a07f554"}}, {"head": {"id": "0eab2d59-03c8-49f1-91f4-a6447c685834", "name": "Configuration task cost before running: 368 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090068248100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e40bb39-7a65-45b3-8097-43fa424001d3", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090088601100, "endTime": 224090112734200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "4b5d5ea7-da48-4ce4-9c92-f4aee6e2b9ff", "logId": "60b4e86c-12de-4c1e-b24e-509d85460662"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b5d5ea7-da48-4ce4-9c92-f4aee6e2b9ff", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090071915200}, "additional": {"logType": "detail", "children": [], "durationId": "5e40bb39-7a65-45b3-8097-43fa424001d3"}}, {"head": {"id": "8f1211cb-fca9-41f1-aee4-89b9e5841bd7", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090073616400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1dc4017-2fa4-4d52-8d96-dc2497a211d3", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090074008300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0d89534-53e4-48a4-9307-a53295089a3d", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090075977500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37f77638-56d1-4bd5-b279-95be4b15f91f", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090077295400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "056e9d48-c654-44f9-8373-49d9707d84db", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090079320100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24bbc952-c114-4dd5-93e8-14955c0ff130", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090079509200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a09b546a-6055-436d-802c-a84c0745ec30", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090088617600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46180736-d7b0-473c-bdca-656a1f009934", "name": "Incremental task entry:default@PreBuild pre-execution cost: 16 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090112393300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66b6b0b0-0566-44ba-b008-fa476ed115b5", "name": "entry : default@PreBuild cost memory 0.3171844482421875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090112608900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60b4e86c-12de-4c1e-b24e-509d85460662", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090088601100, "endTime": 224090112734200}, "additional": {"logType": "info", "children": [], "durationId": "5e40bb39-7a65-45b3-8097-43fa424001d3"}}, {"head": {"id": "ca774875-6b47-4c99-a77d-a4b88ab019fb", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090125060500, "endTime": 224090128382900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b87938f0-8f3e-4dac-a5ef-4f9339ed237e", "logId": "bdad3af2-2e48-42d1-860c-b63322f3a7c7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b87938f0-8f3e-4dac-a5ef-4f9339ed237e", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090122618400}, "additional": {"logType": "detail", "children": [], "durationId": "ca774875-6b47-4c99-a77d-a4b88ab019fb"}}, {"head": {"id": "8b211fad-fc12-4cb2-8e69-bc8275d06472", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090124047800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ecc1d12-2c88-4f92-868d-6c14f7d45137", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090124200500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2544b35c-0b8d-42af-be47-4e3193494b74", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090125073300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3494585-feac-4e77-8682-21fee0ca208f", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090126080000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32aa0215-444f-4e7a-800b-0e4bbad8156c", "name": "entry : default@CreateModuleInfo cost memory 0.06087493896484375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090128054200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83ef7ae2-a83c-4315-8650-bc51b25<PERSON>ca", "name": "runTaskFromQueue task cost before running: 428 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090128271800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdad3af2-2e48-42d1-860c-b63322f3a7c7", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090125060500, "endTime": 224090128382900, "totalTime": 3170300}, "additional": {"logType": "info", "children": [], "durationId": "ca774875-6b47-4c99-a77d-a4b88ab019fb"}}, {"head": {"id": "afeee198-43ee-454b-83a3-e66b24387aed", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090145357300, "endTime": 224090151476400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c31899f0-8b01-4f85-87c4-7ceaa4759b48", "logId": "f0c74e44-0bd9-4989-8f86-94e19ff5e16c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c31899f0-8b01-4f85-87c4-7ceaa4759b48", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090132218200}, "additional": {"logType": "detail", "children": [], "durationId": "afeee198-43ee-454b-83a3-e66b24387aed"}}, {"head": {"id": "4beed6f7-7fbc-4b5d-93e8-514b945d37d8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090134179600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10b6ab59-a85a-4658-87d6-e4a56246772b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090134365600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f725ecce-69a2-44a4-9e2e-2c062c801fc4", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090145377900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cd1e8df-25fd-48e9-8e62-338b981a6751", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090147399000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bb4a0fe-93d4-498f-b972-c639e81a02af", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090151131900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ca50b0b-31aa-48e2-baec-180195d728da", "name": "entry : default@GenerateMetadata cost memory -4.149192810058594", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090151340500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0c74e44-0bd9-4989-8f86-94e19ff5e16c", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090145357300, "endTime": 224090151476400}, "additional": {"logType": "info", "children": [], "durationId": "afeee198-43ee-454b-83a3-e66b24387aed"}}, {"head": {"id": "8776066f-6134-4142-8cff-752d0424c645", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090156616700, "endTime": 224090157147600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "c87d866e-ea65-4667-bdae-1adcc87e78cf", "logId": "4675dd31-94f9-4aef-8b4b-27f6e05f5f20"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c87d866e-ea65-4667-bdae-1adcc87e78cf", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090154603600}, "additional": {"logType": "detail", "children": [], "durationId": "8776066f-6134-4142-8cff-752d0424c645"}}, {"head": {"id": "34895330-d9eb-44b7-8f90-ea45cfa1fb32", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090156082900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07025e81-db50-41ce-a8d0-e97bc01a502c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090156229500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaaecbe8-46f7-49d3-b6ec-c4e076e9105f", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090156631800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad85b870-0b10-42e1-bfcb-bfacb993a956", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090156796200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f526cc9e-de84-4429-8a1b-193cedd22f96", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090156878000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1059505-2045-4c53-bc98-35c4cbc81ad4", "name": "entry : default@ConfigureCmake cost memory 0.0373382568359375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090156977700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaf457d5-a64e-4ae2-8123-e2f43fb94e5c", "name": "runTaskFromQueue task cost before running: 457 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090157081000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4675dd31-94f9-4aef-8b4b-27f6e05f5f20", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090156616700, "endTime": 224090157147600, "totalTime": 440600}, "additional": {"logType": "info", "children": [], "durationId": "8776066f-6134-4142-8cff-752d0424c645"}}, {"head": {"id": "56af703c-bb4f-43e9-8d15-b0b5d6b85e22", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090163290800, "endTime": 224090167401300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "2161091b-5cb8-4c34-afea-98deecc7960c", "logId": "8ffed0c6-bf62-4a65-b4a6-c4e371b10f94"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2161091b-5cb8-4c34-afea-98deecc7960c", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090159888600}, "additional": {"logType": "detail", "children": [], "durationId": "56af703c-bb4f-43e9-8d15-b0b5d6b85e22"}}, {"head": {"id": "b1cadfa7-b342-44aa-a12b-7717fd258706", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090161796400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c64672d-3eb4-4f7f-88d0-47071cf23273", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090161972700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d3fddfc-a25f-4d78-9667-944ef14b1cf5", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090163309100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "622f2f5f-4999-4d4c-a516-de21092f0b26", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090167043300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19d2fc43-4472-4402-ac25-319dc1e4d4bb", "name": "entry : default@MergeProfile cost memory 0.11789703369140625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090167224700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ffed0c6-bf62-4a65-b4a6-c4e371b10f94", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090163290800, "endTime": 224090167401300}, "additional": {"logType": "info", "children": [], "durationId": "56af703c-bb4f-43e9-8d15-b0b5d6b85e22"}}, {"head": {"id": "821b818f-7cfa-4351-9183-96633af2e725", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090174761500, "endTime": 224090180777900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9620ebe3-a41d-43d8-aab5-76583ed6affc", "logId": "8929f859-ae18-4871-85c2-be1629f8dfc1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9620ebe3-a41d-43d8-aab5-76583ed6affc", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090170508400}, "additional": {"logType": "detail", "children": [], "durationId": "821b818f-7cfa-4351-9183-96633af2e725"}}, {"head": {"id": "b251bae2-9f9b-4d9c-a709-c60b507b3022", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090172741400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "747d816f-0750-48c1-8013-b6a22e8f6640", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090172946000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbece04f-1100-4622-9468-8299dee86a02", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090174781900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74a4253e-842a-4093-87b5-fac83f0a3a83", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090177036600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c3b4182-4a5b-4ed6-bfea-3a430c03cafb", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090180412300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60493b9d-5fd0-48a1-bafe-3f3e01c2ea95", "name": "entry : default@CreateBuildProfile cost memory 0.10776519775390625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090180664200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8929f859-ae18-4871-85c2-be1629f8dfc1", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090174761500, "endTime": 224090180777900}, "additional": {"logType": "info", "children": [], "durationId": "821b818f-7cfa-4351-9183-96633af2e725"}}, {"head": {"id": "6b302a98-7529-498d-990f-21416d510a3d", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090188486500, "endTime": 224090189840200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "bc660b17-b8b9-4277-b8d4-c829ea923206", "logId": "1e23b115-5168-4047-842a-ff7b5d7e7c45"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc660b17-b8b9-4277-b8d4-c829ea923206", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090184129600}, "additional": {"logType": "detail", "children": [], "durationId": "6b302a98-7529-498d-990f-21416d510a3d"}}, {"head": {"id": "e2abe3fa-82f7-4cdc-b354-dd5a898b0516", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090186849200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d13d084-2427-4b16-8d25-cefa6871bb03", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090187053900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a4f3297-cf35-43ee-9cd3-1f6d231bac46", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090188502400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02fceeab-f2e1-4075-a0c9-b59f029cb30f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090188720600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e637c283-6c8d-48d6-8872-566fbcd3c63e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090188826200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e15f176e-cbe6-4bf4-b14b-917aee0871e3", "name": "entry : default@PreCheckSyscap cost memory 0.0409698486328125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090189485700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82ff63a6-cfbd-40c4-8bc9-121284e1371b", "name": "runTaskFromQueue task cost before running: 489 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090189732300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e23b115-5168-4047-842a-ff7b5d7e7c45", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090188486500, "endTime": 224090189840200, "totalTime": 1205800}, "additional": {"logType": "info", "children": [], "durationId": "6b302a98-7529-498d-990f-21416d510a3d"}}, {"head": {"id": "5944ae35-e758-4d4e-8cb9-835f5df7e51a", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090199103200, "endTime": 224090209754600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b414ce86-e9b8-4488-b3bf-6b0c2afb11e8", "logId": "fedac432-47f9-442e-9c70-9eaa7a1cd303"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b414ce86-e9b8-4488-b3bf-6b0c2afb11e8", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090194208000}, "additional": {"logType": "detail", "children": [], "durationId": "5944ae35-e758-4d4e-8cb9-835f5df7e51a"}}, {"head": {"id": "9574f7ee-37af-47c4-987a-ae813108044e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090196429300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a6da57b-0401-4e33-b3b9-f239c258cc23", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090196626800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3be3b86c-3332-4941-87b1-9b9c8f9f3ff7", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090199123300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c12632bd-688c-440e-83d2-cc2d6ce712a2", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090208206200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85660a2d-2b59-44b6-863b-7f1c8e8fbe6a", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090209433100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5463bcf3-0fb6-459a-bcef-41df5bb67d44", "name": "entry : default@GeneratePkgContextInfo cost memory 0.2545928955078125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090209619600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fedac432-47f9-442e-9c70-9eaa7a1cd303", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090199103200, "endTime": 224090209754600}, "additional": {"logType": "info", "children": [], "durationId": "5944ae35-e758-4d4e-8cb9-835f5df7e51a"}}, {"head": {"id": "27f5b496-3322-4464-937d-18c8cfbe0276", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090227074300, "endTime": 224090232626100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "42eb7c9d-c295-4c03-a56a-7c2898d823a4", "logId": "db4740c2-3e10-409f-b7e1-6cf80f2ad39f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42eb7c9d-c295-4c03-a56a-7c2898d823a4", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090212862600}, "additional": {"logType": "detail", "children": [], "durationId": "27f5b496-3322-4464-937d-18c8cfbe0276"}}, {"head": {"id": "66626eab-d3a8-4d31-bbe6-b138595c05e9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090214508000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79b34d8a-ea41-4c6e-a54c-0d95c6f286f9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090214688300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4671c11-6838-4316-a458-85e506d15125", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090227104000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "255eb25b-1e31-411b-8ae7-9379e6b46e22", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090231592600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b00d6f3-a178-4238-b66d-27c800e48a22", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090231873200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5b25f4d-e6f5-4d18-b4d8-a6b4d006c756", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090232076800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5e9d572-ab4f-4e66-83d3-554bfc0cdf65", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090232206600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1746bf90-cfc0-4da3-917d-a4bf0c361d04", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1210784912109375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090232367000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82583e47-3443-4642-ac04-46cc220f4888", "name": "runTaskFromQueue task cost before running: 532 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090232518300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db4740c2-3e10-409f-b7e1-6cf80f2ad39f", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090227074300, "endTime": 224090232626100, "totalTime": 5416400}, "additional": {"logType": "info", "children": [], "durationId": "27f5b496-3322-4464-937d-18c8cfbe0276"}}, {"head": {"id": "7b71a9cd-ffd2-4d08-82c6-e5bd06acb7e8", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090242431400, "endTime": 224090243165200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "fae743d9-1a6c-42dc-be4d-1d4ce2cbbec7", "logId": "d0093b93-48fa-42a6-bfd5-24b7f21814f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fae743d9-1a6c-42dc-be4d-1d4ce2cbbec7", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090238122400}, "additional": {"logType": "detail", "children": [], "durationId": "7b71a9cd-ffd2-4d08-82c6-e5bd06acb7e8"}}, {"head": {"id": "49a87735-545e-4041-826b-17970b11d52b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090240625900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4099785e-ccc6-460d-b65b-3ec0a60cc223", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090240823600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4de98814-9d6c-42e8-b43b-808f5bbd1adc", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090242451000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c2ca0a2-d1cc-475e-983e-1376c0851910", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090242672600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1959a8ea-6608-44a1-bf06-b4452bc982b7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090242779700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48e9b654-6fbc-451c-ae29-cc5060956b8e", "name": "entry : default@BuildNativeWithCmake cost memory 0.03838348388671875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090242908100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0af09fb-c09a-4977-bc35-7b2bafea0113", "name": "runTaskFromQueue task cost before running: 543 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090243065800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0093b93-48fa-42a6-bfd5-24b7f21814f3", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090242431400, "endTime": 224090243165200, "totalTime": 606600}, "additional": {"logType": "info", "children": [], "durationId": "7b71a9cd-ffd2-4d08-82c6-e5bd06acb7e8"}}, {"head": {"id": "4be5d13e-864d-419c-b58e-e215fb55dc55", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090250758700, "endTime": 224090259939200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "cb8a8202-b276-4177-ade7-a6398a20a910", "logId": "f35c92c2-e9d0-4bef-b28b-1cbd49824208"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cb8a8202-b276-4177-ade7-a6398a20a910", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090246778800}, "additional": {"logType": "detail", "children": [], "durationId": "4be5d13e-864d-419c-b58e-e215fb55dc55"}}, {"head": {"id": "81d6b0b3-22d3-47cf-a164-21b3dcc053f8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090249101700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e71f114-d742-406c-81a7-4dfc8c78a96a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090249281900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11ca2ab3-eef6-43d8-9310-fffd9169d0d8", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090250776600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed0d9595-2e45-412f-a8a6-f0448aea04ee", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090259553600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b674cb3d-09d1-4438-ac98-f98bad4c5535", "name": "entry : default@MakePackInfo cost memory 0.163909912109375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090259793000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f35c92c2-e9d0-4bef-b28b-1cbd49824208", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090250758700, "endTime": 224090259939200}, "additional": {"logType": "info", "children": [], "durationId": "4be5d13e-864d-419c-b58e-e215fb55dc55"}}, {"head": {"id": "7e53a653-da51-45a6-823d-00dbd798e900", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090270284000, "endTime": 224090276360600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c16a1c7e-9137-48d3-9825-dad1430af2ac", "logId": "d98993ba-aa5d-43a3-8665-6ecd28ae8aa7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c16a1c7e-9137-48d3-9825-dad1430af2ac", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090265311400}, "additional": {"logType": "detail", "children": [], "durationId": "7e53a653-da51-45a6-823d-00dbd798e900"}}, {"head": {"id": "19d254dc-8d8f-4df3-86ae-ee809248009d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090267776000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "007601b0-ab7f-48ff-9c8e-468f70eb2765", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090267959400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9046c50f-e99e-49af-b92e-5ef553b4713f", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090270296500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea80aa6d-a391-436f-a3da-9b1799d17c19", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090270551000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e1a7a5f-5f32-4c00-ac76-178f4926a377", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090271644300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0784a54-3fbe-4f6f-a463-0ba4ccdf52bd", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090275921600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78084c86-bd37-42d8-8888-b323d585c9eb", "name": "entry : default@SyscapTransform cost memory 0.1504364013671875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090276116400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d98993ba-aa5d-43a3-8665-6ecd28ae8aa7", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090270284000, "endTime": 224090276360600}, "additional": {"logType": "info", "children": [], "durationId": "7e53a653-da51-45a6-823d-00dbd798e900"}}, {"head": {"id": "11c0af94-6386-4dbf-8b0a-9a765e6cb86f", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090286338300, "endTime": 224090291373600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "b0b8a91e-d37c-4eca-ba2f-b7f6d8e97f0e", "logId": "d4083b3e-567b-406f-a4ed-575adcc43c39"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b0b8a91e-d37c-4eca-ba2f-b7f6d8e97f0e", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090280100200}, "additional": {"logType": "detail", "children": [], "durationId": "11c0af94-6386-4dbf-8b0a-9a765e6cb86f"}}, {"head": {"id": "ffb8e272-be39-4ac8-b41c-ab79518742d2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090282532200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "635247d5-883a-4271-9660-82a5d0e2c720", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090282717500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baba018f-8310-4fa0-82b3-02aa725d2676", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090286367200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e3b9c93-d548-4fc7-a4f3-716be94de736", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090291084300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4f429ac-dc9d-4490-a2bf-55d9ce9f4881", "name": "entry : default@ProcessProfile cost memory 0.1247406005859375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090291262800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4083b3e-567b-406f-a4ed-575adcc43c39", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090286338300, "endTime": 224090291373600}, "additional": {"logType": "info", "children": [], "durationId": "11c0af94-6386-4dbf-8b0a-9a765e6cb86f"}}, {"head": {"id": "4fac1dc8-af9a-4828-8fdd-86352c047408", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090300456700, "endTime": 224090312468100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7a01546f-587b-4ee1-9603-d096ff7651d6", "logId": "9b389d8e-6adf-48cc-af05-855e3a89c072"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7a01546f-587b-4ee1-9603-d096ff7651d6", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090294528400}, "additional": {"logType": "detail", "children": [], "durationId": "4fac1dc8-af9a-4828-8fdd-86352c047408"}}, {"head": {"id": "b6749b3b-0c23-400b-8aff-d328ab09103c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090296688200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72119cfb-2592-49a7-adad-7165fe455568", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090296847400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c310b7e6-a3c2-44f7-943a-615907784109", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090300474800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f713112-5042-496d-99ab-33689dea48ad", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090312123300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "842d6387-eb3d-4faf-9195-bb17cb415a2f", "name": "entry : default@ProcessRouterMap cost memory 0.23372650146484375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090312333700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b389d8e-6adf-48cc-af05-855e3a89c072", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090300456700, "endTime": 224090312468100}, "additional": {"logType": "info", "children": [], "durationId": "4fac1dc8-af9a-4828-8fdd-86352c047408"}}, {"head": {"id": "cbbd4436-a5db-4958-8449-9c9fde2bd565", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090320123500, "endTime": 224090332579400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "7eedf606-504f-4251-b5c4-6a555c6fb5d8", "logId": "dcb2fe7d-03f2-477c-8cd1-c04b1cf91942"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7eedf606-504f-4251-b5c4-6a555c6fb5d8", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090318085800}, "additional": {"logType": "detail", "children": [], "durationId": "cbbd4436-a5db-4958-8449-9c9fde2bd565"}}, {"head": {"id": "09075c7e-bd07-471b-a50a-95a61dbb8c54", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090319914100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4539130-f9c9-48e0-ac79-45051aed9fd0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090320036400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a51b1215-3849-4974-bdde-0a8af013a0d7", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090320130400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99d126eb-81d9-4da9-a4f1-0e147abe50d0", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090320306700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5edae93-5bf0-4874-99d5-79b98a428552", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090328863800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e2a7afc-fed8-4cbd-9397-a786b25f292c", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090329101900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9e37d52-b047-4c68-bbc7-1a042f60934b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090329262500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "604daf5d-b6df-49e2-aed4-2a84f37e6d15", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090329361100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66976850-c24a-4460-b18a-29ff50ae0cdd", "name": "entry : default@ProcessStartupConfig cost memory 0.2604217529296875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090332223300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "808f37bb-ba0a-40f2-8469-792c98c26a9e", "name": "runTaskFromQueue task cost before running: 632 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090332469400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcb2fe7d-03f2-477c-8cd1-c04b1cf91942", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090320123500, "endTime": 224090332579400, "totalTime": 12298600}, "additional": {"logType": "info", "children": [], "durationId": "cbbd4436-a5db-4958-8449-9c9fde2bd565"}}, {"head": {"id": "0e6712d8-f94a-43e1-81a9-5d6757815d3e", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090342354000, "endTime": 224090344645200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "d4aa1566-5716-43d3-8327-95830ac0482c", "logId": "4d7269ba-4538-4404-8df9-9eca7aa94277"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d4aa1566-5716-43d3-8327-95830ac0482c", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090338611200}, "additional": {"logType": "detail", "children": [], "durationId": "0e6712d8-f94a-43e1-81a9-5d6757815d3e"}}, {"head": {"id": "23431f8c-83d2-48c5-9869-8b010578d5f9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090340801200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7c6b319-0b16-4f3e-9659-3c72603c44d6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090340966600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4b27944-72e2-4db8-95a4-c03c331ac377", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090342372600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f6a88ee-a7fc-42e9-baef-19c1c1025c45", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090342570200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb036d24-bd3d-4c63-b8d8-b88f9df206ce", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090342654600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecd8523b-e122-4495-b340-7c96bcdd73fb", "name": "entry : default@BuildNativeWithNinja cost memory 0.3014984130859375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090344432400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c946ddb4-3186-459b-8374-6ce36f605cf1", "name": "runTaskFromQueue task cost before running: 644 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090344581500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d7269ba-4538-4404-8df9-9eca7aa94277", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090342354000, "endTime": 224090344645200, "totalTime": 2204700}, "additional": {"logType": "info", "children": [], "durationId": "0e6712d8-f94a-43e1-81a9-5d6757815d3e"}}, {"head": {"id": "b2a2d143-985a-4bbd-adf6-e34211a33e91", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090359172500, "endTime": 224090370867000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "0aafb698-0496-47b4-87a6-be8e1914a3c9", "logId": "8199d47d-0b25-48e8-a9d8-4db09524e6bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0aafb698-0496-47b4-87a6-be8e1914a3c9", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090351885500}, "additional": {"logType": "detail", "children": [], "durationId": "b2a2d143-985a-4bbd-adf6-e34211a33e91"}}, {"head": {"id": "f117a0cd-74f7-4e0b-996f-3a8e718d9d5a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090354271600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c358d463-a24f-4224-93c1-135d51ab8b9e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090354461100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0814d0c8-fb96-4b18-a05f-ba3121c9c855", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090356571900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ee0cfe3-c700-4e46-9ce8-43e882d9eba2", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090361787800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99606d22-abdf-4e0e-8a81-eec444327073", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090366029000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a67f977-b889-4b1a-a687-92fc768282a7", "name": "entry : default@ProcessResource cost memory 0.16211700439453125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090366272300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8199d47d-0b25-48e8-a9d8-4db09524e6bc", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090359172500, "endTime": 224090370867000}, "additional": {"logType": "info", "children": [], "durationId": "b2a2d143-985a-4bbd-adf6-e34211a33e91"}}, {"head": {"id": "603a419d-5be4-4d40-96e2-23943afa1a99", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090395781600, "endTime": 224090441566000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b0a17fc6-efc8-4767-a6a8-a4476c500350", "logId": "c59287dc-da91-4c7d-980d-c86431174330"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b0a17fc6-efc8-4767-a6a8-a4476c500350", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090388138500}, "additional": {"logType": "detail", "children": [], "durationId": "603a419d-5be4-4d40-96e2-23943afa1a99"}}, {"head": {"id": "893a33ac-fd16-4061-827e-4c1a002e7d13", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090389824000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e43145cc-6c5c-491e-8c42-aa1b971995b8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090389994100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f29f0129-02e1-4f34-8391-a4e5a545f6fc", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090395803900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f348f8db-1a1f-4a4a-96a5-6bf15f69b041", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 25 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090441231600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f895b114-4b02-43f2-b1c9-fbab8c1218f6", "name": "entry : default@GenerateLoaderJson cost memory 0.9193649291992188", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090441440400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c59287dc-da91-4c7d-980d-c86431174330", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090395781600, "endTime": 224090441566000}, "additional": {"logType": "info", "children": [], "durationId": "603a419d-5be4-4d40-96e2-23943afa1a99"}}, {"head": {"id": "e02355e5-b5b4-4bf3-b8c3-71e22ad2dbdd", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090455355300, "endTime": 224090464790400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "52f7cc67-510d-4089-b3dc-b8eae6bd8ad6", "logId": "15cf933f-fef0-4b0b-84ee-1ff88c298213"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52f7cc67-510d-4089-b3dc-b8eae6bd8ad6", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090452251700}, "additional": {"logType": "detail", "children": [], "durationId": "e02355e5-b5b4-4bf3-b8c3-71e22ad2dbdd"}}, {"head": {"id": "d9139ada-b4e6-4ae7-92f7-deb95f5e642a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090453538800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42b8f449-df71-49cf-bc56-ce67c2a27aee", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090453773700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b184329c-fff4-43cc-aeeb-29597ac2e6d9", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090455373400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41561aea-6cdf-4387-81da-dad31bd25afa", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090464480500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "685ac088-817b-40d4-8d88-2a813928795d", "name": "entry : default@ProcessLibs cost memory 0.14365386962890625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090464690100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15cf933f-fef0-4b0b-84ee-1ff88c298213", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090455355300, "endTime": 224090464790400}, "additional": {"logType": "info", "children": [], "durationId": "e02355e5-b5b4-4bf3-b8c3-71e22ad2dbdd"}}, {"head": {"id": "cc2ecfc0-31e4-4c9b-a159-4a40034f1f6e", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090477552700, "endTime": 224090538474700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "67d2f7b0-f2fa-48b8-8220-5e43dd782f29", "logId": "92044e8a-04ea-430e-8ef8-be4fd8994bf5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "67d2f7b0-f2fa-48b8-8220-5e43dd782f29", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090468815900}, "additional": {"logType": "detail", "children": [], "durationId": "cc2ecfc0-31e4-4c9b-a159-4a40034f1f6e"}}, {"head": {"id": "78450648-bf43-4f60-b0d8-1f966e32547f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090471323800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "659f63f4-3646-4172-ab19-8822760e9cb2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090471485600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f07adafc-80c3-4b9d-9310-c083d97ef843", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090472987700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92b25cf7-1bc0-452d-ad6c-f87e436a3c2e", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090477602400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d223972-5f8e-4b44-ad57-714c2cfd2522", "name": "Incremental task entry:default@CompileResource pre-execution cost: 58 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090538150200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc3d3fd6-fc31-4a71-8cb0-38b62a97fb90", "name": "entry : default@CompileResource cost memory 1.3172607421875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090538344800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92044e8a-04ea-430e-8ef8-be4fd8994bf5", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090477552700, "endTime": 224090538474700}, "additional": {"logType": "info", "children": [], "durationId": "cc2ecfc0-31e4-4c9b-a159-4a40034f1f6e"}}, {"head": {"id": "c84d1992-70cc-45a1-8e83-9512a25e3fce", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090551588800, "endTime": 224090557215800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "63d78fc1-3176-4466-8cd5-ae9267929156", "logId": "bcb4c2ed-8353-4f7f-87c6-f79b4438d9ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "63d78fc1-3176-4466-8cd5-ae9267929156", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090545354700}, "additional": {"logType": "detail", "children": [], "durationId": "c84d1992-70cc-45a1-8e83-9512a25e3fce"}}, {"head": {"id": "5f94385e-9393-4c9b-91f4-24abaa9b1580", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090547136100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e339e3ed-955e-4da3-a011-ec5de09672ee", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090547290100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1db2a1eb-a35e-4b45-8ff6-9b29689d0943", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090551602600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f86e47a-f93d-4dc1-8c98-5ff920925efa", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090552674200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3afaac39-2db6-4587-9cc6-c77cc35cd180", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090556880900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "834ba95e-d345-4b0f-b572-89993fdf82f5", "name": "entry : default@DoNativeStrip cost memory 0.08042144775390625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090557084700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcb4c2ed-8353-4f7f-87c6-f79b4438d9ce", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090551588800, "endTime": 224090557215800}, "additional": {"logType": "info", "children": [], "durationId": "c84d1992-70cc-45a1-8e83-9512a25e3fce"}}, {"head": {"id": "f89c73e6-57cc-493a-8ad0-9e4ffa70312a", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090575561400, "endTime": 224090640727900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "e6ce177f-0c10-4f67-afa9-9dd51ecd2f9c", "logId": "4b107bfe-7964-4478-8dac-511cd54415ab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6ce177f-0c10-4f67-afa9-9dd51ecd2f9c", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090561720300}, "additional": {"logType": "detail", "children": [], "durationId": "f89c73e6-57cc-493a-8ad0-9e4ffa70312a"}}, {"head": {"id": "b832e7ca-238e-4cda-ba74-6d07da08d083", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090564592600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a756791-39a3-4ef7-8922-a2c1c69e8304", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090564779400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f68cafd-dd08-4916-980b-3bf38e8b5cec", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090575582000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9fc93cc-35f1-4e6e-87a4-f70113211e8f", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090575847900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfa06c9c-27ba-446f-be51-8378a30fa00b", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 50 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090640417300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef99cf8b-07df-4a00-9aef-bce6f4ccef7a", "name": "entry : default@CompileArkTS cost memory 1.4352035522460938", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090640604300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b107bfe-7964-4478-8dac-511cd54415ab", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090575561400, "endTime": 224090640727900}, "additional": {"logType": "info", "children": [], "durationId": "f89c73e6-57cc-493a-8ad0-9e4ffa70312a"}}, {"head": {"id": "68a40603-e99a-4904-ab25-9b3f7e01e466", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090662545900, "endTime": 224090674824100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "732d1004-350b-49e9-8491-dbf01a9880a7", "logId": "79dc8943-0210-418f-b4a8-9f70f3dd974d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "732d1004-350b-49e9-8491-dbf01a9880a7", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090653108400}, "additional": {"logType": "detail", "children": [], "durationId": "68a40603-e99a-4904-ab25-9b3f7e01e466"}}, {"head": {"id": "4071e1a1-0e4a-46d9-ac4b-0ae31375ff23", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090655188000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1a47c94-1406-459d-9fc6-fbd497298735", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090655339200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87f4689c-5291-42dd-8d96-7cdfd2506623", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090662562200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73839841-d596-4144-acf2-fb187f05261f", "name": "entry : default@BuildJS cost memory 0.3510589599609375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090674546900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "973a3a36-9311-4acc-bf25-a882a98f473b", "name": "runTaskFromQueue task cost before running: 974 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090674737100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79dc8943-0210-418f-b4a8-9f70f3dd974d", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090662545900, "endTime": 224090674824100, "totalTime": 12159400}, "additional": {"logType": "info", "children": [], "durationId": "68a40603-e99a-4904-ab25-9b3f7e01e466"}}, {"head": {"id": "ce0dc57d-b560-4b5d-b22f-468fae7fbb33", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090683748800, "endTime": 224090690668300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "8f8f46a3-475c-4222-b5fc-9c6ed2630d8d", "logId": "6491d425-be68-40a2-8a86-39901db170b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f8f46a3-475c-4222-b5fc-9c6ed2630d8d", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090677314200}, "additional": {"logType": "detail", "children": [], "durationId": "ce0dc57d-b560-4b5d-b22f-468fae7fbb33"}}, {"head": {"id": "a21b6420-3f7f-4877-bcb6-8a3280fcd70f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090679204000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6475e188-5490-4cde-a572-b1a491167436", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090679331500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc651a70-a2d5-4df9-b02d-119e214114b1", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090683761600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba92cdd0-68b3-48d1-ab52-60604811d091", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090685360100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb8fe2a0-faa9-40c4-ac5f-746f764faaa7", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090690354700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8f9a9a4-21b2-47d7-936e-e0ad3edfe52c", "name": "entry : default@CacheNativeLibs cost memory 0.09664154052734375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090690554300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6491d425-be68-40a2-8a86-39901db170b9", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090683748800, "endTime": 224090690668300}, "additional": {"logType": "info", "children": [], "durationId": "ce0dc57d-b560-4b5d-b22f-468fae7fbb33"}}, {"head": {"id": "1406de5c-5b5c-4e89-bc46-ca7440a2197d", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090697559300, "endTime": 224090699844900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "f12cc436-0aa6-4464-9f40-3e28d71d5449", "logId": "060b745e-ff3c-4635-97e6-bbed1b023f44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f12cc436-0aa6-4464-9f40-3e28d71d5449", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090692961200}, "additional": {"logType": "detail", "children": [], "durationId": "1406de5c-5b5c-4e89-bc46-ca7440a2197d"}}, {"head": {"id": "29ec9867-397c-484f-9a64-5b2f1f19577f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090695438200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9990c56-128b-4879-b92b-5fd47f92d014", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090695596900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5b47340-16a1-4b30-ac97-9e3de44d0eaa", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090697576200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "213203d0-d7ab-44f7-8369-d78e3229d8f8", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090698104400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f5f8b6f-aed0-40db-8243-68c60eda1f71", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090699607100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fe3fda6-b89f-4269-8de8-02a1edd3ad89", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07546234130859375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090699749700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "060b745e-ff3c-4635-97e6-bbed1b023f44", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090697559300, "endTime": 224090699844900}, "additional": {"logType": "info", "children": [], "durationId": "1406de5c-5b5c-4e89-bc46-ca7440a2197d"}}, {"head": {"id": "5a648a84-856e-46fe-bab0-3bff74f3afac", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090720735200, "endTime": 224090756184100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "f5727777-36a4-4d37-945f-802b4e8f0183", "logId": "ab212485-bc18-4936-9970-4e99c4a5a347"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f5727777-36a4-4d37-945f-802b4e8f0183", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090703461000}, "additional": {"logType": "detail", "children": [], "durationId": "5a648a84-856e-46fe-bab0-3bff74f3afac"}}, {"head": {"id": "364bbdf0-7fd9-4434-82a0-c641a2e62b76", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090705389700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d3a5faa-9728-4687-b55b-ab7085903199", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090705537200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9d91df0-4827-4c12-966b-b703d9fe2f66", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090720748200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94f787ac-c820-4570-9113-6b77b658dc65", "name": "Incremental task entry:default@PackageHap pre-execution cost: 33 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090755961200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21b544b1-4735-438e-86f3-d61809ceec59", "name": "entry : default@PackageHap cost memory 0.9464797973632812", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090756121300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab212485-bc18-4936-9970-4e99c4a5a347", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090720735200, "endTime": 224090756184100}, "additional": {"logType": "info", "children": [], "durationId": "5a648a84-856e-46fe-bab0-3bff74f3afac"}}, {"head": {"id": "db249cf3-d1ab-4f90-a4a4-3535537f2ad4", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090766313800, "endTime": 224090772889500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": [], "detailId": "c1868184-1ea7-4ea5-8b71-a907861c7fff", "logId": "5fc22d74-61fd-45f1-9a47-33b5f09adea9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1868184-1ea7-4ea5-8b71-a907861c7fff", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090759719700}, "additional": {"logType": "detail", "children": [], "durationId": "db249cf3-d1ab-4f90-a4a4-3535537f2ad4"}}, {"head": {"id": "adae2139-104e-4fda-a0a8-c071dbeb4863", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090761245900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "586c4803-f426-4387-a6f0-5de09c0c035d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090761402700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c11d13b5-0277-483f-b4b8-669d93444ad7", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090766333600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0013db6-2cea-4a84-bc91-cc56e5c13096", "name": "Incremental task entry:default@SignHap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090772537000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69e19f05-3d13-49cd-aca0-bcc8738cc2c8", "name": "entry : default@SignHap cost memory 0.1059722900390625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090772751500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fc22d74-61fd-45f1-9a47-33b5f09adea9", "name": "UP-TO-DATE :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090766313800, "endTime": 224090772889500}, "additional": {"logType": "info", "children": [], "durationId": "db249cf3-d1ab-4f90-a4a4-3535537f2ad4"}}, {"head": {"id": "f8ff1803-af08-4b7b-896d-9dbc37be34a8", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090780912800, "endTime": 224090795114500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "81e257db-3d02-4e06-9cec-5a70750c5e4c", "logId": "2f7fad8d-700c-4ad6-bdc8-933d0833b849"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "81e257db-3d02-4e06-9cec-5a70750c5e4c", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090776162400}, "additional": {"logType": "detail", "children": [], "durationId": "f8ff1803-af08-4b7b-896d-9dbc37be34a8"}}, {"head": {"id": "e5516093-e2c5-46dd-b8a7-68e6bbbca2b7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090778683100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ece4ce05-4569-45e0-b54e-2ab0ed35ce44", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090778892700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cc68702-f20a-408c-adcb-6a0656595a80", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090780931900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9724da78-10fb-470b-93e7-2fd1d7a0e8cd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090794227400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99c28365-73b9-4f38-adc1-8131b5a37ed8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090794578900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3508f06d-f783-4e8d-b17c-d29857bfcfdb", "name": "entry : default@CollectDebugSymbol cost memory 0.24311065673828125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090794801500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee7c878e-0177-4884-8e1c-1d43426e601f", "name": "runTaskFromQueue task cost before running: 1 s 95 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090795026400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f7fad8d-700c-4ad6-bdc8-933d0833b849", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090780912800, "endTime": 224090795114500, "totalTime": 14076000}, "additional": {"logType": "info", "children": [], "durationId": "f8ff1803-af08-4b7b-896d-9dbc37be34a8"}}, {"head": {"id": "920dd3ad-f1bf-4f99-af0f-26991ce719be", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090796785500, "endTime": 224090797016800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "28585c91-fbca-4ded-8a45-67b430063aea", "logId": "ce168281-0595-43be-97c0-ea55b7027d72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28585c91-fbca-4ded-8a45-67b430063aea", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090796729900}, "additional": {"logType": "detail", "children": [], "durationId": "920dd3ad-f1bf-4f99-af0f-26991ce719be"}}, {"head": {"id": "cd7fa232-ca67-429e-8b1d-e72ca600d58c", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090796793800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7691b258-f129-4c56-8a6f-0d46cefcc40a", "name": "entry : assembleHap cost memory 0.01165771484375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090796909700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "352013ac-406f-4908-a048-63978bb1aac2", "name": "runTaskFromQueue task cost before running: 1 s 97 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090796978500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce168281-0595-43be-97c0-ea55b7027d72", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090796785500, "endTime": 224090797016800, "totalTime": 175200}, "additional": {"logType": "info", "children": [], "durationId": "920dd3ad-f1bf-4f99-af0f-26991ce719be"}}, {"head": {"id": "a9a80464-f53e-46cc-adf4-28f3062b9285", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090811389700, "endTime": 224090811429400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5a8e6d2a-be11-4e52-aa4a-762943922eb7", "logId": "1453305c-61de-48ae-bbc6-4a8eb8494c2f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1453305c-61de-48ae-bbc6-4a8eb8494c2f", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090811389700, "endTime": 224090811429400}, "additional": {"logType": "info", "children": [], "durationId": "a9a80464-f53e-46cc-adf4-28f3062b9285"}}, {"head": {"id": "0f1fcbcb-5a7f-44de-a1e2-676aa15eb663", "name": "BUILD SUCCESSFUL in 1 s 111 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090811484800}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "799e2797-5111-44e9-817a-393affe0481c", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224089700908100, "endTime": 224090811841200}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 20, "minute": 12, "second": 0}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "e560b33e-078b-4ed5-90ae-b7cd2afa0048", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090811876600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64c13288-e244-4a5c-8f75-648588f49417", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090811943700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97203a1b-f32c-4c5a-897f-71cd8074f0f6", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090812324400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ecc25a3-e99e-49f1-b2b5-cecc2d1b9f31", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090812450000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ab7c2bc-5400-4db6-b78f-9565cc7a6070", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090812574200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc618301-c9f7-41ba-9715-9b161ba0a047", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090812660600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c2452dc-041c-4ef2-b82e-e5dcb2a5a15e", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090812727700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6f98c03-2288-44f4-ba40-d208b8a5459e", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090813825200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c49dc4a2-f45e-4e5c-8d36-3906f2ed8ef5", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090814212700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "744617b4-7c82-4f4f-ba24-63076eeb19f1", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090814310700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91187e41-3056-40f2-9a23-6dcdcfbf8eeb", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090814381800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9235c0f8-ec86-42ba-ae09-f69c7b50692f", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090814442700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6313ef4-a4cb-4b83-b781-d64893296777", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090814502800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50bf3827-1270-45a5-ad7a-8e722cafce15", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090816891500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d59cb3df-659b-40e8-a5f7-2c4186ccc58f", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090817487000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0537a196-deac-4769-b248-2ee607fe3d99", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090817841600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9964b195-ca12-426f-983b-7f77a72d9b42", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090817941500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1598c7fa-5d20-4ae0-b801-ce62caaf59c0", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090818016600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd7ffc97-3a25-4d46-a62f-a3c79d60bb30", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090818074500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "997730ad-80c0-44c5-bd2b-3ed9a6dbcee4", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090818138100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d682017-dbca-4067-bd0d-11facac1420f", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090818217300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ceb181dc-dac1-4ed7-a272-f710192d4f99", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090818274000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "207590df-b5a9-4092-8fce-6b33c282cbbc", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090822232100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12361dee-8327-4cae-a017-08ea862b1363", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090823413700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6544003a-3b94-4cc1-af3e-c34f98564122", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090823875300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1173ed2-f29b-4937-bc2c-e7979605d335", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090824151400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3da17cfc-724b-4225-9728-303cead2df3f", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090824717800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50b6c303-fbbe-4fee-842b-d8d1a6a114bb", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090826716700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da457540-b21e-4abc-aa7d-2b34faa2419e", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090829027800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a12de68-5461-469b-8f59-d9a090b000c2", "name": "Incremental task entry:default@BuildJS post-execution cost:12 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090829522800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58015654-c564-4af0-af6b-127de32dab6d", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090829637000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68240217-d27e-4d25-be2e-93c6e18a65b9", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090829729100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6289350-e420-4e4e-84ed-20bd55912f7f", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090829806100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91df1112-e0c1-4e50-828c-76eded0f30a6", "name": "There is no need to refresh cache, since the incremental task entry:default@SignHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090829876200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6be01314-339b-48f7-9ab6-6716789f0fe3", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090836743800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8b5012a-6c87-4092-8600-feb6a5dd0aa3", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090837151100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e8cb487-89ee-4436-9df2-a9537c82c1f0", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090837688800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "262cde96-5876-4659-a450-7b7b859195fb", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224090837958300}, "additional": {"logType": "debug", "children": []}}], "workLog": []}