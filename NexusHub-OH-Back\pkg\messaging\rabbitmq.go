package messaging

import (
	"context"
	"fmt"
	"time"

	"nexushub-oh-back/config"

	amqp "github.com/rabbitmq/amqp091-go"
)

// RabbitMQClient RabbitMQ客户端结构体
type RabbitMQClient struct {
	Connection *amqp.Connection
	Channel    *amqp.Channel
}

// NewRabbitMQClient 创建一个新的RabbitMQ客户端
func NewRabbitMQClient(cfg *config.RabbitMQConfig) (*RabbitMQClient, error) {
	// 构建连接URL
	url := fmt.Sprintf("amqp://%s:%s@%s:%s/",
		cfg.User, cfg.Password, cfg.Host, cfg.Port)

	// 连接RabbitMQ
	conn, err := amqp.Dial(url)
	if err != nil {
		return nil, fmt.Errorf("无法连接到RabbitMQ: %w", err)
	}

	// 创建通道
	ch, err := conn.Channel()
	if err != nil {
		conn.Close()
		return nil, fmt.Errorf("无法创建通道: %w", err)
	}

	return &RabbitMQClient{
		Connection: conn,
		Channel:    ch,
	}, nil
}

// Close 关闭连接
func (r *RabbitMQClient) Close() error {
	if r.Channel != nil {
		if err := r.Channel.Close(); err != nil {
			return fmt.Errorf("关闭通道失败: %w", err)
		}
	}

	if r.Connection != nil {
		if err := r.Connection.Close(); err != nil {
			return fmt.Errorf("关闭连接失败: %w", err)
		}
	}

	return nil
}

// DeclareQueue 声明队列
func (r *RabbitMQClient) DeclareQueue(name string, durable, autoDelete, exclusive, noWait bool) (amqp.Queue, error) {
	return r.Channel.QueueDeclare(
		name,       // 队列名称
		durable,    // 持久化
		autoDelete, // 自动删除
		exclusive,  // 独占
		noWait,     // 无等待
		nil,        // 参数
	)
}

// PublishMessage 发布消息
func (r *RabbitMQClient) PublishMessage(ctx context.Context, exchange, routingKey string, message []byte) error {
	return r.Channel.PublishWithContext(
		ctx,
		exchange,   // 交换机
		routingKey, // 路由键
		false,      // mandatory
		false,      // immediate
		amqp.Publishing{
			ContentType:  "application/json",
			DeliveryMode: amqp.Persistent,
			Timestamp:    time.Now(),
			Body:         message,
		},
	)
}

// ConsumeMessages 消费消息
func (r *RabbitMQClient) ConsumeMessages(queueName, consumerName string, autoAck, exclusive, noLocal, noWait bool) (<-chan amqp.Delivery, error) {
	return r.Channel.Consume(
		queueName,    // 队列名称
		consumerName, // 消费者名称
		autoAck,      // 自动确认
		exclusive,    // 独占
		noLocal,      // 无本地
		noWait,       // 无等待
		nil,          // 参数
	)
}
