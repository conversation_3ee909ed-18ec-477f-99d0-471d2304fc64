/*
 * @Author: xiaoxiaozhou <EMAIL>
 * @Date: 2025-06-14 22:44:30
 * @LastEditors: xiaoxiaozhou <EMAIL>
 * @LastEditTime: 2025-06-14 22:54:23
 * @FilePath: \NexusHub-OH\NexusHub-OH-Front\src\config\logto.ts
 * @Description: 
 * 技术生的工作日常
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
/**
 * Logto 认证配置
 */

export const logtoConfig = {
  // Logto 服务端点
  endpoint: process.env.REACT_APP_LOGTO_ENDPOINT || 'http://localhost:3001',
  
  // 应用 ID
  appId: process.env.REACT_APP_LOGTO_APP_ID || 'your-app-id',
  
  // 重定向 URI
  redirectUri: `${window.location.origin}/callback`,
  
  // 登出后重定向 URI
  postLogoutRedirectUri: `${window.location.origin}`,
  
  // 请求的资源
  resources: [
    process.env.REACT_APP_API_RESOURCE || 'http://localhost:8080'
  ],
  
  // 请求的权限范围
  scopes: [
    'openid',
    'profile',
    'email',
    'offline_access'
  ]
};

// Logto 用户信息类型定义
export interface LogtoUser {
  sub: string;
  name?: string;
  username?: string;
  email?: string;
  email_verified?: boolean;
  picture?: string;
  roles?: string[];
}

// Logto 认证状态类型定义
export interface LogtoAuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user?: LogtoUser;
  error?: Error;
}