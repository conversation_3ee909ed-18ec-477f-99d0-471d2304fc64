import {
  ProForm,
  ProFormDependency,
  ProFormFieldSet,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useRequest, request } from '@umijs/max';
import { Button, Input, message, Upload } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import React, { useState, useEffect } from 'react';
import { uploadFile } from '@/services/upload';
import { queryCity, queryCurrent, queryProvince, queryCountry, queryDistrict, queryStreet } from '../service';
import useStyles from './index.style';

const validatorPhone = (rule: any, value: string, callback: (message?: string) => void) => {
  if (!value) {
    callback('请输入您的联系电话!');
    return;
  }
  // 验证手机号格式（支持国内手机号）
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(value)) {
    callback('请输入正确的手机号格式!');
    return;
  }
  callback();
};

const BaseView: React.FC = () => {
  const { styles } = useStyles();
  const [avatarUploading, setAvatarUploading] = useState(false);
  const [currentAvatar, setCurrentAvatar] = useState('');
  const [submitting, setSubmitting] = useState(false);

  // 头像组件 方便以后独立，增加裁剪之类的功能
  const AvatarView = ({ avatar }: { avatar: string }) => {
    // 处理头像上传
    const handleAvatarUpload = async (options: any) => {
      const { file, onSuccess, onError } = options;
      
      try {
        setAvatarUploading(true);
        
        // 验证文件类型和大小
        const isImage = file.type.startsWith('image/');
        if (!isImage) {
          message.error('只能上传图片文件!');
          onError(new Error('文件类型错误'));
          return;
        }
        
        const isLt2M = file.size / 1024 / 1024 < 2;
        if (!isLt2M) {
          message.error('图片大小不能超过2MB!');
          onError(new Error('文件大小超限'));
          return;
        }
        
        // 上传文件
        const fileUrl = await uploadFile('avatar', file);
        
        // 更新用户头像
        const response = await request<API.Response & { data?: API.UserResponse }>('/users/profile', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          data: {
            avatar: fileUrl,
          },
        });
        
        if (response.code === 200) {
          message.success('头像更新成功');
          // 立即更新本地头像状态
          setCurrentAvatar(fileUrl);
          onSuccess(fileUrl);
          // 重新获取用户数据以确保数据同步
          refreshUserData();
        } else {
          message.error(response.message || '头像更新失败');
          onError(new Error(response.message || '更新失败'));
        }
      } catch (error) {
        console.error('头像上传失败:', error);
        message.error('头像上传失败，请稍后再试');
        onError(error);
      } finally {
        setAvatarUploading(false);
      }
    };
    
    return (
      <>
        <div className={styles.avatar_title}>头像</div>
        <div className={styles.avatar}>
          <img src={currentAvatar || avatar} alt="avatar" />
        </div>
        <Upload 
          showUploadList={false}
          customRequest={handleAvatarUpload}
          accept="image/*"
          disabled={avatarUploading}
          beforeUpload={() => false}
        >
          <div className={styles.button_view}>
            <Button loading={avatarUploading}>
              <UploadOutlined />
              {avatarUploading ? '上传中...' : '更换头像'}
            </Button>
          </div>
        </Upload>
      </>
    );
  };
  const { data: currentUser, loading, refresh: refreshUserData } = useRequest(() => {
    return queryCurrent();
  });

  // 当用户数据加载完成后，初始化头像状态
  useEffect(() => {
    if (currentUser) {
      // 无论是否有头像都要设置，确保状态同步
      setCurrentAvatar(currentUser.avatar || '');
    }
  }, [currentUser]);
  const getAvatarURL = () => {
    if (currentUser) {
      if (currentUser.avatar) {
        return currentUser.avatar;
      }
      const url = 'https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png';
      return url;
    }
    return '';
  };
  const handleFinish = async (values: Record<string, any>) => {
    if (submitting) {
      return; // 防止重复提交
    }
    
    try {
      setSubmitting(true);
      
      // 构建更新请求数据
      const updateData: API.UpdateProfileRequest = {
        username: values.name,
        email: values.email,
        description: values.profile,
        company_name: values.group,
        address: values.detailAddress,
        province: values.province?.label || values.province,
        city: values.city?.label || values.city,
        district: values.district?.label || values.district,
        street: values.street?.label || values.street,
        phone: values.phone,
      };

      console.log('🔍 [DEBUG] 提交更新数据:', updateData);

      // 调用后端API更新用户资料
      const response = await request<API.Response & { data?: API.UserResponse }>('/users/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        data: updateData,
      });

      console.log('🔍 [DEBUG] 更新响应:', response);

      if (response.code === 200 || response.code === 0) {
        message.success('更新基本信息成功');
        // 重新获取用户数据以刷新页面显示
        refreshUserData();
      } else {
        message.error(response.message || '更新基本信息失败');
      }
    } catch (error) {
      console.error('更新用户资料失败:', error);
      message.error('更新基本信息失败，请稍后再试');
    } finally {
      setSubmitting(false);
    }
  };
  return (
    <div className={styles.baseView}>
      {loading ? null : (
        <>
          <div className={styles.left}>
            <ProForm
              layout="vertical"
              onFinish={handleFinish}
              submitter={{
                searchConfig: {
                  submitText: submitting ? '更新中...' : '更新基本信息',
                },
                render: (_, dom) => dom[1],
                submitButtonProps: {
                  loading: submitting,
                  disabled: submitting,
                },
              }}
              initialValues={{
                ...currentUser,
                phone: currentUser?.phone,
                country: currentUser?.country ? {
                  label: currentUser.country === 'China' ? '中国' : currentUser.country,
                  value: currentUser.country === 'China' ? 'CN' : currentUser.country
                } : undefined,
                province: currentUser?.geographic?.province ? {
                  label: currentUser.geographic.province.name,
                  value: currentUser.geographic.province.id
                } : undefined,
                city: currentUser?.geographic?.city ? {
                  label: currentUser.geographic.city.name,
                  value: currentUser.geographic.city.id
                } : undefined,
                district: currentUser?.geographic?.district ? {
                  label: currentUser.geographic.district.name,
                  value: currentUser.geographic.district.id
                } : undefined,
                street: currentUser?.geographic?.street ? {
                  label: currentUser.geographic.street.name,
                  value: currentUser.geographic.street.id
                } : undefined,
              }}
              hideRequiredMark
            >
              <ProFormText
                width="md"
                name="email"
                label="邮箱"
                rules={[
                  {
                    required: true,
                    message: '请输入您的邮箱!',
                  },
                ]}
              />
              <ProFormText
                width="md"
                name="name"
                label="昵称"
                rules={[
                  {
                    required: true,
                    message: '请输入您的昵称!',
                  },
                ]}
              />
              <ProFormTextArea
                name="profile"
                label="个人简介"
                rules={[
                  {
                    required: true,
                    message: '请输入个人简介!',
                  },
                ]}
                placeholder="个人简介"
              />
              <ProFormSelect
                width="sm"
                name="country"
                label="国家/地区"
                placeholder="请选择国家/地区"
                rules={[
                  {
                    required: true,
                    message: '请输入您的国家或地区!',
                  },
                ]}
                request={async () => {
                  console.log('🔍 [DEBUG] 开始请求国家数据...');
                  return queryCountry().then(({ data }) => {
                    console.log('🔍 [DEBUG] 国家数据原始响应:', data);
                    const result = data.map((item) => {
                      console.log('🔍 [DEBUG] 处理国家项:', item);
                      return {
                        label: item.label || item.name,
                        value: item.key || item.id,
                      };
                    });
                    console.log('🔍 [DEBUG] 国家数据最终结果:', result);
                    return result;
                  }).catch(error => {
                    console.error('🔍 [DEBUG] 国家数据请求失败:', error);
                    return [];
                  });
                }}
              />

              <ProForm.Group title="所在省市" size={8}>
                <ProFormSelect
                  rules={[
                    {
                      required: true,
                      message: '请输入您的所在省!',
                    },
                  ]}
                  width="sm"
                  placeholder="请选择省份"
                  fieldProps={{
                    labelInValue: true,
                  }}
                  name="province"
                  className={styles.item}
                  request={async () => {
                    console.log('🔍 [DEBUG] 开始请求省份数据...');
                    return queryProvince().then(({ data }) => {
                      console.log('🔍 [DEBUG] 省份数据原始响应:', data);
                      const result = data.map((item) => {
                        console.log('🔍 [DEBUG] 处理省份项:', item);
                        return {
                          label: item.label || item.name,
                          value: item.key || item.id,
                        };
                      });
                      console.log('🔍 [DEBUG] 省份数据最终结果:', result);
                      return result;
                    }).catch(error => {
                      console.error('🔍 [DEBUG] 省份数据请求失败:', error);
                      return [];
                    });
                  }}
                />
                <ProFormDependency name={['province']}>
                  {({ province }) => {
                    return (
                      <ProFormSelect
                        params={{
                          key: province?.value,
                        }}
                        name="city"
                        width="sm"
                        placeholder="请选择城市"
                        rules={[
                          {
                            required: true,
                            message: '请输入您的所在城市!',
                          },
                        ]}
                        disabled={!province}
                        className={styles.item}
                        fieldProps={{
                          labelInValue: true,
                        }}
                        request={async () => {
                          if (!province?.value) {
                            return [];
                          }
                          console.log('🔍 [DEBUG] 开始请求城市数据，省份ID:', province.value);
                          return queryCity(province.value || '').then(({ data }) => {
                            console.log('🔍 [DEBUG] 城市数据原始响应:', data);
                            // 检查data是否为null或undefined
                            if (!data || !Array.isArray(data)) {
                              console.log('🔍 [DEBUG] 城市数据为空或格式错误，返回空数组');
                              return [];
                            }
                            const result = data.map((item) => {
                              console.log('🔍 [DEBUG] 处理城市项:', item);
                              return {
                                label: item.label || item.name,
                                value: item.key || item.id,
                              };
                            });
                            console.log('🔍 [DEBUG] 城市数据最终结果:', result);
                            return result;
                          }).catch(error => {
                            console.error('🔍 [DEBUG] 城市数据请求失败:', error);
                            return [];
                          });
                        }}
                      />
                    );
                  }}
                </ProFormDependency>
              </ProForm.Group>
              
              <ProForm.Group title="区镇街道" size={8}>
                <ProFormDependency name={['city']}>
                  {({ city }) => {
                    return (
                      <ProFormSelect
                        params={{
                          key: city?.value,
                        }}
                        name="district"
                        width="sm"
                        placeholder="请选择区/县"
                        rules={[
                          {
                            required: true,
                            message: '请选择您的区镇!',
                          },
                        ]}
                        disabled={!city}
                        className={styles.item}
                        fieldProps={{
                          labelInValue: true,
                        }}
                        request={async () => {
                          if (!city?.value) {
                            return [];
                          }
                          console.log('🔍 [DEBUG] 开始请求区镇数据，城市ID:', city.value);
                          return queryDistrict(city.value || '').then(({ data }) => {
                            console.log('🔍 [DEBUG] 区镇数据原始响应:', data);
                            // 检查data是否为null或undefined
                            if (!data || !Array.isArray(data)) {
                              console.log('🔍 [DEBUG] 区镇数据为空或格式错误，返回空数组');
                              return [];
                            }
                            const result = data.map((item) => {
                              console.log('🔍 [DEBUG] 处理区镇项:', item);
                              return {
                                label: item.label || item.name,
                                value: item.key || item.id,
                              };
                            });
                            console.log('🔍 [DEBUG] 区镇数据最终结果:', result);
                            return result;
                          }).catch(error => {
                            console.error('🔍 [DEBUG] 区镇数据请求失败:', error);
                            return [];
                          });
                        }}
                      />
                    );
                  }}
                </ProFormDependency>
                
                <ProFormDependency name={['district']}>
                  {({ district }) => {
                    return (
                      <ProFormSelect
                        params={{
                          key: district?.value,
                        }}
                        name="street"
                        width="sm"
                        placeholder="请选择街道"
                        rules={[
                          {
                            required: true,
                            message: '请选择您的街道!',
                          },
                        ]}
                        disabled={!district}
                        className={styles.item}
                        fieldProps={{
                          labelInValue: true,
                        }}
                        request={async () => {
                          if (!district?.value) {
                            return [];
                          }
                          console.log('🔍 [DEBUG] 开始请求街道数据，区镇ID:', district.value);
                          return queryStreet(district.value || '').then(({ data }) => {
                            console.log('🔍 [DEBUG] 街道数据原始响应:', data);
                            // 检查data是否为null或undefined
                            if (!data || !Array.isArray(data)) {
                              console.log('🔍 [DEBUG] 街道数据为空或格式错误，返回空数组');
                              return [];
                            }
                            const result = data.map((item) => {
                              console.log('🔍 [DEBUG] 处理街道项:', item);
                              return {
                                label: item.label || item.name,
                                value: item.key || item.id,
                              };
                            });
                            console.log('🔍 [DEBUG] 街道数据最终结果:', result);
                            return result;
                          }).catch(error => {
                            console.error('🔍 [DEBUG] 街道数据请求失败:', error);
                            return [];
                          });
                        }}
                      />
                    );
                  }}
                </ProFormDependency>
              </ProForm.Group>
              
              <ProFormText
                width="md"
                name="detailAddress"
                label="详细地址"
                rules={[
                  {
                    required: true,
                    message: '请输入您的详细地址!',
                  },
                ]}
                placeholder="请输入您的详细地址!"
              />
              <ProFormText
                width="md"
                name="phone"
                label="联系电话"
                rules={[
                  {
                    required: true,
                    message: '请输入您的联系电话!',
                  },
                  {
                    validator: validatorPhone,
                  },
                ]}
                placeholder="请输入您的手机号"
              />
            </ProForm>
          </div>
          <div className={styles.right}>
            <AvatarView avatar={getAvatarURL()} />
          </div>
        </>
      )}
    </div>
  );
};
export default BaseView;
