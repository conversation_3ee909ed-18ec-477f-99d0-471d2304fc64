package models

import (
	"time"

	"gorm.io/gorm"
)

// Notification 通知模型
type Notification struct {
	ID          uint      `gorm:"primarykey" json:"id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   *time.Time `gorm:"index" json:"deleted_at,omitempty"`
	UserID      uint             `gorm:"index;not null" json:"user_id"`
	User        User             `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Title       string           `gorm:"type:varchar(255);not null" json:"title"`
	Content     string           `gorm:"type:text;not null" json:"content"`
	Type        NotificationType `gorm:"type:varchar(50);not null" json:"type"`
	Category    string           `gorm:"type:varchar(50);not null" json:"category"` // app_review, developer_verify, version_update
	RelatedID   uint             `gorm:"index" json:"related_id"`                   // 关联的ID（应用ID、版本ID等）
	RelatedType string           `gorm:"type:varchar(50)" json:"related_type"`       // 关联类型（application, version等）
	IsRead      bool             `gorm:"default:false" json:"is_read"`
	ReadAt      *time.Time       `json:"read_at"`
	EmailSent   bool             `gorm:"default:false" json:"email_sent"`
	EmailSentAt *time.Time       `json:"email_sent_at"`
	Priority    Priority         `gorm:"type:varchar(20);default:'normal'" json:"priority"`
	ExpiresAt   *time.Time       `json:"expires_at"` // 通知过期时间
	Metadata    string           `gorm:"type:json" json:"metadata"` // 额外的元数据，JSON格式
}

// NotificationType 通知类型
type NotificationType string

const (
	// NotificationTypeInfo 信息通知
	NotificationTypeInfo NotificationType = "info"
	// NotificationTypeSuccess 成功通知
	NotificationTypeSuccess NotificationType = "success"
	// NotificationTypeWarning 警告通知
	NotificationTypeWarning NotificationType = "warning"
	// NotificationTypeError 错误通知
	NotificationTypeError NotificationType = "error"
)

// Priority 优先级
type Priority string

const (
	// PriorityLow 低优先级
	PriorityLow Priority = "low"
	// PriorityNormal 普通优先级
	PriorityNormal Priority = "normal"
	// PriorityHigh 高优先级
	PriorityHigh Priority = "high"
	// PriorityUrgent 紧急优先级
	PriorityUrgent Priority = "urgent"
)

// NotificationCategory 通知分类常量
const (
	// CategoryAppReview 应用审核
	CategoryAppReview = "app_review"
	// CategoryDeveloperVerify 开发者认证
	CategoryDeveloperVerify = "developer_verify"
	// CategoryVersionUpdate 版本更新
	CategoryVersionUpdate = "version_update"
	// CategorySystem 系统通知
	CategorySystem = "system"
)

// TableName 指定表名
func (Notification) TableName() string {
	return "notifications"
}

// NotificationSettings 用户通知设置模型
type NotificationSettings struct {
	ID          uint      `gorm:"primarykey" json:"id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   *time.Time `gorm:"index" json:"deleted_at,omitempty"`
	UserID                uint `gorm:"uniqueIndex;not null" json:"user_id"`
	User                  User `gorm:"foreignKey:UserID" json:"user,omitempty"`
	WebNotificationEnabled bool `gorm:"default:true" json:"web_notification_enabled"`   // 网页通知
	EmailNotificationEnabled bool `gorm:"default:false" json:"email_notification_enabled"` // 邮件通知
	// 分类通知设置
	AppReviewWebEnabled     bool `gorm:"default:true" json:"app_review_web_enabled"`
	AppReviewEmailEnabled   bool `gorm:"default:false" json:"app_review_email_enabled"`
	DeveloperVerifyWebEnabled bool `gorm:"default:true" json:"developer_verify_web_enabled"`
	DeveloperVerifyEmailEnabled bool `gorm:"default:false" json:"developer_verify_email_enabled"`
	VersionUpdateWebEnabled bool `gorm:"default:true" json:"version_update_web_enabled"`
	VersionUpdateEmailEnabled bool `gorm:"default:false" json:"version_update_email_enabled"`
	SystemWebEnabled        bool `gorm:"default:true" json:"system_web_enabled"`
	SystemEmailEnabled      bool `gorm:"default:false" json:"system_email_enabled"`
}

// TableName 指定表名
func (NotificationSettings) TableName() string {
	return "notification_settings"
}

// CreateNotification 创建通知
func CreateNotification(db *gorm.DB, notification *Notification) error {
	return db.Create(notification).Error
}

// GetUserNotifications 获取用户通知列表
func GetUserNotifications(db *gorm.DB, userID uint, page, pageSize int, unreadOnly bool) ([]Notification, int64, error) {
	var notifications []Notification
	var count int64

	offset := (page - 1) * pageSize

	query := db.Model(&Notification{}).Where("user_id = ?", userID)

	if unreadOnly {
		query = query.Where("is_read = ?", false)
	}

	// 排除过期的通知
	query = query.Where("expires_at IS NULL OR expires_at > ?", time.Now())

	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	if err := query.Order("created_at desc").Offset(offset).Limit(pageSize).Find(&notifications).Error; err != nil {
		return nil, 0, err
	}

	return notifications, count, nil
}

// MarkNotificationAsRead 标记通知为已读
func MarkNotificationAsRead(db *gorm.DB, notificationID, userID uint) error {
	now := time.Now()
	return db.Model(&Notification{}).
		Where("id = ? AND user_id = ?", notificationID, userID).
		Updates(map[string]interface{}{
			"is_read": true,
			"read_at": &now,
		}).Error
}

// MarkAllNotificationsAsRead 标记所有通知为已读
func MarkAllNotificationsAsRead(db *gorm.DB, userID uint) error {
	now := time.Now()
	return db.Model(&Notification{}).
		Where("user_id = ? AND is_read = ?", userID, false).
		Updates(map[string]interface{}{
			"is_read": true,
			"read_at": &now,
		}).Error
}

// GetUnreadNotificationCount 获取未读通知数量
func GetUnreadNotificationCount(db *gorm.DB, userID uint) (int64, error) {
	var count int64
	err := db.Model(&Notification{}).
		Where("user_id = ? AND is_read = ? AND (expires_at IS NULL OR expires_at > ?)", userID, false, time.Now()).
		Count(&count).Error
	return count, err
}

// GetOrCreateNotificationSettings 获取或创建用户通知设置
func GetOrCreateNotificationSettings(db *gorm.DB, userID uint) (*NotificationSettings, error) {
	var settings NotificationSettings
	err := db.Where("user_id = ?", userID).First(&settings).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 创建默认设置
			settings = NotificationSettings{
				UserID:                        userID,
				WebNotificationEnabled:        true,
				EmailNotificationEnabled:      false,
				AppReviewWebEnabled:           true,
				AppReviewEmailEnabled:         false,
				DeveloperVerifyWebEnabled:     true,
				DeveloperVerifyEmailEnabled:   false,
				VersionUpdateWebEnabled:       true,
				VersionUpdateEmailEnabled:     false,
				SystemWebEnabled:              true,
				SystemEmailEnabled:            false,
			}
			if err := db.Create(&settings).Error; err != nil {
				return nil, err
			}
		} else {
			return nil, err
		}
	}
	return &settings, nil
}

// UpdateNotificationSettings 更新用户通知设置
func UpdateNotificationSettings(db *gorm.DB, userID uint, settings *NotificationSettings) error {
	return db.Model(&NotificationSettings{}).Where("user_id = ?", userID).Updates(settings).Error
}