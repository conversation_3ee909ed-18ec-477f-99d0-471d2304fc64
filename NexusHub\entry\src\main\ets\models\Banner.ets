/**
 * 轮播图数据模型
 */
export interface BannerModel {
  id: number;
  title: string;
  subtitle?: string;
  image_url: string;
  link_type: 'app' | 'url' | 'category' | 'none';
  link_value?: string; // 根据link_type存储不同的值：app_id、url、category_id等
  sort_order: number;
  is_active: boolean;
  start_time?: string;
  end_time?: string;
  created_at: string;
  updated_at: string;
}

/**
 * 轮播图响应模型
 */
export interface BannerResponse {
  code: number;
  message: string;
  data: BannerModel[];
}