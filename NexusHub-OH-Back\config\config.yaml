# NexusHub-OH 应用商店后端配置文件

# 服务器配置
server:
  port: 8080  # 注意这里是数字而不是字符串
  host: "0.0.0.0"
  mode: "debug" # debug, release, test

# 数据库配置
database:
  host: "**********"
  port: 5432  # 注意这里是数字而不是字符串
  user: "nexushub"
  password: "nexushub"
  dbname: "nexushub"
  sslmode: "disable"

# Redis配置
redis:
  addr: "**********:6379"  # 使用addr字段替换host和port
  password: "redis_NHh8tw"
  db: 0

# JWT配置
jwt:
  secret: "nexushub_secret_key_please_change_in_production"
  expire: 24h  # 使用expire替换expire_time，并添加时间单位

# Logto配置
logto:
  enabled: false # 设置为true启用Logto认证
  endpoint: "https://your-logto-instance.logto.app" # Logto服务端点
  app_id: "your-app-id" # 从Logto控制台获取
  app_secret: "your-app-secret" # 从Logto控制台获取
  api_resource: "https://api.nexushub-oh.top" # API资源标识符
  jwks_endpoint: "https://your-logto-instance.logto.app/oidc/jwks" # JWKS端点

# 存储配置
storage:
  type: "minio"  # 使用type替换provider
  endpoint: "**********:9000"
  region: "us-east-1"
  bucket: "nexushub"
  access_key: "minio_kcHS83"
  secret_key: "minio_cFtmm8"
  base_path: "./uploads"  # 添加base_path字段

# RabbitMQ配置
rabbitmq:
  host: "**********"
  port: 5672
  user: "rabbitmq"
  password: "rabbitmq"

# Elasticsearch配置
elasticsearch:
  host: "**********"
  port: "9200"
  use_auth: true
  username: "elastic"
  password: "Elastic_fdTWPJ"

# 日志配置
log:
  level: "info" # debug, info, warn, error
  file: "logs/app.log"
  max_size: 100   # MB
  max_backups: 10
  max_age: 30     # 天
  compress: true  # 是否压缩

# 跨域配置
cors:
  allow_origins: ["*"]
  allow_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"]
  allow_headers: ["Origin", "Content-Type", "Content-Length", "Accept-Encoding", "X-CSRF-Token", "Authorization", "Accept"]
  expose_headers: ["Content-Length"]
  allow_credentials: true
  max_age: "12h"

# 限流配置
rate_limit:
  enable: true
  requests: 60  # 每分钟最大请求次数
  period: 1m    # 使用period替换duration
  cleanup_time: 10m  # 使用cleanup_time替换cleanup

# 仪表盘配置
dashboard:
  analytics_enabled: true
  monitoring_enabled: true
  workbench_enabled: true
  monitoring_interval: 60s
  alert_enabled: true
  alert_check_interval: 5m