FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装依赖
RUN apk add --no-cache ca-certificates git

# 复制Go模块文件
COPY go.mod go.sum ./
RUN go mod download

# 复制源代码
COPY . .

# 编译应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o nexushub-oh-back .

# 使用轻量级镜像
FROM alpine:latest

# 安装CA证书和时区数据
RUN apk --no-cache add ca-certificates tzdata

# 设置时区
ENV TZ=Asia/Shanghai

# 创建非root用户
RUN adduser -D -g '' appuser

# 从builder阶段复制二进制文件
COPY --from=builder /app/nexushub-oh-back /app/nexushub-oh-back

# 复制配置文件
COPY --from=builder /app/config /app/config

# 创建配置和日志目录
RUN mkdir -p /app/logs
RUN chown -R appuser:appuser /app

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8080

# 设置工作目录
WORKDIR /app

# 运行应用
CMD ["./nexushub-oh-back"]