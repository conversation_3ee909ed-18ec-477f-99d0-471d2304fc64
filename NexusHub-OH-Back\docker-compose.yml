version: '3.8'

services:
  app:
    build: .
    container_name: nexushub-oh-back
    restart: unless-stopped
    environment:
      # 服务器配置
      SERVER_PORT: 8080
      SERVER_MODE: debug
      # 数据库配置
      DB_HOST: **********
      DB_PORT: 5432
      DB_USER: nexushub
      DB_PASSWORD: nexushub
      DB_NAME: nexushub
      DB_SSL_MODE: disable
      # Redis配置
      REDIS_HOST: **********
      REDIS_PORT: 6379
      REDIS_PASSWORD: redis_NHh8tw
      REDIS_DB: 0
      # JWT配置
      JWT_SECRET: nexushub_secret_key_secure_please_change_in_production
      JWT_EXPIRE_TIME: 24
      # 存储配置
      STORAGE_PROVIDER: minio
      STORAGE_ENDPOINT: **********:9000
      STORAGE_REGION: us-east-1
      STORAGE_BUCKET: nexushub
      STORAGE_ACCESS_KEY: minio_kcHS83
      STORAGE_SECRET_KEY: minio_cFtmm8
      # RabbitMQ配置
      RABBITMQ_HOST: **********
      RABBITMQ_PORT: 5672
      RABBITMQ_USER: rabbitmq
      RABBITMQ_PASSWORD: RabbitMQ
      # Elasticsearch配置
      ES_HOST: **********
      ES_PORT: 9200
      ES_USE_AUTH: "true"
      ES_USERNAME: elastic
      ES_PASSWORD: Elastic_fdTWPJ
    ports:
      - "8080:8080"
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    networks:
      - nexushub_network

# 以下服务已经在外部环境中提供，不需要在docker-compose中创建
# 我们只需配置我们自己的应用服务

networks:
  nexushub_network:
    driver: bridge