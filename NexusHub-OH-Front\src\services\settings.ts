// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/**
 * 获取参数列表
 * @param params - 查询参数
 */
export async function getParamList(params?: {
  group?: string;
  keyword?: string;
}) {
  return request('/settings/params', {
    method: 'GET',
    params,
  });
}

/**
 * 更新参数
 * @param data - 参数数据
 */
export async function updateParam(data: {
  key: string;
  value: any;
}) {
  return request('/settings/params', {
    method: 'PUT',
    data,
  });
}

/**
 * 获取系统日志
 * @param params - 查询参数
 */
export async function getSystemLogs(params?: {
  page?: number;
  pageSize?: number;
  level?: 'info' | 'warning' | 'error';
  startTime?: string;
  endTime?: string;
}) {
  return request('/settings/logs', {
    method: 'GET',
    params,
  });
}

/**
 * 获取标签列表
 * @param params - 查询参数
 */
export async function getTagList(params?: {
  page?: number;
  pageSize?: number;
  keyword?: string;
}) {
  return request('/settings/tags', {
    method: 'GET',
    params,
  });
}

/**
 * 创建标签
 * @param data - 标签数据
 */
export async function createTag(data: {
  name: string;
  description?: string;
}) {
  return request('/settings/tags', {
    method: 'POST',
    data,
  });
}

/**
 * 更新标签
 * @param id - 标签ID
 * @param data - 更新数据
 */
export async function updateTag(id: string, data: {
  name?: string;
  description?: string;
}) {
  return request(`/settings/tags/${id}`, {
    method: 'PUT',
    data,
  });
}

/**
 * 删除标签
 * @param id - 标签ID
 */
export async function deleteTag(id: string) {
  return request(`/settings/tags/${id}`, {
    method: 'DELETE',
  });
}