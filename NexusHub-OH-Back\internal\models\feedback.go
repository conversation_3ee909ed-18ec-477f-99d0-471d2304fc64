package models

import (
	"time"

	"gorm.io/gorm"
)

// FeedbackType 反馈类型枚举
type FeedbackType string

const (
	FeedbackTypeBug         FeedbackType = "bug"         // 错误报告
	FeedbackTypeFeature     FeedbackType = "feature"     // 功能建议
	FeedbackTypeImprovement FeedbackType = "improvement" // 改进建议
	FeedbackTypeComplaint   FeedbackType = "complaint"   // 投诉
	FeedbackTypeOther       FeedbackType = "other"       // 其他
)

// FeedbackStatus 反馈状态枚举
type FeedbackStatus string

const (
	FeedbackStatusPending    FeedbackStatus = "pending"    // 待处理
	FeedbackStatusProcessing FeedbackStatus = "processing" // 处理中
	FeedbackStatusResolved   FeedbackStatus = "resolved"   // 已解决
	FeedbackStatusClosed     FeedbackStatus = "closed"     // 已关闭
	FeedbackStatusRejected   FeedbackStatus = "rejected"   // 已拒绝
)

// FeedbackPriority 反馈优先级枚举
type FeedbackPriority string

const (
	FeedbackPriorityLow      FeedbackPriority = "low"      // 低
	FeedbackPriorityNormal   FeedbackPriority = "normal"   // 普通
	FeedbackPriorityHigh     FeedbackPriority = "high"     // 高
	FeedbackPriorityCritical FeedbackPriority = "critical" // 紧急
)

// Feedback 意见反馈模型
type Feedback struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 基本信息
	Title    string           `gorm:"type:varchar(200);not null" json:"title"`           // 标题
	Content  string           `gorm:"type:text;not null" json:"content"`                 // 内容
	Type     FeedbackType     `gorm:"type:varchar(20);not null" json:"type"`             // 类型
	Status   FeedbackStatus   `gorm:"type:varchar(20);default:'pending'" json:"status"`  // 状态
	Priority FeedbackPriority `gorm:"type:varchar(20);default:'normal'" json:"priority"` // 优先级
	Tags     string           `gorm:"type:varchar(500)" json:"tags"`                     // 标签，逗号分隔

	// 分类信息
	CategoryID *uint             `gorm:"index" json:"category_id"`
	Category   *FeedbackCategory `gorm:"foreignKey:CategoryID" json:"category,omitempty"`

	// 用户信息
	UserID uint `gorm:"not null;index" json:"user_id"`
	User   User `gorm:"foreignKey:UserID" json:"user,omitempty"`

	// 联系信息（用户可能希望通过其他方式联系）
	ContactEmail string `gorm:"type:varchar(100)" json:"contact_email"`
	ContactPhone string `gorm:"type:varchar(20)" json:"contact_phone"`

	// 相关应用（如果反馈与特定应用相关）
	AppID *uint        `gorm:"index" json:"app_id"`
	App   *Application `gorm:"foreignKey:AppID" json:"app,omitempty"`

	// 设备和环境信息
	DeviceInfo    string `gorm:"type:text" json:"device_info"`            // 设备信息
	SystemVersion string `gorm:"type:varchar(100)" json:"system_version"` // 系统版本
	AppVersion    string `gorm:"type:varchar(50)" json:"app_version"`     // 应用版本
	UserAgent     string `gorm:"type:text" json:"user_agent"`             // 用户代理
	IPAddress     string `gorm:"type:varchar(45)" json:"ip_address"`      // IP地址

	// 附件信息
	Attachments string `gorm:"type:text" json:"attachments"` // 附件URL，JSON格式存储
	Screenshots string `gorm:"type:text" json:"screenshots"` // 截图URL，JSON格式存储

	// 处理信息
	AssignedTo *uint `gorm:"index" json:"assigned_to"` // 分配给的管理员
	Assignee   *User `gorm:"foreignKey:AssignedTo" json:"assignee,omitempty"`

	// 解决信息
	ResolvedAt     *time.Time `json:"resolved_at"`
	Resolution     string     `gorm:"type:text" json:"resolution"`      // 解决方案
	ResolutionNote string     `gorm:"type:text" json:"resolution_note"` // 解决备注

	// 评分（用户对处理结果的满意度）
	Rating     *int       `gorm:"check:rating >= 1 AND rating <= 5" json:"rating"`
	RatingNote string     `gorm:"type:text" json:"rating_note"` // 评分备注
	RatedAt    *time.Time `json:"rated_at"`

	// 关联的回复
	Replies []FeedbackReply `gorm:"foreignKey:FeedbackID" json:"replies,omitempty"`
}

// TableName 指定表名
func (Feedback) TableName() string {
	return "feedbacks"
}

// FeedbackReply 反馈回复模型
type FeedbackReply struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 反馈信息
	FeedbackID uint     `gorm:"not null;index" json:"feedback_id"`
	Feedback   Feedback `gorm:"foreignKey:FeedbackID" json:"feedback,omitempty"`

	// 回复内容
	Content string `gorm:"type:text;not null" json:"content"`

	// 回复者信息
	ReplyBy uint `gorm:"not null" json:"reply_by"`
	Replier User `gorm:"foreignKey:ReplyBy" json:"replier,omitempty"`

	// 回复类型
	ReplyType string `gorm:"type:varchar(20);default:'admin'" json:"reply_type"` // admin, system, auto

	// 是否为内部备注（不对用户显示）
	IsInternal bool `gorm:"default:false" json:"is_internal"`

	// 附件
	Attachments string `gorm:"type:text" json:"attachments"`
}

// TableName 指定表名
func (FeedbackReply) TableName() string {
	return "feedback_replies"
}

// FeedbackCategory 反馈分类模型（用于统计和管理）
type FeedbackCategory struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	Name        string `gorm:"type:varchar(100);not null" json:"name"`
	Description string `gorm:"type:text" json:"description"`
	Color       string `gorm:"type:varchar(20)" json:"color"`
	Icon        string `gorm:"type:varchar(255)" json:"icon"`
	SortOrder   int    `gorm:"default:0" json:"sort_order"`
	IsActive    bool   `gorm:"default:true" json:"is_active"`
}

// TableName 指定表名
func (FeedbackCategory) TableName() string {
	return "feedback_categories"
}

// 响应结构体

// FeedbackResponse 反馈响应
type FeedbackResponse struct {
	ID             uint             `json:"id"`
	Title          string           `json:"title"`
	Content        string           `json:"content"`
	Type           FeedbackType     `json:"type"`
	Status         FeedbackStatus   `json:"status"`
	Priority       FeedbackPriority `json:"priority"`
	Tags           []string         `json:"tags"`
	ContactEmail   string           `json:"contact_email"`
	ContactPhone   string           `json:"contact_phone"`
	DeviceInfo     string           `json:"device_info"`
	SystemVersion  string           `json:"system_version"`
	AppVersion     string           `json:"app_version"`
	Attachments    []string         `json:"attachments"`
	Screenshots    []string         `json:"screenshots"`
	Resolution     string           `json:"resolution"`
	ResolutionNote string           `json:"resolution_note"`
	Rating         *int             `json:"rating"`
	RatingNote     string           `json:"rating_note"`
	CreatedAt      time.Time        `json:"created_at"`
	UpdatedAt      time.Time        `json:"updated_at"`
	ResolvedAt     *time.Time       `json:"resolved_at"`
	RatedAt        *time.Time       `json:"rated_at"`

	// 用户信息
	User struct {
		ID       uint   `json:"id"`
		Username string `json:"username"`
		Email    string `json:"email"`
		Avatar   string `json:"avatar"`
	} `json:"user"`

	// 分类信息
	Category *struct {
		ID          uint   `json:"id"`
		Name        string `json:"name"`
		Description string `json:"description"`
		Color       string `json:"color"`
		Icon        string `json:"icon"`
	} `json:"category,omitempty"`

	// 相关应用信息
	App *struct {
		ID          uint   `json:"id"`
		Name        string `json:"name"`
		PackageName string `json:"package_name"`
		Icon        string `json:"icon"`
	} `json:"app,omitempty"`

	// 分配的管理员信息
	Assignee *struct {
		ID       uint   `json:"id"`
		Username string `json:"username"`
		Avatar   string `json:"avatar"`
	} `json:"assignee,omitempty"`

	// 回复列表
	Replies []FeedbackReplyResponse `json:"replies,omitempty"`
}

// FeedbackListResponse 反馈列表响应
type FeedbackListResponse struct {
	ID        uint             `json:"id"`
	Title     string           `json:"title"`
	Type      FeedbackType     `json:"type"`
	Status    FeedbackStatus   `json:"status"`
	Priority  FeedbackPriority `json:"priority"`
	CreatedAt time.Time        `json:"created_at"`
	UpdatedAt time.Time        `json:"updated_at"`

	// 用户信息
	User struct {
		ID       uint   `json:"id"`
		Username string `json:"username"`
	} `json:"user"`

	// 回复数量
	ReplyCount int `json:"reply_count"`
}

// FeedbackReplyResponse 反馈回复响应
type FeedbackReplyResponse struct {
	ID          uint      `json:"id"`
	Content     string    `json:"content"`
	ReplyType   string    `json:"reply_type"`
	IsInternal  bool      `json:"is_internal"`
	Attachments []string  `json:"attachments"`
	CreatedAt   time.Time `json:"created_at"`

	// 回复者信息
	Replier struct {
		ID       uint   `json:"id"`
		Username string `json:"username"`
		Avatar   string `json:"avatar"`
		Role     string `json:"role"`
	} `json:"replier"`
}

// FeedbackCategoryResponse 反馈分类响应
type FeedbackCategoryResponse struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Color       string    `json:"color"`
	Icon        string    `json:"icon"`
	SortOrder   int       `json:"sort_order"`
	IsActive    bool      `json:"is_active"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}
