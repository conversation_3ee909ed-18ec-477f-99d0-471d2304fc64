package api

import (
	"strconv"
	"strings"

	"nexushub-oh-back/internal/services"
	"nexushub-oh-back/pkg/logger"
	"nexushub-oh-back/pkg/response"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// SearchController 搜索控制器
type SearchController struct {
	SearchService       *services.SearchService
	UserSearchService   *services.UserSearchService
	ReviewSearchService *services.ReviewSearchService
	TagSearchService    *services.TagSearchService
}

// NewSearchController 创建搜索控制器
func NewSearchController(
	searchService *services.SearchService,
	userSearchService *services.UserSearchService,
	reviewSearchService *services.ReviewSearchService,
	tagSearchService *services.TagSearchService,
) *SearchController {
	return &SearchController{
		SearchService:       searchService,
		UserSearchService:   userSearchService,
		ReviewSearchService: reviewSearchService,
		TagSearchService:    tagSearchService,
	}
}

// SearchAppsRequest 搜索应用请求
type SearchAppsRequest struct {
	Keyword    string   `form:"keyword" json:"keyword"`
	Category   string   `form:"category" json:"category"`
	Tags       []string `form:"tags" json:"tags"`
	MinRating  float64  `form:"min_rating" json:"min_rating"`
	IsVerified *bool    `form:"is_verified" json:"is_verified"`
	IsFeatured *bool    `form:"is_featured" json:"is_featured"`
	SortBy     string   `form:"sort_by" json:"sort_by"`
	SortOrder  string   `form:"sort_order" json:"sort_order"`
	Page       int      `form:"page" json:"page"`
	PageSize   int      `form:"page_size" json:"page_size"`
}

// SearchApps 搜索应用
//
//	@Summary		搜索应用
//	@Description	使用Elasticsearch搜索应用，支持关键词、分类、标签等多种过滤条件
//	@Tags			搜索
//	@Accept			json
//	@Produce		json
//	@Param			keyword		query		string									false	"搜索关键词"
//	@Param			category	query		string									false	"应用分类"
//	@Param			tags		query		[]string								false	"标签列表"
//	@Param			min_rating	query		number									false	"最低评分"
//	@Param			is_verified	query		boolean									false	"是否认证"
//	@Param			is_featured	query		boolean									false	"是否推荐"
//	@Param			sort_by		query		string									false	"排序字段(name/download_count/rating)"	Enums(name, download_count, rating)
//	@Param			sort_order	query		string									false	"排序方向(asc/desc)"					Enums(asc, desc)
//	@Param			page		query		int										false	"页码，默认1"							default(1)
//	@Param			page_size	query		int										false	"每页数量，默认20"							default(20)
//	@Success		200			{object}	response.Response{data=services.SearchResponse}	"搜索结果"
//	@Failure		400			{object}	response.Response												"请求参数错误"
//	@Failure		500			{object}	response.Response												"服务器错误"
//	@Router			/search/apps [get]
func (c *SearchController) SearchApps(ctx *gin.Context) {
	var req services.SearchRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.BadRequest(ctx, "请求参数错误")
		return
	}

	// 设置默认值
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 20
	}

	// 处理标签参数（支持逗号分隔的字符串）
	if tagsStr := ctx.Query("tags"); tagsStr != "" {
		req.Tags = strings.Split(tagsStr, ",")
		for i, tag := range req.Tags {
			req.Tags[i] = strings.TrimSpace(tag)
		}
	}

	// 构建搜索请求
	searchReq := &services.SearchRequest{
		Keyword:    req.Keyword,
		Category:   req.Category,
		Tags:       req.Tags,
		MinRating:  req.MinRating,
		IsVerified: req.IsVerified,
		IsFeatured: req.IsFeatured,
		SortBy:     req.SortBy,
		SortOrder:  req.SortOrder,
		Page:       req.Page,
		PageSize:   req.PageSize,
	}

	// 执行搜索
	result, err := c.SearchService.SearchApps(ctx, searchReq)
	if err != nil {
		logger.Error("搜索应用失败", zap.Error(err))
		response.InternalServerError(ctx, "搜索失败")
		return
	}

	response.Success(ctx, result)
}

// SearchSuggestions 搜索建议
//
//	@Summary		获取搜索建议
//	@Description	根据输入的关键词获取搜索建议
//	@Tags			搜索
//	@Accept			json
//	@Produce		json
//	@Param			q		query		string					true	"搜索关键词"
//	@Param			limit	query		int						false	"建议数量限制，默认10"	default(10)
//	@Success		200		{object}	response.Response{data=[]string}	"搜索建议列表"
//	@Failure		400		{object}	response.Response				"请求参数错误"
//	@Failure		500		{object}	response.Response				"服务器错误"
//	@Router			/search/suggestions [get]
func (c *SearchController) SearchSuggestions(ctx *gin.Context) {
	query := ctx.Query("q")
	if query == "" {
		response.BadRequest(ctx, "搜索关键词不能为空")
		return
	}

	limitStr := ctx.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 50 {
		limit = 10
	}

	// 构建搜索请求
	searchReq := &services.SearchRequest{
		Keyword:  query,
		Page:     1,
		PageSize: limit,
	}

	// 执行搜索
	result, err := c.SearchService.SearchApps(ctx, searchReq)
	if err != nil {
		response.InternalServerError(ctx, "搜索建议失败")
		return
	}

	// 提取应用名称作为建议
	suggestions := make([]string, 0, len(result.Apps))
	for _, app := range result.Apps {
		suggestions = append(suggestions, app.Name)
	}

	response.SuccessWithMessage(ctx, "获取搜索建议成功", map[string]interface{}{
		"suggestions": suggestions,
		"total":       len(suggestions),
		"limit":       limit,
	})
}

// InitializeSearchIndex 初始化搜索索引
//
//	@Summary		初始化搜索索引
//	@Description	创建Elasticsearch索引并同步所有应用数据（管理员功能）
//	@Tags			搜索
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Success		200	{object}	response.Response	"初始化成功"
//	@Failure		401	{object}	response.Response	"未授权"
//	@Failure		403	{object}	response.Response	"权限不足"
//	@Failure		500	{object}	response.Response	"服务器错误"
//	@Router			/search/initialize [post]
func (c *SearchController) InitializeSearchIndex(ctx *gin.Context) {
	// 初始化索引
	if err := c.SearchService.InitializeIndex(ctx); err != nil {
		logger.Error("初始化搜索索引失败", zap.Error(err))
		response.InternalServerError(ctx, "初始化索引失败")
		return
	}

	// 同步所有应用
	if err := c.SearchService.SyncAllApps(ctx); err != nil {
		logger.Error("同步应用数据失败", zap.Error(err))
		response.InternalServerError(ctx, "同步数据失败")
		return
	}

	response.Success(ctx, "搜索索引初始化成功")
}

// SyncSearchIndex 同步搜索索引
//
//	@Summary		同步搜索索引
//	@Description	重新同步所有应用数据到Elasticsearch（管理员功能）
//	@Tags			搜索
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Success		200	{object}	response.Response	"同步成功"
//	@Failure		401	{object}	response.Response	"未授权"
//	@Failure		403	{object}	response.Response	"权限不足"
//	@Failure		500	{object}	response.Response	"服务器错误"
//	@Router			/search/sync [post]
func (c *SearchController) SyncSearchIndex(ctx *gin.Context) {
	// 同步所有应用
	if err := c.SearchService.SyncAllApps(ctx); err != nil {
		logger.Error("同步应用数据失败", zap.Error(err))
		response.InternalServerError(ctx, "同步数据失败")
		return
	}

	// 同步所有用户
	if err := c.UserSearchService.SyncAllUsers(ctx); err != nil {
		logger.Error("同步用户数据失败", zap.Error(err))
		response.InternalServerError(ctx, "同步用户数据失败")
		return
	}

	// 同步所有评论
	if err := c.ReviewSearchService.SyncAllReviews(ctx); err != nil {
		logger.Error("同步评论数据失败", zap.Error(err))
		response.InternalServerError(ctx, "同步评论数据失败")
		return
	}

	// 同步所有标签
	if err := c.TagSearchService.SyncAllTags(ctx); err != nil {
		logger.Error("同步标签数据失败", zap.Error(err))
		response.InternalServerError(ctx, "同步标签数据失败")
		return
	}

	response.Success(ctx, "搜索索引同步成功")
}

// SearchUsers 搜索用户
//
//	@Summary		搜索用户
//	@Description	使用Elasticsearch搜索用户，支持用户名、邮箱、开发者信息等搜索（管理员功能）
//	@Tags			搜索
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			keyword			query		string										false	"搜索关键词"
//	@Param			role			query		string										false	"用户角色"
//	@Param			status			query		string										false	"用户状态"
//	@Param			is_developer	query		boolean										false	"是否为开发者"
//	@Param			verify_status	query		string										false	"认证状态"
//	@Param			sort_by			query		string										false	"排序字段"		Enums(username, created_at, last_login_at, login_count)
//	@Param			sort_order		query		string										false	"排序方向"		Enums(asc, desc)
//	@Param			page			query		int											false	"页码，默认1"	default(1)
//	@Param			page_size		query		int											false	"每页数量，默认20"	default(20)
//	@Success		200				{object}	response.Response{data=services.UserSearchResponse}	"搜索结果"
//	@Failure		400				{object}	response.Response													"请求参数错误"
//	@Failure		401				{object}	response.Response													"未授权"
//	@Failure		403				{object}	response.Response													"权限不足"
//	@Failure		500				{object}	response.Response													"服务器错误"
//	@Router			/search/users [get]
func (c *SearchController) SearchUsers(ctx *gin.Context) {
	var req services.UserSearchRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.BadRequest(ctx, "请求参数错误")
		return
	}

	// 设置默认值
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 20
	}

	// 执行搜索
	result, err := c.UserSearchService.SearchUsers(ctx, &req)
	if err != nil {
		logger.Error("搜索用户失败", zap.Error(err))
		response.InternalServerError(ctx, "搜索失败")
		return
	}

	response.Success(ctx, result)
}

// SearchReviews 搜索评论
//
//	@Summary		搜索评论
//	@Description	使用Elasticsearch搜索评论，支持评论内容、用户、应用等搜索
//	@Tags			搜索
//	@Accept			json
//	@Produce		json
//	@Param			keyword			query		string											false	"搜索关键词"
//	@Param			user_id			query		int												false	"用户ID"
//	@Param			application_id	query		int												false	"应用ID"
//	@Param			min_rating		query		int												false	"最低评分"
//	@Param			max_rating		query		int												false	"最高评分"
//	@Param			status			query		string											false	"评论状态"
//	@Param			has_dev_reply	query		boolean											false	"是否有开发者回复"
//	@Param			sort_by			query		string											false	"排序字段"		Enums(created_at, rating, like_count)
//	@Param			sort_order		query		string											false	"排序方向"		Enums(asc, desc)
//	@Param			page			query		int												false	"页码，默认1"	default(1)
//	@Param			page_size		query		int												false	"每页数量，默认20"	default(20)
//	@Success		200				{object}	response.Response{data=services.ReviewSearchResponse}	"搜索结果"
//	@Failure		400				{object}	response.Response														"请求参数错误"
//	@Failure		500				{object}	response.Response														"服务器错误"
//	@Router			/search/reviews [get]
func (c *SearchController) SearchReviews(ctx *gin.Context) {
	var req services.ReviewSearchRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.BadRequest(ctx, "请求参数错误")
		return
	}

	// 设置默认值
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 20
	}

	// 执行搜索
	result, err := c.ReviewSearchService.SearchReviews(ctx, &req)
	if err != nil {
		logger.Error("搜索评论失败", zap.Error(err))
		response.InternalServerError(ctx, "搜索失败")
		return
	}

	response.Success(ctx, result)
}

// SearchTags 搜索标签
//
//	@Summary		搜索标签
//	@Description	使用Elasticsearch搜索标签，支持标签名称、描述等搜索
//	@Tags			搜索
//	@Accept			json
//	@Produce		json
//	@Param			keyword		query		string										false	"搜索关键词"
//	@Param			is_active	query		boolean										false	"是否活跃"
//	@Param			sort_by		query		string										false	"排序字段"		Enums(name, app_count, created_at)
//	@Param			sort_order	query		string										false	"排序方向"		Enums(asc, desc)
//	@Param			page		query		int											false	"页码，默认1"	default(1)
//	@Param			page_size	query		int											false	"每页数量，默认20"	default(20)
//	@Success		200			{object}	response.Response{data=services.TagSearchResponse}	"搜索结果"
//	@Failure		400			{object}	response.Response													"请求参数错误"
//	@Failure		500			{object}	response.Response													"服务器错误"
//	@Router			/search/tags [get]
func (c *SearchController) SearchTags(ctx *gin.Context) {
	var req services.TagSearchRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.BadRequest(ctx, "请求参数错误")
		return
	}

	// 设置默认值
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 20
	}

	// 执行搜索
	result, err := c.TagSearchService.SearchTags(ctx, &req)
	if err != nil {
		logger.Error("搜索标签失败", zap.Error(err))
		response.InternalServerError(ctx, "搜索失败")
		return
	}

	response.Success(ctx, result)
}

// SuggestTags 标签自动补全
//
//	@Summary		标签自动补全
//	@Description	根据输入前缀提供标签建议
//	@Tags			搜索
//	@Accept			json
//	@Produce		json
//	@Param			prefix	query		string					true	"搜索前缀"
//	@Param			limit	query		int						false	"建议数量限制，默认10"	default(10)
//	@Success		200		{object}	response.Response{data=[]string}	"建议列表"
//	@Failure		400		{object}	response.Response				"请求参数错误"
//	@Failure		500		{object}	response.Response				"服务器错误"
//	@Router			/search/tags/suggest [get]
func (c *SearchController) SuggestTags(ctx *gin.Context) {
	prefix := ctx.Query("prefix")
	if prefix == "" {
		response.BadRequest(ctx, "搜索前缀不能为空")
		return
	}

	limitStr := ctx.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}

	// 执行建议搜索
	suggestions, err := c.TagSearchService.SuggestTags(ctx, prefix, limit)
	if err != nil {
		logger.Error("标签建议搜索失败", zap.Error(err))
		response.InternalServerError(ctx, "搜索失败")
		return
	}

	response.Success(ctx, suggestions)
}

// GetTagStats 获取标签统计
//
//	@Summary		获取标签统计
//	@Description	获取标签使用统计信息（管理员功能）
//	@Tags			搜索
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Success		200	{object}	response.Response{data=[]services.TagStatsDocument}	"统计结果"
//	@Failure		401	{object}	response.Response													"未授权"
//	@Failure		403	{object}	response.Response													"权限不足"
//	@Failure		500	{object}	response.Response													"服务器错误"
//	@Router			/search/tags/stats [get]
func (c *SearchController) GetTagStats(ctx *gin.Context) {
	// 执行统计查询
	stats, err := c.TagSearchService.GetTagStats(ctx)
	if err != nil {
		logger.Error("获取标签统计失败", zap.Error(err))
		response.InternalServerError(ctx, "获取统计失败")
		return
	}

	response.Success(ctx, stats)
}

// InitializeAllSearchIndexes 初始化所有搜索索引
//
//	@Summary		初始化所有搜索索引
//	@Description	初始化应用、用户、评论、标签的Elasticsearch索引（管理员功能）
//	@Tags			搜索
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Success		200	{object}	response.Response	"初始化成功"
//	@Failure		401	{object}	response.Response	"未授权"
//	@Failure		403	{object}	response.Response	"权限不足"
//	@Failure		500	{object}	response.Response	"服务器错误"
//	@Router			/search/initialize-all [post]
func (c *SearchController) InitializeAllSearchIndexes(ctx *gin.Context) {
	// 初始化应用索引
	if err := c.SearchService.InitializeIndex(ctx); err != nil {
		logger.Error("初始化应用搜索索引失败", zap.Error(err))
		response.InternalServerError(ctx, "初始化应用索引失败")
		return
	}

	// 初始化用户索引
	if err := c.UserSearchService.InitializeUserIndex(ctx); err != nil {
		logger.Error("初始化用户搜索索引失败", zap.Error(err))
		response.InternalServerError(ctx, "初始化用户索引失败")
		return
	}

	// 初始化评论索引
	if err := c.ReviewSearchService.InitializeReviewIndex(ctx); err != nil {
		logger.Error("初始化评论搜索索引失败", zap.Error(err))
		response.InternalServerError(ctx, "初始化评论索引失败")
		return
	}

	// 初始化标签索引
	if err := c.TagSearchService.InitializeTagIndex(ctx); err != nil {
		logger.Error("初始化标签搜索索引失败", zap.Error(err))
		response.InternalServerError(ctx, "初始化标签索引失败")
		return
	}

	response.Success(ctx, "所有搜索索引初始化成功")
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
