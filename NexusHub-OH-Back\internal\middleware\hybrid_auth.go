package middleware

import (
	"net/http"
	"strconv"
	"strings"

	"nexushub-oh-back/pkg/auth"
	"nexushub-oh-back/pkg/logger"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// HybridAuth 混合认证中间件，支持传统JWT和Logto认证
func HybridAuth(jwtService *auth.JWTService, logtoService *auth.LogtoService) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "授权已过期或未授权",
			})
			c.Abort()
			return
		}

		// 检查Bearer前缀
		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "授权格式错误",
			})
			c.Abort()
			return
		}

		token := parts[1]
		
		// 优先尝试Logto认证（如果启用）
		if logtoService != nil && logtoService.IsEnabled() {
			if claims, err := logtoService.ValidateToken(token); err == nil {
				// Logto认证成功
				userID := logtoService.GetUserID(claims)
				username := logtoService.GetUsername(claims)
				role := logtoService.GetUserRole(claims)

				logger.Info("Logto认证成功", 
					zap.String("user_id", userID),
					zap.String("username", username),
					zap.String("role", role))

				// 将用户信息存储在上下文中
				c.Set("auth_type", "logto")
				c.Set("user_id_str", userID) // Logto用户ID是字符串
				c.Set("username", username)
				c.Set("role", role)
				c.Set("logto_claims", claims)

				// 尝试转换用户ID为uint（兼容现有代码）
				if uid, err := strconv.ParseUint(userID, 10, 32); err == nil {
					c.Set("user_id", uint(uid))
				} else {
					// 如果无法转换，设置为0，表示需要特殊处理
					c.Set("user_id", uint(0))
				}

				c.Next()
				return
			} else {
				logger.Debug("Logto认证失败，尝试传统JWT认证", zap.Error(err))
			}
		}

		// 回退到传统JWT认证
		if jwtService != nil {
			if claims, err := jwtService.ValidateToken(token); err == nil {
				// 传统JWT认证成功
				logger.Info("传统JWT认证成功", 
					zap.Uint("user_id", claims.UserID),
					zap.String("username", claims.Username),
					zap.String("role", claims.Role))

				// 将用户信息存储在上下文中
				c.Set("auth_type", "jwt")
				c.Set("user_id", claims.UserID)
				c.Set("user_id_str", strconv.FormatUint(uint64(claims.UserID), 10))
				c.Set("username", claims.Username)
				c.Set("role", claims.Role)
				c.Set("jwt_claims", claims)

				c.Next()
				return
			} else {
				logger.Error("传统JWT认证失败", zap.Error(err))
			}
		}

		// 所有认证方式都失败
		logger.Warn("所有认证方式都失败")
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "无效的令牌",
		})
		c.Abort()
	}
}

// GetUserInfo 从上下文中获取用户信息的辅助函数
func GetUserInfo(c *gin.Context) (userID uint, userIDStr, username, role, authType string, exists bool) {
	authTypeVal, exists := c.Get("auth_type")
	if !exists {
		return
	}

	authType = authTypeVal.(string)
	
	// 获取并安全转换各个字段
	if userIDVal, ok := c.Get("user_id"); ok && userIDVal != nil {
		userID = userIDVal.(uint)
	}
	
	if userIDStrVal, ok := c.Get("user_id_str"); ok && userIDStrVal != nil {
		userIDStr = userIDStrVal.(string)
	}
	
	if usernameVal, ok := c.Get("username"); ok && usernameVal != nil {
		username = usernameVal.(string)
	}
	
	if roleVal, ok := c.Get("role"); ok && roleVal != nil {
		role = roleVal.(string)
	}

	exists = true
	return
}

// RequireLogto 要求使用Logto认证的中间件
func RequireLogto() gin.HandlerFunc {
	return func(c *gin.Context) {
		authType, exists := c.Get("auth_type")
		if !exists || authType != "logto" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "需要Logto认证",
			})
			c.Abort()
			return
		}
		c.Next()
	}
}

// RequireJWT 要求使用传统JWT认证的中间件
func RequireJWT() gin.HandlerFunc {
	return func(c *gin.Context) {
		authType, exists := c.Get("auth_type")
		if !exists || authType != "jwt" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "需要传统JWT认证",
			})
			c.Abort()
			return
		}
		c.Next()
	}
}