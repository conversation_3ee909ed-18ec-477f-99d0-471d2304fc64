package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"nexushub-oh-back/internal/models"
	"nexushub-oh-back/pkg/elasticsearch"
	"nexushub-oh-back/pkg/logger"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// SearchService 搜索服务
type SearchService struct {
	DB       *gorm.DB
	ESClient *elasticsearch.ESClient
}

// NewSearchService 创建搜索服务
func NewSearchService(db *gorm.DB, esClient *elasticsearch.ESClient) *SearchService {
	return &SearchService{
		DB:       db,
		ESClient: esClient,
	}
}

// AppDocument Elasticsearch中的应用文档结构
type AppDocument struct {
	ID              uint     `json:"id"`
	Name            string   `json:"name"`
	Package         string   `json:"package"`
	Description     string   `json:"description"`
	ShortDesc       string   `json:"short_desc"`
	Category        string   `json:"category"`
	DeveloperID     uint     `json:"developer_id"`
	DeveloperName   string   `json:"developer_name"`
	Tags            string   `json:"tags"`
	CurrentVersion  string   `json:"current_version"`
	DownloadCount   int64    `json:"download_count"`
	AverageRating   float64  `json:"average_rating"`
	RatingCount     int      `json:"rating_count"`
	MinOpenHarmonyOSVer string   `json:"min_open_harmony_os_ver"`
	IsVerified      bool     `json:"is_verified"`
	IsFeatured      bool     `json:"is_featured"`
	IsEditor        bool     `json:"is_editor"`
	IsTop           bool     `json:"is_top"`
	Status          string   `json:"status"`
	Keywords        []string `json:"keywords"` // 用于搜索的关键词
}

// SearchRequest 搜索请求
type SearchRequest struct {
	Keyword    string   `json:"keyword"`
	Category   string   `json:"category"`
	Tags       []string `json:"tags"`
	MinRating  float64  `json:"min_rating"`
	IsVerified *bool    `json:"is_verified"`
	IsFeatured *bool    `json:"is_featured"`
	SortBy     string   `json:"sort_by"` // name, download_count, rating, release_date
	SortOrder  string   `json:"sort_order"` // asc, desc
	Page       int      `json:"page"`
	PageSize   int      `json:"page_size"`
}

// SearchResponse 搜索响应
type SearchResponse struct {
	Apps       []AppDocument `json:"apps"`
	Total      int64         `json:"total"`
	Page       int           `json:"page"`
	PageSize   int           `json:"page_size"`
	TotalPages int           `json:"total_pages"`
}

const (
	AppIndexName = "nexushub_apps"
)

// InitializeIndex 初始化Elasticsearch索引
func (s *SearchService) InitializeIndex(ctx context.Context) error {
	// 定义应用索引的映射
	mapping := `{
		"mappings": {
			"properties": {
				"id": {"type": "long"},
				"name": {
					"type": "text",
					"analyzer": "standard",
					"fields": {
						"keyword": {"type": "keyword"}
					}
				},
				"package": {"type": "keyword"},
				"description": {
					"type": "text",
					"analyzer": "standard"
				},
				"short_desc": {
					"type": "text",
					"analyzer": "standard"
				},
				"category": {"type": "keyword"},
				"developer_id": {"type": "long"},
				"developer_name": {
					"type": "text",
					"fields": {
						"keyword": {"type": "keyword"}
					}
				},
				"tags": {"type": "text"},
				"current_version": {"type": "keyword"},
				"download_count": {"type": "long"},
				"average_rating": {"type": "float"},
				"rating_count": {"type": "integer"},
				"min_open_harmony_os_ver": {"type": "keyword"},
				"is_verified": {"type": "boolean"},
				"is_featured": {"type": "boolean"},
				"is_editor": {"type": "boolean"},
				"is_top": {"type": "boolean"},
				"status": {"type": "keyword"},
				"keywords": {"type": "text", "analyzer": "standard"}
			}
		}
	}`

	// 检查索引是否存在
	exists, err := s.ESClient.IndexExists(ctx, AppIndexName)
	if err != nil {
		return fmt.Errorf("检查应用索引是否存在失败: %w", err)
	}

	if !exists {
		if err := s.ESClient.CreateIndex(ctx, AppIndexName, mapping); err != nil {
			return fmt.Errorf("创建应用索引失败: %w", err)
		}
		logger.Info("应用索引创建成功", zap.String("index", AppIndexName))
	} else {
		logger.Info("应用索引已存在", zap.String("index", AppIndexName))
	}

	return nil
}

// IndexApp 将应用索引到Elasticsearch
func (s *SearchService) IndexApp(ctx context.Context, app *models.Application) error {
	if s.ESClient == nil {
		return fmt.Errorf("Elasticsearch客户端未初始化")
	}

	// 获取开发者信息
	var developer models.User
	if err := s.DB.First(&developer, app.DeveloperID).Error; err != nil {
		logger.Error("获取开发者信息失败", zap.Error(err))
		return err
	}

	// 构建搜索关键词
	keywords := s.buildKeywords(app, &developer)

	// 构建文档
	doc := AppDocument{
		ID:              app.ID,
		Name:            app.Name,
		Package:         app.Package,
		Description:     app.Description,
		ShortDesc:       app.ShortDesc,
		Category:        app.Category,
		DeveloperID:     app.DeveloperID,
		DeveloperName:   developer.DeveloperName,
		Tags:            app.Tags,
		CurrentVersion:  app.CurrentVersion,
		DownloadCount:   app.DownloadCount,
		AverageRating:   app.AverageRating,
		RatingCount:     app.RatingCount,
		MinOpenHarmonyOSVer: app.MinOpenHarmonyOSVer,
		IsVerified:      app.IsVerified,
		IsFeatured:      app.IsFeatured,
		IsEditor:        app.IsEditor,
		IsTop:           app.IsTop,
		Status:          string(app.Status),
		Keywords:        keywords,
	}

	// 序列化文档
	docJSON, err := json.Marshal(doc)
	if err != nil {
		return fmt.Errorf("序列化应用文档失败: %w", err)
	}

	// 索引文档
	return s.ESClient.IndexDocument(ctx, AppIndexName, strconv.Itoa(int(app.ID)), string(docJSON))
}

// buildKeywords 构建搜索关键词
func (s *SearchService) buildKeywords(app *models.Application, developer *models.User) []string {
	keywords := []string{
		app.Name,
		app.Package,
		app.ShortDesc,
		developer.DeveloperName,
		app.Category,
	}

	// 添加标签
	if app.Tags != "" {
		tags := strings.Split(app.Tags, ",")
		for _, tag := range tags {
			tag = strings.TrimSpace(tag)
			if tag != "" {
				keywords = append(keywords, tag)
			}
		}
	}

	// 添加描述中的关键词（简单分词）
	if app.Description != "" {
		descWords := strings.Fields(app.Description)
		for _, word := range descWords {
			if len(word) > 2 { // 只添加长度大于2的词
				keywords = append(keywords, word)
			}
		}
	}

	return keywords
}

// SearchApps 搜索应用
func (s *SearchService) SearchApps(ctx context.Context, req *SearchRequest) (*SearchResponse, error) {
	if s.ESClient == nil {
		return nil, fmt.Errorf("Elasticsearch客户端未初始化")
	}

	// 构建搜索查询
	query := s.buildSearchQuery(req)

	// 执行搜索
	result, err := s.ESClient.Search(ctx, AppIndexName, query)
	if err != nil {
		return nil, fmt.Errorf("搜索失败: %w", err)
	}

	// 解析搜索结果
	return s.parseSearchResult(result, req.Page, req.PageSize)
}

// buildSearchQuery 构建搜索查询
func (s *SearchService) buildSearchQuery(req *SearchRequest) string {
	// 基础查询结构
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": []interface{}{},
				"filter": []interface{}{
					map[string]interface{}{
						"term": map[string]interface{}{
							"status": string(models.ApplicationStatusApproved),
						},
					},
				},
			},
		},
		"from": (req.Page - 1) * req.PageSize,
		"size": req.PageSize,
	}

	boolQuery := query["query"].(map[string]interface{})["bool"].(map[string]interface{})
	mustQueries := boolQuery["must"].([]interface{})
	filterQueries := boolQuery["filter"].([]interface{})

	// 关键词搜索
	if req.Keyword != "" {
		mustQueries = append(mustQueries, map[string]interface{}{
			"multi_match": map[string]interface{}{
				"query": req.Keyword,
				"fields": []string{
					"name^3",
					"short_desc^2",
					"description",
					"developer_name^2",
					"tags",
					"keywords",
				},
				"type": "best_fields",
				"fuzziness": "AUTO",
			},
		})
	}

	// 分类过滤
	if req.Category != "" {
		filterQueries = append(filterQueries, map[string]interface{}{
			"term": map[string]interface{}{
				"category": req.Category,
			},
		})
	}

	// 标签过滤
	if len(req.Tags) > 0 {
		for _, tag := range req.Tags {
			filterQueries = append(filterQueries, map[string]interface{}{
				"match": map[string]interface{}{
					"tags": tag,
				},
			})
		}
	}

	// 评分过滤
	if req.MinRating > 0 {
		filterQueries = append(filterQueries, map[string]interface{}{
			"range": map[string]interface{}{
				"average_rating": map[string]interface{}{
					"gte": req.MinRating,
				},
			},
		})
	}

	// 认证状态过滤
	if req.IsVerified != nil {
		filterQueries = append(filterQueries, map[string]interface{}{
			"term": map[string]interface{}{
				"is_verified": *req.IsVerified,
			},
		})
	}

	// 推荐状态过滤
	if req.IsFeatured != nil {
		filterQueries = append(filterQueries, map[string]interface{}{
			"term": map[string]interface{}{
				"is_featured": *req.IsFeatured,
			},
		})
	}

	// 更新查询
	boolQuery["must"] = mustQueries
	boolQuery["filter"] = filterQueries

	// 排序
	if req.SortBy != "" {
		sortOrder := "desc"
		if req.SortOrder == "asc" {
			sortOrder = "asc"
		}

		var sortField string
		switch req.SortBy {
		case "name":
			sortField = "name.keyword"
		case "download_count":
			sortField = "download_count"
		case "rating":
			sortField = "average_rating"
		default:
			sortField = "_score"
		}

		query["sort"] = []interface{}{
			map[string]interface{}{
				sortField: map[string]interface{}{
					"order": sortOrder,
				},
			},
		}
	} else {
		// 默认按相关性排序，然后按下载量排序
		query["sort"] = []interface{}{
			"_score",
			map[string]interface{}{
				"download_count": map[string]interface{}{
					"order": "desc",
				},
			},
		}
	}

	// 序列化查询
	queryJSON, _ := json.Marshal(query)
	return string(queryJSON)
}

// parseSearchResult 解析搜索结果
func (s *SearchService) parseSearchResult(result map[string]interface{}, page, pageSize int) (*SearchResponse, error) {
	hits, ok := result["hits"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("无效的搜索结果格式")
	}

	// 获取总数
	totalInterface, ok := hits["total"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("无法获取搜索结果总数")
	}
	total := int64(totalInterface["value"].(float64))

	// 获取文档列表
	docsInterface, ok := hits["hits"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("无法获取搜索结果文档")
	}

	apps := make([]AppDocument, 0, len(docsInterface))
	for _, docInterface := range docsInterface {
		doc, ok := docInterface.(map[string]interface{})
		if !ok {
			continue
		}

		source, ok := doc["_source"].(map[string]interface{})
		if !ok {
			continue
		}

		// 解析应用文档
		sourceJSON, _ := json.Marshal(source)
		var app AppDocument
		if err := json.Unmarshal(sourceJSON, &app); err != nil {
			logger.Error("解析应用文档失败", zap.Error(err))
			continue
		}

		apps = append(apps, app)
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	return &SearchResponse{
		Apps:       apps,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}, nil
}

// SyncAllApps 同步所有应用到Elasticsearch
func (s *SearchService) SyncAllApps(ctx context.Context) error {
	if s.ESClient == nil {
		return fmt.Errorf("Elasticsearch客户端未初始化")
	}

	// 获取所有已审核的应用
	var apps []models.Application
	if err := s.DB.Where("status = ?", models.ApplicationStatusApproved).Find(&apps).Error; err != nil {
		return fmt.Errorf("获取应用列表失败: %w", err)
	}

	logger.Info("开始同步应用到Elasticsearch", zap.Int("应用数量", len(apps)))

	// 逐个索引应用
	for _, app := range apps {
		if err := s.IndexApp(ctx, &app); err != nil {
			logger.Error("索引应用失败", zap.Uint("应用ID", app.ID), zap.Error(err))
			continue
		}
	}

	logger.Info("应用同步完成")
	return nil
}

// DeleteApp 从Elasticsearch中删除应用
func (s *SearchService) DeleteApp(ctx context.Context, appID uint) error {
	if s.ESClient == nil {
		return fmt.Errorf("Elasticsearch客户端未初始化")
	}

	// 删除文档
	if err := s.ESClient.DeleteDocument(ctx, AppIndexName, strconv.Itoa(int(appID))); err != nil {
		logger.Error("删除应用索引失败", zap.Uint("应用ID", appID), zap.Error(err))
		return err
	}

	logger.Info("删除应用索引成功", zap.Uint("应用ID", appID))
	return nil
}