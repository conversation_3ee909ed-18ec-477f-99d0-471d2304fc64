if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface AppDetailPage_Params {
    appDetail?: AppDetailModel | null;
    reviews?: AppReviewModel[];
    loadingState?: LoadingState;
    downloadStatus?: DownloadStatus;
    downloadProgress?: number;
    currentScreenshotIndex?: number;
    showFullDescription?: boolean;
    reviewsPage?: number;
    hasMoreReviews?: boolean;
    isLoadingReviews?: boolean;
    appId?: string;
    deviceUtils?;
    apiService?;
    swiperController?;
}
import { DownloadStatus } from "@normalized:N&&&entry/src/main/ets/models/App&";
import type { AppDetailModel, AppReviewModel, AppDetailResponse } from "@normalized:N&&&entry/src/main/ets/models/App&";
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { DeviceUtils } from "@normalized:N&&&entry/src/main/ets/utils/DeviceUtils&";
import { ApiService } from "@normalized:N&&&entry/src/main/ets/services/ApiService&";
import { LoadingView, LoadingState } from "@normalized:N&&&entry/src/main/ets/components/LoadingView&";
import preferences from "@ohos:data.preferences";
import hilog from "@ohos:hilog";
// getContext is deprecated, use this.getUIContext().getHostContext() instead
interface AppDetailPageParams {
    appId: string;
}
class AppDetailPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__appDetail = new ObservedPropertyObjectPU(null, this, "appDetail");
        this.__reviews = new ObservedPropertyObjectPU([], this, "reviews");
        this.__loadingState = new ObservedPropertySimplePU(LoadingState.LOADING, this, "loadingState");
        this.__downloadStatus = new ObservedPropertySimplePU(DownloadStatus.PENDING, this, "downloadStatus");
        this.__downloadProgress = new ObservedPropertySimplePU(0, this, "downloadProgress");
        this.__currentScreenshotIndex = new ObservedPropertySimplePU(0, this, "currentScreenshotIndex");
        this.__showFullDescription = new ObservedPropertySimplePU(false, this, "showFullDescription");
        this.__reviewsPage = new ObservedPropertySimplePU(1, this, "reviewsPage");
        this.__hasMoreReviews = new ObservedPropertySimplePU(true, this, "hasMoreReviews");
        this.__isLoadingReviews = new ObservedPropertySimplePU(false, this, "isLoadingReviews");
        this.appId = '';
        this.deviceUtils = DeviceUtils.getInstance();
        this.apiService = ApiService.getInstance();
        this.swiperController = new SwiperController();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: AppDetailPage_Params) {
        if (params.appDetail !== undefined) {
            this.appDetail = params.appDetail;
        }
        if (params.reviews !== undefined) {
            this.reviews = params.reviews;
        }
        if (params.loadingState !== undefined) {
            this.loadingState = params.loadingState;
        }
        if (params.downloadStatus !== undefined) {
            this.downloadStatus = params.downloadStatus;
        }
        if (params.downloadProgress !== undefined) {
            this.downloadProgress = params.downloadProgress;
        }
        if (params.currentScreenshotIndex !== undefined) {
            this.currentScreenshotIndex = params.currentScreenshotIndex;
        }
        if (params.showFullDescription !== undefined) {
            this.showFullDescription = params.showFullDescription;
        }
        if (params.reviewsPage !== undefined) {
            this.reviewsPage = params.reviewsPage;
        }
        if (params.hasMoreReviews !== undefined) {
            this.hasMoreReviews = params.hasMoreReviews;
        }
        if (params.isLoadingReviews !== undefined) {
            this.isLoadingReviews = params.isLoadingReviews;
        }
        if (params.appId !== undefined) {
            this.appId = params.appId;
        }
        if (params.deviceUtils !== undefined) {
            this.deviceUtils = params.deviceUtils;
        }
        if (params.apiService !== undefined) {
            this.apiService = params.apiService;
        }
        if (params.swiperController !== undefined) {
            this.swiperController = params.swiperController;
        }
    }
    updateStateVars(params: AppDetailPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__appDetail.purgeDependencyOnElmtId(rmElmtId);
        this.__reviews.purgeDependencyOnElmtId(rmElmtId);
        this.__loadingState.purgeDependencyOnElmtId(rmElmtId);
        this.__downloadStatus.purgeDependencyOnElmtId(rmElmtId);
        this.__downloadProgress.purgeDependencyOnElmtId(rmElmtId);
        this.__currentScreenshotIndex.purgeDependencyOnElmtId(rmElmtId);
        this.__showFullDescription.purgeDependencyOnElmtId(rmElmtId);
        this.__reviewsPage.purgeDependencyOnElmtId(rmElmtId);
        this.__hasMoreReviews.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoadingReviews.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__appDetail.aboutToBeDeleted();
        this.__reviews.aboutToBeDeleted();
        this.__loadingState.aboutToBeDeleted();
        this.__downloadStatus.aboutToBeDeleted();
        this.__downloadProgress.aboutToBeDeleted();
        this.__currentScreenshotIndex.aboutToBeDeleted();
        this.__showFullDescription.aboutToBeDeleted();
        this.__reviewsPage.aboutToBeDeleted();
        this.__hasMoreReviews.aboutToBeDeleted();
        this.__isLoadingReviews.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __appDetail: ObservedPropertyObjectPU<AppDetailModel | null>;
    get appDetail() {
        return this.__appDetail.get();
    }
    set appDetail(newValue: AppDetailModel | null) {
        this.__appDetail.set(newValue);
    }
    private __reviews: ObservedPropertyObjectPU<AppReviewModel[]>;
    get reviews() {
        return this.__reviews.get();
    }
    set reviews(newValue: AppReviewModel[]) {
        this.__reviews.set(newValue);
    }
    private __loadingState: ObservedPropertySimplePU<LoadingState>;
    get loadingState() {
        return this.__loadingState.get();
    }
    set loadingState(newValue: LoadingState) {
        this.__loadingState.set(newValue);
    }
    private __downloadStatus: ObservedPropertySimplePU<DownloadStatus>;
    get downloadStatus() {
        return this.__downloadStatus.get();
    }
    set downloadStatus(newValue: DownloadStatus) {
        this.__downloadStatus.set(newValue);
    }
    private __downloadProgress: ObservedPropertySimplePU<number>;
    get downloadProgress() {
        return this.__downloadProgress.get();
    }
    set downloadProgress(newValue: number) {
        this.__downloadProgress.set(newValue);
    }
    private __currentScreenshotIndex: ObservedPropertySimplePU<number>;
    get currentScreenshotIndex() {
        return this.__currentScreenshotIndex.get();
    }
    set currentScreenshotIndex(newValue: number) {
        this.__currentScreenshotIndex.set(newValue);
    }
    private __showFullDescription: ObservedPropertySimplePU<boolean>;
    get showFullDescription() {
        return this.__showFullDescription.get();
    }
    set showFullDescription(newValue: boolean) {
        this.__showFullDescription.set(newValue);
    }
    private __reviewsPage: ObservedPropertySimplePU<number>;
    get reviewsPage() {
        return this.__reviewsPage.get();
    }
    set reviewsPage(newValue: number) {
        this.__reviewsPage.set(newValue);
    }
    private __hasMoreReviews: ObservedPropertySimplePU<boolean>;
    get hasMoreReviews() {
        return this.__hasMoreReviews.get();
    }
    set hasMoreReviews(newValue: boolean) {
        this.__hasMoreReviews.set(newValue);
    }
    private __isLoadingReviews: ObservedPropertySimplePU<boolean>;
    get isLoadingReviews() {
        return this.__isLoadingReviews.get();
    }
    set isLoadingReviews(newValue: boolean) {
        this.__isLoadingReviews.set(newValue);
    }
    private appId: string;
    private deviceUtils;
    private apiService;
    private swiperController;
    aboutToAppear() {
        const params = this.getUIContext().getRouter().getParams() as AppDetailPageParams;
        this.appId = params?.appId || '';
        if (this.appId) {
            this.loadAppDetail();
            this.loadReviews();
        }
    }
    /**
     * 检查并设置认证token
     */
    private async checkAndSetAuthToken(): Promise<void> {
        try {
            const context = this.getUIContext().getHostContext();
            const options: preferences.Options = { name: 'user_data' };
            const dataPreferences = preferences.getPreferencesSync(context, options);
            const token = dataPreferences.getSync('token', '') as string;
            if (token) {
                this.apiService.setAuthToken(token);
            }
        }
        catch (error) {
            hilog.error(0x0000, 'AppDetailPage', '检查登录状态失败: %{public}s', JSON.stringify(error));
        }
    }
    /**
     * 加载应用详情
     */
    private async loadAppDetail() {
        try {
            this.loadingState = LoadingState.LOADING;
            // 检查登录状态并设置token
            await this.checkAndSetAuthToken();
            // 优先使用匿名接口获取应用详情，无需登录
            let response: AppDetailResponse;
            try {
                response = await this.apiService.getAppDetailAnonymous(Number(this.appId)) as AppDetailResponse;
                hilog.info(0x0000, 'AppDetailPage', '匿名接口获取应用详情成功: %{public}s', JSON.stringify(response));
            }
            catch (anonymousError) {
                // 如果匿名接口失败，尝试使用需要认证的接口
                hilog.warn(0x0000, 'AppDetailPage', '匿名接口获取应用详情失败，尝试认证接口: %{public}s', JSON.stringify(anonymousError));
                response = await this.apiService.getAppDetail(Number(this.appId)) as AppDetailResponse;
                hilog.info(0x0000, 'AppDetailPage', '认证接口获取应用详情成功: %{public}s', JSON.stringify(response));
            }
            // 修复数据处理逻辑：适配后端实际返回结构
            if (response.code === 200 && response.data && response.data.list && response.data.list.length > 0) {
                // 从 list 数组中获取第一个应用详情
                const appData = response.data.list[0];
                // 构建完整的应用详情模型（避免使用spread操作符）
                this.appDetail = appData as AppDetailModel;
                this.appDetail.versions = response.data.versions || [];
                hilog.info(0x0000, 'AppDetailPage', '应用详情数据处理成功: 应用名称=%{public}s, 版本数=%{public}d', this.appDetail.name, this.appDetail.versions.length);
                this.loadingState = LoadingState.SUCCESS;
            }
            else {
                hilog.error(0x0000, 'AppDetailPage', '应用详情数据格式错误: code=%{public}d, hasData=%{public}s, listLength=%{public}d', response?.code || -1, response?.data ? 'true' : 'false', response?.data?.list?.length || 0);
                this.loadingState = LoadingState.ERROR;
            }
        }
        catch (error) {
            hilog.error(0x0000, 'AppDetailPage', '加载应用详情失败: %{public}s', JSON.stringify(error));
            this.loadingState = LoadingState.ERROR;
        }
    }
    /**
     * 加载评论
     */
    private async loadReviews(page: number = 1) {
        try {
            if (page === 1) {
                this.isLoadingReviews = true;
            }
            const response = await this.apiService.getAppReviews(Number(this.appId), page, 10);
            if (response.code === 200 && response.data) {
                if (page === 1) {
                    this.reviews = response.data.list;
                }
                else {
                    this.reviews = this.reviews.concat(response.data.list);
                }
                this.hasMoreReviews = response.data.pagination.hasNext ?? false;
                this.reviewsPage = page;
            }
        }
        catch (error) {
            hilog.error(0x0000, 'AppDetailPage', '加载评论失败: %{public}s', JSON.stringify(error));
        }
        finally {
            this.isLoadingReviews = false;
        }
    }
    /**
     * 下载应用
     */
    private async downloadApp() {
        if (!this.appDetail)
            return;
        try {
            // 记录下载（使用匿名API）
            if (this.appDetail.id && this.appDetail.versions?.length > 0) {
                await this.apiService.recordAppDownloadAnonymous(this.appDetail.id, this.appDetail.versions[0].id);
            }
        }
        catch (error) {
            hilog.warn(0x0000, 'AppDetailPage', '记录下载失败: %{public}s', JSON.stringify(error));
            // 即使记录失败也继续下载流程
        }
        // 模拟下载过程
        this.downloadStatus = DownloadStatus.DOWNLOADING;
        this.downloadProgress = 0;
        const timer = setInterval(() => {
            this.downloadProgress += 10;
            if (this.downloadProgress >= 100) {
                clearInterval(timer);
                this.downloadStatus = DownloadStatus.COMPLETED;
                this.downloadProgress = 100;
            }
        }, 200);
    }
    /**
     * 安装应用
     */
    private installApp() {
        if (!this.appDetail)
            return;
        this.downloadStatus = DownloadStatus.INSTALLING;
        setTimeout(() => {
            this.downloadStatus = DownloadStatus.INSTALLED;
        }, 2000);
    }
    /**
     * 格式化文件大小
     */
    private formatFileSize(bytes: number): string {
        if (bytes === 0)
            return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    /**
     * 格式化评分
     */
    private formatRating(rating: number): string {
        return (rating || 0).toFixed(1);
    }
    /**
     * 应用头部信息
     */
    private AppHeader(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) });
            Row.width('100%');
            Row.alignItems(VerticalAlign.Top);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create(this.appDetail?.icon || Constants.PLACEHOLDER_IMAGE);
            Image.width(this.deviceUtils.isTablet() ? 80 : 64);
            Image.height(this.deviceUtils.isTablet() ? 80 : 64);
            Image.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            Image.objectFit(ImageFit.Cover);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.SMALL) });
            Column.alignItems(HorizontalAlign.Start);
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.appDetail?.name || '');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.maxLines(2);
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.appDetail?.developer_name || '');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
            Text.maxLines(1);
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: '8vp' });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.justifyContent(FlexAlign.Start);
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const star = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('★');
                    Text.fontSize(14);
                    Text.fontColor(star <= (this.appDetail?.average_rating || this.appDetail?.rating || 0) ? Constants.COLORS.WARNING : Constants.COLORS.BORDER);
                }, Text);
                Text.pop();
            };
            this.forEachUpdateFunction(elmtId, [1, 2, 3, 4, 5], forEachItemGenFunction);
        }, ForEach);
        ForEach.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.formatRating(this.appDetail?.average_rating || this.appDetail?.rating || 0));
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
        }, Text);
        Text.pop();
        Row.pop();
        Column.pop();
        Row.pop();
    }
    /**
     * 下载按钮
     */
    private DownloadButton(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.width(this.deviceUtils.isTablet() ? 120 : 100);
            Button.height(40);
            Button.backgroundColor(this.getDownloadButtonColor());
            Button.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            Button.enabled(this.downloadStatus !== DownloadStatus.DOWNLOADING && this.downloadStatus !== DownloadStatus.INSTALLING);
            Button.onClick(() => {
                if (this.downloadStatus === DownloadStatus.PENDING) {
                    this.downloadApp();
                }
                else if (this.downloadStatus === DownloadStatus.COMPLETED) {
                    this.installApp();
                }
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.downloadStatus === DownloadStatus.DOWNLOADING) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create({ space: '8vp' });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.width(16);
                        LoadingProgress.height(16);
                        LoadingProgress.color(Constants.COLORS.WHITE);
                    }, LoadingProgress);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`${this.downloadProgress}%`);
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Text.fontColor(Constants.COLORS.WHITE);
                    }, Text);
                    Text.pop();
                    Row.pop();
                });
            }
            else if (this.downloadStatus === DownloadStatus.INSTALLING) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create({ space: '8vp' });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.width(16);
                        LoadingProgress.height(16);
                        LoadingProgress.color(Constants.COLORS.WHITE);
                    }, LoadingProgress);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('安装中');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Text.fontColor(Constants.COLORS.WHITE);
                    }, Text);
                    Text.pop();
                    Row.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.getDownloadButtonText());
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Text.fontColor(Constants.COLORS.WHITE);
                    }, Text);
                    Text.pop();
                });
            }
        }, If);
        If.pop();
        Button.pop();
    }
    private getDownloadButtonText(): string {
        switch (this.downloadStatus) {
            case DownloadStatus.PENDING:
                return '下载';
            case DownloadStatus.COMPLETED:
                return '安装';
            case DownloadStatus.INSTALLED:
                return '打开';
            default:
                return '下载';
        }
    }
    private getDownloadButtonColor(): string {
        switch (this.downloadStatus) {
            case DownloadStatus.INSTALLED:
                return Constants.COLORS.SUCCESS;
            default:
                return Constants.COLORS.PRIMARY;
        }
    }
    /**
     * 应用信息
     */
    private AppInfo(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE) });
            Row.width('100%');
            Row.justifyContent(FlexAlign.SpaceAround);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: '4vp' });
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.formatFileSize(this.appDetail?.size || 0));
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('大小');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: '4vp' });
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.appDetail?.current_version || this.appDetail?.version || '');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('版本');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: '4vp' });
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.appDetail?.category || this.appDetail?.category_name || '');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('分类');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: '4vp' });
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create((this.appDetail?.download_count || 0).toString());
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('下载量');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
    }
    /**
     * 应用截图
     */
    private AppScreenshots(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.appDetail?.screenshots && this.appDetail.screenshots.length > 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('应用截图');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
                        Text.fontWeight(FontWeight.Bold);
                        Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                        Text.alignSelf(ItemAlign.Start);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Swiper.create(this.swiperController);
                        Swiper.width('100%');
                        Swiper.height(this.deviceUtils.isTablet() ? 400 : 300);
                        Swiper.autoPlay(false);
                        Swiper.indicator(true);
                        Swiper.loop(true);
                        Swiper.onChange((index: number) => {
                            this.currentScreenshotIndex = index;
                        });
                    }, Swiper);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = (_item, index: number) => {
                            const screenshot = _item;
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Image.create(screenshot);
                                Image.width('100%');
                                Image.height(this.deviceUtils.isTablet() ? 400 : 300);
                                Image.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
                                Image.objectFit(ImageFit.Cover);
                            }, Image);
                        };
                        this.forEachUpdateFunction(elmtId, this.appDetail.screenshots, forEachItemGenFunction, undefined, true, false);
                    }, ForEach);
                    ForEach.pop();
                    Swiper.pop();
                    Column.pop();
                });
            }
            else /**
             * 应用描述
             */ {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
    }
    /**
     * 应用描述
     */
    private AppDescription(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) });
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('应用介绍');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.appDetail?.description || '');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.lineHeight(24);
            Text.maxLines(this.showFullDescription ? undefined : 3);
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if ((this.appDetail?.description || '').length > 100) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.showFullDescription ? '收起' : '展开');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Text.fontColor(Constants.COLORS.PRIMARY);
                        Text.onClick(() => {
                            this.showFullDescription = !this.showFullDescription;
                        });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    /**
     * 用户评论
     */
    private AppReviews(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) });
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.width('100%');
            Row.justifyContent(FlexAlign.SpaceBetween);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('用户评价');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('查看全部');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.PRIMARY);
            Text.onClick(() => {
                // 跳转到评论页面
            });
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.reviews && this.reviews.length > 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const review = _item;
                            this.ReviewItem.bind(this)(review);
                        };
                        this.forEachUpdateFunction(elmtId, this.reviews.slice(0, 3), forEachItemGenFunction);
                    }, ForEach);
                    ForEach.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无评价');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Text.fontColor(Constants.COLORS.TEXT_HINT);
                        Text.textAlign(TextAlign.Center);
                        Text.width('100%');
                        Text.padding('20vp');
                    }, Text);
                    Text.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    /**
     * 评论项
     */
    private ReviewItem(review: AppReviewModel, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.SMALL) });
            Column.width('100%');
            Column.padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM));
            Column.backgroundColor(Constants.COLORS.BACKGROUND_LIGHT);
            Column.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.SMALL) });
            Row.alignItems(VerticalAlign.Top);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create(review.user_avatar || Constants.PLACEHOLDER_IMAGE);
            Image.width(32);
            Image.height(32);
            Image.borderRadius(16);
            Image.objectFit(ImageFit.Cover);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: '4vp' });
            Column.alignItems(HorizontalAlign.Start);
            Column.justifyContent(FlexAlign.Start);
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(review.username);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.justifyContent(FlexAlign.Start);
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const star = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('★');
                    Text.fontSize(12);
                    Text.fontColor(star <= review.rating ? Constants.COLORS.WARNING : Constants.COLORS.BORDER);
                }, Text);
                Text.pop();
            };
            this.forEachUpdateFunction(elmtId, [1, 2, 3, 4, 5], forEachItemGenFunction);
        }, ForEach);
        ForEach.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(review.created_at);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_HINT);
            Text.margin({ left: '8vp' });
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(review.content);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.lineHeight(20);
            Text.maxLines(3);
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
        }, Text);
        Text.pop();
        Column.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor(Constants.COLORS.BACKGROUND);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(56);
            // 顶部导航栏
            Row.padding({ left: '16vp', right: '16vp' });
            // 顶部导航栏
            Row.justifyContent(FlexAlign.SpaceBetween);
            // 顶部导航栏
            Row.alignItems(VerticalAlign.Center);
            // 顶部导航栏
            Row.backgroundColor(Constants.COLORS.WHITE);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('←');
            Text.fontSize(24);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.onClick(() => {
                this.getUIContext().getRouter().back();
            });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('应用详情');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('⋯');
            Text.fontSize(24);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.onClick(() => {
                // 分享功能
            });
        }, Text);
        Text.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.loadingState === LoadingState.LOADING) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, { state: LoadingState.LOADING }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/AppDetailPage.ets", line: 563, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.LOADING
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.LOADING
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else if (this.loadingState === LoadingState.ERROR) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, {
                                    state: LoadingState.ERROR,
                                    onRetry: () => {
                                        this.loadAppDetail();
                                    }
                                }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/AppDetailPage.ets", line: 566, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.ERROR,
                                        onRetry: () => {
                                            this.loadAppDetail();
                                        }
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.ERROR
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else if (this.appDetail) {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Scroll.create();
                        Scroll.layoutWeight(1);
                        Scroll.scrollable(ScrollDirection.Vertical);
                        Scroll.scrollBar(BarState.Auto);
                    }, Scroll);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE) });
                        Column.padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM));
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 应用头部信息和下载按钮
                        Column.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) });
                        // 应用头部信息和下载按钮
                        Column.alignItems(HorizontalAlign.Start);
                    }, Column);
                    this.AppHeader.bind(this)();
                    this.DownloadButton.bind(this)();
                    // 应用头部信息和下载按钮
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Divider.create();
                        Divider.color(Constants.COLORS.BORDER);
                    }, Divider);
                    // 应用信息
                    this.AppInfo.bind(this)();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Divider.create();
                        Divider.color(Constants.COLORS.BORDER);
                    }, Divider);
                    // 应用截图
                    this.AppScreenshots.bind(this)();
                    // 应用描述
                    this.AppDescription.bind(this)();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Divider.create();
                        Divider.color(Constants.COLORS.BORDER);
                    }, Divider);
                    // 用户评论
                    this.AppReviews.bind(this)();
                    Column.pop();
                    Scroll.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(3, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "AppDetailPage";
    }
}
registerNamedRoute(() => new AppDetailPage(undefined, {}), "", { bundleName: "com.example.nexushub", moduleName: "entry", pagePath: "pages/AppDetailPage", pageFullPath: "entry/src/main/ets/pages/AppDetailPage", integratedHsp: "false", moduleType: "followWithHap" });
