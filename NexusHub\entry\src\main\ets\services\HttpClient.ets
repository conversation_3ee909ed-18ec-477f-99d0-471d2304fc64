import { Constants } from '../utils/Constants';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { http } from '@kit.NetworkKit';
import { BusinessError } from '@kit.BasicServicesKit';

// 定义响应数据接口
interface ResponseData {
  code?: number;
  message?: string;
  data?: Object;
}

// 定义HTTP响应接口
interface HttpResponseData {
  responseCode: number;
  result: string | Object | ArrayBuffer;
}

// 定义请求选项接口
interface RequestOptions {
  method: http.RequestMethod;
  header: Record<string, string>;
  extraData?: Object;
  connectTimeout: number;
  readTimeout: number;
}

// 定义上传文件数据接口
interface UploadFileData {
  name: string;
  value: string;
  contentType?: string;
}

// 定义上传文件选项接口
interface UploadFileOptions {
  method: http.RequestMethod;
  header: Record<string, string>;
  multiFormDataList: Array<UploadFileData>;
  connectTimeout: number;
  readTimeout: number;
}

/**
 * HTTP客户端类
 */
export class HttpClient {
  private static instance: HttpClient;
  private baseUrl: string;
  private timeout: number;
  private defaultHeaders: Record<string, string>;

  private constructor() {
    this.baseUrl = Constants.API_BASE_URL;
    this.timeout = Constants.TIMEOUT;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };
  }

  /**
   * 获取单例实例
   */
  static getInstance(): HttpClient {
    if (!HttpClient.instance) {
      HttpClient.instance = new HttpClient();
    }
    return HttpClient.instance;
  }

  /**
   * 设置认证令牌
   */
  setAuthToken(token: string) {
    if (token) {
      this.defaultHeaders['Authorization'] = `Bearer ${token}`;
    } else {
      // 创建新对象排除该属性
      const newHeaders: Record<string, string> = {};
      Object.keys(this.defaultHeaders).forEach(key => {
        if (key !== 'Authorization') {
          newHeaders[key] = this.defaultHeaders[key];
        }
      });
      this.defaultHeaders = newHeaders;
    }
  }

  /**
   * 获取认证令牌
   */
  getAuthToken(): string | undefined {
    const auth = this.defaultHeaders['Authorization'];
    return auth ? auth.replace('Bearer ', '') : undefined;
  }

  /**
   * 创建HTTP请求
   */
  private createRequest(): http.HttpRequest {
    return http.createHttp();
  }

  /**
   * 构建完整URL
   */
  private buildUrl(endpoint: string): string {
    if (endpoint.startsWith('http')) {
      return endpoint;
    }
    const url = `${this.baseUrl}${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
    hilog.info(0x0000, 'HttpClient', 'Built URL: %{public}s', url);
    return url;
  }

  /**
   * 带重试机制的GET请求
   */
  async getWithRetry<T = Record<string, Object>>(endpoint: string, params?: Object, headers?: Record<string, string>, maxRetries: number = 2): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        hilog.info(0x0000, 'HttpClient', 'Attempt %{public}d for endpoint: %{public}s', attempt + 1, endpoint);
        return await this.get<T>(endpoint, params, headers);
      } catch (error) {
        lastError = error as Error;
        hilog.warn(0x0000, 'HttpClient', 'Attempt %{public}d failed: %{public}s', attempt + 1, lastError.message);
        
        // 如果不是最后一次尝试，等待一段时间后重试
        if (attempt < maxRetries) {
          const delay = Math.pow(2, attempt) * 1000; // 指数退避：1s, 2s, 4s...
          hilog.info(0x0000, 'HttpClient', 'Retrying in %{public}d ms...', delay);
          await new Promise<void>(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    throw lastError!;
  }

  /**
   * 处理响应
   */
  private async handleResponse(response: http.HttpResponse): Promise<Record<string, Object>> {
    const responseCode = response.responseCode;
    const result = response.result;
    
    hilog.info(0x0000, 'HttpClient', 'HTTP Response - Code: %{public}d, URL: %{public}s', responseCode, response.header?.['url'] || 'unknown');
    
    if (responseCode >= 200 && responseCode < 300) {
      if (typeof result === 'string') {
        try {
          return JSON.parse(result) as Record<string, Object>;
        } catch (error) {
          hilog.error(0x0000, 'HttpClient', 'JSON parse error: %{public}s', JSON.stringify(error));
          throw new Error('服务器响应格式错误，请稍后重试');
        }
      }
      return result as Record<string, Object>;
    } else if (responseCode === 408 || responseCode === 504) {
      // 处理超时相关的HTTP状态码
      throw new Error('网络请求超时，请检查网络连接后重试');
    } else if (responseCode >= 500) {
      // 服务器错误
      throw new Error('服务器暂时不可用，请稍后重试');
    } else {
      let errorData: ResponseData;
      if (typeof result === 'string') {
        try {
          errorData = JSON.parse(result) as ResponseData;
        } catch {
          errorData = { message: result };
        }
      } else {
        errorData = result as ResponseData;
      }
      
      const errorMessage = errorData.message || `网络请求失败 (${responseCode})，请稍后重试`;
      hilog.error(0x0000, 'HttpClient', 'HTTP Error - Code: %{public}d, Message: %{public}s', responseCode, errorMessage);
      throw new Error(errorMessage);
    }
  }

  /**
   * GET请求
   */
  async get<T = Record<string, Object>>(endpoint: string, params?: Object, headers?: Record<string, string>): Promise<T> {
    const request = this.createRequest();
    const url = this.buildUrl(endpoint);
    
    // 构建查询参数
    let fullUrl = url;
    if (params) {
      const queryParts: string[] = [];
      Object.entries(params).forEach((entry:[string,string|number|boolean]) => {
        if (entry[1] !== undefined && entry[1] !== null) {
          queryParts.push(`${encodeURIComponent(entry[0])}=${encodeURIComponent(String(entry[1]))}`);
        }
      });
      if (queryParts.length > 0) {
        const queryString = queryParts.join('&');
        fullUrl += (url.includes('?') ? '&' : '?') + queryString;
      }
    }

    try {
      const requestHeaders: Record<string, string> = {};
      Object.keys(this.defaultHeaders).forEach(key => {
        requestHeaders[key] = this.defaultHeaders[key];
      });
      if (headers) {
        Object.keys(headers).forEach(key => {
          requestHeaders[key] = headers[key];
        });
      }
      
      hilog.info(0x0000, 'HttpClient', 'GET Request - URL: %{public}s', fullUrl);
      hilog.info(0x0000, 'HttpClient', 'GET Request - Headers: %{public}s', JSON.stringify(requestHeaders));
      
      const requestOptions: RequestOptions = {
        method: http.RequestMethod.GET,
        header: requestHeaders,
        connectTimeout: this.timeout,
        readTimeout: this.timeout
      };
      
      const response = await request.request(fullUrl, requestOptions);
      hilog.info(0x0000, 'HttpClient', 'GET Response - Code: %{public}d, Result: %{public}s', response.responseCode, JSON.stringify(response.result));
      const result = await this.handleResponse(response);
      return result as T;
    } catch (error) {
      hilog.error(0x0000, 'HttpClient', 'GET request failed for URL: %{public}s, error: %{public}s', fullUrl, JSON.stringify(error));
      
      // 检查是否为超时错误或网络连接错误
      const errorObj = error as BusinessError;
      if (errorObj && (errorObj.code === 2300028 || errorObj.code === 2300001 || errorObj.code === 2300003)) {
        const timeoutMessage = `网络连接超时，请检查网络连接后重试`;
        hilog.error(0x0000, 'HttpClient', 'Network/Timeout Error: %{public}s, Code: %{public}d', timeoutMessage, errorObj.code);
        throw new Error(timeoutMessage);
      }
      
      const errorMessage = error instanceof Error ? error.message : '网络请求失败';
      throw new Error(errorMessage.includes('Network request failed') ? errorMessage : `网络请求失败: ${errorMessage}`);
    } finally {
      request.destroy();
    }
  }

  /**
   * POST请求
   */
  async post<T = Record<string, Object>>(endpoint: string, data?: Object | string, headers?: Record<string, string>): Promise<T> {
    const request = this.createRequest();
    const url = this.buildUrl(endpoint);

    try {
      const requestHeaders: Record<string, string> = {};
      Object.keys(this.defaultHeaders).forEach(key => {
        requestHeaders[key] = this.defaultHeaders[key];
      });
      if (headers) {
        Object.keys(headers).forEach(key => {
          requestHeaders[key] = headers[key];
        });
      }
      
      hilog.info(0x0000, 'HttpClient', 'POST Request - URL: %{public}s, Data: %{public}s', url, data ? JSON.stringify(data) : 'none');
      
      const requestOptions: RequestOptions = {
        method: http.RequestMethod.POST,
        header: requestHeaders,
        extraData: data ? JSON.stringify(data) : undefined,
        connectTimeout: this.timeout,
        readTimeout: this.timeout
      };
      
      const response = await request.request(url, requestOptions as http.HttpRequestOptions);
      const result = await this.handleResponse(response);
      return result as T;
    } catch (error) {
      hilog.error(0x0000, 'HttpClient', 'POST request failed for URL: %{public}s, error: %{public}s', url, JSON.stringify(error));
      
      // 检查是否为超时错误或网络连接错误
      const errorObj = error as BusinessError;
      if (errorObj && (errorObj.code === 2300028 || errorObj.code === 2300001 || errorObj.code === 2300003)) {
        const timeoutMessage = `网络连接超时，请检查网络连接后重试`;
        hilog.error(0x0000, 'HttpClient', 'Network/Timeout Error: %{public}s, Code: %{public}d', timeoutMessage, errorObj.code);
        throw new Error(timeoutMessage);
      }
      
      const errorMessage = error instanceof Error ? error.message : '网络请求失败';
      throw new Error(errorMessage.includes('Network request failed') ? errorMessage : `网络请求失败: ${errorMessage}`);
    } finally {
      request.destroy();
    }
  }

  /**
   * PUT请求
   */
  async put<T = Record<string, Object>>(endpoint: string, data?: Record<string, Object> | string, headers?: Record<string, string>): Promise<T> {
    const request = this.createRequest();
    const url = this.buildUrl(endpoint);

    try {
      const requestHeaders: Record<string, string> = {};
      Object.keys(this.defaultHeaders).forEach(key => {
        requestHeaders[key] = this.defaultHeaders[key];
      });
      if (headers) {
        Object.keys(headers).forEach(key => {
          requestHeaders[key] = headers[key];
        });
      }
      
      const requestOptions: RequestOptions = {
        method: http.RequestMethod.PUT,
        header: requestHeaders,
        extraData: data ? JSON.stringify(data) : undefined,
        connectTimeout: this.timeout,
        readTimeout: this.timeout
      };
      
      const response = await request.request(url, requestOptions);
      const result = await this.handleResponse(response);
      return result as T;
    } catch (error) {
      hilog.error(0x0000, 'HttpClient', 'PUT request failed: %{public}s', JSON.stringify(error));
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(errorMessage);
    } finally {
      request.destroy();
    }
  }

  /**
   * DELETE请求
   */
  async delete<T = Record<string, Object>>(endpoint: string, headers?: Record<string, string>): Promise<T> {
    const request = this.createRequest();
    const url = this.buildUrl(endpoint);

    try {
      const requestHeaders: Record<string, string> = {};
      Object.keys(this.defaultHeaders).forEach(key => {
        requestHeaders[key] = this.defaultHeaders[key];
      });
      if (headers) {
        Object.keys(headers).forEach(key => {
          requestHeaders[key] = headers[key];
        });
      }
      
      const requestOptions: RequestOptions = {
        method: http.RequestMethod.DELETE,
        header: requestHeaders,
        connectTimeout: this.timeout,
        readTimeout: this.timeout
      };
      
      const response = await request.request(url, requestOptions);
      const result = await this.handleResponse(response);
      return result as T;
    } catch (error) {
      hilog.error(0x0000, 'HttpClient', 'DELETE request failed: %{public}s', JSON.stringify(error));
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(errorMessage);
    } finally {
      request.destroy();
    }
  }

  /**
   * 上传文件
   */
  async upload<T = Record<string, Object>>(endpoint: string, filePath: string, headers?: Record<string, string>): Promise<T> {
    const request = this.createRequest();
    const url = this.buildUrl(endpoint);

    try {
      const requestHeaders: Record<string, string> = {};
      Object.keys(this.defaultHeaders).forEach(key => {
        requestHeaders[key] = this.defaultHeaders[key];
      });
      if (headers) {
        Object.keys(headers).forEach(key => {
          requestHeaders[key] = headers[key];
        });
      }
      
      const fileData: UploadFileData = {
        name: 'file',
        value: filePath,
        contentType: 'application/octet-stream'
      };
      const multiFormDataList: Array<UploadFileData> = [fileData];
      
      const requestOptions: UploadFileOptions = {
        method: http.RequestMethod.POST,
        header: requestHeaders,
        multiFormDataList: multiFormDataList,
        connectTimeout: this.timeout,
        readTimeout: this.timeout * 3
      };
      
      const response = await request.request(url, requestOptions as http.HttpRequestOptions);
      const result = await this.handleResponse(response);
      return result as T;
    } catch (error) {
      hilog.error(0x0000, 'HttpClient', 'Upload request failed: %{public}s', JSON.stringify(error));
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(errorMessage);
    } finally {
      request.destroy();
    }
  }

  /**
   * 下载文件
   */
  async download(url: string, filePath: string, onProgress?: (progress: number) => void): Promise<void> {
    const request = this.createRequest();

    return new Promise<void>((resolve, reject) => {
      request.on('dataReceive', (data: ArrayBuffer) => {
        if (onProgress) {
          // 这里需要根据实际情况计算进度
          // onProgress(progress);
        }
      });

      const requestOptions: RequestOptions = {
        method: http.RequestMethod.GET,
        header: this.defaultHeaders,
        connectTimeout: this.timeout,
        readTimeout: this.timeout * 10
      };

      request.request(url, requestOptions).then((response: http.HttpResponse) => {
        if (response.responseCode === 200) {
          // 这里需要将响应数据写入文件
          // 具体实现需要使用文件系统API
          resolve();
        } else {
          const errorMessage = `Download failed: ${response.responseCode}`;
          reject(new Error(errorMessage));
        }
      }).catch((error: Error) => {
        hilog.error(0x0000, 'HttpClient', 'Download request failed: %{public}s', JSON.stringify(error));
        reject(error);
      }).finally(() => {
        request.destroy();
      });
    });
  }
}