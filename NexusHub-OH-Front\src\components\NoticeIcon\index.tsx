import React, { useState, useEffect } from 'react';
import { Badge, Dropdown, List, Spin, Empty, Button, Tabs } from 'antd';
import { BellOutlined, LoadingOutlined } from '@ant-design/icons';
import { createStyles } from 'antd-style';
import { history } from '@umijs/max';
import { getNotifications, getUnreadCount, markAsRead, markAllAsRead } from '@/services/ant-design-pro/notifications';
import type { NotificationItem } from '@/services/ant-design-pro/notifications';
import HeaderDropdown from '../HeaderDropdown';

const useStyles = createStyles(({ token }) => ({
  noticeIcon: {
    fontSize: '16px',
    cursor: 'pointer',
    color: token.colorTextSecondary,
    '&:hover': {
      color: token.colorPrimary,
    },
  },
  noticeDropdown: {
    width: '336px',
    maxHeight: '400px',
  },
  noticeHeader: {
    padding: '12px 16px',
    borderBottom: `1px solid ${token.colorBorderSecondary}`,
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    fontWeight: 500,
  },
  noticeList: {
    maxHeight: '300px',
    overflow: 'auto',
  },
  noticeItem: {
    padding: '12px 16px',
    cursor: 'pointer',
    borderBottom: `1px solid ${token.colorBorderSecondary}`,
    '&:hover': {
      backgroundColor: token.colorBgTextHover,
    },
    '&.unread': {
      backgroundColor: token.colorBgContainer,
      '&::before': {
        content: '""',
        position: 'absolute',
        left: '8px',
        top: '50%',
        transform: 'translateY(-50%)',
        width: '6px',
        height: '6px',
        borderRadius: '50%',
        backgroundColor: token.colorPrimary,
      },
    },
  },
  noticeTitle: {
    fontSize: '14px',
    fontWeight: 500,
    marginBottom: '4px',
    lineHeight: '20px',
  },
  noticeDescription: {
    fontSize: '12px',
    color: token.colorTextSecondary,
    lineHeight: '16px',
    marginBottom: '4px',
  },
  noticeTime: {
    fontSize: '12px',
    color: token.colorTextTertiary,
  },
  noticeFooter: {
    padding: '8px 16px',
    borderTop: `1px solid ${token.colorBorderSecondary}`,
    textAlign: 'center',
  },
  clearButton: {
    fontSize: '12px',
    color: token.colorTextSecondary,
    '&:hover': {
      color: token.colorPrimary,
    },
  },
  emptyContainer: {
    padding: '40px 16px',
    textAlign: 'center',
  },
}));

interface NoticeIconProps {
  className?: string;
}

const NoticeIcon: React.FC<NoticeIconProps> = ({ className }) => {
  const { styles } = useStyles();
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('all');

  // 获取通知列表
  const fetchNotifications = async () => {
    setLoading(true);
    try {
      const response = await getNotifications({ page: 1, pageSize: 20 });
      if (response.success) {
        setNotifications(response.data?.list || []);
      }
    } catch (error) {
      console.error('获取通知列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取未读数量
  const fetchUnreadCount = async () => {
    // 检查token是否存在
    const token = localStorage.getItem('token');
    if (!token) {
      setUnreadCount(0);
      return;
    }

    try {
      const response = await getUnreadCount();
      if (response.success) {
        setUnreadCount(response.data?.count || 0);
      }
    } catch (error: any) {
      // 特殊处理401错误，避免在用户未登录时显示错误
      if (error?.response?.status === 401) {
        setUnreadCount(0);
        return;
      }
      console.error('获取未读数量失败:', error);
    }
  };

  // 标记为已读
  const handleMarkAsRead = async (id: number) => {
    try {
      const response = await markAsRead({ id });
      if (response.success) {
        setNotifications(prev => 
          prev.map(item => 
            item.id === id ? { ...item, read: true } : item
          )
        );
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error('标记已读失败:', error);
    }
  };

  // 标记全部已读
  const handleMarkAllAsRead = async () => {
    try {
      const response = await markAllAsRead();
      if (response.success) {
        setNotifications(prev => 
          prev.map(item => ({ ...item, read: true }))
        );
        setUnreadCount(0);
      }
    } catch (error) {
      console.error('标记全部已读失败:', error);
    }
  };

  // 点击通知项
  const handleNoticeClick = (item: NotificationItem) => {
    if (!item.read) {
      handleMarkAsRead(item.id);
    }
    setVisible(false);
    
    // 根据通知类型跳转到相应页面
    if (item.type === 'system' && item.extra?.url) {
      history.push(item.extra.url);
    }
  };

  // 查看全部通知
  const handleViewAll = () => {
    setVisible(false);
    history.push('/account/notifications');
  };

  // 过滤通知
  const filteredNotifications = notifications.filter(item => {
    if (activeTab === 'unread') {
      return !item.read;
    }
    return true;
  });

  useEffect(() => {
    console.log('NoticeIcon组件已挂载');
    // 检查是否有token，没有token则不获取未读数量
    const token = localStorage.getItem('token');
    console.log('NoticeIcon检查token:', token ? '存在' : '不存在');
    if (token) {
      // 添加小延迟确保登录流程完全完成
      const timer = setTimeout(() => {
        console.log('NoticeIcon开始获取未读数量');
        fetchUnreadCount();
      }, 1000); // 增加延迟到1秒
      
      // 定时刷新未读数量
      const interval = setInterval(() => {
        fetchUnreadCount(); // 函数内部已经有token检查
      }, 30000); // 30秒刷新一次
      
      return () => {
        clearTimeout(timer);
        clearInterval(interval);
      }
    }
  }, []);

  useEffect(() => {
    if (visible) {
      fetchNotifications();
    }
  }, [visible]);

  const dropdownContent = (
    <div className={styles.noticeDropdown}>
      <div className={styles.noticeHeader}>
        <span>通知</span>
        {unreadCount > 0 && (
          <Button 
            type="link" 
            size="small" 
            className={styles.clearButton}
            onClick={handleMarkAllAsRead}
          >
            全部已读
          </Button>
        )}
      </div>
      
      <Tabs
        size="small"
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'all',
            label: '全部',
          },
          {
            key: 'unread',
            label: `未读 ${unreadCount > 0 ? `(${unreadCount})` : ''}`,
          },
        ]}
        style={{ padding: '0 16px' }}
      />
      
      <div className={styles.noticeList}>
        {loading ? (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />
          </div>
        ) : filteredNotifications.length > 0 ? (
          <List
            dataSource={filteredNotifications}
            renderItem={(item) => (
              <div
                key={item.id}
                className={`${styles.noticeItem} ${!item.read ? 'unread' : ''}`}
                onClick={() => handleNoticeClick(item)}
              >
                <div className={styles.noticeTitle}>{item.title}</div>
                {item.description && (
                  <div className={styles.noticeDescription}>{item.description}</div>
                )}
                <div className={styles.noticeTime}>{item.created_at}</div>
              </div>
            )}
          />
        ) : (
          <div className={styles.emptyContainer}>
            <Empty 
              image={Empty.PRESENTED_IMAGE_SIMPLE} 
              description={activeTab === 'unread' ? '暂无未读通知' : '暂无通知'} 
            />
          </div>
        )}
      </div>
      
      {filteredNotifications.length > 0 && (
        <div className={styles.noticeFooter}>
          <Button type="link" size="small" onClick={handleViewAll}>
            查看全部通知
          </Button>
        </div>
      )}
    </div>
  );

  return (
    <HeaderDropdown
      menu={{
        items: [],
      }}
      popupRender={() => dropdownContent}
      onOpenChange={setVisible}
      open={visible}
      trigger={['click']}
      placement="bottomRight"
    >
      <Badge count={unreadCount} size="small" offset={[0, 0]}>
        <BellOutlined className={`${styles.noticeIcon} ${className}`} />
      </Badge>
    </HeaderDropdown>
  );
};

export default NoticeIcon;