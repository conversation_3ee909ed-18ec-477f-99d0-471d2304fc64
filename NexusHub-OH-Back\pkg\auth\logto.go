package auth

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"nexushub-oh-back/config"
	"nexushub-oh-back/pkg/logger"

	"github.com/MicahParks/keyfunc/v3"
	"github.com/golang-jwt/jwt/v5"
	"go.uber.org/zap"
)

// LogtoClaims Logto JWT声明结构
type LogtoClaims struct {
	Sub       string   `json:"sub"`       // 用户ID
	Aud       []string `json:"aud"`       // 受众
	Iss       string   `json:"iss"`       // 颁发者
	Exp       int64    `json:"exp"`       // 过期时间
	Iat       int64    `json:"iat"`       // 颁发时间
	Scope     string   `json:"scope"`     // 权限范围
	Username  string   `json:"username"`  // 用户名
	Email     string   `json:"email"`     // 邮箱
	Roles     []string `json:"roles"`     // 角色列表
	Custom    map[string]interface{} `json:"custom_data"` // 自定义数据
	jwt.RegisteredClaims
}

// LogtoService Logto JWT验证服务
type LogtoService struct {
	config   *config.LogtoConfig
	jwks     keyfunc.Keyfunc
	enabled  bool
}

// NewLogtoService 创建Logto服务实例
func NewLogtoService(cfg *config.LogtoConfig) (*LogtoService, error) {
	if !cfg.Enabled {
		logger.Info("Logto认证已禁用，将使用传统JWT认证")
		return &LogtoService{
			config:  cfg,
			enabled: false,
		}, nil
	}

	// 创建JWKS客户端
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	jwks, err := keyfunc.NewDefaultCtx(ctx, []string{cfg.JWKSEndpoint})
	if err != nil {
		return nil, fmt.Errorf("创建JWKS客户端失败: %w", err)
	}

	logger.Info("Logto服务初始化成功", 
		zap.String("endpoint", cfg.Endpoint),
		zap.String("api_resource", cfg.APIResource))

	return &LogtoService{
		config:  cfg,
		jwks:    jwks,
		enabled: true,
	}, nil
}

// IsEnabled 检查Logto是否启用
func (l *LogtoService) IsEnabled() bool {
	return l.enabled
}

// ValidateToken 验证Logto JWT token
func (l *LogtoService) ValidateToken(tokenString string) (*LogtoClaims, error) {
	if !l.enabled {
		return nil, errors.New("Logto认证未启用")
	}

	// 解析JWT token
	token, err := jwt.ParseWithClaims(tokenString, &LogtoClaims{}, l.jwks.Keyfunc)
	if err != nil {
		logger.Error("解析JWT token失败", zap.Error(err))
		return nil, fmt.Errorf("无效的token: %w", err)
	}

	if !token.Valid {
		return nil, errors.New("token无效")
	}

	claims, ok := token.Claims.(*LogtoClaims)
	if !ok {
		return nil, errors.New("无法解析token声明")
	}

	// 验证受众(audience)
	if !l.validateAudience(claims.Aud) {
		return nil, errors.New("token受众验证失败")
	}

	// 验证颁发者(issuer)
	if claims.Iss != l.config.Endpoint {
		return nil, fmt.Errorf("token颁发者验证失败: 期望 %s, 实际 %s", l.config.Endpoint, claims.Iss)
	}

	// 验证过期时间
	if time.Now().Unix() > claims.Exp {
		return nil, errors.New("token已过期")
	}

	logger.Debug("Logto token验证成功", 
		zap.String("user_id", claims.Sub),
		zap.String("username", claims.Username),
		zap.Strings("roles", claims.Roles))

	return claims, nil
}

// validateAudience 验证token受众
func (l *LogtoService) validateAudience(audiences []string) bool {
	for _, aud := range audiences {
		if aud == l.config.APIResource {
			return true
		}
	}
	return false
}

// GetUserRole 从Logto claims中提取用户角色
func (l *LogtoService) GetUserRole(claims *LogtoClaims) string {
	// 如果有角色信息，返回第一个角色
	if len(claims.Roles) > 0 {
		return claims.Roles[0]
	}

	// 从scope中提取角色信息
	if claims.Scope != "" {
		scopes := strings.Split(claims.Scope, " ")
		for _, scope := range scopes {
			if strings.HasPrefix(scope, "role:") {
				return strings.TrimPrefix(scope, "role:")
			}
		}
	}

	// 默认返回普通用户角色
	return "user"
}

// GetUserID 从Logto claims中提取用户ID
func (l *LogtoService) GetUserID(claims *LogtoClaims) string {
	return claims.Sub
}

// GetUsername 从Logto claims中提取用户名
func (l *LogtoService) GetUsername(claims *LogtoClaims) string {
	if claims.Username != "" {
		return claims.Username
	}
	// 如果没有用户名，使用邮箱
	if claims.Email != "" {
		return claims.Email
	}
	// 最后使用用户ID
	return claims.Sub
}

// Close 关闭Logto服务
func (l *LogtoService) Close() {
	// keyfunc.Keyfunc接口通常不需要显式关闭
	// 如果需要关闭，应该检查具体实现的关闭方法
}