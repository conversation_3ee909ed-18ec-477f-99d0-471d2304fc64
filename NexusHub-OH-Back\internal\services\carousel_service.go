package services

import (
	"errors"
	"fmt"
	"time"

	"nexushub-oh-back/internal/models"
	"gorm.io/gorm"
)

// CarouselService 轮播图服务
type CarouselService struct {
	db *gorm.DB
}

// NewCarouselService 创建轮播图服务实例
func NewCarouselService(db *gorm.DB) *CarouselService {
	return &CarouselService{
		db: db,
	}
}

// CreateCarousel 创建轮播图
func (s *CarouselService) CreateCarousel(req *models.CarouselCreateRequest, userID uint) (*models.Carousel, error) {
	// 验证目标ID的有效性
	if err := s.validateTargetID(req.Type, req.TargetID); err != nil {
		return nil, err
	}

	// 验证时间范围
	if err := s.validateTimeRange(req.StartTime, req.EndTime); err != nil {
		return nil, err
	}

	carousel := &models.Carousel{
		Title:     req.Title,
		Subtitle:  req.Subtitle,
		ImageURL:  req.ImageURL,
		Type:      req.Type,
		TargetID:  req.TargetID,
		TargetURL: req.TargetURL,
		Status:    req.Status,
		SortOrder: req.SortOrder,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		CreatedBy: userID,
		UpdatedBy: userID,
	}

	if err := s.db.Create(carousel).Error; err != nil {
		return nil, fmt.Errorf("创建轮播图失败: %w", err)
	}

	return carousel, nil
}

// GetCarouselByID 根据ID获取轮播图
func (s *CarouselService) GetCarouselByID(id uint) (*models.Carousel, error) {
	var carousel models.Carousel
	err := s.db.Preload("Creator").Preload("Updater").
		Preload("TargetApp").Preload("TargetCollection").
		First(&carousel, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("轮播图不存在")
		}
		return nil, fmt.Errorf("获取轮播图失败: %w", err)
	}
	return &carousel, nil
}

// UpdateCarousel 更新轮播图
func (s *CarouselService) UpdateCarousel(id uint, req *models.CarouselUpdateRequest, userID uint) (*models.Carousel, error) {
	carousel, err := s.GetCarouselByID(id)
	if err != nil {
		return nil, err
	}

	// 验证目标ID的有效性
	if req.Type != nil && req.TargetID != nil {
		if err := s.validateTargetID(*req.Type, req.TargetID); err != nil {
			return nil, err
		}
	} else if req.Type != nil {
		if err := s.validateTargetID(*req.Type, carousel.TargetID); err != nil {
			return nil, err
		}
	} else if req.TargetID != nil {
		if err := s.validateTargetID(carousel.Type, req.TargetID); err != nil {
			return nil, err
		}
	}

	// 验证时间范围
	startTime := carousel.StartTime
	endTime := carousel.EndTime
	if req.StartTime != nil {
		startTime = req.StartTime
	}
	if req.EndTime != nil {
		endTime = req.EndTime
	}
	if err := s.validateTimeRange(startTime, endTime); err != nil {
		return nil, err
	}

	// 更新字段
	updates := map[string]interface{}{
		"updated_by": userID,
		"updated_at": time.Now(),
	}

	if req.Title != nil {
		updates["title"] = *req.Title
	}
	if req.Subtitle != nil {
		updates["subtitle"] = *req.Subtitle
	}
	if req.ImageURL != nil {
		updates["image_url"] = *req.ImageURL
	}
	if req.Type != nil {
		updates["type"] = *req.Type
	}
	if req.TargetID != nil {
		updates["target_id"] = *req.TargetID
	}
	if req.TargetURL != nil {
		updates["target_url"] = *req.TargetURL
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}
	if req.SortOrder != nil {
		updates["sort_order"] = *req.SortOrder
	}
	if req.StartTime != nil {
		updates["start_time"] = *req.StartTime
	}
	if req.EndTime != nil {
		updates["end_time"] = *req.EndTime
	}

	if err := s.db.Model(carousel).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("更新轮播图失败: %w", err)
	}

	return s.GetCarouselByID(id)
}

// DeleteCarousel 删除轮播图
func (s *CarouselService) DeleteCarousel(id uint) error {
	result := s.db.Delete(&models.Carousel{}, id)
	if result.Error != nil {
		return fmt.Errorf("删除轮播图失败: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("轮播图不存在")
	}
	return nil
}

// GetCarouselList 获取轮播图列表
func (s *CarouselService) GetCarouselList(req *models.CarouselListRequest) (*models.CarouselListResponse, error) {
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	query := s.db.Model(&models.Carousel{}).
		Preload("Creator").Preload("Updater").
		Preload("TargetApp").Preload("TargetCollection")

	// 添加过滤条件
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.Type != "" {
		query = query.Where("type = ?", req.Type)
	}
	if req.Keyword != "" {
		query = query.Where("title ILIKE ? OR subtitle ILIKE ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取轮播图总数失败: %w", err)
	}

	// 获取列表
	var carousels []models.Carousel
	offset := (req.Page - 1) * req.PageSize
	err := query.Order("sort_order ASC, created_at DESC").
		Offset(offset).Limit(req.PageSize).
		Find(&carousels).Error
	if err != nil {
		return nil, fmt.Errorf("获取轮播图列表失败: %w", err)
	}

	// 转换为响应格式
	responseList := make([]models.CarouselResponse, len(carousels))
	for i, carousel := range carousels {
		responseList[i] = s.convertToResponse(&carousel)
	}

	return &models.CarouselListResponse{
		List:  responseList,
		Total: total,
		Page:  req.Page,
		Size:  req.PageSize,
	}, nil
}

// GetActiveCarousels 获取激活的轮播图列表（用于前端展示）
func (s *CarouselService) GetActiveCarousels() ([]models.CarouselResponse, error) {
	var carousels []models.Carousel
	now := time.Now()
	
	query := s.db.Model(&models.Carousel{}).
		Preload("TargetApp").Preload("TargetCollection").
		Where("status = ?", models.CarouselStatusActive).
		Where("(start_time IS NULL OR start_time <= ?)", now).
		Where("(end_time IS NULL OR end_time >= ?)", now).
		Order("sort_order ASC, created_at DESC")
	
	if err := query.Find(&carousels).Error; err != nil {
		return nil, fmt.Errorf("获取激活轮播图失败: %w", err)
	}

	responseList := make([]models.CarouselResponse, len(carousels))
	for i, carousel := range carousels {
		responseList[i] = s.convertToResponse(&carousel)
	}

	return responseList, nil
}

// IncrementClickCount 增加点击次数
func (s *CarouselService) IncrementClickCount(id uint) error {
	result := s.db.Model(&models.Carousel{}).Where("id = ?", id).UpdateColumn("click_count", gorm.Expr("click_count + 1"))
	if result.Error != nil {
		return fmt.Errorf("更新点击次数失败: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("轮播图不存在")
	}
	return nil
}

// UpdateSortOrder 批量更新排序
func (s *CarouselService) UpdateSortOrder(updates models.CarouselSortRequest, userID uint) error {
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, update := range updates {
		if err := tx.Model(&models.Carousel{}).Where("id = ?", update.ID).
			Updates(map[string]interface{}{
				"sort_order": update.SortOrder,
				"updated_by": userID,
				"updated_at": time.Now(),
			}).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("更新排序失败: %w", err)
		}
	}

	return tx.Commit().Error
}

// validateTargetID 验证目标ID的有效性
func (s *CarouselService) validateTargetID(carouselType models.CarouselType, targetID *uint) error {
	if targetID == nil {
		return nil
	}

	switch carouselType {
	case models.CarouselTypeApp:
		var count int64
		if err := s.db.Model(&models.Application{}).Where("id = ?", *targetID).Count(&count).Error; err != nil {
			return fmt.Errorf("验证应用ID失败: %w", err)
		}
		if count == 0 {
			return fmt.Errorf("应用不存在")
		}
	case models.CarouselTypeCollection:
		var count int64
		if err := s.db.Model(&models.FeaturedCollection{}).Where("id = ?", *targetID).Count(&count).Error; err != nil {
			return fmt.Errorf("验证精选集ID失败: %w", err)
		}
		if count == 0 {
			return fmt.Errorf("精选集不存在")
		}
	}

	return nil
}

// validateTimeRange 验证时间范围
func (s *CarouselService) validateTimeRange(startTime, endTime *time.Time) error {
	if startTime != nil && endTime != nil {
		if startTime.After(*endTime) {
			return fmt.Errorf("开始时间不能晚于结束时间")
		}
	}
	return nil
}

// convertToResponse 转换为响应格式
func (s *CarouselService) convertToResponse(carousel *models.Carousel) models.CarouselResponse {
	response := models.CarouselResponse{
		ID:         carousel.ID,
		Title:      carousel.Title,
		Subtitle:   carousel.Subtitle,
		ImageURL:   carousel.ImageURL,
		Type:       carousel.Type,
		TargetID:   carousel.TargetID,
		TargetURL:  carousel.TargetURL,
		Status:     carousel.Status,
		SortOrder:  carousel.SortOrder,
		StartTime:  carousel.StartTime,
		EndTime:    carousel.EndTime,
		ClickCount: carousel.ClickCount,
		CreatedBy:  carousel.CreatedBy,
		UpdatedBy:  carousel.UpdatedBy,
		CreatedAt:  carousel.CreatedAt,
		UpdatedAt:  carousel.UpdatedAt,
	}

	// 设置创建者和更新者名称
	if carousel.Creator.ID != 0 {
		response.CreatorName = carousel.Creator.Username
	}
	if carousel.Updater.ID != 0 {
		response.UpdaterName = carousel.Updater.Username
	}

	// 设置目标信息
	switch carousel.Type {
	case models.CarouselTypeApp:
		if carousel.TargetApp != nil {
			response.TargetInfo = map[string]interface{}{
				"id":          carousel.TargetApp.ID,
				"name":        carousel.TargetApp.Name,
				"icon":        carousel.TargetApp.Icon,
				"description": carousel.TargetApp.Description,
			}
		}
	case models.CarouselTypeCollection:
		if carousel.TargetCollection != nil {
			response.TargetInfo = map[string]interface{}{
				"id":          carousel.TargetCollection.ID,
				"title":       carousel.TargetCollection.Title,
				"description": carousel.TargetCollection.Description,
				"cover_image": carousel.TargetCollection.CoverImage,
			}
		}
	}

	return response
}