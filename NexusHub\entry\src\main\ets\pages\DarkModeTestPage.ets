import { router } from '@kit.ArkUI';
import { Constants } from '../utils/Constants';

/**
 * 深色模式测试页面
 */
@Entry
@Component
struct DarkModeTestPage {
  build() {
    Column() {
      // 顶部导航
      Row() {
        Image($r('app.media.ic_back'))
          .width(24)
          .height(24)
          .onClick(() => router.back())

        Text('深色模式测试')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .fontColor($r('sys.color.ohos_id_color_text_primary'))
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Column()
          .width(24)
          .height(24)
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor($r('sys.color.ohos_id_color_background'))

      Scroll() {
        Column({ space: 16 }) {
          // 测试卡片1 - 使用系统颜色
          Column() {
            Text('使用系统颜色的卡片')
              .fontSize(16)
              .fontWeight(FontWeight.Bold)
              .fontColor($r('sys.color.ohos_id_color_text_primary'))
              .margin({ bottom: 8 })
            
            Text('这个卡片使用了系统定义的颜色资源，应该能够自动适配深色模式。')
              .fontSize(14)
              .fontColor($r('sys.color.ohos_id_color_text_secondary'))
              .textAlign(TextAlign.Start)
          }
          .width('100%')
          .padding(16)
          .backgroundColor($r('sys.color.ohos_id_color_background'))
          .borderRadius(8)

          // 测试卡片2 - 使用Constants中的颜色
          Column() {
            Text('使用Constants颜色的卡片')
              .fontSize(16)
              .fontWeight(FontWeight.Bold)
              .fontColor(Constants.COLORS.TEXT_PRIMARY)
              .margin({ bottom: 8 })
            
            Text('这个卡片使用了Constants中定义的颜色，现在已经改为使用系统资源颜色。')
              .fontSize(14)
              .fontColor(Constants.COLORS.TEXT_SECONDARY)
              .textAlign(TextAlign.Start)
          }
          .width('100%')
          .padding(16)
          .backgroundColor(Constants.COLORS.BACKGROUND_LIGHT)
          .borderRadius(8)

          // 测试卡片3 - 自定义颜色资源
          Column() {
            Text('自定义颜色资源测试')
              .fontSize(16)
              .fontWeight(FontWeight.Bold)
              .fontColor($r('app.color.text_primary'))
              .margin({ bottom: 8 })
            
            Row({ space: 8 }) {
              Text('★★★★★')
                .fontSize(18)
                .fontColor($r('app.color.star_active'))
              
              Text('状态指示器')
                .fontSize(12)
                .fontColor($r('app.color.status_success'))
                .backgroundColor($r('app.color.overlay_medium'))
                .padding({ left: 8, right: 8, top: 4, bottom: 4 })
                .borderRadius(4)
            }
            .width('100%')
            .justifyContent(FlexAlign.SpaceBetween)
          }
          .width('100%')
          .padding(16)
          .backgroundColor($r('app.color.card_background'))
          .borderRadius(8)

          // 测试按钮
          Button('测试按钮')
            .width('100%')
            .height(48)
            .backgroundColor(Constants.COLORS.PRIMARY)
            .fontColor(Constants.COLORS.WHITE)
            .borderRadius(8)
            .onClick(() => {
              // 测试功能
            })

          // 分割线
          Divider()
            .color($r('sys.color.ohos_id_color_list_separator'))
            .strokeWidth(1)

          // 说明文字
          Text('深色模式测试说明：\n\n1. 切换系统深色模式，观察页面颜色变化\n2. 在设置页面开启"跟随系统深色模式"\n3. 在设置页面测试"手动深色模式"开关\n\n✅ 已完成的深色模式适配：\n• 使用系统资源颜色支持自动切换\n• 添加自定义颜色资源文件\n• 更新所有硬编码颜色值\n• 支持跟随系统设置和手动控制\n\n如果深色模式正常工作，页面背景和文字颜色应该会根据系统设置自动切换。')
            .fontSize(14)
            .fontColor($r('sys.color.ohos_id_color_text_secondary'))
            .textAlign(TextAlign.Start)
            .lineHeight(20)
        }
        .padding(16)
      }
      .layoutWeight(1)
    }
    .width('100%')
    .height('100%')
    .backgroundColor($r('sys.color.ohos_id_color_background'))
  }
}