package elasticsearch

import (
	"context"
	"crypto/tls"
	"fmt"
	"net/http"
	"strings"
	"time"

	"nexushub-oh-back/config"

	"encoding/json"

	"github.com/elastic/go-elasticsearch/v8"
	"github.com/elastic/go-elasticsearch/v8/esapi"
)

// ESClient Elasticsearch客户端结构体
type ESClient struct {
	Client *elasticsearch.Client
}

// NewESClient 创建一个新的Elasticsearch客户端
func NewESClient(cfg *config.ESConfig) (*ESClient, error) {
	// 创建ES配置
	esCfg := elasticsearch.Config{
		Addresses: []string{
			fmt.Sprintf("http://%s:%s", cfg.Host, cfg.Port),
		},
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}

	// 如果启用了认证
	if cfg.UseAuth {
		esCfg.Username = cfg.Username
		esCfg.Password = cfg.Password
	}

	// 创建客户端
	client, err := elasticsearch.NewClient(esCfg)
	if err != nil {
		return nil, fmt.Errorf("无法创建Elasticsearch客户端: %w", err)
	}

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	res, err := client.Info(
		client.Info.WithContext(ctx),
	)
	if err != nil {
		return nil, fmt.Errorf("无法连接到Elasticsearch: %w", err)
	}
	defer res.Body.Close()

	// 检查状态
	if res.IsError() {
		return nil, fmt.Errorf("Elasticsearch连接错误: %s", res.String())
	}

	return &ESClient{
		Client: client,
	}, nil
}

// CreateIndex 创建索引
func (e *ESClient) CreateIndex(ctx context.Context, indexName string, mapping string) error {
	req := esapi.IndicesCreateRequest{
		Index:  indexName,
		Body:   strings.NewReader(mapping),
		Pretty: true,
	}

	res, err := req.Do(ctx, e.Client)
	if err != nil {
		return fmt.Errorf("创建索引失败: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("创建索引响应错误: %s", res.String())
	}

	return nil
}

// IndexDocument 索引文档
func (e *ESClient) IndexDocument(ctx context.Context, indexName string, documentID string, document string) error {
	req := esapi.IndexRequest{
		Index:      indexName,
		DocumentID: documentID,
		Body:       strings.NewReader(document),
		Refresh:    "true",
	}

	res, err := req.Do(ctx, e.Client)
	if err != nil {
		return fmt.Errorf("索引文档失败: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("索引文档响应错误: %s", res.String())
	}

	return nil
}

// Search 搜索
func (e *ESClient) Search(ctx context.Context, indexName string, query string) (map[string]interface{}, error) {
	req := esapi.SearchRequest{
		Index: []string{indexName},
		Body:  strings.NewReader(query),
	}

	res, err := req.Do(ctx, e.Client)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, fmt.Errorf("搜索响应错误: %s", res.String())
	}

	var result map[string]interface{}
	if err := json.NewDecoder(res.Body).Decode(&result); err != nil {
		return nil, err
	}

	return result, nil
}

// DeleteDocument 删除文档
func (e *ESClient) DeleteDocument(ctx context.Context, indexName string, documentID string) error {
	req := esapi.DeleteRequest{
		Index:      indexName,
		DocumentID: documentID,
		Refresh:    "true",
	}

	res, err := req.Do(ctx, e.Client)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	if res.IsError() && res.StatusCode != 404 {
		return fmt.Errorf("删除文档响应错误: %s", res.String())
	}

	return nil
}

// IndexExists 检查索引是否存在
func (e *ESClient) IndexExists(ctx context.Context, indexName string) (bool, error) {
	req := esapi.IndicesExistsRequest{
		Index: []string{indexName},
	}

	res, err := req.Do(ctx, e.Client)
	if err != nil {
		return false, err
	}
	defer res.Body.Close()

	return res.StatusCode == 200, nil
}
