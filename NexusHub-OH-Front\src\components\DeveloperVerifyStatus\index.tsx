import React from 'react';
import { Card, Steps, Tag, Typography, Space, Alert, Descriptions, Button } from 'antd';
import { CheckCircleOutlined, ClockCircleOutlined, CloseCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import styles from './index.less';

const { Title, Paragraph, Text } = Typography;
const { Step } = Steps;

interface VerifyStatusData {
  verify_status: 'pending' | 'approved' | 'rejected' | '';
  verify_reason?: string;
  submitted_at?: string;
  verified_at?: string;
  developer_name?: string;
  company_name?: string;
  contact_email?: string;
  contact_phone?: string;
}

interface DeveloperVerifyStatusProps {
  data: VerifyStatusData | null;
  onResubmit?: () => void;
  showResubmitButton?: boolean;
}

const DeveloperVerifyStatus: React.FC<DeveloperVerifyStatusProps> = ({
  data,
  onResubmit,
  showResubmitButton = true,
}) => {
  // 如果没有数据或者认证状态为空字符串，则显示未提交状态
  if (!data || !data.verify_status || data.verify_status === '') {
    return (
      <Card className={styles.statusCard}>
        <div className={styles.emptyStatus}>
          <ExclamationCircleOutlined className={styles.emptyIcon} />
          <Title level={4}>尚未提交认证申请</Title>
          <Paragraph type="secondary">
            您还没有提交开发者认证申请，请先选择开发者类型并填写认证信息。
          </Paragraph>
        </div>
      </Card>
    );
  }

  const { verify_status, verify_reason, submitted_at, verified_at } = data;

  // 获取当前步骤
  const getCurrentStep = () => {
    switch (verify_status) {
      case 'pending':
        return 1;
      case 'approved':
        return 2;
      case 'rejected':
        return 1;
      default:
        return 0;
    }
  };

  // 获取步骤状态
  const getStepStatus = () => {
    switch (verify_status) {
      case 'pending':
        return 'process';
      case 'approved':
        return 'finish';
      case 'rejected':
        return 'error';
      default:
        return 'wait';
    }
  };

  // 渲染状态图标
  const renderStatusIcon = () => {
    switch (verify_status) {
      case 'pending':
        return <ClockCircleOutlined className={styles.pendingIcon} />;
      case 'approved':
        return <CheckCircleOutlined className={styles.approvedIcon} />;
      case 'rejected':
        return <CloseCircleOutlined className={styles.rejectedIcon} />;
      default:
        return <ExclamationCircleOutlined className={styles.defaultIcon} />;
    }
  };

  // 渲染状态标签
  const renderStatusTag = () => {
    const statusMap = {
      pending: { color: 'orange', text: '审核中' },
      approved: { color: 'green', text: '已通过' },
      rejected: { color: 'red', text: '已拒绝' },
    };
    
    const config = statusMap[verify_status as keyof typeof statusMap];
    return config ? <Tag color={config.color}>{config.text}</Tag> : null;
  };

  // 渲染状态描述
  const renderStatusDescription = () => {
    switch (verify_status) {
      case 'pending':
        return (
          <Alert
            message="认证申请审核中"
            description="您的开发者认证申请已提交，我们将在1-3个工作日内完成审核，请耐心等待。"
            type="info"
            showIcon
            className={styles.statusAlert}
          />
        );
      case 'approved':
        return (
          <Alert
            message="认证申请已通过"
            description="恭喜！您的开发者认证申请已通过审核，现在您可以上传和管理应用了。"
            type="success"
            showIcon
            className={styles.statusAlert}
          />
        );
      case 'rejected':
        return (
          <Alert
            message="认证申请被拒绝"
            description={`很抱歉，您的开发者认证申请未通过审核。${verify_reason ? `拒绝原因：${verify_reason}` : ''}您可以修改信息后重新提交申请。`}
            type="error"
            showIcon
            className={styles.statusAlert}
            action={
              showResubmitButton && onResubmit ? (
                <Button size="small" type="primary" onClick={onResubmit}>
                  重新申请
                </Button>
              ) : null
            }
          />
        );
      default:
        return null;
    }
  };

  return (
    <Card className={styles.statusCard}>
      <div className={styles.statusHeader}>
        <Space align="center">
          {renderStatusIcon()}
          <div>
            <Title level={4} className={styles.statusTitle}>
              开发者认证状态 {renderStatusTag()}
            </Title>
            <Text type="secondary">
              {submitted_at && `申请时间：${dayjs(submitted_at).format('YYYY-MM-DD HH:mm')}`}
              {verified_at && ` | 审核时间：${dayjs(verified_at).format('YYYY-MM-DD HH:mm')}`}
            </Text>
          </div>
        </Space>
      </div>

      <div className={styles.statusContent}>
        {renderStatusDescription()}

        <div className={styles.stepsContainer}>
          <Steps current={getCurrentStep()} status={getStepStatus()}>
            <Step title="提交申请" description="填写认证信息并提交" />
            <Step title="审核中" description="管理员审核认证材料" />
            <Step title="审核完成" description="获得开发者权限" />
          </Steps>
        </div>

        {data.developer_name && (
          <div className={styles.infoContainer}>
            <Title level={5}>申请信息</Title>
            <Descriptions column={2} size="small">
              <Descriptions.Item label="开发者姓名">{data.developer_name}</Descriptions.Item>
              <Descriptions.Item label="公司名称">{data.company_name || '-'}</Descriptions.Item>
              <Descriptions.Item label="联系邮箱">{data.contact_email}</Descriptions.Item>
              <Descriptions.Item label="联系电话">{data.contact_phone}</Descriptions.Item>
            </Descriptions>
          </div>
        )}
      </div>
    </Card>
  );
};

export default DeveloperVerifyStatus;