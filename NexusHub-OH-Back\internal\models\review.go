package models

import (
	"time"

	"gorm.io/gorm"
)

// Review 评论模型
type Review struct {
	ID          uint      `gorm:"primarykey" json:"id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   *time.Time `gorm:"index" json:"deleted_at,omitempty"`
	UserID        uint            `gorm:"index" json:"user_id"`
	User          User            `gorm:"foreignKey:UserID" json:"user"`
	ApplicationID uint            `gorm:"index" json:"application_id"`
	Application   Application     `gorm:"foreignKey:ApplicationID" json:"application"`
	AppVersion    string          `gorm:"type:varchar(50)" json:"app_version"`
	Title         string          `gorm:"type:varchar(100)" json:"title"`
	Content       string          `gorm:"type:text" json:"content"`
	Rating        int             `gorm:"type:smallint;default:0" json:"rating"` // 1-5星评分
	LikeCount     int             `gorm:"default:0" json:"like_count"`
	Status        ReviewStatus    `gorm:"type:varchar(20);default:'pending'" json:"status"`
	DevResponse   string          `gorm:"type:text" json:"dev_response"` // 开发者回复
	DevResponseAt *gorm.DeletedAt `json:"dev_response_at"`
}

// ReviewStatus 评论状态
type ReviewStatus string

const (
	// ReviewStatusPending 待审核
	ReviewStatusPending ReviewStatus = "pending"
	// ReviewStatusApproved 已通过
	ReviewStatusApproved ReviewStatus = "approved"
	// ReviewStatusRejected 已拒绝
	ReviewStatusRejected ReviewStatus = "rejected"
	// ReviewStatusSpam 垃圾评论
	ReviewStatusSpam ReviewStatus = "spam"
)

// TableName 指定表名
func (Review) TableName() string {
	return "reviews"
}

// ReviewLike 评论点赞
type ReviewLike struct {
	gorm.Model
	UserID   uint `gorm:"index" json:"user_id"`
	ReviewID uint `gorm:"index" json:"review_id"`
}

// TableName 指定表名
func (ReviewLike) TableName() string {
	return "review_likes"
}

// CreateReview 创建评论
func CreateReview(db *gorm.DB, review *Review) error {
	return db.Transaction(func(tx *gorm.DB) error {
		// 创建评论
		if err := tx.Create(review).Error; err != nil {
			return err
		}

		// 更新应用的评分
		var app Application
		if err := tx.First(&app, review.ApplicationID).Error; err != nil {
			return err
		}

		// 计算新的评分和评论数
		app.RatingCount++
		totalRating := float64(app.RatingCount-1)*app.AverageRating + float64(review.Rating)
		app.AverageRating = totalRating / float64(app.RatingCount)

		return tx.Save(&app).Error
	})
}

// GetReviewsByAppID 获取应用评论
func GetReviewsByAppID(db *gorm.DB, appID uint, page, pageSize int) ([]Review, int64, error) {
	var reviews []Review
	var count int64

	offset := (page - 1) * pageSize

	query := db.Model(&Review{}).Where("application_id = ? AND status = ?", appID, ReviewStatusApproved)

	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	if err := query.Preload("User").Order("created_at desc").Offset(offset).Limit(pageSize).Find(&reviews).Error; err != nil {
		return nil, 0, err
	}

	return reviews, count, nil
}

// GetReviewsByUserID 获取用户评论
func GetReviewsByUserID(db *gorm.DB, userID uint, page, pageSize int) ([]Review, int64, error) {
	var reviews []Review
	var count int64

	offset := (page - 1) * pageSize

	query := db.Model(&Review{}).Where("user_id = ?", userID)

	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	if err := query.Preload("Application").Order("created_at desc").Offset(offset).Limit(pageSize).Find(&reviews).Error; err != nil {
		return nil, 0, err
	}

	return reviews, count, nil
}

// LikeReview 点赞评论
func LikeReview(db *gorm.DB, userID, reviewID uint) error {
	return db.Transaction(func(tx *gorm.DB) error {
		// 检查是否已点赞
		var count int64
		if err := tx.Model(&ReviewLike{}).Where("user_id = ? AND review_id = ?", userID, reviewID).Count(&count).Error; err != nil {
			return err
		}

		if count > 0 {
			return nil // 已点赞，无需操作
		}

		// 创建点赞记录
		like := &ReviewLike{
			UserID:   userID,
			ReviewID: reviewID,
		}
		if err := tx.Create(like).Error; err != nil {
			return err
		}

		// 更新评论点赞数
		return tx.Model(&Review{}).Where("id = ?", reviewID).UpdateColumn("like_count", gorm.Expr("like_count + 1")).Error
	})
}

// UnlikeReview 取消点赞评论
func UnlikeReview(db *gorm.DB, userID, reviewID uint) error {
	return db.Transaction(func(tx *gorm.DB) error {
		// 删除点赞记录
		result := tx.Where("user_id = ? AND review_id = ?", userID, reviewID).Delete(&ReviewLike{})
		if result.Error != nil {
			return result.Error
		}

		if result.RowsAffected == 0 {
			return nil // 未点赞，无需操作
		}

		// 更新评论点赞数
		return tx.Model(&Review{}).Where("id = ?", reviewID).UpdateColumn("like_count", gorm.Expr("like_count - 1")).Error
	})
}
