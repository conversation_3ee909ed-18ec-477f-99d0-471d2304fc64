import { PageContainer } from '@ant-design/pro-components';
import { Card, Tabs, Form, Input, Button, Switch, InputNumber, Select, Slider, Space, message, Alert, Divider, Typography, Row, Col, Modal } from 'antd';
import { SaveOutlined, LockOutlined, SafetyOutlined, GlobalOutlined, KeyOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { useRequest } from '@umijs/max';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

interface SecuritySettings {
  passwordPolicy: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSpecialChars: boolean;
    passwordExpiration: number; // 天数，0表示永不过期
    passwordHistory: number; // 记住多少个历史密码，0表示不限制
  };
  loginSecurity: {
    maxLoginAttempts: number;
    lockoutDuration: number; // 分钟
    sessionTimeout: number; // 分钟
    enableTwoFactor: boolean;
    twoFactorMethod: 'sms' | 'email' | 'app';
    rememberMeDuration: number; // 天数
  };
  ipSecurity: {
    enableIpWhitelist: boolean;
    ipWhitelist: string[];
    enableGeoBlocking: boolean;
    blockedCountries: string[];
  };
  dataSecurity: {
    enableDataEncryption: boolean;
    encryptionLevel: 'standard' | 'high';
    enableDataBackup: boolean;
    backupFrequency: 'daily' | 'weekly' | 'monthly';
    backupRetention: number; // 天数
  };
}

// 模拟获取安全设置数据
const fetchSecuritySettings = async () => {
  // 实际项目中应该调用API
  console.log('Fetching security settings');
  
  // 模拟数据
  const mockData: SecuritySettings = {
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: false,
      passwordExpiration: 90,
      passwordHistory: 5,
    },
    loginSecurity: {
      maxLoginAttempts: 5,
      lockoutDuration: 30,
      sessionTimeout: 120,
      enableTwoFactor: false,
      twoFactorMethod: 'sms',
      rememberMeDuration: 7,
    },
    ipSecurity: {
      enableIpWhitelist: false,
      ipWhitelist: [],
      enableGeoBlocking: false,
      blockedCountries: [],
    },
    dataSecurity: {
      enableDataEncryption: true,
      encryptionLevel: 'standard',
      enableDataBackup: true,
      backupFrequency: 'daily',
      backupRetention: 30,
    },
  };

  return mockData;
};

// 模拟更新安全设置数据
const updateSecuritySettings = async (settings: Partial<SecuritySettings>) => {
  // 实际项目中应该调用API
  console.log('Updating security settings:', settings);
  
  // 模拟成功响应
  return { success: true };
};

const SecuritySettings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('passwordPolicy');
  const [passwordForm] = Form.useForm();
  const [loginForm] = Form.useForm();
  const [ipForm] = Form.useForm();
  const [dataForm] = Form.useForm();
  const [ipWhitelistModalVisible, setIpWhitelistModalVisible] = useState(false);
  const [ipWhitelist, setIpWhitelist] = useState<string[]>([]);
  const [newIpAddress, setNewIpAddress] = useState('');

  // 获取安全设置数据
  const { data: securitySettings, loading } = useRequest(fetchSecuritySettings, {
    onSuccess: (data) => {
      // 初始化表单数据
      passwordForm.setFieldsValue(data.passwordPolicy);
      loginForm.setFieldsValue(data.loginSecurity);
      ipForm.setFieldsValue({
        ...data.ipSecurity,
        // 不设置数组，避免表单控件问题
        ipWhitelist: undefined,
        blockedCountries: undefined,
      });
      dataForm.setFieldsValue(data.dataSecurity);
      
      // 设置IP白名单
      setIpWhitelist(data.ipSecurity.ipWhitelist);
    },
  });

  // 处理密码策略表单提交
  const handlePasswordPolicySubmit = async (values: any) => {
    try {
      await updateSecuritySettings({ passwordPolicy: values });
      message.success('密码策略设置已更新');
    } catch (error) {
      message.error('更新密码策略设置失败');
    }
  };

  // 处理登录安全表单提交
  const handleLoginSecuritySubmit = async (values: any) => {
    try {
      await updateSecuritySettings({ loginSecurity: values });
      message.success('登录安全设置已更新');
    } catch (error) {
      message.error('更新登录安全设置失败');
    }
  };

  // 处理IP安全表单提交
  const handleIpSecuritySubmit = async (values: any) => {
    try {
      const ipSecurityData = {
        ...values,
        ipWhitelist,
        // 这里应该有blockedCountries的处理
        blockedCountries: [],
      };
      await updateSecuritySettings({ ipSecurity: ipSecurityData });
      message.success('IP安全设置已更新');
    } catch (error) {
      message.error('更新IP安全设置失败');
    }
  };

  // 处理数据安全表单提交
  const handleDataSecuritySubmit = async (values: any) => {
    try {
      await updateSecuritySettings({ dataSecurity: values });
      message.success('数据安全设置已更新');
    } catch (error) {
      message.error('更新数据安全设置失败');
    }
  };

  // 添加IP地址到白名单
  const handleAddIpToWhitelist = () => {
    if (!newIpAddress) {
      message.warning('请输入IP地址');
      return;
    }

    // 简单的IP地址验证
    const ipRegex = /^(\d{1,3}\.){3}\d{1,3}(\/\d{1,2})?$/;
    if (!ipRegex.test(newIpAddress)) {
      message.error('请输入有效的IP地址或CIDR格式');
      return;
    }

    if (ipWhitelist.includes(newIpAddress)) {
      message.warning('该IP地址已在白名单中');
      return;
    }

    setIpWhitelist([...ipWhitelist, newIpAddress]);
    setNewIpAddress('');
    message.success('IP地址已添加到白名单');
  };

  // 从白名单中移除IP地址
  const handleRemoveIpFromWhitelist = (ip: string) => {
    setIpWhitelist(ipWhitelist.filter(item => item !== ip));
    message.success('IP地址已从白名单中移除');
  };

  // 计算密码强度
  const calculatePasswordStrength = () => {
    const minLength = passwordForm.getFieldValue('minLength') || 0;
    const requireUppercase = passwordForm.getFieldValue('requireUppercase') || false;
    const requireLowercase = passwordForm.getFieldValue('requireLowercase') || false;
    const requireNumbers = passwordForm.getFieldValue('requireNumbers') || false;
    const requireSpecialChars = passwordForm.getFieldValue('requireSpecialChars') || false;

    let strength = 0;
    if (minLength >= 12) strength += 2;
    else if (minLength >= 8) strength += 1;
    if (requireUppercase) strength += 1;
    if (requireLowercase) strength += 1;
    if (requireNumbers) strength += 1;
    if (requireSpecialChars) strength += 1;

    if (strength <= 2) return { level: 'weak', color: '#ff4d4f' };
    if (strength <= 4) return { level: 'medium', color: '#faad14' };
    return { level: 'strong', color: '#52c41a' };
  };

  const passwordStrength = calculatePasswordStrength();

  return (
    <PageContainer
      header={{
        title: '安全设置',
      }}
    >
      <Card bordered={false}>
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
        >
          <Tabs.TabPane 
            tab={<span><LockOutlined />密码策略</span>} 
            key="passwordPolicy" 
          >
            <Alert
              message="密码策略设置"
              description="设置系统的密码复杂度要求，增强账户安全性。强密码策略可以有效防止暴力破解和密码猜测攻击。"
              type="info"
              showIcon
              style={{ marginBottom: 24 }}
            />
            
            <Form
              form={passwordForm}
              layout="vertical"
              onFinish={handlePasswordPolicySubmit}
              initialValues={securitySettings?.passwordPolicy}
            >
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="minLength"
                    label="密码最小长度"
                    rules={[{ required: true, message: '请设置密码最小长度' }]}
                  >
                    <Slider
                      min={6}
                      max={16}
                      marks={{
                        6: '6',
                        8: '8',
                        10: '10',
                        12: '12',
                        14: '14',
                        16: '16',
                      }}
                      onChange={() => passwordForm.validateFields(['minLength'])}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <div style={{ marginTop: 40, marginBottom: 24 }}>
                    <Text>密码强度：</Text>
                    <Text strong style={{ color: passwordStrength.color }}>
                      {passwordStrength.level === 'weak' && '弱'}
                      {passwordStrength.level === 'medium' && '中'}
                      {passwordStrength.level === 'strong' && '强'}
                    </Text>
                    <div style={{ 
                      marginTop: 8, 
                      height: 8, 
                      borderRadius: 4, 
                      background: '#f0f0f0',
                      overflow: 'hidden'
                    }}>
                      <div style={{ 
                        width: passwordStrength.level === 'weak' ? '30%' : 
                              passwordStrength.level === 'medium' ? '60%' : '100%', 
                        height: '100%', 
                        background: passwordStrength.color 
                      }} />
                    </div>
                  </div>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="requireUppercase"
                    valuePropName="checked"
                  >
                    <Switch checkedChildren="开启" unCheckedChildren="关闭" /> 必须包含大写字母
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="requireLowercase"
                    valuePropName="checked"
                  >
                    <Switch checkedChildren="开启" unCheckedChildren="关闭" /> 必须包含小写字母
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="requireNumbers"
                    valuePropName="checked"
                  >
                    <Switch checkedChildren="开启" unCheckedChildren="关闭" /> 必须包含数字
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="requireSpecialChars"
                    valuePropName="checked"
                  >
                    <Switch checkedChildren="开启" unCheckedChildren="关闭" /> 必须包含特殊字符
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="passwordExpiration"
                    label="密码过期时间（天）"
                    tooltip="设置为0表示密码永不过期"
                  >
                    <InputNumber min={0} max={365} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="passwordHistory"
                    label="密码历史记录数量"
                    tooltip="系统记住多少个历史密码，防止用户重复使用旧密码。设置为0表示不限制。"
                  >
                    <InputNumber min={0} max={24} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item>
                <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                  保存设置
                </Button>
              </Form.Item>
            </Form>
          </Tabs.TabPane>

          <Tabs.TabPane 
            tab={<span><SafetyOutlined />登录安全</span>} 
            key="loginSecurity" 
          >
            <Alert
              message="登录安全设置"
              description="配置登录尝试限制、会话超时和双因素认证等安全措施，保护用户账户安全。"
              type="info"
              showIcon
              style={{ marginBottom: 24 }}
            />
            
            <Form
              form={loginForm}
              layout="vertical"
              onFinish={handleLoginSecuritySubmit}
              initialValues={securitySettings?.loginSecurity}
            >
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="maxLoginAttempts"
                    label="最大登录尝试次数"
                    tooltip="超过此次数后账户将被临时锁定"
                    rules={[{ required: true, message: '请设置最大登录尝试次数' }]}
                  >
                    <InputNumber min={1} max={10} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="lockoutDuration"
                    label="账户锁定时长（分钟）"
                    tooltip="账户被锁定的持续时间"
                    rules={[{ required: true, message: '请设置账户锁定时长' }]}
                  >
                    <InputNumber min={5} max={1440} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="sessionTimeout"
                    label="会话超时时间（分钟）"
                    tooltip="用户无操作后自动退出登录的时间"
                    rules={[{ required: true, message: '请设置会话超时时间' }]}
                  >
                    <InputNumber min={5} max={1440} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="rememberMeDuration"
                    label="记住登录状态时长（天）"
                    tooltip="用户选择记住登录状态后的有效期"
                  >
                    <InputNumber min={1} max={30} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="enableTwoFactor"
                label="启用双因素认证"
                valuePropName="checked"
              >
                <Switch checkedChildren="开启" unCheckedChildren="关闭" />
              </Form.Item>

              <Form.Item
                name="twoFactorMethod"
                label="双因素认证方式"
                dependencies={['enableTwoFactor']}
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!getFieldValue('enableTwoFactor') || value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('请选择双因素认证方式'));
                    },
                  }),
                ]}
              >
                <Select
                  placeholder="请选择双因素认证方式"
                  disabled={!loginForm.getFieldValue('enableTwoFactor')}
                >
                  <Option value="sms">短信验证码</Option>
                  <Option value="email">邮箱验证码</Option>
                  <Option value="app">认证应用</Option>
                </Select>
              </Form.Item>

              <Form.Item>
                <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                  保存设置
                </Button>
              </Form.Item>
            </Form>
          </Tabs.TabPane>

          <Tabs.TabPane 
            tab={<span><GlobalOutlined />IP安全</span>} 
            key="ipSecurity" 
          >
            <Alert
              message="IP安全设置"
              description="配置IP白名单和地理位置限制，限制特定IP或地区的访问，提高系统安全性。"
              type="info"
              showIcon
              style={{ marginBottom: 24 }}
            />
            
            <Form
              form={ipForm}
              layout="vertical"
              onFinish={handleIpSecuritySubmit}
              initialValues={securitySettings?.ipSecurity}
            >
              <Form.Item
                name="enableIpWhitelist"
                label="启用IP白名单"
                valuePropName="checked"
              >
                <Switch checkedChildren="开启" unCheckedChildren="关闭" />
              </Form.Item>

              <Form.Item
                label="IP白名单管理"
                dependencies={['enableIpWhitelist']}
              >
                <div>
                  <Button 
                    type="primary" 
                    onClick={() => setIpWhitelistModalVisible(true)}
                    disabled={!ipForm.getFieldValue('enableIpWhitelist')}
                    style={{ marginBottom: 16 }}
                  >
                    管理IP白名单
                  </Button>
                  
                  <div>
                    {ipWhitelist.length > 0 ? (
                      <ul style={{ listStyle: 'none', padding: 0 }}>
                        {ipWhitelist.map(ip => (
                          <li key={ip} style={{ marginBottom: 8 }}>
                            {ip}
                            <Button 
                              type="text" 
                              danger 
                              size="small" 
                              onClick={() => handleRemoveIpFromWhitelist(ip)}
                              style={{ marginLeft: 8 }}
                              disabled={!ipForm.getFieldValue('enableIpWhitelist')}
                            >
                              删除
                            </Button>
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <Text type="secondary">暂无IP白名单</Text>
                    )}
                  </div>
                </div>
              </Form.Item>

              <Form.Item
                name="enableGeoBlocking"
                label="启用地理位置限制"
                valuePropName="checked"
              >
                <Switch checkedChildren="开启" unCheckedChildren="关闭" />
              </Form.Item>

              <Form.Item
                label="禁止访问的国家/地区"
                dependencies={['enableGeoBlocking']}
              >
                <Select
                  mode="multiple"
                  placeholder="请选择禁止访问的国家/地区"
                  disabled={!ipForm.getFieldValue('enableGeoBlocking')}
                  style={{ width: '100%' }}
                >
                  <Option value="AF">阿富汗</Option>
                  <Option value="AL">阿尔巴尼亚</Option>
                  <Option value="DZ">阿尔及利亚</Option>
                  <Option value="US">美国</Option>
                  <Option value="GB">英国</Option>
                  <Option value="CA">加拿大</Option>
                  <Option value="AU">澳大利亚</Option>
                  <Option value="RU">俄罗斯</Option>
                  <Option value="JP">日本</Option>
                  <Option value="KR">韩国</Option>
                  <Option value="IN">印度</Option>
                  <Option value="BR">巴西</Option>
                  {/* 更多国家选项 */}
                </Select>
              </Form.Item>

              <Form.Item>
                <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                  保存设置
                </Button>
              </Form.Item>
            </Form>

            {/* IP白名单管理模态框 */}
            <Modal
              title="管理IP白名单"
              open={ipWhitelistModalVisible}
              onCancel={() => setIpWhitelistModalVisible(false)}
              footer={[
                <Button key="cancel" onClick={() => setIpWhitelistModalVisible(false)}>
                  关闭
                </Button>,
              ]}
            >
              <div style={{ marginBottom: 16 }}>
                <Input
                  placeholder="输入IP地址或CIDR格式（如***********或***********/24）"
                  value={newIpAddress}
                  onChange={e => setNewIpAddress(e.target.value)}
                  style={{ width: 'calc(100% - 88px)' }}
                />
                <Button
                  type="primary"
                  onClick={handleAddIpToWhitelist}
                  style={{ marginLeft: 8 }}
                >
                  添加
                </Button>
              </div>
              
              <div>
                <Title level={5}>当前IP白名单</Title>
                {ipWhitelist.length > 0 ? (
                  <ul style={{ listStyle: 'none', padding: 0 }}>
                    {ipWhitelist.map(ip => (
                      <li key={ip} style={{ marginBottom: 8 }}>
                        {ip}
                        <Button 
                          type="text" 
                          danger 
                          size="small" 
                          onClick={() => handleRemoveIpFromWhitelist(ip)}
                          style={{ marginLeft: 8 }}
                        >
                          删除
                        </Button>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <Text type="secondary">暂无IP白名单</Text>
                )}
              </div>
            </Modal>
          </Tabs.TabPane>

          <Tabs.TabPane 
            tab={<span><KeyOutlined />数据安全</span>} 
            key="dataSecurity" 
          >
            <Alert
              message="数据安全设置"
              description="配置数据加密和备份策略，保护系统数据安全，防止数据泄露和丢失。"
              type="info"
              showIcon
              style={{ marginBottom: 24 }}
            />
            
            <Form
              form={dataForm}
              layout="vertical"
              onFinish={handleDataSecuritySubmit}
              initialValues={securitySettings?.dataSecurity}
            >
              <Form.Item
                name="enableDataEncryption"
                label="启用数据加密"
                valuePropName="checked"
              >
                <Switch checkedChildren="开启" unCheckedChildren="关闭" />
              </Form.Item>

              <Form.Item
                name="encryptionLevel"
                label="加密级别"
                dependencies={['enableDataEncryption']}
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!getFieldValue('enableDataEncryption') || value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('请选择加密级别'));
                    },
                  }),
                ]}
              >
                <Select
                  placeholder="请选择加密级别"
                  disabled={!dataForm.getFieldValue('enableDataEncryption')}
                >
                  <Option value="standard">标准（AES-128）</Option>
                  <Option value="high">高级（AES-256）</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="enableDataBackup"
                label="启用数据备份"
                valuePropName="checked"
              >
                <Switch checkedChildren="开启" unCheckedChildren="关闭" />
              </Form.Item>

              <Form.Item
                name="backupFrequency"
                label="备份频率"
                dependencies={['enableDataBackup']}
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!getFieldValue('enableDataBackup') || value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('请选择备份频率'));
                    },
                  }),
                ]}
              >
                <Select
                  placeholder="请选择备份频率"
                  disabled={!dataForm.getFieldValue('enableDataBackup')}
                >
                  <Option value="daily">每日备份</Option>
                  <Option value="weekly">每周备份</Option>
                  <Option value="monthly">每月备份</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="backupRetention"
                label="备份保留时间（天）"
                dependencies={['enableDataBackup']}
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!getFieldValue('enableDataBackup') || value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('请设置备份保留时间'));
                    },
                  }),
                ]}
              >
                <InputNumber 
                  min={1} 
                  max={365} 
                  style={{ width: '100%' }} 
                  disabled={!dataForm.getFieldValue('enableDataBackup')}
                />
              </Form.Item>

              <Form.Item>
                <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                  保存设置
                </Button>
              </Form.Item>
            </Form>
          </Tabs.TabPane>
        </Tabs>
      </Card>
    </PageContainer>
  );
};

export default SecuritySettings;