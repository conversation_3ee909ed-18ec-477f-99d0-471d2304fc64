// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 获取用户列表 管理员获取所有用户列表 GET /admin/users */
export async function getAdminUsers(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAdminUsersParams,
  options?: { [key: string]: any },
) {
  return request<API.Response & { data?: API.UserResponse[] }>('/admin/users', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 管理员创建用户 管理员创建新用户并指定角色 POST /admin/users */
export async function postAdminUsers(
  body: API.AdminCreateUserRequest,
  options?: { [key: string]: any },
) {
  return request<API.Response>('/admin/users', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新用户角色 管理员更新用户角色 PUT /admin/users/${param0}/role */
export async function putAdminUsersIdRole(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putAdminUsersIdRoleParams,
  body: API.UpdateUserRoleRequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Response>(`/admin/users/${param0}/role`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 更新用户状态 管理员更新用户状态（禁用/启用） PUT /admin/users/${param0}/status */
export async function putAdminUsersIdStatus(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putAdminUsersIdStatusParams,
  body: API.UpdateUserStatusRequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Response>(`/admin/users/${param0}/status`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}
