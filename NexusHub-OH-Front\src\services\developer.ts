/*
 * @Author: xiaoxiaozhou <EMAIL>
 * @Date: 2025-06-11 22:42:21
 * @LastEditors: xiaoxiaozhou <EMAIL>
 * @LastEditTime: 2025-06-11 22:55:21
 * @FilePath: \NexusHub-OH\NexusHub-OH-Front\src\services\developer.ts
 * @Description: 
 * 技术生的工作日常
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
import { request } from '@umijs/max';

/** 获取开发者应用列表 GET /developer/apps */
export async function getDeveloperApps(params?: {
  page?: number;
  page_size?: number;
  status?: string;
  keyword?: string;
}) {
  return request<API.PageResponse<API.AppDetailsResponse>>('/developer/apps', {
    method: 'GET',
    params,
  });
}

/** 获取开发者认证状态 GET /developers/verify/status */
export async function getDeveloperVerifyStatus() {
  return request<API.Response & { data?: API.DeveloperVerifyResponse }>('/api/v1/developers/verify/status', {
    method: 'GET',
  });
}

/** 提交开发者认证 POST /developers/verify */
export async function submitDeveloperVerify(body: API.DeveloperVerifyRequest) {
  return request<API.Response & { data?: API.DeveloperVerifyResponse }>('/api/v1/developers/verify', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}