import { AppstoreOutlined, CloudDownloadOutlined, TeamOutlined, CommentOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { Area, Column } from '@ant-design/plots';
import { Col, Progress, Row, Tooltip } from 'antd';
import numeral from 'numeral';
import type { DataItem } from '../data.d';
import useStyles from '../style.style';
import { ChartCard, Field } from './Charts';
import Trend from './Trend';

const topColResponsiveProps = {
  xs: 24,
  sm: 12,
  md: 12,
  lg: 12,
  xl: 6,
  style: {
    marginBottom: 24,
  },
};

const IntroduceRow = ({ loading, summaryData }: { loading: boolean; summaryData: any }) => {
  const { styles } = useStyles();
  
  // 使用默认值，在数据未加载时显示
  const {
    total_apps = 0,
    total_downloads = 0,
    total_users = 0,
    total_reviews = 0,
    new_apps_today = 0,
    new_downloads_today = 0,
    new_users_today = 0,
    pending_apps_count = 0,
    pending_reviews_count = 0,
    total_developers = 0
  } = summaryData || {};

  // 计算审核待办比例
  const totalPending = pending_apps_count + pending_reviews_count;
  const totalItems = total_apps + total_reviews;
  const pendingPercent = totalItems > 0 ? Math.round((totalPending / totalItems) * 100) : 0;

  return (
    <Row gutter={24}>
      <Col {...topColResponsiveProps}>
        <ChartCard
          bordered={false}
          title="应用统计"
          action={
            <Tooltip title="平台上的应用总数和今日新增数量">
              <InfoCircleOutlined />
            </Tooltip>
          }
          loading={loading}
          total={() => numeral(total_apps).format('0,0')}
          footer={<Field label="今日新增" value={numeral(new_apps_today).format('0,0')} />}
          contentHeight={46}
        >
          <AppstoreOutlined 
            style={{
              position: 'absolute',
              right: 16,
              top: 4,
              color: '#1890ff',
              fontSize: 32,
              opacity: 0.4,
            }} 
          />
          <Trend
            flag="up"
            style={{
              marginRight: 16,
            }}
          >
            开发者
            <span className={styles.trendText}>{numeral(total_developers).format('0,0')}</span>
          </Trend>
          <Trend flag="up">
            类别
            <span className={styles.trendText}>12</span>
          </Trend>
        </ChartCard>
      </Col>

      <Col {...topColResponsiveProps}>
        <ChartCard
          bordered={false}
          loading={loading}
          title="下载统计"
          action={
            <Tooltip title="应用总下载量和今日新增下载量">
              <InfoCircleOutlined />
            </Tooltip>
          }
          total={numeral(total_downloads).format('0,0')}
          footer={<Field label="今日下载" value={numeral(new_downloads_today).format('0,0')} />}
          contentHeight={46}
        >
          <CloudDownloadOutlined 
            style={{
              position: 'absolute',
              right: 16,
              top: 4,
              color: '#52c41a',
              fontSize: 32,
              opacity: 0.4,
            }} 
          />
          <Area
            xField="x"
            yField="y"
            shapeField="smooth"
            height={46}
            axis={false}
            style={{
              fill: 'linear-gradient(-90deg, white 0%, #52c41a 100%)',
              fillOpacity: 0.6,
              width: '100%',
            }}
            padding={-20}
            data={Array.isArray(summaryData?.download_trend) ? summaryData.download_trend.map((item: any) => ({
              x: item.date,
              y: item.value
            })) : []}
          />
        </ChartCard>
      </Col>
      <Col {...topColResponsiveProps}>
        <ChartCard
          bordered={false}
          loading={loading}
          title="用户统计"
          action={
            <Tooltip title="平台总用户数和今日新增用户数">
              <InfoCircleOutlined />
            </Tooltip>
          }
          total={numeral(total_users).format('0,0')}
          footer={<Field label="今日新增" value={numeral(new_users_today).format('0,0')} />}
          contentHeight={46}
        >
          <TeamOutlined 
            style={{
              position: 'absolute',
              right: 16,
              top: 4,
              color: '#722ed1',
              fontSize: 32,
              opacity: 0.4,
            }} 
          />
          <Column
            xField="x"
            yField="y"
            padding={-20}
            axis={false}
            height={46}
            data={Array.isArray(summaryData?.user_trend) ? summaryData.user_trend.map((item: any) => ({
              x: item.date,
              y: item.value
            })).slice(-7) : []}
            scale={{ x: { paddingInner: 0.4 } }}
          />
        </ChartCard>
      </Col>
      <Col {...topColResponsiveProps}>
        <ChartCard
          loading={loading}
          bordered={false}
          title="审核待办"
          action={
            <Tooltip title="待审核的应用和评论数量">
              <InfoCircleOutlined />
            </Tooltip>
          }
          total={numeral(totalPending).format('0,0')}
          footer={
            <div
              style={{
                whiteSpace: 'nowrap',
                overflow: 'hidden',
              }}
            >
              <Trend
                flag="up"
                style={{
                  marginRight: 16,
                }}
              >
                待审应用
                <span className={styles.trendText}>{numeral(pending_apps_count).format('0,0')}</span>
              </Trend>
              <Trend flag="up">
                待审评论
                <span className={styles.trendText}>{numeral(pending_reviews_count).format('0,0')}</span>
              </Trend>
            </div>
          }
          contentHeight={46}
        >
          <CommentOutlined 
            style={{
              position: 'absolute',
              right: 16,
              top: 4,
              color: '#fa8c16',
              fontSize: 32,
              opacity: 0.4,
            }} 
          />
          <Progress percent={pendingPercent} strokeColor={{ from: '#fa8c16', to: '#ffd666' }} status="active" />
        </ChartCard>
      </Col>
    </Row>
  );
};

export default IntroduceRow;
