package models

import (
	"time"

	"gorm.io/gorm"
)

// User 用户模型
type User struct {
	ID          uint       `gorm:"primarykey" json:"id"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	DeletedAt   *time.Time `gorm:"index" json:"deleted_at,omitempty"`
	Username    string     `gorm:"type:varchar(50);uniqueIndex;not null" json:"username"`
	Email       string     `gorm:"type:varchar(100);uniqueIndex;not null" json:"email"`
	Phone       string     `gorm:"type:varchar(20);index" json:"phone"`
	Password    string     `gorm:"type:varchar(255);not null" json:"-"` // 使用json:"-"避免在JSON输出中包含密码
	Salt        string     `gorm:"type:varchar(32);not null" json:"-"`
	Role        string     `gorm:"type:varchar(20);default:'user'" json:"role"` // user, developer, admin
	Status      UserStatus `gorm:"type:varchar(20);default:'active'" json:"status"`
	Avatar      string     `gorm:"type:varchar(255)" json:"avatar"`
	LastLoginAt *time.Time `json:"last_login_at"`
	LoginCount  int        `gorm:"default:0" json:"login_count"`

	// 地址信息字段
	Address     string     `gorm:"type:varchar(255)" json:"address"`     // 详细地址
	Province    string     `gorm:"type:varchar(50)" json:"province"`     // 省份
	City        string     `gorm:"type:varchar(50)" json:"city"`         // 城市
	District    string     `gorm:"type:varchar(50)" json:"district"`     // 区/县
	Street      string     `gorm:"type:varchar(50)" json:"street"`       // 街道

	// 开发者相关字段
	IsDeveloper      bool       `gorm:"default:false" json:"is_developer"`
	DeveloperName    string     `gorm:"type:varchar(100)" json:"developer_name"`
	CompanyName      string     `gorm:"type:varchar(100)" json:"company_name"`
	Website          string     `gorm:"type:varchar(255)" json:"website"`
	Description      string     `gorm:"type:text" json:"description"`
	ContactEmail     string     `gorm:"type:varchar(100)" json:"contact_email"`     // 联系邮箱
	ContactPhone     string     `gorm:"type:varchar(20)" json:"contact_phone"`      // 联系电话
	BusinessLicense  string     `gorm:"type:varchar(500)" json:"business_license"`  // 营业执照URL
	IdentityCard     string     `gorm:"type:varchar(500)" json:"identity_card"`     // 身份证URL
	DeveloperAvatar  string     `gorm:"type:varchar(500)" json:"developer_avatar"`  // 开发者头像URL
	DeveloperAddress string     `gorm:"type:varchar(255)" json:"developer_address"` // 开发者地址
	SubmittedAt      time.Time  `gorm:"type:timestamp" json:"submitted_at"`         // 提交认证时间
	VerifiedAt       *time.Time `json:"verified_at"`                                // 认证通过时间
	VerifyReason     string     `gorm:"type:varchar(255)" json:"verify_reason"`     // 认证结果说明
	// 审核状态
	VerifyStatus VerifyStatus `gorm:"type:varchar(20);default:''" json:"verify_status"`
}

// UserStatus 用户状态
type UserStatus string

const (
	// UserStatusActive 活跃状态
	UserStatusActive UserStatus = "active"
	// UserStatusInactive 非活跃状态
	UserStatusInactive UserStatus = "inactive"
	// UserStatusSuspended 封禁状态
	UserStatusSuspended UserStatus = "suspended"
)

// VerifyStatus 验证状态
type VerifyStatus string

const (
	// VerifyStatusPending 待审核
	VerifyStatusPending VerifyStatus = "pending"
	// VerifyStatusApproved 已通过
	VerifyStatusApproved VerifyStatus = "approved"
	// VerifyStatusRejected 已拒绝
	VerifyStatusRejected VerifyStatus = "rejected"
)

// UserRole 用户角色
type UserRole string

const (
	// UserRoleUser 普通用户
	UserRoleUser UserRole = "user"
	// UserRoleDeveloper 开发者
	UserRoleDeveloper UserRole = "developer"
	// UserRoleAdmin 管理员
	UserRoleAdmin UserRole = "admin"
	// UserRoleOperator 运营人员
	UserRoleOperator UserRole = "operator"
	// UserRoleReviewer 审核员
	UserRoleReviewer UserRole = "reviewer"
)

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// GetAdminUsers 获取所有管理员用户
func GetAdminUsers(db *gorm.DB) ([]User, error) {
	var admins []User
	err := db.Where("role = ? AND status = ?", UserRoleAdmin, UserStatusActive).Find(&admins).Error
	return admins, err
}

// BeforeCreate 创建前钩子
func (u *User) BeforeCreate(tx *gorm.DB) error {
	// 默认值设置等操作
	if u.Status == "" {
		u.Status = UserStatusActive
	}

	if u.Role == "" {
		u.Role = string(UserRoleUser)
	}

	return nil
}

// CreateUser 创建用户
func CreateUser(db *gorm.DB, user *User) error {
	return db.Create(user).Error
}

// GetUserByID 通过ID获取用户
func GetUserByID(db *gorm.DB, id uint) (*User, error) {
	var user User
	err := db.First(&user, id).Error
	return &user, err
}

// GetUserByUsername 通过用户名获取用户
func GetUserByUsername(db *gorm.DB, username string) (*User, error) {
	var user User
	err := db.Where("username = ?", username).First(&user).Error
	return &user, err
}

// GetUserByEmail 通过邮箱获取用户
func GetUserByEmail(db *gorm.DB, email string) (*User, error) {
	var user User
	err := db.Where("email = ?", email).First(&user).Error
	return &user, err
}

// UpdateUser 更新用户信息
func UpdateUser(db *gorm.DB, user *User) error {
	return db.Save(user).Error
}

// DeleteUser 删除用户
func DeleteUser(db *gorm.DB, id uint) error {
	return db.Delete(&User{}, id).Error
}

// GetPendingDevelopers 获取待审核的开发者
func GetPendingDevelopers(db *gorm.DB, status VerifyStatus, page, pageSize int) ([]User, int64, error) {
	var developers []User
	var count int64

	offset := (page - 1) * pageSize

	query := db.Model(&User{}).Where("verify_status = ?", status)

	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	if err := query.Order("submitted_at desc").Offset(offset).Limit(pageSize).Find(&developers).Error; err != nil {
		return nil, 0, err
	}

	return developers, count, nil
}
