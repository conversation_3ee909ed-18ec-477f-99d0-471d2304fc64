import { PageContainer } from '@ant-design/pro-components';
import { Card, Table, Button, Tag, Space, Input, Select, DatePicker, Badge, Avatar, Tooltip, message, Modal, Dropdown, Checkbox, Form } from 'antd';
import { SearchOutlined, ReloadOutlined, EyeOutlined, EditOutlined, DeleteOutlined, DownloadOutlined, PlusOutlined, AppstoreOutlined, MoreOutlined, ExportOutlined, FilterOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { useRequest, history, useAccess, useModel } from '@umijs/max';
import type { ColumnsType } from 'antd/es/table';
import { getAppList, submitAppForReview } from '@/services/app';
import { getDeveloperApps } from '@/services/developer';
import PermissionWrapper, { PermissionButton } from '@/components/PermissionWrapper';
import { canEditApp, canDeleteApp, getRoleDisplayName } from '@/utils/permission';

const { RangePicker } = DatePicker;
const { Option } = Select;

interface AppItem {
  id: string | number;
  name: string;
  icon: string;
  package: string;
  description?: string;
  short_desc?: string;
  category: string;
  developer_id?: number;
  developer_name?: string;
  developer?: string;
  developerName?: string;
  current_version?: string;
  currentVersion?: string;
  version?: string;
  status: string;
  reject_reason?: string;
  size?: string | number;
  download_count?: number;
  downloadCount?: number;
  downloads?: number;
  average_rating?: number;
  averageRating?: number;
  rating?: number;
  rating_count?: number;
  min_open_harmony_os_ver?: string;
  tags?: string;
  website_url?: string;
  privacy_url?: string;
  is_verified?: boolean;
  is_featured?: boolean;
  is_editor?: boolean;
  is_top?: boolean;
  release_date?: string | Date;
  releaseDate?: string | Date;
  publishDate?: string;
  updateDate?: string;
  created_at?: string | Date;
  updated_at?: string | Date;
}

// 修改为根据用户角色使用不同的API获取数据
const fetchAppList = async (params: any, userRole?: string) => {
  console.log('Fetching app list with params:', params, 'userRole:', userRole);
  
  try {
    let response;
    
    // 构建API参数，过滤掉undefined值
    const apiParams: any = {
      page: params.page || 1,
      page_size: params.pageSize || 10,
    };
    
    // 只有当参数有值时才添加到请求中
    if (params.keyword) apiParams.keyword = params.keyword;
    if (params.category) apiParams.category = params.category;
    
    // 如果是开发者角色，调用开发者应用接口
    if (userRole === 'developer') {
      // 开发者只能看到自己的应用，包含所有状态
      if (params.status) apiParams.status = params.status;
      response = await getDeveloperApps(apiParams);
    } else if (userRole === 'admin') {
      // 管理员可以看到所有应用的所有状态
      // 只有当明确指定状态时才添加status参数
      if (params.status && params.status !== undefined && params.status !== '') {
        apiParams.status = params.status;
      }
      // 不传status参数，后端会返回所有状态的应用
      response = await getAppList(apiParams);
    } else {
      // 普通用户只能看到已审核通过的应用
      apiParams.status = 'approved';
      response = await getAppList(apiParams);
    }
    
    console.log('API响应数据:', response);
    
    // 检查响应格式并处理数据
    if (response && response.code === 200) {
      // 适配后端返回的数据结构
      return {
        data: response.data || [],
        total: response.total || 0,
        page: response.page || 1,
        pageSize: response.page_size || 10
      };
    }
    
    return { data: [], total: 0 };
  } catch (error) {
    console.error('获取应用列表失败:', error);
    message.error('获取应用列表失败，请稍后重试');
    return { data: [], total: 0 };
  }
};

const AppList: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const currentUser = initialState?.currentUser;
  
  const [searchParams, setSearchParams] = useState({
    page: 1,
    pageSize: 10,
    keyword: '',
    category: '',
    status: undefined, // 默认不筛选状态，显示全部应用
    developer: '',
    dateRange: null,
    minDownloads: '',
    minRating: '',
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<AppItem[]>([]);
  const [rejectReasonModalVisible, setRejectReasonModalVisible] = useState(false);
  const [currentRejectReason, setCurrentRejectReason] = useState<string>('');
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [advancedSearchVisible, setAdvancedSearchVisible] = useState(false);
  const [batchOperationVisible, setBatchOperationVisible] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [form] = Form.useForm();

  // 使用真实API请求，根据用户角色调用不同接口
  const { data, loading, refresh } = useRequest(() => fetchAppList({
    ...searchParams,
    page: current,
    pageSize: pageSize,
  }, currentUser?.role), {
    refreshDeps: [searchParams, current, pageSize, currentUser?.role],
    formatResult: (res) => {
      console.log('格式化API响应:', res);
      return res;
    }, 
  });

  // 处理API返回的数据结构
  const appListData = data?.data || [];
  const totalItems = data?.total || 0;

  const handleSearch = (values: any) => {
    setSearchParams(values);
    setCurrent(1); // 搜索时重置到第一页
  };

  const handleViewDetail = (id: string) => {
    history.push(`/app/detail/${id}`);
  };

  const handleEdit = (id: string) => {
    history.push(`/app/edit/${id}`);
  };

  const handleDelete = (id: string) => {
    message.success(`删除应用 ID: ${id}`);
    refresh();
  };

  // 提交审核
  const handleSubmitReview = async (id: number) => {
    try {
      await submitAppForReview(id.toString());
      message.success('应用已提交审核');
      refresh();
    } catch (error) {
      message.error('提交审核失败，请重试');
      console.error('提交审核失败:', error);
    }
  };

  // 重新提交审核（被拒绝的应用）
  const handleResubmitReview = async (id: number) => {
    try {
      await submitAppForReview(id.toString());
      message.success('应用已重新提交审核');
      refresh();
    } catch (error) {
      message.error('重新提交审核失败，请重试');
      console.error('重新提交审核失败:', error);
    }
  };

  // 显示拒绝原因
  const handleShowRejectReason = (reason: string) => {
    setCurrentRejectReason(reason || '无具体原因');
    setRejectReasonModalVisible(true);
  };

  // 批量操作功能
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的应用');
      return;
    }
    
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedRowKeys.length} 个应用吗？此操作不可恢复。`,
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 这里应该调用批量删除API
          // await batchDeleteApps(selectedRowKeys);
          message.success(`已删除${selectedRowKeys.length}个应用`);
          setSelectedRowKeys([]);
          setSelectedRows([]);
          refresh();
        } catch (error) {
          message.error('批量删除失败，请重试');
        }
      },
    });
  };

  const handleBatchApprove = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要审核通过的应用');
      return;
    }
    
    Modal.confirm({
      title: '批量审核通过',
      content: `确定要审核通过选中的 ${selectedRowKeys.length} 个应用吗？`,
      okText: '确认通过',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 这里应该调用批量审核API
          // await batchApproveApps(selectedRowKeys);
          message.success(`已审核通过${selectedRowKeys.length}个应用`);
          setSelectedRowKeys([]);
          setSelectedRows([]);
          refresh();
        } catch (error) {
          message.error('批量审核失败，请重试');
        }
      },
    });
  };

  const handleBatchReject = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要拒绝的应用');
      return;
    }
    
    Modal.confirm({
      title: '批量拒绝',
      content: `确定要拒绝选中的 ${selectedRowKeys.length} 个应用吗？`,
      okText: '确认拒绝',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 这里应该调用批量拒绝API
          // await batchRejectApps(selectedRowKeys);
          message.success(`已拒绝${selectedRowKeys.length}个应用`);
          setSelectedRowKeys([]);
          setSelectedRows([]);
          refresh();
        } catch (error) {
          message.error('批量拒绝失败，请重试');
        }
      },
    });
  };

  const handleCreate = () => {
    history.push('/app/create');
  };

  // 导出功能
  const handleExport = async (format: 'excel' | 'csv' = 'excel') => {
    setExportLoading(true);
    try {
      // 构建导出参数
      const exportParams = {
        ...searchParams,
        format,
        selectedIds: selectedRowKeys.length > 0 ? selectedRowKeys : undefined,
      };
      
      // 这里应该调用导出API
      // const response = await exportAppList(exportParams);
      
      // 模拟导出功能
      const exportData = selectedRowKeys.length > 0 ? selectedRows : appListData;
      const csvContent = generateCSV(exportData);
      downloadFile(csvContent, `应用列表_${new Date().toISOString().split('T')[0]}.csv`, 'text/csv');
      
      message.success(`导出${format.toUpperCase()}文件成功`);
    } catch (error) {
      message.error('导出失败，请重试');
      console.error('导出失败:', error);
    } finally {
      setExportLoading(false);
    }
  };

  // 生成CSV内容
  const generateCSV = (data: AppItem[]) => {
    const headers = ['应用名称', '开发者', '类别', '状态', '版本', '下载量', '评分', '更新时间'];
    const rows = data.map(item => [
      item.name,
      item.developer_name || item.developerName || item.developer || '',
      item.category,
      item.status,
      item.current_version || item.currentVersion || item.version || '',
      item.download_count || item.downloadCount || item.downloads || 0,
      item.average_rating || item.averageRating || item.rating || 0,
      item.updated_at ? new Date(item.updated_at).toLocaleString() : ''
    ]);
    
    return [headers, ...rows].map(row => row.join(',')).join('\n');
  };

  // 下载文件
  const downloadFile = (content: string, filename: string, contentType: string) => {
    const blob = new Blob([content], { type: contentType });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  // 高级搜索
  const handleAdvancedSearch = (values: any) => {
    const newSearchParams = {
      ...searchParams,
      ...values,
      dateRange: values.dateRange,
    };
    setSearchParams(newSearchParams);
    setCurrent(1);
    setAdvancedSearchVisible(false);
    refresh();
  };

  // 重置搜索
  const handleResetSearch = () => {
    const resetParams = {
      page: 1,
      pageSize: 10,
      keyword: '',
      category: '',
      status: undefined,
      developer: '',
      dateRange: null,
      minDownloads: '',
      minRating: '',
    };
    setSearchParams(resetParams);
    form.resetFields();
    setCurrent(1);
    refresh();
  };

  const columns: ColumnsType<AppItem> = [
    {
      title: '应用信息',
      key: 'appInfo',
      width: 250,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar 
            src={record.icon} 
            size={40} 
            style={{ marginRight: 12 }}
            icon={<AppstoreOutlined />}
            onError={() => {
              console.warn('应用图标加载失败:', record.icon);
              return false;
            }}
          />
          <div>
            <div style={{ fontWeight: 'bold' }}>{record.name}</div>
            <div style={{ fontSize: 12, color: '#888' }}>{record.developer_name || record.developerName || record.developer}</div>
          </div>
        </div>
      ),
    },
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
      width: 100,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => {
        let color = 'blue';
        let text = '待审核';
        
        if (status === 'published' || status === 'approved') {
          color = 'green';
          text = '已发布';
        } else if (status === 'draft') {
          color = 'gray';
          text = '草稿';
        } else if (status === 'rejected') {
          color = 'red';
          text = '已拒绝';
        }
        
        return <Badge status={color as any} text={text} />;
      },
    },
    {
      title: '版本',
      dataIndex: 'current_version',
      key: 'version',
      width: 100,
      render: (version, record) => record.current_version || record.currentVersion || record.version,
    },
    {
      title: '大小',
      dataIndex: 'size',
      key: 'size',
      width: 100,
    },
    {
      title: '下载量',
      dataIndex: 'download_count',
      key: 'downloads',
      width: 120,
      render: (downloads, record) => {
        const count = record.download_count || record.downloadCount || record.downloads || 0;
        if (count >= 1000000000) {
          return `${(count / 1000000000).toFixed(1)}B`;
        } else if (count >= 1000000) {
          return `${(count / 1000000).toFixed(1)}M`;
        } else if (count >= 1000) {
          return `${(count / 1000).toFixed(1)}K`;
        }
        return count;
      },
      sorter: (a, b) => {
        const aCount = a.download_count || a.downloadCount || a.downloads || 0;
        const bCount = b.download_count || b.downloadCount || b.downloads || 0;
        return aCount - bCount;
      },
    },
    {
      title: '评分',
      dataIndex: 'average_rating',
      key: 'rating',
      width: 100,
      render: (rating, record) => {
        const score = record.average_rating || record.averageRating || record.rating || 0;
        if (score === 0) return '暂无评分';
        return score.toFixed(1);
      },
      sorter: (a, b) => {
        const aRating = a.average_rating || a.averageRating || a.rating || 0;
        const bRating = b.average_rating || b.averageRating || b.rating || 0;
        return aRating - bRating;
      },
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      width: 100,
      render: (price: number | string) => {
        // 由于后端暂未实现价格字段，统一显示为免费
        return '免费';
      },
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updateDate',
      width: 120,
      render: (date, record) => {
        const updateDate = record.updated_at || record.updatedAt;
        if (!updateDate) return '-';
        return new Date(updateDate).toLocaleString();
      },
      sorter: (a, b) => {
        const aDate = new Date(a.updated_at || a.updatedAt || 0).getTime();
        const bDate = new Date(b.updated_at || b.updatedAt || 0).getTime();
        return aDate - bDate;
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => {
        const canEdit = canEditApp(
          currentUser?.role as any,
          record.developer_id?.toString() || '',
          currentUser?.id?.toString() || ''
        );
        const canDelete = canDeleteApp(
          currentUser?.role as any,
          record.developer_id?.toString() || '',
          currentUser?.id?.toString() || ''
        );
        
        return (
          <Space size="small">
            <Tooltip title="查看详情">
              <Button
                type="link"
                size="small"
                icon={<EyeOutlined />}
                onClick={() => handleViewDetail(record.id)}
              >
                详情
              </Button>
            </Tooltip>
            
            <Tooltip title="版本管理">
              <Button
                type="link"
                size="small"
                onClick={() => history.push(`/app/list/versions/${record.id}`)}
              >
                版本管理
              </Button>
            </Tooltip>
            
            {/* 只有草稿状态的应用才显示提交审核按钮，且只有开发者可以提交自己的应用 */}
            {record.status === 'draft' && canEdit && currentUser?.role === 'developer' && (
              <Tooltip title="提交审核">
                <Button
                  type="link"
                  size="small"
                  onClick={() => handleSubmitReview(record.id)}
                >
                  提交审核
                </Button>
              </Tooltip>
            )}
            
            {/* 被拒绝的应用显示拒绝原因和再次提交按钮 */}
            {record.status === 'rejected' && canEdit && currentUser?.role === 'developer' && (
              <>
                <Tooltip title={`拒绝原因：${record.reject_reason || '无具体原因'}`}>
                  <Button
                    type="link"
                    size="small"
                    danger
                    onClick={() => handleShowRejectReason(record.reject_reason)}
                  >
                    查看拒绝原因
                  </Button>
                </Tooltip>
                <Tooltip title="重新提交审核">
                  <Button
                    type="link"
                    size="small"
                    onClick={() => handleResubmitReview(record.id)}
                  >
                    重新提交
                  </Button>
                </Tooltip>
              </>
            )}
            
            <PermissionButton permission={canEdit ? 'canViewApp' : 'canEditApp'}>
              <Tooltip title="编辑应用">
                <Button
                  type="link"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => handleEdit(record.id)}
                  disabled={!canEdit}
                >
                  编辑
                </Button>
              </Tooltip>
            </PermissionButton>
            
            <PermissionButton permission="canDeleteApp">
              <Tooltip title="删除应用">
                <Button
                  type="link"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => handleDelete(record.id)}
                  disabled={!canDelete}
                >
                  删除
                </Button>
              </Tooltip>
            </PermissionButton>
          </Space>
        );
      },
    },
  ];

  return (
    <PermissionWrapper permission="canViewApp">
      <PageContainer>
        <Card>
          {/* 搜索表单 */}
          <div style={{ marginBottom: 16 }}>
            <Space wrap>
              <Input
                placeholder="搜索应用名称或包名"
                prefix={<SearchOutlined />}
                style={{ width: 250 }}
                value={searchParams.keyword}
                onChange={(e) => setSearchParams({ ...searchParams, keyword: e.target.value })}
                onPressEnter={() => refresh()}
              />
              <Select
                placeholder="选择分类"
                style={{ width: 150 }}
                allowClear
                value={searchParams.category || undefined}
                onChange={(value) => setSearchParams({ ...searchParams, category: value || '' })}
              >
                <Option value="productivity">效率工具</Option>
                <Option value="entertainment">娱乐</Option>
                <Option value="education">教育</Option>
                <Option value="business">商务</Option>
                <Option value="lifestyle">生活</Option>
                <Option value="games">游戏</Option>
              </Select>
              <Select
                placeholder="选择状态"
                style={{ width: 120 }}
                allowClear
                value={searchParams.status || undefined}
                onChange={(value) => setSearchParams({ ...searchParams, status: value || undefined })}
              >
                <Option value="pending">待审核</Option>
                <Option value="approved">已上架</Option>
                <Option value="rejected">已拒绝</Option>
                <Option value="offline">已下架</Option>
              </Select>
              <Button type="primary" icon={<SearchOutlined />} onClick={() => refresh()}>
                搜索
              </Button>
              <Button 
                icon={<FilterOutlined />} 
                onClick={() => setAdvancedSearchVisible(true)}
              >
                高级搜索
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleResetSearch}>
                重置
              </Button>
            </Space>
          </div>

          {/* 高级搜索模态框 */}
          <Modal
            title="高级搜索"
            open={advancedSearchVisible}
            onCancel={() => setAdvancedSearchVisible(false)}
            footer={null}
            width={600}
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={handleAdvancedSearch}
              initialValues={searchParams}
            >
              <Form.Item label="开发者" name="developer">
                <Input placeholder="请输入开发者名称" />
              </Form.Item>
              <Form.Item label="更新时间范围" name="dateRange">
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>
              <Form.Item label="最小下载量" name="minDownloads">
                <Input placeholder="请输入最小下载量" type="number" />
              </Form.Item>
              <Form.Item label="最小评分" name="minRating">
                <Select placeholder="请选择最小评分" allowClear>
                  <Option value="1">1星及以上</Option>
                  <Option value="2">2星及以上</Option>
                  <Option value="3">3星及以上</Option>
                  <Option value="4">4星及以上</Option>
                  <Option value="5">5星</Option>
                </Select>
              </Form.Item>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit">
                    搜索
                  </Button>
                  <Button onClick={() => {
                    form.resetFields();
                    setAdvancedSearchVisible(false);
                  }}>
                    取消
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Modal>

          {/* 操作按钮 */}
          <div style={{ marginBottom: 16 }}>
            <Space wrap>
              <PermissionButton permission="canCreateApp">
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />}
                  onClick={() => history.push('/app/create')}
                >
                  创建应用
                </Button>
              </PermissionButton>
              
              <PermissionButton permission={['canEditApp', 'canDeleteApp']}>
                <Dropdown
                  menu={{
                    items: [
                      {
                        key: 'approve',
                        label: '批量审核通过',
                        icon: <EyeOutlined />,
                        onClick: handleBatchApprove,
                        disabled: selectedRowKeys.length === 0,
                      },
                      {
                        key: 'reject',
                        label: '批量拒绝',
                        icon: <DeleteOutlined />,
                        onClick: handleBatchReject,
                        disabled: selectedRowKeys.length === 0,
                      },
                      {
                        type: 'divider',
                      },
                      {
                        key: 'delete',
                        label: '批量删除',
                        icon: <DeleteOutlined />,
                        onClick: handleBatchDelete,
                        disabled: selectedRowKeys.length === 0,
                        danger: true,
                      },
                    ],
                  }}
                  trigger={['click']}
                >
                  <Button>
                    批量操作 {selectedRowKeys.length > 0 && `(${selectedRowKeys.length})`}
                    <MoreOutlined />
                  </Button>
                </Dropdown>
              </PermissionButton>
              
              <Dropdown
                menu={{
                  items: [
                    {
                      key: 'csv',
                      label: '导出为CSV',
                      icon: <ExportOutlined />,
                      onClick: () => handleExport('csv'),
                    },
                    {
                      key: 'excel',
                      label: '导出为Excel',
                      icon: <ExportOutlined />,
                      onClick: () => handleExport('excel'),
                    },
                    {
                      type: 'divider',
                    },
                    {
                      key: 'selected',
                      label: `导出选中项 (${selectedRowKeys.length})`,
                      icon: <ExportOutlined />,
                      onClick: () => handleExport('csv'),
                      disabled: selectedRowKeys.length === 0,
                    },
                  ],
                }}
                trigger={['click']}
              >
                <Button icon={<DownloadOutlined />} loading={exportLoading}>
                  导出数据
                </Button>
              </Dropdown>
              
              {selectedRowKeys.length > 0 && (
                <Tag color="blue">
                  已选择 {selectedRowKeys.length} 项
                </Tag>
              )}
              
              {/* 开发者只能看到自己的应用 */}
              {currentUser?.role === 'developer' && (
                <Tag color="green">
                  显示我的应用
                </Tag>
              )}
            </Space>
          </div>

          {/* 应用列表表格 */}
          <Table
            columns={columns}
            dataSource={appListData}
            loading={loading}
            rowKey="id"
            scroll={{ x: 1200 }}
            rowSelection={{
              selectedRowKeys,
              onChange: (selectedRowKeys: React.Key[], selectedRows: AppItem[]) => {
                setSelectedRowKeys(selectedRowKeys);
                setSelectedRows(selectedRows);
              },
              getCheckboxProps: (record: AppItem) => ({
                disabled: false, // 可以根据权限或状态禁用某些行的选择
                name: record.name,
              }),
            }}
            pagination={{
              current: current,
              pageSize: pageSize,
              total: totalItems,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
              onChange: (page, size) => {
                setCurrent(page);
                setPageSize(size || 10);
              },
            }}
          />
        </Card>
        
        {/* 拒绝原因显示模态框 */}
        <Modal
          title="拒绝原因"
          open={rejectReasonModalVisible}
          onCancel={() => setRejectReasonModalVisible(false)}
          footer={[
            <Button key="close" onClick={() => setRejectReasonModalVisible(false)}>
              关闭
            </Button>
          ]}
          width={500}
        >
          <div style={{ padding: '16px 0' }}>
            <p style={{ fontSize: '14px', lineHeight: '1.6', margin: 0 }}>
              {currentRejectReason}
            </p>
          </div>
        </Modal>
      </PageContainer>
    </PermissionWrapper>
  );
};

export default AppList;