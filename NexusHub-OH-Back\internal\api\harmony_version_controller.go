package api

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"

	"nexushub-oh-back/internal/models"
)

// HarmonyVersionController OpenHarmony版本控制器
type HarmonyVersionController struct {
	DB       *gorm.DB
	Validate *validator.Validate
}

// NewHarmonyVersionController 创建OpenHarmony版本控制器
func NewHarmonyVersionController(db *gorm.DB) *HarmonyVersionController {
	return &HarmonyVersionController{
		DB:       db,
		Validate: validator.New(),
	}
}

// OpenHarmonyVersionRequest OpenHarmonyOS版本请求
type OpenHarmonyVersionRequest struct {
	VersionName string `json:"version_name" validate:"required,max=50"`
	VersionCode string `json:"version_code" validate:"required,max=20"`
	Description string `json:"description" validate:"max=255"`
	IsActive    bool   `json:"is_active"`
}

// OpenHarmonyVersionResponse OpenHarmonyOS版本响应
type OpenHarmonyVersionResponse struct {
	ID          uint   `json:"id"`
	VersionName string `json:"version_name"`
	VersionCode string `json:"version_code"`
	Description string `json:"description"`
	IsActive    bool   `json:"is_active"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
}

// CreateOpenHarmonyVersion 创建OpenHarmonyOS版本 (仅系统管理员)
func (ctrl *HarmonyVersionController) CreateOpenHarmonyVersion(c *gin.Context) {
	// 检查数据库连接
	if ctrl.DB == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "服务器配置错误：数据库未连接，请联系管理员"})
		return
	}

	// 检查用户权限
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权"})
		return
	}

	// 获取用户信息
	var user models.User
	if err := ctrl.DB.First(&user, userID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取用户信息失败"})
		return
	}

	// 检查是否为系统管理员
	if user.Role != string(models.UserRoleAdmin) {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足，仅系统管理员可操作"})
		return
	}

	// 绑定请求参数
	var req OpenHarmonyVersionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "参数错误: " + err.Error()})
		return
	}

	// 验证参数
	if err := ctrl.Validate.Struct(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "参数验证失败: " + err.Error()})
		return
	}

	// 检查版本代码是否已存在
	var existingVersion models.OpenHarmonyVersion
	if err := ctrl.DB.Where("version_code = ?", req.VersionCode).First(&existingVersion).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "版本代码已存在"})
		return
	}

	// 创建版本记录
	version := models.OpenHarmonyVersion{
		VersionName: req.VersionName,
		VersionCode: req.VersionCode,
		Description: req.Description,
		IsActive:    req.IsActive,
	}

	if err := ctrl.DB.Create(&version).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建版本失败"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "版本创建成功",
		"data": OpenHarmonyVersionResponse{
			ID:          version.ID,
			VersionName: version.VersionName,
			VersionCode: version.VersionCode,
			Description: version.Description,
			IsActive:    version.IsActive,
			CreatedAt:   version.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:   version.UpdatedAt.Format("2006-01-02 15:04:05"),
		},
	})
}

// GetOpenHarmonyVersions 获取OpenHarmonyOS版本列表
func (ctrl *HarmonyVersionController) GetOpenHarmonyVersions(c *gin.Context) {
	// 检查数据库连接
	if ctrl.DB == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "服务器配置错误：数据库未连接，请联系管理员"})
		return
	}

	// 获取查询参数
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "10")
	isActiveStr := c.Query("is_active")

	page, _ := strconv.Atoi(pageStr)
	pageSize, _ := strconv.Atoi(pageSizeStr)
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 构建查询
	query := ctrl.DB.Model(&models.OpenHarmonyVersion{})
	if isActiveStr != "" {
		isActive, _ := strconv.ParseBool(isActiveStr)
		query = query.Where("is_active = ?", isActive)
	}

	// 获取总数
	var total int64
	query.Count(&total)

	// 获取数据
	var versions []models.OpenHarmonyVersion
	offset := (page - 1) * pageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&versions).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取版本列表失败"})
		return
	}

	// 转换响应格式
	var responseData []OpenHarmonyVersionResponse
	for _, version := range versions {
		responseData = append(responseData, OpenHarmonyVersionResponse{
			ID:          version.ID,
			VersionName: version.VersionName,
			VersionCode: version.VersionCode,
			Description: version.Description,
			IsActive:    version.IsActive,
			CreatedAt:   version.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:   version.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	c.JSON(http.StatusOK, PagedResponse{
		Total:    total,
		Page:     page,
		PageSize: pageSize,
		Data:     responseData,
	})
}

// GetOpenHarmonyVersion 获取单个OpenHarmonyOS版本详情
func (ctrl *HarmonyVersionController) GetOpenHarmonyVersion(c *gin.Context) {
	// 检查数据库连接
	if ctrl.DB == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "服务器配置错误：数据库未连接，请联系管理员"})
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的版本ID"})
		return
	}

	var version models.OpenHarmonyVersion
	if err := ctrl.DB.First(&version, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "版本不存在"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "获取版本详情失败"})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": OpenHarmonyVersionResponse{
			ID:          version.ID,
			VersionName: version.VersionName,
			VersionCode: version.VersionCode,
			Description: version.Description,
			IsActive:    version.IsActive,
			CreatedAt:   version.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:   version.UpdatedAt.Format("2006-01-02 15:04:05"),
		},
	})
}

// UpdateOpenHarmonyVersion 更新OpenHarmonyOS版本 (仅系统管理员)
func (ctrl *HarmonyVersionController) UpdateOpenHarmonyVersion(c *gin.Context) {
	// 检查数据库连接
	if ctrl.DB == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "服务器配置错误：数据库未连接，请联系管理员"})
		return
	}

	// 检查用户权限
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权"})
		return
	}

	// 获取用户信息
	var user models.User
	if err := ctrl.DB.First(&user, userID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取用户信息失败"})
		return
	}

	// 检查是否为系统管理员
	if user.Role != string(models.UserRoleAdmin) {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足，仅系统管理员可操作"})
		return
	}

	// 获取版本ID
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的版本ID"})
		return
	}

	// 绑定请求参数
	var req OpenHarmonyVersionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "参数错误: " + err.Error()})
		return
	}

	// 验证参数
	if err := ctrl.Validate.Struct(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "参数验证失败: " + err.Error()})
		return
	}

	// 获取现有版本
	var version models.OpenHarmonyVersion
	if err := ctrl.DB.First(&version, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "版本不存在"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "获取版本信息失败"})
		}
		return
	}

	// 检查版本代码是否与其他版本冲突
	if req.VersionCode != version.VersionCode {
		var existingVersion models.OpenHarmonyVersion
		if err := ctrl.DB.Where("version_code = ? AND id != ?", req.VersionCode, id).First(&existingVersion).Error; err == nil {
			c.JSON(http.StatusConflict, gin.H{"error": "版本代码已存在"})
			return
		}
	}

	// 更新版本信息
	version.VersionName = req.VersionName
	version.VersionCode = req.VersionCode
	version.Description = req.Description
	version.IsActive = req.IsActive

	if err := ctrl.DB.Save(&version).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新版本失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "版本更新成功",
		"data": OpenHarmonyVersionResponse{
			ID:          version.ID,
			VersionName: version.VersionName,
			VersionCode: version.VersionCode,
			Description: version.Description,
			IsActive:    version.IsActive,
			CreatedAt:   version.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:   version.UpdatedAt.Format("2006-01-02 15:04:05"),
		},
	})
}

// DeleteOpenHarmonyVersion 删除OpenHarmonyOS版本 (仅系统管理员)
func (ctrl *HarmonyVersionController) DeleteOpenHarmonyVersion(c *gin.Context) {
	// 检查数据库连接
	if ctrl.DB == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "服务器配置错误：数据库未连接，请联系管理员"})
		return
	}

	// 检查用户权限
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权"})
		return
	}

	// 获取用户信息
	var user models.User
	if err := ctrl.DB.First(&user, userID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取用户信息失败"})
		return
	}

	// 检查是否为系统管理员
	if user.Role != string(models.UserRoleAdmin) {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足，仅系统管理员可操作"})
		return
	}

	// 获取版本ID
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的版本ID"})
		return
	}

	// 检查版本是否存在
	var version models.OpenHarmonyVersion
	if err := ctrl.DB.First(&version, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "版本不存在"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "获取版本信息失败"})
		}
		return
	}

	// 检查是否有应用正在使用此版本
	var appCount int64
	ctrl.DB.Model(&models.Application{}).Where("min_open_harmony_os_ver = ?", version.VersionCode).Count(&appCount)
	if appCount > 0 {
		c.JSON(http.StatusConflict, gin.H{"error": "无法删除，仍有应用正在使用此版本"})
		return
	}

	// 删除版本
	if err := ctrl.DB.Delete(&version).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除版本失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "版本删除成功"})
}
