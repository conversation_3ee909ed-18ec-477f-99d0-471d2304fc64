# NexusHub-OH-Back

## 项目介绍

NexusHub-OH-Back 是一个基于 Go + Gin 框架开发的应用商店后端服务，为 OpenHarmony 系统的应用商店 NexusHub-OH 提供完整的 API 支持。

## 技术架构

- **语言框架**：Go + Gin
- **数据库**：PostgreSQL (主数据库)
- **缓存**：Redis
- **对象存储**：MinIO/OSS
- **消息队列**：RabbitMQ
- **搜索引擎**：Elasticsearch
- **认证机制**：JWT
- **API 风格**：RESTful
- **API 文档**：Swagger/OpenAPI 3.0

## 主要功能模块

- **用户模块**：注册、登录、资料管理、权限控制
- **应用模块**：应用上传、更新、下架、审核、发布
- **分类模块**：应用分类管理、多级分类支持
- **标签模块**：应用标签管理、应用标签关联
- **评价模块**：用户评分、评论、评论点赞、开发者回复评论
- **数据统计模块**：用户数据分析、应用下载统计
- **开发者认证模块**：开发者认证申请、认证审核、开发者信息管理

## 用户角色与权限

系统定义了以下角色，每个角色拥有不同的权限：

- **普通用户（user）**：可以浏览、下载应用，评论和评分应用
- **开发者（developer）**：拥有普通用户的所有权限，额外可以上传和管理自己的应用
- **审核员（reviewer）**：负责审核应用，决定应用是否可以上架
- **运营人员（operator）**：负责管理应用的运营相关事务，如推荐应用、置顶应用等
- **管理员（admin）**：拥有系统的所有权限，可以管理所有用户和应用

### 权限控制说明

以下功能仅限特定用户角色访问：

1. **应用分类管理**：
   - 创建分类 (`POST /api/v1/categories`)：仅管理员可访问
   - 更新分类 (`PUT /api/v1/categories/{id}`)：仅管理员可访问
   - 删除分类 (`DELETE /api/v1/categories/{id}`)：仅管理员可访问
   - 查看分类：所有用户可访问

2. **标签管理**：
   - 创建标签 (`POST /api/v1/tags`)：仅管理员可访问
   - 更新标签 (`PUT /api/v1/tags/{id}`)：仅管理员可访问
   - 删除标签 (`DELETE /api/v1/tags/{id}`)：仅管理员可访问
   - 查看标签：所有用户可访问
   
3. **应用标签关联**：
   - 为应用添加标签：应用开发者或管理员可操作
   - 移除应用标签：应用开发者或管理员可操作

4. **应用审核**：
   - 审核应用：仅审核员和管理员可操作

5. **开发者认证**：
   - 审核开发者认证申请：仅管理员可操作

## 项目结构

```
NexusHub-OH-Back/
├── cmd/                # 命令行入口
│   └── server/         # 服务器启动代码
├── config/             # 配置相关
│   └── config.yaml     # 主配置文件
├── docs/               # API 文档 (Swagger)
│   ├── docs.go         # Swagger 生成的 Go 文件
│   ├── swagger.json    # Swagger JSON 文档
│   └── swagger.yaml    # Swagger YAML 文档
├── internal/           # 内部包
│   ├── api/            # API 控制器
│   ├── middleware/     # 中间件
│   ├── models/         # 数据模型
│   ├── repository/     # 数据访问层
│   └── services/       # 业务逻辑层
├── pkg/                # 可导出的公共包
│   ├── auth/           # 认证相关
│   ├── database/       # 数据库
│   ├── elasticsearch/  # Elasticsearch搜索引擎
│   ├── logger/         # 日志
│   ├── messaging/      # 消息队列
│   └── storage/        # 存储服务
├── .env                # 环境变量（备用）
├── .gitignore          # Git 忽略规则
├── docker-compose.yml  # Docker Compose 配置
├── Dockerfile          # Docker 构建文件
├── go.mod              # Go 模块定义
└── README.md           # 项目说明
```

## 快速开始

### 环境要求

- Go 1.21+
- PostgreSQL 15+
- Redis 7+
- MinIO
- RabbitMQ
- Elasticsearch
- Docker & Docker Compose (用于部署)

### 本地开发环境搭建

1. 克隆项目
```bash
git clone https://gitee.com/nexushub-oh/NexusHub-OH-Back.git
cd NexusHub-OH-Back
```

2. 安装依赖
```bash
go mod download
```

3. 配置应用
有两种配置方式，可以选择其中一种：

#### A. 使用 YAML 配置文件（推荐）
项目会优先读取配置文件，按照以下顺序查找：
1. 项目根目录的 `config.yaml`
2. 项目 `config` 目录下的 `config.yaml`

配置示例：

```yaml
# 服务器配置
server:
  port: "8080"
  mode: "debug" # debug, release, test

# 数据库配置
database:
  host: "*************"
  port: "5432"
  user: "nexushub"
  password: "nexushub"
  dbname: "nexushub"
  sslmode: "disable"

# Redis配置
redis:
  host: "*************"
  port: "6379"
  password: "redis_NHh8tw"
  db: 0

# 更多配置参见 config/config.yaml 文件
```

#### B. 使用环境变量

如果不存在 YAML 配置文件，系统将从环境变量读取配置：

```bash
cp .env.example .env
# 编辑 .env 文件，填入正确的配置
```

环境变量配置说明：
```
# 服务器配置
SERVER_PORT=8080                # 服务器端口
SERVER_MODE=debug               # 运行模式 (debug/release)

# 数据库配置
DB_HOST=*************           # 数据库主机地址
DB_PORT=5432                    # 数据库端口
DB_USER=nexushub                # 数据库用户名
DB_PASSWORD=nexushub            # 数据库密码
DB_NAME=nexushub                # 数据库名称
DB_SSL_MODE=disable             # SSL模式

# 更多环境变量参见 .env 文件
```

4. 生成 API 文档（可选）
```bash
# 如果需要更新 API 文档，执行：
swag init
```

5. 启动服务
```bash
go run main.go
```

6. 访问 API 文档
```
http://localhost:8080/swagger/index.html
```

### 使用 Docker 部署

1. 构建并启动服务
```bash
docker-compose up -d
```

2. 检查服务状态
```bash
docker-compose ps
```

3. 查看日志
```bash
docker-compose logs -f app
```

4. 停止服务
```bash
docker-compose down
```

## API 文档

项目集成了 Swagger/OpenAPI 3.0 作为 API 文档工具，提供了详细的 API 接口说明与在线测试功能。你可以通过以下方式访问：

1. 在浏览器中访问 `/swagger/index.html` 路径（例如 http://localhost:8080/swagger/index.html）
2. 查看 `docs` 目录下的 `swagger.yaml` 或 `swagger.json` 文件

API 文档提供了以下功能：
- 所有 API 端点的详细说明
- 请求参数与响应结构示例
- 在线测试 API 端点的功能
- 认证信息的配置（需要使用 Bearer Token）

### 更新 API 文档

当你修改了控制器中的 API 注解后，需要重新生成 API 文档。在项目根目录执行以下命令：

```bash
# 安装 swag 工具（如果尚未安装）
go install github.com/swaggo/swag/cmd/swag@latest

# 生成 Swagger 文档
swag init
```

### 使用 Bearer 认证

在需要认证的接口中，需要在请求头中添加 `Authorization` 字段，值为 `Bearer {token}`。
你可以通过登录接口获取 token：

1. 调用 `POST /api/v1/users/login` 接口登录
2. 从响应中获取 token
3. 在 Swagger UI 中点击右上角的 "Authorize" 按钮
4. 在弹出的窗口中输入 `Bearer {token}`（注意保留 "Bearer" 前缀和空格）
5. 点击 "Authorize" 按钮完成认证

## API 接口详细说明

NexusHub-OH-Back 的 API 遵循 RESTful 风格设计，所有端点都以 `/api/v1` 为基础路径。以下是完整的 API 接口列表及详细说明：

### 1. 系统接口

- `GET /api/v1/health` - 健康检查
  - 功能：检查 API 服务是否正常运行
  - 权限：无需权限
  - 响应：服务状态和当前时间

### 2. 用户接口

#### 2.1 用户认证

- `POST /api/v1/users/register` - 用户注册
  - 功能：创建新用户账号
  - 请求体：
    ```json
    {
      "username": "string", // 必填，3-50字符
      "email": "string",    // 必填，有效邮箱
      "phone": "string",    // 可选，11位手机号
      "password": "string"  // 必填，6-50字符
    }
    ```
  - 响应：注册成功信息

- `POST /api/v1/users/login` - 用户登录
  - 功能：验证用户凭证并返回JWT令牌
  - 请求体：
    ```json
    {
      "username_or_email": "string", // 必填，用户名或邮箱
      "password": "string"           // 必填，密码
    }
    ```
  - 响应：
    ```json
    {
      "token": "string",         // JWT令牌
      "expires_at": "timestamp", // 令牌过期时间
      "user": {                  // 用户基本信息
        "id": 1,
        "username": "string",
        "email": "string",
        "role": "string",
        // ...其他用户信息
      }
    }
    ```

#### 2.2 用户资料

- `GET /api/v1/users/profile` - 获取用户资料
  - 功能：获取当前登录用户的详细资料
  - 权限：需要用户登录
  - 请求头：Authorization: Bearer {token}
  - 响应：用户详细信息

- `PUT /api/v1/users/profile` - 更新用户资料
  - 功能：更新当前登录用户的基本信息
  - 权限：需要用户登录
  - 请求头：Authorization: Bearer {token}
  - 请求体：
    ```json
    {
      "username": "string",     // 可选，3-50字符
      "email": "string",        // 可选，有效邮箱
      "phone": "string",        // 可选，11位手机号
      "avatar": "string",       // 可选，头像URL
      "old_password": "string", // 可选，当前密码（修改密码时必填）
      "new_password": "string", // 可选，新密码（6-50字符）
      "is_developer": true,     // 可选，是否为开发者
      "developer_info": {       // 可选，开发者信息
        "developer_name": "string", // is_developer为true时必填
        "company_name": "string",
        "website": "string",
        "description": "string"
      }
    }
    ```
  - 响应：更新后的用户信息

### 3. 分类管理接口

#### 3.1 公开接口

- `GET /api/v1/categories` - 获取分类列表
  - 功能：获取所有应用分类
  - 权限：无需权限
  - 参数：
    - `include_inactive`（可选，布尔值）：是否包含未激活的分类，默认为 false
  - 响应：分类列表

- `GET /api/v1/categories/root` - 获取根分类列表
  - 功能：获取所有根应用分类（没有父分类的分类）
  - 权限：无需权限
  - 参数：
    - `include_inactive`（可选，布尔值）：是否包含未激活的分类，默认为 false
  - 响应：根分类列表

- `GET /api/v1/categories/{id}` - 获取分类详情
  - 功能：获取指定分类的详细信息和子分类
  - 权限：无需权限
  - 参数：
    - `id`（路径参数，整数）：分类 ID
  - 响应：分类详情和子分类列表

#### 3.2 管理员接口

- `POST /api/v1/categories` - 创建分类
  - 功能：创建一个新的应用分类
  - 权限：管理员
  - 请求体：
    ```json
    {
      "name": "string", // 必填，分类名称，最大50个字符
      "description": "string", // 分类描述
      "icon": "string", // 分类图标URL
      "sort_order": 0, // 排序权重
      "parent_id": null // 父分类ID，支持多级分类
    }
    ```
  - 响应：创建成功的分类信息

- `PUT /api/v1/categories/{id}` - 更新分类
  - 功能：更新应用分类信息
  - 权限：管理员
  - 参数：
    - `id`（路径参数，整数）：分类 ID
  - 请求体：
    ```json
    {
      "name": "string", // 分类名称，最大50个字符
      "description": "string", // 分类描述
      "icon": "string", // 分类图标URL
      "sort_order": 0, // 排序权重
      "is_active": true, // 是否启用
      "parent_id": null // 父分类ID
    }
    ```
  - 响应：更新成功的分类信息

- `DELETE /api/v1/categories/{id}` - 删除分类
  - 功能：删除应用分类
  - 权限：管理员
  - 参数：
    - `id`（路径参数，整数）：分类 ID
  - 响应：删除结果

### 4. 标签管理接口

#### 4.1 公开接口

- `GET /api/v1/tags` - 获取标签列表
  - 功能：获取所有应用标签
  - 权限：无需权限
  - 参数：
    - `include_inactive`（可选，布尔值）：是否包含未激活的标签，默认为 false
  - 响应：标签列表

- `GET /api/v1/tags/{id}` - 获取标签详情
  - 功能：获取指定标签的详细信息
  - 权限：无需权限
  - 参数：
    - `id`（路径参数，整数）：标签 ID
  - 响应：标签详情和使用该标签的应用数量

- `GET /api/v1/tags/{id}/apps` - 获取标签下的应用
  - 功能：获取包含指定标签的所有应用
  - 权限：无需权限
  - 参数：
    - `id`（路径参数，整数）：标签 ID
  - 响应：应用列表

- `GET /api/v1/apps/{id}/tags` - 获取应用的标签
  - 功能：获取应用的所有标签
  - 权限：无需权限
  - 参数：
    - `id`（路径参数，整数）：应用 ID
  - 响应：标签列表

#### 4.2 需要认证的接口

- `POST /api/v1/apps/{id}/tags` - 为应用添加标签
  - 功能：为应用添加多个标签
  - 权限：应用开发者或管理员
  - 参数：
    - `id`（路径参数，整数）：应用 ID
  - 请求体：
    ```json
    {
      "tag_ids": [1, 2, 3] // 标签ID列表
    }
    ```
  - 响应：添加结果

- `DELETE /api/v1/apps/{id}/tags/{tag_id}` - 删除应用的标签
  - 功能：删除应用的指定标签
  - 权限：应用开发者或管理员
  - 参数：
    - `id`（路径参数，整数）：应用 ID
    - `tag_id`（路径参数，整数）：标签 ID
  - 响应：删除结果

#### 4.3 管理员接口

- `POST /api/v1/tags` - 创建标签
  - 功能：创建一个新的应用标签
  - 权限：管理员
  - 请求体：
    ```json
    {
      "name": "string", // 必填，标签名称，最大50个字符
      "description": "string", // 标签描述
      "color": "#666666" // 标签颜色，十六进制
    }
    ```
  - 响应：创建成功的标签信息

- `PUT /api/v1/tags/{id}` - 更新标签
  - 功能：更新应用标签信息
  - 权限：管理员
  - 参数：
    - `id`（路径参数，整数）：标签 ID
  - 请求体：
    ```json
    {
      "name": "string", // 标签名称，最大50个字符
      "description": "string", // 标签描述
      "color": "#666666", // 标签颜色，十六进制
      "is_active": true // 是否启用
    }
    ```
  - 响应：更新成功的标签信息

- `DELETE /api/v1/tags/{id}` - 删除标签
  - 功能：删除应用标签
  - 权限：管理员
  - 参数：
    - `id`（路径参数，整数）：标签 ID
  - 响应：删除结果

### 5. 应用接口

#### 5.1 应用浏览

- `GET /api/v1/apps` - 获取应用列表
  - 功能：分页获取应用列表，支持多种过滤和排序
  - 参数：
    - `page`: 页码，默认1
    - `page_size`: 每页数量，默认20
    - `category`: 应用分类
    - `sort`: 排序方式(newest/popular/rating)
    - `keyword`: 搜索关键词
  - 响应：应用列表和分页信息

- `GET /api/v1/apps/:id` - 获取应用详情
  - 功能：获取单个应用的详细信息
  - 参数：应用ID (path)
  - 响应：应用详细信息，包括开发者、截图、最新版本等

#### 5.2 应用管理

- `POST /api/v1/apps` - 创建应用
  - 功能：开发者创建新应用
  - 权限：需要开发者权限
  - 请求头：Authorization: Bearer {token}
  - 请求体：
    ```json
    {
      "name": "string",            // 必填，2-100字符
      "package": "string",         // 必填，3-100字符
      "description": "string",     // 可选，应用详细描述
      "short_desc": "string",      // 可选，最多200字符
      "category": "string",        // 必填，应用分类
      "icon": "string",            // 必填，应用图标URL
      "banner_image": "string",    // 可选，横幅图片URL
      "min_open_harmony_os_ver": "string", // 必填，最低系统版本
      "tags": "string",            // 可选，标签，最多255字符
      "website_url": "string",     // 可选，应用网站URL
      "privacy_url": "string"      // 可选，隐私政策URL
    }
    ```
  - 响应：创建的应用信息

- `PUT /api/v1/apps/:id` - 更新应用
  - 功能：更新应用信息
  - 权限：需要是应用的开发者
  - 请求头：Authorization: Bearer {token}
  - 参数：应用ID (path)
  - 请求体：与创建应用类似，字段均为可选
  - 响应：更新后的应用信息

### 6. 评论接口

#### 6.1 评论管理

- `GET /api/v1/apps/:id/reviews` - 获取应用评论
  - 功能：获取指定应用的评论列表
  - 参数：
    - 应用ID (path)
    - `page`: 页码，默认1
    - `page_size`: 每页数量，默认20
  - 响应：评论列表和分页信息

- `POST /api/v1/apps/:id/reviews` - 发表评论
  - 功能：用户对应用发表评论和评分
  - 权限：需要用户登录
  - 请求头：Authorization: Bearer {token}
  - 参数：应用ID (path)
  - 请求体：
    ```json
    {
      "title": "string",     // 必填，最多100字符
      "content": "string",   // 必填，1-1000字符
      "rating": 5,           // 必填，1-5分
      "app_version": "string" // 必填，应用版本，最多50字符
    }
    ```
  - 响应：创建的评论信息

- `POST /api/v1/apps/:id/reviews/:review_id/respond` - 开发者回复评论
  - 功能：应用开发者回复用户评论
  - 权限：需要是应用的开发者
  - 请求头：Authorization: Bearer {token}
  - 参数：
    - 应用ID (path)
    - 评论ID (path)
  - 请求体：
    ```json
    {
      "content": "string" // 必填，1-1000字符
    }
    ```
  - 响应：更新后的评论信息

#### 6.2 评论互动

- `POST /api/v1/apps/:id/reviews/:review_id/like` - 点赞评论
  - 功能：用户对评论点赞
  - 权限：需要用户登录
  - 请求头：Authorization: Bearer {token}
  - 参数：
    - 应用ID (path)
    - 评论ID (path)
  - 响应：操作结果

- `POST /api/v1/apps/:id/reviews/:review_id/unlike` - 取消点赞评论
  - 功能：用户取消对评论的点赞
  - 权限：需要用户登录
  - 请求头：Authorization: Bearer {token}
  - 参数：
    - 应用ID (path)
    - 评论ID (path)
  - 响应：操作结果

### 7. 统计接口

#### 7.1 下载统计

- `GET /api/v1/stats/apps/:id/downloads` - 获取应用下载统计
  - 功能：获取应用的下载统计数据
  - 权限：需要是应用的开发者或管理员
  - 请求头：Authorization: Bearer {token}
  - 参数：
    - 应用ID (path)
    - `days`: 统计天数，默认30
  - 响应：
    ```json
    {
      "total_downloads": 1000,
      "daily_stats": [
        {"date": "2023-10-01", "downloads": 100},
        {"date": "2023-10-02", "downloads": 120}
      ],
      "device_stats": [
        {"device_type": "phone", "downloads": 800},
        {"device_type": "tablet", "downloads": 200}
      ]
    }
    ```

- `GET /api/v1/stats/downloads` - 获取用户下载记录
  - 功能：获取当前用户的应用下载记录
  - 权限：需要用户登录
  - 请求头：Authorization: Bearer {token}
  - 参数：
    - `page`: 页码，默认1
    - `page_size`: 每页数量，默认20
  - 响应：下载记录列表和分页信息

- `POST /api/v1/apps/:id/versions/:version_id/download` - 记录应用下载
  - 功能：记录用户下载应用的行为
  - 权限：需要用户登录
  - 请求头：Authorization: Bearer {token}
  - 参数：
    - 应用ID (path)
    - 版本ID (path)
    - `device_type`: 设备类型
    - `device_model`: 设备型号
    - `device_os`: 设备操作系统版本
  - 响应：操作结果

### 8. 管理员接口

#### 8.1 用户管理

- `POST /api/v1/admin/users` - 创建用户
  - 功能：管理员创建新用户
  - 权限：需要管理员权限
  - 请求头：Authorization: Bearer {token}
  - 请求体：
    ```json
    {
      "username": "string", // 必填，3-50字符
      "email": "string",    // 必填，有效邮箱
      "phone": "string",    // 可选，11位手机号
      "password": "string", // 必填，6-50字符
      "role": "string"      // 必填，角色(user/developer/operator/reviewer/admin)
    }
    ```
  - 响应：操作结果

- `PUT /api/v1/admin/users/:id/role` - 更新用户角色
  - 功能：管理员更新用户角色
  - 权限：需要管理员权限
  - 请求头：Authorization: Bearer {token}
  - 参数：用户ID (path)
  - 请求体：
    ```json
    {
      "role": "string" // 必填，角色(user/developer/operator/reviewer/admin)
    }
    ```
  - 响应：操作结果

#### 8.2 开发者管理

- `GET /api/v1/admin/developers/verify` - 获取待审核的开发者列表
  - 功能：获取待审核的开发者认证申请
  - 权限：需要管理员权限
  - 请求头：Authorization: Bearer {token}
  - 参数：
    - `page`: 页码，默认1
    - `page_size`: 每页数量，默认20
    - `status`: 认证状态(pending/approved/rejected)，默认pending
  - 响应：开发者列表和分页信息

- `POST /api/v1/admin/developers/:id/verify` - 审核开发者认证
  - 功能：管理员审核开发者认证申请
  - 权限：需要管理员权限
  - 请求头：Authorization: Bearer {token}
  - 参数：用户ID (path)
  - 请求体：
    ```json
    {
      "verify_status": "string", // 必填，审核结果(approved/rejected)
      "verify_reason": "string"  // 拒绝时必填，拒绝理由
    }
    ```
  - 响应：更新后的认证信息

### 9. 审核员接口

- `GET /api/v1/reviewer/apps/pending` - 获取待审核应用
  - 功能：获取所有待审核的应用列表
  - 权限：需要审核员或管理员权限
  - 请求头：Authorization: Bearer {token}
  - 参数：
    - `page`: 页码，默认1
    - `page_size`: 每页数量，默认20
  - 响应：待审核应用列表和分页信息

- `POST /api/v1/reviewer/apps/:id/review` - 审核应用
  - 功能：审核员审核应用
  - 权限：需要审核员或管理员权限
  - 请求头：Authorization: Bearer {token}
  - 参数：应用ID (path)
  - 请求体：
    ```json
    {
      "status": "string", // 必填，审核结果(approved/rejected)
      "reason": "string"  // 拒绝时必填，拒绝理由
    }
    ```
  - 响应：操作结果

## 数据模型

### 用户模型

用户模型包含以下主要字段：

```go
type User struct {
    ID             uint      // 用户ID
    Username       string    // 用户名
    Email          string    // 邮箱
    Phone          string    // 电话号码
    Password       string    // 密码（加密存储）
    Role           string    // 角色(user/developer/operator/reviewer/admin)
    Status         string    // 状态(active/inactive/suspended)
    Avatar         string    // 头像URL
    LastLoginAt    time.Time // 最后登录时间
    
    // 开发者相关字段
    IsDeveloper      bool      // 是否为开发者
    DeveloperName    string    // 开发者名称
    CompanyName      string    // 公司名称
    Website          string    // 网站
    Description      string    // 描述
    VerifyStatus     string    // 认证状态(pending/approved/rejected)
    // 更多开发者字段...
}
```

### 应用模型

应用模型包含以下主要字段：

```go
type Application struct {
    ID              uint      // 应用ID
    Name            string    // 应用名称
    Package         string    // 包名
    Description     string    // 描述
    ShortDesc       string    // 简短描述
    Category        string    // 分类
    Icon            string    // 图标URL
    DeveloperID     uint      // 开发者ID
    Status          string    // 状态(pending/approved/rejected)
    DownloadCount   int64     // 下载次数
    AverageRating   float64   // 平均评分
    // 更多应用相关字段...
}
```

## 常见问题与解决方案

### 1. 登录请求404错误

**问题描述**：
用户尝试登录时，API服务返回404错误(Not Found)。

**原因分析**：
前端通过`/api/v1/user/login`路径发送请求，而后端API实际路径为`/api/v1/users/login`(复数形式)，导致路径不匹配。

**解决方法**：
1. 修改前端`services/user.ts`文件中的请求路径，将`/user/login`改为`/users/login`
2. 修改前端登录请求参数，使用`username_or_email`作为参数名代替`username`，与后端API参数匹配

这种API路径不一致的问题通常在前后端协作开发时容易出现，建议前后端开发人员保持良好的沟通，确保API路径和参数一致。

## 详细API文档

除了Swagger提供的在线文档外，项目现在还提供了更详细的Markdown格式API文档：

- [API-文档.md](API-文档.md) - 基础信息、认证机制、用户和应用API
- [API-文档-评论和统计.md](API-文档-评论和统计.md) - 评论管理和数据统计API
- [API-文档-审核和管理.md](API-文档-审核和管理.md) - 审核员、运营人员和系统API
- [API-文档-索引.md](API-文档-索引.md) - API文档的总索引和概览

这些文档提供了每个API端点的详细信息，包括请求格式、参数说明和响应示例，方便开发人员进行前后端集成。

## 仪表盘功能说明

NexusHub-OH-Back 提供了仪表盘功能，包括分析页、监控页和工作台。以下是相关接口说明：

### 分析页

- `GET /api/v1/dashboard/analytics/summary` - 获取分析页摘要数据（总用户数、总应用数、总下载量等）
- `GET /api/v1/dashboard/analytics/trend` - 获取趋势分析数据（用户增长、应用增长、下载趋势等）
- `GET /api/v1/dashboard/analytics/categories` - 获取分类统计数据
- `GET /api/v1/dashboard/analytics/popular-apps` - 获取热门应用

### 监控页

- `GET /api/v1/dashboard/monitoring/data` - 获取系统监控数据（CPU、内存、磁盘使用率等）
- `GET /api/v1/dashboard/monitoring/logs` - 获取系统日志
- `GET /api/v1/dashboard/monitoring/alerts` - 获取告警事件

### 工作台

- `GET /api/v1/dashboard/workbench/summary` - 获取工作台摘要（我的应用数量、下载量、评分等）
- `GET /api/v1/dashboard/workbench/activities` - 获取最近活动
- `GET /api/v1/dashboard/workbench/tasks` - 获取任务列表
- `POST /api/v1/dashboard/workbench/tasks` - 创建任务
- `PUT /api/v1/dashboard/workbench/tasks/{id}` - 更新任务
- `DELETE /api/v1/dashboard/workbench/tasks/{id}` - 删除任务 

## 开发指南

详细开发指南请参考 `/docs` 目录下的文档。

## 贡献指南

欢迎提交 Issue 或 Pull Request 来帮助我们改进项目。

## 许可证

[Apache-2.0 许可证](LICENSE)