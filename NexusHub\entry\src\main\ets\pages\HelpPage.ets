import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';
import { LoadingView, LoadingState } from '../components/LoadingView';
import { router } from '@kit.ArkUI';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { LengthMetrics } from '@kit.ArkUI';

/**
 * 帮助项模型
 */
interface HelpItem {
  id: number;
  title: string;
  content: string;
  category: string;
  isExpanded?: boolean;
}

/**
 * 反馈类型
 */
interface FeedbackType {
  id: string;
  name: string;
  icon: string;
}

/**
 * 帮助与反馈页面
 */
@Entry
@Component
struct HelpPage {
  @State selectedTab: number = 0; // 0: 帮助中心, 1: 意见反馈
  @State helpItems: HelpItem[] = [];
  @State searchText: string = '';
  @State feedbackType: string = '';
  @State feedbackContent: string = '';
  @State contactInfo: string = '';
  @State loadingState: LoadingState = LoadingState.SUCCESS;

  private deviceUtils = DeviceUtils.getInstance();
  private feedbackTypes: FeedbackType[] = [
    { id: 'bug', name: '功能异常', icon: '🐛' },
    { id: 'suggestion', name: '功能建议', icon: '💡' },
    { id: 'ui', name: '界面问题', icon: '🎨' },
    { id: 'performance', name: '性能问题', icon: '⚡' },
    { id: 'other', name: '其他问题', icon: '❓' }
  ];

  aboutToAppear() {
    this.loadHelpItems();
  }

  /**
   * 加载帮助项目
   */
  private loadHelpItems() {
    this.helpItems = [
      {
        id: 1,
        title: '如何下载应用？',
        content: '1. 在首页或分类页面找到您想要的应用\n2. 点击应用图标进入详情页面\n3. 点击"下载"按钮开始下载\n4. 下载完成后可在"我的下载"中查看',
        category: '下载安装',
        isExpanded: false
      },
      {
        id: 2,
        title: '如何管理我的下载？',
        content: '在个人中心页面点击"我的下载"，您可以：\n• 查看所有已下载的应用\n• 查看下载进度\n• 暂停或继续下载\n• 删除下载记录',
        category: '下载安装',
        isExpanded: false
      },
      {
        id: 3,
        title: '如何收藏应用？',
        content: '在应用详情页面点击❤️图标即可收藏应用。收藏的应用可以在个人中心的"我的收藏"中查看。',
        category: '收藏管理',
        isExpanded: false
      },
      {
        id: 4,
        title: '如何搜索应用？',
        content: '1. 点击首页顶部的搜索框\n2. 输入应用名称或关键词\n3. 点击搜索按钮或按回车键\n4. 浏览搜索结果',
        category: '搜索功能',
        isExpanded: false
      },
      {
        id: 5,
        title: '如何查看应用详情？',
        content: '点击任意应用图标或名称即可进入应用详情页面，您可以查看：\n• 应用介绍和截图\n• 用户评价和评分\n• 版本信息和更新日志\n• 开发者信息',
        category: '应用详情',
        isExpanded: false
      },
      {
        id: 6,
        title: '如何发表评论？',
        content: '1. 进入应用详情页面\n2. 滑动到评论区域\n3. 点击"写评论"按钮\n4. 选择评分并输入评论内容\n5. 点击"发布"按钮',
        category: '评论功能',
        isExpanded: false
      },
      {
        id: 7,
        title: '账号登录问题',
        content: '如果遇到登录问题，请检查：\n• 网络连接是否正常\n• 用户名和密码是否正确\n• 是否需要验证码\n如仍无法解决，请联系客服',
        category: '账号问题',
        isExpanded: false
      },
      {
        id: 8,
        title: '应用无法下载怎么办？',
        content: '请尝试以下解决方案：\n• 检查网络连接\n• 确保存储空间充足\n• 重启应用后重试\n• 清除应用缓存\n如问题持续，请联系技术支持',
        category: '常见问题',
        isExpanded: false
      }
    ];
  }

  /**
   * 获取过滤后的帮助项目
   */
  private getFilteredHelpItems(): HelpItem[] {
    if (!this.searchText.trim()) {
      return this.helpItems;
    }
    
    const keyword = this.searchText.toLowerCase();
    return this.helpItems.filter(item => 
      item.title.toLowerCase().includes(keyword) || 
      item.content.toLowerCase().includes(keyword) ||
      item.category.toLowerCase().includes(keyword)
    );
  }

  /**
   * 切换帮助项目展开状态
   */
  private toggleHelpItem(item: HelpItem) {
    const index = this.helpItems.findIndex(h => h.id === item.id);
    if (index !== -1) {
      this.helpItems[index].isExpanded = !this.helpItems[index].isExpanded;
    }
  }

  /**
   * 提交反馈
   */
  private async submitFeedback() {
    if (!this.feedbackType || !this.feedbackContent.trim()) {
      // 这里应该显示提示信息
      hilog.warn(0x0000, 'HelpPage', '请填写完整的反馈信息');
      return;
    }

    try {
      this.loadingState = LoadingState.LOADING;
      
      // 这里应该调用API提交反馈
      // 暂时模拟提交过程
      await new Promise<void>(resolve => setTimeout(resolve, 2000));
      
      // 清空表单
      this.feedbackType = '';
      this.feedbackContent = '';
      this.contactInfo = '';
      
      this.loadingState = LoadingState.SUCCESS;
      
      // 这里应该显示成功提示
      hilog.info(0x0000, 'HelpPage', '反馈提交成功');
    } catch (error) {
      hilog.error(0x0000, 'HelpPage', '提交反馈失败: %{public}s', JSON.stringify(error));
      this.loadingState = LoadingState.ERROR;
    }
  }

  /**
   * 标签栏
   */
  @Builder
  private TabBar() {
    Row() {
      ForEach(['帮助中心', '意见反馈'], (title: string, index: number) => {
        Text(title)
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontColor(this.selectedTab === index ? Constants.COLORS.PRIMARY : Constants.COLORS.TEXT_SECONDARY)
          .fontWeight(this.selectedTab === index ? FontWeight.Bold : FontWeight.Normal)
          .padding({ left: 16, right: 16, top: 12, bottom: 12 })
          .onClick(() => {
            this.selectedTab = index;
          })
      }, (title: string) => title)
    }
    .width('100%')
    .backgroundColor(Constants.COLORS.WHITE)
    .justifyContent(FlexAlign.SpaceAround)
  }

  /**
   * 搜索框
   */
  @Builder
  private SearchBox() {
    Row({ space: 8 }) {
      Text('🔍')
        .fontSize(16)
        .fontColor(Constants.COLORS.TEXT_HINT)

      TextInput({ placeholder: '搜索帮助内容...', text: this.searchText })
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
        .fontColor(Constants.COLORS.TEXT_PRIMARY)
        .backgroundColor('transparent')
        .border({ width: 0 })
        .layoutWeight(1)
        .onChange((value: string) => {
          this.searchText = value;
        })
    }
    .width('100%')
    .height(40)
    .padding({ left: 12, right: 12 })
    .backgroundColor(Constants.COLORS.BACKGROUND_LIGHT)
    .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
    .margin({ left: 16, right: 16, top: 12, bottom: 12 })
  }

  /**
   * 帮助项目
   */
  @Builder
  private HelpItemView(item: HelpItem) {
    Column() {
      Row({ space: 12 }) {
        Column({ space: 4 }) {
          Text(item.title)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .fontWeight(FontWeight.Medium)
            .width('100%')
            .textAlign(TextAlign.Start)

          Text(item.category)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.PRIMARY)
            .backgroundColor($r('app.color.overlay_medium'))
            .padding({ left: 8, right: 8, top: 2, bottom: 2 })
            .borderRadius(Constants.BORDER_RADIUS.SMALL)
            .alignSelf(ItemAlign.Start)
        }
        .alignItems(HorizontalAlign.Start)
        .layoutWeight(1)

        Text(item.isExpanded ? '▲' : '▼')
          .fontSize(12)
          .fontColor(Constants.COLORS.TEXT_HINT)
      }
      .width('100%')
      .onClick(() => {
        this.toggleHelpItem(item);
      })

      if (item.isExpanded) {
        Text(item.content)
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
          .lineHeight(20)
          .width('100%')
          .margin({ top: 12 })
          .textAlign(TextAlign.Start)
      }
    }
    .width('100%')
    .padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
    .backgroundColor(Constants.COLORS.WHITE)
    .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
    .margin({ left: 16, right: 16, bottom: 8 })
  }

  /**
   * 帮助中心内容
   */
  @Builder
  private HelpContent() {
    Column() {
      this.SearchBox()
      
      Scroll() {
        Column() {
          if (this.getFilteredHelpItems().length === 0) {
            Column({ space: 16 }) {
              Text('❓')
                .fontSize(48)
                .fontColor(Constants.COLORS.TEXT_HINT)
              
              Text('未找到相关帮助内容')
                .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
                .fontColor(Constants.COLORS.TEXT_SECONDARY)
              
              Text('请尝试其他关键词或联系客服')
                .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
                .fontColor(Constants.COLORS.TEXT_HINT)
            }
            .width('100%')
            .height(200)
            .justifyContent(FlexAlign.Center)
            .alignItems(HorizontalAlign.Center)
          } else {
            ForEach(this.getFilteredHelpItems(), (item: HelpItem) => {
              this.HelpItemView(item)
            }, (item: HelpItem) => item.id.toString())
          }
          
          // 底部间距
          Column()
            .height(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE))
        }
      }
      .scrollable(ScrollDirection.Vertical)
      .scrollBar(BarState.Auto)
      .layoutWeight(1)
    }
    .layoutWeight(1)
  }

  /**
   * 反馈类型选择
   */
  @Builder
  private FeedbackTypeSelector() {
    Column({ space: 8 }) {
      Text('反馈类型 *')
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
        .fontColor(Constants.COLORS.TEXT_PRIMARY)
        .fontWeight(FontWeight.Medium)
        .width('100%')
        .textAlign(TextAlign.Start)

      Flex({ wrap: FlexWrap.Wrap, space: { main: LengthMetrics.vp(8), cross: LengthMetrics.vp(8) } }) {
        ForEach(this.feedbackTypes, (type: FeedbackType) => {
          Row({ space: 6 }) {
            Text(type.icon)
              .fontSize(16)
            Text(type.name)
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
              .fontColor(this.feedbackType === type.id ? Constants.COLORS.WHITE : Constants.COLORS.TEXT_SECONDARY)
          }
          .padding({ left: 12, right: 12, top: 8, bottom: 8 })
          .backgroundColor(this.feedbackType === type.id ? Constants.COLORS.PRIMARY : $r('app.color.overlay_light'))
          .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
          .onClick(() => {
            this.feedbackType = type.id;
          })
        }, (type: FeedbackType) => type.id.toString())
      }
      .width('100%')
    }
    .width('100%')
    .alignItems(HorizontalAlign.Start)
  }

  /**
   * 意见反馈内容
   */
  @Builder
  private FeedbackContent() {
    Scroll() {
      Column({ space: 16 }) {
        // 反馈类型
        this.FeedbackTypeSelector()

        // 反馈内容
        Column({ space: 8 }) {
          Text('问题描述 *')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .fontWeight(FontWeight.Medium)
            .width('100%')
            .textAlign(TextAlign.Start)

          TextArea({ placeholder: '请详细描述您遇到的问题或建议...', text: this.feedbackContent })
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .backgroundColor(Constants.COLORS.BACKGROUND_LIGHT)
            .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
            .height(120)
            .onChange((value: string) => {
              this.feedbackContent = value;
            })
        }
        .width('100%')
        .alignItems(HorizontalAlign.Start)

        // 联系方式
        Column({ space: 8 }) {
          Text('联系方式（可选）')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .fontWeight(FontWeight.Medium)
            .width('100%')
            .textAlign(TextAlign.Start)

          TextInput({ placeholder: '请输入您的邮箱或手机号，以便我们联系您', text: this.contactInfo })
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .backgroundColor(Constants.COLORS.BACKGROUND_LIGHT)
            .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
            .onChange((value: string) => {
              this.contactInfo = value;
            })
        }
        .width('100%')
        .alignItems(HorizontalAlign.Start)

        // 提交按钮
        Button('提交反馈')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontColor(Constants.COLORS.WHITE)
          .backgroundColor(Constants.COLORS.PRIMARY)
          .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
          .width('100%')
          .height(48)
          .enabled(this.feedbackType !== '' && this.feedbackContent.trim() !== '')
          .opacity(this.feedbackType !== '' && this.feedbackContent.trim() !== '' ? 1 : 0.5)
          .onClick(() => {
            this.submitFeedback();
          })

        // 底部间距
        Column()
          .height(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE))
      }
      .padding(16)
    }
    .scrollable(ScrollDirection.Vertical)
    .scrollBar(BarState.Auto)
    .layoutWeight(1)
  }

  build() {
    Column() {
      // 标题栏
      Row() {
        Text('←')
          .fontSize(24)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .onClick(() => {
            this.getUIContext().getRouter().back();
          })

        Text('帮助与反馈')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .fontWeight(FontWeight.Bold)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('')
          .width(24)
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor(Constants.COLORS.WHITE)
      .alignItems(VerticalAlign.Center)

      if (this.loadingState === LoadingState.LOADING) {
        LoadingView({ state: LoadingState.LOADING })
          .layoutWeight(1)
      } else {
        Column() {
          // 标签栏
          this.TabBar()
          
          Divider()
            .color(Constants.COLORS.BORDER)

          // 内容区域
          if (this.selectedTab === 0) {
            this.HelpContent()
          } else {
            this.FeedbackContent()
          }
        }
        .layoutWeight(1)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Constants.COLORS.BACKGROUND)
  }
}

export { HelpPage };