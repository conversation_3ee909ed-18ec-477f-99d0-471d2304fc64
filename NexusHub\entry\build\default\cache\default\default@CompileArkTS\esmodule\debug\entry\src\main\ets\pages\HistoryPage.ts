if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface HistoryPage_Params {
    historyItems?: HistoryItem[];
    loadingState?: LoadingState;
    selectedPeriod?: string;
    periods?: string[];
    isEditMode?: boolean;
    selectedItems?: Set<number>;
    deviceUtils?;
    apiService?;
}
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { DeviceUtils } from "@normalized:N&&&entry/src/main/ets/utils/DeviceUtils&";
import { ApiService } from "@normalized:N&&&entry/src/main/ets/services/ApiService&";
import { LoadingView, LoadingState } from "@normalized:N&&&entry/src/main/ets/components/LoadingView&";
import type { AppModel } from '../models/App';
import hilog from "@ohos:hilog";
/**
 * 浏览历史项模型
 */
interface HistoryItem {
    app: AppModel;
    viewTime: string;
    viewCount: number;
}
class HistoryPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__historyItems = new ObservedPropertyObjectPU([], this, "historyItems");
        this.__loadingState = new ObservedPropertySimplePU(LoadingState.LOADING, this, "loadingState");
        this.__selectedPeriod = new ObservedPropertySimplePU('全部', this, "selectedPeriod");
        this.__periods = new ObservedPropertyObjectPU(['全部', '今天', '昨天', '本周', '本月'], this, "periods");
        this.__isEditMode = new ObservedPropertySimplePU(false, this, "isEditMode");
        this.__selectedItems = new ObservedPropertyObjectPU(new Set(), this, "selectedItems");
        this.deviceUtils = DeviceUtils.getInstance();
        this.apiService = ApiService.getInstance();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: HistoryPage_Params) {
        if (params.historyItems !== undefined) {
            this.historyItems = params.historyItems;
        }
        if (params.loadingState !== undefined) {
            this.loadingState = params.loadingState;
        }
        if (params.selectedPeriod !== undefined) {
            this.selectedPeriod = params.selectedPeriod;
        }
        if (params.periods !== undefined) {
            this.periods = params.periods;
        }
        if (params.isEditMode !== undefined) {
            this.isEditMode = params.isEditMode;
        }
        if (params.selectedItems !== undefined) {
            this.selectedItems = params.selectedItems;
        }
        if (params.deviceUtils !== undefined) {
            this.deviceUtils = params.deviceUtils;
        }
        if (params.apiService !== undefined) {
            this.apiService = params.apiService;
        }
    }
    updateStateVars(params: HistoryPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__historyItems.purgeDependencyOnElmtId(rmElmtId);
        this.__loadingState.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedPeriod.purgeDependencyOnElmtId(rmElmtId);
        this.__periods.purgeDependencyOnElmtId(rmElmtId);
        this.__isEditMode.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedItems.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__historyItems.aboutToBeDeleted();
        this.__loadingState.aboutToBeDeleted();
        this.__selectedPeriod.aboutToBeDeleted();
        this.__periods.aboutToBeDeleted();
        this.__isEditMode.aboutToBeDeleted();
        this.__selectedItems.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __historyItems: ObservedPropertyObjectPU<HistoryItem[]>;
    get historyItems() {
        return this.__historyItems.get();
    }
    set historyItems(newValue: HistoryItem[]) {
        this.__historyItems.set(newValue);
    }
    private __loadingState: ObservedPropertySimplePU<LoadingState>;
    get loadingState() {
        return this.__loadingState.get();
    }
    set loadingState(newValue: LoadingState) {
        this.__loadingState.set(newValue);
    }
    private __selectedPeriod: ObservedPropertySimplePU<string>;
    get selectedPeriod() {
        return this.__selectedPeriod.get();
    }
    set selectedPeriod(newValue: string) {
        this.__selectedPeriod.set(newValue);
    }
    private __periods: ObservedPropertyObjectPU<string[]>;
    get periods() {
        return this.__periods.get();
    }
    set periods(newValue: string[]) {
        this.__periods.set(newValue);
    }
    private __isEditMode: ObservedPropertySimplePU<boolean>;
    get isEditMode() {
        return this.__isEditMode.get();
    }
    set isEditMode(newValue: boolean) {
        this.__isEditMode.set(newValue);
    }
    private __selectedItems: ObservedPropertyObjectPU<Set<number>>;
    get selectedItems() {
        return this.__selectedItems.get();
    }
    set selectedItems(newValue: Set<number>) {
        this.__selectedItems.set(newValue);
    }
    private deviceUtils;
    private apiService;
    aboutToAppear() {
        this.loadBrowseHistory();
    }
    /**
     * 加载浏览历史
     */
    private async loadBrowseHistory() {
        try {
            this.loadingState = LoadingState.LOADING;
            // 这里应该从本地存储或API获取浏览历史
            // 暂时使用模拟数据
            await this.simulateApiCall();
            this.historyItems = this.getMockHistoryItems();
            this.loadingState = LoadingState.SUCCESS;
        }
        catch (error) {
            hilog.error(0x0000, 'HistoryPage', '加载浏览历史失败: %{public}s', JSON.stringify(error));
            this.loadingState = LoadingState.ERROR;
        }
    }
    /**
     * 模拟API调用
     */
    private async simulateApiCall(): Promise<void> {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve();
            }, 1000);
        });
    }
    /**
     * 获取模拟历史数据
     */
    private getMockHistoryItems(): HistoryItem[] {
        const mockApps: AppModel[] = [
            {
                id: 1,
                created_at: '2024-01-15',
                updated_at: '2024-01-15',
                name: '微信',
                package_name: 'com.tencent.mm',
                description: '一个为智能终端提供即时通讯服务的免费应用程序',
                short_description: '即时通讯服务',
                icon: Constants.PLACEHOLDER_IMAGE,
                category_id: 1,
                category_name: '社交',
                developer_id: 1,
                developer_name: '腾讯科技',
                version: '8.0.32',
                version_code: 1,
                min_sdk_version: 21,
                target_sdk_version: 33,
                size: 245760000,
                download_url: '',
                download_count: 1000000000,
                rating: 4.8,
                review_count: 500000,
                screenshots: [Constants.PLACEHOLDER_IMAGE],
                permissions: [],
                tags: ['社交', '通讯'],
                changelog: '优化用户体验',
                privacy_policy: 'https://example.com/privacy',
                support_email: '<EMAIL>',
                website: 'https://example.com',
                status: 'published',
                is_featured: true,
                is_editor_choice: true,
                is_top: true,
                published_at: '2024-01-15',
                review_status: 'approved',
                review_reason: '',
                reviewed_at: '2024-01-15',
                reviewer_id: 1
            },
            {
                id: 2,
                name: '支付宝',
                description: '数字生活开放平台',
                icon: Constants.PLACEHOLDER_IMAGE,
                version: '10.3.20',
                size: 156672000,
                rating: 4.7,
                download_count: 800000000,
                category_id: 2,
                category_name: '金融',
                screenshots: [Constants.PLACEHOLDER_IMAGE],
                developer_id: 2,
                developer_name: '蚂蚁集团',
                created_at: '2024-01-10',
                updated_at: '2024-01-10',
                download_url: '',
                package_name: 'com.eg.android.AlipayGphone',
                version_code: 1,
                min_sdk_version: 21,
                target_sdk_version: 33,
                review_count: 500000,
                changelog: '优化用户体验',
                short_description: '数字生活开放平台',
                privacy_policy: 'https://example.com/privacy',
                support_email: '<EMAIL>',
                website: 'https://example.com',
                status: 'published',
                is_featured: true,
                is_editor_choice: true,
                is_top: true,
                published_at: '2024-01-10',
                review_status: 'approved',
                review_reason: '',
                reviewed_at: '2024-01-10',
                reviewer_id: 1,
                permissions: [],
                tags: ['支付', '金融']
            },
            {
                id: 3,
                name: '王者荣耀',
                description: '5v5英雄公平对战手游',
                icon: Constants.PLACEHOLDER_IMAGE,
                version: '********',
                size: 1887436800,
                rating: 4.5,
                download_count: 500000000,
                category_id: 1,
                category_name: '游戏',
                screenshots: [Constants.PLACEHOLDER_IMAGE],
                developer_id: 1,
                developer_name: '腾讯游戏',
                created_at: '2024-01-12',
                updated_at: '2024-01-12',
                download_url: '',
                package_name: 'com.tencent.tmgp.sgame',
                version_code: 1,
                min_sdk_version: 21,
                target_sdk_version: 33,
                review_count: 1000000,
                changelog: '修复已知问题',
                short_description: '5v5英雄公平对战手游',
                privacy_policy: 'https://example.com/privacy',
                support_email: '<EMAIL>',
                website: 'https://example.com',
                status: 'published',
                is_featured: true,
                is_editor_choice: false,
                is_top: true,
                published_at: '2024-01-12',
                review_status: 'approved',
                review_reason: '',
                reviewed_at: '2024-01-12',
                reviewer_id: 1,
                permissions: [],
                tags: ['MOBA', '竞技']
            }
        ];
        return [
            {
                app: mockApps[0],
                viewTime: '2024-01-22 16:45:00',
                viewCount: 3
            },
            {
                app: mockApps[1],
                viewTime: '2024-01-22 14:20:00',
                viewCount: 1
            },
            {
                app: mockApps[2],
                viewTime: '2024-01-21 20:30:00',
                viewCount: 5
            }
        ];
    }
    /**
     * 获取过滤后的历史记录
     */
    private getFilteredHistory(): HistoryItem[] {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
        const weekStart = new Date(today.getTime() - (today.getDay() || 7) * 24 * 60 * 60 * 1000);
        const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
        return this.historyItems.filter(item => {
            const viewDate = new Date(item.viewTime);
            switch (this.selectedPeriod) {
                case '今天':
                    return viewDate >= today;
                case '昨天':
                    return viewDate >= yesterday && viewDate < today;
                case '本周':
                    return viewDate >= weekStart;
                case '本月':
                    return viewDate >= monthStart;
                default:
                    return true;
            }
        });
    }
    /**
     * 跳转到应用详情页面
     */
    private navigateToAppDetail(app: AppModel) {
        if (!this.isEditMode) {
            this.getUIContext().getRouter().pushUrl({
                url: 'pages/AppDetailPage',
                params: { appId: app.id.toString() }
            });
        }
    }
    /**
     * 切换编辑模式
     */
    private toggleEditMode() {
        this.isEditMode = !this.isEditMode;
        if (!this.isEditMode) {
            this.selectedItems.clear();
        }
    }
    /**
     * 选择/取消选择项目
     */
    private toggleSelectItem(appId: number) {
        if (this.selectedItems.has(appId)) {
            this.selectedItems.delete(appId);
        }
        else {
            this.selectedItems.add(appId);
        }
    }
    /**
     * 全选/取消全选
     */
    private toggleSelectAll() {
        const filteredHistory = this.getFilteredHistory();
        if (this.selectedItems.size === filteredHistory.length) {
            this.selectedItems.clear();
        }
        else {
            this.selectedItems.clear();
            filteredHistory.forEach(item => {
                this.selectedItems.add(item.app.id);
            });
        }
    }
    /**
     * 删除选中的历史记录
     */
    private async deleteSelectedItems() {
        try {
            // 这里应该调用API删除历史记录
            // 暂时直接从列表中移除
            this.historyItems = this.historyItems.filter(item => !this.selectedItems.has(item.app.id));
            this.selectedItems.clear();
            this.isEditMode = false;
        }
        catch (error) {
            hilog.error(0x0000, 'HistoryPage', '删除历史记录失败: %{public}s', JSON.stringify(error));
        }
    }
    /**
     * 清空所有历史记录
     */
    private async clearAllHistory() {
        try {
            // 这里应该调用API清空历史记录
            // 暂时直接清空列表
            this.historyItems = [];
            this.selectedItems.clear();
            this.isEditMode = false;
        }
        catch (error) {
            hilog.error(0x0000, 'HistoryPage', '清空历史记录失败: %{public}s', JSON.stringify(error));
        }
    }
    /**
     * 时间段标签栏
     */
    private PeriodTabs(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.scrollable(ScrollDirection.Horizontal);
            Scroll.scrollBar(BarState.Off);
            Scroll.width('100%');
            Scroll.backgroundColor(Constants.COLORS.WHITE);
            Scroll.padding({ top: 12, bottom: 12 });
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 8 });
            Row.padding({ left: 16, right: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const period = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(period);
                    Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                    Text.fontColor(this.selectedPeriod === period ? Constants.COLORS.WHITE : Constants.COLORS.TEXT_SECONDARY);
                    Text.backgroundColor(this.selectedPeriod === period ? Constants.COLORS.PRIMARY : { "id": 16777231, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                    Text.padding({ left: 12, right: 12, top: 6, bottom: 6 });
                    Text.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
                    Text.onClick(() => {
                        this.selectedPeriod = period;
                    });
                }, Text);
                Text.pop();
            };
            this.forEachUpdateFunction(elmtId, this.periods, forEachItemGenFunction, (period: string) => period, false, false);
        }, ForEach);
        ForEach.pop();
        Row.pop();
        Scroll.pop();
    }
    /**
     * 历史记录项
     */
    private HistoryItem(item: HistoryItem, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) });
            Row.width('100%');
            Row.padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM));
            Row.backgroundColor(Constants.COLORS.WHITE);
            Row.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            Row.margin({ left: 16, right: 16, bottom: 8 });
            Row.onClick(() => this.navigateToAppDetail(item.app));
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 编辑模式下的选择框
            if (this.isEditMode) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.selectedItems.has(item.app.id) ? '☑️' : '☐');
                        Text.fontSize(20);
                        Text.onClick(() => {
                            this.toggleSelectItem(item.app.id);
                        });
                    }, Text);
                    Text.pop();
                });
            }
            // 应用图标
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 应用图标
            Image.create(item.app.icon);
            // 应用图标
            Image.width(this.deviceUtils.isTablet() ? 64 : 56);
            // 应用图标
            Image.height(this.deviceUtils.isTablet() ? 64 : 56);
            // 应用图标
            Image.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            // 应用图标
            Image.objectFit(ImageFit.Cover);
            // 应用图标
            Image.backgroundColor(Constants.COLORS.BACKGROUND_LIGHT);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 应用信息
            Column.create({ space: 4 });
            // 应用信息
            Column.alignItems(HorizontalAlign.Start);
            // 应用信息
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(item.app.name);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.fontWeight(FontWeight.Medium);
            Text.maxLines(1);
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(item.app.category_name);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.PRIMARY);
            Text.backgroundColor({ "id": 16777232, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Text.padding({ left: 8, right: 8, top: 2, bottom: 2 });
            Text.borderRadius(Constants.BORDER_RADIUS.SMALL);
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(item.app.description);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
            Text.maxLines(1);
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 4 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('⭐');
            Text.fontSize(12);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create((item.app.rating || 0).toString());
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_HINT);
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`${((item.app.size || 0) / 1024 / 1024).toFixed(1)}MB`);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_HINT);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`浏览${item.viewCount}次`);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_HINT);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.formatViewTime(item.viewTime));
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_HINT);
        }, Text);
        Text.pop();
        Row.pop();
        // 应用信息
        Column.pop();
        Row.pop();
    }
    /**
     * 格式化浏览时间
     */
    private formatViewTime(viewTime: string): string {
        const now = new Date();
        const viewDate = new Date(viewTime);
        const diffMs = now.getTime() - viewDate.getTime();
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffHours / 24);
        if (diffHours < 1) {
            return '刚刚';
        }
        else if (diffHours < 24) {
            return `${diffHours}小时前`;
        }
        else if (diffDays < 7) {
            return `${diffDays}天前`;
        }
        else {
            return viewTime.split(' ')[0];
        }
    }
    /**
     * 历史记录列表
     */
    private HistoryList(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.getFilteredHistory().length === 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create({ space: 16 });
                        Column.width('100%');
                        Column.height(200);
                        Column.justifyContent(FlexAlign.Center);
                        Column.alignItems(HorizontalAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('🕒');
                        Text.fontSize(48);
                        Text.fontColor(Constants.COLORS.TEXT_HINT);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.selectedPeriod === '全部' ? '暂无浏览记录' : `${this.selectedPeriod}暂无浏览记录`);
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('去应用商店发现更多精彩应用');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                        Text.fontColor(Constants.COLORS.TEXT_HINT);
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.width('100%');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const item = _item;
                            this.HistoryItem.bind(this)(item);
                        };
                        this.forEachUpdateFunction(elmtId, this.getFilteredHistory(), forEachItemGenFunction, (item: HistoryItem) => item.app.id.toString(), false, false);
                    }, ForEach);
                    ForEach.pop();
                    Column.pop();
                });
            }
        }, If);
        If.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor(Constants.COLORS.BACKGROUND);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 标题栏
            Row.create();
            // 标题栏
            Row.width('100%');
            // 标题栏
            Row.height(56);
            // 标题栏
            Row.padding({ left: 16, right: 16 });
            // 标题栏
            Row.backgroundColor(Constants.COLORS.WHITE);
            // 标题栏
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('←');
            Text.fontSize(24);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.onClick(() => {
                this.getUIContext().getRouter().back();
            });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('浏览历史');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.fontWeight(FontWeight.Bold);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.historyItems.length > 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.isEditMode ? '完成' : '编辑');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Text.fontColor(Constants.COLORS.PRIMARY);
                        Text.onClick(() => {
                            this.toggleEditMode();
                        });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('');
                        Text.width(32);
                    }, Text);
                    Text.pop();
                });
            }
        }, If);
        If.pop();
        // 标题栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 编辑模式下的操作栏
            if (this.isEditMode && this.historyItems.length > 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.width('100%');
                        Row.padding({ left: 16, right: 16, top: 8, bottom: 8 });
                        Row.backgroundColor(Constants.COLORS.WHITE);
                        Row.border({ width: { top: 1 }, color: Constants.COLORS.BORDER });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.selectedItems.size === this.getFilteredHistory().length ? '取消全选' : '全选');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Text.fontColor(Constants.COLORS.PRIMARY);
                        Text.onClick(() => {
                            this.toggleSelectAll();
                        });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Blank.create();
                    }, Blank);
                    Blank.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create({ space: 16 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.selectedItems.size > 0) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create('删除选中');
                                    Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                                    Text.fontColor(Constants.COLORS.ERROR);
                                    Text.onClick(() => {
                                        this.deleteSelectedItems();
                                    });
                                }, Text);
                                Text.pop();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('清空全部');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Text.fontColor(Constants.COLORS.ERROR);
                        Text.onClick(() => {
                            this.clearAllHistory();
                        });
                    }, Text);
                    Text.pop();
                    Row.pop();
                    Row.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.loadingState === LoadingState.LOADING) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, { state: LoadingState.LOADING }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/HistoryPage.ets", line: 553, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.LOADING
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.LOADING
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else if (this.loadingState === LoadingState.ERROR) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, {
                                    state: LoadingState.ERROR,
                                    onRetry: (): Promise<void> => this.loadBrowseHistory()
                                }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/HistoryPage.ets", line: 556, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.ERROR,
                                        onRetry: (): Promise<void> => this.loadBrowseHistory()
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.ERROR
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.layoutWeight(1);
                    }, Column);
                    // 时间段标签栏
                    this.PeriodTabs.bind(this)();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Divider.create();
                        Divider.color(Constants.COLORS.BORDER);
                    }, Divider);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 历史记录列表
                        Scroll.create();
                        // 历史记录列表
                        Scroll.scrollable(ScrollDirection.Vertical);
                        // 历史记录列表
                        Scroll.scrollBar(BarState.Auto);
                        // 历史记录列表
                        Scroll.layoutWeight(1);
                    }, Scroll);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                    }, Column);
                    this.HistoryList.bind(this)();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 底部间距
                        Column.create();
                        // 底部间距
                        Column.height(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE));
                    }, Column);
                    // 底部间距
                    Column.pop();
                    Column.pop();
                    // 历史记录列表
                    Scroll.pop();
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "HistoryPage";
    }
}
export { HistoryPage };
registerNamedRoute(() => new HistoryPage(undefined, {}), "", { bundleName: "com.example.nexushub", moduleName: "entry", pagePath: "pages/HistoryPage", pageFullPath: "entry/src/main/ets/pages/HistoryPage", integratedHsp: "false", moduleType: "followWithHap" });
