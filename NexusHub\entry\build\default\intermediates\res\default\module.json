{"app": {"bundleName": "com.example.nexushub", "vendor": "example", "versionCode": 1000000, "versionName": "1.0.0", "icon": "$media:layered_image", "label": "$string:app_name", "apiReleaseType": "Release", "compileSdkVersion": "*********", "targetAPIVersion": 18, "minAPIVersion": 12, "compileSdkType": "OpenHarmony", "appEnvironments": [], "bundleType": "app", "buildMode": "debug", "debug": true, "iconId": 16777219, "labelId": 16777216}, "module": {"name": "entry", "type": "entry", "description": "$string:module_desc", "mainElement": "EntryAbility", "deviceTypes": ["default", "tablet"], "deliveryWithInstall": true, "installationFree": false, "pages": "$profile:main_pages", "abilities": [{"name": "EntryAbility", "srcEntry": "./ets/entryability/EntryAbility.ets", "description": "$string:EntryAbility_desc", "icon": "$media:layered_image", "label": "$string:EntryAbility_label", "startWindowIcon": "$media:startIcon", "startWindowBackground": "$color:start_window_background", "exported": true, "skills": [{"entities": ["entity.system.home"], "actions": ["action.system.home"]}], "descriptionId": 16777221, "iconId": 16777219, "labelId": 16777222, "startWindowIconId": 16777279, "startWindowBackgroundId": 16777235}], "extensionAbilities": [{"name": "EntryBackupAbility", "srcEntry": "./ets/entrybackupability/EntryBackupAbility.ets", "type": "backup", "exported": false, "metadata": [{"name": "ohos.extension.backup", "resource": "$profile:backup_config", "resourceId": 16777280}]}], "requestPermissions": [{"name": "ohos.permission.INTERNET", "reason": "$string:permission_internet_reason", "usedScene": {"abilities": ["EntryAbility"], "when": "inuse"}, "reasonId": 16777224}, {"name": "ohos.permission.GET_NETWORK_INFO"}], "metadata": [{"name": "network_security_config", "resource": "$profile:network_security_config", "resourceId": 16777220}], "packageName": "entry", "virtualMachine": "ark12.0.2.0", "compileMode": "esmodule", "dependencies": [], "descriptionId": 16777223}}