package models

import (
	"time"

	"gorm.io/gorm"
)

// LoginRecord 登录记录模型
type LoginRecord struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
	UserID    uint           `gorm:"not null;index" json:"user_id"`
	IP        string         `gorm:"type:varchar(45)" json:"ip"`
	UserAgent string         `gorm:"type:text" json:"user_agent"`
	Location  string         `gorm:"type:varchar(100)" json:"location"`
	LoginTime time.Time      `gorm:"not null" json:"login_time"`
	Status    string         `gorm:"type:varchar(20);default:'success'" json:"status"` // success, failed
	Remark    string         `gorm:"type:varchar(255)" json:"remark"`

	// 关联用户
	User User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// TableName 指定表名
func (LoginRecord) TableName() string {
	return "login_records"
}

// CreateLoginRecord 创建登录记录
func CreateLoginRecord(db *gorm.DB, record *LoginRecord) error {
	return db.Create(record).Error
}

// GetUserLoginRecords 获取用户登录记录
func GetUserLoginRecords(db *gorm.DB, userID uint, page, pageSize int) ([]LoginRecord, int64, error) {
	var records []LoginRecord
	var count int64

	offset := (page - 1) * pageSize

	query := db.Model(&LoginRecord{}).Where("user_id = ?", userID)

	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	if err := query.Order("login_time desc").Offset(offset).Limit(pageSize).Find(&records).Error; err != nil {
		return nil, 0, err
	}

	return records, count, nil
}
