{"version": "2.0", "ppid": 32784, "events": [{"head": {"id": "eed1ce01-5c04-4449-a6e6-b3e64b31eb62", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277979242100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74b8a8ff-b94a-46c8-b3c3-e13c6e796b69", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277980115400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4deb0d8b-d26a-4e02-9426-f5bc2bc9f5d9", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277981492700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42779840-ced3-4208-9fd2-f002231a0bb1", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277982685700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f7f0eac-ef5e-419e-969e-cccbebdb0514", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277983640800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69e1780c-0777-41fc-ba34-472697f09e4e", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277984903800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d485e81d-7bea-4534-bb5b-baaecc20ce6a", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277985344500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68772ec6-ffca-45ad-836c-bc2925f80b4c", "name": "worker[18] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277987156100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "772da3cb-e623-4717-a46c-6a255512cd69", "name": "worker[4] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221278075077100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f1135d7-9e1b-4f29-afa6-e053fdd850b5", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820322784300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5984e52-d3dd-42e8-9b23-5b258d613907", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820380295100, "endTime": 222820712322300}, "additional": {"children": ["8bd7ca25-6541-45aa-8dc0-d7880b588202", "075d1d07-4ab4-4fc7-98d1-824898ffa1a7", "640e7b4a-5091-4b5a-9879-a66a85435a12", "548549bd-2c00-48b1-b9f4-f5501ff676b0", "b677ffb2-71b4-4980-8c55-842922b159d6", "96c7d609-6d6f-42fd-9219-0b3ac0432d38", "99eb62f3-89c4-4bfe-b782-4c87e1af27e1"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "a78c2c11-ce52-4252-87b3-f93d1ed7eee0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8bd7ca25-6541-45aa-8dc0-d7880b588202", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820380304000, "endTime": 222820416420900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b5984e52-d3dd-42e8-9b23-5b258d613907", "logId": "d281adc6-dcb6-420e-9f11-6f7a39272684"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "075d1d07-4ab4-4fc7-98d1-824898ffa1a7", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820416442500, "endTime": 222820710741800}, "additional": {"children": ["4b76df40-6256-4670-81b6-d5d859c19613", "cc7e338d-1ffa-4dae-9f93-7014818cbf9a", "f32d942a-55ec-499d-a789-4acf2e9abb63", "1ec84139-d7ed-4838-a691-bc206136fbfb", "69d20ddf-01e3-4837-91fe-be9f445e1e57", "46551d1f-4a6b-4198-9a70-4c39decab738", "ee16e141-2b02-44f3-b212-d9dfa14d63a0", "dd0ebb1d-2280-47b4-b5d7-3c4e6fd7f1bd", "5cdeeecb-105a-4e1e-a0ed-451a6fb494d3"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b5984e52-d3dd-42e8-9b23-5b258d613907", "logId": "afb21d9b-4955-4d41-a7ea-6f8e6a65159d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "640e7b4a-5091-4b5a-9879-a66a85435a12", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820710770200, "endTime": 222820712283400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b5984e52-d3dd-42e8-9b23-5b258d613907", "logId": "7b64a40c-84f6-4afc-aeb9-1ee5a6a18ab4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "548549bd-2c00-48b1-b9f4-f5501ff676b0", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820712289900, "endTime": 222820712317900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b5984e52-d3dd-42e8-9b23-5b258d613907", "logId": "9a3843f9-90db-4755-be44-a33c6bc903ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b677ffb2-71b4-4980-8c55-842922b159d6", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820391765000, "endTime": 222820392426300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b5984e52-d3dd-42e8-9b23-5b258d613907", "logId": "f4b98fdd-3f59-4879-b64e-45e015ddbabb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4b98fdd-3f59-4879-b64e-45e015ddbabb", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820391765000, "endTime": 222820392426300}, "additional": {"logType": "info", "children": [], "durationId": "b677ffb2-71b4-4980-8c55-842922b159d6", "parent": "a78c2c11-ce52-4252-87b3-f93d1ed7eee0"}}, {"head": {"id": "96c7d609-6d6f-42fd-9219-0b3ac0432d38", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820403274800, "endTime": 222820403300000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b5984e52-d3dd-42e8-9b23-5b258d613907", "logId": "35bf2ab4-005b-47f8-9d66-41484e24413d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35bf2ab4-005b-47f8-9d66-41484e24413d", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820403274800, "endTime": 222820403300000}, "additional": {"logType": "info", "children": [], "durationId": "96c7d609-6d6f-42fd-9219-0b3ac0432d38", "parent": "a78c2c11-ce52-4252-87b3-f93d1ed7eee0"}}, {"head": {"id": "5309ba4c-29ef-4efd-b6ea-565be8889e49", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820404205700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c591076-de2f-4240-9df1-50f23240ef9d", "name": "Cache service initialization finished in 12 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820416250900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d281adc6-dcb6-420e-9f11-6f7a39272684", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820380304000, "endTime": 222820416420900}, "additional": {"logType": "info", "children": [], "durationId": "8bd7ca25-6541-45aa-8dc0-d7880b588202", "parent": "a78c2c11-ce52-4252-87b3-f93d1ed7eee0"}}, {"head": {"id": "4b76df40-6256-4670-81b6-d5d859c19613", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820422912500, "endTime": 222820422930400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "075d1d07-4ab4-4fc7-98d1-824898ffa1a7", "logId": "35e40d25-67d6-47e2-9f53-2c5aff3db690"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc7e338d-1ffa-4dae-9f93-7014818cbf9a", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820422954400, "endTime": 222820431623200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "075d1d07-4ab4-4fc7-98d1-824898ffa1a7", "logId": "12d13485-2ef7-4cd3-95bc-716ff067e0df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f32d942a-55ec-499d-a789-4acf2e9abb63", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820431658000, "endTime": 222820574983300}, "additional": {"children": ["25252b73-1f9d-4bdb-a1e1-d68f60a496a6", "2907c56e-02d5-4e6d-9fe5-580c8ecd87a7", "cd217c54-a129-4b06-992d-19164b1fd382"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "075d1d07-4ab4-4fc7-98d1-824898ffa1a7", "logId": "67da4863-27ac-4896-bc75-20385ddca40c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ec84139-d7ed-4838-a691-bc206136fbfb", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820574998900, "endTime": 222820609257300}, "additional": {"children": ["15fa9cbb-0608-4a3f-910a-65ed06a0ad1c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "075d1d07-4ab4-4fc7-98d1-824898ffa1a7", "logId": "f71739ca-da6c-421e-b190-0d889e80d138"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69d20ddf-01e3-4837-91fe-be9f445e1e57", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820609264000, "endTime": 222820674863100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "075d1d07-4ab4-4fc7-98d1-824898ffa1a7", "logId": "2374cd23-35bf-4a07-bd7a-32223d212677"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "46551d1f-4a6b-4198-9a70-4c39decab738", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820676122600, "endTime": 222820687526800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "075d1d07-4ab4-4fc7-98d1-824898ffa1a7", "logId": "0186d683-5b6d-4212-92d0-2b613eb3aa59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ee16e141-2b02-44f3-b212-d9dfa14d63a0", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820687556400, "endTime": 222820710498100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "075d1d07-4ab4-4fc7-98d1-824898ffa1a7", "logId": "ad37b45a-a0d7-4518-832a-85d44a149d92"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd0ebb1d-2280-47b4-b5d7-3c4e6fd7f1bd", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820710543800, "endTime": 222820710725100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "075d1d07-4ab4-4fc7-98d1-824898ffa1a7", "logId": "3d57cfaf-d131-41c5-bb7d-d7fab04aede1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35e40d25-67d6-47e2-9f53-2c5aff3db690", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820422912500, "endTime": 222820422930400}, "additional": {"logType": "info", "children": [], "durationId": "4b76df40-6256-4670-81b6-d5d859c19613", "parent": "afb21d9b-4955-4d41-a7ea-6f8e6a65159d"}}, {"head": {"id": "12d13485-2ef7-4cd3-95bc-716ff067e0df", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820422954400, "endTime": 222820431623200}, "additional": {"logType": "info", "children": [], "durationId": "cc7e338d-1ffa-4dae-9f93-7014818cbf9a", "parent": "afb21d9b-4955-4d41-a7ea-6f8e6a65159d"}}, {"head": {"id": "25252b73-1f9d-4bdb-a1e1-d68f60a496a6", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820432464200, "endTime": 222820432486700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f32d942a-55ec-499d-a789-4acf2e9abb63", "logId": "948b2be5-3779-4cd7-a8fa-03e8fa4d09b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "948b2be5-3779-4cd7-a8fa-03e8fa4d09b3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820432464200, "endTime": 222820432486700}, "additional": {"logType": "info", "children": [], "durationId": "25252b73-1f9d-4bdb-a1e1-d68f60a496a6", "parent": "67da4863-27ac-4896-bc75-20385ddca40c"}}, {"head": {"id": "2907c56e-02d5-4e6d-9fe5-580c8ecd87a7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820434762300, "endTime": 222820574231200}, "additional": {"children": ["4ea8dd90-b152-4a48-b4a4-975a345d779c", "d4ea0d1d-174a-4f49-90fc-091343a0b62b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f32d942a-55ec-499d-a789-4acf2e9abb63", "logId": "a8c92656-fbc0-4e5c-aadf-fd75ff3d6c38"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ea8dd90-b152-4a48-b4a4-975a345d779c", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820434763400, "endTime": 222820467039100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2907c56e-02d5-4e6d-9fe5-580c8ecd87a7", "logId": "22d1230f-1e5c-440d-9b0c-3593be700a24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d4ea0d1d-174a-4f49-90fc-091343a0b62b", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820467066100, "endTime": 222820574216600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2907c56e-02d5-4e6d-9fe5-580c8ecd87a7", "logId": "a30ac915-8ff4-4037-a9d7-3ea2aca8d094"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fd8abae5-7fed-4ca4-bd1f-20ac28ac25c2", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820434770400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48bc7695-b241-457a-9965-6f04bbd42b93", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820466769100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22d1230f-1e5c-440d-9b0c-3593be700a24", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820434763400, "endTime": 222820467039100}, "additional": {"logType": "info", "children": [], "durationId": "4ea8dd90-b152-4a48-b4a4-975a345d779c", "parent": "a8c92656-fbc0-4e5c-aadf-fd75ff3d6c38"}}, {"head": {"id": "d4fd83b3-2e86-46b2-b5fb-490f79c83827", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820467086800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3361722-01ff-4608-9fb4-e7d353936a1b", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820487898000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "353158f7-f3bc-43c6-8633-61d22c2e7ae7", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820488108200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19558fa7-a7e0-419b-bffa-f1f0789c2332", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820488287300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6e57c1e-0eb3-498d-9b83-590cdeb2deea", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820488475300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98c96263-9eea-4e79-bdf3-c3ed2ca3051a", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820493443700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d731c22f-04c3-4198-a39b-32b92b851dcc", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820517314800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5d9587f-d10f-4f1a-9b11-f2633da6486a", "name": "Sdk init in 42 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820546499400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ff07ed7-0580-4b2e-8a29-c0d4db0f3215", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820546773500}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 19, "minute": 50, "second": 50}, "markType": "other"}}, {"head": {"id": "34e2a84e-f5c0-44e6-b718-bd562121b12f", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820546822300}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 19, "minute": 50, "second": 50}, "markType": "other"}}, {"head": {"id": "90812720-3b26-4917-b0dc-81d51dbf37b7", "name": "Project task initialization takes 25 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820573799300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2e707c5-2c4e-4425-ac63-aa9224afe98d", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820574013900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dbf1e1a-2e30-43d7-9cb6-2959cbf74756", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820574084400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "271ae8ee-025d-4f54-a753-ef40538e13b7", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820574144800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a30ac915-8ff4-4037-a9d7-3ea2aca8d094", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820467066100, "endTime": 222820574216600}, "additional": {"logType": "info", "children": [], "durationId": "d4ea0d1d-174a-4f49-90fc-091343a0b62b", "parent": "a8c92656-fbc0-4e5c-aadf-fd75ff3d6c38"}}, {"head": {"id": "a8c92656-fbc0-4e5c-aadf-fd75ff3d6c38", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820434762300, "endTime": 222820574231200}, "additional": {"logType": "info", "children": ["22d1230f-1e5c-440d-9b0c-3593be700a24", "a30ac915-8ff4-4037-a9d7-3ea2aca8d094"], "durationId": "2907c56e-02d5-4e6d-9fe5-580c8ecd87a7", "parent": "67da4863-27ac-4896-bc75-20385ddca40c"}}, {"head": {"id": "cd217c54-a129-4b06-992d-19164b1fd382", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820574944100, "endTime": 222820574964000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f32d942a-55ec-499d-a789-4acf2e9abb63", "logId": "46fc3e68-f9ce-465d-b891-8aa792cfe1a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "46fc3e68-f9ce-465d-b891-8aa792cfe1a5", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820574944100, "endTime": 222820574964000}, "additional": {"logType": "info", "children": [], "durationId": "cd217c54-a129-4b06-992d-19164b1fd382", "parent": "67da4863-27ac-4896-bc75-20385ddca40c"}}, {"head": {"id": "67da4863-27ac-4896-bc75-20385ddca40c", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820431658000, "endTime": 222820574983300}, "additional": {"logType": "info", "children": ["948b2be5-3779-4cd7-a8fa-03e8fa4d09b3", "a8c92656-fbc0-4e5c-aadf-fd75ff3d6c38", "46fc3e68-f9ce-465d-b891-8aa792cfe1a5"], "durationId": "f32d942a-55ec-499d-a789-4acf2e9abb63", "parent": "afb21d9b-4955-4d41-a7ea-6f8e6a65159d"}}, {"head": {"id": "15fa9cbb-0608-4a3f-910a-65ed06a0ad1c", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820575666300, "endTime": 222820609243400}, "additional": {"children": ["61596b79-9881-4926-b525-82c4a4e97353", "6d33bbab-3d27-47a6-bddf-07d6bb92250e", "ffb1b6e6-b518-4415-aba8-1be52b8471e4"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1ec84139-d7ed-4838-a691-bc206136fbfb", "logId": "b68b0f2f-4e59-4324-b2d0-6e6b21d23670"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61596b79-9881-4926-b525-82c4a4e97353", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820579078000, "endTime": 222820579099500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "15fa9cbb-0608-4a3f-910a-65ed06a0ad1c", "logId": "4f6e53f3-63c7-42a4-8ddd-8e6a096b915c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f6e53f3-63c7-42a4-8ddd-8e6a096b915c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820579078000, "endTime": 222820579099500}, "additional": {"logType": "info", "children": [], "durationId": "61596b79-9881-4926-b525-82c4a4e97353", "parent": "b68b0f2f-4e59-4324-b2d0-6e6b21d23670"}}, {"head": {"id": "6d33bbab-3d27-47a6-bddf-07d6bb92250e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820582747300, "endTime": 222820607765200}, "additional": {"children": ["ef484757-5196-489f-a1e8-0ffe583145cd", "ce5ce500-0d18-4305-8649-12baf9e29b83"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "15fa9cbb-0608-4a3f-910a-65ed06a0ad1c", "logId": "a51d46f0-fe7a-457a-b9c4-467daff21b7b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef484757-5196-489f-a1e8-0ffe583145cd", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820582748500, "endTime": 222820589220400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6d33bbab-3d27-47a6-bddf-07d6bb92250e", "logId": "e2fbf1d1-4793-45b5-9a5c-68a1bf2580b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce5ce500-0d18-4305-8649-12baf9e29b83", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820589246200, "endTime": 222820607753100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6d33bbab-3d27-47a6-bddf-07d6bb92250e", "logId": "1b6a506a-738c-4e52-8fb6-b6e7c02009b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "84c31c93-fa81-424e-8645-7241f1580a4a", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820582758100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72e7121b-9c8b-4bd5-9001-49ee1cfe2c97", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820588928800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2fbf1d1-4793-45b5-9a5c-68a1bf2580b5", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820582748500, "endTime": 222820589220400}, "additional": {"logType": "info", "children": [], "durationId": "ef484757-5196-489f-a1e8-0ffe583145cd", "parent": "a51d46f0-fe7a-457a-b9c4-467daff21b7b"}}, {"head": {"id": "71279b88-2137-4384-ad99-a6f7d01f8615", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820589267000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46c1223a-5665-4e35-bd9d-583e6b2f20cb", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820597783500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80b218b2-4ecf-4dc0-a169-9c229e721287", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820597977300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74362403-1bc9-45ea-8754-d90dc71a20c8", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820599152500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b007b330-98f5-4050-9159-8b38d8ed3fec", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820599398900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9306eb1-3bca-4fcf-b7b2-4c7379616b93", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820599538100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9342d58f-542e-4dec-88fd-d0788e90c5e4", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820599594800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fdc564f-88d9-473e-a66d-69b91dc8d3ca", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820599653700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95725376-704f-4b9b-98d0-1d263b317572", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820599729500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17b687af-e1f1-4b25-b774-3f2a2922cd24", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820599942400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bffc680-6ae3-4ce0-bdc2-c99cb4a0d3eb", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820600033800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfe2872a-927c-4502-96f2-99c21fb6c724", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820600084200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88b1a8e9-4c1f-4e54-9216-e42ca229d98b", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820600123600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d75f2e5-5800-46c2-a5a3-3a6d1ffdca82", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820600170400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0ccb4ee-3242-40a6-827a-a794440c90c6", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820600205600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a15f9ca-db0f-45a9-bb73-1aa5b6709930", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820600299200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcc97c03-8a4f-4231-a463-167d2492a7dd", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820600364600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be2b6b26-9b29-45af-823e-625aae8d8890", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820600396700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1dc1853-b575-41b8-bb2c-c059f4760d91", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820600428800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cde3618-dd3f-4d75-9673-8e4c7d2d4741", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820600553000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "802d37d9-2ce6-4276-a413-f202c8b5c4d7", "name": "Module entry task initialization takes 5 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820607322700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d958838-c132-4268-a6c3-2ea26e9af796", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820607549900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "715641a1-c12d-432a-9151-8e848ae43c5c", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820607609900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64f16038-7120-42cb-8966-600da490342d", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820607709600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b6a506a-738c-4e52-8fb6-b6e7c02009b4", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820589246200, "endTime": 222820607753100}, "additional": {"logType": "info", "children": [], "durationId": "ce5ce500-0d18-4305-8649-12baf9e29b83", "parent": "a51d46f0-fe7a-457a-b9c4-467daff21b7b"}}, {"head": {"id": "a51d46f0-fe7a-457a-b9c4-467daff21b7b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820582747300, "endTime": 222820607765200}, "additional": {"logType": "info", "children": ["e2fbf1d1-4793-45b5-9a5c-68a1bf2580b5", "1b6a506a-738c-4e52-8fb6-b6e7c02009b4"], "durationId": "6d33bbab-3d27-47a6-bddf-07d6bb92250e", "parent": "b68b0f2f-4e59-4324-b2d0-6e6b21d23670"}}, {"head": {"id": "ffb1b6e6-b518-4415-aba8-1be52b8471e4", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820609210200, "endTime": 222820609226100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "15fa9cbb-0608-4a3f-910a-65ed06a0ad1c", "logId": "7c21ed58-65df-45e6-981f-6d203560f644"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c21ed58-65df-45e6-981f-6d203560f644", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820609210200, "endTime": 222820609226100}, "additional": {"logType": "info", "children": [], "durationId": "ffb1b6e6-b518-4415-aba8-1be52b8471e4", "parent": "b68b0f2f-4e59-4324-b2d0-6e6b21d23670"}}, {"head": {"id": "b68b0f2f-4e59-4324-b2d0-6e6b21d23670", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820575666300, "endTime": 222820609243400}, "additional": {"logType": "info", "children": ["4f6e53f3-63c7-42a4-8ddd-8e6a096b915c", "a51d46f0-fe7a-457a-b9c4-467daff21b7b", "7c21ed58-65df-45e6-981f-6d203560f644"], "durationId": "15fa9cbb-0608-4a3f-910a-65ed06a0ad1c", "parent": "f71739ca-da6c-421e-b190-0d889e80d138"}}, {"head": {"id": "f71739ca-da6c-421e-b190-0d889e80d138", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820574998900, "endTime": 222820609257300}, "additional": {"logType": "info", "children": ["b68b0f2f-4e59-4324-b2d0-6e6b21d23670"], "durationId": "1ec84139-d7ed-4838-a691-bc206136fbfb", "parent": "afb21d9b-4955-4d41-a7ea-6f8e6a65159d"}}, {"head": {"id": "be8512a0-b98e-4404-ad47-f8bd16ad3a2a", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820634783500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a549269-3673-448f-be91-dfb712d89401", "name": "hvigorfile, resolve hvigorfile dependencies in 65 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820674645500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2374cd23-35bf-4a07-bd7a-32223d212677", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820609264000, "endTime": 222820674863100}, "additional": {"logType": "info", "children": [], "durationId": "69d20ddf-01e3-4837-91fe-be9f445e1e57", "parent": "afb21d9b-4955-4d41-a7ea-6f8e6a65159d"}}, {"head": {"id": "5cdeeecb-105a-4e1e-a0ed-451a6fb494d3", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820675875800, "endTime": 222820676109200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "075d1d07-4ab4-4fc7-98d1-824898ffa1a7", "logId": "ac0d5e6f-1272-4658-9a0c-a6514737d5e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "02569f3f-17f2-4a63-af7b-92407e1b00c5", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820675912600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac0d5e6f-1272-4658-9a0c-a6514737d5e1", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820675875800, "endTime": 222820676109200}, "additional": {"logType": "info", "children": [], "durationId": "5cdeeecb-105a-4e1e-a0ed-451a6fb494d3", "parent": "afb21d9b-4955-4d41-a7ea-6f8e6a65159d"}}, {"head": {"id": "f4b3e765-00ab-4d3e-bfa9-e6440b9bb6ab", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820677979200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28ba0df7-05a2-4b0a-87d8-f3f74dfce71f", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820686518700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0186d683-5b6d-4212-92d0-2b613eb3aa59", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820676122600, "endTime": 222820687526800}, "additional": {"logType": "info", "children": [], "durationId": "46551d1f-4a6b-4198-9a70-4c39decab738", "parent": "afb21d9b-4955-4d41-a7ea-6f8e6a65159d"}}, {"head": {"id": "5eccd1ef-8990-414f-bcc0-fe8c1b3f0b4b", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820687593700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed9302be-dca7-4e48-9f93-ef762742418f", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820700474000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2d2a97a-3e82-48a4-8540-b29bc442e74e", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820700632500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0814e5fd-ca35-47bf-b82d-b167f63a0259", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820700898900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f2722d7-d562-4696-a3e9-7f5339dac392", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820704302400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5be0ed60-a184-4279-b83a-85e4dc19b72d", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820704440900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad37b45a-a0d7-4518-832a-85d44a149d92", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820687556400, "endTime": 222820710498100}, "additional": {"logType": "info", "children": [], "durationId": "ee16e141-2b02-44f3-b212-d9dfa14d63a0", "parent": "afb21d9b-4955-4d41-a7ea-6f8e6a65159d"}}, {"head": {"id": "4921191a-ed33-4628-be03-2899d69c84f3", "name": "Configuration phase cost:288 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820710577900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d57cfaf-d131-41c5-bb7d-d7fab04aede1", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820710543800, "endTime": 222820710725100}, "additional": {"logType": "info", "children": [], "durationId": "dd0ebb1d-2280-47b4-b5d7-3c4e6fd7f1bd", "parent": "afb21d9b-4955-4d41-a7ea-6f8e6a65159d"}}, {"head": {"id": "afb21d9b-4955-4d41-a7ea-6f8e6a65159d", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820416442500, "endTime": 222820710741800}, "additional": {"logType": "info", "children": ["35e40d25-67d6-47e2-9f53-2c5aff3db690", "12d13485-2ef7-4cd3-95bc-716ff067e0df", "67da4863-27ac-4896-bc75-20385ddca40c", "f71739ca-da6c-421e-b190-0d889e80d138", "2374cd23-35bf-4a07-bd7a-32223d212677", "0186d683-5b6d-4212-92d0-2b613eb3aa59", "ad37b45a-a0d7-4518-832a-85d44a149d92", "3d57cfaf-d131-41c5-bb7d-d7fab04aede1", "ac0d5e6f-1272-4658-9a0c-a6514737d5e1"], "durationId": "075d1d07-4ab4-4fc7-98d1-824898ffa1a7", "parent": "a78c2c11-ce52-4252-87b3-f93d1ed7eee0"}}, {"head": {"id": "99eb62f3-89c4-4bfe-b782-4c87e1af27e1", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820712246200, "endTime": 222820712266700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b5984e52-d3dd-42e8-9b23-5b258d613907", "logId": "bca98325-358a-41e3-b2a1-1511ec6905b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bca98325-358a-41e3-b2a1-1511ec6905b2", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820712246200, "endTime": 222820712266700}, "additional": {"logType": "info", "children": [], "durationId": "99eb62f3-89c4-4bfe-b782-4c87e1af27e1", "parent": "a78c2c11-ce52-4252-87b3-f93d1ed7eee0"}}, {"head": {"id": "7b64a40c-84f6-4afc-aeb9-1ee5a6a18ab4", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820710770200, "endTime": 222820712283400}, "additional": {"logType": "info", "children": [], "durationId": "640e7b4a-5091-4b5a-9879-a66a85435a12", "parent": "a78c2c11-ce52-4252-87b3-f93d1ed7eee0"}}, {"head": {"id": "9a3843f9-90db-4755-be44-a33c6bc903ee", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820712289900, "endTime": 222820712317900}, "additional": {"logType": "info", "children": [], "durationId": "548549bd-2c00-48b1-b9f4-f5501ff676b0", "parent": "a78c2c11-ce52-4252-87b3-f93d1ed7eee0"}}, {"head": {"id": "a78c2c11-ce52-4252-87b3-f93d1ed7eee0", "name": "init", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820380295100, "endTime": 222820712322300}, "additional": {"logType": "info", "children": ["d281adc6-dcb6-420e-9f11-6f7a39272684", "afb21d9b-4955-4d41-a7ea-6f8e6a65159d", "7b64a40c-84f6-4afc-aeb9-1ee5a6a18ab4", "9a3843f9-90db-4755-be44-a33c6bc903ee", "f4b98fdd-3f59-4879-b64e-45e015ddbabb", "35bf2ab4-005b-47f8-9d66-41484e24413d", "bca98325-358a-41e3-b2a1-1511ec6905b2"], "durationId": "b5984e52-d3dd-42e8-9b23-5b258d613907"}}, {"head": {"id": "8a802e51-14bb-42c9-9fe1-2e49ff7bd8bf", "name": "Configuration task cost before running: 338 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820712603900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea2262d0-4d89-4af8-b892-b7f9105a1993", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820726620900, "endTime": 222820744478500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "0473c7dd-bb3a-4c94-9ccf-a121852bd85c", "logId": "21d7ae6f-3399-429e-b431-a7c97b2d8c86"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0473c7dd-bb3a-4c94-9ccf-a121852bd85c", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820714777800}, "additional": {"logType": "detail", "children": [], "durationId": "ea2262d0-4d89-4af8-b892-b7f9105a1993"}}, {"head": {"id": "f548bab6-5a58-4608-b518-7539edc54da9", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820716073400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd5b67ac-edf5-4bc7-acfb-8a1a7bb10fe6", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820716289400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "261e6340-9ce4-4a30-97d1-3e293a2e114c", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820717435000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8fd4662-2de1-4781-9c06-b50d34fbf95f", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820718934700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0ddddb4-1016-4fc8-9666-3bb09a9fd8b9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820720244000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "704815b5-3b10-410c-8a94-b6c3a692746f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820720357700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "448c863e-e380-449a-87cc-ca0f441c1e84", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820726662300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cb53a45-072a-4e9f-8550-405235be6a9e", "name": "Incremental task entry:default@PreBuild pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820744194200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3504ffa0-b03b-461c-9302-c5d04f8443fa", "name": "entry : default@PreBuild cost memory 0.32163238525390625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820744397400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21d7ae6f-3399-429e-b431-a7c97b2d8c86", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820726620900, "endTime": 222820744478500}, "additional": {"logType": "info", "children": [], "durationId": "ea2262d0-4d89-4af8-b892-b7f9105a1993"}}, {"head": {"id": "162df86d-fef6-4487-9cde-1dabf34be50f", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820754594900, "endTime": 222820758196600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "bdc30b5a-8c18-4d30-9bde-89e5cb675a73", "logId": "ead0b23f-8ec0-4d7f-ab7f-28cd5d20f27c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bdc30b5a-8c18-4d30-9bde-89e5cb675a73", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820752021000}, "additional": {"logType": "detail", "children": [], "durationId": "162df86d-fef6-4487-9cde-1dabf34be50f"}}, {"head": {"id": "5421fa75-6d67-4872-9b0d-46d2cd1bca8f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820753510900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc4f55cc-0bda-473d-8d96-8c5b1f9a57d3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820753663500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10436ab5-529c-4c94-a1c0-604d89da062c", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820754610500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15c47ef9-ab8b-4721-8ab2-5a3c71a306cf", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820756166100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11fadde8-b567-4a0f-bde5-4e26c21aca51", "name": "entry : default@CreateModuleInfo cost memory 0.06137847900390625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820757855400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da33719a-8695-426d-8e2e-144a65b2092c", "name": "runTaskFromQueue task cost before running: 383 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820758056100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ead0b23f-8ec0-4d7f-ab7f-28cd5d20f27c", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820754594900, "endTime": 222820758196600, "totalTime": 3422100}, "additional": {"logType": "info", "children": [], "durationId": "162df86d-fef6-4487-9cde-1dabf34be50f"}}, {"head": {"id": "f33bc260-61ff-49a3-90e9-87bdb7893b31", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820773404500, "endTime": 222820777762600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "df584219-aab1-4b79-a1bf-65151f4f9219", "logId": "e4e7cd82-6c3f-432b-a6e1-42cd6153519c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df584219-aab1-4b79-a1bf-65151f4f9219", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820761926600}, "additional": {"logType": "detail", "children": [], "durationId": "f33bc260-61ff-49a3-90e9-87bdb7893b31"}}, {"head": {"id": "0f7dac21-008c-46f7-a7c1-d13d25c6d0fe", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820763243800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bff62ca4-32fe-476d-9814-a82b1fc96983", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820763451500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "342db625-de9a-4391-85a8-c0a2bbd0ffda", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820773425300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16f9efbc-83f3-4793-82d9-d9e637aa27ca", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820775416200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "408a291a-9aab-4894-a875-dbd1436f67f7", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820777464600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baf819ec-c7c0-4c15-8fbd-c909cc36903a", "name": "entry : default@GenerateMetadata cost memory 0.10308837890625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820777662800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4e7cd82-6c3f-432b-a6e1-42cd6153519c", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820773404500, "endTime": 222820777762600}, "additional": {"logType": "info", "children": [], "durationId": "f33bc260-61ff-49a3-90e9-87bdb7893b31"}}, {"head": {"id": "3bc8a7ba-7a2d-49d4-9ef8-0e79235b53b3", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820785744500, "endTime": 222820786393700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "ecead58a-27df-4300-a11c-f250e07bd770", "logId": "483fca0c-49cb-44b2-ae44-f70b5629b408"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ecead58a-27df-4300-a11c-f250e07bd770", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820783469600}, "additional": {"logType": "detail", "children": [], "durationId": "3bc8a7ba-7a2d-49d4-9ef8-0e79235b53b3"}}, {"head": {"id": "5ac7ffe2-0e59-4892-b61a-c597e52cf151", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820785383000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72446f10-970e-4105-b82f-552adfb8b0fd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820785532200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24993917-0ba6-4033-9425-54a72ba6c77a", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820785756700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6e038bd-8808-4b43-817f-965157524312", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820785872200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b874498d-7b34-45a8-97d8-8ec0808e3f15", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820785935600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba8b6f61-2604-4fae-843d-8b623b463258", "name": "entry : default@ConfigureCmake cost memory 0.0374755859375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820786130900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdeed3dd-7d51-46e9-bdd8-1a3599d524c1", "name": "runTaskFromQueue task cost before running: 412 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820786320100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "483fca0c-49cb-44b2-ae44-f70b5629b408", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820785744500, "endTime": 222820786393700, "totalTime": 540700}, "additional": {"logType": "info", "children": [], "durationId": "3bc8a7ba-7a2d-49d4-9ef8-0e79235b53b3"}}, {"head": {"id": "cda1f926-5f96-470f-bc89-3edda5ebc67d", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820792007200, "endTime": 222820796242100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "bea569a7-45e6-491f-8956-ee38024ed328", "logId": "5bc3e22c-11e1-478b-86a4-d1851635f600"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bea569a7-45e6-491f-8956-ee38024ed328", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820788944000}, "additional": {"logType": "detail", "children": [], "durationId": "cda1f926-5f96-470f-bc89-3edda5ebc67d"}}, {"head": {"id": "5f668354-f1b0-45e9-88fa-7d5fc0ef7676", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820790902400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f84981fe-07ff-4c74-811d-2d98f6c26f6e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820791055200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9d3f7c3-2e46-416c-844d-2ef3c14ea24b", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820792024800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44c69973-3fff-4cf4-b1c0-c41b2828cac1", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820795879200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "054c9deb-6b4b-416e-aae1-95e175b3b964", "name": "entry : default@MergeProfile cost memory 0.1185302734375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820796112500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bc3e22c-11e1-478b-86a4-d1851635f600", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820792007200, "endTime": 222820796242100}, "additional": {"logType": "info", "children": [], "durationId": "cda1f926-5f96-470f-bc89-3edda5ebc67d"}}, {"head": {"id": "e9aeb902-a2fa-4384-a387-792b2b77e5db", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820803206200, "endTime": 222820808253600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e3e6500a-f458-442b-8e28-7882a63e45ae", "logId": "dc54cc2d-a0cb-46e8-bfb9-d26e20bca5a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3e6500a-f458-442b-8e28-7882a63e45ae", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820800486800}, "additional": {"logType": "detail", "children": [], "durationId": "e9aeb902-a2fa-4384-a387-792b2b77e5db"}}, {"head": {"id": "7309d371-d0f7-47ff-ae2e-6f23cc1fc4a9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820802051300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d9dcee9-e9e9-482b-8bc0-481e7033533b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820802208000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b7ab0ae-a2a3-446d-a4fd-ba1b78dc769d", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820803221100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be8318ec-39cf-4b0e-8c49-a50b7cb7cc98", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820804512300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f13af582-5f24-4e3f-acc1-8f4465a81206", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820807882600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4590de8a-3760-41c9-95a6-1d8a0b785974", "name": "entry : default@CreateBuildProfile cost memory 0.10845184326171875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820808135600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc54cc2d-a0cb-46e8-bfb9-d26e20bca5a6", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820803206200, "endTime": 222820808253600}, "additional": {"logType": "info", "children": [], "durationId": "e9aeb902-a2fa-4384-a387-792b2b77e5db"}}, {"head": {"id": "7051227d-141f-427c-9cbf-f9e89ccf48d1", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820815058100, "endTime": 222820816296700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "1a822174-fc9d-4a3e-bef4-e8f492127a3d", "logId": "582daa2d-ad6b-4052-861a-9f89787d9922"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a822174-fc9d-4a3e-bef4-e8f492127a3d", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820810864100}, "additional": {"logType": "detail", "children": [], "durationId": "7051227d-141f-427c-9cbf-f9e89ccf48d1"}}, {"head": {"id": "0ef7ec0c-341f-40e9-9be7-beb0d9844ae0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820812981600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "252d4de2-f192-4594-8b0a-47cb523cb282", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820813174900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ede9671f-77de-4b36-8073-e09693337ba4", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820815081700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "243acf2a-094d-4aee-97d7-94658ed68966", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820815383400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73981129-dc6e-4404-8018-afd61f359945", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820815503700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad44acb9-f32d-4a69-9612-67ae9c3cc833", "name": "entry : default@PreCheckSyscap cost memory 0.041107177734375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820815898000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a033b54-3d5a-4d88-930a-4e355516963a", "name": "runTaskFromQueue task cost before running: 441 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820816125900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "582daa2d-ad6b-4052-861a-9f89787d9922", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820815058100, "endTime": 222820816296700, "totalTime": 1026400}, "additional": {"logType": "info", "children": [], "durationId": "7051227d-141f-427c-9cbf-f9e89ccf48d1"}}, {"head": {"id": "15c02fe2-cf0e-4dcd-bf48-a5e95ec310c1", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820824112200, "endTime": 222820836814800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "bc9559c8-a0a6-4e16-9a6b-43609949c169", "logId": "e7dedab0-4872-45e8-9ab5-7aa9def14f98"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc9559c8-a0a6-4e16-9a6b-43609949c169", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820818624200}, "additional": {"logType": "detail", "children": [], "durationId": "15c02fe2-cf0e-4dcd-bf48-a5e95ec310c1"}}, {"head": {"id": "069c0d6d-b000-415f-9d3f-d9aba57c1e40", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820820400100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbbb8b7a-7316-4f86-87dc-3974f172dfe0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820820568800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8babcba-5494-4857-82f9-0c3355dc66ec", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820824146400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59cd90e6-e030-400f-b054-33fd0988ae9e", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820834471400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0093b879-3e1d-4e99-b991-e86d8e52aa2b", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820836327200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d77b5016-6c3c-4da5-99c6-bcd0a10ba5a9", "name": "entry : default@GeneratePkgContextInfo cost memory 0.49083709716796875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820836620300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7dedab0-4872-45e8-9ab5-7aa9def14f98", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820824112200, "endTime": 222820836814800}, "additional": {"logType": "info", "children": [], "durationId": "15c02fe2-cf0e-4dcd-bf48-a5e95ec310c1"}}, {"head": {"id": "957cd8dd-404c-463e-aef5-c463a061a1f6", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820853046800, "endTime": 222820857101600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "e7316b51-07e3-4b33-92d5-b65c8706c1d0", "logId": "6ac671ac-7046-44cd-81e7-2e9876acbb16"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7316b51-07e3-4b33-92d5-b65c8706c1d0", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820841281000}, "additional": {"logType": "detail", "children": [], "durationId": "957cd8dd-404c-463e-aef5-c463a061a1f6"}}, {"head": {"id": "6be8edbe-7c05-4821-a319-dc1df3622588", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820843689500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d41c0acd-d514-4094-b648-d81882f10214", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820843849300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "032ef7b1-45d2-4ab4-8029-0bc2b14987c8", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820853069600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb84fd61-1b2a-4050-9049-f351fe5106f0", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820856312100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7eb26a49-e2f8-4ff9-88a0-72c36c210024", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820856575800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55312477-c4a6-42b4-9deb-e187eea4473c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820856711000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fac84916-418d-456f-8a83-c5b73dd0232f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820856782900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2e32379-211b-448f-8dea-f72c02f3ec1b", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1212158203125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820856907300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b2f088c-dcfd-4086-824e-0f50f5665160", "name": "runTaskFromQueue task cost before running: 482 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820857021100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ac671ac-7046-44cd-81e7-2e9876acbb16", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820853046800, "endTime": 222820857101600, "totalTime": 3949800}, "additional": {"logType": "info", "children": [], "durationId": "957cd8dd-404c-463e-aef5-c463a061a1f6"}}, {"head": {"id": "e3180048-1638-45db-a89a-af6ab43e10c4", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820864159700, "endTime": 222820865544000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "a8f6e12f-b157-415c-81ce-303d894dfd37", "logId": "86f2420a-4c37-4fab-a296-0f3fdaec5e80"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8f6e12f-b157-415c-81ce-303d894dfd37", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820860562700}, "additional": {"logType": "detail", "children": [], "durationId": "e3180048-1638-45db-a89a-af6ab43e10c4"}}, {"head": {"id": "8d4e6c8b-d23d-4c45-8110-d4adfce2421f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820862280000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05f1437e-8b99-4d10-8bb2-133302011fe6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820862446000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54c9f15c-3036-44b4-934f-f60a7ff140ed", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820864212900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce3ad451-7bba-40f0-a667-7888f9e46e22", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820864716200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94968a17-82ab-4caf-8c2a-e8d421bbca4e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820864895600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cb4777c-cb5b-436b-a614-fa049ef960aa", "name": "entry : default@BuildNativeWithCmake cost memory 0.03852081298828125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820865130800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bff7853e-7c08-446f-863a-0d6a78d48f84", "name": "runTaskFromQueue task cost before running: 491 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820865380700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86f2420a-4c37-4fab-a296-0f3fdaec5e80", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820864159700, "endTime": 222820865544000, "totalTime": 1165000}, "additional": {"logType": "info", "children": [], "durationId": "e3180048-1638-45db-a89a-af6ab43e10c4"}}, {"head": {"id": "4143739e-2a85-40cc-ab2b-7673753b3807", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820871229100, "endTime": 222820881307100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "948e0bc3-c62b-412a-aabc-acadc7d3d7ac", "logId": "d3a3058d-096a-4f6b-9ecf-e0a22d55d124"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "948e0bc3-c62b-412a-aabc-acadc7d3d7ac", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820868239600}, "additional": {"logType": "detail", "children": [], "durationId": "4143739e-2a85-40cc-ab2b-7673753b3807"}}, {"head": {"id": "6dbc25c3-68b9-4216-ab38-50a38dc7e4bc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820869688800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6107b483-1e00-490a-aa86-b1ef38a0d5cc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820869844600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "520ce6ee-aa01-4175-b2b5-99c1d0793e35", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820871245800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34138433-75f5-412a-be27-8947a3946f19", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820879617300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ddf41dc-c96a-46a5-90d4-d9b6d4da77a2", "name": "entry : default@MakePackInfo cost memory 0.16413116455078125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820880793200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3a3058d-096a-4f6b-9ecf-e0a22d55d124", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820871229100, "endTime": 222820881307100}, "additional": {"logType": "info", "children": [], "durationId": "4143739e-2a85-40cc-ab2b-7673753b3807"}}, {"head": {"id": "4e2aa703-c312-4bfe-95b1-743073737949", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820894550300, "endTime": 222820911909300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "04e36437-ef4c-4d11-90d0-973f442d8646", "logId": "9d32bbb9-0739-4f37-95b9-4c54d7870ce6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "04e36437-ef4c-4d11-90d0-973f442d8646", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820887859000}, "additional": {"logType": "detail", "children": [], "durationId": "4e2aa703-c312-4bfe-95b1-743073737949"}}, {"head": {"id": "a1536d7e-3a82-4e5d-b39c-a6e6d892fba8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820891229700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a44f7a38-d083-4fd7-9b38-0e7ff3fce5c6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820891456200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "462084c8-6b5b-4fdb-b30f-b62c4c437442", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820894581500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f184662-5b53-4a47-85b6-1e026264e9f8", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820895261200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9eb606b2-7327-44d0-8a21-369221bb0545", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 5 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820899763300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e88f8f4d-56af-47cc-bfe6-f66c7497a5c4", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820910445900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b015bd4c-2fad-4b29-9c3a-ec2100da6080", "name": "entry : default@SyscapTransform cost memory 0.1511688232421875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820911333100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d32bbb9-0739-4f37-95b9-4c54d7870ce6", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820894550300, "endTime": 222820911909300}, "additional": {"logType": "info", "children": [], "durationId": "4e2aa703-c312-4bfe-95b1-743073737949"}}, {"head": {"id": "ffde44a3-b066-41d4-b9f0-9b5064a5b5e4", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820957772100, "endTime": 222820979254300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "dc15c863-32ed-4371-a490-6bf6e1d2f8a1", "logId": "86293f71-d0c9-4e84-91a0-ffd495759e6d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc15c863-32ed-4371-a490-6bf6e1d2f8a1", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820920806400}, "additional": {"logType": "detail", "children": [], "durationId": "ffde44a3-b066-41d4-b9f0-9b5064a5b5e4"}}, {"head": {"id": "21286290-cdcd-4bf7-a4fd-71d5d60202c6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820923144700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88ba15a1-2391-4465-aae3-d707827e2be0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820936492700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24a61334-269f-4015-9be1-9ba2ca1136e5", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820957804500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c87ce42-6ed6-49bc-a013-36ed72af3542", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820978907100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e99399ae-1e00-4f5f-92b6-fcb6bd8c3dc7", "name": "entry : default@ProcessProfile cost memory 0.12526702880859375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820979133600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86293f71-d0c9-4e84-91a0-ffd495759e6d", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820957772100, "endTime": 222820979254300}, "additional": {"logType": "info", "children": [], "durationId": "ffde44a3-b066-41d4-b9f0-9b5064a5b5e4"}}, {"head": {"id": "d34f6eb5-4390-4680-8e07-555a160bec16", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820988334700, "endTime": 222821003487800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "af0788d8-c868-49c0-8e8b-313b1d474b30", "logId": "24ada1ce-de0f-4a42-86c6-e8e5b7f3c9c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af0788d8-c868-49c0-8e8b-313b1d474b30", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820983233000}, "additional": {"logType": "detail", "children": [], "durationId": "d34f6eb5-4390-4680-8e07-555a160bec16"}}, {"head": {"id": "9655946e-90a9-45c5-af53-4a628083edea", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820985110000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72dc9c70-5afd-4a3d-ae66-5e108957d2cb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820985300100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "948a032c-2b79-48fa-b284-a3931ae7e3b6", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820988354100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e22375f-ce6e-4009-8770-040aaeba47b4", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821003049200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fb806a6-4a1e-499d-b4f0-4e7d43f8e579", "name": "entry : default@ProcessRouterMap cost memory 0.2339324951171875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821003330100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24ada1ce-de0f-4a42-86c6-e8e5b7f3c9c3", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820988334700, "endTime": 222821003487800}, "additional": {"logType": "info", "children": [], "durationId": "d34f6eb5-4390-4680-8e07-555a160bec16"}}, {"head": {"id": "218898eb-8947-4ec0-b0be-b44678d01c0d", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821012993100, "endTime": 222821024211200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "7d22ade2-d1de-4fa5-a73c-36da86258b95", "logId": "88cf78f0-a11c-42da-9046-c2176a27b1f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d22ade2-d1de-4fa5-a73c-36da86258b95", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821011043300}, "additional": {"logType": "detail", "children": [], "durationId": "218898eb-8947-4ec0-b0be-b44678d01c0d"}}, {"head": {"id": "f84a70fc-9197-4aba-b620-491c51e5c3d0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821012718400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c9d3b96-f882-4f80-af9d-6a48acef084d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821012874600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0245e68-413d-4823-88b5-4b600ae8d0ff", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821013001400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53eeab05-4b99-4c15-86ad-51b8bf0765c1", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821013218800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "200f9bb7-1cad-442d-88ff-49a9ec5832ba", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821021217500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76053e1c-2556-436e-91dd-2bbd45cafa92", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821021439700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d1e680a-7977-447d-b329-678925135b77", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821021583500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52432b9b-cf43-4231-9941-c4156083c9c5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821021672000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29e2555e-2748-489b-a1a3-80ed364ea61a", "name": "entry : default@ProcessStartupConfig cost memory 0.26166534423828125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821023916900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f3f5d47-cbeb-4826-b419-adadfc0d0682", "name": "runTaskFromQueue task cost before running: 649 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821024134200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88cf78f0-a11c-42da-9046-c2176a27b1f0", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821012993100, "endTime": 222821024211200, "totalTime": 11097600}, "additional": {"logType": "info", "children": [], "durationId": "218898eb-8947-4ec0-b0be-b44678d01c0d"}}, {"head": {"id": "e8dce7f4-59ee-4083-b834-f598da7469b3", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821030066300, "endTime": 222821033775400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "d347c306-541f-4b4a-9ff2-460e3d569e93", "logId": "3cbb65d2-99d9-4b3a-b3fb-7019918108b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d347c306-541f-4b4a-9ff2-460e3d569e93", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821027640600}, "additional": {"logType": "detail", "children": [], "durationId": "e8dce7f4-59ee-4083-b834-f598da7469b3"}}, {"head": {"id": "264e5bb1-b160-40a1-ab54-971e69818ee4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821028870500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6123f4db-58d6-4cca-9080-2d74aba0196c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821029022600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "174acd23-72d1-4e36-b00f-431dd9abcbef", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821030089900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d56e9ca-83ed-45df-a0ff-b08d6ed7b8cf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821030415700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "205f39ae-bfa7-4368-9faa-e6d8b3dc1414", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821030502800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53c995aa-7680-4654-9310-f46f934da51c", "name": "entry : default@BuildNativeWithNinja cost memory 0.058319091796875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821033308600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16c5a5c9-249f-49d6-8b74-19c44bbe24f2", "name": "runTaskFromQueue task cost before running: 659 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821033619800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cbb65d2-99d9-4b3a-b3fb-7019918108b3", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821030066300, "endTime": 222821033775400, "totalTime": 3499600}, "additional": {"logType": "info", "children": [], "durationId": "e8dce7f4-59ee-4083-b834-f598da7469b3"}}, {"head": {"id": "08dc88a8-7a64-4270-be0b-2efd898724c9", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821049275300, "endTime": 222821058436000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "78f28c35-6b7a-4495-b755-0fd0dea42fef", "logId": "19a1dc0b-3bae-41d7-8d34-6073c1ad08a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "78f28c35-6b7a-4495-b755-0fd0dea42fef", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821039789100}, "additional": {"logType": "detail", "children": [], "durationId": "08dc88a8-7a64-4270-be0b-2efd898724c9"}}, {"head": {"id": "15d3bd3b-485d-4b3a-b5a1-ec66c7a5064c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821042919500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "038dc300-91f8-4ca2-8531-e9effd22104c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821043148400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbfaa034-47d0-4bfb-97e8-fbc12a6f4fab", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821046040700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ad85b62-5df0-41bc-bf60-0d1806870d90", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821052291000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f88c63e0-ba1e-4156-9bb9-9833b40d8f35", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821055400500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "931060ba-9e52-41e5-a1b4-b76a1925f28a", "name": "entry : default@ProcessResource cost memory 0.16242218017578125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821055894400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19a1dc0b-3bae-41d7-8d34-6073c1ad08a8", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821049275300, "endTime": 222821058436000}, "additional": {"logType": "info", "children": [], "durationId": "08dc88a8-7a64-4270-be0b-2efd898724c9"}}, {"head": {"id": "c016cb46-e106-4550-b308-9bd4a5a5430d", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821072168600, "endTime": 222821109498800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ad1bd0dd-0f00-452c-a440-33fbc4df7cd3", "logId": "a5a00a8e-e928-4375-b36c-c9f5756ce57d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad1bd0dd-0f00-452c-a440-33fbc4df7cd3", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821063016200}, "additional": {"logType": "detail", "children": [], "durationId": "c016cb46-e106-4550-b308-9bd4a5a5430d"}}, {"head": {"id": "19973124-6679-417c-a112-d4fa29ac8204", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821066410500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78766fc3-94ab-4e02-9690-062d5f4243d2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821066626800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "783f70f5-aed2-471b-916c-513f1ec6b25f", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821072238600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2581811b-8dbb-47b1-8be9-48b57a688e48", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 19 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821109106400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebffa2fa-60a7-40a8-ab70-4f63839b09c7", "name": "entry : default@GenerateLoaderJson cost memory 0.8822174072265625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821109365900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5a00a8e-e928-4375-b36c-c9f5756ce57d", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821072168600, "endTime": 222821109498800}, "additional": {"logType": "info", "children": [], "durationId": "c016cb46-e106-4550-b308-9bd4a5a5430d"}}, {"head": {"id": "d3c135f8-79a3-4370-a71d-3d02233ffe37", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821127338900, "endTime": 222821133965300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "fc5febc8-35b3-40d9-8638-25a323879a90", "logId": "8ae66531-9bc7-42cf-b5bd-fddf7c69de03"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc5febc8-35b3-40d9-8638-25a323879a90", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821124757100}, "additional": {"logType": "detail", "children": [], "durationId": "d3c135f8-79a3-4370-a71d-3d02233ffe37"}}, {"head": {"id": "d0259499-789e-4851-8c01-ad343858d250", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821126139200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55057fe0-c202-4556-b1f6-57ac47a512e1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821126320300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85ed8106-dfd5-4469-b562-0db3b0778fb3", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821127354800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "914c0187-be1f-4557-b097-df7893c3f34e", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821133589800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df728951-03b2-495f-9bcc-b9a0ecbe83f8", "name": "entry : default@ProcessLibs cost memory 0.14253997802734375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821133839700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ae66531-9bc7-42cf-b5bd-fddf7c69de03", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821127338900, "endTime": 222821133965300}, "additional": {"logType": "info", "children": [], "durationId": "d3c135f8-79a3-4370-a71d-3d02233ffe37"}}, {"head": {"id": "da0a9b0b-d112-443a-b780-de40bd7723a2", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821147830300, "endTime": 222821190240800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "7657c427-a953-43f5-b89c-c91094877a8a", "logId": "27a8c34d-8e47-4a7c-a0b7-da1c63c86b23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7657c427-a953-43f5-b89c-c91094877a8a", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821136897300}, "additional": {"logType": "detail", "children": [], "durationId": "da0a9b0b-d112-443a-b780-de40bd7723a2"}}, {"head": {"id": "bdb440ad-ca22-449b-9536-a79722468ef6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821138714900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fe97676-e106-4cb2-9f25-d33aa79a7751", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821139018800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "599059db-4995-4cc2-96ed-cbab652d862f", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821141964800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4e4e3c1-188c-48a0-b4d7-5fd17e2e9ebc", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821147896300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87da69ee-600e-4894-ad4f-00572b4f1a7e", "name": "Incremental task entry:default@CompileResource pre-execution cost: 40 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821189837500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2c583aa-919a-43f4-ad19-c560b73fc28f", "name": "entry : default@CompileResource cost memory 1.3160476684570312", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821190095700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27a8c34d-8e47-4a7c-a0b7-da1c63c86b23", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821147830300, "endTime": 222821190240800}, "additional": {"logType": "info", "children": [], "durationId": "da0a9b0b-d112-443a-b780-de40bd7723a2"}}, {"head": {"id": "c02138f1-bfc5-4cd3-ad79-21653932f300", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821199193900, "endTime": 222821202166000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "3af17aa8-6bfb-4e19-a8a6-557e3813a4d3", "logId": "414536d8-4886-44ae-a89d-d3f1f85c26ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3af17aa8-6bfb-4e19-a8a6-557e3813a4d3", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821193500500}, "additional": {"logType": "detail", "children": [], "durationId": "c02138f1-bfc5-4cd3-ad79-21653932f300"}}, {"head": {"id": "a23c5ca0-cbf3-43c1-ab4e-9891d14aeb3f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821194797400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "261f3653-0cc1-4127-bdb8-f2127606900f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821194924100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d06dcfb-45a4-4a28-b044-dd9cac42baf0", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821199210800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3973e2c-26fc-4d74-ac9b-ae70d56ba495", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821199862600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f958b408-c4a1-405d-830a-7771d5c4f18f", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821201883300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee039a84-7e89-45ec-8cfd-11563314b319", "name": "entry : default@DoNativeStrip cost memory 0.08003997802734375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821202064500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "414536d8-4886-44ae-a89d-d3f1f85c26ee", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821199193900, "endTime": 222821202166000}, "additional": {"logType": "info", "children": [], "durationId": "c02138f1-bfc5-4cd3-ad79-21653932f300"}}, {"head": {"id": "be36c42f-f312-4963-8211-8c7eef8962e6", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821212359400, "endTime": 222837294550300}, "additional": {"children": ["666019c6-fefb-4934-b135-bee3c597659f", "a95df640-51e6-4fc5-8a0b-deeb91d805dd"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed."], "detailId": "41849be4-2527-414e-88e1-3c06bb23ca05", "logId": "625442f7-1258-4086-a9e5-2be869a72f64"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41849be4-2527-414e-88e1-3c06bb23ca05", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821204245500}, "additional": {"logType": "detail", "children": [], "durationId": "be36c42f-f312-4963-8211-8c7eef8962e6"}}, {"head": {"id": "2fcd7e0e-8b3c-48c4-af91-d9b8ef809620", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821206221200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c5f1e5c-81e0-4e1f-97d1-4f28940c8655", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821206425100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5125dd9a-e4d0-433c-a2e7-26f6bb57d542", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821212376100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f02a3d7-f889-4b53-b48e-8025949cb650", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821212662900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4644d32e-16e1-4156-ae3a-a62aed8ed225", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821250298300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f3c6aa5-da83-415d-9ecd-10656f142139", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 30 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821250480200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa981445-a999-4cba-99a8-1c6c568e94e1", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821273939700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b16e0ca-ac1c-450a-9697-817accf109a5", "name": "default@CompileArkTS work[6] is submitted.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821276103900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "666019c6-fefb-4934-b135-bee3c597659f", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 222822136145300, "endTime": 222837294311000}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "be36c42f-f312-4963-8211-8c7eef8962e6", "logId": "c57c392c-886f-420a-855d-fbf1417c691d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "107672de-05fd-4049-98c2-6fc1ef31e304", "name": "default@CompileArkTS work[6] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821277130100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40105c82-f7c4-4a9d-922f-0b039e7faca6", "name": "default@CompileArkTS work[6] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821277280400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34e81f07-dcf0-497c-b8b6-c6e221def112", "name": "CopyResources startTime: 222821277377400", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821277381000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29637e16-cce8-4059-a999-cf4072d3d266", "name": "default@CompileArkTS work[7] is submitted.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821277458700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a95df640-51e6-4fc5-8a0b-deeb91d805dd", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker18", "startTime": 222822685979000, "endTime": 222822700960900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "be36c42f-f312-4963-8211-8c7eef8962e6", "logId": "b5cc2186-6a55-44e6-b1ea-dc235d9aea8a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c959276e-f1a7-4b06-b2cf-557c9a180f2c", "name": "default@CompileArkTS work[7] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821278246000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76b12234-356f-46d3-a089-94903b2fd362", "name": "default@CompileArkTS work[7] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821278350300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88535b79-2655-47d1-82f9-23d04d770e21", "name": "entry : default@CompileArkTS cost memory 2.0673599243164062", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821278456200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5a0e4aa-af5f-43df-9c79-f3c752f60495", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821285162300, "endTime": 222821295551700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "1cc4e3b4-8d62-420e-b876-dc715c399596", "logId": "c5f5900a-b5c5-479e-b006-abe87032eca2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1cc4e3b4-8d62-420e-b876-dc715c399596", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821279866400}, "additional": {"logType": "detail", "children": [], "durationId": "e5a0e4aa-af5f-43df-9c79-f3c752f60495"}}, {"head": {"id": "9fc088e4-f29c-4f71-8759-1f8c6aba9eb7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821281099600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a67449e-ea1d-4c9e-a3f6-4566fd5278bc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821281234800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ea62bc3-9868-435e-8534-743c3ed032d2", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821285177500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a125160-8d44-4392-b8a6-cddb341ba202", "name": "entry : default@BuildJS cost memory -6.7841644287109375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821295309500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d7df6fa-4ed3-4acf-9151-7ecffaaa7acd", "name": "runTaskFromQueue task cost before running: 921 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821295481700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5f5900a-b5c5-479e-b006-abe87032eca2", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821285162300, "endTime": 222821295551700, "totalTime": 10291200}, "additional": {"logType": "info", "children": [], "durationId": "e5a0e4aa-af5f-43df-9c79-f3c752f60495"}}, {"head": {"id": "1ec34e8a-9569-4869-abcd-01213572f7d0", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821301139900, "endTime": 222821304730300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "bd73f380-ff3a-41e4-9628-a909ec9587ba", "logId": "2ddf23a4-c9e6-4a10-a5b7-5d8c0f5a6bad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bd73f380-ff3a-41e4-9628-a909ec9587ba", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821297135400}, "additional": {"logType": "detail", "children": [], "durationId": "1ec34e8a-9569-4869-abcd-01213572f7d0"}}, {"head": {"id": "39da2df0-1d06-4e6c-a0c3-2d6acb5874de", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821298437000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fbc7968-c5b6-45d7-8d1b-ac5ae7cc22b9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821298599700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60b38acf-6691-4484-bed3-3f71b877cd62", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821301154200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9253b67-a8a1-4f67-b6ca-01ca47f4c80c", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821302003000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eac94cf1-b44c-41ba-b56f-aac4734d049a", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821304427700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0687f4dd-64e4-443b-99c1-d5b67788f878", "name": "entry : default@CacheNativeLibs cost memory 0.0952911376953125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821304642500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ddf23a4-c9e6-4a10-a5b7-5d8c0f5a6bad", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821301139900, "endTime": 222821304730300}, "additional": {"logType": "info", "children": [], "durationId": "1ec34e8a-9569-4869-abcd-01213572f7d0"}}, {"head": {"id": "f633a75b-7db9-4f6f-9704-cd1eeef93e49", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821482903000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c3ef51b-caab-4102-9bda-74ca6e0686e0", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821483193800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bef4461d-6e19-4051-943b-76cf333c5074", "name": "default@CompileArkTS work[7] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821484179700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af67f88d-d7d7-464f-b850-4030d452d25d", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222822134625800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16da8a5f-7c1b-4da4-9b71-4d1403531011", "name": "A work dispatched to worker[18] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222822134809000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f00c244-720c-4022-a969-84680e17ec7a", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222822134888100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54296a6e-e1ee-4770-980d-866bd3370704", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222822134923300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "881b125e-b345-4e29-ad0b-349f51abcf7c", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222822134953100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c040bcf3-a42e-46a3-9e11-29d324a0b09c", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222822134982300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95faa8e3-6ae4-49be-a636-2a887f141b87", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222822135012000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81ca4e06-d7fe-4178-b6b6-7d0ca8966bbe", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222822135038300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab8655d7-1b53-4407-900c-6b05bc7e8147", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222822135065200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e64d569-9486-4721-ad04-d59850085ab3", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222822135102900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b11c5f12-c86d-4bca-afd6-a7efdca71902", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222822135138400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "430c7c11-0fcf-4203-a9cd-930dcff30fc0", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222822135165500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0d3f5a4-e9a1-4ce1-aa43-1b78e105118b", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222822135194200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c689a796-1401-45e1-96f0-e7b855467225", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222822135223400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1164bb13-f8c2-45e1-83b9-330684cd68db", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222822135257100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e542c0df-1967-4496-98f6-2caad814d0cd", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222822135295700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ebcd46b-4fa3-4fe8-b40b-3ba6f0e1ae4c", "name": "default@CompileArkTS work[6] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222822136158800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27a39fac-72c4-4616-b887-06c27583549e", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222822701309600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c194e275-fea3-43b4-b9fc-277584c655fb", "name": "CopyResources is end, endTime: 222822701464100", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222822701469000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25aafe97-a310-4ef0-b000-c6aeda8eb694", "name": "default@CompileArkTS work[7] done.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222822701565900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5cc2186-6a55-44e6-b1ea-dc235d9aea8a", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Worker18", "startTime": 222822685979000, "endTime": 222822700960900}, "additional": {"logType": "info", "children": [], "durationId": "a95df640-51e6-4fc5-8a0b-deeb91d805dd", "parent": "625442f7-1258-4086-a9e5-2be869a72f64"}}, {"head": {"id": "b1c6dc21-5b91-4786-99c8-25fb7bb75326", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222823237500700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f9fbe7e-406d-43c7-9de2-2dd833ef4b1e", "name": "worker[4] has one work error.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837294040700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f97fdafd-4e44-4968-95aa-c663ba6eb3b8", "name": "default@CompileArkTS work[6] failed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837294412300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c57c392c-886f-420a-855d-fbf1417c691d", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 222822136145300, "endTime": 222837294311000}, "additional": {"logType": "error", "children": [], "durationId": "666019c6-fefb-4934-b135-bee3c597659f", "parent": "625442f7-1258-4086-a9e5-2be869a72f64"}}, {"head": {"id": "625442f7-1258-4086-a9e5-2be869a72f64", "name": "Failed :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222821212359400, "endTime": 222837294550300}, "additional": {"logType": "error", "children": ["c57c392c-886f-420a-855d-fbf1417c691d", "b5cc2186-6a55-44e6-b1ea-dc235d9aea8a"], "durationId": "be36c42f-f312-4963-8211-8c7eef8962e6"}}, {"head": {"id": "8eb7f654-07cb-408a-8b38-d8704482c1e1", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837294721500}, "additional": {"logType": "debug", "children": [], "durationId": "be36c42f-f312-4963-8211-8c7eef8962e6"}}, {"head": {"id": "532bcfa1-5c69-4441-9691-dbb1884edef8", "name": "ERROR: stacktrace = Error: ArkTS Compiler Error\r\n\u001b[33m1 WARN: \u001b[33m\u001b[33mArkTS:WARN: For details about ArkTS syntax errors, see FAQs\u001b[39m\u001b[39m\r\n\u001b[33m2 WARN: \u001b[33m\u001b[33mArkTS:WARN File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/WelcomePage.ets:29:64\n 'getContext' has been deprecated.\n\u001b[39m\u001b[39m\r\n\u001b[33m3 WARN: \u001b[33m\u001b[33mArkTS:WARN File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/WelcomePage.ets:66:12\n 'pushUrl' has been deprecated.\n\u001b[39m\u001b[39m\r\n\u001b[31m1 ERROR: \u001b[31m10605008 ArkTS Compiler Error\r\nError Message: Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/CategoryPage.ets:138:17\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m2 ERROR: \u001b[31m10605099 ArkTS Compiler Error\r\nError Message: It is possible to spread only arrays or classes derived from arrays into the rest parameter or array literals (arkts-no-spread) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/CategoryPage.ets:141:40\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m3 ERROR: \u001b[31m10605008 ArkTS Compiler Error\r\nError Message: Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/CategoryPage.ets:150:17\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m4 ERROR: \u001b[31m10605008 ArkTS Compiler Error\r\nError Message: Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/CategoryPage.ets:151:17\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m5 ERROR: \u001b[31m10605008 ArkTS Compiler Error\r\nError Message: Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/CategoryPage.ets:152:17\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m6 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'data' does not exist on type 'AppListData'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/services/ApiService.ets:532:46\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m7 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'total' does not exist on type 'AppListData'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/services/ApiService.ets:533:50\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m8 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'page' does not exist on type 'AppListData'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/services/ApiService.ets:533:79\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m9 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'data' does not exist on type 'AppListData'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/services/ApiService.ets:533:107\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m10 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'data' does not exist on type 'AppListData'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/CategoryPage.ets:137:44\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m11 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'data' does not exist on type 'AppListData'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/CategoryPage.ets:137:80\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m12 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'data' does not exist on type 'AppListData'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/CategoryPage.ets:138:41\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m13 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'page' does not exist on type 'AppListData'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/CategoryPage.ets:150:48\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m14 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'total' does not exist on type 'AppListData'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/CategoryPage.ets:151:44\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m15 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'page_size' does not exist on type 'AppListData'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/CategoryPage.ets:152:42\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31mCOMPILE RESULT:FAIL {ERROR:16 WARN:3}\u001b[39m\n    at runArkPack (C:\\command-line-tools\\hvigor\\hvigor-ohos-plugin\\node_modules\\@ohos\\hvigor-arkts-compose\\dist\\src\\arkts-pack.js:1:5479)", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837296511900}, "additional": {"logType": "debug", "children": [], "durationId": "be36c42f-f312-4963-8211-8c7eef8962e6"}}, {"head": {"id": "af209223-cb67-466e-b252-0ca0e48a0830", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837310725400, "endTime": 222837310955200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86315d1f-9707-499c-84ba-48cbbd96ffcc", "logId": "9cacbf93-12bc-41e7-bdda-6dc17df30271"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9cacbf93-12bc-41e7-bdda-6dc17df30271", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837310725400, "endTime": 222837310955200}, "additional": {"logType": "info", "children": [], "durationId": "af209223-cb67-466e-b252-0ca0e48a0830"}}, {"head": {"id": "03d82369-bafb-48dd-8006-aa11b2a84354", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222820375264300, "endTime": 222837311369000}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 19, "minute": 51, "second": 7}, "completeCommand": "{\"prop\":[\"product=default\"],\"mode\":\"module\",\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "1efcc4e0-4569-4c21-88df-6c02a763347a", "name": "BUILD FAILED in 16 s 936 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837311417500}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "4e8326ec-867e-48ef-8755-a03b9b2bcf15", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837311697100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f9221c1-61ed-4798-954c-4bb39927814f", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837311776000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e3afeb8-793f-404f-84f6-6ed6ad8c2802", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837312197400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "560fb4c0-0be9-4360-b8d1-1418fd09e275", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837312551600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "074ed4b4-d06e-4e20-9be6-16dde987f546", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837312677200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd7b685b-5376-4fa3-ae49-9bd970074f79", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837312780700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "104eb76f-dd5b-4c27-bfa6-3d1a61f7e6f8", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837313596500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a97744d4-ea06-41c5-8d61-0ebdc1b77c91", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837314356300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b24ae15-6c45-49cb-b81f-e6b381aff592", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837314618300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cdc269b-244c-42a3-a998-2bc2f8d76a01", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837314682200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c334272f-750d-42d3-ab53-437edf963a9a", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837314722200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d196223-32b5-4720-b936-0f40d43ce174", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837314758000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81458d8f-06e9-4360-bb7c-12e365549d0d", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837314791300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60c46043-5e29-4d13-bb43-f118189c5c54", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837315830400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21d4f915-4006-4123-aac7-a2db555e0575", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837316153800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ac94a1a-71a8-4144-90f7-e71228e0fecd", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837316404800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e3512a9-22c5-427b-9a11-9e1fc5976a20", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837316470900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcbf0e4f-5d20-4a4d-a35f-9006333c9817", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837316514900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0de9012-1dd9-4dc8-804a-82636d43d043", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837316551400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2663961-dfd9-4839-b6cf-aa1fba847e95", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837316589500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e71cafc-dd3c-4c86-aabf-ab30dcb16055", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837316624000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b4ea960-9133-415e-abb1-2f981bad72e1", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837319731100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26e6ec03-2b82-4661-aedb-1eb9ad170c82", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837320543300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b97c4696-2264-469e-ae7d-fed05b76e8be", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837321024500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1efdc135-7202-42e1-b1e4-600d8dd51d76", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837321367300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d412f579-60d8-4217-a568-37e0dbdf11cd", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837321675100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49cd4a1c-01d4-457e-b139-63a232d904ad", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837322521700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10a80106-7b8f-44e0-ae58-aed3a05b3128", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837322614300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3522fd6-0f30-46c6-ba05-909a87743fc6", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837322837300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f09f600a-d516-480c-a7a5-77450b13d426", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837323265900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7c0e208-7366-406f-8a1a-35bc6013489e", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837324786000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bfc7a5a-641d-4094-a44c-f40014199fad", "name": "Incremental task entry:default@CompileArkTS post-execution cost:9 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837325456200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d027d30-9d9e-4e1a-a62d-51ae66d88a46", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837327659500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb41cd9d-9ed9-4b57-a31f-2270aee45d61", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837328463400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ef6a29d-9892-4984-a6b3-ccce29b63352", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837328979900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3894232-3266-4490-adce-2dda636c3153", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837329375400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30e84417-151b-41bb-b749-b963ac91450e", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837329662600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3848bd3b-50dc-43f6-b643-f7cad5a48fcf", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837330499300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73d13e52-a8e6-451b-9837-9d0a3581e1fc", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837332221400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e4bc844-98bb-45b0-91ed-dbe3fe811999", "name": "Incremental task entry:default@BuildJS post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837332688200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec8016eb-1e5d-4165-a66c-54d4f8508cfb", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 222837332779800}, "additional": {"logType": "debug", "children": []}}], "workLog": []}