import { PageContainer } from '@ant-design/pro-components';
import { Card, Table, Button, Tag, Space, Modal, Form, Input, InputNumber, Switch, Select, message, Tabs, Row, Col, Tooltip, Divider } from 'antd';
import { EditOutlined, ReloadOutlined, QuestionCircleOutlined, SaveOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { useRequest } from '@umijs/max';
import type { ColumnsType } from 'antd/es/table';
import { getParamList, updateParam } from '@/services/settings';

interface ParamItem {
  id: string;
  key: string;
  name: string;
  value: string;
  defaultValue: string;
  description: string;
  type: 'string' | 'number' | 'boolean' | 'select';
  options?: string[];
  group: 'app' | 'user' | 'security' | 'system' | 'email' | 'storage';
  editable: boolean;
  updatedTime?: string;
  updatedBy?: string;
}

// 定义搜索参数接口
interface SearchParams {
  keyword?: string;
  [key: string]: any;
}

// 模拟数据获取函数
const fetchParamList = async (params: any) => {
  // 实际项目中应该调用API
  console.log('Fetching param list with params:', params);
  
  // 模拟数据
  const mockData: ParamItem[] = [
    // 邮箱配置
    {
      id: '11',
      key: 'email.smtp.host',
      name: 'SMTP服务器地址',
      value: 'smtp.example.com',
      defaultValue: 'smtp.example.com',
      description: '邮件发送服务器地址',
      type: 'string',
      group: 'email',
      editable: true,
      updatedTime: '2023-06-10 09:30:00',
      updatedBy: 'admin',
    },
    {
      id: '12',
      key: 'email.smtp.port',
      name: 'SMTP服务器端口',
      value: '587',
      defaultValue: '587',
      description: '邮件发送服务器端口',
      type: 'number',
      group: 'email',
      editable: true,
    },
    {
      id: '13',
      key: 'email.smtp.username',
      name: 'SMTP用户名',
      value: '<EMAIL>',
      defaultValue: '<EMAIL>',
      description: '邮件发送账号用户名',
      type: 'string',
      group: 'email',
      editable: true,
    },
    {
      id: '14',
      key: 'email.smtp.encryption',
      name: '加密方式',
      value: 'tls',
      defaultValue: 'tls',
      description: '邮件发送加密方式',
      type: 'select',
      options: ['none', 'ssl', 'tls'],
      group: 'email',
      editable: true,
    },
    // 存储配置
    {
      id: '19',
      key: 'storage.type',
      name: '存储类型',
      value: 'local',
      defaultValue: 'local',
      description: '文件存储类型',
      type: 'select',
      options: ['local', 's3', 'oss', 'cos'],
      group: 'storage',
      editable: true,
      updatedTime: '2023-06-20 11:10:00',
      updatedBy: 'admin',
    },
    {
      id: '20',
      key: 'storage.max_file_size',
      name: '最大文件大小',
      value: '10',
      defaultValue: '5',
      description: '允许上传的最大文件大小（MB）',
      type: 'number',
      group: 'storage',
      editable: true,
    },
    {
      id: '21',
      key: 'storage.allowed_types',
      name: '允许的文件类型',
      value: 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx',
      defaultValue: 'jpg,jpeg,png,gif,pdf',
      description: '允许上传的文件类型（逗号分隔）',
      type: 'string',
      group: 'storage',
      editable: true,
    },
  ];

  // 根据分组过滤
  const groupFilteredData = params.group ? mockData.filter(item => item.group === params.group) : mockData;
  
  // 根据关键词过滤
  const keywordFilteredData = params.keyword
    ? groupFilteredData.filter(item => 
        item.name.toLowerCase().includes(params.keyword.toLowerCase()) ||
        item.key.toLowerCase().includes(params.keyword.toLowerCase()) ||
        item.description.toLowerCase().includes(params.keyword.toLowerCase())
      )
    : groupFilteredData;

  return { data: keywordFilteredData, total: keywordFilteredData.length };
};

const ParamsConfig: React.FC = () => {
  const [searchParams, setSearchParams] = useState<SearchParams>({});
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [currentParam, setCurrentParam] = useState<ParamItem | null>(null);
  const [editForm] = Form.useForm();
  const [activeTab, setActiveTab] = useState('email');
  const [editingKey, setEditingKey] = useState('');

  // 替换模拟数据获取函数
  const { data, loading, refresh } = useRequest(() => getParamList({
    group: activeTab !== 'all' ? activeTab : undefined,
    keyword: searchParams.keyword,
  }), {
    refreshDeps: [searchParams, activeTab],
  });

  const handleSearch = (values: any) => {
    setSearchParams(values);
  };

  const handleTabChange = (key: string) => {
    setActiveTab(key);
    setEditingKey('');
  };

  const handleEdit = (record: ParamItem) => {
    setCurrentParam(record);
    editForm.setFieldsValue({
      value: record.type === 'boolean' ? record.value === 'true' : record.value,
    });
    setEditModalVisible(true);
  };

  const handleInlineEdit = (record: ParamItem) => {
    setEditingKey(record.id);
  };

  const handleInlineCancel = () => {
    setEditingKey('');
  };

  const handleInlineSave = async (record: ParamItem, newValue: any) => {
    try {
      await updateParam({
        key: record.key,
        value: newValue,
      });
      message.success('参数更新成功');
      setEditingKey('');
      refresh();
    } catch (error) {
      message.error('参数更新失败');
    }
  };

  const handleReset = (record: ParamItem) => {
    Modal.confirm({
      title: '确认重置',
      content: `确定要将「${record.name}」重置为默认值吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          await updateParam({
            key: record.key,
            value: record.defaultValue,
          });
          message.success('参数已重置为默认值');
          refresh();
        } catch (error) {
          message.error('参数重置失败');
        }
      },
    });
  };

  const handleEditSubmit = () => {
    editForm.validateFields().then(async values => {
      try {
        await updateParam({
          key: currentParam?.key || '',
          value: values.value,
        });
        message.success('参数更新成功');
        setEditModalVisible(false);
        refresh();
      } catch (error) {
        message.error('参数更新失败');
      }
    });
  };

  const getGroupText = (group: string) => {
    switch (group) {
      case 'app':
        return '应用设置';
      case 'user':
        return '用户设置';
      case 'security':
        return '安全设置';
      case 'system':
        return '系统设置';
      case 'email':
        return '邮件设置';
      case 'storage':
        return '存储设置';
      default:
        return group;
    }
  };

  const renderValueInput = (record: ParamItem, editing: boolean) => {
    if (!editing) {
      let displayValue = record.value;
      if (record.type === 'boolean') {
        displayValue = record.value === 'true' ? '是' : '否';
      } else if (record.type === 'select' && record.options) {
        const optionMap: Record<string, string> = {
          'weekly': '每周',
          'biweekly': '每两周',
          'monthly': '每月',
        };
        displayValue = optionMap[record.value] || record.value;
      }
      return displayValue;
    }

    switch (record.type) {
      case 'string':
        return (
          <Input 
            defaultValue={record.value} 
            onPressEnter={(e) => handleInlineSave(record, e.currentTarget.value)}
            onBlur={(e) => handleInlineSave(record, e.target.value)}
            autoFocus
          />
        );
      case 'number':
        return (
          <InputNumber 
            defaultValue={Number(record.value)} 
            onPressEnter={(e) => handleInlineSave(record, e.currentTarget.value)}
            onBlur={(e) => handleInlineSave(record, e.target.value)}
            autoFocus
          />
        );
      case 'boolean':
        return (
          <Switch 
            defaultChecked={record.value === 'true'} 
            onChange={(checked) => handleInlineSave(record, checked.toString())}
          />
        );
      case 'select':
        if (record.options) {
          const options = record.options.map(opt => {
            const optionMap: Record<string, string> = {
              'weekly': '每周',
              'biweekly': '每两周',
              'monthly': '每月',
            };
            return { value: opt, label: optionMap[opt] || opt };
          });
          return (
            <Select 
              defaultValue={record.value} 
              options={options}
              onChange={(value) => handleInlineSave(record, value)}
              style={{ width: '100%' }}
              autoFocus
            />
          );
        }
        return null;
      default:
        return null;
    }
  };

  const columns: ColumnsType<ParamItem> = [
    {
      title: '参数名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Tooltip title={record.description}>
          <span>{text} <QuestionCircleOutlined style={{ color: '#999' }} /></span>
        </Tooltip>
      ),
    },
    {
      title: '参数键',
      dataIndex: 'key',
      key: 'key',
    },
    {
      title: '参数值',
      dataIndex: 'value',
      key: 'value',
      render: (_, record) => renderValueInput(record, editingKey === record.id),
    },
    {
      title: '默认值',
      dataIndex: 'defaultValue',
      key: 'defaultValue',
      render: (text, record) => {
        if (record.type === 'boolean') {
          return text === 'true' ? '是' : '否';
        }
        if (record.type === 'select' && record.options) {
          const optionMap: Record<string, string> = {
            'weekly': '每周',
            'biweekly': '每两周',
            'monthly': '每月',
          };
          return optionMap[text] || text;
        }
        return text;
      },
    },
    {
      title: '分组',
      dataIndex: 'group',
      key: 'group',
      render: (group) => getGroupText(group),
    },
    {
      title: '更新信息',
      key: 'updated',
      render: (_, record) => (
        <div>
          {record.updatedTime ? (
            <>
              <div>更新时间: {record.updatedTime}</div>
              <div>更新人: {record.updatedBy}</div>
            </>
          ) : (
            <span>未修改过</span>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => {
        if (editingKey === record.id) {
          return (
            <Space size="small">
              <a onClick={handleInlineCancel}>取消</a>
            </Space>
          );
        }
        return (
          <Space size="small">
            {record.editable && (
              <a onClick={() => handleInlineEdit(record)}><EditOutlined /> 编辑</a>
            )}
            {record.value !== record.defaultValue && (
              <a onClick={() => handleReset(record)}><ReloadOutlined /> 重置</a>
            )}
          </Space>
        );
      },
    },
  ];

  return (
    <PageContainer
      header={{
        title: '参数配置',
      }}
    >
      <Card bordered={false}>
        <Tabs 
          activeKey={activeTab} 
          onChange={handleTabChange}
          items={[
            {
              key: 'all',
              label: '全部',
            },
            {
              key: 'system',
              label: '系统设置',
            },
            {
              key: 'app',
              label: '应用设置',
            },
            {
              key: 'user',
              label: '用户设置',
            },
            {
              key: 'security',
              label: '安全设置',
            },
            {
              key: 'email',
              label: '邮件设置',
            },
            {
              key: 'storage',
              label: '存储设置',
            }
          ]}
        >
        </Tabs>

        <Form layout="inline" onFinish={handleSearch} style={{ marginBottom: 24 }}>
          <Form.Item name="keyword" label="关键词">
            <Input placeholder="参数名称/键/描述" style={{ width: 200 }} />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit">
              搜索
            </Button>
          </Form.Item>
        </Form>

        <Table
          columns={columns}
          dataSource={data?.data}
          rowKey="id"
          loading={loading}
          pagination={false}
        />
      </Card>

      {/* 编辑弹窗 */}
      <Modal
        title={`编辑参数: ${currentParam?.name}`}
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
      >
        {currentParam && (
          <Form form={editForm} layout="vertical" onFinish={handleEditSubmit}>
            <Form.Item label="参数键">
              <Input value={currentParam.key} disabled />
            </Form.Item>
            
            <Form.Item label="参数描述">
              <Input.TextArea value={currentParam.description} disabled rows={2} />
            </Form.Item>
            
            <Divider />
            
            <Form.Item
              name="value"
              label="参数值"
              rules={[{ required: true, message: '请输入参数值' }]}
            >
              {currentParam.type === 'string' && <Input />}
              {currentParam.type === 'number' && <InputNumber style={{ width: '100%' }} />}
              {currentParam.type === 'boolean' && <Switch />}
              {currentParam.type === 'select' && currentParam.options && (
                <Select>
                  {currentParam.options.map(opt => (
                    <Select.Option key={opt} value={opt}>
                      {opt === 'weekly' ? '每周' : opt === 'biweekly' ? '每两周' : opt === 'monthly' ? '每月' : opt}
                    </Select.Option>
                  ))}
                </Select>
              )}
            </Form.Item>
            
            <Form.Item label="默认值">
              {currentParam.type === 'boolean' ? (
                <Switch checked={currentParam.defaultValue === 'true'} disabled />
              ) : (
                <Input value={currentParam.defaultValue} disabled />
              )}
            </Form.Item>
            
            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                  保存
                </Button>
                <Button onClick={() => setEditModalVisible(false)}>
                  取消
                </Button>
                {currentParam.value !== currentParam.defaultValue && (
                  <Button danger onClick={() => handleReset(currentParam)}>
                    重置为默认值
                  </Button>
                )}
              </Space>
            </Form.Item>
          </Form>
        )}
      </Modal>
    </PageContainer>
  );
};

export default ParamsConfig;