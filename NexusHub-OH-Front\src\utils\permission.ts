import type { UserRole } from '@/access';

/**
 * 权限工具函数
 */

// 角色层级定义（数字越大权限越高）
const ROLE_LEVELS = {
  user: 1,
  developer: 2,
  operator: 3,
  reviewer: 3,
  admin: 5,
};

/**
 * 检查角色是否有足够的权限级别
 * @param userRole 用户角色
 * @param requiredRole 需要的角色
 * @returns 是否有权限
 */
export const hasRoleLevel = (userRole: UserRole, requiredRole: UserRole): boolean => {
  return ROLE_LEVELS[userRole] >= ROLE_LEVELS[requiredRole];
};

/**
 * 检查用户是否可以管理目标用户
 * @param managerRole 管理者角色
 * @param targetRole 目标用户角色
 * @returns 是否可以管理
 */
export const canManageUser = (managerRole: UserRole, targetRole: UserRole): boolean => {
  // 管理员可以管理所有人
  if (managerRole === 'admin') {
    return true;
  }
  
  // 运营人员可以管理普通用户和开发者
  if (managerRole === 'operator') {
    return ['user', 'developer'].includes(targetRole);
  }
  
  // 其他角色不能管理用户
  return false;
};

/**
 * 检查用户是否可以编辑应用
 * @param userRole 用户角色
 * @param appOwnerId 应用所有者ID
 * @param currentUserId 当前用户ID
 * @returns 是否可以编辑
 */
export const canEditApp = (
  userRole: UserRole,
  appOwnerId: string,
  currentUserId: string
): boolean => {
  // 管理员和运营人员可以编辑所有应用
  if (['admin', 'operator'].includes(userRole)) {
    return true;
  }
  
  // 开发者只能编辑自己的应用
  if (userRole === 'developer') {
    return appOwnerId === currentUserId;
  }
  
  return false;
};

/**
 * 检查用户是否可以删除应用
 * @param userRole 用户角色
 * @param appOwnerId 应用所有者ID
 * @param currentUserId 当前用户ID
 * @returns 是否可以删除
 */
export const canDeleteApp = (
  userRole: UserRole,
  appOwnerId: string,
  currentUserId: string
): boolean => {
  // 只有管理员可以删除应用
  return userRole === 'admin';
};

/**
 * 检查用户是否可以审核应用
 * @param userRole 用户角色
 * @returns 是否可以审核
 */
export const canAuditApp = (userRole: UserRole): boolean => {
  return ['admin', 'reviewer'].includes(userRole);
};

/**
 * 检查用户是否可以查看统计数据
 * @param userRole 用户角色
 * @param dataType 数据类型 ('all' | 'own')
 * @returns 是否可以查看
 */
export const canViewStatistics = (
  userRole: UserRole,
  dataType: 'all' | 'own' = 'all'
): boolean => {
  // 管理员、运营人员、审核员可以查看所有统计
  if (['admin', 'operator', 'reviewer'].includes(userRole)) {
    return true;
  }
  
  // 开发者只能查看自己的统计
  if (userRole === 'developer' && dataType === 'own') {
    return true;
  }
  
  return false;
};

/**
 * 获取用户可见的菜单项
 * @param userRole 用户角色
 * @returns 可见的菜单项列表
 */
export const getVisibleMenus = (userRole: UserRole): string[] => {
  const baseMenus = ['account'];
  
  switch (userRole) {
    case 'admin':
      return [
        ...baseMenus,
        'dashboard',
        'statistics',
        'user-management',
        'app',
        'review',
        'settings',
      ];
    
    case 'operator':
      return [
        ...baseMenus,
        'dashboard',
        'statistics',
        'user-management',
        'app',
        'review',
        'settings',
      ];
    
    case 'reviewer':
      return [
        ...baseMenus,
        'dashboard',
        'statistics',
        'user-management',
        'app',
        'review',
      ];
    
    case 'developer':
      return [
        ...baseMenus,
        'dashboard',
        'statistics',
        'app',
        'review',
      ];
    
    case 'user':
    default:
      return baseMenus;
  }
};

/**
 * 获取角色显示名称
 * @param role 角色
 * @returns 显示名称
 */
export const getRoleDisplayName = (role: UserRole): string => {
  const roleNames = {
    user: '普通用户',
    developer: '开发者',
    operator: '运营人员',
    reviewer: '审核员',
    admin: '管理员',
  };
  
  return roleNames[role] || role;
};

/**
 * 获取角色颜色
 * @param role 角色
 * @returns 颜色
 */
export const getRoleColor = (role: UserRole): string => {
  const roleColors = {
    user: 'default',
    developer: 'blue',
    operator: 'orange',
    reviewer: 'purple',
    admin: 'red',
  };
  
  return roleColors[role] || 'default';
};

/**
 * 检查是否为高级角色
 * @param role 角色
 * @returns 是否为高级角色
 */
export const isAdvancedRole = (role: UserRole): boolean => {
  return ['admin', 'operator', 'reviewer'].includes(role);
};

/**
 * 获取用户可以分配的角色列表
 * @param userRole 当前用户角色
 * @returns 可分配的角色列表
 */
export const getAssignableRoles = (userRole: UserRole): UserRole[] => {
  switch (userRole) {
    case 'admin':
      return ['user', 'developer', 'operator', 'reviewer', 'admin'];
    
    case 'operator':
      return ['user', 'developer'];
    
    default:
      return [];
  }
};