// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 获取应用的标签 获取应用的所有标签 GET /apps/${param0}/tags */
export async function getAppsIdTags(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAppsIdTagsParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.TagResponse[]>(`/apps/${param0}/tags`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 为应用添加标签 为应用添加多个标签 POST /apps/${param0}/tags */
export async function postAppsIdTags(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postAppsIdTagsParams,
  body: API.AppTagRequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.SuccessResponse>(`/apps/${param0}/tags`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 删除应用的标签 删除应用的指定标签 DELETE /apps/${param0}/tags/${param1} */
export async function deleteAppsIdTagsTagId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteAppsIdTagsTagIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, tag_id: param1, ...queryParams } = params;
  return request<API.SuccessResponse>(`/apps/${param0}/tags/${param1}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取应用标签列表 获取所有应用标签 GET /tags */
export async function getTags(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getTagsParams,
  options?: { [key: string]: any },
) {
  return request<API.TagResponse[]>('/tags', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建应用标签 创建一个新的应用标签 POST /tags */
export async function postTags(body: API.CreateTagRequest, options?: { [key: string]: any }) {
  return request<API.TagResponse>('/tags', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取应用标签 获取应用标签详情 GET /tags/${param0} */
export async function getTagsId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getTagsIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.TagAppCountResponse>(`/tags/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 更新应用标签 更新应用标签信息 PUT /tags/${param0} */
export async function putTagsId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putTagsIdParams,
  body: API.UpdateTagRequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.TagResponse>(`/tags/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 删除应用标签 删除应用标签 DELETE /tags/${param0} */
export async function deleteTagsId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteTagsIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.SuccessResponse>(`/tags/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取指定标签的应用 获取包含指定标签的所有应用 GET /tags/${param0}/apps */
export async function getTagsIdApps(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getTagsIdAppsParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.AppResponse[]>(`/tags/${param0}/apps`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}
