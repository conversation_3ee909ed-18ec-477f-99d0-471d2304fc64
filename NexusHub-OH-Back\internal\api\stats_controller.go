package api

import (
	"fmt"
	"strconv"

	"nexushub-oh-back/internal/models"
	"nexushub-oh-back/pkg/logger"
	"nexushub-oh-back/pkg/response"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// StatsController 统计控制器
type StatsController struct {
	DB *gorm.DB
}

// NewStatsController 创建统计控制器
func NewStatsController(db *gorm.DB) *StatsController {
	return &StatsController{
		DB: db,
	}
}

// DownloadRecordResponse 下载记录响应
type DownloadRecordResponse struct {
	ID            uint   `json:"id"`
	UserID        uint   `json:"user_id"`
	ApplicationID uint   `json:"application_id"`
	AppName       string `json:"app_name"`
	AppIcon       string `json:"app_icon"`
	VersionName   string `json:"version_name"`
	DeviceType    string `json:"device_type"`
	DeviceOS      string `json:"device_os"`
	DeviceModel   string `json:"device_model"`
	Status        string `json:"status"`
	CreatedAt     string `json:"created_at"`
}

// UserDownloadResponse 用户下载记录响应
type UserDownloadResponse struct {
	TotalDownloads int64                    `json:"total_downloads"`
	Records        []DownloadRecordResponse `json:"records"`
}

// AppDownloadStatsResponse 应用下载统计响应
type AppDownloadStatsResponse struct {
	TotalDownloads int64                        `json:"total_downloads"`
	DailyStats     []models.AppDownloadStats    `json:"daily_stats"`
	DeviceStats    []models.DeviceDownloadStats `json:"device_stats"`
}

// GetUserDownloads 获取用户的下载记录
//
//	@Summary		获取用户下载记录
//	@Description	获取当前登录用户的应用下载记录
//	@Tags			统计
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			page		query		int											false	"页码，默认1"	default(1)
//	@Param			page_size	query		int											false	"每页数量，默认20"	default(20)
//	@Success		200			{object}	response.PageResponse{data=[]DownloadRecordResponse}	"返回下载记录和分页信息"
//	@Failure		401			{object}	response.Response													"未授权"
//	@Failure		500			{object}	response.Response													"服务器错误"
//	@Router			/stats/downloads [get]
func (c *StatsController) GetUserDownloads(ctx *gin.Context) {
	// 获取用户ID（支持匿名用户）
	userIDInterface, exists := ctx.Get("user_id")
	var userID uint = 0 // 匿名用户使用0
	if exists {
		userID = userIDInterface.(uint)
	}

	// 解析分页参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// 获取用户下载记录
	records, total, err := models.GetUserDownloadRecords(c.DB, userID, page, pageSize)
	if err != nil {
		logger.Error("获取用户下载记录失败", zap.Error(err))
		response.InternalServerError(ctx, "获取下载记录失败，请稍后重试")
		return
	}

	// 构建响应
	result := make([]DownloadRecordResponse, 0, len(records))
	for _, record := range records {
		// 获取应用信息
		var app models.Application
		if err := c.DB.Select("name, icon").First(&app, record.ApplicationID).Error; err != nil {
			logger.Error("获取应用信息失败", zap.Error(err), zap.Uint("app_id", record.ApplicationID))
			continue
		}

		result = append(result, DownloadRecordResponse{
			ID:            record.ID,
			UserID:        record.UserID,
			ApplicationID: record.ApplicationID,
			AppName:       app.Name,
			AppIcon:       app.Icon,
			VersionName:   record.VersionName,
			DeviceType:    record.DeviceType,
			DeviceOS:      record.DeviceOS,
			DeviceModel:   record.DeviceModel,
			Status:        record.Status,
			CreatedAt:     record.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	response.SuccessWithPagination(ctx, result, total, page, pageSize)
}

// GetAppDownloadStats 获取应用下载统计
//
//	@Summary		获取应用下载统计
//	@Description	获取应用的下载统计数据，包括总数、每日统计和设备类型统计
//	@Tags			统计
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			id		path		int										true	"应用ID"
//	@Param			days	query		int										false	"统计天数，默认30天"	default(30)
//	@Success		200		{object}	response.Response{data=AppDownloadStatsResponse}	"返回下载统计数据"
//	@Failure		400		{object}	response.Response												"参数错误"
//	@Failure		401		{object}	response.Response												"未授权"
//	@Failure		403		{object}	response.Response												"无权限查看此应用统计"
//	@Failure		404		{object}	response.Response												"应用不存在"
//	@Failure		500		{object}	response.Response												"服务器错误"
//	@Router			/stats/apps/{id}/downloads [get]
func (c *StatsController) GetAppDownloadStats(ctx *gin.Context) {
	appID, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		response.BadRequest(ctx, "无效的应用ID")
		return
	}

	// 解析参数
	days, _ := strconv.Atoi(ctx.DefaultQuery("days", "30"))
	if days < 1 || days > 365 {
		days = 30
	}

	// 获取用户ID（支持匿名用户）
	userIDInterface, exists := ctx.Get("user_id")
	var actualUserID uint = 0 // 匿名用户使用0
	if exists {
		actualUserID = userIDInterface.(uint)
	}

	// 检查应用是否存在并且用户是否有权限查看
	var app models.Application
	if err := c.DB.First(&app, appID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "应用不存在")
		} else {
			response.InternalServerError(ctx, "获取应用信息失败")
		}
		return
	}

	// 只有应用开发者或管理员可以查看应用的下载统计
	userRole, _ := ctx.Get("role")
	if app.DeveloperID != actualUserID && userRole != string(models.UserRoleAdmin) {
		response.Forbidden(ctx, "您无权查看此应用的下载统计")
		return
	}

	// 获取统计数据
	// 1. 总下载数 - 直接从应用对象获取
	totalDownloads := app.DownloadCount

	// 2. 每日下载统计
	dailyStats, err := models.GetDailyDownloadStats(c.DB, uint(appID), days)
	if err != nil {
		logger.Error("获取应用每日下载统计失败", zap.Error(err))
		response.InternalServerError(ctx, "获取下载统计失败，请稍后重试")
		return
	}

	// 3. 设备类型统计
	deviceStats, err := models.GetDeviceTypeStats(c.DB, uint(appID))
	if err != nil {
		logger.Error("获取应用设备类型统计失败", zap.Error(err))
		response.InternalServerError(ctx, "获取下载统计失败，请稍后重试")
		return
	}

	// 返回统计数据
	response.Success(ctx, AppDownloadStatsResponse{
		TotalDownloads: totalDownloads,
		DailyStats:     dailyStats,
		DeviceStats:    deviceStats,
	})
}

// AppOverallStatsResponse 应用整体统计响应
type AppOverallStatsResponse struct {
	TotalApps      int64                `json:"total_apps"`
	TotalDownloads int64                `json:"total_downloads"`
	AverageRating  float64              `json:"average_rating"`
	CategoryStats  []CategoryStats      `json:"category_stats"`
	TopApps        []TopAppStats        `json:"top_apps"`
	DownloadTrends []DownloadTrendStats `json:"download_trends"`
	StatusStats    []StatusStats        `json:"status_stats"`
}

// CategoryStats 分类统计
type CategoryStats struct {
	Category string `json:"category"`
	Count    int64  `json:"count"`
}

// TopAppStats 热门应用统计
type TopAppStats struct {
	ID            uint    `json:"id"`
	Name          string  `json:"name"`
	Icon          string  `json:"icon"`
	DownloadCount int64   `json:"download_count"`
	Rating        float64 `json:"rating"`
	Category      string  `json:"category"`
}

// DownloadTrendStats 下载趋势统计
type DownloadTrendStats struct {
	Date      string `json:"date"`
	Downloads int64  `json:"downloads"`
}

// StatusStats 应用状态统计
type StatusStats struct {
	Status string `json:"status"`
	Count  int64  `json:"count"`
}

// GetAppOverallStats 获取应用整体统计
//
//	@Summary		获取应用整体统计
//	@Description	获取应用商店的整体统计数据，包括应用总数、下载总数、分类统计等
//	@Tags			统计
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			time_range		query		string									false	"时间范围：week, month, year"	default(month)
//	@Param			developer_id	query		int										false	"开发者ID，用于筛选特定开发者的应用统计"
//	@Success		200				{object}	response.Response{data=AppOverallStatsResponse}	"返回应用整体统计数据"
//	@Failure		401				{object}	response.Response												"未授权"
//	@Failure		500				{object}	response.Response												"服务器错误"
//	@Router			/stats/apps/overall [get]
func (c *StatsController) GetAppOverallStats(ctx *gin.Context) {
	// 获取查询参数
	timeRange := ctx.DefaultQuery("time_range", "month")
	developerIDStr := ctx.Query("developer_id")

	// 获取用户信息
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "")
		return
	}

	userRole, _ := ctx.Get("role")

	// 构建查询条件
	var whereConditions []string
	var whereArgs []interface{}
	var totalAppsWhereConditions []string
	var totalAppsWhereArgs []interface{}

	// 如果指定了开发者ID，添加过滤条件
	if developerIDStr != "" {
		developerID, err := strconv.Atoi(developerIDStr)
		if err != nil {
			response.BadRequest(ctx, "无效的开发者ID")
			return
		}

		// 检查权限：只有管理员或开发者本人可以查看特定开发者的统计
		if userRole != string(models.UserRoleAdmin) && userID.(uint) != uint(developerID) {
			response.Forbidden(ctx, "您无权查看此开发者的统计数据")
			return
		}

		// 为已发布应用统计添加条件
		whereConditions = append(whereConditions, "status = ?")
		whereArgs = append(whereArgs, string(models.ApplicationStatusApproved))
		whereConditions = append(whereConditions, "developer_id = ?")
		whereArgs = append(whereArgs, developerID)

		// 为总应用数统计添加条件（不限制状态）
		totalAppsWhereConditions = append(totalAppsWhereConditions, "developer_id = ?")
		totalAppsWhereArgs = append(totalAppsWhereArgs, developerID)
	} else if userRole != string(models.UserRoleAdmin) {
		// 非管理员只能查看自己的应用统计
		// 为已发布应用统计添加条件
		whereConditions = append(whereConditions, "status = ?")
		whereArgs = append(whereArgs, string(models.ApplicationStatusApproved))
		whereConditions = append(whereConditions, "developer_id = ?")
		whereArgs = append(whereArgs, userID.(uint))

		// 为总应用数统计添加条件（不限制状态）
		totalAppsWhereConditions = append(totalAppsWhereConditions, "developer_id = ?")
		totalAppsWhereArgs = append(totalAppsWhereArgs, userID.(uint))
	} else {
		// 管理员查看所有应用
		// 为已发布应用统计添加条件
		whereConditions = append(whereConditions, "status = ?")
		whereArgs = append(whereArgs, string(models.ApplicationStatusApproved))
		// 总应用数不添加任何条件，统计所有应用
	}

	// 1. 获取应用总数（统计所有状态的应用）
	var totalApps int64
	totalAppsQuery := c.DB.Model(&models.Application{})
	for i, condition := range totalAppsWhereConditions {
		totalAppsQuery = totalAppsQuery.Where(condition, totalAppsWhereArgs[i])
	}
	if err := totalAppsQuery.Count(&totalApps).Error; err != nil {
		logger.Error("获取应用总数失败", zap.Error(err))
		response.InternalServerError(ctx, "获取统计数据失败")
		return
	}

	// 2. 获取总下载数和平均评分
	var stats struct {
		TotalDownloads int64   `json:"total_downloads"`
		AverageRating  float64 `json:"average_rating"`
	}

	sqlQuery := "SELECT COALESCE(SUM(download_count), 0) as total_downloads, COALESCE(AVG(NULLIF(average_rating, 0)), 0) as average_rating FROM applications WHERE " +
		"status = ?"
	sqlArgs := []interface{}{string(models.ApplicationStatusApproved)}

	if developerIDStr != "" {
		sqlQuery += " AND developer_id = ?"
		sqlArgs = append(sqlArgs, developerIDStr)
	} else if userRole != string(models.UserRoleAdmin) {
		sqlQuery += " AND developer_id = ?"
		sqlArgs = append(sqlArgs, userID.(uint))
	}

	if err := c.DB.Raw(sqlQuery, sqlArgs...).Scan(&stats).Error; err != nil {
		logger.Error("获取下载和评分统计失败", zap.Error(err))
		response.InternalServerError(ctx, "获取统计数据失败")
		return
	}

	// 3. 获取分类统计
	var categoryStats []CategoryStats
	categorySql := "SELECT category, COUNT(*) as count FROM applications WHERE status = ?"
	categoryArgs := []interface{}{string(models.ApplicationStatusApproved)}

	if developerIDStr != "" {
		categorySql += " AND developer_id = ?"
		categoryArgs = append(categoryArgs, developerIDStr)
	} else if userRole != string(models.UserRoleAdmin) {
		categorySql += " AND developer_id = ?"
		categoryArgs = append(categoryArgs, userID.(uint))
	}

	categorySql += " GROUP BY category ORDER BY count DESC"

	if err := c.DB.Raw(categorySql, categoryArgs...).Scan(&categoryStats).Error; err != nil {
		logger.Error("获取分类统计失败", zap.Error(err))
		response.InternalServerError(ctx, "获取统计数据失败")
		return
	}

	// 4. 获取热门应用（按下载量排序，取前10个）
	var topApps []TopAppStats
	topAppsSql := "SELECT id, name, icon, download_count, average_rating, category FROM applications WHERE status = ?"
	topAppsArgs := []interface{}{string(models.ApplicationStatusApproved)}

	if developerIDStr != "" {
		topAppsSql += " AND developer_id = ?"
		topAppsArgs = append(topAppsArgs, developerIDStr)
	} else if userRole != string(models.UserRoleAdmin) {
		topAppsSql += " AND developer_id = ?"
		topAppsArgs = append(topAppsArgs, userID.(uint))
	}

	topAppsSql += " ORDER BY download_count DESC LIMIT 10"

	if err := c.DB.Raw(topAppsSql, topAppsArgs...).Scan(&topApps).Error; err != nil {
		logger.Error("获取热门应用失败", zap.Error(err))
		response.InternalServerError(ctx, "获取统计数据失败")
		return
	}

	// 5. 获取下载趋势（根据时间范围）
	var downloadTrends []DownloadTrendStats
	days := 30 // 默认30天
	switch timeRange {
	case "week":
		days = 7
	case "month":
		days = 30
	case "year":
		days = 365
	}

	// 构建下载趋势查询
	trendSql := fmt.Sprintf(`
		SELECT 
			DATE(dr.created_at) as date,
			COUNT(*) as downloads
		FROM 
			download_records dr
			JOIN applications a ON dr.application_id = a.id
		WHERE 
			a.status = ?
			AND dr.created_at >= NOW() - INTERVAL '%d days'
	`, days)
	trendArgs := []interface{}{string(models.ApplicationStatusApproved)}

	if developerIDStr != "" {
		trendSql += " AND a.developer_id = ?"
		trendArgs = append(trendArgs, developerIDStr)
	} else if userRole != string(models.UserRoleAdmin) {
		trendSql += " AND a.developer_id = ?"
		trendArgs = append(trendArgs, userID.(uint))
	}

	trendSql += " GROUP BY date ORDER BY date DESC"

	if err := c.DB.Raw(trendSql, trendArgs...).Scan(&downloadTrends).Error; err != nil {
		logger.Error("获取下载趋势失败", zap.Error(err))
		// 下载趋势失败不影响其他数据，设置为空数组
		downloadTrends = []DownloadTrendStats{}
	}

	// 6. 获取应用状态统计
	var statusStats []StatusStats
	statusSql := `
		SELECT 
			status,
			COUNT(*) as count
		FROM 
			applications
		WHERE 
			1=1
	`
	statusArgs := []interface{}{}

	if developerIDStr != "" {
		statusSql += " AND developer_id = ?"
		statusArgs = append(statusArgs, developerIDStr)
	} else if userRole != string(models.UserRoleAdmin) {
		statusSql += " AND developer_id = ?"
		statusArgs = append(statusArgs, userID.(uint))
	}

	statusSql += " GROUP BY status ORDER BY count DESC"

	if err := c.DB.Raw(statusSql, statusArgs...).Scan(&statusStats).Error; err != nil {
		logger.Error("获取应用状态统计失败", zap.Error(err))
		// 状态统计失败不影响其他数据，设置为空数组
		statusStats = []StatusStats{}
	}

	// 构建响应
	statsResponse := AppOverallStatsResponse{
		TotalApps:      totalApps,
		TotalDownloads: stats.TotalDownloads,
		AverageRating:  stats.AverageRating,
		CategoryStats:  categoryStats,
		TopApps:        topApps,
		DownloadTrends: downloadTrends,
		StatusStats:    statusStats,
	}

	response.Success(ctx, statsResponse)
}

// RecordDownload 记录下载
//
//	@Summary		记录应用下载
//	@Description	记录用户下载应用的行为，更新下载统计
//	@Tags			统计
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			id			path		int			true	"应用ID"
//	@Param			version_id	path		int			true	"版本ID"
//	@Success		200			{object}	response.Response	"记录成功"
//	@Failure		400			{object}	response.Response	"参数错误"
//	@Failure		401			{object}	response.Response	"未授权"
//	@Failure		404			{object}	response.Response	"应用或版本不存在"
//	@Failure		500			{object}	response.Response	"服务器错误"
//	@Router			/apps/{id}/versions/{version_id}/download [post]
func (c *StatsController) RecordDownload(ctx *gin.Context) {
	appID, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		response.BadRequest(ctx, "无效的应用ID")
		return
	}

	versionID, err := strconv.Atoi(ctx.Param("version_id"))
	if err != nil {
		response.BadRequest(ctx, "无效的版本ID")
		return
	}

	// 获取用户ID（支持匿名用户）
	userIDInterface, exists := ctx.Get("user_id")
	var userID uint = 0 // 匿名用户使用0
	if exists {
		userID = userIDInterface.(uint)
	}

	// 检查应用是否存在
	var app models.Application
	if err := c.DB.First(&app, appID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "应用不存在")
		} else {
			response.InternalServerError(ctx, "获取应用信息失败")
		}
		return
	}

	// 检查版本是否存在
	var version models.AppVersion
	if err := c.DB.First(&version, versionID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "版本不存在")
		} else {
			response.InternalServerError(ctx, "获取版本信息失败")
		}
		return
	}

	// 确认版本属于当前应用
	if version.ApplicationID != uint(appID) {
		response.BadRequest(ctx, "版本不属于当前应用")
		return
	}

	// 获取客户端信息
	ip := ctx.ClientIP()
	userAgent := ctx.Request.UserAgent()

	// 简单提取设备信息（实际应用中应该使用更复杂的User-Agent解析）
	deviceType := "unknown"
	deviceOS := "unknown"
	deviceModel := "unknown"

	// 创建下载记录
	record := &models.DownloadRecord{
		UserID:        userID,
		ApplicationID: uint(appID),
		AppVersionID:  uint(versionID),
		VersionName:   version.VersionName,
		IP:            ip,
		UserAgent:     userAgent,
		DeviceType:    deviceType,
		DeviceOS:      deviceOS,
		DeviceModel:   deviceModel,
		Status:        "success",
	}

	if err := models.CreateDownloadRecord(c.DB, record); err != nil {
		logger.Error("创建下载记录失败", zap.Error(err))
		response.InternalServerError(ctx, "记录下载失败，请稍后重试")
		return
	}

	response.SuccessWithMessage(ctx, "下载记录已保存", nil)
}
