# 初始化搜索索引的PowerShell脚本

$headers = @{
    'Content-Type' = 'application/json'
    'Authorization' = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiaXNzIjoibmV4dXNodWIiLCJzdWIiOiIxIiwiZXhwIjoxNzQ5MzU2MTY1LCJuYmYiOjE3NDkyNjk3NjUsImlhdCI6MTc0OTI2OTc2NX0.ih2EzCnivxygkSSeqbBoo9AjcLyhPEeJMc6EKgEQUD4'
}

try {
    Write-Host "正在初始化所有搜索索引..."
    $response = Invoke-RestMethod -Uri 'http://localhost:8080/api/v1/admin/search/initialize-all' -Method POST -Headers $headers
    Write-Host "初始化成功: $($response | ConvertTo-Json)"
} catch {
    Write-Host "初始化失败: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "响应内容: $responseBody"
    }
}