import React, { useState, useEffect } from 'react';
import { List, Switch, message, Spin, Card } from 'antd';
import { 
  getNotificationSettings, 
  updateNotificationSettings,
  type NotificationSettings 
} from '@/services/ant-design-pro/notifications';

type Unpacked<T> = T extends (infer U)[] ? U : T;

const NotificationView: React.FC = () => {
  const [settings, setSettings] = useState<NotificationSettings | null>(null);
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState<string | null>(null);

  // 获取通知设置
  const fetchSettings = async () => {
    setLoading(true);
    try {
      const response = await getNotificationSettings();
      if (response.success && response.data) {
        setSettings(response.data);
      }
    } catch (error) {
      message.error('获取通知设置失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新设置
  const handleSettingChange = async (key: keyof NotificationSettings, value: boolean) => {
    if (!settings) return;
    
    setUpdating(key);
    try {
      const response = await updateNotificationSettings({ [key]: value });
      if (response.success) {
        setSettings(prev => prev ? { ...prev, [key]: value } : null);
        message.success('设置更新成功');
      }
    } catch (error) {
      message.error('设置更新失败');
    } finally {
      setUpdating(null);
    }
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  const getData = () => {
    if (!settings) return [];
    
    return [
      {
        key: 'account_security',
        title: '账户安全',
        description: '账户密码变更、登录异常等安全相关通知',
        checked: settings.account_security,
      },
      {
        key: 'system_messages',
        title: '系统消息',
        description: '系统公告、维护通知等重要消息',
        checked: settings.system_messages,
      },
      {
        key: 'task_reminders',
        title: '任务提醒',
        description: '应用审核、开发者认证等任务状态变更提醒',
        checked: settings.task_reminders,
      },
      {
        key: 'email_notifications',
        title: '邮件通知',
        description: '重要通知同时发送到您的邮箱',
        checked: settings.email_notifications,
      },
    ];
  };

  const data = getData();

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  return (
    <Card title="通知设置" extra={<span style={{ fontSize: '14px', color: '#666' }}>管理您接收通知的方式</span>}>
      <List<Unpacked<typeof data>>
        itemLayout="horizontal"
        dataSource={data}
        renderItem={(item) => {
          const isUpdating = updating === item.key;
          return (
            <List.Item 
              actions={[
                <Switch 
                  key="switch"
                  checkedChildren="开" 
                  unCheckedChildren="关" 
                  checked={item.checked}
                  loading={isUpdating}
                  onChange={(checked) => handleSettingChange(item.key as keyof NotificationSettings, checked)}
                />
              ]}
            >
              <List.Item.Meta 
                title={item.title} 
                description={item.description} 
              />
            </List.Item>
          );
        }}
      />
    </Card>
  );
};

export default NotificationView;
