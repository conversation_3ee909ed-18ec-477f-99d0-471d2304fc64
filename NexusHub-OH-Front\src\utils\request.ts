import { history, RequestConfig } from '@umijs/max';
import { message, notification } from 'antd';
import { RequestOptions } from '@@/plugin-request/request';

// 错误处理方案
const codeMessage: Record<number, string> = {
  200: '服务器成功返回请求的数据。',
  201: '新建或修改数据成功。',
  202: '一个请求已经进入后台排队（异步任务）。',
  204: '删除数据成功。',
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  401: '用户没有权限（令牌、用户名、密码错误）。',
  403: '用户得到授权，但是访问是被禁止的。',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。',
};

// 接口基本路径配置
export const API_BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://api.nexushub-oh.top/api/v1'  // 生产环境
  : '/api/v1';  // 开发环境（走代理）

// 全局请求错误处理
const errorHandler = (error: any) => {
  const { response } = error;
  if (response && response.status) {
    const errorText = codeMessage[response.status] || response.statusText;
    const { status, url } = response;

    notification.error({
      message: `请求错误 ${status}: ${url}`,
      description: errorText,
    });

    // 401未授权时跳转到登录页
    if (status === 401) {
      message.error('您的登录已过期，请重新登录');
      history.push('/user/login');
    }
  } else if (!response) {
    notification.error({
      description: '您的网络发生异常，无法连接服务器',
      message: '网络异常',
    });
  }
  return Promise.reject(error);
};

// 请求拦截器配置
const requestInterceptor = (config: RequestOptions) => {
  // 获取token
  const token = localStorage.getItem('token');
  
  // 添加token到请求头
  if (token) {
    return {
      ...config,
      headers: {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      },
    };
  }
  
  return config;
};

// 导出配置
export const requestConfig: RequestConfig = {
  errorHandler,
  requestInterceptors: [requestInterceptor],
  // 默认前缀
  prefix: API_BASE_URL,
};

export default requestConfig;

 