package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"nexushub-oh-back/internal/models"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// NotificationService 通知服务
type NotificationService struct {
	db             *gorm.DB
	messageService *MessageService
	logger         *zap.Logger
}

// NewNotificationService 创建通知服务实例
func NewNotificationService(db *gorm.DB, messageService *MessageService, logger *zap.Logger) *NotificationService {
	return &NotificationService{
		db:             db,
		messageService: messageService,
		logger:         logger,
	}
}

// GetDB 获取数据库实例
func (s *NotificationService) GetDB() *gorm.DB {
	return s.db
}

// NotificationRequest 通知请求结构
type NotificationRequest struct {
	UserID      uint                    `json:"user_id"`
	Title       string                  `json:"title"`
	Content     string                  `json:"content"`
	Type        models.NotificationType `json:"type"`
	Category    string                  `json:"category"`
	RelatedID   uint                    `json:"related_id,omitempty"`
	RelatedType string                  `json:"related_type,omitempty"`
	Priority    models.Priority         `json:"priority"`
	ExpiresAt   *time.Time              `json:"expires_at,omitempty"`
	Metadata    map[string]interface{}  `json:"metadata,omitempty"`
	SendEmail   bool                    `json:"send_email,omitempty"`
}

// CreateNotification 创建通知
func (s *NotificationService) CreateNotification(ctx context.Context, req *NotificationRequest) (*models.Notification, error) {
	// 序列化元数据
	var metadataJSON string
	if req.Metadata != nil {
		metadataBytes, err := json.Marshal(req.Metadata)
		if err != nil {
			s.logger.Error("序列化通知元数据失败", zap.Error(err))
			return nil, fmt.Errorf("序列化元数据失败: %w", err)
		}
		metadataJSON = string(metadataBytes)
	}

	// 创建通知记录
	notification := &models.Notification{
		UserID:      req.UserID,
		Title:       req.Title,
		Content:     req.Content,
		Type:        req.Type,
		Category:    req.Category,
		RelatedID:   req.RelatedID,
		RelatedType: req.RelatedType,
		Priority:    req.Priority,
		ExpiresAt:   req.ExpiresAt,
		Metadata:    metadataJSON,
	}

	// 保存到数据库
	if err := models.CreateNotification(s.db, notification); err != nil {
		s.logger.Error("创建通知失败", zap.Error(err), zap.Uint("user_id", req.UserID))
		return nil, fmt.Errorf("创建通知失败: %w", err)
	}

	// 发送Web通知（通过消息队列）
	if err := s.sendWebNotification(ctx, notification); err != nil {
		s.logger.Error("发送Web通知失败", zap.Error(err), zap.Uint("notification_id", notification.ID))
	}

	// 检查是否需要发送邮件通知
	if req.SendEmail {
		if err := s.sendEmailNotification(ctx, notification); err != nil {
			s.logger.Error("发送邮件通知失败", zap.Error(err), zap.Uint("notification_id", notification.ID))
		}
	}

	s.logger.Info("通知创建成功",
		zap.Uint("notification_id", notification.ID),
		zap.Uint("user_id", req.UserID),
		zap.String("category", req.Category),
	)

	return notification, nil
}

// sendWebNotification 发送Web通知
func (s *NotificationService) sendWebNotification(ctx context.Context, notification *models.Notification) error {
	return s.messageService.PublishNotificationMessage(
		ctx,
		notification.UserID,
		notification.Title,
		notification.Content,
		string(notification.Type),
	)
}

// sendEmailNotification 发送邮件通知
func (s *NotificationService) sendEmailNotification(ctx context.Context, notification *models.Notification) error {
	// 获取用户信息
	var user models.User
	if err := s.db.First(&user, notification.UserID).Error; err != nil {
		return fmt.Errorf("获取用户信息失败: %w", err)
	}

	// 检查用户通知设置
	settings, err := models.GetOrCreateNotificationSettings(s.db, notification.UserID)
	if err != nil {
		return fmt.Errorf("获取通知设置失败: %w", err)
	}

	// 检查是否启用了邮件通知
	if !s.shouldSendEmail(settings, notification.Category) {
		s.logger.Info("用户未启用此类邮件通知",
			zap.Uint("user_id", notification.UserID),
			zap.String("category", notification.Category),
		)
		return nil
	}

	// 发送邮件
	err = s.messageService.PublishEmailMessage(
		ctx,
		user.Email,
		notification.Title,
		notification.Content,
		false, // isHTML
	)

	if err == nil {
		// 标记邮件已发送
		now := time.Now()
		s.db.Model(notification).Updates(map[string]interface{}{
			"email_sent":    true,
			"email_sent_at": &now,
		})
	}

	return err
}

// shouldSendEmail 检查是否应该发送邮件
func (s *NotificationService) shouldSendEmail(settings *models.NotificationSettings, category string) bool {
	if !settings.EmailNotificationEnabled {
		return false
	}

	switch category {
	case models.CategoryAppReview:
		return settings.AppReviewEmailEnabled
	case models.CategoryDeveloperVerify:
		return settings.DeveloperVerifyEmailEnabled
	case models.CategoryVersionUpdate:
		return settings.VersionUpdateEmailEnabled
	case models.CategorySystem:
		return settings.SystemEmailEnabled
	default:
		return false
	}
}

// GetUserNotifications 获取用户通知列表
func (s *NotificationService) GetUserNotifications(userID uint, page, pageSize int, unreadOnly bool) ([]models.Notification, int64, error) {
	return models.GetUserNotifications(s.db, userID, page, pageSize, unreadOnly)
}

// MarkAsRead 标记通知为已读
func (s *NotificationService) MarkAsRead(notificationID, userID uint) error {
	return models.MarkNotificationAsRead(s.db, notificationID, userID)
}

// MarkAllAsRead 标记所有通知为已读
func (s *NotificationService) MarkAllAsRead(userID uint) error {
	return models.MarkAllNotificationsAsRead(s.db, userID)
}

// GetUnreadCount 获取未读通知数量
func (s *NotificationService) GetUnreadCount(userID uint) (int64, error) {
	return models.GetUnreadNotificationCount(s.db, userID)
}

// GetNotificationSettings 获取用户通知设置
func (s *NotificationService) GetNotificationSettings(userID uint) (*models.NotificationSettings, error) {
	return models.GetOrCreateNotificationSettings(s.db, userID)
}

// UpdateNotificationSettings 更新用户通知设置
func (s *NotificationService) UpdateNotificationSettings(userID uint, settings *models.NotificationSettings) error {
	return models.UpdateNotificationSettings(s.db, userID, settings)
}

// 预定义的通知模板

// CreateAppReviewNotification 创建应用审核通知
func (s *NotificationService) CreateAppReviewNotification(ctx context.Context, userID uint, appName string, status string, reason string) error {
	var title, content string
	var notificationType models.NotificationType
	var priority models.Priority

	switch status {
	case "approved":
		title = "应用审核通过"
		content = fmt.Sprintf("恭喜！您的应用 \"%s\" 已通过审核，现在可以在应用商店中下载了。", appName)
		notificationType = models.NotificationTypeSuccess
		priority = models.PriorityNormal
	case "rejected":
		title = "应用审核未通过"
		content = fmt.Sprintf("很抱歉，您的应用 \"%s\" 未通过审核。原因：%s", appName, reason)
		notificationType = models.NotificationTypeError
		priority = models.PriorityHigh
	case "submitted":
		title = "应用提交成功"
		content = fmt.Sprintf("您的应用 \"%s\" 已提交审核，我们将在1-3个工作日内完成审核。", appName)
		notificationType = models.NotificationTypeInfo
		priority = models.PriorityNormal
	default:
		return fmt.Errorf("未知的审核状态: %s", status)
	}

	req := &NotificationRequest{
		UserID:   userID,
		Title:    title,
		Content:  content,
		Type:     notificationType,
		Category: models.CategoryAppReview,
		Priority: priority,
		Metadata: map[string]interface{}{
			"app_name": appName,
			"status":   status,
			"reason":   reason,
		},
		SendEmail: status == "approved" || status == "rejected", // 重要状态发送邮件
	}

	_, err := s.CreateNotification(ctx, req)
	return err
}

// NotifyAdminsForDeveloperSubmission 通知所有管理员有新的开发者认证申请
func (s *NotificationService) NotifyAdminsForDeveloperSubmission(ctx context.Context, developerID uint, developerName, username string) error {
	// 获取所有管理员
	admins, err := models.GetAdminUsers(s.db)
	if err != nil {
		s.logger.Error("获取管理员列表失败", zap.Error(err))
		return fmt.Errorf("获取管理员列表失败: %w", err)
	}

	if len(admins) == 0 {
		s.logger.Warn("没有找到管理员用户")
		return nil
	}

	title := "新的开发者认证申请"
	content := fmt.Sprintf("用户 %s (%s) 提交了开发者认证申请，请及时审核。", username, developerName)

	// 为每个管理员创建通知
	for _, admin := range admins {
		req := &NotificationRequest{
			UserID:   admin.ID,
			Title:    title,
			Content:  content,
			Type:     models.NotificationTypeInfo,
			Category: models.CategoryDeveloperVerify,
			Priority: models.PriorityHigh,
			Metadata: map[string]interface{}{
				"developer_id":   developerID,
				"developer_name": developerName,
				"username":       username,
				"action":         "submitted",
			},
			SendEmail: true, // 管理员通知发送邮件
		}

		if _, err := s.CreateNotification(ctx, req); err != nil {
			s.logger.Error("为管理员创建通知失败", 
				zap.Error(err), 
				zap.Uint("admin_id", admin.ID),
				zap.Uint("developer_id", developerID))
			// 继续为其他管理员创建通知，不因单个失败而中断
		}
	}

	s.logger.Info("已通知所有管理员新的开发者认证申请", 
		zap.Int("admin_count", len(admins)),
		zap.Uint("developer_id", developerID))

	return nil
}

// CreateDeveloperVerifyNotification 创建开发者认证通知
func (s *NotificationService) CreateDeveloperVerifyNotification(ctx context.Context, userID uint, status string, reason string) error {
	var title, content string
	var notificationType models.NotificationType
	var priority models.Priority

	switch status {
	case "approved":
		title = "开发者认证通过"
		content = "恭喜！您的开发者认证已通过，现在可以发布应用了。"
		notificationType = models.NotificationTypeSuccess
		priority = models.PriorityHigh
	case "rejected":
		title = "开发者认证未通过"
		content = fmt.Sprintf("很抱歉，您的开发者认证未通过。原因：%s", reason)
		notificationType = models.NotificationTypeError
		priority = models.PriorityHigh
	case "submitted":
		title = "开发者认证提交成功"
		content = "您的开发者认证申请已提交，我们将在3-5个工作日内完成审核。"
		notificationType = models.NotificationTypeInfo
		priority = models.PriorityNormal
	default:
		return fmt.Errorf("未知的认证状态: %s", status)
	}

	req := &NotificationRequest{
		UserID:   userID,
		Title:    title,
		Content:  content,
		Type:     notificationType,
		Category: models.CategoryDeveloperVerify,
		Priority: priority,
		Metadata: map[string]interface{}{
			"status": status,
			"reason": reason,
		},
		SendEmail: true, // 开发者认证结果都发送邮件
	}

	_, err := s.CreateNotification(ctx, req)
	return err
}

// CreateVersionUpdateNotification 创建版本更新通知
func (s *NotificationService) CreateVersionUpdateNotification(ctx context.Context, userID uint, appName, version string, status string, reason string) error {
	var title, content string
	var notificationType models.NotificationType
	var priority models.Priority

	switch status {
	case "approved":
		title = "版本更新审核通过"
		content = fmt.Sprintf("您的应用 \"%s\" 版本 %s 已通过审核，用户现在可以更新到最新版本了。", appName, version)
		notificationType = models.NotificationTypeSuccess
		priority = models.PriorityNormal
	case "rejected":
		title = "版本更新审核未通过"
		content = fmt.Sprintf("您的应用 \"%s\" 版本 %s 未通过审核。原因：%s", appName, version, reason)
		notificationType = models.NotificationTypeError
		priority = models.PriorityHigh
	case "submitted":
		title = "版本更新提交成功"
		content = fmt.Sprintf("您的应用 \"%s\" 版本 %s 已提交审核，我们将在1-2个工作日内完成审核。", appName, version)
		notificationType = models.NotificationTypeInfo
		priority = models.PriorityNormal
	default:
		return fmt.Errorf("未知的审核状态: %s", status)
	}

	req := &NotificationRequest{
		UserID:   userID,
		Title:    title,
		Content:  content,
		Type:     notificationType,
		Category: models.CategoryVersionUpdate,
		Priority: priority,
		Metadata: map[string]interface{}{
			"app_name": appName,
			"version":  version,
			"status":   status,
			"reason":   reason,
		},
		SendEmail: status == "approved" || status == "rejected", // 重要状态发送邮件
	}

	_, err := s.CreateNotification(ctx, req)
	return err
}
