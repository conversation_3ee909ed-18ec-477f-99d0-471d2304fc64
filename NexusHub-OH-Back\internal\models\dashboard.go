package models

import (
	"time"

	"gorm.io/gorm"
)

// AnalyticsSummary 分析页数据摘要
type AnalyticsSummary struct {
	TotalUsers          int64 `json:"total_users"`           // 总用户数
	TotalApps           int64 `json:"total_apps"`            // 总应用数
	TotalDownloads      int64 `json:"total_downloads"`       // 总下载量
	TotalReviews        int64 `json:"total_reviews"`         // 总评论数
	TotalDevelopers     int64 `json:"total_developers"`      // 总开发者数
	NewUsersToday       int64 `json:"new_users_today"`       // 今日新增用户
	NewAppsToday        int64 `json:"new_apps_today"`        // 今日新增应用
	NewDownloadsToday   int64 `json:"new_downloads_today"`   // 今日新增下载
	PendingAppsCount    int64 `json:"pending_apps_count"`    // 待审核应用数
	PendingReviewsCount int64 `json:"pending_reviews_count"` // 待审核评论数
}

// DateValue 日期值对
type DateValue struct {
	Date  string `json:"date"`  // 日期，格式 YYYY-MM-DD
	Value int64  `json:"value"` // 值
}

// AnalyticsTrend 趋势分析数据
type AnalyticsTrend struct {
	UserTrend      []DateValue `json:"user_trend"`      // 用户增长趋势
	AppTrend       []DateValue `json:"app_trend"`       // 应用增长趋势
	DownloadTrend  []DateValue `json:"download_trend"`  // 下载增长趋势
	DeveloperTrend []DateValue `json:"developer_trend"` // 开发者增长趋势
}

// CategoryStats 分类统计
type CategoryStats struct {
	CategoryID    uint   `json:"category_id"`    // 分类ID
	CategoryName  string `json:"category_name"`  // 分类名称
	AppCount      int64  `json:"app_count"`      // 应用数量
	DownloadCount int64  `json:"download_count"` // 下载数量
}

// MonitoringSummary 监控页面摘要数据
type MonitoringSummary struct {
	ServerStatus        string  `json:"server_status"`         // 服务器状态
	CPUUsage            float64 `json:"cpu_usage"`             // CPU使用率
	MemoryUsage         float64 `json:"memory_usage"`          // 内存使用率
	DiskUsage           float64 `json:"disk_usage"`            // 磁盘使用率
	DatabaseConnections int     `json:"database_connections"`  // 数据库连接数
	AverageResponseTime float64 `json:"average_response_time"` // 平均响应时间(ms)
	RequestsPerMinute   int64   `json:"requests_per_minute"`   // 每分钟请求数
	ErrorRate           float64 `json:"error_rate"`            // 错误率
	UptimeHours         float64 `json:"uptime_hours"`          // 运行时间(小时)
}

// SystemLog 系统日志
type SystemLog struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	Level     string    `json:"level"`      // 日志级别：info, warning, error, critical
	Source    string    `json:"source"`     // 日志来源
	Message   string    `json:"message"`    // 日志消息
	CreatedAt time.Time `json:"created_at"` // 创建时间
}

// AlertEvent 告警事件
type AlertEvent struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Severity    string    `json:"severity"`    // 严重程度：low, medium, high, critical
	Type        string    `json:"type"`        // 告警类型
	Description string    `json:"description"` // 告警描述
	Status      string    `json:"status"`      // 状态：active, resolved
	CreatedAt   time.Time `json:"created_at"`  // 创建时间
	ResolvedAt  time.Time `json:"resolved_at"` // 解决时间
}

// WorkbenchSummary 工作台摘要
type WorkbenchSummary struct {
	MyAppCount         int64   `json:"my_app_count"`         // 我的应用数量
	TotalDownloads     int64   `json:"total_downloads"`      // 我的应用总下载量
	AverageRating      float64 `json:"average_rating"`       // 我的应用平均评分
	NewReviewsCount    int64   `json:"new_reviews_count"`    // 我的应用新评论数量
	PendingAppsCount   int64   `json:"pending_apps_count"`   // 我的待审核应用数量
	CompletedTaskCount int64   `json:"completed_task_count"` // 已完成任务数量
	TotalTaskCount     int64   `json:"total_task_count"`     // 总任务数量
}

// TaskItem 任务项
type TaskItem struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	UserID      uint      `json:"user_id"`      // 任务所属用户ID
	Title       string    `json:"title"`        // 任务标题
	Description string    `json:"description"`  // 任务描述
	Status      string    `json:"status"`       // 任务状态：pending, in_progress, completed
	Priority    string    `json:"priority"`     // 优先级：low, medium, high
	DueDate     time.Time `json:"due_date"`     // 截止日期
	CreatedAt   time.Time `json:"created_at"`   // 创建时间
	UpdatedAt   time.Time `json:"updated_at"`   // 更新时间
	CompletedAt time.Time `json:"completed_at"` // 完成时间
}

// RecentActivity 最近活动
type RecentActivity struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    uint      `json:"user_id"`    // 活动所属用户ID
	Type      string    `json:"type"`       // 活动类型：app_create, app_update, review, download, etc.
	Content   string    `json:"content"`    // 活动内容
	CreatedAt time.Time `json:"created_at"` // 创建时间
}

// PopularApp 热门应用
type PopularApp struct {
	AppID         uint    `json:"app_id"`
	AppName       string  `json:"app_name"`
	AppIcon       string  `json:"app_icon"`
	DeveloperName string  `json:"developer_name"`
	DownloadCount int64   `json:"download_count"`
	Rating        float64 `json:"rating"`
	CategoryName  string  `json:"category_name"`
}

// GetAnalyticsSummary 获取分析页摘要数据
func GetAnalyticsSummary(db *gorm.DB) (*AnalyticsSummary, error) {
	var summary AnalyticsSummary
	var err error

	// 获取总用户数
	if err = db.Model(&User{}).Count(&summary.TotalUsers).Error; err != nil {
		return nil, err
	}

	// 获取总应用数
	if err = db.Model(&Application{}).Count(&summary.TotalApps).Error; err != nil {
		return nil, err
	}

	// 获取总下载量
	if err = db.Raw("SELECT COALESCE(SUM(download_count), 0) FROM applications").Scan(&summary.TotalDownloads).Error; err != nil {
		return nil, err
	}

	// 获取总评论数
	if err = db.Model(&Review{}).Count(&summary.TotalReviews).Error; err != nil {
		return nil, err
	}

	// 获取总开发者数
	if err = db.Model(&User{}).Where("is_developer = ?", true).Count(&summary.TotalDevelopers).Error; err != nil {
		return nil, err
	}

	// 获取今日新增用户
	today := time.Now().Format("2006-01-02")
	if err = db.Model(&User{}).Where("DATE(created_at) = ?", today).Count(&summary.NewUsersToday).Error; err != nil {
		return nil, err
	}

	// 获取今日新增应用
	if err = db.Model(&Application{}).Where("DATE(created_at) = ?", today).Count(&summary.NewAppsToday).Error; err != nil {
		return nil, err
	}

	// 获取今日新增下载
	if err = db.Raw("SELECT COUNT(*) FROM download_records WHERE DATE(created_at) = ?", today).Scan(&summary.NewDownloadsToday).Error; err != nil {
		return nil, err
	}

	// 获取待审核应用数
	if err = db.Model(&Application{}).Where("status = ?", "pending").Count(&summary.PendingAppsCount).Error; err != nil {
		return nil, err
	}

	// 获取待审核评论数
	if err = db.Model(&Review{}).Where("status = ?", "pending").Count(&summary.PendingReviewsCount).Error; err != nil {
		return nil, err
	}

	return &summary, nil
}

// GetAnalyticsTrend 获取趋势分析数据
func GetAnalyticsTrend(db *gorm.DB, days int) (*AnalyticsTrend, error) {
	trend := &AnalyticsTrend{
		UserTrend:      make([]DateValue, 0, days),
		AppTrend:       make([]DateValue, 0, days),
		DownloadTrend:  make([]DateValue, 0, days),
		DeveloperTrend: make([]DateValue, 0, days),
	}

	// 构建日期范围
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -days+1)

	// 用于存储每一天的日期
	dateRange := make([]string, 0, days)
	currentDate := startDate
	for currentDate.Before(endDate) || currentDate.Equal(endDate) {
		dateStr := currentDate.Format("2006-01-02")
		dateRange = append(dateRange, dateStr)
		currentDate = currentDate.AddDate(0, 0, 1)
	}

	// 查询用户趋势
	for _, date := range dateRange {
		var count int64
		if err := db.Model(&User{}).Where("DATE(created_at) = ?", date).Count(&count).Error; err != nil {
			return nil, err
		}
		trend.UserTrend = append(trend.UserTrend, DateValue{
			Date:  date,
			Value: count,
		})
	}

	// 查询应用趋势
	for _, date := range dateRange {
		var count int64
		if err := db.Model(&Application{}).Where("DATE(created_at) = ?", date).Count(&count).Error; err != nil {
			return nil, err
		}
		trend.AppTrend = append(trend.AppTrend, DateValue{
			Date:  date,
			Value: count,
		})
	}

	// 查询下载趋势
	for _, date := range dateRange {
		var count int64
		if err := db.Model(&DownloadRecord{}).Where("DATE(created_at) = ?", date).Count(&count).Error; err != nil {
			return nil, err
		}
		trend.DownloadTrend = append(trend.DownloadTrend, DateValue{
			Date:  date,
			Value: count,
		})
	}

	// 查询开发者趋势
	for _, date := range dateRange {
		var count int64
		if err := db.Model(&User{}).Where("is_developer = ? AND DATE(verified_at) = ?", true, date).Count(&count).Error; err != nil {
			return nil, err
		}
		trend.DeveloperTrend = append(trend.DeveloperTrend, DateValue{
			Date:  date,
			Value: count,
		})
	}

	return trend, nil
}

// GetCategoryStats 获取分类统计
func GetCategoryStats(db *gorm.DB) ([]CategoryStats, error) {
	var stats []CategoryStats

	// 查询各分类的应用数量和下载量
	rows, err := db.Raw(`
		SELECT 
			c.id AS category_id, 
			c.name AS category_name, 
			COUNT(DISTINCT a.id) AS app_count,
			COALESCE(SUM(a.download_count), 0) AS download_count
		FROM categories c
		LEFT JOIN applications a ON a.category = c.name
		GROUP BY c.id, c.name
		ORDER BY app_count DESC
	`).Rows()

	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var stat CategoryStats
		if err := db.ScanRows(rows, &stat); err != nil {
			return nil, err
		}
		stats = append(stats, stat)
	}

	return stats, nil
}

// GetWorkbenchSummary 获取工作台摘要
func GetWorkbenchSummary(db *gorm.DB, userID uint) (*WorkbenchSummary, error) {
	var summary WorkbenchSummary
	var err error

	// 获取我的应用数量
	if err = db.Model(&Application{}).Where("developer_id = ?", userID).Count(&summary.MyAppCount).Error; err != nil {
		return nil, err
	}

	// 获取我的应用总下载量
	if err = db.Raw(`
		SELECT COALESCE(SUM(download_count), 0) FROM applications WHERE developer_id = ?
	`, userID).Scan(&summary.TotalDownloads).Error; err != nil {
		return nil, err
	}

	// 获取我的应用平均评分
	if err = db.Raw(`
		SELECT COALESCE(AVG(r.rating), 0) FROM reviews r
		JOIN applications a ON r.application_id = a.id
		WHERE a.developer_id = ?
	`, userID).Scan(&summary.AverageRating).Error; err != nil {
		return nil, err
	}

	// 获取我的应用新评论数量（最近7天）
	lastWeek := time.Now().AddDate(0, 0, -7).Format("2006-01-02")
	if err = db.Raw(`
		SELECT COUNT(*) FROM reviews r
		JOIN applications a ON r.application_id = a.id
		WHERE a.developer_id = ? AND DATE(r.created_at) >= ?
	`, userID, lastWeek).Scan(&summary.NewReviewsCount).Error; err != nil {
		return nil, err
	}

	// 获取我的待审核应用数量
	if err = db.Model(&Application{}).Where("developer_id = ? AND status = ?", userID, "pending").Count(&summary.PendingAppsCount).Error; err != nil {
		return nil, err
	}

	// 任务相关数据（假设有一个TaskItem模型）
	if err = db.Model(&TaskItem{}).Where("user_id = ? AND status = ?", userID, "completed").Count(&summary.CompletedTaskCount).Error; err != nil {
		// 如果表不存在，忽略错误
		summary.CompletedTaskCount = 0
	}

	if err = db.Model(&TaskItem{}).Where("user_id = ?", userID).Count(&summary.TotalTaskCount).Error; err != nil {
		// 如果表不存在，忽略错误
		summary.TotalTaskCount = 0
	}

	return &summary, nil
}

// GetPopularApps 获取热门应用
func GetPopularApps(db *gorm.DB, limit int) ([]PopularApp, error) {
	var apps []PopularApp

	// 查询下载量最多的应用
	if err := db.Raw(`
		SELECT 
			a.id AS app_id, 
			a.name AS app_name, 
			a.icon AS app_icon,
			u.developer_name,
			a.download_count,
			a.average_rating AS rating,
			a.category AS category_name
		FROM applications a
		JOIN users u ON a.developer_id = u.id
		WHERE a.status = 'approved'
		ORDER BY a.download_count DESC, a.average_rating DESC
		LIMIT ?
	`, limit).Scan(&apps).Error; err != nil {
		return nil, err
	}

	return apps, nil
}

// GetSystemLogs 获取系统日志
func GetSystemLogs(db *gorm.DB, page, pageSize int, levelFilter string) ([]SystemLog, int64, error) {
	var logs []SystemLog
	var count int64

	query := db.Model(&SystemLog{})
	if levelFilter != "" {
		query = query.Where("level = ?", levelFilter)
	}

	// 获取总数
	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&logs).Error; err != nil {
		return nil, 0, err
	}

	return logs, count, nil
}

// GetAlertEvents 获取告警事件
func GetAlertEvents(db *gorm.DB, page, pageSize int, statusFilter string) ([]AlertEvent, int64, error) {
	var events []AlertEvent
	var count int64

	query := db.Model(&AlertEvent{})
	if statusFilter != "" {
		query = query.Where("status = ?", statusFilter)
	}

	// 获取总数
	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&events).Error; err != nil {
		return nil, 0, err
	}

	return events, count, nil
}
