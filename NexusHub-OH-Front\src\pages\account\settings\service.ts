/*
 * @Author: xiaoxiaozhou <EMAIL>
 * @Date: 2025-05-05 18:15:47
 * @LastEditors: xiaoxiaozhou <EMAIL>
 * @LastEditTime: 2025-06-15 20:29:14
 * @FilePath: \NexusHub-OH\NexusHub-OH-Front\src\pages\account\settings\service.ts
 * @Description: 
 * 技术生的工作日常
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
import { request } from '@umijs/max';
import type { CurrentUser, GeographicItemType } from './data';

/**
 * 获取当前用户信息
 * 使用后端API获取用户资料并转换为前端需要的格式
 */
export async function queryCurrent(): Promise<{ data: CurrentUser }> {
  const response = await request<API.Response & { data?: API.UserResponse }>('/users/profile', {
    method: 'GET',
  });
  
  // 将API.UserResponse转换为CurrentUser格式
  if (response.data) {
    const userResponse = response.data;
    const currentUser: CurrentUser = {
      name: userResponse.username || '',
      avatar: userResponse.avatar || 'https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png',
      userid: String(userResponse.id) || '',
      email: userResponse.email || '',
      signature: userResponse.description || '',
      title: userResponse.is_developer ? '开发者' : '普通用户',
      group: userResponse.company_name || '',
      tags: [],
      notifyCount: 0,
      unreadCount: 0,
      country: userResponse.country || null,
      geographic: {
        province: userResponse.province ? { name: userResponse.province, id: '330000' } : null,
        city: userResponse.city ? { name: userResponse.city, id: '330100' } : null,
      },
      address: userResponse.address || '',
      phone: userResponse.phone || '',
    };
    
    // 添加开发者状态标签
    if (userResponse.is_developer) {
      currentUser.tags = [
        { key: 'developer', label: '开发者' },
      ];
      
      // 添加验证状态标签
      if (userResponse.verify_status) {
        switch(userResponse.verify_status) {
          case 'approved':
            currentUser.tags.push({ key: 'verified', label: '已认证' });
            break;
          case 'pending':
            currentUser.tags.push({ key: 'pending', label: '认证中' });
            break;
          case 'rejected':
            currentUser.tags.push({ key: 'rejected', label: '认证失败' });
            break;
        }
      }
    }
    
    return { data: currentUser };
  }
  
  // 如果没有数据，返回空的CurrentUser对象
  return { 
    data: {
      name: '',
      avatar: 'https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png',
      userid: '',
      notice: [],
      email: '',
      signature: '',
      title: '',
      group: '',
      tags: [],
      notifyCount: 0,
      unreadCount: 0,
      country: 'China',
      geographic: {
        province: { name: '浙江省', id: '330000' },
        city: { name: '杭州市', id: '330100' },
      },
      address: '',
      phone: '',
    } 
  };
}

/**
 * 获取国家列表
 * 注意：API路径会自动添加/api/v1前缀
 */
export async function queryCountry(): Promise<{ data: GeographicItemType[] }> {
  return request('/geographic/country');
}

/**
 * 获取省份列表
 * 注意：API路径会自动添加/api/v1前缀
 */
export async function queryProvince(): Promise<{ data: GeographicItemType[] }> {
  return request('/geographic/province');
}

/**
 * 获取城市列表
 * @param province - 省份ID
 * 注意：API路径会自动添加/api/v1前缀
 */
export async function queryCity(province: string): Promise<{ data: GeographicItemType[] }> {
  return request(`/geographic/city/${province}`);
}

/**
 * 获取区镇列表
 * @param city - 城市ID
 * 注意：API路径会自动添加/api/v1前缀
 */
export async function queryDistrict(city: string): Promise<{ data: GeographicItemType[] }> {
  return request(`/geographic/district/${city}`);
}

/**
 * 获取街道列表
 * @param district - 区镇ID
 * 注意：API路径会自动添加/api/v1前缀
 */
export async function queryStreet(district: string): Promise<{ data: GeographicItemType[] }> {
  return request(`/geographic/street/${district}`);
}

/**
 * 获取用户列表
 * 注意：API路径会自动添加/api/v1前缀
 */
export async function query() {
  return request('/users');
}
