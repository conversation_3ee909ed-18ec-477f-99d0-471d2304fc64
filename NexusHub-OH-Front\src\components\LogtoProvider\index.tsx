import React, { createContext, useContext, useEffect, useState } from 'react';
import { LogtoProvider as LogtoReactProvider, LogtoConfig } from '@logto/react';
import { logtoConfig, LogtoAuthState, LogtoUser } from '@/config/logto';
import { message } from 'antd';

// 创建认证上下文
const AuthContext = createContext<LogtoAuthState | null>(null);

// Logto 配置
const config: LogtoConfig = {
  endpoint: logtoConfig.endpoint,
  appId: logtoConfig.appId,
  resources: logtoConfig.resources,
  scopes: logtoConfig.scopes,
};

interface LogtoProviderProps {
  children: React.ReactNode;
}

/**
 * Logto 认证提供者组件
 * 包装应用并提供认证功能
 */
export const LogtoProvider: React.FC<LogtoProviderProps> = ({ children }) => {
  return (
    <LogtoReactProvider config={config}>
      <AuthContextProvider>
        {children}
      </AuthContextProvider>
    </LogtoReactProvider>
  );
};

/**
 * 认证上下文提供者
 */
const AuthContextProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [authState, setAuthState] = useState<LogtoAuthState>({
    isAuthenticated: false,
    isLoading: true,
  });

  useEffect(() => {
    // 初始化认证状态
    const initAuth = async () => {
      try {
        // 这里可以添加初始化逻辑
        setAuthState(prev => ({ ...prev, isLoading: false }));
      } catch (error) {
        console.error('认证初始化失败:', error);
        setAuthState({
          isAuthenticated: false,
          isLoading: false,
          error: error as Error,
        });
        message.error('认证初始化失败');
      }
    };

    initAuth();
  }, []);

  return (
    <AuthContext.Provider value={authState}>
      {children}
    </AuthContext.Provider>
  );
};

/**
 * 使用认证状态的 Hook
 */
export const useAuth = (): LogtoAuthState => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within a LogtoProvider');
  }
  return context;
};

export default LogtoProvider;