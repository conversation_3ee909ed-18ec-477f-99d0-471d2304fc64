{"version": "2.0", "ppid": 32784, "events": [{"head": {"id": "18f8e248-368e-4c5a-9cd3-020047c18ed1", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614313034500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0bbd5a3-88cd-4d9e-bb4c-00e5c3710414", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614314908700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "060842ee-04e3-4802-ba16-9a272f8dce9f", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614318852200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75a24c54-bc3f-4c6c-921f-21593d16f70d", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614319709700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df31e26a-27fc-4371-b456-8b81ed45b90f", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614321249000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00c3ef37-7e12-487d-9c4e-5928f05f2c36", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614322074200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e56dbcb4-8b18-4af6-96e9-b290ad17a192", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614324097600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6703a2b8-3eb6-47bf-937a-3ac9970c95e9", "name": "worker[18] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614328041500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5832519b-30c0-45c8-89c9-6b824be280b4", "name": "worker[4] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219614413625300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dff0efda-7ab7-4eea-851c-3160af4296e3", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708162185900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fe4c0e3-3c7e-44a9-9a4e-cfa7ff0e56a8", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708175917500, "endTime": 219708566607200}, "additional": {"children": ["d8b60666-01ac-4f6a-b7ad-94a03307e179", "7091a0c6-7b4d-4a90-bbf9-b3a72f59a310", "2d454a73-d6e3-40a2-b952-72b9be9d0c2f", "7e2bde64-5908-4f44-b41f-1494aeada879", "0e1f4a20-da26-4ba2-b328-f3bc5bbc723d", "be3cb411-8765-43d9-b8f4-b6c8ed03e55d", "95619d2e-921a-4d32-b240-a4665d707228"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "aad700ce-5cff-4234-ba70-9a24fadccf09"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8b60666-01ac-4f6a-b7ad-94a03307e179", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708175921000, "endTime": 219708205961400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7fe4c0e3-3c7e-44a9-9a4e-cfa7ff0e56a8", "logId": "015b053e-f203-4d06-97bf-f44ff57e892a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7091a0c6-7b4d-4a90-bbf9-b3a72f59a310", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708205995200, "endTime": 219708564546900}, "additional": {"children": ["f87e92c3-a018-4c8e-8dfa-4e1ed04beadc", "f7245c28-af3d-43fc-8e41-7da838f1abf5", "4061ffec-685e-4cc1-aac9-18a7cdaa4272", "b94e492e-a156-41a9-b730-ddfdfe550c59", "2026ea2d-82ba-4001-a537-e135924fec87", "34f52e08-f83a-4132-9e59-3c223b29e06e", "59fdac54-ed74-44e2-835d-c3c139d4d7bc", "baa6899f-10c4-4e8d-ad45-832be4a9f104", "36162ed1-d761-43c7-b6aa-a54a505fe55d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7fe4c0e3-3c7e-44a9-9a4e-cfa7ff0e56a8", "logId": "1d80d111-02fc-4217-9502-b3c96fc3e956"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d454a73-d6e3-40a2-b952-72b9be9d0c2f", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708564579600, "endTime": 219708566562500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7fe4c0e3-3c7e-44a9-9a4e-cfa7ff0e56a8", "logId": "d520766a-3e63-4b1b-b30b-bf489c843d70"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e2bde64-5908-4f44-b41f-1494aeada879", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708566569900, "endTime": 219708566597500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7fe4c0e3-3c7e-44a9-9a4e-cfa7ff0e56a8", "logId": "e28465a4-d58f-4512-862c-ea2f5eda0e16"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e1f4a20-da26-4ba2-b328-f3bc5bbc723d", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708183632900, "endTime": 219708183705900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7fe4c0e3-3c7e-44a9-9a4e-cfa7ff0e56a8", "logId": "2a25e567-ee80-47b3-afa4-3da0c4536d35"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a25e567-ee80-47b3-afa4-3da0c4536d35", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708183632900, "endTime": 219708183705900}, "additional": {"logType": "info", "children": [], "durationId": "0e1f4a20-da26-4ba2-b328-f3bc5bbc723d", "parent": "aad700ce-5cff-4234-ba70-9a24fadccf09"}}, {"head": {"id": "be3cb411-8765-43d9-b8f4-b6c8ed03e55d", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708193969600, "endTime": 219708194011400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7fe4c0e3-3c7e-44a9-9a4e-cfa7ff0e56a8", "logId": "0dd56de0-04ad-4bcd-83a1-60890b6afce5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0dd56de0-04ad-4bcd-83a1-60890b6afce5", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708193969600, "endTime": 219708194011400}, "additional": {"logType": "info", "children": [], "durationId": "be3cb411-8765-43d9-b8f4-b6c8ed03e55d", "parent": "aad700ce-5cff-4234-ba70-9a24fadccf09"}}, {"head": {"id": "9a655406-b178-4b92-abd0-1bb93bf3c00c", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708194697100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57dd71d5-b968-4dc6-b8e3-848f92b444b1", "name": "Cache service initialization finished in 11 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708205712500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "015b053e-f203-4d06-97bf-f44ff57e892a", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708175921000, "endTime": 219708205961400}, "additional": {"logType": "info", "children": [], "durationId": "d8b60666-01ac-4f6a-b7ad-94a03307e179", "parent": "aad700ce-5cff-4234-ba70-9a24fadccf09"}}, {"head": {"id": "f87e92c3-a018-4c8e-8dfa-4e1ed04beadc", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708218050100, "endTime": 219708218077200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7091a0c6-7b4d-4a90-bbf9-b3a72f59a310", "logId": "53dd9819-8747-4d67-9f39-c33dd2b15261"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7245c28-af3d-43fc-8e41-7da838f1abf5", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708218125100, "endTime": 219708238056800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7091a0c6-7b4d-4a90-bbf9-b3a72f59a310", "logId": "7fd80335-8d90-4cb6-88a7-c00fa7b7dc31"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4061ffec-685e-4cc1-aac9-18a7cdaa4272", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708238090400, "endTime": 219708390693800}, "additional": {"children": ["987b4622-85fe-4ec8-b937-06088c726e6d", "9c01199a-c5ab-42b0-85ba-3695c259291a", "2cfba105-fcba-4a52-acc7-9a7cda136582"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7091a0c6-7b4d-4a90-bbf9-b3a72f59a310", "logId": "cef07190-fd80-443e-845a-b087c579acfe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b94e492e-a156-41a9-b730-ddfdfe550c59", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708390719000, "endTime": 219708430482200}, "additional": {"children": ["72ac1f23-642d-422a-a6e1-5321720898bf"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7091a0c6-7b4d-4a90-bbf9-b3a72f59a310", "logId": "2baca4da-ee8c-4ffb-9653-300321a26283"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2026ea2d-82ba-4001-a537-e135924fec87", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708430496300, "endTime": 219708527180900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7091a0c6-7b4d-4a90-bbf9-b3a72f59a310", "logId": "6d8848be-1604-4d52-966b-ba8459187f21"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34f52e08-f83a-4132-9e59-3c223b29e06e", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708528939100, "endTime": 219708542541500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7091a0c6-7b4d-4a90-bbf9-b3a72f59a310", "logId": "bc289653-668c-487e-9235-0bf1c5ce84f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59fdac54-ed74-44e2-835d-c3c139d4d7bc", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708542579300, "endTime": 219708564307600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7091a0c6-7b4d-4a90-bbf9-b3a72f59a310", "logId": "1ef0c4b8-1d93-429f-95c4-9fd25561729a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "baa6899f-10c4-4e8d-ad45-832be4a9f104", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708564338900, "endTime": 219708564530400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7091a0c6-7b4d-4a90-bbf9-b3a72f59a310", "logId": "664924b6-a42e-4669-a011-e4220c9f36f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53dd9819-8747-4d67-9f39-c33dd2b15261", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708218050100, "endTime": 219708218077200}, "additional": {"logType": "info", "children": [], "durationId": "f87e92c3-a018-4c8e-8dfa-4e1ed04beadc", "parent": "1d80d111-02fc-4217-9502-b3c96fc3e956"}}, {"head": {"id": "7fd80335-8d90-4cb6-88a7-c00fa7b7dc31", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708218125100, "endTime": 219708238056800}, "additional": {"logType": "info", "children": [], "durationId": "f7245c28-af3d-43fc-8e41-7da838f1abf5", "parent": "1d80d111-02fc-4217-9502-b3c96fc3e956"}}, {"head": {"id": "987b4622-85fe-4ec8-b937-06088c726e6d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708240251400, "endTime": 219708240293300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4061ffec-685e-4cc1-aac9-18a7cdaa4272", "logId": "818d191b-d702-4c64-8f97-a194a3c8e0c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "818d191b-d702-4c64-8f97-a194a3c8e0c0", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708240251400, "endTime": 219708240293300}, "additional": {"logType": "info", "children": [], "durationId": "987b4622-85fe-4ec8-b937-06088c726e6d", "parent": "cef07190-fd80-443e-845a-b087c579acfe"}}, {"head": {"id": "9c01199a-c5ab-42b0-85ba-3695c259291a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708247854600, "endTime": 219708389673300}, "additional": {"children": ["3ce40955-f27f-44f5-ad1f-be77601c565c", "2e93787b-53a6-41e1-8b19-bb2f958d7f32"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4061ffec-685e-4cc1-aac9-18a7cdaa4272", "logId": "1fe96eae-75c1-468c-9f8b-9d6f0df655fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ce40955-f27f-44f5-ad1f-be77601c565c", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708247858200, "endTime": 219708263561000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c01199a-c5ab-42b0-85ba-3695c259291a", "logId": "3bd3d9bb-62bf-4d94-ad3d-4839b42bfa62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e93787b-53a6-41e1-8b19-bb2f958d7f32", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708263610300, "endTime": 219708389657900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c01199a-c5ab-42b0-85ba-3695c259291a", "logId": "e2aa3fdc-d98a-4dc7-a795-d4d81ffb7a1b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "874c635a-2b4c-46fa-84fe-8b01e2007126", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708247873800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32f37c63-bce3-41b9-ae8f-abe919feb858", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708263305300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bd3d9bb-62bf-4d94-ad3d-4839b42bfa62", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708247858200, "endTime": 219708263561000}, "additional": {"logType": "info", "children": [], "durationId": "3ce40955-f27f-44f5-ad1f-be77601c565c", "parent": "1fe96eae-75c1-468c-9f8b-9d6f0df655fb"}}, {"head": {"id": "e341eb8f-6a86-430a-8de4-543b4cd17596", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708263647600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4406b38f-3a92-4583-a44b-05ffa7cbdc29", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708279531900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33f83fd6-bf81-424a-80a8-2723b0f72569", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708279896800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33bb5616-9c56-435b-b559-df0035f6bdcd", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708281639200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26d4d185-3eca-4492-b247-f0722a24fd2d", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708281866000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "011e00ea-3d1a-472c-972c-e3be21e32e7c", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708285985800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdbfdc04-c485-4d42-a005-0c8e64952bea", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708314992200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e208df5d-868f-41d5-909c-5f395fa0cbac", "name": "Sdk init in 58 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708355214400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98cdae98-9d8e-4e6b-bd10-8745c449ea7e", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708355479500}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 18, "minute": 58, "second": 58}, "markType": "other"}}, {"head": {"id": "49376176-9b2d-4238-94af-0beeeebaafd4", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708355501700}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 18, "minute": 58, "second": 58}, "markType": "other"}}, {"head": {"id": "3f833548-4e2d-4851-98dc-c1eb45fff2c8", "name": "Project task initialization takes 32 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708389295300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8a91c41-afb3-4c2a-bced-310fe360fd07", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708389480700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b55218c8-5884-4f30-9449-4b6d81b95d96", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708389553300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33a9f958-48f1-492b-9359-60380f4beed6", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708389607500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2aa3fdc-d98a-4dc7-a795-d4d81ffb7a1b", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708263610300, "endTime": 219708389657900}, "additional": {"logType": "info", "children": [], "durationId": "2e93787b-53a6-41e1-8b19-bb2f958d7f32", "parent": "1fe96eae-75c1-468c-9f8b-9d6f0df655fb"}}, {"head": {"id": "1fe96eae-75c1-468c-9f8b-9d6f0df655fb", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708247854600, "endTime": 219708389673300}, "additional": {"logType": "info", "children": ["3bd3d9bb-62bf-4d94-ad3d-4839b42bfa62", "e2aa3fdc-d98a-4dc7-a795-d4d81ffb7a1b"], "durationId": "9c01199a-c5ab-42b0-85ba-3695c259291a", "parent": "cef07190-fd80-443e-845a-b087c579acfe"}}, {"head": {"id": "2cfba105-fcba-4a52-acc7-9a7cda136582", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708390632000, "endTime": 219708390660500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4061ffec-685e-4cc1-aac9-18a7cdaa4272", "logId": "e2859979-32ae-427a-a478-90fc302c9cc3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2859979-32ae-427a-a478-90fc302c9cc3", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708390632000, "endTime": 219708390660500}, "additional": {"logType": "info", "children": [], "durationId": "2cfba105-fcba-4a52-acc7-9a7cda136582", "parent": "cef07190-fd80-443e-845a-b087c579acfe"}}, {"head": {"id": "cef07190-fd80-443e-845a-b087c579acfe", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708238090400, "endTime": 219708390693800}, "additional": {"logType": "info", "children": ["818d191b-d702-4c64-8f97-a194a3c8e0c0", "1fe96eae-75c1-468c-9f8b-9d6f0df655fb", "e2859979-32ae-427a-a478-90fc302c9cc3"], "durationId": "4061ffec-685e-4cc1-aac9-18a7cdaa4272", "parent": "1d80d111-02fc-4217-9502-b3c96fc3e956"}}, {"head": {"id": "72ac1f23-642d-422a-a6e1-5321720898bf", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708393011100, "endTime": 219708430461000}, "additional": {"children": ["46a3729b-8abd-48cd-a36d-5662f29b5d79", "b4a8d362-56fb-41c2-8571-b0d7498d4d70", "16d4f81a-88f0-4e7a-a7fb-b20abe13d537"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b94e492e-a156-41a9-b730-ddfdfe550c59", "logId": "5da0f912-c47c-4430-945c-1bce0e09a697"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "46a3729b-8abd-48cd-a36d-5662f29b5d79", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708398329100, "endTime": 219708398360700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "72ac1f23-642d-422a-a6e1-5321720898bf", "logId": "51202a1c-22c7-43ec-86c2-757e7efccaae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51202a1c-22c7-43ec-86c2-757e7efccaae", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708398329100, "endTime": 219708398360700}, "additional": {"logType": "info", "children": [], "durationId": "46a3729b-8abd-48cd-a36d-5662f29b5d79", "parent": "5da0f912-c47c-4430-945c-1bce0e09a697"}}, {"head": {"id": "b4a8d362-56fb-41c2-8571-b0d7498d4d70", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708401691700, "endTime": 219708427870300}, "additional": {"children": ["6275c36a-5d0e-4426-ba2c-ae778dee40d6", "b03df63a-ee51-400f-a1f0-789a5ddbfc40"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "72ac1f23-642d-422a-a6e1-5321720898bf", "logId": "5876f750-cb8f-49c9-8ed9-e04b34f30592"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6275c36a-5d0e-4426-ba2c-ae778dee40d6", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708401693900, "endTime": 219708407668100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b4a8d362-56fb-41c2-8571-b0d7498d4d70", "logId": "45f2a2d2-b0d8-4f0b-8e88-3556432b030e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b03df63a-ee51-400f-a1f0-789a5ddbfc40", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708407710900, "endTime": 219708427853800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b4a8d362-56fb-41c2-8571-b0d7498d4d70", "logId": "919976ad-4c96-4e9f-973a-d2fa0b553e0e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0036e487-f9e1-47b2-a422-2adee1f546d2", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708401709000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49a4dbdf-fe96-4dba-acca-e4ef62868bc0", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708407420900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45f2a2d2-b0d8-4f0b-8e88-3556432b030e", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708401693900, "endTime": 219708407668100}, "additional": {"logType": "info", "children": [], "durationId": "6275c36a-5d0e-4426-ba2c-ae778dee40d6", "parent": "5876f750-cb8f-49c9-8ed9-e04b34f30592"}}, {"head": {"id": "4ca29a04-f899-4de6-9839-900e64f392a1", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708407744800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6abe0b18-d7d2-4293-8ab6-046e6619036b", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708418930900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2cb7eb1-7fe8-4fe8-a4d2-4eab2e232893", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708419134000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26b12005-03cc-467e-9fb7-c34adf5bc02d", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708419436900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc10b85f-5aec-47dd-80c1-ebe46a23060b", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708419604500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa3eab9a-21f0-407a-8eb6-e04dd203a688", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708419667800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd1470af-d38c-4905-8b23-a25bb77c6eab", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708419718900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb103855-7289-41e2-af40-b9e3c33f93f2", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708419809400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee5f8059-7635-4c4c-acda-d5375be26a47", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708419875400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b67eb1ae-224d-4cf6-b568-7dc59c01bfdf", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708420381200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82ceb334-ef76-4913-8098-1a704d36c257", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708420589900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc66c673-766d-490d-bd6d-2af0fd8f7e20", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708420672700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f2683cd-d760-4c1c-9855-1b08e5c7eb3e", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708420719000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba6bcf03-d375-49bb-89ff-10ebd0bd96e2", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708420796500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e00e7711-25b1-48f6-80c6-ee8f5521fd1a", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708420857000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5141249-da7b-4fa2-933b-6cc8804fce1a", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708420989900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ac7f36e-3667-4342-897b-d7119cd09581", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708421990300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ab4c9f8-9bc4-4cac-8dee-1546d74dfd07", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708422115600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7cf2e10-dc95-4bb2-81a6-3b2967cabd63", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708422171800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14be2ec9-9e18-4577-ac19-a3b543c4de07", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708422238900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f328c8bd-2693-44bc-868d-291970d617f8", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708427237600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1900c409-a983-4765-a8c5-8fc58cb7ce58", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708427579800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb1dfd2b-58c7-4480-8440-3d92cb8d98e7", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708427729900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "689abd9a-a1dd-4191-b9b2-3e400bdc1094", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708427807900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "919976ad-4c96-4e9f-973a-d2fa0b553e0e", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708407710900, "endTime": 219708427853800}, "additional": {"logType": "info", "children": [], "durationId": "b03df63a-ee51-400f-a1f0-789a5ddbfc40", "parent": "5876f750-cb8f-49c9-8ed9-e04b34f30592"}}, {"head": {"id": "5876f750-cb8f-49c9-8ed9-e04b34f30592", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708401691700, "endTime": 219708427870300}, "additional": {"logType": "info", "children": ["45f2a2d2-b0d8-4f0b-8e88-3556432b030e", "919976ad-4c96-4e9f-973a-d2fa0b553e0e"], "durationId": "b4a8d362-56fb-41c2-8571-b0d7498d4d70", "parent": "5da0f912-c47c-4430-945c-1bce0e09a697"}}, {"head": {"id": "16d4f81a-88f0-4e7a-a7fb-b20abe13d537", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708430380800, "endTime": 219708430428100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "72ac1f23-642d-422a-a6e1-5321720898bf", "logId": "c223d022-fdc9-468d-a1a2-d9d6cd41c732"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c223d022-fdc9-468d-a1a2-d9d6cd41c732", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708430380800, "endTime": 219708430428100}, "additional": {"logType": "info", "children": [], "durationId": "16d4f81a-88f0-4e7a-a7fb-b20abe13d537", "parent": "5da0f912-c47c-4430-945c-1bce0e09a697"}}, {"head": {"id": "5da0f912-c47c-4430-945c-1bce0e09a697", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708393011100, "endTime": 219708430461000}, "additional": {"logType": "info", "children": ["51202a1c-22c7-43ec-86c2-757e7efccaae", "5876f750-cb8f-49c9-8ed9-e04b34f30592", "c223d022-fdc9-468d-a1a2-d9d6cd41c732"], "durationId": "72ac1f23-642d-422a-a6e1-5321720898bf", "parent": "2baca4da-ee8c-4ffb-9653-300321a26283"}}, {"head": {"id": "2baca4da-ee8c-4ffb-9653-300321a26283", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708390719000, "endTime": 219708430482200}, "additional": {"logType": "info", "children": ["5da0f912-c47c-4430-945c-1bce0e09a697"], "durationId": "b94e492e-a156-41a9-b730-ddfdfe550c59", "parent": "1d80d111-02fc-4217-9502-b3c96fc3e956"}}, {"head": {"id": "565ba5b6-4848-4f0d-9e5c-288b267cff9b", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708463346500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59605616-1121-4242-a77d-84b542612ddc", "name": "hvigorfile, resolve hvigorfile dependencies in 97 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708526989600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d8848be-1604-4d52-966b-ba8459187f21", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708430496300, "endTime": 219708527180900}, "additional": {"logType": "info", "children": [], "durationId": "2026ea2d-82ba-4001-a537-e135924fec87", "parent": "1d80d111-02fc-4217-9502-b3c96fc3e956"}}, {"head": {"id": "36162ed1-d761-43c7-b6aa-a54a505fe55d", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708528463600, "endTime": 219708528914700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7091a0c6-7b4d-4a90-bbf9-b3a72f59a310", "logId": "97d960ab-575d-4f75-8ffa-7309d8ddc322"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "970e5d1e-dfda-448f-8df6-754b0771db40", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708528512100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97d960ab-575d-4f75-8ffa-7309d8ddc322", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708528463600, "endTime": 219708528914700}, "additional": {"logType": "info", "children": [], "durationId": "36162ed1-d761-43c7-b6aa-a54a505fe55d", "parent": "1d80d111-02fc-4217-9502-b3c96fc3e956"}}, {"head": {"id": "725fc51a-d929-4576-9014-ae2c3e43dc79", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708531100000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "245696ad-7bdd-4088-a9d1-1cec20396e9d", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708540676500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc289653-668c-487e-9235-0bf1c5ce84f3", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708528939100, "endTime": 219708542541500}, "additional": {"logType": "info", "children": [], "durationId": "34f52e08-f83a-4132-9e59-3c223b29e06e", "parent": "1d80d111-02fc-4217-9502-b3c96fc3e956"}}, {"head": {"id": "46eb5f89-ae26-4547-a0c6-67d2acd3d0d9", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708542605700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9865f6f5-425a-4fd1-8789-6e53f86bb171", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708553154400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "422a8112-3816-4aca-83ef-9fc7da24bf79", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708553335000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d906830-5c99-4c1e-ae06-114e701e0024", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708553550200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba776994-654c-4dc3-af58-bbe4849529a1", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708558911600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9f2dfa1-29f9-4ffc-859d-9b45d1ba583b", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708559133600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ef0c4b8-1d93-429f-95c4-9fd25561729a", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708542579300, "endTime": 219708564307600}, "additional": {"logType": "info", "children": [], "durationId": "59fdac54-ed74-44e2-835d-c3c139d4d7bc", "parent": "1d80d111-02fc-4217-9502-b3c96fc3e956"}}, {"head": {"id": "f7b494fa-0d73-4c11-9b92-614270cedf77", "name": "Configuration phase cost:347 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708564375000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "664924b6-a42e-4669-a011-e4220c9f36f8", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708564338900, "endTime": 219708564530400}, "additional": {"logType": "info", "children": [], "durationId": "baa6899f-10c4-4e8d-ad45-832be4a9f104", "parent": "1d80d111-02fc-4217-9502-b3c96fc3e956"}}, {"head": {"id": "1d80d111-02fc-4217-9502-b3c96fc3e956", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708205995200, "endTime": 219708564546900}, "additional": {"logType": "info", "children": ["53dd9819-8747-4d67-9f39-c33dd2b15261", "7fd80335-8d90-4cb6-88a7-c00fa7b7dc31", "cef07190-fd80-443e-845a-b087c579acfe", "2baca4da-ee8c-4ffb-9653-300321a26283", "6d8848be-1604-4d52-966b-ba8459187f21", "bc289653-668c-487e-9235-0bf1c5ce84f3", "1ef0c4b8-1d93-429f-95c4-9fd25561729a", "664924b6-a42e-4669-a011-e4220c9f36f8", "97d960ab-575d-4f75-8ffa-7309d8ddc322"], "durationId": "7091a0c6-7b4d-4a90-bbf9-b3a72f59a310", "parent": "aad700ce-5cff-4234-ba70-9a24fadccf09"}}, {"head": {"id": "95619d2e-921a-4d32-b240-a4665d707228", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708566511700, "endTime": 219708566540100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7fe4c0e3-3c7e-44a9-9a4e-cfa7ff0e56a8", "logId": "9608fcb4-5f00-419f-9bd9-75551e7d21f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9608fcb4-5f00-419f-9bd9-75551e7d21f3", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708566511700, "endTime": 219708566540100}, "additional": {"logType": "info", "children": [], "durationId": "95619d2e-921a-4d32-b240-a4665d707228", "parent": "aad700ce-5cff-4234-ba70-9a24fadccf09"}}, {"head": {"id": "d520766a-3e63-4b1b-b30b-bf489c843d70", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708564579600, "endTime": 219708566562500}, "additional": {"logType": "info", "children": [], "durationId": "2d454a73-d6e3-40a2-b952-72b9be9d0c2f", "parent": "aad700ce-5cff-4234-ba70-9a24fadccf09"}}, {"head": {"id": "e28465a4-d58f-4512-862c-ea2f5eda0e16", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708566569900, "endTime": 219708566597500}, "additional": {"logType": "info", "children": [], "durationId": "7e2bde64-5908-4f44-b41f-1494aeada879", "parent": "aad700ce-5cff-4234-ba70-9a24fadccf09"}}, {"head": {"id": "aad700ce-5cff-4234-ba70-9a24fadccf09", "name": "init", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708175917500, "endTime": 219708566607200}, "additional": {"logType": "info", "children": ["015b053e-f203-4d06-97bf-f44ff57e892a", "1d80d111-02fc-4217-9502-b3c96fc3e956", "d520766a-3e63-4b1b-b30b-bf489c843d70", "e28465a4-d58f-4512-862c-ea2f5eda0e16", "2a25e567-ee80-47b3-afa4-3da0c4536d35", "0dd56de0-04ad-4bcd-83a1-60890b6afce5", "9608fcb4-5f00-419f-9bd9-75551e7d21f3"], "durationId": "7fe4c0e3-3c7e-44a9-9a4e-cfa7ff0e56a8"}}, {"head": {"id": "bdc95692-3627-4d3c-a3c1-e37d96772fc8", "name": "Configuration task cost before running: 398 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708566920500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c02955e-72d9-49e4-913d-df42120fdf2f", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708581313000, "endTime": 219708600434700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "9c29f72a-8e0e-4f46-916b-7edfde3eae3f", "logId": "a6f7ec8b-d68a-4384-8e7e-0beeab0aa499"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c29f72a-8e0e-4f46-916b-7edfde3eae3f", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708569433400}, "additional": {"logType": "detail", "children": [], "durationId": "8c02955e-72d9-49e4-913d-df42120fdf2f"}}, {"head": {"id": "a587a6ff-8bc4-443f-92eb-9cec662292e7", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708570426400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26400479-20e0-4dae-80ff-5f73a1f55ef0", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708570632200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18b705c6-b896-4481-b776-f3a5b032c327", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708571830200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8810a8fd-ac68-40ac-9ceb-0a8100f9f2e9", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708573115600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f14dd137-6d05-4ecd-87f5-a6c8f08c2738", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708575246500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c6e74c2-5a2f-4509-8a19-38879f7691af", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708575461600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42756964-e10a-4ae3-8573-02bb10f1778b", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708581338200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "604d8bc4-4012-405b-ba73-9477146b1575", "name": "Incremental task entry:default@PreBuild pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708599994500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98830ccb-fa89-461a-9ead-385c51f4b804", "name": "entry : default@PreBuild cost memory 0.3191375732421875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708600291600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6f7ec8b-d68a-4384-8e7e-0beeab0aa499", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708581313000, "endTime": 219708600434700}, "additional": {"logType": "info", "children": [], "durationId": "8c02955e-72d9-49e4-913d-df42120fdf2f"}}, {"head": {"id": "855c0e2d-07c2-4881-b985-c12694e0bf06", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708614434300, "endTime": 219708618627700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4195cc98-a7e9-4099-944c-de19fd739faa", "logId": "7042134d-1cc7-423d-8e40-ebe256f7a41d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4195cc98-a7e9-4099-944c-de19fd739faa", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708610629900}, "additional": {"logType": "detail", "children": [], "durationId": "855c0e2d-07c2-4881-b985-c12694e0bf06"}}, {"head": {"id": "b76995c9-b971-4910-b3bf-7f87b5ca6509", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708613008700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23875ce8-053c-40a5-8eeb-f0a8cbea05a2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708613252800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c646f98e-16d9-4274-b664-4429923bf9d2", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708614456500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b921ad8d-890d-4a5e-aa11-0a62462ac17c", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708615789700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da14c066-2eb2-4063-a59e-e2c882c4999c", "name": "entry : default@CreateModuleInfo cost memory 0.06244659423828125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708618149800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc51bbd8-8f15-4555-8d30-948268007c4a", "name": "runTaskFromQueue task cost before running: 449 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708618466900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7042134d-1cc7-423d-8e40-ebe256f7a41d", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708614434300, "endTime": 219708618627700, "totalTime": 3959600}, "additional": {"logType": "info", "children": [], "durationId": "855c0e2d-07c2-4881-b985-c12694e0bf06"}}, {"head": {"id": "2fe2834e-ae31-4a1e-8682-686e000c6c16", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708637724000, "endTime": 219708643131600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b7b8f29c-bd7d-43e1-8cff-db70f9b8e64d", "logId": "3dda34d0-eccb-4038-94b5-83f8edddb16b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b7b8f29c-bd7d-43e1-8cff-db70f9b8e64d", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708623562900}, "additional": {"logType": "detail", "children": [], "durationId": "2fe2834e-ae31-4a1e-8682-686e000c6c16"}}, {"head": {"id": "a326ba1a-0905-43a4-a752-a3f5d76f4474", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708626702800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac598c16-3b7b-4bdb-a2ad-03e5c8ce07f6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708626938200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "941b3dd2-0bdc-4c36-8c46-453d4f2b8a65", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708637755100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8f3d22b-b81b-4843-b63a-157dc52bb908", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708639873800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3db614e5-e5a3-4c1f-bce6-e0edec5b16b2", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708642744600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32da08cc-ea24-49e0-a7ad-475d1e26dc75", "name": "entry : default@GenerateMetadata cost memory 0.10375213623046875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708642984300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dda34d0-eccb-4038-94b5-83f8edddb16b", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708637724000, "endTime": 219708643131600}, "additional": {"logType": "info", "children": [], "durationId": "2fe2834e-ae31-4a1e-8682-686e000c6c16"}}, {"head": {"id": "c492b53b-8386-42d5-83c2-d8caf831131d", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708647980000, "endTime": 219708648458000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "d1f019cc-d774-470b-ae59-c4bcb6c0ab65", "logId": "4cb3fbfb-2b4d-42fd-87fb-92b6eea9b864"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d1f019cc-d774-470b-ae59-c4bcb6c0ab65", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708646196600}, "additional": {"logType": "detail", "children": [], "durationId": "c492b53b-8386-42d5-83c2-d8caf831131d"}}, {"head": {"id": "2fbcc996-c879-457c-bddb-8406816f5f25", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708647619700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "836192aa-3f40-4635-bca3-ec7e3113579b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708647781100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3500b6de-7271-4adb-8099-9f8728150300", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708647992300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa886230-b4dc-4a72-a5dd-ffa58011ded0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708648156400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c7bde15-1d0e-415f-82e3-dc10bec4e295", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708648217500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d65d4901-87e9-46f3-8894-3280ac20e7cb", "name": "entry : default@ConfigureCmake cost memory 0.03774261474609375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708648306400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc65b3f9-4a6d-4f4f-8633-4fc9f724eb9c", "name": "runTaskFromQueue task cost before running: 479 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708648390000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cb3fbfb-2b4d-42fd-87fb-92b6eea9b864", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708647980000, "endTime": 219708648458000, "totalTime": 385100}, "additional": {"logType": "info", "children": [], "durationId": "c492b53b-8386-42d5-83c2-d8caf831131d"}}, {"head": {"id": "2022fddb-0918-4fcf-99db-99682162bd5e", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708655870800, "endTime": 219708660112800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "d436a518-e24b-4c35-b411-25911f096366", "logId": "f0c17118-46dc-4d17-a06a-68ff076ab7ad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d436a518-e24b-4c35-b411-25911f096366", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708653040000}, "additional": {"logType": "detail", "children": [], "durationId": "2022fddb-0918-4fcf-99db-99682162bd5e"}}, {"head": {"id": "fd49c19c-aa89-49c4-a02e-1c2dd0e76cff", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708654784000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa583600-3692-4891-9dfa-2b7f895f151d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708654958900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cd2addd-fb71-4d43-8e48-e19662bc8cb7", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708655890700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1182cd9-9ce2-4358-a459-e112d2b5f451", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708659441300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2351af91-2080-452f-9eaf-3a4756dde794", "name": "entry : default@MergeProfile cost memory 0.11853790283203125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708659909700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0c17118-46dc-4d17-a06a-68ff076ab7ad", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708655870800, "endTime": 219708660112800}, "additional": {"logType": "info", "children": [], "durationId": "2022fddb-0918-4fcf-99db-99682162bd5e"}}, {"head": {"id": "70575896-72d7-42f9-8d85-00d10045d31a", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708669963700, "endTime": 219708676471300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5ef5f53e-93e1-4286-8577-9c0613134dc3", "logId": "ef38525b-0997-4dfe-9644-276f41e01fc1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ef5f53e-93e1-4286-8577-9c0613134dc3", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708663684000}, "additional": {"logType": "detail", "children": [], "durationId": "70575896-72d7-42f9-8d85-00d10045d31a"}}, {"head": {"id": "93ed2ff1-555b-4a2c-92c4-ce3ef7e513ef", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708665050200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fbaf0dc-5261-4a8e-b776-1e6284a66321", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708665220100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "763ca751-6cf5-45bb-bff6-7081d545238d", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708669984100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c288d249-9643-41fe-a447-048258eb7ebe", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708671904600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f305a9ca-8b26-4226-8b24-c4788eeb2134", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708676038300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ebeed7b-c09d-4956-8dd8-9f0cbb5e7744", "name": "entry : default@CreateBuildProfile cost memory 0.10887908935546875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708676324300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef38525b-0997-4dfe-9644-276f41e01fc1", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708669963700, "endTime": 219708676471300}, "additional": {"logType": "info", "children": [], "durationId": "70575896-72d7-42f9-8d85-00d10045d31a"}}, {"head": {"id": "f5e4b88a-1ad0-4804-8cdd-8652d54aee73", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708683587700, "endTime": 219708684918600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "03210e56-6a92-4e3f-8dee-e2c43f87262c", "logId": "0d0b2162-ff25-476f-af0a-bd2efec1bbbd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03210e56-6a92-4e3f-8dee-e2c43f87262c", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708679650900}, "additional": {"logType": "detail", "children": [], "durationId": "f5e4b88a-1ad0-4804-8cdd-8652d54aee73"}}, {"head": {"id": "65e89b56-7f93-4b10-acde-ecc8cc5cc0a3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708681775600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c281153-47f7-4cb4-813b-64a49046e99c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708681933100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7d68c38-d706-40d7-8bef-be6fca1dcf34", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708683622300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5462093-2100-4f85-a19f-616cb66f46f0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708684008300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d64fda7-3429-45ea-874b-89b08d7a1424", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708684137100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8849614d-9827-4ebf-bfb1-29b6c12381e5", "name": "entry : default@PreCheckSyscap cost memory 0.0416412353515625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708684556500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dda3f653-72c8-40a0-adbb-080615937443", "name": "runTaskFromQueue task cost before running: 516 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708684770400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d0b2162-ff25-476f-af0a-bd2efec1bbbd", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708683587700, "endTime": 219708684918600, "totalTime": 1128000}, "additional": {"logType": "info", "children": [], "durationId": "f5e4b88a-1ad0-4804-8cdd-8652d54aee73"}}, {"head": {"id": "7748cf65-53e4-4906-b4dd-6a282c01207d", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708693539000, "endTime": 219708703290000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d8778f28-528b-43ed-8f8e-e399db8d8c86", "logId": "52e2b0a3-2b2f-4133-abe1-d0b381ce43aa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8778f28-528b-43ed-8f8e-e399db8d8c86", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708688246000}, "additional": {"logType": "detail", "children": [], "durationId": "7748cf65-53e4-4906-b4dd-6a282c01207d"}}, {"head": {"id": "a1b0b5eb-3699-4fc5-9ab8-1ca44a4aef50", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708690003100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0101d4a0-d8e5-47fb-babc-56ec8bab5fee", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708690169000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5b69b41-1dfb-479c-8f71-c918611bb633", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708693564000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb4601fc-6a88-48ba-b7bc-6a800baf7deb", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708701480900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23631f45-d338-42c9-a14e-686aead18e50", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708702998700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c1ea0b4-ead5-42d2-ad01-d8c740514260", "name": "entry : default@GeneratePkgContextInfo cost memory 0.266021728515625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708703206500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52e2b0a3-2b2f-4133-abe1-d0b381ce43aa", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708693539000, "endTime": 219708703290000}, "additional": {"logType": "info", "children": [], "durationId": "7748cf65-53e4-4906-b4dd-6a282c01207d"}}, {"head": {"id": "8af61c3c-5dda-4e29-be68-e8a308dc6080", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708719931600, "endTime": 219708722783600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "8adbeccb-0ab3-4a60-94ae-273fa1a2b0bb", "logId": "198d9d32-0af7-4bd0-941a-d061400807c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8adbeccb-0ab3-4a60-94ae-273fa1a2b0bb", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708706216800}, "additional": {"logType": "detail", "children": [], "durationId": "8af61c3c-5dda-4e29-be68-e8a308dc6080"}}, {"head": {"id": "3f54a836-00f6-4b6f-a3a4-0669863fe132", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708707830000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad07b783-46a1-43f5-bf81-4c02dc7fc645", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708708117800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2c31807-ccde-41a2-9084-71d3af0b6505", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708719957300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dceab0ec-648d-43e9-8944-40d702ba7f16", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708722234200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c61356c-dfac-43ab-9207-7e51c2aed012", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708722428800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccbbc5fe-0cb9-477b-b2ff-b7f4e63ab3f2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708722531900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5be7951-0a66-4eb0-9823-fb7c14d18198", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708722581500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a27c4066-078a-4670-9967-77bda2468722", "name": "entry : default@ProcessIntegratedHsp cost memory 0.12291717529296875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708722664400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09fda4f3-abc5-4fbe-b41a-6cb6d5a66871", "name": "runTaskFromQueue task cost before running: 554 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708722734400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "198d9d32-0af7-4bd0-941a-d061400807c4", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708719931600, "endTime": 219708722783600, "totalTime": 2785800}, "additional": {"logType": "info", "children": [], "durationId": "8af61c3c-5dda-4e29-be68-e8a308dc6080"}}, {"head": {"id": "3a94dc5f-6ab5-4694-8930-9c9f8d8e3e9e", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708730834900, "endTime": 219708731279700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "51432af3-7a39-4daf-863a-f89761ba84ce", "logId": "6105c488-0139-42c3-bee2-79e8ddfe8e6a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51432af3-7a39-4daf-863a-f89761ba84ce", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708727321700}, "additional": {"logType": "detail", "children": [], "durationId": "3a94dc5f-6ab5-4694-8930-9c9f8d8e3e9e"}}, {"head": {"id": "b8961911-53a8-4696-bdea-2b43709fe849", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708729630200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27449309-64f8-4054-a6ba-f57c4dc6240a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708729796800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b83d1c5c-9099-499d-837b-ef8fe29147cb", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708730851800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "360efcb9-9a6c-4e1c-8554-f6c9c4f27431", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708731021500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac3304ec-7fc0-4e6f-8269-34ac1bf4f922", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708731079300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "117aa915-bef7-4811-95e5-77a23c35a887", "name": "entry : default@BuildNativeWithCmake cost memory 0.03905487060546875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708731156400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e86ecaec-81e3-4b23-acdf-d7d498fe9664", "name": "runTaskFromQueue task cost before running: 562 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708731235700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6105c488-0139-42c3-bee2-79e8ddfe8e6a", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708730834900, "endTime": 219708731279700, "totalTime": 378000}, "additional": {"logType": "info", "children": [], "durationId": "3a94dc5f-6ab5-4694-8930-9c9f8d8e3e9e"}}, {"head": {"id": "ce373d5d-5757-4cad-a074-6c4dbae99fb4", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708740477100, "endTime": 219708747670300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "97f1c80e-8ae7-4938-abca-f2edc3f00276", "logId": "e649a9b5-9566-468c-9eb0-8f847112a7ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "97f1c80e-8ae7-4938-abca-f2edc3f00276", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708734688200}, "additional": {"logType": "detail", "children": [], "durationId": "ce373d5d-5757-4cad-a074-6c4dbae99fb4"}}, {"head": {"id": "40503ed0-98d5-44ea-99d5-727f5b5b8a9e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708738212000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62c8a526-127b-407f-837b-14a939b30d3d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708738447200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31b06659-02c7-4f88-bad5-773d9b1d3de8", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708740512200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b54cddf-69f3-49f5-b539-13252cacf901", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708747386500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07ddb2fb-222b-422c-b949-08e4f278c78e", "name": "entry : default@MakePackInfo cost memory 0.16561126708984375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708747587700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e649a9b5-9566-468c-9eb0-8f847112a7ea", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708740477100, "endTime": 219708747670300}, "additional": {"logType": "info", "children": [], "durationId": "ce373d5d-5757-4cad-a074-6c4dbae99fb4"}}, {"head": {"id": "f04f4a47-14b8-4a16-bc1f-54eaff6e8455", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708757358000, "endTime": 219708763099700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ba89a341-b636-412f-8ec1-aacbeaa5ac35", "logId": "29bdd8d0-9c5b-4dd7-8ff1-13be7f8b67dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ba89a341-b636-412f-8ec1-aacbeaa5ac35", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708753776300}, "additional": {"logType": "detail", "children": [], "durationId": "f04f4a47-14b8-4a16-bc1f-54eaff6e8455"}}, {"head": {"id": "95fc5ba6-0976-417e-b9fa-1875752afe8c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708755426500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa294b05-477a-4d99-8b4d-326e541cdb23", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708755593200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "534e99c9-6325-441b-b51a-f7c3536e2a03", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708757380200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a346067d-4330-4770-8934-db49c8cce45e", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708757990400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bd5666c-df46-4e97-b3b7-3ed440b3c50c", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708759588400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79849394-ac6e-4d1f-bf14-c9b234c317da", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708762792600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e5a2957-c076-4119-8326-de72a75a344e", "name": "entry : default@SyscapTransform cost memory 0.15181732177734375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708762992700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29bdd8d0-9c5b-4dd7-8ff1-13be7f8b67dd", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708757358000, "endTime": 219708763099700}, "additional": {"logType": "info", "children": [], "durationId": "f04f4a47-14b8-4a16-bc1f-54eaff6e8455"}}, {"head": {"id": "19890d4d-4eb6-4ddb-83a7-67f4552c4d72", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708770759900, "endTime": 219708774112900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "4487fd92-2fd9-4d4a-b3be-a1fb1a3e7516", "logId": "2d4bf2a9-2c23-4968-8cbc-36793b278d23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4487fd92-2fd9-4d4a-b3be-a1fb1a3e7516", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708765276600}, "additional": {"logType": "detail", "children": [], "durationId": "19890d4d-4eb6-4ddb-83a7-67f4552c4d72"}}, {"head": {"id": "d74460d3-dea0-4289-a92c-4f0ecec0be7a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708768099600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "796336d3-7cf8-4556-b90a-66e9158826e7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708768368400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a5765d2-077e-46cf-b8a3-3401b33bfa14", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708770778900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45f10018-e39b-440f-9f4d-a6b2676d88a7", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708773741000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "925be496-b8a4-4230-9971-10b83f87fde7", "name": "entry : default@ProcessProfile cost memory 0.12522125244140625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708773942700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d4bf2a9-2c23-4968-8cbc-36793b278d23", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708770759900, "endTime": 219708774112900}, "additional": {"logType": "info", "children": [], "durationId": "19890d4d-4eb6-4ddb-83a7-67f4552c4d72"}}, {"head": {"id": "b024340e-cac7-4310-b3e9-45b7b3a2f429", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708783995800, "endTime": 219708794092900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9cde1dfd-cff5-4c3f-9613-be75e1fa49a6", "logId": "feeb0da2-8672-4eef-829f-b7e1f9e235c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9cde1dfd-cff5-4c3f-9613-be75e1fa49a6", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708778311200}, "additional": {"logType": "detail", "children": [], "durationId": "b024340e-cac7-4310-b3e9-45b7b3a2f429"}}, {"head": {"id": "9b3ab97e-03cd-4f81-b944-49f61158e0b4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708780665700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bec4eba0-8c38-4fba-b949-bc259813cc9f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708780853400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96871ed6-15db-485d-9edb-bf86fd1f349f", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708784020200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b6b04f6-f324-4b8a-9f74-ac90e425a27b", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708793669100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e549089-3887-4269-96fc-f04a69039b6b", "name": "entry : default@ProcessRouterMap cost memory 0.23639678955078125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708793935200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "feeb0da2-8672-4eef-829f-b7e1f9e235c6", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708783995800, "endTime": 219708794092900}, "additional": {"logType": "info", "children": [], "durationId": "b024340e-cac7-4310-b3e9-45b7b3a2f429"}}, {"head": {"id": "43e2f96c-07f2-4248-90c9-3a5ace16a44f", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708802448200, "endTime": 219708812248300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "9a53d8eb-361a-4d14-9b36-b0e38ac41422", "logId": "0d01e372-89e6-4c8f-a120-12b2f461f9e6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a53d8eb-361a-4d14-9b36-b0e38ac41422", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708798799700}, "additional": {"logType": "detail", "children": [], "durationId": "43e2f96c-07f2-4248-90c9-3a5ace16a44f"}}, {"head": {"id": "1c5de7a2-94bf-4610-b516-2a8acae010d3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708802020000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66a103d8-bdb6-4d23-a9f7-5cd29a59f9da", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708802255000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "538046a5-c621-4269-ba2f-4b75ba11ad1f", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708802466300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e87449d6-f174-4e2a-9ff7-9b14f7a149dd", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708802883600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19add6ad-bab8-483f-96b7-ed6d5c84392c", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708810129700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5e244e7-05ce-4920-aee4-479b4bbf9b49", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708810352300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b7f9e32-3532-41ed-859e-89d6544a1e59", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708810473000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "193984dc-e564-4743-8030-48b7ff33e889", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708810529800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df01cfea-f2be-4239-ac90-721ebe26809b", "name": "entry : default@ProcessStartupConfig cost memory 0.26068878173828125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708811894900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf7b5563-f4ac-4d6f-be98-1c4050434165", "name": "runTaskFromQueue task cost before running: 643 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708812132900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d01e372-89e6-4c8f-a120-12b2f461f9e6", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708802448200, "endTime": 219708812248300, "totalTime": 9633700}, "additional": {"logType": "info", "children": [], "durationId": "43e2f96c-07f2-4248-90c9-3a5ace16a44f"}}, {"head": {"id": "c0d9bc28-aeb7-4071-b8e6-b21dec3ee250", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708821571400, "endTime": 219708823846600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "44f2efc5-b389-4077-ba2d-850a934f7af8", "logId": "76120f13-2558-4f91-a0f5-7ed781a10c69"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44f2efc5-b389-4077-ba2d-850a934f7af8", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708818154000}, "additional": {"logType": "detail", "children": [], "durationId": "c0d9bc28-aeb7-4071-b8e6-b21dec3ee250"}}, {"head": {"id": "f98ee85c-e6bc-4cd1-be06-9dc86c565bf7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708820210700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3587c23-ad6f-47e5-aae5-572030ec7076", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708820433300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1751d42-47fb-465a-af77-bfaa90d88144", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708821590600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7922c482-8a8f-463a-a32c-0297843d9cc4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708821772800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0836ddb-66c2-4d67-8ca8-13d8ebc569c4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708821839600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbc65a3b-e661-4f92-b258-0c10dc507015", "name": "entry : default@BuildNativeWithNinja cost memory 0.058380126953125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708823543000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea1ba650-8cbe-4d42-92db-00393b12595d", "name": "runTaskFromQueue task cost before running: 655 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708823765100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76120f13-2558-4f91-a0f5-7ed781a10c69", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708821571400, "endTime": 219708823846600, "totalTime": 2152000}, "additional": {"logType": "info", "children": [], "durationId": "c0d9bc28-aeb7-4071-b8e6-b21dec3ee250"}}, {"head": {"id": "57d44e16-d739-4ed6-80e6-768f3d520dd6", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708838573300, "endTime": 219708848787400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "34d5a407-dcea-4309-9273-3869e7820422", "logId": "26ed10c5-1a20-405a-9378-a1164e507140"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34d5a407-dcea-4309-9273-3869e7820422", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708829342700}, "additional": {"logType": "detail", "children": [], "durationId": "57d44e16-d739-4ed6-80e6-768f3d520dd6"}}, {"head": {"id": "61b1ef80-b9cf-47f6-88a7-77cbfc894f84", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708831459800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00731fec-4c24-4eb4-bd4c-31abae30f937", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708831677900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d8483eb-7b2e-4c0d-81b6-6af518827e4f", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708835829300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c9f29ff-d75f-4f71-8906-2278fd0acdfb", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708841760100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c686ab6b-bc58-47eb-ace6-e08feb7fee6c", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708846270900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24021a7d-30c4-499e-a8d9-03cf39e67f37", "name": "entry : default@ProcessResource cost memory 0.16272735595703125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708846474500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26ed10c5-1a20-405a-9378-a1164e507140", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708838573300, "endTime": 219708848787400}, "additional": {"logType": "info", "children": [], "durationId": "57d44e16-d739-4ed6-80e6-768f3d520dd6"}}, {"head": {"id": "f9b6b98d-2e84-4c92-beab-f5177d354049", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708862688800, "endTime": 219708898519200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "fc3f5233-6b18-4e0a-aa2d-95219becb3d4", "logId": "99941108-fec9-492a-be71-00fb31db79d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc3f5233-6b18-4e0a-aa2d-95219becb3d4", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708855468900}, "additional": {"logType": "detail", "children": [], "durationId": "f9b6b98d-2e84-4c92-beab-f5177d354049"}}, {"head": {"id": "8eaebd25-82a0-4503-9cce-888081c7447c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708857203900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26ebc70d-2b0b-429a-a0ae-4a1c6e4dc388", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708857370600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2801ea67-6aba-4718-80e6-1e17d7e71302", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708862710500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cb357c1-27b2-4315-a967-419497eb51a1", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 19 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708898207100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24a34712-1b9b-4ba0-9089-b25cf282ece4", "name": "entry : default@GenerateLoaderJson cost memory -4.789215087890625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708898406400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99941108-fec9-492a-be71-00fb31db79d1", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708862688800, "endTime": 219708898519200}, "additional": {"logType": "info", "children": [], "durationId": "f9b6b98d-2e84-4c92-beab-f5177d354049"}}, {"head": {"id": "1f5e4db9-4d20-46e6-8b7b-69d66e247f58", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708927874900, "endTime": 219708936708900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "ba397d8b-85e5-4b26-bca3-e0b0b68c4208", "logId": "cbe99080-6074-4984-aa88-7fed07d1fe8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ba397d8b-85e5-4b26-bca3-e0b0b68c4208", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708924626800}, "additional": {"logType": "detail", "children": [], "durationId": "1f5e4db9-4d20-46e6-8b7b-69d66e247f58"}}, {"head": {"id": "f63073b3-0a78-453e-8324-d8a8e647010a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708926460100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dff4f1ed-30e7-4440-bd30-a0334f2349fc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708926636200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bd950b1-c4bc-4a72-a25a-a21422da3e4f", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708927892200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b36cc02-e518-4754-96bb-ad2411c6ff76", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708936292200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c50dd844-6557-4d4b-b025-74499a5a8c05", "name": "entry : default@ProcessLibs cost memory 0.24629974365234375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708936581500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbe99080-6074-4984-aa88-7fed07d1fe8d", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708927874900, "endTime": 219708936708900}, "additional": {"logType": "info", "children": [], "durationId": "1f5e4db9-4d20-46e6-8b7b-69d66e247f58"}}, {"head": {"id": "590019ce-ff1d-4d8e-a748-64035388cf88", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708948607000, "endTime": 219709000507000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "a8775187-1a61-45ed-812f-2cf557e37142", "logId": "13e13a6d-0ec1-411b-b071-234104b6dde2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8775187-1a61-45ed-812f-2cf557e37142", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708941166900}, "additional": {"logType": "detail", "children": [], "durationId": "590019ce-ff1d-4d8e-a748-64035388cf88"}}, {"head": {"id": "43f5748c-b475-4454-b43d-136845993bf3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708942787400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f3d1fe8-3051-4f28-950e-da8f0ec70266", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708942954300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ce01cc6-12ef-449b-ab30-e6ffcc6f2aff", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708944490500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0b9b20d-79ba-46ef-8c95-48351bfc4d50", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708948745300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "552fa057-d641-4ecf-9abd-414e1dc29b9c", "name": "Incremental task entry:default@CompileResource pre-execution cost: 49 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708999937500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "402afdee-b7c7-4072-aeb2-5d7f496dfaaa", "name": "entry : default@CompileResource cost memory 1.3989028930664062", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709000302700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13e13a6d-0ec1-411b-b071-234104b6dde2", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708948607000, "endTime": 219709000507000}, "additional": {"logType": "info", "children": [], "durationId": "590019ce-ff1d-4d8e-a748-64035388cf88"}}, {"head": {"id": "05578ade-0ac6-4d54-b00b-6a2667e6e3c2", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709011137500, "endTime": 219709015152500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "fcfed68f-9f73-4fb4-b3aa-89e78604ce1b", "logId": "f424d012-db7a-4e9c-9ec0-6e49e25552d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fcfed68f-9f73-4fb4-b3aa-89e78604ce1b", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709005155700}, "additional": {"logType": "detail", "children": [], "durationId": "05578ade-0ac6-4d54-b00b-6a2667e6e3c2"}}, {"head": {"id": "2f8ae9d3-128d-44c6-b813-34afffc0c2a8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709006664300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "099946eb-157b-44b9-851b-c440f90d16e2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709006834400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75b803a6-83ba-4932-811a-0c4f3f0b38bd", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709011158100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "196035f5-77e2-4147-9392-a3a3012bf5bc", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709011889600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8bfd467-3f98-4754-935c-a62bd505ddc9", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709014863400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e30b96f-618c-45eb-b826-b48cf96d05f4", "name": "entry : default@DoNativeStrip cost memory 0.08196258544921875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709015046100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f424d012-db7a-4e9c-9ec0-6e49e25552d6", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709011137500, "endTime": 219709015152500}, "additional": {"logType": "info", "children": [], "durationId": "05578ade-0ac6-4d54-b00b-6a2667e6e3c2"}}, {"head": {"id": "e816acbf-cf74-4a58-991f-d17c318263de", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709025474400, "endTime": 219723947313200}, "additional": {"children": ["d42267db-5990-4104-a649-a21fc1024226", "8d4aeb19-6647-4f7a-b7f3-f018f3dbe483"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed."], "detailId": "1183fee6-7571-46e2-b295-3ed2eabe115d", "logId": "41549f28-e4ce-46b8-bef2-13981a1ee87b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1183fee6-7571-46e2-b295-3ed2eabe115d", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709017795700}, "additional": {"logType": "detail", "children": [], "durationId": "e816acbf-cf74-4a58-991f-d17c318263de"}}, {"head": {"id": "1b8eb910-870a-4abf-9841-f31763abd07f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709019222500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57a790c8-e4e3-46c1-9cf5-4d1bdb437a8a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709019384100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2e3bd06-8a43-4915-9cf1-a01763f9a62e", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709025498900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2babb23f-24db-465a-be74-d22ca1eeb1ff", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709025817600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d4db0f6-7fdb-4f45-971b-c82f5f5c8d9b", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709064948200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0df1cc97-262e-40c9-88fc-0856fe510c51", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 31 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709065148700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfcb722a-e398-4f20-94ef-4b49dfc1e3a8", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709091658700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a37cae4b-dc5b-46f0-b638-839f205c0880", "name": "default@CompileArkTS work[2] is submitted.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709094059500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d42267db-5990-4104-a649-a21fc1024226", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219713629757600, "endTime": 219723935784600}, "additional": {"children": ["c7e8fadf-5581-418b-83d0-dcd6c3674fad", "330bbef1-e52d-4835-9f32-5e98a295ddde", "eaede114-d155-458e-8b45-17b04ffa9b67", "abda7001-bc0b-4331-8495-be5191c0efd5", "5aa730dd-2433-413f-bfa9-1df2547fd181", "869f0f63-83f0-4833-896d-f8c75c912d18", "35959898-5226-404e-9edf-182923c34e18"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "e816acbf-cf74-4a58-991f-d17c318263de", "logId": "e193ec54-99b2-458f-a8bf-919cc8364cd1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "394f3299-8af7-462e-92f6-df9d79483bd4", "name": "default@CompileArkTS work[2] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709095451300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3baebe20-9872-49a1-9b11-687321ed90c8", "name": "default@CompileArkTS work[2] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709095676400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c59c67b5-0e4d-4011-9680-d76ece5c450a", "name": "CopyResources startTime: 219709095825200", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709095830800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2abff327-a608-468d-a427-217ec1f197e1", "name": "default@CompileArkTS work[3] is submitted.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709095919000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d4aeb19-6647-4f7a-b7f3-f018f3dbe483", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker18", "startTime": 219710485998300, "endTime": 219710506806300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "e816acbf-cf74-4a58-991f-d17c318263de", "logId": "7488953b-224b-42b7-9bc0-74e1cbd061bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "755101b3-57fc-4035-b3f6-736862201233", "name": "default@CompileArkTS work[3] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709096818500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f3869cb-76ed-4797-a662-0db7dcad945e", "name": "default@CompileArkTS work[3] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709096910000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34c18bb3-1827-48c7-a3ab-24ad260c6f31", "name": "entry : default@CompileArkTS cost memory 2.2087554931640625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709097049800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "920a61b6-cc3f-4939-bbbd-f82dbced9ee6", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709105123100, "endTime": 219709112601600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "c19f4a1a-5126-4156-b0a1-57c887c5c8ab", "logId": "baa07f7d-c64c-495d-8a0d-a25e7d914305"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c19f4a1a-5126-4156-b0a1-57c887c5c8ab", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709098706600}, "additional": {"logType": "detail", "children": [], "durationId": "920a61b6-cc3f-4939-bbbd-f82dbced9ee6"}}, {"head": {"id": "28c11cf1-dbf8-4213-8f48-ca4aee764d38", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709100151200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f742bd6-bd39-4985-9a03-44a2d4d3e42b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709100287300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6f9124c-b910-4209-95be-94089556645a", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709105138200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4a12e94-b381-432d-a112-5e08ce9d821b", "name": "entry : default@BuildJS cost memory 0.34688568115234375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709112371400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "321326dd-ab4f-4aab-8b33-3a7fbd166ff2", "name": "runTaskFromQueue task cost before running: 943 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709112534300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baa07f7d-c64c-495d-8a0d-a25e7d914305", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709105123100, "endTime": 219709112601600, "totalTime": 7383700}, "additional": {"logType": "info", "children": [], "durationId": "920a61b6-cc3f-4939-bbbd-f82dbced9ee6"}}, {"head": {"id": "5f70a1bd-a585-4fe2-8e91-10ffcef0ee80", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709117719500, "endTime": 219709121393400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "b6ceacc8-1d65-4b09-90d7-6fc029c140f2", "logId": "0bbac623-2752-463c-8877-eb6e8f776af3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6ceacc8-1d65-4b09-90d7-6fc029c140f2", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709114160300}, "additional": {"logType": "detail", "children": [], "durationId": "5f70a1bd-a585-4fe2-8e91-10ffcef0ee80"}}, {"head": {"id": "4337606d-71c5-443e-804f-dd4ee1fb291e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709115129600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da1ede94-428f-4e37-ae65-7eed9682443f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709115234200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af8bba54-2ec0-4894-a366-d9707b24ec8e", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709117734000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6648ea9e-1f9b-428e-b1d2-91a710f64398", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709118703700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57a05dab-1bf5-42bc-9c71-d66cba29c357", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709121192500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e721c23-870f-43bd-a49d-5e957d7c8d26", "name": "entry : default@CacheNativeLibs cost memory 0.07723236083984375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709121323800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bbac623-2752-463c-8877-eb6e8f776af3", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709117719500, "endTime": 219709121393400}, "additional": {"logType": "info", "children": [], "durationId": "5f70a1bd-a585-4fe2-8e91-10ffcef0ee80"}}, {"head": {"id": "265ad06d-ef19-4f96-8c18-84ceabaeaa11", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709305280300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd49066a-fb14-4031-96ff-b4c97c16bde3", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709305649300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d38eef72-77fd-42d2-b860-5b15ca5846fb", "name": "default@CompileArkTS work[3] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709307343400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1252143a-d672-4d0f-a39b-e61d3870b453", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709958830500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8175af9a-0c2f-45b1-acd9-5eaa0da8aa0e", "name": "A work dispatched to worker[18] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709959215900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6729e31-0a3b-4294-b068-eb880f707654", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709959410100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e49c113d-a2ba-4792-b6b8-2a1b055aa092", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709959522700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a54ecfd-d601-4836-b363-fb5b2332be75", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709959560700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f1e7ad2-e77e-44f0-8a26-bc42e7351e04", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709959650900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b63c942-dedc-4048-bf5c-6f7f9cbf526b", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709959874400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb642cd7-a583-4144-8189-2f92f5cc5ccb", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709959939000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ecd6bed-d434-4935-a824-5e7dbddab0c4", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709959969900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdc22610-66b4-4da3-b098-ac7c47a40878", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709959997500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ec29b1d-449d-4e87-8f3b-20667bff1b18", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709960023200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4596ddd-9fb0-4747-89e1-515196c186ef", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709960048500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de360cd3-d898-4bb9-a713-b461f49b3292", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709960074500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a60144a-6cec-4f7e-83aa-db75a90fe6ff", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709960101100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "016f31b0-2634-44c9-a8ae-0fdcac201fe5", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709960126200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b82a4ca5-3963-4345-8519-709953afac25", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709960172500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a682b542-499e-4a3f-8287-862fd38ec56b", "name": "default@CompileArkTS work[2] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709961152700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cfb7242-f76b-4ea0-bb4d-573419bdfda6", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219710507133900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f11ccce-09c6-4627-964e-b0d9f0cf5fd8", "name": "CopyResources is end, endTime: 219710507350800", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219710507364200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa871d22-8230-4a7f-b6b4-6b72b89cf702", "name": "default@CompileArkTS work[3] done.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219710507687100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7488953b-224b-42b7-9bc0-74e1cbd061bf", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Worker18", "startTime": 219710485998300, "endTime": 219710506806300}, "additional": {"logType": "info", "children": [], "durationId": "8d4aeb19-6647-4f7a-b7f3-f018f3dbe483", "parent": "41549f28-e4ce-46b8-bef2-13981a1ee87b"}}, {"head": {"id": "800109c1-855f-4a0d-b0c1-a95e922f5576", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219711050136700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe660a61-fc2b-4cfb-b19e-069675dbfa3c", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219723936247700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7e8fadf-5581-418b-83d0-dcd6c3674fad", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219713631013800, "endTime": 219715103184400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d42267db-5990-4104-a649-a21fc1024226", "logId": "89037504-d6dd-4142-9c85-d7cd900861b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "89037504-d6dd-4142-9c85-d7cd900861b2", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219713631013800, "endTime": 219715103184400}, "additional": {"logType": "info", "children": [], "durationId": "c7e8fadf-5581-418b-83d0-dcd6c3674fad", "parent": "e193ec54-99b2-458f-a8bf-919cc8364cd1"}}, {"head": {"id": "330bbef1-e52d-4835-9f32-5e98a295ddde", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219715105021100, "endTime": 219715232098200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d42267db-5990-4104-a649-a21fc1024226", "logId": "a2cd0d7f-9579-460c-a768-f905e52acc17"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a2cd0d7f-9579-460c-a768-f905e52acc17", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219715105021100, "endTime": 219715232098200}, "additional": {"logType": "info", "children": [], "durationId": "330bbef1-e52d-4835-9f32-5e98a295ddde", "parent": "e193ec54-99b2-458f-a8bf-919cc8364cd1"}}, {"head": {"id": "eaede114-d155-458e-8b45-17b04ffa9b67", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219715232326200, "endTime": 219715233043200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d42267db-5990-4104-a649-a21fc1024226", "logId": "993e1dfb-7851-4eb1-b6c1-398ead13b4d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "993e1dfb-7851-4eb1-b6c1-398ead13b4d6", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219715232326200, "endTime": 219715233043200}, "additional": {"logType": "info", "children": [], "durationId": "eaede114-d155-458e-8b45-17b04ffa9b67", "parent": "e193ec54-99b2-458f-a8bf-919cc8364cd1"}}, {"head": {"id": "abda7001-bc0b-4331-8495-be5191c0efd5", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219715233337900, "endTime": 219723600801900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d42267db-5990-4104-a649-a21fc1024226", "logId": "3e8bb329-c174-4b07-97cb-b999a309ad92"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e8bb329-c174-4b07-97cb-b999a309ad92", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219715233337900, "endTime": 219723600801900}, "additional": {"logType": "info", "children": [], "durationId": "abda7001-bc0b-4331-8495-be5191c0efd5", "parent": "e193ec54-99b2-458f-a8bf-919cc8364cd1"}}, {"head": {"id": "5aa730dd-2433-413f-bfa9-1df2547fd181", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219723600977600, "endTime": 219723619609900}, "additional": {"children": ["50658797-efc1-4235-9a79-aac399ab4551", "653652fc-4120-470d-aa09-166b11f88a7b", "32ab55c8-c190-4431-bdc9-cbdab96064be"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d42267db-5990-4104-a649-a21fc1024226", "logId": "f7b952c2-d15f-4782-aa32-c9886ff38f5a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7b952c2-d15f-4782-aa32-c9886ff38f5a", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219723600977600, "endTime": 219723619609900}, "additional": {"logType": "info", "children": ["eef6ed4e-df85-4db8-a246-747c642984cc", "a300fcd7-61a3-4e62-b584-f0c3b2f1f21c", "42e5e78d-b8d1-4a2c-88f2-3374cff54047"], "durationId": "5aa730dd-2433-413f-bfa9-1df2547fd181", "parent": "e193ec54-99b2-458f-a8bf-919cc8364cd1"}}, {"head": {"id": "50658797-efc1-4235-9a79-aac399ab4551", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219723601189500, "endTime": 219723601216700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5aa730dd-2433-413f-bfa9-1df2547fd181", "logId": "eef6ed4e-df85-4db8-a246-747c642984cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eef6ed4e-df85-4db8-a246-747c642984cc", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219723601189500, "endTime": 219723601216700}, "additional": {"logType": "info", "children": [], "durationId": "50658797-efc1-4235-9a79-aac399ab4551", "parent": "f7b952c2-d15f-4782-aa32-c9886ff38f5a"}}, {"head": {"id": "653652fc-4120-470d-aa09-166b11f88a7b", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219723601229600, "endTime": 219723610664500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5aa730dd-2433-413f-bfa9-1df2547fd181", "logId": "a300fcd7-61a3-4e62-b584-f0c3b2f1f21c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a300fcd7-61a3-4e62-b584-f0c3b2f1f21c", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219723601229600, "endTime": 219723610664500}, "additional": {"logType": "info", "children": [], "durationId": "653652fc-4120-470d-aa09-166b11f88a7b", "parent": "f7b952c2-d15f-4782-aa32-c9886ff38f5a"}}, {"head": {"id": "32ab55c8-c190-4431-bdc9-cbdab96064be", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219723610891500, "endTime": 219723619510800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5aa730dd-2433-413f-bfa9-1df2547fd181", "logId": "42e5e78d-b8d1-4a2c-88f2-3374cff54047"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42e5e78d-b8d1-4a2c-88f2-3374cff54047", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219723610891500, "endTime": 219723619510800}, "additional": {"logType": "info", "children": [], "durationId": "32ab55c8-c190-4431-bdc9-cbdab96064be", "parent": "f7b952c2-d15f-4782-aa32-c9886ff38f5a"}}, {"head": {"id": "869f0f63-83f0-4833-896d-f8c75c912d18", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219723619649700, "endTime": 219723929524400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d42267db-5990-4104-a649-a21fc1024226", "logId": "d1c36a3a-232d-488b-b9b4-1ef82d3decdf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d1c36a3a-232d-488b-b9b4-1ef82d3decdf", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219723619649700, "endTime": 219723929524400}, "additional": {"logType": "info", "children": [], "durationId": "869f0f63-83f0-4833-896d-f8c75c912d18", "parent": "e193ec54-99b2-458f-a8bf-919cc8364cd1"}}, {"head": {"id": "35959898-5226-404e-9edf-182923c34e18", "name": "load compilation dependencies", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219711236894300, "endTime": 219713627488100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d42267db-5990-4104-a649-a21fc1024226", "logId": "e528e0df-96ee-4e46-aa91-f22009be450c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e528e0df-96ee-4e46-aa91-f22009be450c", "name": "load compilation dependencies", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219711236894300, "endTime": 219713627488100}, "additional": {"logType": "info", "children": [], "durationId": "35959898-5226-404e-9edf-182923c34e18", "parent": "e193ec54-99b2-458f-a8bf-919cc8364cd1"}}, {"head": {"id": "80aa2df6-bbf2-4d51-9aac-3dcaac271159", "name": "default@CompileArkTS work[2] done.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219723946899700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e193ec54-99b2-458f-a8bf-919cc8364cd1", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 219713629757600, "endTime": 219723935784600}, "additional": {"logType": "info", "children": ["89037504-d6dd-4142-9c85-d7cd900861b2", "a2cd0d7f-9579-460c-a768-f905e52acc17", "993e1dfb-7851-4eb1-b6c1-398ead13b4d6", "3e8bb329-c174-4b07-97cb-b999a309ad92", "f7b952c2-d15f-4782-aa32-c9886ff38f5a", "d1c36a3a-232d-488b-b9b4-1ef82d3decdf", "e528e0df-96ee-4e46-aa91-f22009be450c"], "durationId": "d42267db-5990-4104-a649-a21fc1024226", "parent": "41549f28-e4ce-46b8-bef2-13981a1ee87b"}}, {"head": {"id": "41549f28-e4ce-46b8-bef2-13981a1ee87b", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219709025474400, "endTime": 219723947313200, "totalTime": 10398478700}, "additional": {"logType": "info", "children": ["e193ec54-99b2-458f-a8bf-919cc8364cd1", "7488953b-224b-42b7-9bc0-74e1cbd061bf"], "durationId": "e816acbf-cf74-4a58-991f-d17c318263de"}}, {"head": {"id": "56da6647-94a2-44f2-82fd-e3dbb0dd848a", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219723959669900, "endTime": 219723961499500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "e1a70bf5-eaaf-424b-83de-4a306ee80d86", "logId": "7a92bf9f-19f2-4947-8c32-29c30015dfa2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1a70bf5-eaaf-424b-83de-4a306ee80d86", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219723955583400}, "additional": {"logType": "detail", "children": [], "durationId": "56da6647-94a2-44f2-82fd-e3dbb0dd848a"}}, {"head": {"id": "fe3ab2f1-1e5b-4d7a-af08-5757ad9d5a6e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219723957347100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1bbf300-c027-4e54-a538-416c5746c64f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219723957520900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d07db26-c6e8-4a27-88bd-5a269a294148", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219723959717100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f840c9f-2015-4613-a5f9-a3c0df2fcd61", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219723960203900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5876ad3d-8d42-4220-9827-b133cf7aae59", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219723961289700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "013eec42-107a-4e9c-b2c8-1a15ffd5518f", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07826995849609375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219723961428000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a92bf9f-19f2-4947-8c32-29c30015dfa2", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219723959669900, "endTime": 219723961499500}, "additional": {"logType": "info", "children": [], "durationId": "56da6647-94a2-44f2-82fd-e3dbb0dd848a"}}, {"head": {"id": "87f73a0b-c44d-4410-8c9f-7e7eb2bbad4c", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219723979720400, "endTime": 219724169140500}, "additional": {"children": ["323c7e3b-bcc2-4ee9-a877-76bbb3411b6e", "d4a933ac-6ead-4b6f-a769-d7096f3faecf"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "97966f6f-ccf3-46b7-a24f-bd877b51e1fc", "logId": "d51cac55-2181-42f9-8b80-e5a5665fdb1b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "97966f6f-ccf3-46b7-a24f-bd877b51e1fc", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219723965398300}, "additional": {"logType": "detail", "children": [], "durationId": "87f73a0b-c44d-4410-8c9f-7e7eb2bbad4c"}}, {"head": {"id": "779ba1a5-492c-433c-b8ff-3fc64882a7d2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219723967911200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2d95b3c-766b-4f7c-b6a7-3127e493e3f5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219723968143200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30dab8e0-5c89-4638-ad1a-1a143a0f6360", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219723979741800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e45d7e38-df01-4284-9267-92ecf40e6c84", "name": "entry:default@PackageHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724009650500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86064835-d5c0-428c-94ef-73f144e591a6", "name": "Incremental task entry:default@PackageHap pre-execution cost: 27 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724009898200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "818ebe5b-8de4-4a63-b008-13bf03981c39", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724010008400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "012f720e-334b-4b9e-b53a-77b52cb89db9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724010060600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "323c7e3b-bcc2-4ee9-a877-76bbb3411b6e", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724011891300, "endTime": 219724016027700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "87f73a0b-c44d-4410-8c9f-7e7eb2bbad4c", "logId": "ea438ad3-c597-4d14-bcba-e79e2f1e3832"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "532e393c-d1d4-44c4-9488-88a9d5b51b5e", "name": "Use tool [C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Huawei\\\\Sdk\\\\OpenHarmony\\\\Sdk\\\\18\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--rpcid-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\syscap\\\\default\\\\rpcid.sc',\n  '--ets-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets',\n  '--pkg-context-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader\\\\default\\\\pkgContextInfo.json'\n]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724015585100}, "additional": {"logType": "debug", "children": [], "durationId": "87f73a0b-c44d-4410-8c9f-7e7eb2bbad4c"}}, {"head": {"id": "ea438ad3-c597-4d14-bcba-e79e2f1e3832", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724011891300, "endTime": 219724016027700}, "additional": {"logType": "info", "children": [], "durationId": "323c7e3b-bcc2-4ee9-a877-76bbb3411b6e", "parent": "d51cac55-2181-42f9-8b80-e5a5665fdb1b"}}, {"head": {"id": "d4a933ac-6ead-4b6f-a769-d7096f3faecf", "name": "submit HAP packaging task to java daemon server", "description": "Pack HAP in java daemon", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724017203400, "endTime": 219724161455300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "87f73a0b-c44d-4410-8c9f-7e7eb2bbad4c", "logId": "5d288ca1-4614-48d5-aaf0-42353b8cfbc4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d17a3ea-3a9a-4caf-9e9a-5f13402ef31d", "name": "java daemon socket received message:{\"code\":0,\"message\":\"pack success\"}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724160619600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d288ca1-4614-48d5-aaf0-42353b8cfbc4", "name": "submit HAP packaging task to java daemon server", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724017203400, "endTime": 219724161447500}, "additional": {"logType": "info", "children": [], "durationId": "d4a933ac-6ead-4b6f-a769-d7096f3faecf", "parent": "d51cac55-2181-42f9-8b80-e5a5665fdb1b"}}, {"head": {"id": "93b5df3d-7b41-414d-96f6-5ac98dbc4a3f", "name": "entry : default@PackageHap cost memory 1.4394073486328125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724168591100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b594443-ddc2-4ca3-9e0c-4359fbb7088a", "name": "runTaskFromQueue task cost before running: 15 s 1000 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724168959600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d51cac55-2181-42f9-8b80-e5a5665fdb1b", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219723979720400, "endTime": 219724169140500, "totalTime": 189161100}, "additional": {"logType": "info", "children": ["ea438ad3-c597-4d14-bcba-e79e2f1e3832", "5d288ca1-4614-48d5-aaf0-42353b8cfbc4"], "durationId": "87f73a0b-c44d-4410-8c9f-7e7eb2bbad4c"}}, {"head": {"id": "4b3d764d-3908-4abc-947e-2d9e255dfe88", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724182818100, "endTime": 219724535383200}, "additional": {"children": ["8d7d1b12-825f-49d5-a534-a3154f4b35a6", "de0978ba-1481-488f-bb57-4ec26d8aeff3"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "345fb485-9477-447a-92cc-d67202e8dcf2", "logId": "a466640e-2830-45a4-9877-b49c8379cd09"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "345fb485-9477-447a-92cc-d67202e8dcf2", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724176614900}, "additional": {"logType": "detail", "children": [], "durationId": "4b3d764d-3908-4abc-947e-2d9e255dfe88"}}, {"head": {"id": "38f4d6a9-a9c9-4382-8310-6704cfa01226", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724178441300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2da3436e-9813-4b37-8680-19a53a5c1e85", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724178638500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57138fb4-6833-4e67-b327-8f378638473d", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724182850500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e47f711-7776-4902-9562-6fa4b220a49e", "name": "entry:default@SignHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724186461000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9336e436-3e21-414f-9382-9c4c4deb99bc", "name": "Incremental task entry:default@SignHap pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724186703100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05c7c444-b70d-431a-8f2d-261d280e7971", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724186885400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42cfd5fa-3238-4215-b047-002042594668", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724186982800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d7d1b12-825f-49d5-a534-a3154f4b35a6", "name": "generate hap signing command", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724189653600, "endTime": 219724301607700}, "additional": {"children": ["566a0d16-d5fc-412c-a627-f0498665171b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4b3d764d-3908-4abc-947e-2d9e255dfe88", "logId": "6cd2292b-2d00-4eb0-8f48-cebb09860635"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "566a0d16-d5fc-412c-a627-f0498665171b", "name": "verify signing configuration and get bundle name", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724220531800, "endTime": 219724299454000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8d7d1b12-825f-49d5-a534-a3154f4b35a6", "logId": "cc778b21-d16c-4116-82ee-f031b0ac5afa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c6f7c7c-f830-4389-9e70-5848fccb0553", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724224779700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad58eff1-d250-4903-8a23-63009c363c6e", "name": "java daemon socket received message:{\"code\":0,\"message\":\"verify profile success\"}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724298353800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc778b21-d16c-4116-82ee-f031b0ac5afa", "name": "verify signing configuration and get bundle name", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724220531800, "endTime": 219724299454000}, "additional": {"logType": "info", "children": [], "durationId": "566a0d16-d5fc-412c-a627-f0498665171b", "parent": "6cd2292b-2d00-4eb0-8f48-cebb09860635"}}, {"head": {"id": "6cd2292b-2d00-4eb0-8f48-cebb09860635", "name": "generate hap signing command", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724189653600, "endTime": 219724301607700}, "additional": {"logType": "info", "children": ["cc778b21-d16c-4116-82ee-f031b0ac5afa"], "durationId": "8d7d1b12-825f-49d5-a534-a3154f4b35a6", "parent": "a466640e-2830-45a4-9877-b49c8379cd09"}}, {"head": {"id": "de0978ba-1481-488f-bb57-4ec26d8aeff3", "name": "execute hap signing command", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724303209300, "endTime": 219724534556700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4b3d764d-3908-4abc-947e-2d9e255dfe88", "logId": "f4ce9323-c164-4064-9b82-5edf92fbeeea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad61509f-85a1-4342-a088-0a1edadcf344", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724307719300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a435fae-4ca7-42ad-b79d-cba66fcf8c62", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724465775600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a6d30c0-7b6a-464e-9218-0f3b2ee72033", "name": "java daemon socket received message:{\"code\":0,\"message\":\"sign app success\"}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724533808600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4ce9323-c164-4064-9b82-5edf92fbeeea", "name": "execute hap signing command", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724303209300, "endTime": 219724534556700}, "additional": {"logType": "info", "children": [], "durationId": "de0978ba-1481-488f-bb57-4ec26d8aeff3", "parent": "a466640e-2830-45a4-9877-b49c8379cd09"}}, {"head": {"id": "4e43b336-6a7b-4f93-bec2-92eb9831f21c", "name": "entry : default@SignHap cost memory 1.0271148681640625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724535029000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb46af8e-80b3-4e55-b9c5-4795736e7c15", "name": "runTaskFromQueue task cost before running: 16 s 366 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724535271800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a466640e-2830-45a4-9877-b49c8379cd09", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724182818100, "endTime": 219724535383200, "totalTime": 352407100}, "additional": {"logType": "info", "children": ["6cd2292b-2d00-4eb0-8f48-cebb09860635", "f4ce9323-c164-4064-9b82-5edf92fbeeea"], "durationId": "4b3d764d-3908-4abc-947e-2d9e255dfe88"}}, {"head": {"id": "597d3080-a23a-4fcc-b8ea-089c483ae9fa", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724543330600, "endTime": 219724555205600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "19e673de-27d0-4b28-98f5-51ad79b3f7ca", "logId": "b21ee6dc-f86d-4e53-8f92-d81e90bfc78d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19e673de-27d0-4b28-98f5-51ad79b3f7ca", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724539036800}, "additional": {"logType": "detail", "children": [], "durationId": "597d3080-a23a-4fcc-b8ea-089c483ae9fa"}}, {"head": {"id": "01a27a34-d8e6-4155-b08c-c79cf418f8c5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724541068500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6f34df0-32e0-456c-b11f-6073fee4ad3e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724541388000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff13e5cd-a144-4845-bf0e-4340bf654b8e", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724543365800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f61ea1a0-4e0b-4806-9711-677b428a6975", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724554717800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a71f307-7903-44e4-a206-ee43a90b87d7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724554899300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "639ee104-4fe8-42a0-99b8-799c529c8c3d", "name": "entry : default@CollectDebugSymbol cost memory 0.24373626708984375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724555018600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dd94bee-b898-44e6-8700-33fe3439cd59", "name": "runTaskFromQueue task cost before running: 16 s 386 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724555139100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b21ee6dc-f86d-4e53-8f92-d81e90bfc78d", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724543330600, "endTime": 219724555205600, "totalTime": 11770500}, "additional": {"logType": "info", "children": [], "durationId": "597d3080-a23a-4fcc-b8ea-089c483ae9fa"}}, {"head": {"id": "2811e4d2-b235-41e0-b59d-df55cc87657c", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724557752900, "endTime": 219724558395400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "d7f5dcff-1959-461b-80e8-eee671637948", "logId": "725a4b47-b2c9-4325-a2ba-de22012a3b68"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7f5dcff-1959-461b-80e8-eee671637948", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724557586400}, "additional": {"logType": "detail", "children": [], "durationId": "2811e4d2-b235-41e0-b59d-df55cc87657c"}}, {"head": {"id": "0726d484-01ce-4316-b4d6-466d6462176a", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724557777100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea88ef84-bd05-4a47-a71b-67af996d19a8", "name": "entry : assembleHap cost memory 0.0117950439453125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724558073400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60ad3406-4043-4fb7-b0cb-1af72418e1ec", "name": "runTaskFromQueue task cost before running: 16 s 389 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724558270000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "725a4b47-b2c9-4325-a2ba-de22012a3b68", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724557752900, "endTime": 219724558395400, "totalTime": 476400}, "additional": {"logType": "info", "children": [], "durationId": "2811e4d2-b235-41e0-b59d-df55cc87657c"}}, {"head": {"id": "08eef449-6cd4-427f-89de-a3244944f6f6", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724575413000, "endTime": 219724575462300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0c905057-2251-4b8e-8380-cf6c51f796e9", "logId": "db7ef767-289d-4456-9dcd-9a116a9dbf86"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db7ef767-289d-4456-9dcd-9a116a9dbf86", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724575413000, "endTime": 219724575462300}, "additional": {"logType": "info", "children": [], "durationId": "08eef449-6cd4-427f-89de-a3244944f6f6"}}, {"head": {"id": "4653b9b6-25f2-4941-b655-0b8c6e054230", "name": "BUILD SUCCESSFUL in 16 s 406 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724575550500}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "1afe0a6c-bbc1-42ed-9b08-c0cccf764a84", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219708169634500, "endTime": 219724575971900}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 18, "minute": 59, "second": 14}, "completeCommand": "{\"prop\":[\"product=default\"],\"mode\":\"module\",\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "d03d044c-822e-4309-8d83-7e934874386c", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724576009700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43eb622d-ad8e-492e-b434-0ff36c3c0b48", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724576109900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5796feff-876b-41ae-a232-d8b299bac97f", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724576780800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb4ff933-cfc3-4d78-8222-f7cb86796a27", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724576900300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e00e074a-2070-4efc-95a4-ddf0e582227d", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724576977900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "615dfce7-f212-4b90-92c0-3fca64a8dd62", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724577053900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a5eacd8-98f4-4a5f-bbd7-09942e03fe9e", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724577123400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81cdcb21-7fee-491f-ad11-2f9c5ea8bc32", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724577940800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a636557a-2270-4405-bafe-182822e32e08", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724578250200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e036891-999a-4a01-8393-946404e8ac81", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724578329900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a704efe5-0e96-4ed9-b709-bbc32333cba7", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724578376200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1aac2157-81b3-490e-b51f-10f8a32b927d", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724578415000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca167228-0419-4318-a3d3-22c5a5d65e36", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724578453600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c86a87be-2101-44ed-b26e-125af6d57132", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724579869800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb5d9dbd-2be1-4e58-8cca-37d6e9e5cb50", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724580246200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e845740-2649-4aea-8f33-46f8fc3a8d10", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724580526200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf1f5859-57cb-4ddb-aba8-96e4e49ba391", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724580620900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "547fcba8-8b95-4182-add2-c0dcd2c5e273", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724580671500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50e914c7-3883-4c1e-b618-6ff59b7216ed", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724580714100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec414be4-90f6-4f03-acf4-557ae0730305", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724580751200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1569d917-5bb6-4477-bff1-f5f7b8f1be0c", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724580783900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd511d76-9cd5-4dd4-86a2-870e2bf858b2", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724584894100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e36d539f-4a01-4813-9448-5027fd13b238", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724585910600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39bd2769-c894-43e3-89d0-83abbe6cc3fb", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724586470700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e124d711-1abc-43d7-b2f1-a657f9d8289d", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724586785100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a3e4493-f747-43f1-ba11-763607685125", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724587055300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb4a4183-6c99-4af4-bd41-75f63ba07fb0", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724587923600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c0251d7-ee95-4e4b-a61b-baa9e52285e9", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724588009900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b9bb761-6950-4374-ad80-7f3f32e7173f", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724588271600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8b2acb7-fd67-4d5b-9b9a-d82e3d1fe867", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724588693100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7b783b3-89df-4274-b5e5-dcfac9f70ccf", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724590003800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "411c84ba-5f91-4b66-aef8-75ed8613224d", "name": "Incremental task entry:default@CompileArkTS post-execution cost:11 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724591172900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04321ed2-aab1-4452-9538-20b4a02150ea", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724594046300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38e156ac-0e09-42af-8551-0b973c28c957", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724594924300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d41a8f4-7e01-4280-a31a-21a7a9b8610d", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724595499300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d94f8dc1-16a8-4528-8e1c-827a8b06de44", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724595833500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bd4b2d3-312d-438d-a5ae-1dd284a120c9", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724596124400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "296172e5-7fdc-42ef-af5f-382bcf574692", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724597067300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db6dce24-7250-4b0a-b14a-3410443d8093", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724598492300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0ff9cb8-3da9-4416-98b6-0bf0878b85dd", "name": "Incremental task entry:default@BuildJS post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724599006400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78cec283-bf41-4258-a9eb-83fd14e76354", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724599142500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68f6cc00-66af-4973-b8ec-96a2db4f8eb9", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724599244800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbd93c51-f4c0-45c8-a3c1-89ba763364a1", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724601432200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6dfea77-b4b7-4834-950f-939abf4a039a", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724602102100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "320a0af1-57bb-4298-9ba3-9d938410b08d", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724602448100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1040209d-405d-4316-b431-69b330c90efb", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724612263600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cf7f694-47f3-4d7d-82f6-a5e83388d62d", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724612670100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c225c70b-0b20-45b1-a3b2-80e7ad2d34fa", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724612953800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff06e9a4-19e2-4731-829a-bb18534d2ce9", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724613254600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4c1fa2b-4e0b-44d7-95a2-b39f0cb54b59", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724613319400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3018847-095f-40ee-9216-22f8513b129f", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724613549500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10b7281c-15ff-4276-8907-4e653e0a70c7", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724613778700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1e30438-65cf-4e0d-b401-8520b7784dc6", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724614965800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5d418f4-769c-41b6-bf77-c96803378be1", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724615442700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "854d5d49-a9b7-4f17-a2dd-7a5b3e92659f", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724615798400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2816a21-c7b1-4cfa-ad3f-64faf193e83b", "name": "Incremental task entry:default@PackageHap post-execution cost:17 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724616225800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "975b915b-0c6e-495b-95b3-a08ee1992d0b", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.cer cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724616636800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2d20fc1-2ffe-435c-b49c-77e478c2d7f3", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p7b cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724616937800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37e35b79-9a61-4a1c-82b2-94a8bdd828d4", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p12 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724617188100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2128babb-d670-485e-8d1e-02a4f1a44412", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724617428500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8544662-bfd2-430b-b2af-ad8a3bb87745", "name": "Update task entry:default@SignHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724617510500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6759302b-9ac8-49b1-bdd3-80f3c4ee15e1", "name": "Incremental task entry:default@SignHap post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724617812300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20c3b08d-c76e-4604-81a2-828aa7a1bcba", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724620912600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc232a11-26f3-4a3d-8757-7613e48e097c", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724621274600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee385835-c634-450b-b411-00402b87c9da", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724623571500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e46f88c-5dfe-488b-88a7-7ec70c32f88c", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:7 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219724624188200}, "additional": {"logType": "debug", "children": []}}], "workLog": []}