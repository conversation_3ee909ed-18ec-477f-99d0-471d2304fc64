# RabbitMQ 消息队列集成文档

## 概述

NexusHub-OH 后端项目已成功集成 RabbitMQ 消息队列，用于处理异步任务和事件通知。本文档详细介绍了消息队列的架构、使用方法和最佳实践。

## 架构设计

### 核心组件

1. **RabbitMQ 客户端** (`pkg/messaging/rabbitmq.go`)
   - 负责与 RabbitMQ 服务器的连接和通信
   - 提供队列声明、消息发布和消费功能

2. **消息服务** (`internal/services/message_service.go`)
   - 封装消息队列操作的业务逻辑
   - 定义消息类型和队列常量
   - 提供高级消息发布接口

3. **消息控制器** (`internal/api/message_controller.go`)
   - 提供 HTTP API 接口来操作消息队列
   - 支持发送通知、邮件、用户活动记录等

4. **消息工作者** (`internal/workers/message_worker.go`)
   - 消费队列中的消息并执行相应的业务逻辑
   - 支持多种消息类型的处理

### 队列设计

项目定义了以下消息队列：

| 队列名称 | 用途 | 消息类型 |
|---------|------|----------|
| `app_review_queue` | 应用审核流程 | 应用提交、通过、拒绝 |
| `notification_queue` | 用户通知 | 系统通知、提醒消息 |
| `email_send_queue` | 邮件发送 | 注册确认、密码重置等 |
| `app_analytics_queue` | 应用分析 | 下载统计、使用分析 |
| `user_activity_queue` | 用户活动 | 登录、操作记录 |

## 配置说明

### 配置文件

在 `config/config.yaml` 中配置 RabbitMQ 连接信息：

```yaml
# RabbitMQ配置
rabbitmq:
  host: "*************"
  port: "5672"
  user: "rabbitmq"
  password: "rabbitmq"
```

### 环境变量

也可以通过环境变量覆盖配置：

```bash
export RABBITMQ_HOST=localhost
export RABBITMQ_PORT=5672
export RABBITMQ_USER=guest
export RABBITMQ_PASSWORD=guest
```

## API 接口

### 认证要求

所有消息队列相关的 API 都需要 JWT 认证，请在请求头中包含：

```
Authorization: Bearer <your-jwt-token>
```

### 接口列表

#### 1. 发送通知消息

```http
POST /api/v1/messages/notification
Content-Type: application/json

{
  "user_id": 1,
  "title": "系统通知",
  "content": "您的应用审核已通过",
  "type": "success"
}
```

#### 2. 发送邮件消息

```http
POST /api/v1/messages/email
Content-Type: application/json

{
  "to": "<EMAIL>",
  "subject": "欢迎注册 NexusHub",
  "body": "感谢您注册我们的服务...",
  "is_html": true
}
```

#### 3. 记录用户活动

```http
POST /api/v1/messages/activity
Content-Type: application/json

{
  "user_id": 1,
  "action": "download",
  "resource": "app",
  "resource_id": 123
}
```

#### 4. 触发应用审核

```http
POST /api/v1/messages/app-review/123?action=approve&app_name=MyApp&developer_id=1
```

#### 5. 获取队列状态

```http
GET /api/v1/messages/status
```

## 消息类型定义

### 基础消息结构

```go
type BaseMessage struct {
    ID        string      `json:"id"`
    Type      MessageType `json:"type"`
    Timestamp time.Time   `json:"timestamp"`
    Data      interface{} `json:"data"`
}
```

### 应用审核消息

```go
type AppReviewMessage struct {
    AppID       uint   `json:"app_id"`
    AppName     string `json:"app_name"`
    DeveloperID uint   `json:"developer_id"`
    Action      string `json:"action"` // submit, approve, reject
    Reason      string `json:"reason,omitempty"`
}
```

### 通知消息

```go
type NotificationMessage struct {
    UserID  uint   `json:"user_id"`
    Title   string `json:"title"`
    Content string `json:"content"`
    Type    string `json:"type"` // info, warning, error, success
}
```

### 邮件消息

```go
type EmailMessage struct {
    To      string `json:"to"`
    Subject string `json:"subject"`
    Body    string `json:"body"`
    IsHTML  bool   `json:"is_html"`
}
```

### 用户活动消息

```go
type UserActivityMessage struct {
    UserID     uint      `json:"user_id"`
    Action     string    `json:"action"`
    Resource   string    `json:"resource"`
    ResourceID uint      `json:"resource_id,omitempty"`
    IP         string    `json:"ip"`
    UserAgent  string    `json:"user_agent"`
    Timestamp  time.Time `json:"timestamp"`
}
```

## 使用示例

### 在业务代码中发送消息

```go
// 在控制器或服务中使用消息服务
func (c *AppController) ApproveApp(ctx *gin.Context) {
    // ... 业务逻辑 ...
    
    // 发送应用审核通过消息
    err := messageService.PublishAppReviewMessage(
        ctx.Request.Context(),
        appID,
        appName,
        developerID,
        "approve",
        "",
    )
    if err != nil {
        logger.Error("发送审核消息失败", zap.Error(err))
    }
    
    // 发送通知给开发者
    err = messageService.PublishNotificationMessage(
        ctx.Request.Context(),
        developerID,
        "应用审核通过",
        fmt.Sprintf("您的应用 %s 已通过审核", appName),
        "success",
    )
    if err != nil {
        logger.Error("发送通知失败", zap.Error(err))
    }
}
```

### 自定义消息处理器

```go
// 扩展消息工作者以处理自定义业务逻辑
func (w *MessageWorker) handleAppApproved(msg services.AppReviewMessage) error {
    // 1. 更新应用状态
    result := w.db.Model(&models.App{}).Where("id = ?", msg.AppID).Update("status", "published")
    if result.Error != nil {
        return fmt.Errorf("更新应用状态失败: %w", result.Error)
    }
    
    // 2. 更新搜索索引
    // searchService.IndexApp(msg.AppID)
    
    // 3. 发送邮件通知
    emailBody := fmt.Sprintf("恭喜！您的应用 %s 已通过审核并发布", msg.AppName)
    return w.messageService.PublishEmailMessage(
        context.Background(),
        "<EMAIL>", // 从数据库获取开发者邮箱
        "应用审核通过通知",
        emailBody,
        false,
    )
}
```

## 监控和调试

### 日志记录

系统会自动记录消息队列的关键操作：

- 消息发布成功/失败
- 消息消费成功/失败
- 队列连接状态
- 工作者启动状态

### 队列状态监控

可以通过以下方式监控队列状态：

1. **API 接口**：`GET /api/v1/messages/status`
2. **RabbitMQ 管理界面**：访问 `http://rabbitmq-host:15672`
3. **日志文件**：查看 `logs/app.log`

### 常见问题排查

1. **连接失败**
   - 检查 RabbitMQ 服务是否运行
   - 验证连接配置（主机、端口、用户名、密码）
   - 检查网络连接

2. **消息处理失败**
   - 查看工作者日志
   - 检查消息格式是否正确
   - 验证业务逻辑是否有异常

3. **队列堆积**
   - 检查消费者是否正常运行
   - 增加消费者数量
   - 优化消息处理逻辑

## 最佳实践

### 消息设计

1. **幂等性**：确保消息处理是幂等的，重复处理不会产生副作用
2. **错误处理**：实现适当的错误处理和重试机制
3. **消息大小**：保持消息体积小，避免传输大量数据
4. **版本兼容**：设计消息格式时考虑向后兼容性

### 性能优化

1. **批量处理**：对于高频消息，考虑批量处理
2. **连接复用**：复用 RabbitMQ 连接和通道
3. **预取设置**：合理设置消费者预取数量
4. **持久化策略**：根据业务需求选择消息持久化策略

### 安全考虑

1. **认证授权**：使用强密码和适当的用户权限
2. **网络安全**：在生产环境中使用 TLS 加密
3. **消息验证**：验证消息来源和内容的合法性
4. **敏感信息**：避免在消息中传输敏感信息

## 扩展功能

### 延迟消息

可以通过 RabbitMQ 的延迟插件实现延迟消息功能：

```go
// 发送延迟消息（需要安装 rabbitmq-delayed-message-exchange 插件）
func (r *RabbitMQClient) PublishDelayedMessage(ctx context.Context, exchange, routingKey string, message []byte, delay time.Duration) error {
    return r.Channel.PublishWithContext(
        ctx,
        exchange,
        routingKey,
        false,
        false,
        amqp.Publishing{
            ContentType:  "application/json",
            DeliveryMode: amqp.Persistent,
            Timestamp:    time.Now(),
            Body:         message,
            Headers: amqp.Table{
                "x-delay": int64(delay / time.Millisecond),
            },
        },
    )
}
```

### 死信队列

配置死信队列处理失败的消息：

```go
// 声明带死信队列的队列
func (r *RabbitMQClient) DeclareQueueWithDLX(name, dlxName string) (amqp.Queue, error) {
    return r.Channel.QueueDeclare(
        name,
        true,  // durable
        false, // autoDelete
        false, // exclusive
        false, // noWait
        amqp.Table{
            "x-dead-letter-exchange": dlxName,
            "x-message-ttl":          300000, // 5分钟TTL
        },
    )
}
```

## 总结

RabbitMQ 消息队列的集成为 NexusHub-OH 项目提供了强大的异步处理能力，支持：

- ✅ 应用审核流程自动化
- ✅ 用户通知系统
- ✅ 邮件发送服务
- ✅ 用户活动追踪
- ✅ 应用分析统计
- ✅ 可扩展的消息处理架构
- ✅ 完整的错误处理和日志记录
- ✅ RESTful API 接口

通过合理使用消息队列，可以显著提高系统的响应性能、可靠性和可扩展性。