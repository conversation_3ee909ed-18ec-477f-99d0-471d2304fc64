import { <PERSON>Container } from '@ant-design/pro-components';
import { Card, Table, Button, Space, DatePicker, Select, Radio, Tabs, message, Typography, Row, Col } from 'antd';
import { DownloadOutlined, FileExcelOutlined, FilePdfOutlined, BarChartOutlined, Line<PERSON>hartOutlined, PieChartOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { useRequest } from '@umijs/max';
import { Line, Column, Pie } from '@ant-design/plots';
import type { ColumnsType } from 'antd/es/table';

const { RangePicker } = DatePicker;
const { Title, Paragraph } = Typography;

interface ReportData {
  id: string;
  name: string;
  type: 'daily' | 'weekly' | 'monthly' | 'custom';
  createTime: string;
  creator: string;
  status: 'generating' | 'completed' | 'failed';
  downloadUrl?: string;
}

interface ChartData {
  date: string;
  value: number;
  type: string;
}

// 模拟数据获取函数
const fetchReportList = async (params: any) => {
  // 实际项目中应该调用API
  console.log('Fetching report list with params:', params);
  
  // 模拟数据
  const mockData: ReportData[] = [
    {
      id: '1',
      name: '每日应用下载报表',
      type: 'daily',
      createTime: '2023-05-01 10:00:00',
      creator: 'admin',
      status: 'completed',
      downloadUrl: 'https://example.com/reports/daily-app-downloads.xlsx',
    },
    {
      id: '2',
      name: '每周销售额报表',
      type: 'weekly',
      createTime: '2023-05-02 14:30:00',
      creator: 'admin',
      status: 'completed',
      downloadUrl: 'https://example.com/reports/weekly-sales.xlsx',
    },
    {
      id: '3',
      name: '每月用户增长报表',
      type: 'monthly',
      createTime: '2023-05-03 09:15:00',
      creator: 'admin',
      status: 'generating',
    },
    {
      id: '4',
      name: '自定义时间段报表',
      type: 'custom',
      createTime: '2023-05-04 16:45:00',
      creator: 'manager',
      status: 'failed',
    },
  ];

  return { data: mockData, total: mockData.length };
};

// 模拟图表数据获取函数
const fetchChartData = async (params: any) => {
  // 实际项目中应该调用API
  console.log('Fetching chart data with params:', params);
  
  // 模拟数据
  const mockData: ChartData[] = [
    // 下载量数据
    { date: '2023-01', value: 1200, type: '下载量' },
    { date: '2023-02', value: 1500, type: '下载量' },
    { date: '2023-03', value: 2000, type: '下载量' },
    { date: '2023-04', value: 1800, type: '下载量' },
    { date: '2023-05', value: 2200, type: '下载量' },
    { date: '2023-06', value: 2500, type: '下载量' },
    // 销售额数据
    { date: '2023-01', value: 5000, type: '销售额' },
    { date: '2023-02', value: 6500, type: '销售额' },
    { date: '2023-03', value: 8000, type: '销售额' },
    { date: '2023-04', value: 7500, type: '销售额' },
    { date: '2023-05', value: 9000, type: '销售额' },
    { date: '2023-06', value: 10000, type: '销售额' },
    // 用户数据
    { date: '2023-01', value: 3000, type: '用户数' },
    { date: '2023-02', value: 3300, type: '用户数' },
    { date: '2023-03', value: 3600, type: '用户数' },
    { date: '2023-04', value: 4000, type: '用户数' },
    { date: '2023-05', value: 4500, type: '用户数' },
    { date: '2023-06', value: 5000, type: '用户数' },
  ];

  return { data: mockData };
};

const ReportGeneration: React.FC = () => {
  const [searchParams, setSearchParams] = useState({});
  const [reportType, setReportType] = useState<string>('daily');
  const [dateRange, setDateRange] = useState<[string, string] | null>(null);
  const [chartType, setChartType] = useState<string>('line');
  
  const { data: reportList, loading: reportLoading } = useRequest(() => fetchReportList(searchParams), {
    refreshDeps: [searchParams],
  });

  const { data: chartData, loading: chartLoading } = useRequest(() => fetchChartData({
    type: reportType,
    dateRange,
  }), {
    refreshDeps: [reportType, dateRange],
  });

  const handleGenerateReport = () => {
    message.success('报表生成任务已提交，请稍后查看');
    // 实际项目中应该调用API
  };

  const handleDownload = (record: ReportData) => {
    if (record.downloadUrl) {
      message.success(`正在下载报表: ${record.name}`);
      // 实际项目中应该调用下载API或打开新窗口
      window.open(record.downloadUrl, '_blank');
    } else {
      message.error('下载链接不可用');
    }
  };

  const handleReportTypeChange = (e: any) => {
    setReportType(e.target.value);
  };

  const handleDateRangeChange = (dates: any, dateStrings: [string, string]) => {
    setDateRange(dateStrings);
  };

  const handleChartTypeChange = (e: any) => {
    setChartType(e.target.value);
  };

  const columns: ColumnsType<ReportData> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '报表名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '报表类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => {
        const typeMap: Record<string, string> = {
          daily: '日报',
          weekly: '周报',
          monthly: '月报',
          custom: '自定义',
        };
        return typeMap[type] || type;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      sorter: (a, b) => a.createTime.localeCompare(b.createTime),
    },
    {
      title: '创建者',
      dataIndex: 'creator',
      key: 'creator',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusMap: Record<string, { text: string; color: string }> = {
          generating: { text: '生成中', color: 'orange' },
          completed: { text: '已完成', color: 'green' },
          failed: { text: '失败', color: 'red' },
        };
        const { text, color } = statusMap[status] || { text: status, color: 'default' };
        return <span style={{ color }}>{text}</span>;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          {record.status === 'completed' && record.downloadUrl && (
            <Button 
              type="primary" 
              icon={<DownloadOutlined />} 
              size="small"
              onClick={() => handleDownload(record)}
            >
              下载
            </Button>
          )}
        </Space>
      ),
    },
  ];

  // 图表配置
  const lineConfig = {
    data: chartData?.data || [],
    xField: 'date',
    yField: 'value',
    seriesField: 'type',
    smooth: true,
    animation: {
      appear: {
        animation: 'path-in',
        duration: 1000,
      },
    },
  };

  const columnConfig = {
    data: chartData?.data || [],
    xField: 'date',
    yField: 'value',
    seriesField: 'type',
    isGroup: true,
    columnStyle: {
      radius: [20, 20, 0, 0],
    },
    animation: {
      appear: {
        animation: 'wave-in',
        duration: 1000,
      },
    },
  };

  // 为饼图准备数据
  const getPieData = () => {
    if (!chartData?.data) return [];
    
    // 按类型分组并计算总和
    const groupedData = chartData.data.reduce((acc, item) => {
      if (!acc[item.type]) {
        acc[item.type] = 0;
      }
      acc[item.type] += item.value;
      return acc;
    }, {} as Record<string, number>);
    
    // 转换为饼图所需格式
    return Object.entries(groupedData).map(([type, value]) => ({
      type,
      value,
    }));
  };

  const pieConfig = {
    data: getPieData(),
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name} {percentage}',
    },
    interactions: [{ type: 'pie-legend-active' }, { type: 'element-active' }],
  };

  return (
    <PageContainer>
      <Card title="报表生成" bordered={false}>
        <Tabs defaultActiveKey="generate">
          <Tabs.TabPane tab="生成报表" key="generate">
            <Card bordered={false}>
              <Title level={4}>创建新报表</Title>
              <Paragraph>选择报表类型和时间范围，生成所需的数据报表。</Paragraph>
              
              <Row gutter={16} style={{ marginBottom: 16 }}>
                <Col span={8}>
                  <Radio.Group value={reportType} onChange={handleReportTypeChange}>
                    <Radio.Button value="daily">日报</Radio.Button>
                    <Radio.Button value="weekly">周报</Radio.Button>
                    <Radio.Button value="monthly">月报</Radio.Button>
                    <Radio.Button value="custom">自定义</Radio.Button>
                  </Radio.Group>
                </Col>
                <Col span={8}>
                  <RangePicker onChange={handleDateRangeChange} />
                </Col>
                <Col span={8}>
                  <Space>
                    <Select
                      defaultValue="downloads"
                      style={{ width: 120 }}
                      options={[
                        { value: 'downloads', label: '下载量' },
                        { value: 'sales', label: '销售额' },
                        { value: 'users', label: '用户数' },
                        { value: 'all', label: '全部数据' },
                      ]}
                    />
                    <Button 
                      type="primary" 
                      onClick={handleGenerateReport}
                    >
                      生成报表
                    </Button>
                  </Space>
                </Col>
              </Row>

              <Title level={4} style={{ marginTop: 24 }}>报表预览</Title>
              <Row gutter={16} style={{ marginBottom: 16 }}>
                <Col span={24}>
                  <Radio.Group value={chartType} onChange={handleChartTypeChange}>
                    <Radio.Button value="line"><LineChartOutlined /> 折线图</Radio.Button>
                    <Radio.Button value="column"><BarChartOutlined /> 柱状图</Radio.Button>
                    <Radio.Button value="pie"><PieChartOutlined /> 饼图</Radio.Button>
                  </Radio.Group>
                </Col>
              </Row>
              
              <div style={{ height: 400 }}>
                {chartType === 'line' && <Line {...lineConfig} />}
                {chartType === 'column' && <Column {...columnConfig} />}
                {chartType === 'pie' && <Pie {...pieConfig} />}
              </div>

              <Row gutter={16} style={{ marginTop: 24 }}>
                <Col span={24} style={{ textAlign: 'right' }}>
                  <Space>
                    <Button icon={<FileExcelOutlined />}>导出 Excel</Button>
                    <Button icon={<FilePdfOutlined />}>导出 PDF</Button>
                  </Space>
                </Col>
              </Row>
            </Card>
          </Tabs.TabPane>
          
          <Tabs.TabPane tab="历史报表" key="history">
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Select
                  placeholder="报表类型"
                  style={{ width: 120 }}
                  options={[
                    { value: 'daily', label: '日报' },
                    { value: 'weekly', label: '周报' },
                    { value: 'monthly', label: '月报' },
                    { value: 'custom', label: '自定义' },
                  ]}
                />
                <RangePicker placeholder={['开始日期', '结束日期']} />
                <Button type="primary">搜索</Button>
                <Button>重置</Button>
              </Space>
            </div>
            
            <Table 
              columns={columns} 
              dataSource={reportList?.data} 
              rowKey="id" 
              loading={reportLoading}
              pagination={{
                total: reportList?.total,
                showSizeChanger: true,
                showQuickJumper: true,
              }}
            />
          </Tabs.TabPane>
        </Tabs>
      </Card>
    </PageContainer>
  );
};

export default ReportGeneration;