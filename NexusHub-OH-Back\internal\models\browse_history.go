package models

import (
	"time"

	"gorm.io/gorm"
)

// BrowseHistory 浏览历史模型
type BrowseHistory struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 用户信息
	UserID uint `gorm:"not null;index" json:"user_id"`
	User   User `gorm:"foreignKey:UserID" json:"user,omitempty"`

	// 浏览的应用信息
	AppID uint        `gorm:"not null;index" json:"app_id"`
	App   Application `gorm:"foreignKey:AppID" json:"app,omitempty"`

	// 浏览详情
	BrowseTime time.Time `gorm:"not null;index" json:"browse_time"` // 浏览时间
	Duration   int       `gorm:"default:0" json:"duration"`         // 浏览时长（秒）
	IPAddress  string    `gorm:"type:varchar(45)" json:"ip_address"` // IP地址
	UserAgent  string    `gorm:"type:text" json:"user_agent"`        // 用户代理
	Referrer   string    `gorm:"type:varchar(500)" json:"referrer"`  // 来源页面

	// 浏览行为类型
	ActionType string `gorm:"type:varchar(50);default:'view'" json:"action_type"` // view, download, share等

	// 设备信息
	DeviceType string `gorm:"type:varchar(50)" json:"device_type"` // mobile, desktop, tablet
	Platform   string `gorm:"type:varchar(50)" json:"platform"`    // android, ios, web
}

// TableName 指定表名
func (BrowseHistory) TableName() string {
	return "browse_histories"
}

// BrowseHistoryResponse 浏览历史响应结构体
type BrowseHistoryResponse struct {
	ID         uint      `json:"id"`
	BrowseTime time.Time `json:"browse_time"`
	Duration   int       `json:"duration"`
	ActionType string    `json:"action_type"`
	DeviceType string    `json:"device_type"`
	Platform   string    `json:"platform"`

	// 应用信息
	App struct {
		ID          uint   `json:"id"`
		Name        string `json:"name"`
		PackageName string `json:"package_name"`
		Icon        string `json:"icon"`
		Description string `json:"description"`
	} `json:"app"`
}