import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';

/**
 * 导航项
 */
export interface NavItem {
  label: string;
  icon: Resource | string;
  activeIcon?: Resource | string;
  route: string;
}

/**
 * 导航栏组件
 */
@Component
export struct NavigationBar {
  @Prop navItems: NavItem[];
  @Prop currentIndex: number = 0;
  @Prop showLabels: boolean = true;
  @Prop isBottomNav: boolean = false;
  onItemClick?: (index: number) => void;

  private deviceUtils = DeviceUtils.getInstance();

  /**
   * 底部导航栏
   */
  @Builder
  private BottomNavBar() {
    Row() {
      ForEach(this.navItems, (item: NavItem, index: number) => {
        Column() {
          // 统一使用Image组件显示图标
          Image(this.currentIndex === index ? (item.activeIcon || item.icon) : item.icon)
            .width(24)
            .height(24)
            .objectFit(ImageFit.Contain)
            .fillColor(this.currentIndex === index ? $r('sys.color.ohos_id_color_primary') : $r('sys.color.ohos_id_color_text_secondary'))

          if (this.showLabels) {
            Text(item.label)
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
              .fontColor(this.currentIndex === index ? $r('sys.color.ohos_id_color_primary') : $r('sys.color.ohos_id_color_text_secondary'))
              .margin({ top: 4 })
              .maxLines(1)
              .textOverflow({ overflow: TextOverflow.Ellipsis })
          }
        }
        .width('100%')
        .height('100%')
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)
        .layoutWeight(1)
        .padding({ top: 8, bottom: 8 })
        .onClick(() => {
          this.onItemClick?.(index);
        })
      })
    }
    .width('100%')
    .height(64)
    .padding({ left: 8, right: 8 })
    .backgroundColor($r('sys.color.ohos_id_color_background'))
    .shadow({
      radius: 8,
      color: $r('sys.color.ohos_id_color_foreground_contrary'),
      offsetX: 0,
      offsetY: -2
    })
  }

  /**
   * 侧边导航栏（左侧竖直布局）
   */
  @Builder
  private SideNavBar() {
    Column() {
      // 应用标志
      Column() {
        Image($r('app.media.app_icon'))
          .width(28)
          .height(28)
          .objectFit(ImageFit.Contain)
          .fillColor($r('sys.color.ohos_id_color_primary'))
          .margin({ bottom: 4 })

        Text('NexusHub')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontWeight(FontWeight.Bold)
          .fontColor($r('sys.color.ohos_id_color_text_primary'))
          .textAlign(TextAlign.Center)
          .maxLines(1)
      }
      .width('100%')
      .padding({ top: 16, bottom: 8, left: 8, right: 8 })
      .justifyContent(FlexAlign.Center)
      .alignItems(HorizontalAlign.Center)

      // 导航项
      ForEach(this.navItems, (item: NavItem, index: number) => {
        Column() {
          // 统一使用Image组件显示图标
          Image(this.currentIndex === index ? (item.activeIcon || item.icon) : item.icon)
            .width(20)
            .height(20)
            .objectFit(ImageFit.Contain)
            .fillColor(this.currentIndex === index ? $r('sys.color.ohos_id_color_primary') : $r('sys.color.ohos_id_color_text_secondary'))
            .margin({ bottom: 3 })

          if (this.showLabels) {
            Text(item.label)
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
              .fontColor(this.currentIndex === index ? $r('sys.color.ohos_id_color_primary') : $r('sys.color.ohos_id_color_text_secondary'))
              .textAlign(TextAlign.Center)
              .maxLines(1)
              .textOverflow({ overflow: TextOverflow.Ellipsis })
          }
        }
        .width('100%')
        .padding({ top: 10, bottom: 10, left: 6, right: 6 })
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)
        .backgroundColor(this.currentIndex === index ? $r('sys.color.ohos_id_color_primary') : 'transparent')
        .borderRadius(Constants.BORDER_RADIUS.SMALL)
        .margin({ bottom: 3 })
        .onClick(() => {
          if (this.currentIndex !== index) {
            this.onItemClick?.(index);
          }
        })
      })

      Blank()

      // 版本信息
      Text('v1.0.0')
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
        .fontColor($r('sys.color.ohos_id_color_text_hint'))
        .textAlign(TextAlign.Center)
        .padding({ bottom: 8 })
    }
    .width(this.deviceUtils.getSidebarWidth())
    .height('100%')
    .backgroundColor($r('sys.color.ohos_id_color_background'))
    .shadow({
      radius: 4,
      color: $r('sys.color.ohos_id_color_foreground_contrary'),
      offsetX: 1,
      offsetY: 0
    })
  }

  build() {
    if (this.isBottomNav) {
      this.BottomNavBar()
    } else {
      this.SideNavBar()
    }
  }
}