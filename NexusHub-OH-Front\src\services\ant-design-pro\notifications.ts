import { request } from '@umijs/max';

// 通知项接口
export interface NotificationItem {
  id: number;
  user_id: number;
  title: string;
  description?: string;
  type: 'system' | 'review' | 'developer' | 'security';
  status: 'info' | 'success' | 'warning' | 'error';
  read: boolean;
  extra?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

// 通知设置接口
export interface NotificationSettings {
  id: number;
  user_id: number;
  account_security: boolean;
  system_messages: boolean;
  task_reminders: boolean;
  email_notifications: boolean;
  created_at: string;
  updated_at: string;
}

// 获取通知列表请求参数
export interface GetNotificationsParams {
  page?: number;
  pageSize?: number;
  type?: string;
  read?: boolean;
}

// 获取通知列表响应
export interface GetNotificationsResponse {
  success: boolean;
  data?: {
    list: NotificationItem[];
    total: number;
    page: number;
    pageSize: number;
  };
  message?: string;
}

// 未读数量响应
export interface GetUnreadCountResponse {
  success: boolean;
  data?: {
    count: number;
  };
  message?: string;
}

// 标记已读请求参数
export interface MarkAsReadParams {
  id: number;
}

// 通用响应
export interface CommonResponse {
  success: boolean;
  message?: string;
}

// 获取通知设置响应
export interface GetNotificationSettingsResponse {
  success: boolean;
  data?: NotificationSettings;
  message?: string;
}

// 更新通知设置请求参数
export interface UpdateNotificationSettingsParams {
  account_security?: boolean;
  system_messages?: boolean;
  task_reminders?: boolean;
  email_notifications?: boolean;
}

// 删除通知请求参数
export interface DeleteNotificationParams {
  id: number;
}

/**
 * 获取通知列表
 */
export async function getNotifications(params: GetNotificationsParams = {}): Promise<GetNotificationsResponse> {
  return request('/notifications', {
    method: 'GET',
    params,
  });
}

/**
 * 获取未读通知数量
 */
export async function getUnreadCount(): Promise<GetUnreadCountResponse> {
  return request('/notifications/unread-count', {
    method: 'GET',
  });
}

/**
 * 标记通知为已读
 */
export async function markAsRead(params: MarkAsReadParams): Promise<CommonResponse> {
  return request('/notifications/read', {
    method: 'POST',
    data: params,
  });
}

/**
 * 标记全部通知为已读
 */
export async function markAllAsRead(): Promise<CommonResponse> {
  return request('/notifications/read-all', {
    method: 'POST',
  });
}

/**
 * 获取通知设置
 */
export async function getNotificationSettings(): Promise<GetNotificationSettingsResponse> {
  return request('/notifications/settings', {
    method: 'GET',
  });
}

/**
 * 更新通知设置
 */
export async function updateNotificationSettings(params: UpdateNotificationSettingsParams): Promise<CommonResponse> {
  return request('/notifications/settings', {
    method: 'PUT',
    data: params,
  });
}

/**
 * 删除通知
 */
export async function deleteNotification(params: DeleteNotificationParams): Promise<CommonResponse> {
  return request(`/notifications/${params.id}`, {
    method: 'DELETE',
  });
}