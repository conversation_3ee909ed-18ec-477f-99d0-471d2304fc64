import { PageContainer } from '@ant-design/pro-components';
import { Card, Table, Button, Space, Input, Modal, Form, message, Popconfirm, Tooltip, Tree, Tabs, Switch, Tag, Select } from 'antd';
import { SearchOutlined, PlusOutlined, EditOutlined, DeleteOutlined, ReloadOutlined, LockOutlined, UnlockOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { useRequest } from '@umijs/max';
import type { ColumnsType } from 'antd/es/table';

const { Option } = Select;

interface PermissionItem {
  id: string;
  name: string;
  code: string;
  type: 'menu' | 'operation' | 'data';
  parentId: string | null;
  path: string | null;
  component: string | null;
  icon: string | null;
  sort: number;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

// 模拟数据获取函数
const fetchPermissions = async (params: any) => {
  console.log('Fetching permissions with params:', params);
  
  // 模拟数据
  const mockData: PermissionItem[] = [
    {
      id: 'perm001',
      name: '系统管理',
      code: 'system',
      type: 'menu',
      parentId: null,
      path: '/system',
      component: null,
      icon: 'SettingOutlined',
      sort: 1,
      status: 'active',
      createdAt: '2023-01-01',
      updatedAt: '2023-11-20',
    },
    {
      id: 'perm002',
      name: '用户管理',
      code: 'system:user',
      type: 'menu',
      parentId: 'perm001',
      path: '/system/user',
      component: './System/User',
      icon: 'UserOutlined',
      sort: 1,
      status: 'active',
      createdAt: '2023-01-01',
      updatedAt: '2023-11-20',
    },
    {
      id: 'perm003',
      name: '查看用户',
      code: 'system:user:view',
      type: 'operation',
      parentId: 'perm002',
      path: null,
      component: null,
      icon: null,
      sort: 1,
      status: 'active',
      createdAt: '2023-01-01',
      updatedAt: '2023-11-20',
    },
    {
      id: 'perm004',
      name: '创建用户',
      code: 'system:user:create',
      type: 'operation',
      parentId: 'perm002',
      path: null,
      component: null,
      icon: null,
      sort: 2,
      status: 'active',
      createdAt: '2023-01-01',
      updatedAt: '2023-11-20',
    },
    {
      id: 'perm005',
      name: '编辑用户',
      code: 'system:user:edit',
      type: 'operation',
      parentId: 'perm002',
      path: null,
      component: null,
      icon: null,
      sort: 3,
      status: 'active',
      createdAt: '2023-01-01',
      updatedAt: '2023-11-20',
    },
    {
      id: 'perm006',
      name: '删除用户',
      code: 'system:user:delete',
      type: 'operation',
      parentId: 'perm002',
      path: null,
      component: null,
      icon: null,
      sort: 4,
      status: 'active',
      createdAt: '2023-01-01',
      updatedAt: '2023-11-20',
    },
    {
      id: 'perm007',
      name: '角色管理',
      code: 'system:role',
      type: 'menu',
      parentId: 'perm001',
      path: '/system/role',
      component: './System/Role',
      icon: 'TeamOutlined',
      sort: 2,
      status: 'active',
      createdAt: '2023-01-01',
      updatedAt: '2023-11-20',
    },
    {
      id: 'perm008',
      name: '查看角色',
      code: 'system:role:view',
      type: 'operation',
      parentId: 'perm007',
      path: null,
      component: null,
      icon: null,
      sort: 1,
      status: 'active',
      createdAt: '2023-01-01',
      updatedAt: '2023-11-20',
    },
    {
      id: 'perm009',
      name: '创建角色',
      code: 'system:role:create',
      type: 'operation',
      parentId: 'perm007',
      path: null,
      component: null,
      icon: null,
      sort: 2,
      status: 'active',
      createdAt: '2023-01-01',
      updatedAt: '2023-11-20',
    },
    {
      id: 'perm010',
      name: '权限管理',
      code: 'system:permission',
      type: 'menu',
      parentId: 'perm001',
      path: '/system/permission',
      component: './System/Permission',
      icon: 'LockOutlined',
      sort: 3,
      status: 'active',
      createdAt: '2023-01-01',
      updatedAt: '2023-11-20',
    },
    {
      id: 'perm011',
      name: '应用管理',
      code: 'app',
      type: 'menu',
      parentId: null,
      path: '/app',
      component: null,
      icon: 'AppstoreOutlined',
      sort: 2,
      status: 'active',
      createdAt: '2023-01-02',
      updatedAt: '2023-11-19',
    },
    {
      id: 'perm012',
      name: '应用列表',
      code: 'app:list',
      type: 'menu',
      parentId: 'perm011',
      path: '/app/list',
      component: './App/List',
      icon: 'UnorderedListOutlined',
      sort: 1,
      status: 'active',
      createdAt: '2023-01-02',
      updatedAt: '2023-11-19',
    },
    {
      id: 'perm013',
      name: '应用审核',
      code: 'app:audit',
      type: 'menu',
      parentId: 'perm011',
      path: '/app/audit',
      component: './App/Audit',
      icon: 'CheckSquareOutlined',
      sort: 2,
      status: 'active',
      createdAt: '2023-01-02',
      updatedAt: '2023-11-19',
    },
    {
      id: 'perm014',
      name: '内容管理',
      code: 'content',
      type: 'menu',
      parentId: null,
      path: '/content',
      component: null,
      icon: 'FileTextOutlined',
      sort: 3,
      status: 'active',
      createdAt: '2023-01-03',
      updatedAt: '2023-11-18',
    },
    {
      id: 'perm015',
      name: '推荐内容',
      code: 'content:featured',
      type: 'menu',
      parentId: 'perm014',
      path: '/content/featured',
      component: './Content/Featured',
      icon: 'StarOutlined',
      sort: 1,
      status: 'active',
      createdAt: '2023-01-03',
      updatedAt: '2023-11-18',
    },
  ];

  // 根据类型过滤
  const typeFilteredData = params.type ? mockData.filter(item => item.type === params.type) : mockData;
  
  // 根据状态过滤
  const statusFilteredData = params.status ? typeFilteredData.filter(item => item.status === params.status) : typeFilteredData;
  
  // 根据关键词过滤
  const keywordFilteredData = params.keyword
    ? statusFilteredData.filter(item => 
        item.name.toLowerCase().includes(params.keyword.toLowerCase()) ||
        item.code.toLowerCase().includes(params.keyword.toLowerCase())
      )
    : statusFilteredData;

  return { data: keywordFilteredData, total: keywordFilteredData.length };
};

const PermissionControl: React.FC = () => {
  const [searchParams, setSearchParams] = useState({});
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingPermission, setEditingPermission] = useState<PermissionItem | null>(null);
  const [form] = Form.useForm();

  const { data, loading, refresh } = useRequest(() => fetchPermissions(searchParams), {
    refreshDeps: [searchParams],
  });

  const handleSearch = (values: any) => {
    setSearchParams(values);
  };

  const showAddModal = () => {
    setEditingPermission(null);
    form.resetFields();
    setModalVisible(true);
  };

  const showEditModal = (record: PermissionItem) => {
    setEditingPermission(record);
    form.setFieldsValue({
      name: record.name,
      code: record.code,
      type: record.type,
      parentId: record.parentId,
      path: record.path,
      component: record.component,
      icon: record.icon,
      sort: record.sort,
      status: record.status === 'active',
    });
    setModalVisible(true);
  };

  const handleModalOk = () => {
    form.validateFields().then(values => {
      if (editingPermission) {
        message.success(`权限 "${values.name}" 已更新`);
      } else {
        message.success(`权限 "${values.name}" 已创建`);
      }
      setModalVisible(false);
      refresh();
    });
  };

  const handleDelete = (id: string, name: string) => {
    message.success(`权限 "${name}" 已删除`);
    refresh();
  };

  const handleBatchDelete = () => {
    message.success(`已删除 ${selectedRowKeys.length} 个权限`);
    setSelectedRowKeys([]);
    refresh();
  };

  const handleStatusChange = (id: string, checked: boolean) => {
    message.success(`权限状态已${checked ? '启用' : '禁用'}`);
    refresh();
  };

  // 获取所有可能的父权限选项
  const getParentOptions = () => {
    const menuPermissions = (data?.data || []).filter(item => item.type === 'menu');
    return menuPermissions.map(item => (
      <Option key={item.id} value={item.id}>{item.name}</Option>
    ));
  };

  const columns: ColumnsType<PermissionItem> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
    },
    {
      title: '权限名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: '权限编码',
      dataIndex: 'code',
      key: 'code',
      width: 180,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: string) => {
        const typeMap = {
          menu: { color: 'blue', text: '菜单' },
          operation: { color: 'green', text: '操作' },
          data: { color: 'purple', text: '数据' },
        };
        const { color, text } = typeMap[type as keyof typeof typeMap] || { color: 'default', text: type };
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '路径',
      dataIndex: 'path',
      key: 'path',
      width: 150,
      render: (path: string) => path || '-',
    },
    {
      title: '组件',
      dataIndex: 'component',
      key: 'component',
      width: 150,
      render: (component: string) => component || '-',
    },
    {
      title: '图标',
      dataIndex: 'icon',
      key: 'icon',
      width: 100,
      render: (icon: string) => icon || '-',
    },
    {
      title: '排序',
      dataIndex: 'sort',
      key: 'sort',
      width: 80,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string, record: PermissionItem) => (
        <Switch
          checkedChildren="启用"
          unCheckedChildren="禁用"
          checked={status === 'active'}
          onChange={(checked) => handleStatusChange(record.id, checked)}
        />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 120,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              onClick={() => showEditModal(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除这个权限吗？"
              onConfirm={() => handleDelete(record.id, record.name)}
              okText="确定"
              cancelText="取消"
            >
              <Button 
                type="text" 
                danger 
                icon={<DeleteOutlined />} 
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer
      header={{
        title: '权限控制',
        subTitle: '管理系统权限',
      }}
    >
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Form layout="inline" onFinish={handleSearch}>
            <Form.Item name="keyword" label="关键词">
              <Input 
                placeholder="搜索权限名称/编码" 
                prefix={<SearchOutlined />}
                style={{ width: 200 }}
              />
            </Form.Item>
            <Form.Item name="type" label="类型">
              <Select 
                placeholder="选择类型" 
                style={{ width: 120 }}
                allowClear
              >
                <Option value="menu">菜单</Option>
                <Option value="operation">操作</Option>
                <Option value="data">数据</Option>
              </Select>
            </Form.Item>
            <Form.Item name="status" label="状态">
              <Select 
                placeholder="选择状态" 
                style={{ width: 120 }}
                allowClear
              >
                <Option value="active">启用</Option>
                <Option value="inactive">禁用</Option>
              </Select>
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                搜索
              </Button>
            </Form.Item>
            <Form.Item>
              <Button onClick={() => setSearchParams({})}>
                重置
              </Button>
            </Form.Item>
          </Form>
        </div>

        <div style={{ marginBottom: 16 }}>
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={showAddModal}
            style={{ marginRight: 8 }}
          >
            新建权限
          </Button>
          <Popconfirm
            title="确定要删除选中的权限吗？"
            onConfirm={handleBatchDelete}
            okText="确定"
            cancelText="取消"
            disabled={selectedRowKeys.length === 0}
          >
            <Button 
              danger 
              disabled={selectedRowKeys.length === 0}
              style={{ marginRight: 8 }}
            >
              批量删除
            </Button>
          </Popconfirm>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={refresh}
          >
            刷新
          </Button>
          <span style={{ marginLeft: 8 }}>
            {selectedRowKeys.length > 0 ? `已选择 ${selectedRowKeys.length} 项` : ''}
          </span>
        </div>

        <Table
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
          columns={columns}
          dataSource={data?.data}
          rowKey="id"
          loading={loading}
          pagination={{
            total: data?.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
          }}
        />
      </Card>

      {/* 权限编辑模态框 */}
      <Modal
        title={editingPermission ? '编辑权限' : '新建权限'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
        destroyOnHidden
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="权限名称"
            rules={[{ required: true, message: '请输入权限名称' }]}
          >
            <Input placeholder="请输入权限名称" />
          </Form.Item>
          <Form.Item
            name="code"
            label="权限编码"
            rules={[{ required: true, message: '请输入权限编码' }]}
          >
            <Input placeholder="请输入权限编码，如system:user:view" />
          </Form.Item>
          <Form.Item
            name="type"
            label="权限类型"
            rules={[{ required: true, message: '请选择权限类型' }]}
          >
            <Select placeholder="请选择权限类型">
              <Option value="menu">菜单</Option>
              <Option value="operation">操作</Option>
              <Option value="data">数据</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="parentId"
            label="父级权限"
          >
            <Select placeholder="请选择父级权限" allowClear>
              {getParentOptions()}
            </Select>
          </Form.Item>
          <Form.Item
            name="path"
            label="路径"
            tooltip="仅菜单类型需要填写"
          >
            <Input placeholder="请输入路径，如/system/user" />
          </Form.Item>
          <Form.Item
            name="component"
            label="组件"
            tooltip="仅菜单类型需要填写"
          >
            <Input placeholder="请输入组件路径，如./System/User" />
          </Form.Item>
          <Form.Item
            name="icon"
            label="图标"
            tooltip="仅菜单类型需要填写"
          >
            <Input placeholder="请输入图标名称，如UserOutlined" />
          </Form.Item>
          <Form.Item
            name="sort"
            label="排序"
            rules={[{ required: true, message: '请输入排序' }]}
          >
            <Input type="number" placeholder="请输入排序，数字越小越靠前" />
          </Form.Item>
          <Form.Item
            name="status"
            label="状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" defaultChecked />
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default PermissionControl;