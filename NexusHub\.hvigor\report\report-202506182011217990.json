{"version": "2.0", "ppid": 32784, "events": [{"head": {"id": "d2da6fa9-b72c-49a3-8629-5907199acb35", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073293279600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22bbf8a1-9ac8-4f18-9ff6-e197e38b4aa3", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073293582200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7ce11b5-c471-4d9d-819f-406a38695a34", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073329122400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b93c883-9839-451d-85cb-ca8207c6f455", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073329493300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fe3436f-0890-4718-8c0d-4be95f0b4994", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073331639700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72a372d5-34ba-4099-85d6-cbf0eabf5d47", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 223073332058300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed36e519-0eac-4ec2-b325-1cae80994d02", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224051951284500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3e27ca4-8d0d-4576-bf71-87f5ee7f9527", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224051963845600, "endTime": 224052273382000}, "additional": {"children": ["fb7096da-0a30-4e94-a1c3-ec4215caa2e4", "9414feff-fbf0-4dff-bafa-630d29483a43", "0cae33e3-abf4-4564-832f-c0283486b18d", "78698d08-262f-4426-bbfc-26a899b220c9", "65eea487-d3a4-49b7-b20b-c12178d26b58", "c4a69b97-9911-486f-bf0f-60985a3565d6", "6827b5a0-c8cb-4130-a4f2-6c656ec5d837"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "d7b6305e-41d4-4bea-b8d4-3981a6327061"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb7096da-0a30-4e94-a1c3-ec4215caa2e4", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224051963850900, "endTime": 224051984778300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3e27ca4-8d0d-4576-bf71-87f5ee7f9527", "logId": "7d06e0e1-7255-4d14-86a3-dd7e89dad9b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9414feff-fbf0-4dff-bafa-630d29483a43", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224051984806700, "endTime": 224052270876200}, "additional": {"children": ["c23189b7-c054-4d4a-8256-aeebb22ae702", "7ab00b86-2ef0-4c74-93dc-f7a3cc82123f", "d2fa3707-8f45-4ff9-b58e-2ce0a382877a", "583a396d-69a1-4248-8eca-0451c8270a04", "db2ec3db-e9ce-4c70-a505-f6785ed94889", "568e9e28-385a-42ac-b95e-76cace7fa9df", "574cb62e-d620-466a-9623-9dd950ec196f", "f3672abd-0679-4a05-bca9-b15897abbd40", "7177ed61-fc04-4961-9177-70680481e605"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3e27ca4-8d0d-4576-bf71-87f5ee7f9527", "logId": "064c65a2-1303-40fa-8b34-752ed8bbbf9c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0cae33e3-abf4-4564-832f-c0283486b18d", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052270933900, "endTime": 224052273341300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3e27ca4-8d0d-4576-bf71-87f5ee7f9527", "logId": "26cf488d-b706-4572-8704-d2cd1e62129f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "78698d08-262f-4426-bbfc-26a899b220c9", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052273356600, "endTime": 224052273373200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3e27ca4-8d0d-4576-bf71-87f5ee7f9527", "logId": "043c2431-7659-4369-8f88-46e2e14d6e20"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65eea487-d3a4-49b7-b20b-c12178d26b58", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224051971806200, "endTime": 224051972467000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3e27ca4-8d0d-4576-bf71-87f5ee7f9527", "logId": "a972ae45-392c-4a79-93a7-6cce48494779"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a972ae45-392c-4a79-93a7-6cce48494779", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224051971806200, "endTime": 224051972467000}, "additional": {"logType": "info", "children": [], "durationId": "65eea487-d3a4-49b7-b20b-c12178d26b58", "parent": "d7b6305e-41d4-4bea-b8d4-3981a6327061"}}, {"head": {"id": "c4a69b97-9911-486f-bf0f-60985a3565d6", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224051977178900, "endTime": 224051977200300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3e27ca4-8d0d-4576-bf71-87f5ee7f9527", "logId": "11dbf835-133f-4fbc-8abb-fe84fedd4954"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11dbf835-133f-4fbc-8abb-fe84fedd4954", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224051977178900, "endTime": 224051977200300}, "additional": {"logType": "info", "children": [], "durationId": "c4a69b97-9911-486f-bf0f-60985a3565d6", "parent": "d7b6305e-41d4-4bea-b8d4-3981a6327061"}}, {"head": {"id": "2caa2f75-db82-456e-bb1b-f02d55583b26", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224051977277500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "488bb51f-33ca-4dcd-a6ab-9b9f88fb20e5", "name": "Cache service initialization finished in 8 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224051984594500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d06e0e1-7255-4d14-86a3-dd7e89dad9b3", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224051963850900, "endTime": 224051984778300}, "additional": {"logType": "info", "children": [], "durationId": "fb7096da-0a30-4e94-a1c3-ec4215caa2e4", "parent": "d7b6305e-41d4-4bea-b8d4-3981a6327061"}}, {"head": {"id": "c23189b7-c054-4d4a-8256-aeebb22ae702", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224051993943900, "endTime": 224051993959600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9414feff-fbf0-4dff-bafa-630d29483a43", "logId": "adae896e-9763-4dc7-adf6-e701dd34e30c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ab00b86-2ef0-4c74-93dc-f7a3cc82123f", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224051993992900, "endTime": 224052001965600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9414feff-fbf0-4dff-bafa-630d29483a43", "logId": "cc8e6ccd-725a-42df-9d07-8d3f6d90b89c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2fa3707-8f45-4ff9-b58e-2ce0a382877a", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052001980200, "endTime": 224052108505200}, "additional": {"children": ["68a4deef-278d-479c-be16-21ad4dc46e2b", "2f7ddb9b-2eea-437f-9723-80ca188f4e18", "cf7d4704-d049-40a6-be3f-7c880324ee68"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9414feff-fbf0-4dff-bafa-630d29483a43", "logId": "a27dfe63-5351-4681-a922-348cd84bdc42"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "583a396d-69a1-4248-8eca-0451c8270a04", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052108565400, "endTime": 224052143887800}, "additional": {"children": ["8b48a694-0e8c-43b8-937d-05bc483fc37b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9414feff-fbf0-4dff-bafa-630d29483a43", "logId": "444aeef0-2a13-45db-bc2f-9fd432764ab8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db2ec3db-e9ce-4c70-a505-f6785ed94889", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052143906900, "endTime": 224052225421700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9414feff-fbf0-4dff-bafa-630d29483a43", "logId": "0c6fca0c-de66-41a0-91b7-7116413659cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "568e9e28-385a-42ac-b95e-76cace7fa9df", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052226911700, "endTime": 224052243317000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9414feff-fbf0-4dff-bafa-630d29483a43", "logId": "66348fb8-a61a-442f-b7c3-9518b2cf1e5b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "574cb62e-d620-466a-9623-9dd950ec196f", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052243346400, "endTime": 224052270592100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9414feff-fbf0-4dff-bafa-630d29483a43", "logId": "16204be8-9bd6-45ee-b87f-049b6b1a1b91"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f3672abd-0679-4a05-bca9-b15897abbd40", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052270633100, "endTime": 224052270850700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9414feff-fbf0-4dff-bafa-630d29483a43", "logId": "480466f9-a997-4080-ba76-5ef1f93ffc75"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "adae896e-9763-4dc7-adf6-e701dd34e30c", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224051993943900, "endTime": 224051993959600}, "additional": {"logType": "info", "children": [], "durationId": "c23189b7-c054-4d4a-8256-aeebb22ae702", "parent": "064c65a2-1303-40fa-8b34-752ed8bbbf9c"}}, {"head": {"id": "cc8e6ccd-725a-42df-9d07-8d3f6d90b89c", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224051993992900, "endTime": 224052001965600}, "additional": {"logType": "info", "children": [], "durationId": "7ab00b86-2ef0-4c74-93dc-f7a3cc82123f", "parent": "064c65a2-1303-40fa-8b34-752ed8bbbf9c"}}, {"head": {"id": "68a4deef-278d-479c-be16-21ad4dc46e2b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052002510600, "endTime": 224052002665300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d2fa3707-8f45-4ff9-b58e-2ce0a382877a", "logId": "a6fb9d33-599c-4389-be61-dc2b66c200f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6fb9d33-599c-4389-be61-dc2b66c200f5", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052002510600, "endTime": 224052002665300}, "additional": {"logType": "info", "children": [], "durationId": "68a4deef-278d-479c-be16-21ad4dc46e2b", "parent": "a27dfe63-5351-4681-a922-348cd84bdc42"}}, {"head": {"id": "2f7ddb9b-2eea-437f-9723-80ca188f4e18", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052004175200, "endTime": 224052106348200}, "additional": {"children": ["7b329661-621d-4173-bb7b-754a7a4d8631", "f768c790-07df-4177-acda-fe555046c4af"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d2fa3707-8f45-4ff9-b58e-2ce0a382877a", "logId": "cd9b608c-6957-45fb-b6ae-e63c76f286f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b329661-621d-4173-bb7b-754a7a4d8631", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052004176300, "endTime": 224052012045300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2f7ddb9b-2eea-437f-9723-80ca188f4e18", "logId": "75999b42-b728-472e-b7e1-f5a32a3181eb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f768c790-07df-4177-acda-fe555046c4af", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052012069800, "endTime": 224052106329100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2f7ddb9b-2eea-437f-9723-80ca188f4e18", "logId": "0b4b2835-ddb1-4bfd-b26c-79514d462872"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dbf354da-9e53-4c60-8022-3b005b3bdcaf", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052004180800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e48e7071-8b7f-4bd8-bb18-12d10357ece8", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052011868600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75999b42-b728-472e-b7e1-f5a32a3181eb", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052004176300, "endTime": 224052012045300}, "additional": {"logType": "info", "children": [], "durationId": "7b329661-621d-4173-bb7b-754a7a4d8631", "parent": "cd9b608c-6957-45fb-b6ae-e63c76f286f9"}}, {"head": {"id": "46011dea-5ed1-41f8-90a8-dde9dbe142a7", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052012089300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc7cb702-55ba-4832-99cc-a11035414a61", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052021662000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76d69906-37e3-44b4-bf66-66bd1b2748c3", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052021868500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12c78a39-476b-4cd9-8595-1e424445745c", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052022176700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccecc451-574f-4f0b-99e5-ce73b2c6d90d", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052022330300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "961ff6e8-7a24-4bfb-b0b4-0b598f2b6513", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052024280200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd4ed06c-05c3-44d8-beaa-12377f85200e", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052041101700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7969963d-515b-4168-8ca2-0edb3c57a46a", "name": "Sdk init in 46 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052075110100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbb4815b-0be4-4c66-b7a8-c97cc49b1dcb", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052075564500}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 20, "minute": 11, "second": 21}, "markType": "other"}}, {"head": {"id": "514bb184-7a3e-48d1-8a17-c9bfa1a85d6f", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052075600600}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 20, "minute": 11, "second": 21}, "markType": "other"}}, {"head": {"id": "3090cf53-25d1-49fc-aa34-5fe85c07447e", "name": "Project task initialization takes 28 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052105852900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1091eed3-4267-4716-b6e7-9aabb6fe5e56", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052106030500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "534e8e4e-0bc8-4256-a545-6ac856c43963", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052106133500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "604613e0-3cbd-46a4-81ee-4924b06687ae", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052106248400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b4b2835-ddb1-4bfd-b26c-79514d462872", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052012069800, "endTime": 224052106329100}, "additional": {"logType": "info", "children": [], "durationId": "f768c790-07df-4177-acda-fe555046c4af", "parent": "cd9b608c-6957-45fb-b6ae-e63c76f286f9"}}, {"head": {"id": "cd9b608c-6957-45fb-b6ae-e63c76f286f9", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052004175200, "endTime": 224052106348200}, "additional": {"logType": "info", "children": ["75999b42-b728-472e-b7e1-f5a32a3181eb", "0b4b2835-ddb1-4bfd-b26c-79514d462872"], "durationId": "2f7ddb9b-2eea-437f-9723-80ca188f4e18", "parent": "a27dfe63-5351-4681-a922-348cd84bdc42"}}, {"head": {"id": "cf7d4704-d049-40a6-be3f-7c880324ee68", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052108434100, "endTime": 224052108469200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d2fa3707-8f45-4ff9-b58e-2ce0a382877a", "logId": "0df3f3d2-0206-42e7-b3ad-e4c973b2724d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0df3f3d2-0206-42e7-b3ad-e4c973b2724d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052108434100, "endTime": 224052108469200}, "additional": {"logType": "info", "children": [], "durationId": "cf7d4704-d049-40a6-be3f-7c880324ee68", "parent": "a27dfe63-5351-4681-a922-348cd84bdc42"}}, {"head": {"id": "a27dfe63-5351-4681-a922-348cd84bdc42", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052001980200, "endTime": 224052108505200}, "additional": {"logType": "info", "children": ["a6fb9d33-599c-4389-be61-dc2b66c200f5", "cd9b608c-6957-45fb-b6ae-e63c76f286f9", "0df3f3d2-0206-42e7-b3ad-e4c973b2724d"], "durationId": "d2fa3707-8f45-4ff9-b58e-2ce0a382877a", "parent": "064c65a2-1303-40fa-8b34-752ed8bbbf9c"}}, {"head": {"id": "8b48a694-0e8c-43b8-937d-05bc483fc37b", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052110324500, "endTime": 224052143875000}, "additional": {"children": ["3ff7c300-b226-47c7-817f-e545ec34b739", "bf87ff24-c9d6-4dde-8b13-b3faed4b189f", "953eb697-4908-4b8c-a948-ac73fb374ed9"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "583a396d-69a1-4248-8eca-0451c8270a04", "logId": "dabc5b9a-9e13-4d30-9e21-0778dd94b8bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ff7c300-b226-47c7-817f-e545ec34b739", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052115024300, "endTime": 224052115069800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8b48a694-0e8c-43b8-937d-05bc483fc37b", "logId": "6386ec4e-906b-41d2-a71a-97e19cebf2c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6386ec4e-906b-41d2-a71a-97e19cebf2c0", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052115024300, "endTime": 224052115069800}, "additional": {"logType": "info", "children": [], "durationId": "3ff7c300-b226-47c7-817f-e545ec34b739", "parent": "dabc5b9a-9e13-4d30-9e21-0778dd94b8bd"}}, {"head": {"id": "bf87ff24-c9d6-4dde-8b13-b3faed4b189f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052120854900, "endTime": 224052142493400}, "additional": {"children": ["224f0a32-6708-4066-810f-625a18f19882", "45b750c3-4f90-4462-a675-9e5ff874ef68"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8b48a694-0e8c-43b8-937d-05bc483fc37b", "logId": "cd409cbe-b583-4580-8eac-8bced8a033a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "224f0a32-6708-4066-810f-625a18f19882", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052120857000, "endTime": 224052125830300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bf87ff24-c9d6-4dde-8b13-b3faed4b189f", "logId": "8dc3a4c5-46b3-4ca7-86c5-f50b09f8648e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "45b750c3-4f90-4462-a675-9e5ff874ef68", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052125874200, "endTime": 224052142481700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bf87ff24-c9d6-4dde-8b13-b3faed4b189f", "logId": "1615b910-15bd-42d1-95bf-8aa7119cc7ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7be366b-a0aa-424b-82a2-11c4bd0a56b4", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052120865800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6393c9cf-9845-4171-9491-d9bbd4a15583", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052125600400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dc3a4c5-46b3-4ca7-86c5-f50b09f8648e", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052120857000, "endTime": 224052125830300}, "additional": {"logType": "info", "children": [], "durationId": "224f0a32-6708-4066-810f-625a18f19882", "parent": "cd409cbe-b583-4580-8eac-8bced8a033a4"}}, {"head": {"id": "d3c67487-4192-4f8b-83ca-752befbc743c", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052125903200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0f035ae-f0cd-4676-8819-fe424a4693fa", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052134006800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ae162b8-8f23-41cb-b16b-2699a9c6229f", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052134157800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1275a8df-12d9-4994-b7d5-6f51ffa57e52", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052134345100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9000912c-ec9f-426f-a20d-99700c134210", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052134446300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4145f043-96e6-4163-8a05-d4ef066a6bbd", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052134488000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e766863c-f4e9-4181-a05f-9c7a0488105e", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052134525900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21769afa-bb65-4c3c-9b17-d241fb7292d7", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052134569600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "889bdb2a-1341-4cd2-9ab3-2541ebfb7cb7", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052134608200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79deb65f-e4aa-4ced-bcfe-44ecde4cbc2e", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052134768000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a1d41a6-6e99-42f6-a08f-a4f3641d7bef", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052134842900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8ad12a9-a805-47ab-bd1a-1bb7ca3e5c2c", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052134881600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8c7626b-cc7c-47a1-bb37-747b68c324ba", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052134909500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed6f69bb-666d-41e3-9d8b-a6cd4cdb25c4", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052134979000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e10df703-2842-4aa7-8d62-005bde8004c7", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052135015300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "825f0661-a7c8-4cce-a190-8d31568f17ee", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052135096800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82b98dfd-3c6b-43f0-a933-fcfb14d30142", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052135158600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77cc8391-c552-4792-a4e3-474f500a2765", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052135187300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43d07873-9ab3-490b-9374-189db8a6ddf3", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052136932700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c36374f-c9cb-47fb-bfa9-4ec589de5b12", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052137120800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77dc45d6-09db-4276-8167-74d59c0a9c0e", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052142155500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2fbfa60-e490-4d69-8a28-6aa6c80a48d9", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052142352600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19a0e97e-a9ed-41d0-8f64-75cf2b34f93d", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052142407900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e15b526c-f984-4ba1-a92d-a678d13997f9", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052142441100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1615b910-15bd-42d1-95bf-8aa7119cc7ec", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052125874200, "endTime": 224052142481700}, "additional": {"logType": "info", "children": [], "durationId": "45b750c3-4f90-4462-a675-9e5ff874ef68", "parent": "cd409cbe-b583-4580-8eac-8bced8a033a4"}}, {"head": {"id": "cd409cbe-b583-4580-8eac-8bced8a033a4", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052120854900, "endTime": 224052142493400}, "additional": {"logType": "info", "children": ["8dc3a4c5-46b3-4ca7-86c5-f50b09f8648e", "1615b910-15bd-42d1-95bf-8aa7119cc7ec"], "durationId": "bf87ff24-c9d6-4dde-8b13-b3faed4b189f", "parent": "dabc5b9a-9e13-4d30-9e21-0778dd94b8bd"}}, {"head": {"id": "953eb697-4908-4b8c-a948-ac73fb374ed9", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052143843800, "endTime": 224052143857700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8b48a694-0e8c-43b8-937d-05bc483fc37b", "logId": "4a598130-2ba1-4fba-960b-b8eaf69f3729"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a598130-2ba1-4fba-960b-b8eaf69f3729", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052143843800, "endTime": 224052143857700}, "additional": {"logType": "info", "children": [], "durationId": "953eb697-4908-4b8c-a948-ac73fb374ed9", "parent": "dabc5b9a-9e13-4d30-9e21-0778dd94b8bd"}}, {"head": {"id": "dabc5b9a-9e13-4d30-9e21-0778dd94b8bd", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052110324500, "endTime": 224052143875000}, "additional": {"logType": "info", "children": ["6386ec4e-906b-41d2-a71a-97e19cebf2c0", "cd409cbe-b583-4580-8eac-8bced8a033a4", "4a598130-2ba1-4fba-960b-b8eaf69f3729"], "durationId": "8b48a694-0e8c-43b8-937d-05bc483fc37b", "parent": "444aeef0-2a13-45db-bc2f-9fd432764ab8"}}, {"head": {"id": "444aeef0-2a13-45db-bc2f-9fd432764ab8", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052108565400, "endTime": 224052143887800}, "additional": {"logType": "info", "children": ["dabc5b9a-9e13-4d30-9e21-0778dd94b8bd"], "durationId": "583a396d-69a1-4248-8eca-0451c8270a04", "parent": "064c65a2-1303-40fa-8b34-752ed8bbbf9c"}}, {"head": {"id": "30c57a2c-d717-4c2c-bc62-c8c04cbd8db4", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052170466100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a530b370-bbac-468c-90d7-c1ee8225a5c2", "name": "hvigorfile, resolve hvigorfile dependencies in 82 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052225243500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c6fca0c-de66-41a0-91b7-7116413659cc", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052143906900, "endTime": 224052225421700}, "additional": {"logType": "info", "children": [], "durationId": "db2ec3db-e9ce-4c70-a505-f6785ed94889", "parent": "064c65a2-1303-40fa-8b34-752ed8bbbf9c"}}, {"head": {"id": "7177ed61-fc04-4961-9177-70680481e605", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052226552400, "endTime": 224052226886300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9414feff-fbf0-4dff-bafa-630d29483a43", "logId": "6f9bd54b-0476-4cb2-82ea-1762b98ba483"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e9b096c-045f-4053-a8a7-f2b776641997", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052226606600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f9bd54b-0476-4cb2-82ea-1762b98ba483", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052226552400, "endTime": 224052226886300}, "additional": {"logType": "info", "children": [], "durationId": "7177ed61-fc04-4961-9177-70680481e605", "parent": "064c65a2-1303-40fa-8b34-752ed8bbbf9c"}}, {"head": {"id": "8f5e6b09-7743-425e-83b1-1ee8c66bed1b", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052229144400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be9918f3-2664-490e-be2a-cc16ddc66497", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052240897100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66348fb8-a61a-442f-b7c3-9518b2cf1e5b", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052226911700, "endTime": 224052243317000}, "additional": {"logType": "info", "children": [], "durationId": "568e9e28-385a-42ac-b95e-76cace7fa9df", "parent": "064c65a2-1303-40fa-8b34-752ed8bbbf9c"}}, {"head": {"id": "1565a055-ee05-40b9-b852-76c4dfca935f", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052243371400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dde388eb-09b6-4310-9cbe-933ef240a351", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052258945500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dee1ea78-2c57-4086-ad1d-2dcbd3d84736", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052259110500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "266c874b-e3e0-468d-b0af-5d914fd68ddb", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052259326100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c85460d8-d718-401c-b206-67051f9c01a6", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052265610800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f15470d7-6909-4be1-81e7-349c55d0795a", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052265767100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16204be8-9bd6-45ee-b87f-049b6b1a1b91", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052243346400, "endTime": 224052270592100}, "additional": {"logType": "info", "children": [], "durationId": "574cb62e-d620-466a-9623-9dd950ec196f", "parent": "064c65a2-1303-40fa-8b34-752ed8bbbf9c"}}, {"head": {"id": "5b7b7403-cb66-42c3-ba31-3ea381e6e61d", "name": "Configuration phase cost:277 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052270689600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "480466f9-a997-4080-ba76-5ef1f93ffc75", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052270633100, "endTime": 224052270850700}, "additional": {"logType": "info", "children": [], "durationId": "f3672abd-0679-4a05-bca9-b15897abbd40", "parent": "064c65a2-1303-40fa-8b34-752ed8bbbf9c"}}, {"head": {"id": "064c65a2-1303-40fa-8b34-752ed8bbbf9c", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224051984806700, "endTime": 224052270876200}, "additional": {"logType": "info", "children": ["adae896e-9763-4dc7-adf6-e701dd34e30c", "cc8e6ccd-725a-42df-9d07-8d3f6d90b89c", "a27dfe63-5351-4681-a922-348cd84bdc42", "444aeef0-2a13-45db-bc2f-9fd432764ab8", "0c6fca0c-de66-41a0-91b7-7116413659cc", "66348fb8-a61a-442f-b7c3-9518b2cf1e5b", "16204be8-9bd6-45ee-b87f-049b6b1a1b91", "480466f9-a997-4080-ba76-5ef1f93ffc75", "6f9bd54b-0476-4cb2-82ea-1762b98ba483"], "durationId": "9414feff-fbf0-4dff-bafa-630d29483a43", "parent": "d7b6305e-41d4-4bea-b8d4-3981a6327061"}}, {"head": {"id": "6827b5a0-c8cb-4130-a4f2-6c656ec5d837", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052273277200, "endTime": 224052273310000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3e27ca4-8d0d-4576-bf71-87f5ee7f9527", "logId": "0ccaaea0-1e40-44de-be26-89690980b3f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ccaaea0-1e40-44de-be26-89690980b3f2", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052273277200, "endTime": 224052273310000}, "additional": {"logType": "info", "children": [], "durationId": "6827b5a0-c8cb-4130-a4f2-6c656ec5d837", "parent": "d7b6305e-41d4-4bea-b8d4-3981a6327061"}}, {"head": {"id": "26cf488d-b706-4572-8704-d2cd1e62129f", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052270933900, "endTime": 224052273341300}, "additional": {"logType": "info", "children": [], "durationId": "0cae33e3-abf4-4564-832f-c0283486b18d", "parent": "d7b6305e-41d4-4bea-b8d4-3981a6327061"}}, {"head": {"id": "043c2431-7659-4369-8f88-46e2e14d6e20", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052273356600, "endTime": 224052273373200}, "additional": {"logType": "info", "children": [], "durationId": "78698d08-262f-4426-bbfc-26a899b220c9", "parent": "d7b6305e-41d4-4bea-b8d4-3981a6327061"}}, {"head": {"id": "d7b6305e-41d4-4bea-b8d4-3981a6327061", "name": "init", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224051963845600, "endTime": 224052273382000}, "additional": {"logType": "info", "children": ["7d06e0e1-7255-4d14-86a3-dd7e89dad9b3", "064c65a2-1303-40fa-8b34-752ed8bbbf9c", "26cf488d-b706-4572-8704-d2cd1e62129f", "043c2431-7659-4369-8f88-46e2e14d6e20", "a972ae45-392c-4a79-93a7-6cce48494779", "11dbf835-133f-4fbc-8abb-fe84fedd4954", "0ccaaea0-1e40-44de-be26-89690980b3f2"], "durationId": "a3e27ca4-8d0d-4576-bf71-87f5ee7f9527"}}, {"head": {"id": "b04620bd-b0c4-42cf-bceb-c3aa6b772d60", "name": "Configuration task cost before running: 316 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052273671200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b092087-431f-4d73-a59d-800dfd674092", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052294907800, "endTime": 224052315615100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "38f707c2-6f09-4844-8b79-f20c455ab8b3", "logId": "316ca289-5dee-4d8b-b345-dabd3e4098a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "38f707c2-6f09-4844-8b79-f20c455ab8b3", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052276199000}, "additional": {"logType": "detail", "children": [], "durationId": "2b092087-431f-4d73-a59d-800dfd674092"}}, {"head": {"id": "b9ed23b4-0982-44c0-8923-439d0d36993f", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052277669700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03f3f629-2a3e-45eb-aa7e-9894b23964eb", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052277926000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c17e95b4-306b-4b94-b80b-e207fa808501", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052280216400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2bec2c4-3c91-4539-8fc4-3df709b4c9cd", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052282671300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "367cab1f-a540-4612-829f-3a3a0642d10d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052284954900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47805f34-ed3e-4694-9363-13b050b44e59", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052285181700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40569097-9a25-490c-ad86-bb0d065d3a92", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052294934900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13c5d71f-62fa-4e38-8940-f3531b9324c9", "name": "Incremental task entry:default@PreBuild pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052315004100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ec06b60-9c2c-40f8-926e-532204e856d5", "name": "entry : default@PreBuild cost memory 0.3169403076171875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052315355400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "316ca289-5dee-4d8b-b345-dabd3e4098a5", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052294907800, "endTime": 224052315615100}, "additional": {"logType": "info", "children": [], "durationId": "2b092087-431f-4d73-a59d-800dfd674092"}}, {"head": {"id": "6ab96372-2d9e-429b-81f6-f0f51a2c2995", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052330827500, "endTime": 224052334931000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1150105c-1550-4242-a6bf-2a24aebe9fca", "logId": "d42ef996-9fc9-49a9-9922-41f9128d5b8c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1150105c-1550-4242-a6bf-2a24aebe9fca", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052327667900}, "additional": {"logType": "detail", "children": [], "durationId": "6ab96372-2d9e-429b-81f6-f0f51a2c2995"}}, {"head": {"id": "623d6d71-6a56-4d2a-a9a1-48ed0532081a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052329491500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afba185a-ff48-4896-a13c-b850b883ff1f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052329684500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9459d00e-5336-4c5f-9b9f-41655087a5f3", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052330847500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20c4be98-73f1-451d-87e7-ea2b1a8b22a3", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052332038100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fe4994e-803e-4fae-8539-1ead178b7273", "name": "entry : default@CreateModuleInfo cost memory 0.06116485595703125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052334384900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d908a4f-6027-4827-9de4-b47ddcc7ed2c", "name": "runTaskFromQueue task cost before running: 377 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052334760900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d42ef996-9fc9-49a9-9922-41f9128d5b8c", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052330827500, "endTime": 224052334931000, "totalTime": 3879800}, "additional": {"logType": "info", "children": [], "durationId": "6ab96372-2d9e-429b-81f6-f0f51a2c2995"}}, {"head": {"id": "a614a807-ba3c-4ba3-a256-e9c8d797a91c", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052352643200, "endTime": 224052358134400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d1484621-7b51-4c4c-a190-749ec7482887", "logId": "64c34f8e-d7af-413b-b174-5f41fde29dd2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d1484621-7b51-4c4c-a190-749ec7482887", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052339984800}, "additional": {"logType": "detail", "children": [], "durationId": "a614a807-ba3c-4ba3-a256-e9c8d797a91c"}}, {"head": {"id": "3fde133e-e873-4962-a419-756f82b21410", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052342098700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5214a539-8f2e-493d-a1cf-5a92fce2203d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052342305300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60151906-5c47-422a-8136-36524188f998", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052352680900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c606b95-f504-4374-a99b-f0979471e176", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 3 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052355345400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0c48692-9cbd-4b2b-ac97-436a001c4f76", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052357577000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e8fc0c9-0443-472e-8350-72eacb694921", "name": "entry : default@GenerateMetadata cost memory 0.1021270751953125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052357912300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64c34f8e-d7af-413b-b174-5f41fde29dd2", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052352643200, "endTime": 224052358134400}, "additional": {"logType": "info", "children": [], "durationId": "a614a807-ba3c-4ba3-a256-e9c8d797a91c"}}, {"head": {"id": "4fd32152-d1b3-4ea7-b807-07d6ecd1d673", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052366158400, "endTime": 224052367063600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "811f60cc-1eaa-495e-90a0-aad9d6603e57", "logId": "7a0d014a-63f2-4fba-867b-395593e7652d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "811f60cc-1eaa-495e-90a0-aad9d6603e57", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052362158100}, "additional": {"logType": "detail", "children": [], "durationId": "4fd32152-d1b3-4ea7-b807-07d6ecd1d673"}}, {"head": {"id": "943936d0-efa2-461a-8089-601c7f9d9617", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052365612400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca019a00-4627-4c2c-aaa7-ca7f520c05bc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052365835700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f47dd4f-ab81-4b4a-8736-e7cdf0994cbd", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052366182000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad1b4496-8d11-473f-bfe6-bb8721ba9e9c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052366394200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "441627bb-6647-47b0-a8ac-61e058136991", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052366532800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d4f9d0a-0306-423b-8084-6c0fc8b90f36", "name": "entry : default@ConfigureCmake cost memory 0.0373382568359375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052366699500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82fdd672-ee1f-4854-9e54-49dbd88bf3dd", "name": "runTaskFromQueue task cost before running: 409 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052366908000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a0d014a-63f2-4fba-867b-395593e7652d", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052366158400, "endTime": 224052367063600, "totalTime": 716700}, "additional": {"logType": "info", "children": [], "durationId": "4fd32152-d1b3-4ea7-b807-07d6ecd1d673"}}, {"head": {"id": "97271739-46e1-4cd2-a79b-f02a0e4c20fd", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052376656400, "endTime": 224052382828100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "5ed5436a-7e98-4dbb-97ff-ef5fbc140350", "logId": "3822e1ff-4076-427b-8e2d-53c435472d49"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ed5436a-7e98-4dbb-97ff-ef5fbc140350", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052371244600}, "additional": {"logType": "detail", "children": [], "durationId": "97271739-46e1-4cd2-a79b-f02a0e4c20fd"}}, {"head": {"id": "28366743-2ed4-44c9-b630-8e90eddd2a3e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052374457700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9528a2c-ed01-4f59-ab50-d8484413d217", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052374698600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2a0710d-5ffa-4e75-9b03-3d8533680ca1", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052376689400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f3d428f-66ce-4273-a3a4-581916ff818c", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052382418600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80007d87-d7c8-474d-bb3e-c6c8d80111cb", "name": "entry : default@MergeProfile cost memory 0.1175537109375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052382679100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3822e1ff-4076-427b-8e2d-53c435472d49", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052376656400, "endTime": 224052382828100}, "additional": {"logType": "info", "children": [], "durationId": "97271739-46e1-4cd2-a79b-f02a0e4c20fd"}}, {"head": {"id": "be23f970-53c0-4d29-b5b1-529e995abb11", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052393847000, "endTime": 224052402450400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "45b70f3d-96c7-4ee0-948d-7ac2b7d2a4bd", "logId": "ac379c90-ccb1-48d4-8056-015fa9feaf67"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "45b70f3d-96c7-4ee0-948d-7ac2b7d2a4bd", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052388911300}, "additional": {"logType": "detail", "children": [], "durationId": "be23f970-53c0-4d29-b5b1-529e995abb11"}}, {"head": {"id": "cfa6f577-2787-494d-910a-5926b0eb70ae", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052391572600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17adcccc-3eb0-48d2-880c-cbc99d80e04a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052391806500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3591b9d8-4b8a-418c-a4e5-dac6ac2726e9", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052393871600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28f1e6ad-8b26-4b09-ad33-0bbc661bb63d", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052396113900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05b6fb03-96ce-46ac-9980-5c65d138b529", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052402028500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c0cfe6a-8531-49e4-94c5-92e9bc34d8f9", "name": "entry : default@CreateBuildProfile cost memory 0.10823822021484375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052402273500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac379c90-ccb1-48d4-8056-015fa9feaf67", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052393847000, "endTime": 224052402450400}, "additional": {"logType": "info", "children": [], "durationId": "be23f970-53c0-4d29-b5b1-529e995abb11"}}, {"head": {"id": "3b378c3b-2c48-4c90-b53f-821bba69ba78", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052409822800, "endTime": 224052410652300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "77289780-e5d7-4210-b564-71a6b9a0c482", "logId": "6768112c-e927-4b1d-a29d-6020d433e3ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "77289780-e5d7-4210-b564-71a6b9a0c482", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052406026900}, "additional": {"logType": "detail", "children": [], "durationId": "3b378c3b-2c48-4c90-b53f-821bba69ba78"}}, {"head": {"id": "b6cace1d-fea1-4402-a088-56df86a2022b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052408151900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcad84c1-c582-43af-b579-0324cb3ef62d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052408354800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5fa9ca3-6590-46a9-8948-fe8902f71782", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052409842000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b5b6e86-0920-4970-9b4d-2d809394ac47", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052410032900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82e1c419-dec1-4854-af5b-762e7c9c3747", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052410111800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d384e92c-399d-4441-84ac-8d21602ad1c1", "name": "entry : default@PreCheckSyscap cost memory 0.0409698486328125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052410423600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c7b597e-0866-4101-8435-af634f96c432", "name": "runTaskFromQueue task cost before running: 453 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052410566800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6768112c-e927-4b1d-a29d-6020d433e3ff", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052409822800, "endTime": 224052410652300, "totalTime": 718500}, "additional": {"logType": "info", "children": [], "durationId": "3b378c3b-2c48-4c90-b53f-821bba69ba78"}}, {"head": {"id": "4be6e5f2-c15a-4587-8cd1-96053e56b435", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052418969200, "endTime": 224052431927700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "79f7aaf0-1b6c-48b5-a27c-ba5d09cce971", "logId": "c9bf2a0a-03e4-4e36-821e-ac0340382dfe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "79f7aaf0-1b6c-48b5-a27c-ba5d09cce971", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052414519100}, "additional": {"logType": "detail", "children": [], "durationId": "4be6e5f2-c15a-4587-8cd1-96053e56b435"}}, {"head": {"id": "a0612be8-7874-4395-bffa-37b85e624e7a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052416312100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cade85f-2358-40ae-83e5-2b7f813c45b4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052416459500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47029949-187a-458e-9939-f34603cc0436", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052418989000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e82d1877-9cc9-422a-9d17-b070031bff1e", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052429641300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a551780c-d2e7-4ae0-a9c3-63324b0d7cc9", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052431510700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69d701ae-3cc4-4312-80fd-6d4e79d7b4ad", "name": "entry : default@GeneratePkgContextInfo cost memory 0.25438690185546875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052431758400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9bf2a0a-03e4-4e36-821e-ac0340382dfe", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052418969200, "endTime": 224052431927700}, "additional": {"logType": "info", "children": [], "durationId": "4be6e5f2-c15a-4587-8cd1-96053e56b435"}}, {"head": {"id": "e242b439-0122-4e22-a303-3d74865e4508", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052452831700, "endTime": 224052457300300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "c2097d7c-e518-413b-95bb-8d266f2303ff", "logId": "9c9fbbc7-8d22-4552-83b8-00d70ccd3612"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c2097d7c-e518-413b-95bb-8d266f2303ff", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052435881900}, "additional": {"logType": "detail", "children": [], "durationId": "e242b439-0122-4e22-a303-3d74865e4508"}}, {"head": {"id": "19cdc74d-1499-4b70-af66-e5e1b9b6b2a3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052438837200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "432611fe-8c1d-4722-97c5-b1355a2e3136", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052439151800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f855526-2f46-41aa-b2b1-0967ce51701f", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052452858300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18e1dccd-1c63-46a9-9bdb-9af5fe1b2e39", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052456337300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "219ac9eb-268b-450b-9447-fe79a2c06968", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052456623400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5062c216-c49d-46cd-ab1c-eb6071a273d6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052456815600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "852a194c-819d-486a-8955-b099fb10d9c3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052456929200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dd2160e-9be7-4c50-8985-e0e8b5cf2cf1", "name": "entry : default@ProcessIntegratedHsp cost memory 0.12058258056640625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052457071700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10fff676-e77d-49d0-8222-8d9b8b4be029", "name": "runTaskFromQueue task cost before running: 499 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052457201200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c9fbbc7-8d22-4552-83b8-00d70ccd3612", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052452831700, "endTime": 224052457300300, "totalTime": 4344500}, "additional": {"logType": "info", "children": [], "durationId": "e242b439-0122-4e22-a303-3d74865e4508"}}, {"head": {"id": "c7c2b2e6-62c5-4d70-ae10-c5b95e87e0d2", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052464973300, "endTime": 224052465684200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "df8dd178-4123-4537-91d4-bcd30667a836", "logId": "0fb15d31-3309-42da-854b-fc80916252a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df8dd178-4123-4537-91d4-bcd30667a836", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052461335300}, "additional": {"logType": "detail", "children": [], "durationId": "c7c2b2e6-62c5-4d70-ae10-c5b95e87e0d2"}}, {"head": {"id": "bb810aa1-4731-4712-aa68-30b074e7dc0b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052463173800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c9b6736-ccdc-497c-9381-9377be9c9012", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052463412500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8583f29-072a-485b-9721-7b81b07a7ce0", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052464992700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b374e8db-1763-4e7e-8853-63ad769425a2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052465234300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fbb2762-7d4d-4877-a888-6642f4d8331c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052465338100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8573c529-44ce-4704-9264-387911e5d9a6", "name": "entry : default@BuildNativeWithCmake cost memory 0.03838348388671875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052465457600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b21aff47-3d40-42d3-9104-dfeff947edd7", "name": "runTaskFromQueue task cost before running: 508 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052465589700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fb15d31-3309-42da-854b-fc80916252a2", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052464973300, "endTime": 224052465684200, "totalTime": 588900}, "additional": {"logType": "info", "children": [], "durationId": "c7c2b2e6-62c5-4d70-ae10-c5b95e87e0d2"}}, {"head": {"id": "8f86aa75-3443-44f4-956d-492dd314ba92", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052472489300, "endTime": 224052480873700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "85122ffe-b95f-4ac6-a7f8-0fc15aee467a", "logId": "66ea16ee-8fa7-4043-8c08-2684c99a2ff1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85122ffe-b95f-4ac6-a7f8-0fc15aee467a", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052468470600}, "additional": {"logType": "detail", "children": [], "durationId": "8f86aa75-3443-44f4-956d-492dd314ba92"}}, {"head": {"id": "392004f9-b8d4-4e46-a314-1a04f89fb879", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052470835300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b75321fc-c7a4-4250-8330-5ebb2c53c57b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052471007400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12c62157-a8b0-4108-9523-639d53ed1ff4", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052472512000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "035a96e9-2ef1-4318-8627-50fab8f81376", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052480296600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49d9c04e-afd3-4998-a2c8-bbe370e97c14", "name": "entry : default@MakePackInfo cost memory 0.16400146484375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052480643700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66ea16ee-8fa7-4043-8c08-2684c99a2ff1", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052472489300, "endTime": 224052480873700}, "additional": {"logType": "info", "children": [], "durationId": "8f86aa75-3443-44f4-956d-492dd314ba92"}}, {"head": {"id": "cfa9c9a3-a709-4d6c-b020-120557cb8b93", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052491438500, "endTime": 224052498429600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "242211e8-75bf-4f70-9567-f15c10b141d4", "logId": "42fa444f-154d-4125-9089-3be962329c80"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "242211e8-75bf-4f70-9567-f15c10b141d4", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052485398400}, "additional": {"logType": "detail", "children": [], "durationId": "cfa9c9a3-a709-4d6c-b020-120557cb8b93"}}, {"head": {"id": "9e56775c-282b-4491-b767-0d73479de24f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052487541000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71fb52ca-0a9e-4ecf-ab26-6de78c1ed0a6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052487741600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "482fc92e-b5ac-4511-9725-3d7c22471045", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052491472700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "107e70af-9def-4353-936d-43edb2916851", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052492014800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5804b7c5-3baa-4a46-b0fa-80e4b0268746", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052493584300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f81e2c9c-ca05-424b-bbd1-590703355bed", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052497968000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0639f917-f96c-46d1-9f0c-fa998926588a", "name": "entry : default@SyscapTransform cost memory 0.14977264404296875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052498264400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42fa444f-154d-4125-9089-3be962329c80", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052491438500, "endTime": 224052498429600}, "additional": {"logType": "info", "children": [], "durationId": "cfa9c9a3-a709-4d6c-b020-120557cb8b93"}}, {"head": {"id": "bc4fd4b0-10c2-4564-93d3-5e37bd6237cc", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052506863700, "endTime": 224052511867700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "19808a55-96a7-4a14-b8fb-16b5ad3b75a9", "logId": "235aaa7c-6540-49e5-88fe-b2a5215529d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19808a55-96a7-4a14-b8fb-16b5ad3b75a9", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052501470800}, "additional": {"logType": "detail", "children": [], "durationId": "bc4fd4b0-10c2-4564-93d3-5e37bd6237cc"}}, {"head": {"id": "c0631103-107f-491a-91b2-4b6b192c1070", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052503024500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00ef978b-1af2-4a08-bc48-3f596d6eb4b7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052503171500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e02ae363-7cc5-44a3-b9f7-bd1e74037395", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052506884300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d136e479-a0da-4b37-bf26-ec98607df6f2", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052511466600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a0b4a69-de08-4d3a-9e49-4cdc2dc66078", "name": "entry : default@ProcessProfile cost memory 0.12444305419921875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052511721000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "235aaa7c-6540-49e5-88fe-b2a5215529d7", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052506863700, "endTime": 224052511867700}, "additional": {"logType": "info", "children": [], "durationId": "bc4fd4b0-10c2-4564-93d3-5e37bd6237cc"}}, {"head": {"id": "1109d59d-265e-4c01-b7ca-bfb8669bf618", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052522210000, "endTime": 224052536044000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0d372fec-3dc2-4f52-95e0-b0c5387e9998", "logId": "960734a3-3122-4f89-b307-4d71d15ef2c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d372fec-3dc2-4f52-95e0-b0c5387e9998", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052515963600}, "additional": {"logType": "detail", "children": [], "durationId": "1109d59d-265e-4c01-b7ca-bfb8669bf618"}}, {"head": {"id": "c93058cd-6f68-4e88-b3a4-36d3510f046c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052518061300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8d94cad-aa92-411c-bb31-518a6ebfe9ff", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052518265400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56e8dc64-856e-4620-8d61-baf19521b144", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052522329400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6981bb9b-a7cf-4077-bb15-dfb2e8be7dd3", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052535702800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af29e3a5-bf7a-41ce-b24a-cf7b5905f234", "name": "entry : default@ProcessRouterMap cost memory 0.234649658203125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052535919100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "960734a3-3122-4f89-b307-4d71d15ef2c1", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052522210000, "endTime": 224052536044000}, "additional": {"logType": "info", "children": [], "durationId": "1109d59d-265e-4c01-b7ca-bfb8669bf618"}}, {"head": {"id": "91cd82dc-f3ad-407f-848a-a3df50ca506f", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052543785100, "endTime": 224052554957700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "a05fa59c-98a7-4aae-9736-f3f216e1e700", "logId": "6315813f-671d-4184-b245-05cdb910eaf9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a05fa59c-98a7-4aae-9736-f3f216e1e700", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052541332600}, "additional": {"logType": "detail", "children": [], "durationId": "91cd82dc-f3ad-407f-848a-a3df50ca506f"}}, {"head": {"id": "418e4553-4d4a-4bc3-93f0-d6370ab02cdd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052543420200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3de1f9b8-9a27-45ec-b718-4fdce33c08fb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052543610300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2fa313f-7b51-4f30-9cba-30ed9ccba30e", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052543799400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "057ef0a1-a89b-4598-aec8-977d37a2ca57", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052543998700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94103531-4560-4a51-9dbb-3d0f5b2b0edb", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052552588800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46064c65-656b-4e0a-993c-8c83aee17692", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052552795900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f9458da-79d4-4e8c-a926-1bd7149a9125", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052552949400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4290797d-06f9-4a3a-b334-3713ce9ef7c6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052553032100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d73135f-71d2-4b1d-aa95-f39b1661ac7f", "name": "entry : default@ProcessStartupConfig cost memory 0.258270263671875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052554666400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cce00141-721e-4099-bc91-4f070d872557", "name": "runTaskFromQueue task cost before running: 597 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052554858000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6315813f-671d-4184-b245-05cdb910eaf9", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052543785100, "endTime": 224052554957700, "totalTime": 11042400}, "additional": {"logType": "info", "children": [], "durationId": "91cd82dc-f3ad-407f-848a-a3df50ca506f"}}, {"head": {"id": "616879fc-66c4-4d41-911b-3cf399534e78", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052564000700, "endTime": 224052566314400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "087bcefc-0bce-4643-a172-794d7207eb64", "logId": "ca43605a-9aca-4f5f-bd6c-50f2f4459cac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "087bcefc-0bce-4643-a172-794d7207eb64", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052559920700}, "additional": {"logType": "detail", "children": [], "durationId": "616879fc-66c4-4d41-911b-3cf399534e78"}}, {"head": {"id": "4f795d3c-8023-42da-b91b-3b8ed45bf980", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052562224500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2cf3656-a742-4099-ba1f-0865c6085ef1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052562406000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4aefb55d-8078-4553-8699-ec40256092d1", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052564019000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb04392e-516b-4f55-9bbd-c40ce6f75f78", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052564239000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39bdf8ff-a4e0-43ae-8010-580c086bd398", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052564331600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96995ecd-1c1e-4d0d-ab58-ece57cdb025e", "name": "entry : default@BuildNativeWithNinja cost memory 0.05809783935546875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052566049200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46866a07-f2ea-46bf-848a-be13b9be0b8d", "name": "runTaskFromQueue task cost before running: 609 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052566241500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca43605a-9aca-4f5f-bd6c-50f2f4459cac", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052564000700, "endTime": 224052566314400, "totalTime": 2209900}, "additional": {"logType": "info", "children": [], "durationId": "616879fc-66c4-4d41-911b-3cf399534e78"}}, {"head": {"id": "63428595-b58e-4814-83e8-accde531e8ba", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052578263400, "endTime": 224052588666500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "ce755c17-6a55-49ba-9662-6c9afcb1d3c8", "logId": "d0d04119-bbbe-4b43-8784-0afb1035dced"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce755c17-6a55-49ba-9662-6c9afcb1d3c8", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052570026000}, "additional": {"logType": "detail", "children": [], "durationId": "63428595-b58e-4814-83e8-accde531e8ba"}}, {"head": {"id": "b4f89cd7-dddf-4585-a960-403ed67b47d3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052571717300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e33ac213-af4d-46d8-87af-511a3f9e36be", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052572054100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8561f85b-7ad0-4de5-9a8b-aee8130f81f1", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052575213500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe4fcd0d-4a9d-4810-a20c-37b12a85c7c9", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052581851200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e184d5a-2b2b-489d-bcf1-12521320388f", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052585555100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5fb5ed7-a5ab-4208-9328-3e321b1da61f", "name": "entry : default@ProcessResource cost memory 0.1617279052734375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052585764700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0d04119-bbbe-4b43-8784-0afb1035dced", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052578263400, "endTime": 224052588666500}, "additional": {"logType": "info", "children": [], "durationId": "63428595-b58e-4814-83e8-accde531e8ba"}}, {"head": {"id": "ad3fa2a7-b394-4d86-9669-fd8fdd8df55e", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052602118100, "endTime": 224052649195700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ba534b00-d221-4a01-935c-f598fae107ca", "logId": "d44d2bfa-1279-468f-af2f-9e39349b2650"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ba534b00-d221-4a01-935c-f598fae107ca", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052594777800}, "additional": {"logType": "detail", "children": [], "durationId": "ad3fa2a7-b394-4d86-9669-fd8fdd8df55e"}}, {"head": {"id": "6f45a0de-4905-4bbf-a514-fed1d075759b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052596686300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d1d570c-9361-48c1-82a3-db18c4ca66e6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052596951100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "370dd064-4905-4193-bf0c-7c9229fa378f", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052602133600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39a69b61-a3b9-47f8-9163-d47ca8e0706e", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 27 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052648796300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2004dcd2-523d-439e-ba82-9793697a980f", "name": "entry : default@GenerateLoaderJson cost memory -4.6867828369140625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052649055000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d44d2bfa-1279-468f-af2f-9e39349b2650", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052602118100, "endTime": 224052649195700}, "additional": {"logType": "info", "children": [], "durationId": "ad3fa2a7-b394-4d86-9669-fd8fdd8df55e"}}, {"head": {"id": "7877191e-bd46-4775-a2a6-e726bc5c9b57", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052669164900, "endTime": 224052675643100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "d14675a7-2f24-4d1e-9115-6563098a16b8", "logId": "951e7dfb-0cf5-453a-aa7b-fba0dd134bfb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d14675a7-2f24-4d1e-9115-6563098a16b8", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052665942000}, "additional": {"logType": "detail", "children": [], "durationId": "7877191e-bd46-4775-a2a6-e726bc5c9b57"}}, {"head": {"id": "b4bbe77b-49ad-4c6a-bf2b-6fff4e2d0658", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052667526300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba9ed58a-80be-4d8d-b113-3b5089537e28", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052667702500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3d0d8f3-ed31-431e-9dcf-2df9218765bc", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052669174700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b26121d4-2b2b-4e26-afd7-d970f37fd66b", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052675220500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e037232f-e3ff-485f-86a8-f35f8956797a", "name": "entry : default@ProcessLibs cost memory 0.141510009765625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052675478600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "951e7dfb-0cf5-453a-aa7b-fba0dd134bfb", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052669164900, "endTime": 224052675643100}, "additional": {"logType": "info", "children": [], "durationId": "7877191e-bd46-4775-a2a6-e726bc5c9b57"}}, {"head": {"id": "efb9e24c-514e-43bc-8eea-8cbf448c11a5", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052688164700, "endTime": 224052750514900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "b5b5b3ea-70a9-4139-be9d-eae6519cc752", "logId": "59ff767f-ee43-4482-baa6-9f103af36b93"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b5b5b3ea-70a9-4139-be9d-eae6519cc752", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052679254300}, "additional": {"logType": "detail", "children": [], "durationId": "efb9e24c-514e-43bc-8eea-8cbf448c11a5"}}, {"head": {"id": "c36f728c-dc92-4442-93df-f3f5d99a4988", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052681100600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a2874b4-cbf8-4e27-afea-f143d2ec8595", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052681307200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b997cd1-847e-4670-99de-47003ec5bb52", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052683140100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de3d6aba-4dab-4508-b3e7-ca79284b406f", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052688202500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ffd0680-7d19-446e-a54f-73a11bd96995", "name": "Incremental task entry:default@CompileResource pre-execution cost: 60 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052750018800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c52fa2f-09d8-4853-beeb-7280abf859e1", "name": "entry : default@CompileResource cost memory 1.3044662475585938", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052750317600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59ff767f-ee43-4482-baa6-9f103af36b93", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052688164700, "endTime": 224052750514900}, "additional": {"logType": "info", "children": [], "durationId": "efb9e24c-514e-43bc-8eea-8cbf448c11a5"}}, {"head": {"id": "656c0111-39e6-42d6-98b8-4c153ad37302", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052766949500, "endTime": 224052772558200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "22255375-710d-462a-b25b-8cc5a435bc24", "logId": "9b4b817b-9368-4dad-acf3-0e7145075631"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22255375-710d-462a-b25b-8cc5a435bc24", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052756717700}, "additional": {"logType": "detail", "children": [], "durationId": "656c0111-39e6-42d6-98b8-4c153ad37302"}}, {"head": {"id": "c7e0bf29-1023-4837-a28c-175d7b3364e1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052760357600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "372fe2d4-2113-46f3-81d5-bf0c077c9b8a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052760548800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5973a575-8325-4cd4-a729-77e2c6259288", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052766969800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d63a1f6-098e-4922-9528-4c8fed7930a1", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052768002500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abcb4a3a-b72c-49fd-acb0-952671f24d68", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052772199800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4255ac30-1e66-4277-9d40-83a0ae3a0b95", "name": "entry : default@DoNativeStrip cost memory 0.07930755615234375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052772418300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b4b817b-9368-4dad-acf3-0e7145075631", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052766949500, "endTime": 224052772558200}, "additional": {"logType": "info", "children": [], "durationId": "656c0111-39e6-42d6-98b8-4c153ad37302"}}, {"head": {"id": "ab136941-c302-460d-9dfd-dc614f230bf9", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052783907100, "endTime": 224072017496600}, "additional": {"children": ["a7729db6-4dc8-49f5-a80f-bdfbcdf44946", "6419b8e9-0914-4fa7-8e30-603d2bcdcf43"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed."], "detailId": "84fef9a6-1055-4ba4-a3ea-6a3d632e0b1e", "logId": "32f1a93e-470f-4f10-b0ed-86f9ad7d2370"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "84fef9a6-1055-4ba4-a3ea-6a3d632e0b1e", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052774992600}, "additional": {"logType": "detail", "children": [], "durationId": "ab136941-c302-460d-9dfd-dc614f230bf9"}}, {"head": {"id": "8e32d013-df4b-4240-8e34-b34260e05545", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052776632000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42622dd6-b0f7-44a9-aa63-570f64cb4b09", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052776792000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49d95576-2a92-422c-949a-b57d7f275551", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052783930100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5263e655-dbfd-4bf9-9ab6-ee950cf26bae", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052784230400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "036fad24-34d7-4a92-8bd2-f475730850f0", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052825504400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52b76049-31a9-45d8-82a4-717bf12c6573", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 33 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052825920100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87399889-9704-4307-9119-24f95aa2999e", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052873093600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33eeee16-c442-43d9-b818-6366d9b85743", "name": "default@CompileArkTS work[12] is submitted.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052875662600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7729db6-4dc8-49f5-a80f-bdfbcdf44946", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 224057870379100, "endTime": 224071997276600}, "additional": {"children": ["78d80ce2-b22b-4244-8280-35fe7d2f6607", "2f973f0f-2f2d-4243-9e4d-6787c1db3581", "a73790e2-b9a9-451a-a0ac-309b1e56e1df", "024b35b1-f890-4079-a361-730c34015e9c", "3b59ccb9-e761-417f-9c61-e545060bacd1", "b52f807f-31f0-4fd9-ac37-d744e4163788", "09c835fc-a136-4622-8bb3-3d7209837276"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "ab136941-c302-460d-9dfd-dc614f230bf9", "logId": "25974b37-f11f-4516-986e-391d02b0cc41"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ca3132c-3aa6-43db-ad4e-ddbf01e988c0", "name": "default@CompileArkTS work[12] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052877176900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69dee3a2-e6cc-41e0-b3a9-f388769535b7", "name": "default@CompileArkTS work[12] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052877324600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2553c99-1948-46e5-8be3-7bc53c536c25", "name": "CopyResources startTime: 224052877393400", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052877395800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b0dbeb7-d1a2-4dcf-bfb7-4f490620cc8a", "name": "default@CompileArkTS work[13] is submitted.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052877475300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6419b8e9-0914-4fa7-8e30-603d2bcdcf43", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker18", "startTime": 224054541226500, "endTime": 224054559952000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "ab136941-c302-460d-9dfd-dc614f230bf9", "logId": "13291c2a-0dd2-4108-9e1d-076b1c64b1c7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cebc4dd9-fe64-43dd-b0ba-5bc8370772f5", "name": "default@CompileArkTS work[13] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052878465500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f7ccf81-d504-4ad2-b90b-45c33359bcfe", "name": "default@CompileArkTS work[13] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052878585800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61418789-61db-4105-9f4d-2cf9d3dedf7c", "name": "entry : default@CompileArkTS cost memory 2.1562423706054688", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052878755200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52a00833-e568-4728-b364-c257e2fdb793", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052888003200, "endTime": 224052901266000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "88465ce9-4b6a-4928-ba8c-d36c71c91a92", "logId": "8f590acd-0659-437f-bb82-7eaf58fad851"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88465ce9-4b6a-4928-ba8c-d36c71c91a92", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052881507400}, "additional": {"logType": "detail", "children": [], "durationId": "52a00833-e568-4728-b364-c257e2fdb793"}}, {"head": {"id": "f05552d1-736b-497b-9ae9-c2a107816376", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052882740000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b027f28-7ff0-422a-8f1e-b522f9cd30f3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052882877300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fb90ecf-0bbb-45d9-83c7-662b3b21e585", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052888031000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "205dadd5-0af2-4157-baaa-3a2e04eefd3f", "name": "entry : default@BuildJS cost memory 0.34114837646484375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052901015700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02b91ee9-5790-477d-8fb5-05bad0c8a287", "name": "runTaskFromQueue task cost before running: 943 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052901178900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f590acd-0659-437f-bb82-7eaf58fad851", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052888003200, "endTime": 224052901266000, "totalTime": 13172000}, "additional": {"logType": "info", "children": [], "durationId": "52a00833-e568-4728-b364-c257e2fdb793"}}, {"head": {"id": "121a89e4-caf0-47f4-a076-850e870c0d73", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052907596400, "endTime": 224052911151300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "6e442981-af79-4330-8570-9a026e2e45c2", "logId": "aaeb3bf5-54dd-4db5-9412-87a1b1ddbe68"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e442981-af79-4330-8570-9a026e2e45c2", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052903289400}, "additional": {"logType": "detail", "children": [], "durationId": "121a89e4-caf0-47f4-a076-850e870c0d73"}}, {"head": {"id": "0d2b13a7-0acd-41fc-85a5-f140433d0b9d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052904292100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da1dad09-fbd6-4055-84c1-7763e50c0aa9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052904419400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "312bcbcc-394c-4279-97c9-4759db3197c4", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052907609400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef7f7dde-f27a-4ac1-a846-20134b2d1af6", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052908547400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08c1b250-8a4f-43ff-83f8-b48d067d62de", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052910876900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "720d079b-ae2f-4a0d-a0f0-df8708ee73d7", "name": "entry : default@CacheNativeLibs cost memory 0.09442901611328125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052911060500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaeb3bf5-54dd-4db5-9412-87a1b1ddbe68", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052907596400, "endTime": 224052911151300}, "additional": {"logType": "info", "children": [], "durationId": "121a89e4-caf0-47f4-a076-850e870c0d73"}}, {"head": {"id": "6a693ee3-81e3-441f-b97f-24debc762526", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224053090722100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2686c05a-0af3-4dda-b062-8a464ae8dbf5", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224053091168800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd00de31-46e6-41e3-a396-7c97ef43536e", "name": "default@CompileArkTS work[13] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224053092605500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a361a7fd-ba63-4468-8125-b97a61487743", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224053736188800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2e03e38-5eb8-4685-82ab-ba4a2ba412b1", "name": "A work dispatched to worker[18] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224053736396700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67a84402-9891-4fc1-94d2-3e10f5914782", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224053736524400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff4c91c8-2eef-443d-bae1-3<PERSON><PERSON><PERSON>0254", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224053736588300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df6a90c6-a62c-4e00-a51f-055e2000902f", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224053736641100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f81c3cbc-d4dc-4e1b-9b25-40953f9e8aba", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224053736690400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e0b9f45-f079-469a-ac34-83507ebae52a", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224053736736100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc406242-5e13-4d93-94e8-2e4af3f049b1", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224053736790300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1c3f1d7-a4b0-4097-a969-a9179a65e15b", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224053736851200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e24cfaf7-5def-472d-947e-0d6cb542667c", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224053736901500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6fbc907-6efd-4659-b6b0-183d58672d74", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224053737157700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "649d0d5f-af9d-42af-a8cb-d4cd6bc5f00a", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224053737235600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bc6dd04-74e9-47c2-8e93-a022a2ae3e38", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224053737268400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32c906b4-576b-4786-b119-f58314fb6269", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224053737300000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a85e1662-7144-44ab-b6ca-5bee4a7e23bf", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224053737328400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5a0a56d-23d8-4880-8bdb-66c99c33d659", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224053737369200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f3cb1e6-226b-4a1b-8cc9-7623b5657dd6", "name": "default@CompileArkTS work[12] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224053738316200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b2e507c-aa81-4239-acc7-0ce10bdb37b8", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224054560257200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb832e03-5bdc-4205-8ddc-83eeb76549b9", "name": "CopyResources is end, endTime: 224054560497500", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224054560504900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baf59820-5f6f-4fbe-b305-6b190da03f99", "name": "default@CompileArkTS work[13] done.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224054560786600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13291c2a-0dd2-4108-9e1d-076b1c64b1c7", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Worker18", "startTime": 224054541226500, "endTime": 224054559952000}, "additional": {"logType": "info", "children": [], "durationId": "6419b8e9-0914-4fa7-8e30-603d2bcdcf43", "parent": "32f1a93e-470f-4f10-b0ed-86f9ad7d2370"}}, {"head": {"id": "363e1c1b-7720-48f4-a4b2-10d75d56c42b", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224055081373700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2525b74b-a3bf-4a2f-93f7-5f89de5ac50a", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224071998559800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78d80ce2-b22b-4244-8280-35fe7d2f6607", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 224057873121000, "endTime": 224059364663300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a7729db6-4dc8-49f5-a80f-bdfbcdf44946", "logId": "7ce48507-3c3e-49d2-ba1c-836d0fbf9843"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ce48507-3c3e-49d2-ba1c-836d0fbf9843", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224057873121000, "endTime": 224059364663300}, "additional": {"logType": "info", "children": [], "durationId": "78d80ce2-b22b-4244-8280-35fe7d2f6607", "parent": "25974b37-f11f-4516-986e-391d02b0cc41"}}, {"head": {"id": "2f973f0f-2f2d-4243-9e4d-6787c1db3581", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 224059369353700, "endTime": 224059488310200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a7729db6-4dc8-49f5-a80f-bdfbcdf44946", "logId": "58a62986-a107-4f9e-915b-1f1a59e91a71"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "58a62986-a107-4f9e-915b-1f1a59e91a71", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224059369353700, "endTime": 224059488310200}, "additional": {"logType": "info", "children": [], "durationId": "2f973f0f-2f2d-4243-9e4d-6787c1db3581", "parent": "25974b37-f11f-4516-986e-391d02b0cc41"}}, {"head": {"id": "a73790e2-b9a9-451a-a0ac-309b1e56e1df", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 224059488502500, "endTime": 224059489016900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a7729db6-4dc8-49f5-a80f-bdfbcdf44946", "logId": "d399ea46-94d4-4472-a86e-832aeb301a01"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d399ea46-94d4-4472-a86e-832aeb301a01", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224059488502500, "endTime": 224059489016900}, "additional": {"logType": "info", "children": [], "durationId": "a73790e2-b9a9-451a-a0ac-309b1e56e1df", "parent": "25974b37-f11f-4516-986e-391d02b0cc41"}}, {"head": {"id": "024b35b1-f890-4079-a361-730c34015e9c", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 224059489166400, "endTime": 224071700751700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a7729db6-4dc8-49f5-a80f-bdfbcdf44946", "logId": "bc6ecfb4-5c8c-48d7-a6dc-51ff268e570d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc6ecfb4-5c8c-48d7-a6dc-51ff268e570d", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224059489166400, "endTime": 224071700751700}, "additional": {"logType": "info", "children": [], "durationId": "024b35b1-f890-4079-a361-730c34015e9c", "parent": "25974b37-f11f-4516-986e-391d02b0cc41"}}, {"head": {"id": "3b59ccb9-e761-417f-9c61-e545060bacd1", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 224071701054600, "endTime": 224071723402300}, "additional": {"children": ["7019c051-44eb-414b-907e-7224b3a6c11a", "918a2d4b-ffec-45ec-ab41-6e6da7334efc", "f0dd379f-ee74-4f2e-bc11-820aea78684a"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a7729db6-4dc8-49f5-a80f-bdfbcdf44946", "logId": "7104d330-c1d5-4b5f-a3e1-0d67c7db0192"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7104d330-c1d5-4b5f-a3e1-0d67c7db0192", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224071701054600, "endTime": 224071723402300}, "additional": {"logType": "info", "children": ["5456d19e-7da9-4fb8-a696-09d027abfd85", "c5366428-cf9e-4f7f-b09f-459b2c836d49", "ce5a892c-531f-4a03-8f29-027175520849"], "durationId": "3b59ccb9-e761-417f-9c61-e545060bacd1", "parent": "25974b37-f11f-4516-986e-391d02b0cc41"}}, {"head": {"id": "7019c051-44eb-414b-907e-7224b3a6c11a", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 224071701227000, "endTime": 224071701254000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3b59ccb9-e761-417f-9c61-e545060bacd1", "logId": "5456d19e-7da9-4fb8-a696-09d027abfd85"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5456d19e-7da9-4fb8-a696-09d027abfd85", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224071701227000, "endTime": 224071701254000}, "additional": {"logType": "info", "children": [], "durationId": "7019c051-44eb-414b-907e-7224b3a6c11a", "parent": "7104d330-c1d5-4b5f-a3e1-0d67c7db0192"}}, {"head": {"id": "918a2d4b-ffec-45ec-ab41-6e6da7334efc", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 224071701265800, "endTime": 224071716659800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3b59ccb9-e761-417f-9c61-e545060bacd1", "logId": "c5366428-cf9e-4f7f-b09f-459b2c836d49"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c5366428-cf9e-4f7f-b09f-459b2c836d49", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224071701265800, "endTime": 224071716659800}, "additional": {"logType": "info", "children": [], "durationId": "918a2d4b-ffec-45ec-ab41-6e6da7334efc", "parent": "7104d330-c1d5-4b5f-a3e1-0d67c7db0192"}}, {"head": {"id": "f0dd379f-ee74-4f2e-bc11-820aea78684a", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 224071716708000, "endTime": 224071723318200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3b59ccb9-e761-417f-9c61-e545060bacd1", "logId": "ce5a892c-531f-4a03-8f29-027175520849"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce5a892c-531f-4a03-8f29-027175520849", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224071716708000, "endTime": 224071723318200}, "additional": {"logType": "info", "children": [], "durationId": "f0dd379f-ee74-4f2e-bc11-820aea78684a", "parent": "7104d330-c1d5-4b5f-a3e1-0d67c7db0192"}}, {"head": {"id": "b52f807f-31f0-4fd9-ac37-d744e4163788", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 224071723438900, "endTime": 224071986056200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a7729db6-4dc8-49f5-a80f-bdfbcdf44946", "logId": "b2cf59fa-30d8-472e-a3f6-e0dd8ffa4ec8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b2cf59fa-30d8-472e-a3f6-e0dd8ffa4ec8", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224071723438900, "endTime": 224071986056200}, "additional": {"logType": "info", "children": [], "durationId": "b52f807f-31f0-4fd9-ac37-d744e4163788", "parent": "25974b37-f11f-4516-986e-391d02b0cc41"}}, {"head": {"id": "09c835fc-a136-4622-8bb3-3d7209837276", "name": "load compilation dependencies", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 224055017619300, "endTime": 224057868182300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a7729db6-4dc8-49f5-a80f-bdfbcdf44946", "logId": "dc8a05c8-f506-4ac3-90b4-808a72466b0b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc8a05c8-f506-4ac3-90b4-808a72466b0b", "name": "load compilation dependencies", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224055017619300, "endTime": 224057868182300}, "additional": {"logType": "info", "children": [], "durationId": "09c835fc-a136-4622-8bb3-3d7209837276", "parent": "25974b37-f11f-4516-986e-391d02b0cc41"}}, {"head": {"id": "fb53be93-94f9-43cb-9e6d-6fd529d92e8f", "name": "default@CompileArkTS work[12] done.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072017029300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25974b37-f11f-4516-986e-391d02b0cc41", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 224057870379100, "endTime": 224071997276600}, "additional": {"logType": "info", "children": ["7ce48507-3c3e-49d2-ba1c-836d0fbf9843", "58a62986-a107-4f9e-915b-1f1a59e91a71", "d399ea46-94d4-4472-a86e-832aeb301a01", "bc6ecfb4-5c8c-48d7-a6dc-51ff268e570d", "7104d330-c1d5-4b5f-a3e1-0d67c7db0192", "b2cf59fa-30d8-472e-a3f6-e0dd8ffa4ec8", "dc8a05c8-f506-4ac3-90b4-808a72466b0b"], "durationId": "a7729db6-4dc8-49f5-a80f-bdfbcdf44946", "parent": "32f1a93e-470f-4f10-b0ed-86f9ad7d2370"}}, {"head": {"id": "32f1a93e-470f-4f10-b0ed-86f9ad7d2370", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224052783907100, "endTime": 224072017496600, "totalTime": 14240549700}, "additional": {"logType": "info", "children": ["25974b37-f11f-4516-986e-391d02b0cc41", "13291c2a-0dd2-4108-9e1d-076b1c64b1c7"], "durationId": "ab136941-c302-460d-9dfd-dc614f230bf9"}}, {"head": {"id": "90d758f3-0a2a-45a5-ad4a-6dba65e6f21d", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072042798200, "endTime": 224072047107600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "bab6c6b7-d8e2-4876-8f0a-520b3dfaf2ac", "logId": "de07bb37-2cbc-4165-b0a5-d86652d5dba9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bab6c6b7-d8e2-4876-8f0a-520b3dfaf2ac", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072035487600}, "additional": {"logType": "detail", "children": [], "durationId": "90d758f3-0a2a-45a5-ad4a-6dba65e6f21d"}}, {"head": {"id": "80738c6e-25d2-4fb5-94aa-9c06dc93e703", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072039244900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90d61956-e6db-4bac-8fdd-eed5cd4f992b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072039499200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b8195f4-7a0b-4de5-82ad-e683715bd39a", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072042816700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e538f7e1-01e6-4272-b2ba-97623c080307", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072043646300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dbad9c9-d909-4976-8cab-79cc1209d02b", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072046523700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06fe2438-27f8-4cd3-81f5-33d7548f57d4", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07936859130859375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072046969400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de07bb37-2cbc-4165-b0a5-d86652d5dba9", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072042798200, "endTime": 224072047107600}, "additional": {"logType": "info", "children": [], "durationId": "90d758f3-0a2a-45a5-ad4a-6dba65e6f21d"}}, {"head": {"id": "c1089993-0f64-4a2f-a2c4-c3d81e4ac659", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072076969900, "endTime": 224072337882500}, "additional": {"children": ["cfa62dc9-ca10-4f33-8038-2324a157034b", "4fab3a4c-441b-441b-8be5-dc30071b6268"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "f251cefe-1b68-4e2b-b646-ab6b493f5c67", "logId": "5e511b09-776d-48bf-8428-7bfdfa3d8b6a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f251cefe-1b68-4e2b-b646-ab6b493f5c67", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072054628100}, "additional": {"logType": "detail", "children": [], "durationId": "c1089993-0f64-4a2f-a2c4-c3d81e4ac659"}}, {"head": {"id": "4471041e-8fab-48b0-ab1a-1c78a7575715", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072058066500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a424e53-58aa-4797-b77d-8b08d2b59103", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072058335000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "017c5079-6bd1-4781-bc56-97d6d1e47ef2", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072076986500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6de5587-9772-4db5-b6c4-837dc07d8359", "name": "entry:default@PackageHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072121883500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08a6100b-de35-4a3b-aff9-dfa11ba54cef", "name": "Incremental task entry:default@PackageHap pre-execution cost: 39 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072122168700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4a87dba-b980-4e04-9e60-f3eb2049b94c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072122301100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7953465-6d87-45f6-a706-dcde4b9506eb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072122369900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfa62dc9-ca10-4f33-8038-2324a157034b", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072125328800, "endTime": 224072130259900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1089993-0f64-4a2f-a2c4-c3d81e4ac659", "logId": "7dedd571-3d72-4a68-8bb8-f4b2236d44d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "feea973e-6436-435f-b0ff-d606d7bf4289", "name": "Use tool [C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Huawei\\\\Sdk\\\\OpenHarmony\\\\Sdk\\\\18\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--rpcid-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\syscap\\\\default\\\\rpcid.sc',\n  '--ets-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets',\n  '--pkg-context-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader\\\\default\\\\pkgContextInfo.json'\n]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072129926800}, "additional": {"logType": "debug", "children": [], "durationId": "c1089993-0f64-4a2f-a2c4-c3d81e4ac659"}}, {"head": {"id": "7dedd571-3d72-4a68-8bb8-f4b2236d44d2", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072125328800, "endTime": 224072130259900}, "additional": {"logType": "info", "children": [], "durationId": "cfa62dc9-ca10-4f33-8038-2324a157034b", "parent": "5e511b09-776d-48bf-8428-7bfdfa3d8b6a"}}, {"head": {"id": "4fab3a4c-441b-441b-8be5-dc30071b6268", "name": "submit HAP packaging task to java daemon server", "description": "Pack HAP in java daemon", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072131431900, "endTime": 224072331629700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1089993-0f64-4a2f-a2c4-c3d81e4ac659", "logId": "57bfb825-1db7-4d8f-bfd7-ff396ecb1f89"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "abec5016-9d82-481d-ac21-d3a8b4a9a9de", "name": "java daemon socket received message:{\"code\":0,\"message\":\"pack success\"}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072330845200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57bfb825-1db7-4d8f-bfd7-ff396ecb1f89", "name": "submit HAP packaging task to java daemon server", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072131431900, "endTime": 224072331621900}, "additional": {"logType": "info", "children": [], "durationId": "4fab3a4c-441b-441b-8be5-dc30071b6268", "parent": "5e511b09-776d-48bf-8428-7bfdfa3d8b6a"}}, {"head": {"id": "b0723b73-c011-4cf9-b764-66edb273cc4a", "name": "entry : default@PackageHap cost memory -0.05226898193359375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072337588300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44cb03de-7bff-4197-8a5e-691c0cb2ce7e", "name": "runTaskFromQueue task cost before running: 20 s 380 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072337804500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e511b09-776d-48bf-8428-7bfdfa3d8b6a", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072076969900, "endTime": 224072337882500, "totalTime": 260804400}, "additional": {"logType": "info", "children": ["7dedd571-3d72-4a68-8bb8-f4b2236d44d2", "57bfb825-1db7-4d8f-bfd7-ff396ecb1f89"], "durationId": "c1089993-0f64-4a2f-a2c4-c3d81e4ac659"}}, {"head": {"id": "e5503cc6-886b-47a3-b8e8-5881a1d85bc3", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072347357300, "endTime": 224072714555200}, "additional": {"children": ["cbdc778d-da65-480c-b0c0-0f7d3e3cfd6f", "565a46cf-bf92-4c1e-82b6-454d8cbc310b"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "97a026ec-f4a2-4f79-a0d8-cfdf47d6252c", "logId": "955c3a00-1aa5-4a2d-b597-f3d7e28b846d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "97a026ec-f4a2-4f79-a0d8-cfdf47d6252c", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072342890700}, "additional": {"logType": "detail", "children": [], "durationId": "e5503cc6-886b-47a3-b8e8-5881a1d85bc3"}}, {"head": {"id": "39583e9c-afc1-47f5-b563-42a00ce90922", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072344202200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a6f49e2-e705-4110-91b2-f62d1275e6d1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072344335800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85501300-f5aa-45f1-9c38-27c30f5507c3", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072347372200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54cb654a-671c-440a-98d4-1f72bebef39e", "name": "entry:default@SignHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072350094600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "226566fd-e19f-43e9-816f-c8bd18b8b3e1", "name": "Incremental task entry:default@SignHap pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072350390600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b99c72bf-131f-4e7e-9fe8-16bd1289b631", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072350661200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "355f960c-d308-4d1e-a244-ea0d42067d0b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072350721000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbdc778d-da65-480c-b0c0-0f7d3e3cfd6f", "name": "generate hap signing command", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072353923700, "endTime": 224072461001200}, "additional": {"children": ["c4e29499-01a0-4478-b2b2-f5b4e55ef88e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e5503cc6-886b-47a3-b8e8-5881a1d85bc3", "logId": "8a58adcc-bff8-44a5-9233-382915b7ee4f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c4e29499-01a0-4478-b2b2-f5b4e55ef88e", "name": "verify signing configuration and get bundle name", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072388702500, "endTime": 224072459422600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cbdc778d-da65-480c-b0c0-0f7d3e3cfd6f", "logId": "8251c7fb-0872-4c40-a882-20c310616650"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1364c4f1-304e-4855-ad20-2057a0a66b3e", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072393618600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d27057e-d175-499b-a7eb-fc653f4d3bf0", "name": "java daemon socket received message:{\"code\":0,\"message\":\"verify profile success\"}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072458852200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8251c7fb-0872-4c40-a882-20c310616650", "name": "verify signing configuration and get bundle name", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072388702500, "endTime": 224072459422600}, "additional": {"logType": "info", "children": [], "durationId": "c4e29499-01a0-4478-b2b2-f5b4e55ef88e", "parent": "8a58adcc-bff8-44a5-9233-382915b7ee4f"}}, {"head": {"id": "8a58adcc-bff8-44a5-9233-382915b7ee4f", "name": "generate hap signing command", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072353923700, "endTime": 224072461001200}, "additional": {"logType": "info", "children": ["8251c7fb-0872-4c40-a882-20c310616650"], "durationId": "cbdc778d-da65-480c-b0c0-0f7d3e3cfd6f", "parent": "955c3a00-1aa5-4a2d-b597-f3d7e28b846d"}}, {"head": {"id": "565a46cf-bf92-4c1e-82b6-454d8cbc310b", "name": "execute hap signing command", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072461912100, "endTime": 224072713942100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e5503cc6-886b-47a3-b8e8-5881a1d85bc3", "logId": "36f94598-c08a-44f0-ac7f-6a8c7f75ba42"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44ad39ad-6c2f-4358-bf93-83fcba12d8f5", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072464421800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e275184-e98e-4976-9fc4-40ad9df470e3", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072611128700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ff0cd52-6c50-4b1a-b56d-c0e4e2bd1bb2", "name": "java daemon socket received message:{\"code\":0,\"message\":\"sign app success\"}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072713466900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36f94598-c08a-44f0-ac7f-6a8c7f75ba42", "name": "execute hap signing command", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072461912100, "endTime": 224072713942100}, "additional": {"logType": "info", "children": [], "durationId": "565a46cf-bf92-4c1e-82b6-454d8cbc310b", "parent": "955c3a00-1aa5-4a2d-b597-f3d7e28b846d"}}, {"head": {"id": "89a8cba8-baae-4950-aab2-2255910cd34a", "name": "entry : default@SignHap cost memory 0.073883056640625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072714318300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b574a10-10c6-483b-9dcb-f10520d2dab3", "name": "runTaskFromQueue task cost before running: 20 s 757 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072714479000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "955c3a00-1aa5-4a2d-b597-f3d7e28b846d", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072347357300, "endTime": 224072714555200, "totalTime": 367090600}, "additional": {"logType": "info", "children": ["8a58adcc-bff8-44a5-9233-382915b7ee4f", "36f94598-c08a-44f0-ac7f-6a8c7f75ba42"], "durationId": "e5503cc6-886b-47a3-b8e8-5881a1d85bc3"}}, {"head": {"id": "f65767ae-a58b-46d3-add8-abb6adb52043", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072719165900, "endTime": 224072728424300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "da983dd5-0ab5-4663-aba1-f83130553134", "logId": "42c269d8-df76-4587-b961-b79b9db8f57d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da983dd5-0ab5-4663-aba1-f83130553134", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072717060700}, "additional": {"logType": "detail", "children": [], "durationId": "f65767ae-a58b-46d3-add8-abb6adb52043"}}, {"head": {"id": "659ddbb4-eed6-4e08-b9fe-0310e59b9206", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072718175500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35decfb2-8261-4d77-9532-37ea0789b4f1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072718280400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fdea948-9085-4122-8c4e-5753b1fdb4e3", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072719175400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dfc335d-5d59-42a9-840f-50e4c84be253", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072727728300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45e8b0af-2ba3-4eca-bc95-433c1aab90e0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072727937400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c45b86c0-c75f-4185-a474-58f82a02618e", "name": "entry : default@CollectDebugSymbol cost memory -0.6085357666015625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072728151600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01d68465-eaac-40ec-bf96-45f307d54290", "name": "runTaskFromQueue task cost before running: 20 s 771 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072728315500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42c269d8-df76-4587-b961-b79b9db8f57d", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072719165900, "endTime": 224072728424300, "totalTime": 9108000}, "additional": {"logType": "info", "children": [], "durationId": "f65767ae-a58b-46d3-add8-abb6adb52043"}}, {"head": {"id": "e2f9e314-2bbf-4fd8-976d-cb9e3bb5fed0", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072732191000, "endTime": 224072732807400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "efd5ec11-5bae-4941-9615-d1d750cea25c", "logId": "e466e3a0-bf8e-44b8-8e82-7d77ea1a1d23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "efd5ec11-5bae-4941-9615-d1d750cea25c", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072732115300}, "additional": {"logType": "detail", "children": [], "durationId": "e2f9e314-2bbf-4fd8-976d-cb9e3bb5fed0"}}, {"head": {"id": "32140819-7482-47a9-a723-08f6f4dd0267", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072732203900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06d4529d-d58a-4248-8454-8c2b09ed95f4", "name": "entry : assembleHap cost memory 0.01177215576171875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072732478500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f8677e0-4476-4b1e-a742-84c63a9eb734", "name": "runTaskFromQueue task cost before running: 20 s 775 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072732676600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e466e3a0-bf8e-44b8-8e82-7d77ea1a1d23", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072732191000, "endTime": 224072732807400, "totalTime": 447100}, "additional": {"logType": "info", "children": [], "durationId": "e2f9e314-2bbf-4fd8-976d-cb9e3bb5fed0"}}, {"head": {"id": "ab35e1f9-9bad-4513-9cb4-6b8934abc756", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072749360600, "endTime": 224072749397000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7b07f5db-260e-4982-a330-c509d6905699", "logId": "83b8bc3f-feac-4646-bd2e-8ce5968c3803"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "83b8bc3f-feac-4646-bd2e-8ce5968c3803", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072749360600, "endTime": 224072749397000}, "additional": {"logType": "info", "children": [], "durationId": "ab35e1f9-9bad-4513-9cb4-6b8934abc756"}}, {"head": {"id": "1081e9f2-ecb2-434b-aaf0-e4243cafb9d3", "name": "BUILD SUCCESSFUL in 20 s 792 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072749559400}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "1f2f3fc2-cedc-49d3-bf42-737d1a4048ed", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224051958178100, "endTime": 224072750375800}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 20, "minute": 11, "second": 42}, "completeCommand": "{\"prop\":[\"product=default\"],\"mode\":\"module\",\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "d962184c-16d1-4a77-9a28-4178f3a0f7f4", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072750527700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9a91c8d-0528-41f4-acb4-e99d799f6dfd", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072750731500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3b74b7b-3806-4af1-bd8e-a57e2c66e283", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072751548800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d36e85b9-5099-46ac-a603-d31d03c6dc08", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072751688600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ef3e451-bed0-48b7-9cef-5ba452b78fc7", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072751794100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e38d5b12-1f65-491d-8d23-f48e7b31c795", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072751964700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5882c938-5d64-447c-9958-aa4500c86b5b", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072752039900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a67fa816-1bd0-476e-9a2b-8bc53f8a7b73", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072753255100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dc44146-80f0-4ba9-9107-dda766cc5f63", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072753658400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfe99af8-a274-40ac-92ab-59893c56ce77", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072753744500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "739cb6d8-49a0-4826-86ea-60602d97853a", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072753785400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "732c451d-d266-4e85-a594-d71e9df6d54d", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072753814900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bdb2116-9bc1-4f9a-9f78-ec24fa2d54b4", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072753851100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb53f765-b917-48e6-9ab2-64530b43d06f", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072754878500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08ccced1-0edb-4b7e-9a2c-c7dad6b6e8aa", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072756086000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35c28dfd-390b-4f47-bd7c-f84d29d825cd", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072756353600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8fd64f1-90fc-48fe-bd55-98351f4fd4fb", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072756424500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "541e20b0-8678-4309-8c45-153a6e4ac15e", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072756470800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db7ac19e-7a0c-4766-af2a-1416cf10e372", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072756506000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85857cad-8db1-4324-92bd-224a40518545", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072756538600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1949605-215d-4332-a218-2abefc9d237f", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072756569400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b242af1e-ac84-481f-813a-b47ed137f209", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072759577000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a597c8f2-f84d-41d1-b50b-209755d50020", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072760225500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "546f4981-4d1b-4a88-a42b-64290d2807fa", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072760594200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0809b41f-2246-4c56-87b8-00584a32c61c", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072760806700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f752cdb-7a2b-4fe5-8f93-dad3fdff5f41", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072760994600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f30f0e2-a3dc-4df3-833a-611483eeb44a", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072761611400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "189e78dc-a679-426c-8259-147ee62bec4a", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072761745900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43545816-c285-4ff4-ad2f-946e2017cac3", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072761945900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5d9768e-1c6e-4497-a79f-39bb3d090269", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072762495900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60da4bba-24ee-490a-8e0e-75f791cd663f", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072763504600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfaaa0c1-63cd-4719-9b45-c7eaa3bffba7", "name": "Incremental task entry:default@CompileArkTS post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072764232100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abd50363-0395-43a6-8c7f-021f22fb3939", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072765983900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c3d2a69-5da3-4823-b905-968642d0038e", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072766555200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a31a056-f20d-4276-b3bd-a476d5a263e9", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072766903800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c3ab355-5342-4d60-8745-5e3aa0988cb1", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072767167300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3157e7cc-9cc9-4720-a39a-cadd1272a8dd", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072767363500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ae884ee-ee4c-4005-b223-e68432dc95b7", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072768037600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fbe1a6f-bb67-40ac-8499-3b78c157f08c", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072768743000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94d66299-be1e-49f6-a519-8c85ee2944a2", "name": "Incremental task entry:default@BuildJS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072768976600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a07bfc6-30ef-412f-9dc1-0c2fe704a2d7", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072769034900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a1116e4-bb1f-4514-bc11-4bbdd6433d2f", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072769071500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2af25f9-6fa5-4691-8ed1-2c010822c8eb", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072771048900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3726812b-e7ee-40cf-806b-4c800d3e6a1a", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072772492700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bf3ddf3-36b7-463f-ad3d-0b3e1cf7113d", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072772896200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "033f1986-e30d-4fb5-a6a8-b563eb42823c", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072779111800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af33b9d7-463f-48a7-b8cb-21a5fdc7d135", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072779569100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cd11bad-1566-4ef9-8417-5352acc5c1f5", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072779817400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "123aefc9-b7a7-451f-bed5-26e72488437e", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072780034900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ffcf4d4-3579-4efc-955d-24f4f9bd6a62", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072780095200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e400614-19ff-48ff-bab3-236b9e6e5b58", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072780262200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ff9e620-1c2b-4702-8b95-754391c23d4e", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072780468500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9097e611-7716-4067-98a4-fcdf36404a34", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072781645500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bf10379-119d-4ded-b63d-ce32a619bd5c", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072782328200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4925ee9-0edb-4ac7-84f0-8bcead604095", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072782947300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3a06fa3-3b4e-4ea1-ac39-6b08179b557e", "name": "Incremental task entry:default@PackageHap post-execution cost:15 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072783606200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b278bcc-5fdd-4d49-9bee-1bfd7363fe42", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.cer cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072784141900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b86ccc03-5f23-400f-bddb-db9ab9983d8c", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p7b cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072784884300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bd88d73-fb9e-4e29-a795-e0599e798075", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p12 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072785473000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7437da51-1af3-4fb1-be7f-c04f5f8ff304", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072786124800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c4c7f5e-f793-4888-ab0f-2f8962ec6279", "name": "Update task entry:default@SignHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072786315100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e09eba3-3300-4d7c-8840-f0a7fae37435", "name": "Incremental task entry:default@SignHap post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072786977500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef6a0844-a9e4-4ee8-baa6-7b12cc8df1ae", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072790340600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "840b3a87-4852-44ec-b603-aef3c5324854", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072790664700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "405a2caf-559a-4157-bf76-df0feef69fd0", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072791125400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42313419-e6d3-4c24-b997-9edba7246bb8", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 224072791370400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}