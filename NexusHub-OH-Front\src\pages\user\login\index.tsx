import { Footer } from '@/components';
import { sendEmailVerifyCode as getFake<PERSON><PERSON>tcha } from '@/services/user';
import {
  LockOutlined,
  MailOutlined,
  UserOutlined,
  GithubOutlined,
  WechatOutlined,
  QqOutlined,
} from '@ant-design/icons';
import {
  LoginForm,
  ProFormCaptcha,
  ProFormCheckbox,
  ProFormText,
} from '@ant-design/pro-components';
import { FormattedMessage, Helmet, SelectLang, useIntl, useModel, history } from '@umijs/max';
import { Alert, message, Tabs } from 'antd';
import { createStyles } from 'antd-style';
import React, { useState, useEffect } from 'react';
import { flushSync } from 'react-dom';
import Settings from '../../../../config/defaultSettings';
import { login as userLogin, emailLogin } from '@/services/user';
import HybridLogin from '@/components/HybridLogin';
import { useLogtoAuth } from '@/hooks/useLogtoAuth';

// 定义登录参数类型
interface LoginParams {
  username: string;
  password: string;
  autoLogin?: boolean;
  email?: string;
  captcha?: string;
}

// 定义登录结果类型
interface LoginResult {
  status?: string;
  type?: string;
}

const useStyles = createStyles(({ token }) => {
  return {
    action: {
      marginLeft: '8px',
      color: 'rgba(0, 0, 0, 0.2)',
      fontSize: '24px',
      verticalAlign: 'middle',
      cursor: 'pointer',
      transition: 'color 0.3s',
      '&:hover': {
        color: token.colorPrimaryActive,
      },
    },
    lang: {
      width: 42,
      height: 42,
      lineHeight: '42px',
      position: 'fixed',
      right: 16,
      borderRadius: token.borderRadius,
      ':hover': {
        backgroundColor: token.colorBgTextHover,
      },
    },
    container: {
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      overflow: 'auto',
      backgroundImage:
        "url('https://images.unsplash.com/photo-1579548122080-c35fd6820ecb?q=80&w=2070&auto=format&fit=crop')",
      backgroundSize: 'cover',
      backgroundPosition: 'center',
    },
    content: {
      flex: '1',
      padding: '32px 0',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
    },
    form: {
      padding: '32px 24px',
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderRadius: '8px',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    },
    header: {
      display: 'flex',
      alignItems: 'center',
      marginBottom: '24px',
    },
    logo: {
      height: '44px',
      marginRight: '16px',
    },
    title: {
      fontSize: '33px',
      fontWeight: 'bold',
      color: token.colorPrimary,
    },
    desc: {
      fontSize: '14px',
      color: token.colorTextSecondary,
      marginTop: '12px',
    },
  };
});

const ActionIcons = () => {
  const { styles } = useStyles();

  return (
    <>
      <GithubOutlined key="GithubOutlined" className={styles.action} onClick={() => message.info('暂不支持第三方账号登录')} />
      <WechatOutlined key="WechatOutlined" className={styles.action} onClick={() => message.info('暂不支持第三方账号登录')} />
      <QqOutlined key="QqOutlined" className={styles.action} onClick={() => message.info('暂不支持第三方账号登录')} />
    </>
  );
};

const Lang = () => {
  const { styles } = useStyles();

  return (
    <div className={styles.lang} data-lang>
      {SelectLang && <SelectLang />}
    </div>
  );
};

const LoginMessage: React.FC<{
  content: string;
}> = ({ content }) => {
  return (
    <Alert
      style={{
        marginBottom: 24,
      }}
      message={content}
      type="error"
      showIcon
    />
  );
};

const Login: React.FC = () => {
  const [userLoginState, setUserLoginState] = useState<LoginResult>({});
  const [type, setType] = useState<string>('account');
  const { initialState, setInitialState } = useModel('@@initialState');
  const { styles } = useStyles();
  const intl = useIntl();
  
  // 只在启用Logto时才使用useLogtoAuth Hook
  const logtoAuth = initialState?.useLogto ? useLogtoAuth() : { isAuthenticated: false, isLoading: false };
  const { isAuthenticated, isLoading: logtoLoading } = logtoAuth;

  // 如果启用了Logto且用户已认证，自动跳转
  useEffect(() => {
    if (initialState?.useLogto && isAuthenticated && !logtoLoading) {
      const urlParams = new URL(window.location.href).searchParams;
      history.push(urlParams.get('redirect') || '/');
    }
  }, [isAuthenticated, logtoLoading, initialState?.useLogto]);

  const fetchUserInfo = async () => {
    const userInfo = await initialState?.fetchUserInfo?.();
    if (userInfo) {
      flushSync(() => {
        setInitialState((s) => ({
          ...s,
          currentUser: userInfo,
        }));
      });
    }
  };

  const handleSubmit = async (values: LoginParams) => {
    try {
      // 登录
      const loginParams = {
        username: values.username,
        password: values.password
      };
      
      const response = await userLogin(loginParams);
      console.log('登录响应:', response);
      
      if (response) {
        // 获取token，考虑嵌套结构
        const token = response.data?.token || response.token;
        console.log('处理后的token:', token);
        
        if (token) {
          // 存储token
          localStorage.setItem('token', token);
          
          const defaultLoginSuccessMessage = '登录成功！';
          message.success(defaultLoginSuccessMessage);
          await fetchUserInfo();
          const urlParams = new URL(window.location.href).searchParams;
          history.push(urlParams.get('redirect') || '/');
          return;
        } else {
          message.error('登录响应中未包含token');
        }
      }
    } catch (error) {
      console.log(error);
      message.error('登录失败，请重试！');
    }
  };
  const { status, type: loginType } = userLoginState;

  return (
    <div className={styles.container}>
      <Helmet>
        <title>
          {intl.formatMessage({
            id: 'menu.login',
            defaultMessage: '登录页',
          })}
          - {Settings.title}
        </title>
      </Helmet>
      <Lang />
      <div className={styles.content}>
        <div className={styles.form}>
          <div className={styles.header}>
            <img alt="logo" className={styles.logo} src="/logo.svg" />
            <div>
              <h1 className={styles.title}>NexusHub</h1>
              <p className={styles.desc}>
                <FormattedMessage id="pages.login.appDesc" defaultMessage="一站式应用管理与分发平台" />
              </p>
            </div>
          </div>
          <LoginForm
            contentStyle={{
              minWidth: 280,
              maxWidth: '75vw',
            }}
            initialValues={{
              autoLogin: true,
            }}
            actions={[
              <FormattedMessage
                key="loginWith"
                id="pages.login.loginWith"
                defaultMessage="其他登录方式"
              />,
              <ActionIcons key="icons" />,
            ]}
            onFinish={async (values) => {
              await handleSubmit(values as LoginParams);
            }}
          >
            <Tabs
              activeKey={type}
              onChange={setType}
              centered
              items={[
                {
                  key: 'account',
                  label: intl.formatMessage({
                    id: 'pages.login.accountLogin.tab',
                    defaultMessage: '账户密码登录',
                  }),
                },
                {
                  key: 'email',
                  label: intl.formatMessage({
                    id: 'pages.login.emailLogin.tab',
                    defaultMessage: '邮箱登录',
                  }),
                },
              ]}
            />

            {status === 'error' && loginType === 'account' && (
              <LoginMessage
                content={intl.formatMessage({
                  id: 'pages.login.accountLogin.errorMessage',
                  defaultMessage: '账户或密码错误(admin/ant.design)',
                })}
              />
            )}
            {type === 'account' && (
              <>
                <ProFormText
                  name="username"
                  fieldProps={{
                    size: 'large',
                    prefix: <UserOutlined className={'prefixIcon'} />,
                  }}
                  placeholder={intl.formatMessage({
                    id: 'pages.login.username.placeholder',
                    defaultMessage: '用户名: admin 或 user',
                  })}
                  rules={[
                    {
                      required: true,
                      message: (
                        <FormattedMessage
                          id="pages.login.username.required"
                          defaultMessage="请输入用户名!"
                        />
                      ),
                    },
                  ]}
                />
                <ProFormText.Password
                  name="password"
                  fieldProps={{
                    size: 'large',
                    prefix: <LockOutlined className={'prefixIcon'} />,
                  }}
                  placeholder={intl.formatMessage({
                    id: 'pages.login.password.placeholder',
                    defaultMessage: '密码: ant.design',
                  })}
                  rules={[
                    {
                      required: true,
                      message: (
                        <FormattedMessage
                          id="pages.login.password.required"
                          defaultMessage="请输入密码！"
                        />
                      ),
                    },
                  ]}
                />
              </>
            )}

            {status === 'error' && loginType === 'email' && 
              <LoginMessage 
                content={intl.formatMessage({
                  id: 'pages.login.phoneLogin.errorMessage',
                  defaultMessage: '验证码错误',
                })}
              />
            }
            {type === 'email' && (
              <>
                <ProFormText
                  fieldProps={{
                    size: 'large',
                    prefix: <MailOutlined className={'prefixIcon'} />,
                  }}
                  name="email"
                  placeholder={intl.formatMessage({
                    id: 'pages.login.email.placeholder',
                    defaultMessage: '邮箱地址',
                  })}
                  rules={[
                    {
                      required: true,
                      message: (
                        <FormattedMessage
                          id="pages.login.email.required"
                          defaultMessage="请输入邮箱地址！"
                        />
                      ),
                    },
                    {
                      type: 'email',
                      message: (
                        <FormattedMessage
                          id="pages.login.email.invalid"
                          defaultMessage="邮箱地址格式错误！"
                        />
                      ),
                    },
                  ]}
                />
                <ProFormCaptcha
                  fieldProps={{
                    size: 'large',
                    prefix: <LockOutlined className={'prefixIcon'} />,
                  }}
                  captchaProps={{
                    size: 'large',
                  }}
                  placeholder={intl.formatMessage({
                    id: 'pages.login.captcha.placeholder',
                    defaultMessage: '请输入验证码',
                  })}
                  captchaTextRender={(timing, count) => {
                    if (timing) {
                      return `${count} ${intl.formatMessage({
                        id: 'pages.getCaptchaSecondText',
                        defaultMessage: '秒后重新获取',
                      })}`;
                    }
                    return intl.formatMessage({
                      id: 'pages.login.emailLogin.getVerificationCode',
                      defaultMessage: '获取验证码',
                    });
                  }}
                  name="captcha"
                  rules={[
                    {
                      required: true,
                      message: (
                        <FormattedMessage
                          id="pages.login.captcha.required"
                          defaultMessage="请输入验证码！"
                        />
                      ),
                    },
                  ]}
                  onGetCaptcha={async (email) => {
                    const result = await getFakeCaptcha({
                      email,
                    });
                    if (!result) {
                      return;
                    }
                    message.success('获取验证码成功！验证码为：1234');
                  }}
                />
              </>
            )}
            <div
              style={{
                marginBottom: 24,
              }}
            >
              <ProFormCheckbox noStyle name="autoLogin">
                <FormattedMessage id="pages.login.rememberMe" defaultMessage="自动登录" />
              </ProFormCheckbox>
              <div style={{ float: 'right' }}>
                <a style={{ marginRight: 12 }}>
                  <FormattedMessage id="pages.login.forgotPassword" defaultMessage="忘记密码" />
                </a>
                <a href="/user/register">
                  <FormattedMessage id="pages.login.registerAccount" defaultMessage="注册账号" />
                </a>
              </div>
            </div>
          </LoginForm>
          
          {/* 混合登录组件 */}
          <HybridLogin />
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Login;
