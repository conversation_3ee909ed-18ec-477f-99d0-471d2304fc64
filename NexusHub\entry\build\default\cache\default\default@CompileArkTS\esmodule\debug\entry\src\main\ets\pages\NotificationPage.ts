if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface NotificationPage_Params {
    notifications?: NotificationModel[];
    loadingState?: LoadingState;
    currentPage?: number;
    hasMore?: boolean;
    isLoadingMore?: boolean;
    unreadCount?: number;
    showSettings?: boolean;
    deviceUtils?;
    apiService?;
    pageSize?: number;
}
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { DeviceUtils } from "@normalized:N&&&entry/src/main/ets/utils/DeviceUtils&";
import { ApiService } from "@normalized:N&&&entry/src/main/ets/services/ApiService&";
import { LoadingView, LoadingState } from "@normalized:N&&&entry/src/main/ets/components/LoadingView&";
import preferences from "@ohos:data.preferences";
import hilog from "@ohos:hilog";
// getContext is deprecated, use this.getUIContext().getHostContext() instead
/**
 * 通知数据模型
 */
interface NotificationModel {
    id: number;
    title: string;
    content: string;
    type: string;
    is_read: boolean;
    created_at: string;
    data?: Record<string, Object>;
}
/**
 * 分页信息接口
 */
interface PaginationInfo {
    hasNext: boolean;
    page: number;
    total: number;
}
/**
 * 通知列表响应数据模型
 */
interface NotificationListData {
    notifications: NotificationModel[];
    pagination: PaginationInfo;
}
/**
 * 未读通知数量数据模型
 */
interface UnreadCountData {
    count: number;
}
class NotificationPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__notifications = new ObservedPropertyObjectPU([], this, "notifications");
        this.__loadingState = new ObservedPropertySimplePU(LoadingState.LOADING, this, "loadingState");
        this.__currentPage = new ObservedPropertySimplePU(1, this, "currentPage");
        this.__hasMore = new ObservedPropertySimplePU(true, this, "hasMore");
        this.__isLoadingMore = new ObservedPropertySimplePU(false, this, "isLoadingMore");
        this.__unreadCount = new ObservedPropertySimplePU(0, this, "unreadCount");
        this.__showSettings = new ObservedPropertySimplePU(false, this, "showSettings");
        this.deviceUtils = DeviceUtils.getInstance();
        this.apiService = ApiService.getInstance();
        this.pageSize = 20;
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: NotificationPage_Params) {
        if (params.notifications !== undefined) {
            this.notifications = params.notifications;
        }
        if (params.loadingState !== undefined) {
            this.loadingState = params.loadingState;
        }
        if (params.currentPage !== undefined) {
            this.currentPage = params.currentPage;
        }
        if (params.hasMore !== undefined) {
            this.hasMore = params.hasMore;
        }
        if (params.isLoadingMore !== undefined) {
            this.isLoadingMore = params.isLoadingMore;
        }
        if (params.unreadCount !== undefined) {
            this.unreadCount = params.unreadCount;
        }
        if (params.showSettings !== undefined) {
            this.showSettings = params.showSettings;
        }
        if (params.deviceUtils !== undefined) {
            this.deviceUtils = params.deviceUtils;
        }
        if (params.apiService !== undefined) {
            this.apiService = params.apiService;
        }
        if (params.pageSize !== undefined) {
            this.pageSize = params.pageSize;
        }
    }
    updateStateVars(params: NotificationPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__notifications.purgeDependencyOnElmtId(rmElmtId);
        this.__loadingState.purgeDependencyOnElmtId(rmElmtId);
        this.__currentPage.purgeDependencyOnElmtId(rmElmtId);
        this.__hasMore.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoadingMore.purgeDependencyOnElmtId(rmElmtId);
        this.__unreadCount.purgeDependencyOnElmtId(rmElmtId);
        this.__showSettings.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__notifications.aboutToBeDeleted();
        this.__loadingState.aboutToBeDeleted();
        this.__currentPage.aboutToBeDeleted();
        this.__hasMore.aboutToBeDeleted();
        this.__isLoadingMore.aboutToBeDeleted();
        this.__unreadCount.aboutToBeDeleted();
        this.__showSettings.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __notifications: ObservedPropertyObjectPU<NotificationModel[]>;
    get notifications() {
        return this.__notifications.get();
    }
    set notifications(newValue: NotificationModel[]) {
        this.__notifications.set(newValue);
    }
    private __loadingState: ObservedPropertySimplePU<LoadingState>;
    get loadingState() {
        return this.__loadingState.get();
    }
    set loadingState(newValue: LoadingState) {
        this.__loadingState.set(newValue);
    }
    private __currentPage: ObservedPropertySimplePU<number>;
    get currentPage() {
        return this.__currentPage.get();
    }
    set currentPage(newValue: number) {
        this.__currentPage.set(newValue);
    }
    private __hasMore: ObservedPropertySimplePU<boolean>;
    get hasMore() {
        return this.__hasMore.get();
    }
    set hasMore(newValue: boolean) {
        this.__hasMore.set(newValue);
    }
    private __isLoadingMore: ObservedPropertySimplePU<boolean>;
    get isLoadingMore() {
        return this.__isLoadingMore.get();
    }
    set isLoadingMore(newValue: boolean) {
        this.__isLoadingMore.set(newValue);
    }
    private __unreadCount: ObservedPropertySimplePU<number>;
    get unreadCount() {
        return this.__unreadCount.get();
    }
    set unreadCount(newValue: number) {
        this.__unreadCount.set(newValue);
    }
    private __showSettings: ObservedPropertySimplePU<boolean>;
    get showSettings() {
        return this.__showSettings.get();
    }
    set showSettings(newValue: boolean) {
        this.__showSettings.set(newValue);
    }
    private deviceUtils;
    private apiService;
    private pageSize: number;
    aboutToAppear() {
        this.checkAndSetAuthToken().then(() => {
            this.loadNotifications();
            this.loadUnreadCount();
        });
    }
    /**
     * 检查并设置认证token
     */
    private async checkAndSetAuthToken(): Promise<void> {
        try {
            const context = this.getUIContext().getHostContext();
            const dataPreferences = preferences.getPreferencesSync(context, { name: 'user_data' });
            const token: preferences.ValueType = dataPreferences.getSync('token', '');
            if (token && typeof token === 'string' && token.length > 0) {
                this.apiService.setAuthToken(token);
            }
            else {
                // 没有token，跳转到登录页面
                this.getUIContext().getRouter().pushUrl({
                    url: 'pages/LoginPage'
                });
            }
        }
        catch (error) {
            hilog.error(0x0000, 'NotificationPage', '检查登录状态失败: %{public}s', JSON.stringify(error));
            this.getUIContext().getRouter().pushUrl({
                url: 'pages/LoginPage'
            });
        }
    }
    /**
     * 加载通知列表
     */
    private async loadNotifications(page: number = 1) {
        try {
            if (page === 1) {
                this.loadingState = LoadingState.LOADING;
            }
            else {
                this.isLoadingMore = true;
            }
            const response = await this.apiService.getNotifications(page, this.pageSize);
            if (response.code === 200 && response.data) {
                let newNotifications: NotificationModel[] = [];
                // 明确的类型检查和转换
                if (response.data && typeof response.data === 'object') {
                    const data = response.data as NotificationListData;
                    if (data.notifications && Array.isArray(data.notifications)) {
                        newNotifications = data.notifications;
                    }
                    else if (Array.isArray(response.data)) {
                        newNotifications = response.data as NotificationModel[];
                    }
                }
                if (page === 1) {
                    this.notifications = newNotifications;
                }
                else {
                    const updatedNotifications: NotificationModel[] = [];
                    this.notifications.forEach(item => updatedNotifications.push(item));
                    newNotifications.forEach(item => updatedNotifications.push(item));
                    this.notifications = updatedNotifications;
                }
                // 明确的分页信息获取
                let hasNext = false;
                if (response.data && typeof response.data === 'object') {
                    const data = response.data as NotificationListData;
                    if (data.pagination && typeof data.pagination === 'object') {
                        hasNext = data.pagination.hasNext || false;
                    }
                }
                this.hasMore = hasNext;
                this.currentPage = page;
                this.loadingState = LoadingState.SUCCESS;
            }
            else {
                this.loadingState = LoadingState.ERROR;
            }
        }
        catch (error) {
            hilog.error(0x0000, 'NotificationPage', '加载通知失败: %{public}s', JSON.stringify(error));
            this.loadingState = LoadingState.ERROR;
        }
        finally {
            this.isLoadingMore = false;
        }
    }
    /**
     * 加载未读通知数量
     */
    private async loadUnreadCount() {
        try {
            const response = await this.apiService.getUnreadNotificationCount();
            if (response.code === 200 && response.data) {
                this.unreadCount = (response.data as UnreadCountData).count || 0;
            }
        }
        catch (error) {
            hilog.error(0x0000, 'NotificationPage', '加载未读通知数量失败: %{public}s', JSON.stringify(error));
        }
    }
    /**
     * 标记通知为已读
     */
    private async markAsRead(notification: NotificationModel) {
        if (notification.is_read) {
            return;
        }
        try {
            await this.apiService.markNotificationAsRead(notification.id);
            // 更新本地状态
            const index = this.notifications.findIndex(n => n.id === notification.id);
            if (index !== -1) {
                this.notifications[index].is_read = true;
                this.unreadCount = Math.max(0, this.unreadCount - 1);
            }
        }
        catch (error) {
            hilog.error(0x0000, 'NotificationPage', '标记通知已读失败: %{public}s', JSON.stringify(error));
        }
    }
    /**
     * 删除通知
     */
    private async deleteNotification(notificationId: number) {
        try {
            await this.apiService.deleteNotification(notificationId);
            // 从列表中移除
            const filteredNotifications: NotificationModel[] = [];
            this.notifications.forEach(n => {
                if (n.id !== notificationId) {
                    filteredNotifications.push(n);
                }
            });
            this.notifications = filteredNotifications;
        }
        catch (error) {
            hilog.error(0x0000, 'NotificationPage', '删除通知失败: %{public}s', JSON.stringify(error));
        }
    }
    /**
     * 处理通知点击
     */
    private handleNotificationClick(notification: NotificationModel) {
        // 标记为已读
        this.markAsRead(notification);
        // 根据通知类型进行跳转
        if (notification.data && notification.data['app_id']) {
            this.getUIContext().getRouter().pushUrl({
                url: 'pages/AppDetailPage',
                params: {
                    appId: notification.data['app_id']
                }
            });
        }
    }
    /**
     * 格式化时间
     */
    private formatTime(timeStr: string): string {
        const time = new Date(timeStr);
        const now = new Date();
        const diff = now.getTime() - time.getTime();
        const minutes = Math.floor(diff / (1000 * 60));
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        if (minutes < 1) {
            return '刚刚';
        }
        else if (minutes < 60) {
            return `${minutes}分钟前`;
        }
        else if (hours < 24) {
            return `${hours}小时前`;
        }
        else if (days < 7) {
            return `${days}天前`;
        }
        else {
            return time.toLocaleDateString();
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor(Constants.COLORS.BACKGROUND);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 标题栏
            Row.create();
            // 标题栏
            Row.width('100%');
            // 标题栏
            Row.height(56);
            // 标题栏
            Row.padding({ left: 16, right: 16 });
            // 标题栏
            Row.backgroundColor(Constants.COLORS.WHITE);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.width(40);
            Button.height(40);
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                this.getUIContext().getRouter().back();
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777246, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(24);
            Image.height(24);
            Image.fillColor(Constants.COLORS.TEXT_PRIMARY);
        }, Image);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('通知');
            Text.fontSize(Constants.FONT_SIZE.LARGE);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.width(40);
            Button.height(40);
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                this.showSettings = true;
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777271, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(24);
            Image.height(24);
            Image.fillColor(Constants.COLORS.TEXT_PRIMARY);
        }, Image);
        Button.pop();
        // 标题栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 内容区域
            if (this.loadingState === LoadingState.LOADING) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/NotificationPage.ets", line: 297, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {};
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {});
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else if (this.loadingState === LoadingState.ERROR) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.layoutWeight(1);
                        Column.justifyContent(FlexAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Image.create({ "id": 16777254, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                        Image.width(64);
                        Image.height(64);
                        Image.fillColor(Constants.COLORS.TEXT_HINT);
                        Image.margin({ bottom: 16 });
                    }, Image);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('加载失败');
                        Text.fontSize(Constants.FONT_SIZE.MEDIUM);
                        Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                        Text.margin({ bottom: 16 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('重试');
                        Button.fontSize(Constants.FONT_SIZE.NORMAL);
                        Button.fontColor(Constants.COLORS.WHITE);
                        Button.backgroundColor(Constants.COLORS.PRIMARY);
                        Button.borderRadius(8);
                        Button.padding({ left: 24, right: 24, top: 8, bottom: 8 });
                        Button.onClick(() => {
                            this.loadNotifications();
                        });
                    }, Button);
                    Button.pop();
                    Column.pop();
                });
            }
            else if (!this.notifications || this.notifications.length === 0) {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.layoutWeight(1);
                        Column.justifyContent(FlexAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Image.create({ "id": 16777267, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                        Image.width(120);
                        Image.height(120);
                        Image.fillColor(Constants.COLORS.TEXT_HINT);
                        Image.margin({ bottom: 16 });
                    }, Image);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无通知');
                        Text.fontSize(Constants.FONT_SIZE.MEDIUM);
                        Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(3, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        List.create();
                        List.layoutWeight(1);
                        List.divider({
                            strokeWidth: 1,
                            color: Constants.COLORS.BORDER,
                            startMargin: 16,
                            endMargin: 16
                        });
                    }, List);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const notification = _item;
                            {
                                const itemCreation = (elmtId, isInitialRender) => {
                                    ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                    itemCreation2(elmtId, isInitialRender);
                                    if (!isInitialRender) {
                                        ListItem.pop();
                                    }
                                    ViewStackProcessor.StopGetAccessRecording();
                                };
                                const itemCreation2 = (elmtId, isInitialRender) => {
                                    ListItem.create(deepRenderFunction, true);
                                    ListItem.onClick(() => {
                                        this.handleNotificationClick(notification);
                                    });
                                };
                                const deepRenderFunction = (elmtId, isInitialRender) => {
                                    itemCreation(elmtId, isInitialRender);
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        Row.create();
                                        Row.width('100%');
                                        Row.padding(16);
                                        Row.backgroundColor(notification.is_read ? Constants.COLORS.WHITE : Constants.COLORS.BACKGROUND_LIGHT);
                                    }, Row);
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        Column.create();
                                        Column.layoutWeight(1);
                                        Column.alignItems(HorizontalAlign.Start);
                                    }, Column);
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        Row.create();
                                        Row.width('100%');
                                        Row.margin({ bottom: 4 });
                                    }, Row);
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        Text.create(notification.title);
                                        Text.fontSize(Constants.FONT_SIZE.NORMAL);
                                        Text.fontWeight(notification.is_read ? FontWeight.Normal : FontWeight.Medium);
                                        Text.fontColor(notification.is_read ? Constants.COLORS.TEXT_SECONDARY : Constants.COLORS.TEXT_PRIMARY);
                                        Text.layoutWeight(1);
                                        Text.maxLines(1);
                                        Text.textOverflow({ overflow: TextOverflow.Ellipsis });
                                    }, Text);
                                    Text.pop();
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        If.create();
                                        if (!notification.is_read) {
                                            this.ifElseBranchUpdateFunction(0, () => {
                                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                    Circle.create();
                                                    Circle.width(8);
                                                    Circle.height(8);
                                                    Circle.fill(Constants.COLORS.PRIMARY);
                                                    Circle.margin({ left: 8 });
                                                }, Circle);
                                            });
                                        }
                                        else {
                                            this.ifElseBranchUpdateFunction(1, () => {
                                            });
                                        }
                                    }, If);
                                    If.pop();
                                    Row.pop();
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        Text.create(notification.content);
                                        Text.fontSize(Constants.FONT_SIZE.SMALL);
                                        Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                                        Text.maxLines(2);
                                        Text.textOverflow({ overflow: TextOverflow.Ellipsis });
                                        Text.margin({ bottom: 8 });
                                    }, Text);
                                    Text.pop();
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        Text.create(this.formatTime(notification.created_at));
                                        Text.fontSize(Constants.FONT_SIZE.SMALL);
                                        Text.fontColor(Constants.COLORS.TEXT_HINT);
                                    }, Text);
                                    Text.pop();
                                    Column.pop();
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        Button.createWithChild();
                                        Button.width(32);
                                        Button.height(32);
                                        Button.backgroundColor(Color.Transparent);
                                        Button.onClick(() => {
                                            this.deleteNotification(notification.id);
                                        });
                                    }, Button);
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        Image.create({ "id": 16777251, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                                        Image.width(20);
                                        Image.height(20);
                                        Image.fillColor(Constants.COLORS.TEXT_HINT);
                                    }, Image);
                                    Button.pop();
                                    Row.pop();
                                    ListItem.pop();
                                };
                                this.observeComponentCreation2(itemCreation2, ListItem);
                                ListItem.pop();
                            }
                        };
                        this.forEachUpdateFunction(elmtId, this.notifications, forEachItemGenFunction);
                    }, ForEach);
                    ForEach.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.hasMore) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                {
                                    const itemCreation = (elmtId, isInitialRender) => {
                                        ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                        itemCreation2(elmtId, isInitialRender);
                                        if (!isInitialRender) {
                                            ListItem.pop();
                                        }
                                        ViewStackProcessor.StopGetAccessRecording();
                                    };
                                    const itemCreation2 = (elmtId, isInitialRender) => {
                                        ListItem.create(deepRenderFunction, true);
                                        ListItem.onClick(() => {
                                            if (!this.isLoadingMore) {
                                                this.loadNotifications(this.currentPage + 1);
                                            }
                                        });
                                    };
                                    const deepRenderFunction = (elmtId, isInitialRender) => {
                                        itemCreation(elmtId, isInitialRender);
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            If.create();
                                            if (this.isLoadingMore) {
                                                this.ifElseBranchUpdateFunction(0, () => {
                                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                        Row.create();
                                                        Row.justifyContent(FlexAlign.Center);
                                                        Row.padding(16);
                                                    }, Row);
                                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                        LoadingProgress.create();
                                                        LoadingProgress.width(20);
                                                        LoadingProgress.height(20);
                                                        LoadingProgress.margin({ right: 8 });
                                                    }, LoadingProgress);
                                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                        Text.create('加载中...');
                                                        Text.fontSize(Constants.FONT_SIZE.SMALL);
                                                        Text.fontColor(Constants.COLORS.TEXT_HINT);
                                                    }, Text);
                                                    Text.pop();
                                                    Row.pop();
                                                });
                                            }
                                            else {
                                                this.ifElseBranchUpdateFunction(1, () => {
                                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                        Text.create('点击加载更多');
                                                        Text.fontSize(Constants.FONT_SIZE.SMALL);
                                                        Text.fontColor(Constants.COLORS.PRIMARY);
                                                        Text.textAlign(TextAlign.Center);
                                                        Text.padding(16);
                                                    }, Text);
                                                    Text.pop();
                                                });
                                            }
                                        }, If);
                                        If.pop();
                                        ListItem.pop();
                                    };
                                    this.observeComponentCreation2(itemCreation2, ListItem);
                                    ListItem.pop();
                                }
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    List.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "NotificationPage";
    }
}
registerNamedRoute(() => new NotificationPage(undefined, {}), "", { bundleName: "com.example.nexushub", moduleName: "entry", pagePath: "pages/NotificationPage", pageFullPath: "entry/src/main/ets/pages/NotificationPage", integratedHsp: "false", moduleType: "followWithHap" });
