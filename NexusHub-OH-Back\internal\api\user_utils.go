package api

import (
	"nexushub-oh-back/internal/models"

	"github.com/gin-gonic/gin"
)

// 用户信息的上下文键名
const UserContextKey = "user"

// GetUserFromContext 从上下文中获取当前用户信息
func GetUserFromContext(c *gin.Context) (*models.User, bool) {
	userVal, exists := c.Get(UserContextKey)
	if !exists {
		return nil, false
	}

	user, ok := userVal.(*models.User)
	if !ok {
		return nil, false
	}

	return user, true
}

// SetUserToContext 将用户信息设置到上下文中
func SetUserToContext(c *gin.Context, user *models.User) {
	c.Set(UserContextKey, user)
}
