import { Request, Response } from 'express';
import moment from 'moment';
import { parse } from 'url';

// 生成mock数据
const genTags = (count: number) => {
  const tags = [];
  const now = moment().format('YYYY-MM-DD HH:mm:ss');
  
  const baseTags = [
    { name: '热门', color: '#f5222d', description: '热门应用标签', is_active: true },
    { name: '推荐', color: '#fa8c16', description: '推荐应用标签', is_active: true },
    { name: '新品', color: '#fadb14', description: '新上架应用', is_active: true },
    { name: '免费', color: '#52c41a', description: '免费应用', is_active: true },
    { name: '付费', color: '#13c2c2', description: '付费应用', is_active: false },
    { name: '限时', color: '#1890ff', description: '限时优惠应用', is_active: true },
    { name: '精品', color: '#722ed1', description: '精品应用', is_active: true },
    { name: '安全', color: '#eb2f96', description: '安全认证应用', is_active: true },
  ];
  
  for (let i = 0; i < count; i++) {
    const index = i % baseTags.length;
    tags.push({
      id: i + 1,
      name: baseTags[index].name + (i >= baseTags.length ? ` ${Math.floor(i / baseTags.length) + 1}` : ''),
      color: baseTags[index].color,
      description: baseTags[index].description,
      is_active: baseTags[index].is_active,
      created_at: now,
      updated_at: now,
    });
  }
  
  return tags;
};

const tags = genTags(20);

export default {
  'GET tags': (req: Request, res: Response) => {
    const { query } = parse(req.url, true);
    const { include_inactive } = query;
    
    let result = [...tags];
    
    // 如果不包含未激活的标签
    if (include_inactive === 'false') {
      result = result.filter(item => item.is_active);
    }
    
    res.json(result);
  },
  
  'GET tags/:id': (req: Request, res: Response) => {
    const { id } = req.params;
    const tag = tags.find(item => item.id === parseInt(id, 10));
    
    if (tag) {
      // 模拟标签使用的应用数量
      const appCount = Math.floor(Math.random() * 100);
      
      res.json({
        tag,
        app_count: appCount,
      });
    } else {
      res.status(404).json({
        code: 404,
        message: '标签不存在',
      });
    }
  },
  
  'POST tags': (req: Request, res: Response) => {
    const newTag = {
      id: tags.length + 1,
      ...req.body,
      created_at: moment().format('YYYY-MM-DD HH:mm:ss'),
      updated_at: moment().format('YYYY-MM-DD HH:mm:ss'),
    };
    
    tags.push(newTag);
    
    res.json(newTag);
  },
  
  'PUT tags/:id': (req: Request, res: Response) => {
    const { id } = req.params;
    const tagIndex = tags.findIndex(item => item.id === parseInt(id, 10));
    
    if (tagIndex >= 0) {
      const updatedTag = {
        ...tags[tagIndex],
        ...req.body,
        updated_at: moment().format('YYYY-MM-DD HH:mm:ss'),
      };
      
      tags[tagIndex] = updatedTag;
      
      res.json(updatedTag);
    } else {
      res.status(404).json({
        code: 404,
        message: '标签不存在',
      });
    }
  },
  
  'DELETE tags/:id': (req: Request, res: Response) => {
    const { id } = req.params;
    const tagIndex = tags.findIndex(item => item.id === parseInt(id, 10));
    
    if (tagIndex >= 0) {
      tags.splice(tagIndex, 1);
      
      res.json({
        code: 200,
        message: '删除成功',
      });
    } else {
      res.status(404).json({
        code: 404,
        message: '标签不存在',
      });
    }
  },
  
  'GET tags/:id/apps': (req: Request, res: Response) => {
    const { id } = req.params;
    const tag = tags.find(item => item.id === parseInt(id, 10));
    
    if (tag) {
      // 模拟标签关联的应用列表
      const apps = Array.from({ length: Math.floor(Math.random() * 10) + 1 }).map((_, index) => ({
        id: index + 1,
        name: `应用 ${index + 1}`,
        icon: `https://example.com/icon_${index + 1}.png`,
        description: `这是使用 ${tag.name} 标签的应用`,
        developer_id: Math.floor(Math.random() * 10) + 1,
        download_count: Math.floor(Math.random() * 10000),
        created_at: moment().subtract(Math.floor(Math.random() * 30), 'days').format('YYYY-MM-DD HH:mm:ss'),
        updated_at: moment().subtract(Math.floor(Math.random() * 7), 'days').format('YYYY-MM-DD HH:mm:ss'),
      }));
      
      res.json(apps);
    } else {
      res.status(404).json({
        code: 404,
        message: '标签不存在',
      });
    }
  },
  
  'GET apps/:id/tags': (req: Request, res: Response) => {
    const { id } = req.params;
    
    // 模拟应用的标签列表
    const appTags = tags.filter((_, index) => index % 3 === 0).slice(0, 5);
    
    res.json(appTags);
  },
  
  'POST apps/:id/tags': (req: Request, res: Response) => {
    res.json({
      code: 200,
      message: '标签添加成功',
    });
  },
  
  'DELETE apps/:id/tags/:tagId': (req: Request, res: Response) => {
    res.json({
      code: 200,
      message: '标签移除成功',
    });
  },
}; 