/*
 * @Author: xiaoxiaozhou <EMAIL>
 * @Date: 2025-05-01 05:42:20
 * @LastEditors: xiaoxiaozhou <EMAIL>
 * @LastEditTime: 2025-05-01 11:41:54
 * @FilePath: \NexusHub-Front\src\components\RightContent\index.tsx
 * @Description: 
 * 技术生的工作日常
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
import { QuestionCircleOutlined } from '@ant-design/icons';
import { SelectLang as UmiSelectLang } from '@umijs/max';
import { history, useModel } from '@umijs/max';
import { Space } from 'antd';
import React from 'react';
import { AvatarDropdown } from './AvatarDropdown';
import NoticeIcon from '../NoticeIcon';

export type SiderTheme = 'light' | 'dark';

export const SelectLang = () => {
  return <UmiSelectLang />;
};

export const Question = () => {
  return (
    <div
      style={{
        display: 'flex',
        height: 26,
      }}
      onClick={() => {
        history.push('/content/help');
      }}
    >
      <QuestionCircleOutlined />
    </div>
  );
};

// 导出头像下拉菜单组件
export { AvatarDropdown };

// 新增默认RightContent组件
const RightContent: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  
  return (
    <Space size={16}>
      <Question />
      <SelectLang />
      {initialState?.currentUser && <NoticeIcon />}
      <AvatarDropdown>
        <span>
          {initialState?.currentUser?.username}
        </span>
      </AvatarDropdown>
    </Space>
  );
};

export default RightContent;
