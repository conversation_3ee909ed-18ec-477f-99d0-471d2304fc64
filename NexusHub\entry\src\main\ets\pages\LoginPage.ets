import { LoginRequest, RegisterRequest } from '../models/User';
import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';
import { ApiService } from '../services/ApiService';
import { router } from '@kit.ArkUI';
import { preferences } from '@kit.ArkData';
import { promptAction } from '@kit.ArkUI';
// getContext is deprecated, use this.getUIContext().getHostContext() instead
import { hilog } from '@kit.PerformanceAnalysisKit';

interface LocationModel {
  id: number;
  name: string;
  code?: string;
  parent_id?: number;
}

interface LocationPickerParams {
  selectedLocation?: LocationModel;
  locationType?: string;
}

/**
 * 登录注册页面
 */
@Entry
@Component
struct LoginPage {
  @State isLoginMode: boolean = true; // true: 登录模式, false: 注册模式
  @State private username: string = '';
  @State private email: string = '';
  @State private password: string = '';
  @State private confirmPassword: string = ''; @State isLoading: boolean = false;
  @State showPassword: boolean = false;
  @State showConfirmPassword: boolean = false;
  @State agreeTerms: boolean = false;
  @State selectedCountry: string = '';
  @State selectedProvince: string = '';
  @State selectedCity: string = '';
  @State selectedDistrict: string = '';
  @State selectedStreet: string = '';

  private deviceUtils = DeviceUtils.getInstance();
  private apiService = ApiService.getInstance();

  /**
   * 切换登录/注册模式
   */
  private switchMode() {
    this.isLoginMode = !this.isLoginMode;
    this.clearForm();
  }

  /**
   * 清空表单
   */
  private clearForm() {
    this.username = '';
    this.email = '';
    this.password = '';
    this.confirmPassword = '';
    this.showPassword = false;
    this.showConfirmPassword = false;
    this.agreeTerms = false;
    this.selectedCountry = '';
    this.selectedProvince = '';
    this.selectedCity = '';
    this.selectedDistrict = '';
    this.selectedStreet = '';
  }

  /**
   * 验证表单
   */
  private validateForm(): boolean {
    if (!this.username.trim()) {
      this.getUIContext().getPromptAction().showToast({ message: '请输入用户名' });
      return false;
    }

    if (this.username.length < 2) {
      this.getUIContext().getPromptAction().showToast({ message: '用户名长度至少2位' });
      return false;
    }

    if (!this.password.trim()) {
      this.getUIContext().getPromptAction().showToast({ message: '请输入密码' });
      return false;
    }

    if (this.password.length < 6) {
      this.getUIContext().getPromptAction().showToast({ message: '密码长度至少6位' });
      return false;
    }

    if (!this.isLoginMode) {
      if (!this.email.trim()) {
        this.getUIContext().getPromptAction().showToast({ message: '请输入邮箱地址' });
        return false;
      }

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(this.email)) {
        this.getUIContext().getPromptAction().showToast({ message: '请输入有效的邮箱地址' });
        return false;
      }

      if (!this.confirmPassword.trim()) {
        this.getUIContext().getPromptAction().showToast({ message: '请确认密码' });
        return false;
      }

      if (this.password !== this.confirmPassword) {
        this.getUIContext().getPromptAction().showToast({ message: '两次输入的密码不一致' });
        return false;
      }

      if (!this.agreeTerms) {
        this.getUIContext().getPromptAction().showToast({ message: '请同意用户协议和隐私政策' });
        return false;
      }
    }

    return true;
  }

  /**
   * 登录
   */
  private async login() {
    if (!this.validateForm()) {
      return;
    }

    this.isLoading = true;
    try {
      const loginRequest: LoginRequest = {
        username_or_email: this.username.trim(),
        password: this.password
      };

      const response = await this.apiService.login(loginRequest);
      
      if (response.code === 200 && response.data) {
        // 保存登录信息
        const context = this.getUIContext().getHostContext();
        const options: preferences.Options = { name: 'user_data' };
        const dataPreferences = preferences.getPreferencesSync(context, options);
        dataPreferences.putSync('token', response.data.token);
        dataPreferences.putSync('user_id', response.data.user.id.toString());
        dataPreferences.putSync('user_info', JSON.stringify(response.data.user));
        dataPreferences.flush();

        this.getUIContext().getPromptAction().showToast({ message: '登录成功' });
        
        // 使用replaceUrl替换当前页面，确保返回主页面时能触发刷新
        this.getUIContext().getRouter().replaceUrl({ url: 'pages/Index' });
      } else {
        this.getUIContext().getPromptAction().showToast({ message: response.message || '登录失败' });
      }
    } catch (error) {
      hilog.error(0x0000, 'LoginPage', '登录失败: %{public}s', JSON.stringify(error));
      this.getUIContext().getPromptAction().showToast({ message: '网络错误，请稍后重试' });
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 注册
   */
  private async register() {
    if (!this.validateForm()) {
      return;
    }

    this.isLoading = true;
    try {
      const registerRequest: RegisterRequest = {
        email: this.email.trim(),
        password: this.password,
        username: this.username.trim(),
        country: this.selectedCountry || undefined,
        province: this.selectedProvince || undefined,
        city: this.selectedCity || undefined,
        district: this.selectedDistrict || undefined,
        street: this.selectedStreet || undefined
      };

      const response = await this.apiService.register(registerRequest);
      
      if (response.code === 200) {
        this.getUIContext().getPromptAction().showToast({ message: '注册成功' });
        
        // 返回上一页或跳转到首页
        this.getUIContext().getRouter().back();
      } else {
        this.getUIContext().getPromptAction().showToast({ message: response.message || '注册失败' });
      }
    } catch (error) {
      hilog.error(0x0000, 'LoginPage', '注册失败: %{public}s', JSON.stringify(error));
      this.getUIContext().getPromptAction().showToast({ message: '网络错误，请稍后重试' });
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 提交表单
   */
  private submitForm() {
    if (this.isLoginMode) {
      this.login();
    } else {
      this.register();
    }
  }

  /**
   * 忘记密码
   */
  private forgotPassword() {
    this.getUIContext().getRouter().pushUrl({ url: 'pages/ForgotPasswordPage' });
  }

  /**
   * 第三方登录
   */
  private thirdPartyLogin(provider: string): void {
    this.getUIContext().getPromptAction().showToast({ message: `${provider}登录功能开发中` });
  }

  /**
   * 选择地理位置
   */
  private selectLocation(type: string, parentCode?: string): void {
    this.getUIContext().getRouter().pushUrl({
      url: 'pages/LocationPickerPage',
      params: {
        type: type,
        parentCode: parentCode || '',
        title: this.getLocationTitle(type)
      }
    });
  }

  /**
   * 获取地理位置标题
   */
  private getLocationTitle(type: string): string {
    switch (type) {
      case 'country':
        return '选择国家';
      case 'province':
        return '选择省份';
      case 'city':
        return '选择城市';
      case 'district':
        return '选择区县';
      case 'street':
        return '选择街道';
      default:
        return '选择位置';
    }
  }

  /**
   * 页面显示时的回调
   */
  onPageShow() {
    // 处理从LocationPickerPage返回的结果
    const params = this.getUIContext().getRouter().getParams() as LocationPickerParams;
    if (params && params.selectedLocation) {
      const location: LocationModel = params.selectedLocation;
      switch (params.locationType) {
        case 'country':
          this.selectedCountry = location.name;
          // 清空下级选择
          this.selectedProvince = '';
          this.selectedCity = '';
          this.selectedDistrict = '';
          this.selectedStreet = '';
          break;
        case 'province':
          this.selectedProvince = location.name;
          // 清空下级选择
          this.selectedCity = '';
          this.selectedDistrict = '';
          this.selectedStreet = '';
          break;
        case 'city':
          this.selectedCity = location.name;
          // 清空下级选择
          this.selectedDistrict = '';
          this.selectedStreet = '';
          break;
        case 'district':
          this.selectedDistrict = location.name;
          // 清空下级选择
          this.selectedStreet = '';
          break;
        case 'street':
          this.selectedStreet = location.name;
          break;
      }
    }
  }

  /**
   * 输入框组件
   */
  @Builder
  private InputField(placeholder: string, value: string, onValueChange: (value: string) => void, 
                     isPassword: boolean = false, showPasswordToggle: boolean = false, 
                     showPassword: boolean = false, onTogglePassword?: () => void) {
    Row() {
      TextInput({ placeholder: placeholder, text: value })
        .type(isPassword && !showPassword ? InputType.Password : InputType.Normal)
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
        .fontColor(Constants.COLORS.TEXT_PRIMARY)
        .backgroundColor(Constants.COLORS.TRANSPARENT)
        .border({ width: 0 })
        .layoutWeight(1)
        .onChange((value: string) => onValueChange(value))

      if (showPasswordToggle) {
        Text(showPassword ? '👁' : '👁‍🗨')
          .width(20)
          .height(20)
          .fontSize(16)
          .fontColor(Constants.COLORS.TEXT_HINT)
          .textAlign(TextAlign.Center)
          .onClick(() => onTogglePassword?.())
      }
    }
    .width('100%')
    .height(48)
    .padding({ left: '16vp', right: '16vp' })
    .backgroundColor(Constants.COLORS.BACKGROUND_LIGHT)
    .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
    .border({ width: 1, color: Constants.COLORS.BORDER })
  }

  /**
   * 第三方登录按钮
   */
  @Builder
  private ThirdPartyButton(icon: Resource, name: string, color: string, onClick: () => void) {
    Row({ space: 8 }) {
      Text('🔗')
        .width(20)
        .height(20)
        .fontSize(16)
        .fontColor(Constants.COLORS.WHITE)
        .textAlign(TextAlign.Center)

      Text(name)
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
        .fontColor(Constants.COLORS.WHITE)
    }
    .width('100%')
    .height(44)
    .justifyContent(FlexAlign.Center)
    .backgroundColor(color)
    .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
    .onClick(onClick)
  }

  /**
   * 地理位置选择组件
   */
  @Builder
  private LocationSelector() {
    Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.SMALL) }) {
      Text('地理位置（可选）')
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
        .fontColor(Constants.COLORS.TEXT_PRIMARY)
        .fontWeight(FontWeight.Medium)
        .alignSelf(ItemAlign.Start)

      // 国家选择
      this.LocationItem('国家', this.selectedCountry, () => {
        this.selectLocation('country');
      })

      // 省份选择（需要先选择国家）
      if (this.selectedCountry) {
        this.LocationItem('省份', this.selectedProvince, () => {
          this.selectLocation('province', 'country_code'); // 这里需要传入国家代码
        })
      }

      // 城市选择（需要先选择省份）
      if (this.selectedProvince) {
        this.LocationItem('城市', this.selectedCity, () => {
          this.selectLocation('city', 'province_code'); // 这里需要传入省份代码
        })
      }

      // 区县选择（需要先选择城市）
      if (this.selectedCity) {
        this.LocationItem('区县', this.selectedDistrict, () => {
          this.selectLocation('district', 'city_code'); // 这里需要传入城市代码
        })
      }

      // 街道选择（需要先选择区县）
      if (this.selectedDistrict) {
        this.LocationItem('街道', this.selectedStreet, () => {
          this.selectLocation('street', 'district_code'); // 这里需要传入区县代码
        })
      }
    }
    .width('100%')
    .alignItems(HorizontalAlign.Start)
  }

  /**
   * 地理位置选择项
   */
  @Builder
  private LocationItem(label: string, value: string, onClick: () => void) {
    Row() {
      Text(label)
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
        .fontColor(Constants.COLORS.TEXT_PRIMARY)
        .width(60)

      Text(value || `请选择${label}`)
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
        .fontColor(value ? Constants.COLORS.TEXT_PRIMARY : Constants.COLORS.TEXT_HINT)
        .layoutWeight(1)
        .textAlign(TextAlign.Start)

      Text('→')
        .width(16)
        .height(16)
        .fontSize(14)
        .fontColor(Constants.COLORS.TEXT_HINT)
        .textAlign(TextAlign.Center)
    }
    .width('100%')
    .height(48)
    .padding({ left: '16vp', right: '16vp' })
    .backgroundColor(Constants.COLORS.BACKGROUND_LIGHT)
    .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
    .border({ width: 1, color: Constants.COLORS.BORDER })
    .onClick(onClick)
  }

  build() {
    Column() {
      // 顶部导航
      Row() {
        Text('←')
          .width(24)
          .height(24)
          .fontSize(20)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .textAlign(TextAlign.Center)
          .onClick(() => this.getUIContext().getRouter().back())

        Blank()

        Text(this.isLoginMode ? '登录' : '注册')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
          .fontWeight(FontWeight.Bold)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)

        Blank()

        // 占位，保持标题居中
        Column()
          .width(24)
          .height(24)
      }
      .width('100%')
      .height(56)
      .padding({ left: '16vp', right: '16vp' })
      .backgroundColor(Constants.COLORS.WHITE)

      Scroll() {
        Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE) }) {
          // Logo和标题
          Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) }) {
            Image($r('app.media.app_icon'))
              .width(this.deviceUtils.isTablet() ? 120 : 80)
              .height(this.deviceUtils.isTablet() ? 120 : 80)

            Text('NexusHub')
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.EXTRA_LARGE))
              .fontWeight(FontWeight.Bold)
              .fontColor(Constants.COLORS.PRIMARY)

            Text(this.isLoginMode ? '欢迎回来' : '创建新账户')
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
              .fontColor(Constants.COLORS.TEXT_SECONDARY)
          }
          .margin({ top: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.EXTRA_LARGE) })

          // 表单
          Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) }) {
            // 用户名输入
            this.InputField('用户名', this.username, (value) => this.username = value)

            // 邮箱输入（仅注册模式）
            if (!this.isLoginMode) {
              this.InputField('邮箱地址', this.email, (value) => this.email = value)
              
              // 地理位置选择
              this.LocationSelector()
            }

            // 密码输入
            this.InputField('密码', this.password, (value) => this.password = value, 
                           true, true, this.showPassword, () => this.showPassword = !this.showPassword)

            // 确认密码输入（仅注册模式）
            if (!this.isLoginMode) {
              this.InputField('确认密码', this.confirmPassword, (value) => this.confirmPassword = value,
                             true, true, this.showConfirmPassword, () => this.showConfirmPassword = !this.showConfirmPassword)
            }

            // 忘记密码（仅登录模式）
            if (this.isLoginMode) {
              Row() {
                Blank()
                Text('忘记密码？')
                  .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
                  .fontColor(Constants.COLORS.PRIMARY)
                  .onClick(() => this.forgotPassword())
              }
              .width('100%')
            }

            // 用户协议（仅注册模式）
            if (!this.isLoginMode) {
              Row({ space: 8 }) {
                Checkbox({ name: 'agree', group: 'terms' })
                  .select(this.agreeTerms)
                  .selectedColor(Constants.COLORS.PRIMARY)
                  .onChange((value: boolean) => this.agreeTerms = value)

                Row({ space: 4 }) {
                  Text('我已阅读并同意')
                    .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
                    .fontColor(Constants.COLORS.TEXT_SECONDARY)
                  
                  Text('用户协议')
                    .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
                    .fontColor(Constants.COLORS.PRIMARY)
                    .onClick(() => {
                      this.getUIContext().getRouter().pushUrl({ url: 'pages/TermsPage' });
                    })
                  
                  Text('和')
                    .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
                    .fontColor(Constants.COLORS.TEXT_SECONDARY)
                  
                  Text('隐私政策')
                    .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
                    .fontColor(Constants.COLORS.PRIMARY)
                    .onClick(() => {
                      this.getUIContext().getRouter().pushUrl({ url: 'pages/PrivacyPage' });
                    })
                }
                .layoutWeight(1)
              }
              .width('100%')
              .alignItems(VerticalAlign.Top)
            }

            // 登录/注册按钮
            Button(this.isLoginMode ? '登录' : '注册')
              .width('100%')
              .height(48)
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
              .fontWeight(FontWeight.Medium)
              .fontColor(Constants.COLORS.WHITE)
              .backgroundColor(Constants.COLORS.PRIMARY)
              .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
              .enabled(!this.isLoading)
              .opacity(this.isLoading ? 0.6 : 1)
              .onClick(() => this.submitForm())

            if (this.isLoading) {
              LoadingProgress()
                .width(24)
                .height(24)
                .color(Constants.COLORS.PRIMARY)
            }
          }
          .width('100%')
          .padding({ left: '24vp', right: '24vp' })

          // 分割线
          Row({ space: 16 }) {
            Divider()
              .layoutWeight(1)
              .color(Constants.COLORS.BORDER)
            
            Text('或')
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
              .fontColor(Constants.COLORS.TEXT_HINT)
            
            Divider()
              .layoutWeight(1)
              .color(Constants.COLORS.BORDER)
          }
          .width('100%')
          .padding({ left: '24vp', right: '24vp' })

          // 第三方登录
          Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.SMALL) }) {
            this.ThirdPartyButton($r('app.media.ic_share'), '微信登录', '#07C160', () => this.thirdPartyLogin('微信'))
            this.ThirdPartyButton($r('app.media.ic_share'), 'QQ登录', '#1296DB', () => this.thirdPartyLogin('QQ'))
            this.ThirdPartyButton($r('app.media.ic_share'), '微博登录', '#E6162D', () => this.thirdPartyLogin('微博'))
          }
          .width('100%')
          .padding({ left: '24vp', right: '24vp' })

          // 切换登录/注册模式
          Row({ space: 4 }) {
            Text(this.isLoginMode ? '还没有账户？' : '已有账户？')
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
              .fontColor(Constants.COLORS.TEXT_SECONDARY)
            
            Text(this.isLoginMode ? '立即注册' : '立即登录')
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
              .fontColor(Constants.COLORS.PRIMARY)
              .fontWeight(FontWeight.Medium)
              .onClick(() => this.switchMode())
          }
          .justifyContent(FlexAlign.Center)
          .margin({ bottom: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE) })
        }
      }
      .layoutWeight(1)
      .scrollable(ScrollDirection.Vertical)
      .scrollBar(BarState.Auto)
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Constants.COLORS.BACKGROUND)
  }
}