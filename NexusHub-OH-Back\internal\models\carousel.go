package models

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

// CarouselStatus 轮播图状态
type CarouselStatus string

const (
	CarouselStatusActive   CarouselStatus = "active"   // 激活
	CarouselStatusInactive CarouselStatus = "inactive" // 停用
	CarouselStatusDraft    CarouselStatus = "draft"    // 草稿
)

// CarouselType 轮播图类型
type CarouselType string

const (
	CarouselTypeApp        CarouselType = "app"        // 应用推广
	CarouselTypeCollection CarouselType = "collection" // 精选集推广
	CarouselTypeExternal   CarouselType = "external"   // 外部链接
	CarouselTypeAnnouncement CarouselType = "announcement" // 公告
)

// Carousel 轮播图模型
type Carousel struct {
	ID          uint           `json:"id" gorm:"primaryKey;autoIncrement" example:"1"`
	Title       string         `json:"title" gorm:"size:200;not null" example:"热门应用推荐" validate:"required,max=200"`
	Subtitle    string         `json:"subtitle" gorm:"size:500" example:"发现最新最热门的应用" validate:"max=500"`
	ImageURL    string         `json:"image_url" gorm:"size:500;not null" example:"https://example.com/carousel1.jpg" validate:"required,url,max=500"`
	Type        CarouselType   `json:"type" gorm:"size:20;not null;default:'app'" example:"app" validate:"required,oneof=app collection external announcement"`
	TargetID    *uint          `json:"target_id" gorm:"index" example:"123"` // 目标ID（应用ID、精选集ID等）
	TargetURL   string         `json:"target_url" gorm:"size:500" example:"https://example.com" validate:"omitempty,url,max=500"`
	Status      CarouselStatus `json:"status" gorm:"size:20;not null;default:'draft'" example:"active" validate:"required,oneof=active inactive draft"`
	SortOrder   int            `json:"sort_order" gorm:"not null;default:0" example:"1"` // 排序顺序，数字越小越靠前
	StartTime   *time.Time     `json:"start_time" example:"2024-01-01T00:00:00Z"` // 开始时间
	EndTime     *time.Time     `json:"end_time" example:"2024-12-31T23:59:59Z"`   // 结束时间
	ClickCount  int64          `json:"click_count" gorm:"not null;default:0" example:"1000"` // 点击次数
	CreatedBy   uint           `json:"created_by" gorm:"not null;index" example:"1"` // 创建者ID
	UpdatedBy   uint           `json:"updated_by" gorm:"index" example:"1"` // 更新者ID
	CreatedAt   time.Time      `json:"created_at" example:"2024-01-01T00:00:00Z"`
	UpdatedAt   time.Time      `json:"updated_at" example:"2024-01-01T00:00:00Z"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Creator User `json:"creator,omitempty" gorm:"foreignKey:CreatedBy;references:ID"`
	Updater User `json:"updater,omitempty" gorm:"foreignKey:UpdatedBy;references:ID"`

	// 目标关联（根据类型动态关联）
	TargetApp        *Application        `json:"target_app,omitempty" gorm:"foreignKey:TargetID;references:ID"`
	TargetCollection *FeaturedCollection `json:"target_collection,omitempty" gorm:"foreignKey:TargetID;references:ID"`
}

// TableName 指定表名
func (Carousel) TableName() string {
	return "carousels"
}

// CarouselCreateRequest 创建轮播图请求
type CarouselCreateRequest struct {
	Title     string         `json:"title" binding:"required,max=200" example:"热门应用推荐"`
	Subtitle  string         `json:"subtitle" binding:"max=500" example:"发现最新最热门的应用"`
	ImageURL  string         `json:"image_url" binding:"required,url,max=500" example:"https://example.com/carousel1.jpg"`
	Type      CarouselType   `json:"type" binding:"required,oneof=app collection external announcement" example:"app"`
	TargetID  *uint          `json:"target_id" example:"123"`
	TargetURL string         `json:"target_url" binding:"omitempty,url,max=500" example:"https://example.com"`
	Status    CarouselStatus `json:"status" binding:"required,oneof=active inactive draft" example:"active"`
	SortOrder int            `json:"sort_order" example:"1"`
	StartTime *time.Time     `json:"start_time" example:"2024-01-01T00:00:00Z"`
	EndTime   *time.Time     `json:"end_time" example:"2024-12-31T23:59:59Z"`
}

// CarouselUpdateRequest 更新轮播图请求
type CarouselUpdateRequest struct {
	Title     *string         `json:"title" binding:"omitempty,max=200" example:"热门应用推荐"`
	Subtitle  *string         `json:"subtitle" binding:"omitempty,max=500" example:"发现最新最热门的应用"`
	ImageURL  *string         `json:"image_url" binding:"omitempty,url,max=500" example:"https://example.com/carousel1.jpg"`
	Type      *CarouselType   `json:"type" binding:"omitempty,oneof=app collection external announcement" example:"app"`
	TargetID  *uint           `json:"target_id" example:"123"`
	TargetURL *string         `json:"target_url" binding:"omitempty,url,max=500" example:"https://example.com"`
	Status    *CarouselStatus `json:"status" binding:"omitempty,oneof=active inactive draft" example:"active"`
	SortOrder *int            `json:"sort_order" example:"1"`
	StartTime *time.Time      `json:"start_time" example:"2024-01-01T00:00:00Z"`
	EndTime   *time.Time      `json:"end_time" example:"2024-12-31T23:59:59Z"`
}

// CarouselListRequest 轮播图列表请求
type CarouselListRequest struct {
	Page     int            `form:"page" binding:"omitempty,min=1" example:"1"`
	PageSize int            `form:"page_size" binding:"omitempty,min=1,max=100" example:"10"`
	Status   CarouselStatus `form:"status" binding:"omitempty,oneof=active inactive draft" example:"active"`
	Type     CarouselType   `form:"type" binding:"omitempty,oneof=app collection external announcement" example:"app"`
	Keyword  string         `form:"keyword" example:"热门"`
}

// CarouselSortItem 轮播图排序项
type CarouselSortItem struct {
	ID        uint `json:"id" binding:"required" example:"1"`
	SortOrder int  `json:"sort_order" binding:"required" example:"1"`
}

// CarouselSortRequest 轮播图排序请求
type CarouselSortRequest []CarouselSortItem

// CarouselResponse 轮播图响应
type CarouselResponse struct {
	ID          uint           `json:"id" example:"1"`
	Title       string         `json:"title" example:"热门应用推荐"`
	Subtitle    string         `json:"subtitle" example:"发现最新最热门的应用"`
	ImageURL    string         `json:"image_url" example:"https://example.com/carousel1.jpg"`
	Type        CarouselType   `json:"type" example:"app"`
	TargetID    *uint          `json:"target_id" example:"123"`
	TargetURL   string         `json:"target_url" example:"https://example.com"`
	Status      CarouselStatus `json:"status" example:"active"`
	SortOrder   int            `json:"sort_order" example:"1"`
	StartTime   *time.Time     `json:"start_time" example:"2024-01-01T00:00:00Z"`
	EndTime     *time.Time     `json:"end_time" example:"2024-12-31T23:59:59Z"`
	ClickCount  int64          `json:"click_count" example:"1000"`
	CreatedBy   uint           `json:"created_by" example:"1"`
	UpdatedBy   uint           `json:"updated_by" example:"1"`
	CreatedAt   time.Time      `json:"created_at" example:"2024-01-01T00:00:00Z"`
	UpdatedAt   time.Time      `json:"updated_at" example:"2024-01-01T00:00:00Z"`
	CreatorName string         `json:"creator_name" example:"管理员"`
	UpdaterName string         `json:"updater_name" example:"管理员"`

	// 目标信息（根据类型返回）
	TargetInfo interface{} `json:"target_info,omitempty"`
}

// CarouselListResponse 轮播图列表响应
type CarouselListResponse struct {
	List  []CarouselResponse `json:"list"`
	Total int64             `json:"total"`
	Page  int               `json:"page"`
	Size  int               `json:"size"`
}

// CarouselClickRequest 轮播图点击请求
type CarouselClickRequest struct {
	CarouselID uint `json:"carousel_id" binding:"required" example:"1"`
}

// IsActive 检查轮播图是否在有效期内且状态为激活
func (c *Carousel) IsActive() bool {
	if c.Status != CarouselStatusActive {
		return false
	}

	now := time.Now()
	
	// 检查开始时间
	if c.StartTime != nil && now.Before(*c.StartTime) {
		return false
	}
	
	// 检查结束时间
	if c.EndTime != nil && now.After(*c.EndTime) {
		return false
	}
	
	return true
}

// GetTargetURL 获取目标URL
func (c *Carousel) GetTargetURL() string {
	if c.TargetURL != "" {
		return c.TargetURL
	}
	
	// 根据类型生成默认URL
	switch c.Type {
	case CarouselTypeApp:
		if c.TargetID != nil {
			return fmt.Sprintf("/app/detail/%d", *c.TargetID)
		}
	case CarouselTypeCollection:
		if c.TargetID != nil {
			return fmt.Sprintf("/collection/%d", *c.TargetID)
		}
	}
	
	return ""
}