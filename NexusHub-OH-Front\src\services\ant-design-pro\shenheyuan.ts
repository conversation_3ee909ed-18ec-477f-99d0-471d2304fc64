// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 审核应用 审核员审核应用 POST /reviewer/apps/${param0}/review */
export async function postReviewerAppsIdReview(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postReviewerAppsIdReviewParams,
  body: API.AppReviewRequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Response>(`/reviewer/apps/${param0}/review`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 获取待审核应用列表 获取所有待审核的应用 GET /reviewer/apps/pending */
export async function getReviewerAppsPending(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getReviewerAppsPendingParams,
  options?: { [key: string]: any },
) {
  return request<API.Response & { data?: API.PagedResponse }>('/reviewer/apps/pending', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
