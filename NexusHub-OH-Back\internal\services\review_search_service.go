package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"nexushub-oh-back/internal/models"
	"nexushub-oh-back/pkg/elasticsearch"
	"nexushub-oh-back/pkg/logger"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ReviewSearchService 评论搜索服务
type ReviewSearchService struct {
	DB       *gorm.DB
	ESClient *elasticsearch.ESClient
}

// NewReviewSearchService 创建评论搜索服务
func NewReviewSearchService(db *gorm.DB, esClient *elasticsearch.ESClient) *ReviewSearchService {
	return &ReviewSearchService{
		DB:       db,
		ESClient: esClient,
	}
}

// ReviewDocument Elasticsearch中的评论文档结构
type ReviewDocument struct {
	ID            uint      `json:"id"`
	UserID        uint      `json:"user_id"`
	Username      string    `json:"username"`
	ApplicationID uint      `json:"application_id"`
	AppName       string    `json:"app_name"`
	AppPackage    string    `json:"app_package"`
	AppVersion    string    `json:"app_version"`
	Title         string    `json:"title"`
	Content       string    `json:"content"`
	Rating        int       `json:"rating"`
	LikeCount     int       `json:"like_count"`
	Status        string    `json:"status"`
	DevResponse   string    `json:"dev_response"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	Keywords      []string  `json:"keywords"` // 用于搜索的关键词
}

// ReviewSearchRequest 评论搜索请求
type ReviewSearchRequest struct {
	Keyword       string `json:"keyword"`
	UserID        uint   `json:"user_id"`
	ApplicationID uint   `json:"application_id"`
	MinRating     int    `json:"min_rating"`
	MaxRating     int    `json:"max_rating"`
	Status        string `json:"status"`
	HasDevReply   *bool  `json:"has_dev_reply"`
	SortBy        string `json:"sort_by"`    // created_at, rating, like_count
	SortOrder     string `json:"sort_order"` // asc, desc
	Page          int    `json:"page"`
	PageSize      int    `json:"page_size"`
}

// ReviewSearchResponse 评论搜索响应
type ReviewSearchResponse struct {
	Reviews    []ReviewDocument `json:"reviews"`
	Total      int64            `json:"total"`
	Page       int              `json:"page"`
	PageSize   int              `json:"page_size"`
	TotalPages int              `json:"total_pages"`
}

const (
	ReviewIndexName = "nexushub_reviews"
)

// InitializeReviewIndex 初始化评论Elasticsearch索引
func (s *ReviewSearchService) InitializeReviewIndex(ctx context.Context) error {
	// 定义评论索引的映射
	mapping := `{
		"mappings": {
			"properties": {
				"id": {"type": "long"},
				"user_id": {"type": "long"},
				"username": {
					"type": "text",
					"analyzer": "standard",
					"fields": {
						"keyword": {"type": "keyword"}
					}
				},
				"application_id": {"type": "long"},
				"app_name": {
					"type": "text",
					"analyzer": "standard",
					"fields": {
						"keyword": {"type": "keyword"}
					}
				},
				"app_package": {"type": "keyword"},
				"app_version": {"type": "keyword"},
				"title": {
					"type": "text",
					"analyzer": "standard",
					"fields": {
						"keyword": {"type": "keyword"}
					}
				},
				"content": {
					"type": "text",
					"analyzer": "standard"
				},
				"rating": {"type": "integer"},
				"like_count": {"type": "integer"},
				"status": {"type": "keyword"},
				"dev_response": {
					"type": "text",
					"analyzer": "standard"
				},
				"created_at": {"type": "date"},
				"updated_at": {"type": "date"},
				"keywords": {
					"type": "text",
					"analyzer": "standard"
				}
			}
		}
	}`

	// 检查索引是否存在
	exists, err := s.ESClient.IndexExists(ctx, ReviewIndexName)
	if err != nil {
		return fmt.Errorf("检查评论索引是否存在失败: %w", err)
	}

	if !exists {
		if err := s.ESClient.CreateIndex(ctx, ReviewIndexName, mapping); err != nil {
			return fmt.Errorf("创建评论索引失败: %w", err)
		}
		logger.Info("评论索引创建成功", zap.String("index", ReviewIndexName))
	} else {
		logger.Info("评论索引已存在", zap.String("index", ReviewIndexName))
	}

	return nil
}

// IndexReview 索引评论到Elasticsearch
func (s *ReviewSearchService) IndexReview(ctx context.Context, review *models.Review) error {
	// 预加载关联数据
	if err := s.DB.Preload("User").Preload("Application").First(review, review.ID).Error; err != nil {
		return fmt.Errorf("加载评论关联数据失败: %w", err)
	}

	// 构建关键词
	keywords := []string{
		review.Title,
		review.Content,
		review.DevResponse,
		review.User.Username,
		review.Application.Name,
	}

	// 过滤空关键词
	var filteredKeywords []string
	for _, keyword := range keywords {
		if strings.TrimSpace(keyword) != "" {
			filteredKeywords = append(filteredKeywords, keyword)
		}
	}

	// 构建评论文档
	reviewDoc := ReviewDocument{
		ID:            review.ID,
		UserID:        review.UserID,
		Username:      review.User.Username,
		ApplicationID: review.ApplicationID,
		AppName:       review.Application.Name,
		AppPackage:    review.Application.Package,
		AppVersion:    review.AppVersion,
		Title:         review.Title,
		Content:       review.Content,
		Rating:        review.Rating,
		LikeCount:     review.LikeCount,
		Status:        string(review.Status),
		DevResponse:   review.DevResponse,
		CreatedAt:     review.CreatedAt,
		UpdatedAt:     review.UpdatedAt,
		Keywords:      filteredKeywords,
	}

	// 序列化文档
	docJSON, err := json.Marshal(reviewDoc)
	if err != nil {
		return fmt.Errorf("序列化评论文档失败: %w", err)
	}

	// 索引文档
	documentID := strconv.FormatUint(uint64(review.ID), 10)
	if err := s.ESClient.IndexDocument(ctx, ReviewIndexName, documentID, string(docJSON)); err != nil {
		return fmt.Errorf("索引评论文档失败: %w", err)
	}

	logger.Info("评论索引成功", zap.Uint("review_id", review.ID), zap.String("title", review.Title))
	return nil
}

// SearchReviews 搜索评论
func (s *ReviewSearchService) SearchReviews(ctx context.Context, req *ReviewSearchRequest) (*ReviewSearchResponse, error) {
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.SortBy == "" {
		req.SortBy = "created_at"
	}
	if req.SortOrder == "" {
		req.SortOrder = "desc"
	}

	// 构建查询
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": []interface{}{},
				"filter": []interface{}{},
			},
		},
		"sort": []interface{}{
			map[string]interface{}{
				req.SortBy: map[string]interface{}{
					"order": req.SortOrder,
				},
			},
		},
		"from": (req.Page - 1) * req.PageSize,
		"size": req.PageSize,
	}

	mustQueries := query["query"].(map[string]interface{})["bool"].(map[string]interface{})["must"].([]interface{})
	filterQueries := query["query"].(map[string]interface{})["bool"].(map[string]interface{})["filter"].([]interface{})

	// 关键词搜索
	if req.Keyword != "" {
		mustQueries = append(mustQueries, map[string]interface{}{
			"multi_match": map[string]interface{}{
				"query": req.Keyword,
				"fields": []string{
					"title^3",
					"content^2",
					"dev_response",
					"username",
					"app_name",
					"keywords",
				},
				"type": "best_fields",
				"fuzziness": "AUTO",
			},
		})
	}

	// 用户ID过滤
	if req.UserID > 0 {
		filterQueries = append(filterQueries, map[string]interface{}{
			"term": map[string]interface{}{
				"user_id": req.UserID,
			},
		})
	}

	// 应用ID过滤
	if req.ApplicationID > 0 {
		filterQueries = append(filterQueries, map[string]interface{}{
			"term": map[string]interface{}{
				"application_id": req.ApplicationID,
			},
		})
	}

	// 评分范围过滤
	if req.MinRating > 0 || req.MaxRating > 0 {
		rangeFilter := map[string]interface{}{}
		if req.MinRating > 0 {
			rangeFilter["gte"] = req.MinRating
		}
		if req.MaxRating > 0 {
			rangeFilter["lte"] = req.MaxRating
		}
		filterQueries = append(filterQueries, map[string]interface{}{
			"range": map[string]interface{}{
				"rating": rangeFilter,
			},
		})
	}

	// 状态过滤
	if req.Status != "" {
		filterQueries = append(filterQueries, map[string]interface{}{
			"term": map[string]interface{}{
				"status": req.Status,
			},
		})
	}

	// 开发者回复过滤
	if req.HasDevReply != nil {
		if *req.HasDevReply {
			// 有开发者回复
			filterQueries = append(filterQueries, map[string]interface{}{
				"exists": map[string]interface{}{
					"field": "dev_response",
				},
			})
			filterQueries = append(filterQueries, map[string]interface{}{
				"bool": map[string]interface{}{
					"must_not": map[string]interface{}{
						"term": map[string]interface{}{
							"dev_response": "",
						},
					},
				},
			})
		} else {
			// 没有开发者回复
			filterQueries = append(filterQueries, map[string]interface{}{
				"bool": map[string]interface{}{
					"should": []interface{}{
						map[string]interface{}{
							"bool": map[string]interface{}{
								"must_not": map[string]interface{}{
									"exists": map[string]interface{}{
										"field": "dev_response",
									},
								},
							},
						},
						map[string]interface{}{
							"term": map[string]interface{}{
								"dev_response": "",
							},
						},
					},
					"minimum_should_match": 1,
				},
			})
		}
	}

	// 更新查询
	query["query"].(map[string]interface{})["bool"].(map[string]interface{})["must"] = mustQueries
	query["query"].(map[string]interface{})["bool"].(map[string]interface{})["filter"] = filterQueries

	// 序列化查询
	queryJSON, err := json.Marshal(query)
	if err != nil {
		return nil, fmt.Errorf("序列化查询失败: %w", err)
	}

	// 执行搜索
	result, err := s.ESClient.Search(ctx, ReviewIndexName, string(queryJSON))
	if err != nil {
		return nil, fmt.Errorf("搜索评论失败: %w", err)
	}

	// 解析结果
	hits := result["hits"].(map[string]interface{})
	total := int64(hits["total"].(map[string]interface{})["value"].(float64))
	hitsList := hits["hits"].([]interface{})

	var reviews []ReviewDocument
	for _, hit := range hitsList {
		hitMap := hit.(map[string]interface{})
		source := hitMap["_source"].(map[string]interface{})

		// 解析评论文档
		sourceJSON, err := json.Marshal(source)
		if err != nil {
			continue
		}

		var review ReviewDocument
		if err := json.Unmarshal(sourceJSON, &review); err != nil {
			continue
		}

		reviews = append(reviews, review)
	}

	// 计算总页数
	totalPages := int(total) / req.PageSize
	if int(total)%req.PageSize > 0 {
		totalPages++
	}

	return &ReviewSearchResponse{
		Reviews:    reviews,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// DeleteReview 从Elasticsearch删除评论
func (s *ReviewSearchService) DeleteReview(ctx context.Context, reviewID uint) error {
	documentID := strconv.FormatUint(uint64(reviewID), 10)
	if err := s.ESClient.DeleteDocument(ctx, ReviewIndexName, documentID); err != nil {
		return fmt.Errorf("删除评论文档失败: %w", err)
	}

	logger.Info("评论文档删除成功", zap.Uint("review_id", reviewID))
	return nil
}

// SyncAllReviews 同步所有评论到Elasticsearch
func (s *ReviewSearchService) SyncAllReviews(ctx context.Context) error {
	var reviews []models.Review
	if err := s.DB.Preload("User").Preload("Application").Find(&reviews).Error; err != nil {
		return fmt.Errorf("获取评论列表失败: %w", err)
	}

	for _, review := range reviews {
		if err := s.IndexReview(ctx, &review); err != nil {
			logger.Error("同步评论失败", zap.Uint("review_id", review.ID), zap.Error(err))
			continue
		}
	}

	logger.Info("评论同步完成", zap.Int("total", len(reviews)))
	return nil
}