<svg width="108" height="108" viewBox="0 0 108 108" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆形 -->
  <circle cx="54" cy="54" r="54" fill="url(#gradient)"/>
  
  <!-- 主图标 - 应用商店符号 -->
  <g transform="translate(24, 24)">
    <!-- 外框 -->
    <rect x="8" y="8" width="44" height="44" rx="12" fill="none" stroke="white" stroke-width="3"/>
    
    <!-- 内部图标 - 下载箭头和包装盒 -->
    <g transform="translate(18, 18)">
      <!-- 包装盒 -->
      <rect x="6" y="12" width="12" height="12" rx="2" fill="white" opacity="0.9"/>
      <rect x="6" y="12" width="12" height="4" rx="2" fill="white"/>
      
      <!-- 下载箭头 -->
      <path d="M12 2 L12 14 M8 10 L12 14 L16 10" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
    </g>
    
    <!-- 装饰点 -->
    <circle cx="44" cy="16" r="3" fill="white" opacity="0.7"/>
    <circle cx="16" cy="44" r="2" fill="white" opacity="0.5"/>
  </g>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>