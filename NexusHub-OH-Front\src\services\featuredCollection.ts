import { request } from '@umijs/max';

// 精选集数据接口
export interface FeaturedCollection {
  id: number;
  name: string;
  description: string;
  apps: Array<{
    id: number;
    name: string;
    icon: string;
    package: string;
  }>;
  app_count: number;
  is_active: boolean;
  display_order: number;
  created_at: string;
  updated_at: string;
}

// 创建精选集请求参数
export interface CreateFeaturedCollectionRequest {
  title: string;
  description: string;
  icon?: string;
  cover_image?: string;
  sort_order: number;
  is_active: boolean;
  is_public: boolean;
}

// 更新精选集请求参数
export interface UpdateFeaturedCollectionRequest extends Partial<CreateFeaturedCollectionRequest> {}

// 精选集列表查询参数
export interface FeaturedCollectionListParams {
  page?: number;
  page_size?: number;
  keyword?: string;
  is_active?: boolean;
}

/**
 * 获取精选集列表
 * @param params - 查询参数
 */
export async function getFeaturedCollectionList(params?: FeaturedCollectionListParams) {
  return request('/public/featured-collections', {
    method: 'GET',
    params,
  });
}

/**
 * 获取精选集详情
 * @param id - 精选集ID
 */
export async function getFeaturedCollectionDetail(id: number) {
  return request(`/public/featured-collections/${id}`, {
    method: 'GET',
  });
}

/**
 * 创建精选集
 * @param data - 精选集数据
 */
export async function createFeaturedCollection(data: CreateFeaturedCollectionRequest) {
  return request('/admin/featured-collections', {
    method: 'POST',
    data,
  });
}

/**
 * 更新精选集
 * @param id - 精选集ID
 * @param data - 更新数据
 */
export async function updateFeaturedCollection(id: number, data: UpdateFeaturedCollectionRequest) {
  return request(`/admin/featured-collections/${id}`, {
    method: 'PUT',
    data,
  });
}

/**
 * 删除精选集
 * @param id - 精选集ID
 */
export async function deleteFeaturedCollection(id: number) {
  return request(`/admin/featured-collections/${id}`, {
    method: 'DELETE',
  });
}

/**
 * 批量删除精选集
 * @param ids - 精选集ID数组
 */
export async function batchDeleteFeaturedCollections(ids: number[]) {
  return request('/admin/featured-collections/batch-delete', {
    method: 'POST',
    data: { ids },
  });
}

/**
 * 更新精选集状态
 * @param id - 精选集ID
 * @param is_active - 是否启用
 */
export async function updateFeaturedCollectionStatus(id: number, is_active: boolean) {
  return request(`/admin/featured-collections/${id}/status`, {
    method: 'PUT',
    data: { is_active },
  });
}

/**
 * 更新精选集显示顺序
 * @param id - 精选集ID
 * @param display_order - 显示顺序
 */
export async function updateFeaturedCollectionOrder(id: number, display_order: number) {
  return request(`/admin/featured-collections/${id}/order`, {
    method: 'PUT',
    data: { display_order },
  });
}

/**
 * 获取精选集中的应用列表
 * @param id - 精选集ID
 * @param params - 查询参数
 */
export async function getFeaturedCollectionApps(id: number, params?: {
  page?: number;
  page_size?: number;
}) {
  return request(`/public/featured-collections/${id}/apps`, {
    method: 'GET',
    params,
  });
}

/**
 * 向精选集添加应用
 * @param id - 精选集ID
 * @param app_ids - 应用ID数组
 */
export async function addAppsToFeaturedCollection(id: number, app_ids: number[]) {
  return request(`/admin/featured-collections/${id}/apps`, {
    method: 'POST',
    data: { app_ids },
  });
}

/**
 * 从精选集移除应用
 * @param id - 精选集ID
 * @param app_ids - 应用ID数组
 */
export async function removeAppsFromFeaturedCollection(id: number, app_ids: number[]) {
  return request(`/admin/featured-collections/${id}/apps`, {
    method: 'DELETE',
    data: { app_ids },
  });
}