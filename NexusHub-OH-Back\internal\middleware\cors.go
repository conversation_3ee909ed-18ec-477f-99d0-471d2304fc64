/*
 * @Author: xiaoxiaozhou <EMAIL>
 * @Date: 2025-06-03 16:03:08
 * @LastEditors: xiaoxiaozhou <EMAIL>
 * @LastEditTime: 2025-06-15 12:51:10
 * @FilePath: \NexusHub-OH\NexusHub-OH-Back\internal\middleware\cors.go
 * @Description:
 * 技术生的工作日常
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
 */
package middleware

import (
	"time"

	"nexushub-oh-back/config"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// Cors 跨域中间件
func Cors() gin.HandlerFunc {
	return CorsWithConfig(nil)
}

// CorsWithConfig 使用配置的跨域中间件
func CorsWithConfig(cfg *config.CorsConfig) gin.HandlerFunc {
	// 如果未提供配置，使用默认配置
	if cfg == nil {
		return cors.New(cors.Config{
			AllowOrigins:     []string{"http://localhost:8000"}, // 允许前端地址，设置为true时AllowOrigins不能为通配符
			AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
			AllowHeaders:     []string{"Origin", "Content-Type", "Content-Length", "Accept-Encoding", "X-CSRF-Token", "Authorization", "Accept"},
			ExposeHeaders:    []string{"Content-Length"},
			AllowCredentials: true, // 是否允许发送Cookie，设置为true时AllowOrigins不能为通配符
			MaxAge:           12 * time.Hour,
		})
	}

	// 解析MaxAge字符串为时间
	maxAge := 12 * time.Hour
	if cfg.MaxAge != "" {
		duration, err := time.ParseDuration(cfg.MaxAge)
		if err == nil {
			maxAge = duration
		}
	}

	// 使用提供的配置
	return cors.New(cors.Config{
		AllowOrigins:     cfg.AllowOrigins,
		AllowMethods:     cfg.AllowMethods,
		AllowHeaders:     cfg.AllowHeaders,
		ExposeHeaders:    cfg.ExposeHeaders,
		AllowCredentials: cfg.AllowCredentials,
		MaxAge:           maxAge,
	})
}
