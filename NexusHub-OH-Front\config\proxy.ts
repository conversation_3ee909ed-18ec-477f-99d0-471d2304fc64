/**
 * @name 代理的配置
 * @see 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * -------------------------------
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 *
 * @doc https://umijs.org/docs/guides/proxy
 */
export default {
  // 开发环境代理配置
  dev: {
    '/api/v1/': {
      // 后端API地址
      target: 'http://localhost:8080',
      // 配置了这个可以从 http 代理到 https
      changeOrigin: true,
      // 不重写路径
      pathRewrite: { '^': '' },
    },
  },

  /**
   * @name 详细的代理配置
   * @doc https://github.com/chimurai/http-proxy-middleware
   */
  test: {
    '/api/v1/': {
      target: 'http://test-api.nexushub-oh.top',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
  },
  
  pre: {
    '/api/v1/': {
      target: 'http://pre-api.nexushub-oh.top',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
  },
};
