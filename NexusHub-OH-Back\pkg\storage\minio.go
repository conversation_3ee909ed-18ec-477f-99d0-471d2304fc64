package storage

import (
	"context"
	"fmt"
	"io"
	"time"

	"nexushub-oh-back/config"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

// MinioProvider MinIO存储提供者
type MinioProvider struct {
	client     *minio.Client
	bucketName string
}

// NewMinioProvider 创建一个新的MinIO存储提供者
func NewMinioProvider(cfg *config.StorageConfig) (StorageProvider, error) {
	// 创建MinIO客户端
	client, err := minio.New(cfg.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(cfg.AccessKey, cfg.SecretKey, ""),
		Secure: false, // 是否使用HTTPS
		Region: cfg.Region,
	})
	if err != nil {
		return nil, fmt.Errorf("无法创建MinIO客户端: %w", err)
	}

	provider := &MinioProvider{
		client:     client,
		bucketName: cfg.Bucket,
	}

	// 检查存储桶是否存在，不存在则创建
	err = provider.CheckBucket(context.Background(), cfg.Bucket)
	if err != nil {
		return nil, err
	}

	return provider, nil
}

// UploadFile 上传文件到MinIO
func (m *MinioProvider) UploadFile(ctx context.Context, bucketName, objectName string, reader io.Reader, objectSize int64, contentType string) error {
	if bucketName == "" {
		bucketName = m.bucketName
	}

	// 上传文件
	_, err := m.client.PutObject(ctx, bucketName, objectName, reader, objectSize, minio.PutObjectOptions{
		ContentType: contentType,
	})
	if err != nil {
		return fmt.Errorf("上传文件失败: %w", err)
	}

	return nil
}

// GetFileURL 获取MinIO文件的上传URL
func (m *MinioProvider) GetFileURL(ctx context.Context, bucketName, objectName string, expiry time.Duration) (string, error) {
	if bucketName == "" {
		bucketName = m.bucketName
	}

	// 生成上传用的预签名URL
	presignedURL, err := m.client.PresignedPutObject(ctx, bucketName, objectName, expiry)
	if err != nil {
		return "", fmt.Errorf("无法生成预签名上传URL: %w", err)
	}

	return presignedURL.String(), nil
}

// DeleteFile 从MinIO删除文件
func (m *MinioProvider) DeleteFile(ctx context.Context, bucketName, objectName string) error {
	if bucketName == "" {
		bucketName = m.bucketName
	}

	// 删除文件
	err := m.client.RemoveObject(ctx, bucketName, objectName, minio.RemoveObjectOptions{})
	if err != nil {
		return fmt.Errorf("删除文件失败: %w", err)
	}

	return nil
}

// CheckBucket 检查MinIO存储桶是否存在，不存在则创建
func (m *MinioProvider) CheckBucket(ctx context.Context, bucketName string) error {
	if bucketName == "" {
		bucketName = m.bucketName
	}

	// 检查存储桶是否存在
	exists, err := m.client.BucketExists(ctx, bucketName)
	if err != nil {
		return fmt.Errorf("检查存储桶是否存在失败: %w", err)
	}

	// 如果不存在，创建存储桶
	if !exists {
		err = m.client.MakeBucket(ctx, bucketName, minio.MakeBucketOptions{})
		if err != nil {
			return fmt.Errorf("创建存储桶失败: %w", err)
		}
	}

	// 无论存储桶是否存在，都设置公共读取权限
	policy := fmt.Sprintf(`{
		"Version": "2012-10-17",
		"Statement": [
			{
				"Effect": "Allow",
				"Principal": {"AWS": ["*"]},
				"Action": ["s3:GetObject"],
				"Resource": ["arn:aws:s3:::%s/*"]
			}
		]
	}`, bucketName)

	err = m.client.SetBucketPolicy(ctx, bucketName, policy)
	if err != nil {
		return fmt.Errorf("设置存储桶公共访问权限失败: %w", err)
	}

	return nil
}
