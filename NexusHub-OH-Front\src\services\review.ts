/*
 * @Author: xiaoxiaozhou <EMAIL>
 * @Date: 2025-05-01 12:16:13
 * @LastEditors: xiaoxiaozhou <EMAIL>
 * @LastEditTime: 2025-05-01 12:16:19
 * @FilePath: \NexusHub-Front\src\services\review.ts
 * @Description: 
 * 技术生的工作日常
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/**
 * 获取应用评论列表
 * @param params - 查询参数
 */
export async function getAppReviews(params: {
  app_id: string;
  page?: number;
  pageSize?: number;
}) {
  return request('/app/review/list', {
    method: 'GET',
    params,
  });
}

/**
 * 发表评论
 * @param appId - 应用ID
 * @param data - 评论内容
 */
export async function postReview(appId: string, data: {
  rating: number;
  content: string;
}) {
  return request(`/app/review/${appId}`, {
    method: 'POST',
    data,
  });
}

/**
 * 修改评论
 * @param id - 评论ID
 * @param data - 修改内容
 */
export async function updateReview(id: string, data: {
  rating: number;
  content: string;
}) {
  return request(`/app/review/${id}`, {
    method: 'PUT',
    data,
  });
}

/**
 * 删除评论
 * @param id - 评论ID
 */
export async function deleteReview(id: string) {
  return request(`/app/review/${id}`, {
    method: 'DELETE',
  });
}

/**
 * 回复评论
 * @param data - 回复内容
 */
export async function replyReview(data: {
  review_id: number;
  content: string;
}) {
  return request('/app/review/reply', {
    method: 'POST',
    data,
  });
}