import { PageContainer } from '@ant-design/pro-components';
import { Card, Table, Button, Tag, Space, DatePicker, Form, Input, Select, Modal, message, Tabs, Tooltip, Typography } from 'antd';
import { SearchOutlined, DownloadOutlined, DeleteOutlined, EyeOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { useRequest } from '@umijs/max';
import type { ColumnsType } from 'antd/es/table';
import moment from 'moment';

const { RangePicker } = DatePicker;
const { Text } = Typography;

interface LogItem {
  id: string;
  timestamp: string;
  level: 'info' | 'warning' | 'error' | 'debug';
  module: string;
  message: string;
  details?: string;
  user?: string;
  ip?: string;
  userAgent?: string;
}

// 模拟数据获取函数
const fetchLogList = async (params: any) => {
  // 实际项目中应该调用API
  console.log('Fetching log list with params:', params);
  
  // 生成模拟数据
  const mockData: LogItem[] = [];
  const modules = ['用户管理', '应用审核', '内容管理', '安全中心', '系统设置'];
  const levels = ['info', 'warning', 'error', 'debug'];
  const users = ['admin', 'operator1', 'operator2', 'system'];
  const messages = [
    '用户登录成功',
    '用户登录失败',
    '创建新应用',
    '应用审核通过',
    '应用审核拒绝',
    '修改系统参数',
    '删除内容',
    '备份数据',
    '导出报表',
    '修改权限',
    '重置密码',
    '系统异常',
  ];

  // 生成50条随机日志
  for (let i = 0; i < 50; i++) {
    const level = levels[Math.floor(Math.random() * levels.length)] as 'info' | 'warning' | 'error' | 'debug';
    const module = modules[Math.floor(Math.random() * modules.length)];
    const user = Math.random() > 0.2 ? users[Math.floor(Math.random() * users.length)] : undefined;
    const message = messages[Math.floor(Math.random() * messages.length)];
    
    // 生成随机时间，最近30天内
    const date = new Date();
    date.setDate(date.getDate() - Math.floor(Math.random() * 30));
    date.setHours(Math.floor(Math.random() * 24), Math.floor(Math.random() * 60), Math.floor(Math.random() * 60));
    
    mockData.push({
      id: `log-${i + 1}`,
      timestamp: date.toISOString(),
      level,
      module,
      message,
      details: level === 'error' ? `详细错误信息: ${Math.random().toString(36).substring(2, 15)}` : undefined,
      user,
      ip: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    });
  }

  // 按时间倒序排序
  mockData.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

  // 根据查询参数过滤
  let filteredData = [...mockData];

  if (params.dateRange && params.dateRange.length === 2) {
    const startDate = new Date(params.dateRange[0]).getTime();
    const endDate = new Date(params.dateRange[1]).getTime();
    filteredData = filteredData.filter(item => {
      const itemDate = new Date(item.timestamp).getTime();
      return itemDate >= startDate && itemDate <= endDate;
    });
  }

  if (params.level) {
    filteredData = filteredData.filter(item => item.level === params.level);
  }

  if (params.module) {
    filteredData = filteredData.filter(item => item.module === params.module);
  }

  if (params.keyword) {
    const keyword = params.keyword.toLowerCase();
    filteredData = filteredData.filter(item => 
      item.message.toLowerCase().includes(keyword) || 
      (item.details && item.details.toLowerCase().includes(keyword)) ||
      (item.user && item.user.toLowerCase().includes(keyword))
    );
  }

  return { 
    data: filteredData,
    total: filteredData.length,
    success: true,
  };
};

const LogsManagement: React.FC = () => {
  const [searchParams, setSearchParams] = useState<any>({});
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10 });
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [currentLog, setCurrentLog] = useState<LogItem | null>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [activeTab, setActiveTab] = useState('all');

  const { data, loading, refresh } = useRequest(() => fetchLogList({
    ...searchParams,
    level: activeTab !== 'all' ? activeTab : undefined,
  }), {
    refreshDeps: [searchParams, activeTab, pagination],
  });

  const handleSearch = (values: any) => {
    setSearchParams(values);
    setPagination({ ...pagination, current: 1 });
  };

  const handleTabChange = (key: string) => {
    setActiveTab(key);
    setPagination({ ...pagination, current: 1 });
  };

  const handleViewDetails = (record: LogItem) => {
    setCurrentLog(record);
    setDetailModalVisible(true);
  };

  const handleTableChange = (newPagination: any) => {
    setPagination({
      current: newPagination.current,
      pageSize: newPagination.pageSize,
    });
  };

  const handleExport = () => {
    message.success('日志导出成功');
  };

  const handleDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的日志');
      return;
    }

    Modal.confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除选中的 ${selectedRowKeys.length} 条日志记录吗？此操作不可恢复。`,
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        message.success(`成功删除 ${selectedRowKeys.length} 条日志记录`);
        setSelectedRowKeys([]);
        refresh();
      },
    });
  };

  const getLevelTag = (level: string) => {
    switch (level) {
      case 'info':
        return <Tag color="blue">信息</Tag>;
      case 'warning':
        return <Tag color="orange">警告</Tag>;
      case 'error':
        return <Tag color="red">错误</Tag>;
      case 'debug':
        return <Tag color="green">调试</Tag>;
      default:
        return <Tag>{level}</Tag>;
    }
  };

  const columns: ColumnsType<LogItem> = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (text) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 180,
    },
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      render: (level) => getLevelTag(level),
      width: 100,
    },
    {
      title: '模块',
      dataIndex: 'module',
      key: 'module',
      width: 120,
    },
    {
      title: '操作人',
      dataIndex: 'user',
      key: 'user',
      render: (text) => text || '-',
      width: 120,
    },
    {
      title: '消息',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true,
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
      width: 140,
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              onClick={() => handleViewDetails(record)} 
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (keys: React.Key[]) => setSelectedRowKeys(keys),
  };

  return (
    <PageContainer
      header={{
        title: '日志管理',
      }}
    >
      <Card bordered={false}>
        <Tabs 
          activeKey={activeTab} 
          onChange={handleTabChange}
        >
          <Tabs.TabPane tab="全部" key="all" />
          <Tabs.TabPane tab="信息" key="info" />
          <Tabs.TabPane tab="警告" key="warning" />
          <Tabs.TabPane tab="错误" key="error" />
          <Tabs.TabPane tab="调试" key="debug" />
        </Tabs>

        <Form layout="inline" onFinish={handleSearch} style={{ marginBottom: 24 }}>
          <Form.Item name="dateRange" label="时间范围">
            <RangePicker 
              showTime 
              style={{ width: 380 }} 
              placeholder={['开始时间', '结束时间']}
            />
          </Form.Item>
          <Form.Item name="module" label="模块">
            <Select 
              style={{ width: 160 }} 
              placeholder="选择模块"
              allowClear
              options={[
                { value: '用户管理', label: '用户管理' },
                { value: '应用审核', label: '应用审核' },
                { value: '内容管理', label: '内容管理' },
                { value: '安全中心', label: '安全中心' },
                { value: '系统设置', label: '系统设置' },
              ]}
            />
          </Form.Item>
          <Form.Item name="keyword" label="关键词">
            <Input placeholder="消息/详情/用户" style={{ width: 200 }} />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
              搜索
            </Button>
          </Form.Item>
        </Form>

        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button 
              icon={<DownloadOutlined />} 
              onClick={handleExport}
              disabled={!data || !data.data || data.data.length === 0}
            >
              导出日志
            </Button>
            <Button 
              danger 
              icon={<DeleteOutlined />} 
              onClick={handleDelete}
              disabled={selectedRowKeys.length === 0}
            >
              删除所选
            </Button>
          </Space>
          {selectedRowKeys.length > 0 && (
            <span style={{ marginLeft: 8 }}>
              已选择 <Text strong>{selectedRowKeys.length}</Text> 项
            </span>
          )}
        </div>

        <Table
          columns={columns}
          dataSource={data?.data}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            total: data?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          onChange={handleTableChange}
          rowSelection={rowSelection}
        />
      </Card>

      {/* 日志详情弹窗 */}
      <Modal
        title="日志详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
        ]}
        width={700}
      >
        {currentLog && (
          <div>
            <div style={{ marginBottom: 16 }}>
              <div style={{ marginBottom: 8 }}>
                <Text strong>时间：</Text> {moment(currentLog.timestamp).format('YYYY-MM-DD HH:mm:ss')}
              </div>
              <div style={{ marginBottom: 8 }}>
                <Text strong>级别：</Text> {getLevelTag(currentLog.level)}
              </div>
              <div style={{ marginBottom: 8 }}>
                <Text strong>模块：</Text> {currentLog.module}
              </div>
              <div style={{ marginBottom: 8 }}>
                <Text strong>操作人：</Text> {currentLog.user || '-'}
              </div>
              <div style={{ marginBottom: 8 }}>
                <Text strong>IP地址：</Text> {currentLog.ip}
              </div>
              <div style={{ marginBottom: 8 }}>
                <Text strong>消息：</Text> {currentLog.message}
              </div>
              {currentLog.details && (
                <div style={{ marginBottom: 8 }}>
                  <Text strong>详细信息：</Text>
                  <div style={{ 
                    background: '#f5f5f5', 
                    padding: 12, 
                    borderRadius: 4, 
                    marginTop: 8,
                    maxHeight: 200,
                    overflow: 'auto'
                  }}>
                    <pre style={{ margin: 0, whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>
                      {currentLog.details}
                    </pre>
                  </div>
                </div>
              )}
              <div style={{ marginBottom: 8 }}>
                <Text strong>用户代理：</Text>
                <div style={{ 
                  background: '#f5f5f5', 
                  padding: 8, 
                  borderRadius: 4, 
                  marginTop: 8,
                  fontSize: 12,
                  wordBreak: 'break-all'
                }}>
                  {currentLog.userAgent}
                </div>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </PageContainer>
  );
};

export default LogsManagement;