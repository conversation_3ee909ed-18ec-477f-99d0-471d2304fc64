/*
 * @Author: xiaoxiaozhou <EMAIL>
 * @Date: 2025-05-01 12:20:32
 * @LastEditors: xiaoxiaozhou <EMAIL>
 * @LastEditTime: 2025-05-07 20:14:45
 * @FilePath: \NexusHub-Front\src\services\index.ts
 * @Description: 
 * 技术生的工作日常
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
// @ts-ignore
/* eslint-disable */

// 导出原有API服务
export * from './user';
export * from './app';
export * from './review';
export * from './settings';
export * from './admin';

// 导出自动生成的API服务
export * as nexusApi from './ant-design-pro';
export * from './ant-design-pro/yingyong';
export * from './ant-design-pro/yonghu';
export * from './ant-design-pro/kaifazhe';
export * from './ant-design-pro/pinglun';
export * from './ant-design-pro/tongji';
export * from './ant-design-pro/shenheyuan';
export * from './ant-design-pro/guanliyuan';
export * from './ant-design-pro/xitong';

// 类型定义
export interface ResponseData<T = any> {
  code: number;
  message: string;
  data: T;
}

// 分页数据结构
export interface PageData<T = any> {
  total: number;
  list: T[];
  page: number;
  size: number;
}