/*
 * @Author: xiaoxiaozhou <EMAIL>
 * @Date: 2025-06-07 00:57:05
 * @LastEditors: xiaoxiaozhou <EMAIL>
 * @LastEditTime: 2025-06-07 02:07:03
 * @FilePath: \NexusHub-OH\NexusHub-OH-Front\src\pages\dashboard\monitor\service.ts
 * @Description: 
 * 技术生的工作日常
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
import { request } from '@umijs/max';
import type { TagType } from './data';

/**
 * 获取标签数据
 */
export async function queryTags(): Promise<{ data: { list: TagType[] } }> {
  return request('/tags');
}

/**
 * 获取系统监控数据
 * 获取系统运行状态监控数据，包括CPU、内存、磁盘使用率等
 */
export async function getMonitoringData(): Promise<{ data: any }> {
  return request('/dashboard/monitoring/data');
}

/**
 * 获取系统日志
 * 获取系统运行日志记录
 */
export async function getMonitoringLogs(params?: {
  page?: number;
  page_size?: number;
  level?: string;
}): Promise<{ data: any }> {
  return request('/dashboard/monitoring/logs', {
    params: {
      page: 1,
      page_size: 20,
      ...params,
    },
  });
}

/**
 * 获取告警事件
 * 获取系统告警事件
 */
export async function getMonitoringAlerts(params?: {
  page?: number;
  page_size?: number;
  status?: string;
}): Promise<{ data: any }> {
  return request('/dashboard/monitoring/alerts', {
    params: {
      page: 1,
      page_size: 20,
      ...params,
    },
  });
}
