# 调试搜索API响应结构

$headers = @{
    'Content-Type' = 'application/json'
    'Authorization' = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiaXNzIjoibmV4dXNodWIiLCJzdWIiOiIxIiwiZXhwIjoxNzQ5MzU2MTY1LCJuYmYiOjE3NDkyNjk3NjUsImlhdCI6MTc0OTI2OTc2NX0.ih2EzCnivxygkSSeqbBoo9AjcLyhPEeJMc6EKgEQUD4'
}

try {
    Write-Host "调试搜索API响应结构..."
    $response = Invoke-RestMethod -Uri 'http://localhost:8080/api/v1/admin/search/users?keyword=admin&page=1&page_size=10' -Method GET -Headers $headers
    
    Write-Host "完整响应:"
    $response | ConvertTo-Json -Depth 10
    
    Write-Host "`n数据字段:"
    $response.data | Get-Member
    
    Write-Host "`n用户数组字段名:"
    $response.data.PSObject.Properties | ForEach-Object { Write-Host "$($_.Name): $($_.Value.GetType().Name)" }
    
} catch {
    Write-Host "调试失败: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "响应内容: $responseBody"
    }
}