package logger

import (
	"io"
	"os"
	"path/filepath"
	"time"

	"nexushub-oh-back/config"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

var Logger *zap.Logger

// InitLogger 初始化日志系统
func InitLogger(cfg *config.LogConfig) {
	// 创建日志目录
	if err := os.MkdirAll(filepath.Dir(cfg.File), 0755); err != nil {
		panic("无法创建日志目录: " + err.Error())
	}

	// 配置日志输出
	hook := &lumberjack.Logger{
		Filename:   cfg.File,
		MaxSize:    cfg.MaxSize,
		MaxBackups: cfg.MaxBackups,
		MaxAge:     cfg.MaxAge,
		Compress:   cfg.Compress,
	}

	// 配置编码器
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		<PERSON><PERSON><PERSON>:        "logger",
		<PERSON><PERSON><PERSON><PERSON>:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	// 设置日志级别
	var level zapcore.Level
	switch cfg.Level {
	case "debug":
		level = zapcore.DebugLevel
	case "info":
		level = zapcore.InfoLevel
	case "warn":
		level = zapcore.WarnLevel
	case "error":
		level = zapcore.ErrorLevel
	default:
		level = zapcore.InfoLevel
	}

	// 创建核心
	core := zapcore.NewCore(
		zapcore.NewJSONEncoder(encoderConfig),
		zapcore.NewMultiWriteSyncer(
			zapcore.AddSync(os.Stdout),
			zapcore.AddSync(hook),
		),
		zap.NewAtomicLevelAt(level),
	)

	// 创建日志记录器
	Logger = zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1))

	zap.ReplaceGlobals(Logger)
}

// 兼容旧版本的InitLogger
func InitLoggerWithPath(logPath string, logLevel string) {
	// 创建日志配置
	logConfig := &config.LogConfig{
		Level:      logLevel,
		File:       logPath,
		MaxSize:    100,
		MaxBackups: 10,
		MaxAge:     30,
		Compress:   true,
	}

	// 调用新的InitLogger
	InitLogger(logConfig)
}

// GinLogger 为Gin框架提供的日志中间件
func GinLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		query := c.Request.URL.RawQuery
		c.Next()

		end := time.Now()
		latency := end.Sub(start)

		if len(c.Errors) > 0 {
			// 有错误
			for _, e := range c.Errors.Errors() {
				Logger.Error("Gin错误",
					zap.String("错误", e),
				)
			}
		} else {
			// 记录请求
			Logger.Info("Gin请求",
				zap.String("路径", path),
				zap.String("查询", query),
				zap.String("IP", c.ClientIP()),
				zap.String("方法", c.Request.Method),
				zap.Int("状态", c.Writer.Status()),
				zap.String("延迟", latency.String()),
				zap.String("用户代理", c.Request.UserAgent()),
			)
		}
	}
}

// Close 关闭日志记录器
func Close() {
	if Logger != nil {
		_ = Logger.Sync()
	}
}

// Debug 调试级别日志
func Debug(msg string, fields ...zap.Field) {
	Logger.Debug(msg, fields...)
}

// Info 信息级别日志
func Info(msg string, fields ...zap.Field) {
	Logger.Info(msg, fields...)
}

// Warn 警告级别日志
func Warn(msg string, fields ...zap.Field) {
	Logger.Warn(msg, fields...)
}

// Error 错误级别日志
func Error(msg string, fields ...zap.Field) {
	Logger.Error(msg, fields...)
}

// Fatal 致命级别日志
func Fatal(msg string, fields ...zap.Field) {
	Logger.Fatal(msg, fields...)
}

// Writer 返回一个io.Writer，可以用于其他日志系统
func Writer() io.Writer {
	return &zapWriter{logger: Logger.Sugar()}
}

type zapWriter struct {
	logger *zap.SugaredLogger
}

func (w *zapWriter) Write(p []byte) (int, error) {
	w.logger.Info(string(p))
	return len(p), nil
}
