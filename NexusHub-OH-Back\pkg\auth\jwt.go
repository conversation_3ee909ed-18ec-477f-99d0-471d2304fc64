package auth

import (
	"errors"
	"fmt"
	"time"

	"nexushub-oh-back/config"

	"github.com/golang-jwt/jwt/v4"
)

// TokenClaims 自定义的JWT声明
type TokenClaims struct {
	UserID   uint   `json:"user_id"`
	Username string `json:"username"`
	Role     string `json:"role"`
	jwt.RegisteredClaims
}

// JWTService JWT服务结构体
type JWTService struct {
	secretKey  string
	expireTime int // 小时
}

// NewJWTService 创建一个新的JWT服务
func NewJWTService(cfg *config.JWTConfig) *JWTService {
	// 将过期时间从time.Duration转换为小时
	expireHours := int(cfg.Expire.Hours())
	return &JWTService{
		secretKey:  cfg.Secret,
		expireTime: expireHours,
	}
}

// GenerateToken 生成JWT令牌
func (j *JWTService) GenerateToken(userID uint, username, role string) (string, error) {
	claims := TokenClaims{
		UserID:   userID,
		Username: username,
		Role:     role,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Duration(j.expireTime) * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "nexushub",
			Subject:   fmt.Sprintf("%d", userID),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(j.secretKey))
}

// ValidateToken 验证并解析JWT令牌
func (j *JWTService) ValidateToken(tokenString string) (*TokenClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &TokenClaims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名算法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("意外的签名方法: %v", token.Header["alg"])
		}
		return []byte(j.secretKey), nil
	})

	if err != nil {
		return nil, err
	}

	if !token.Valid {
		return nil, errors.New("无效的令牌")
	}

	claims, ok := token.Claims.(*TokenClaims)
	if !ok {
		return nil, errors.New("无法解析令牌声明")
	}

	return claims, nil
}
