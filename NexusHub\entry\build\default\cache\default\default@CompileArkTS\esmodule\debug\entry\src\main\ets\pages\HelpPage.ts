if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface HelpPage_Params {
    selectedTab?: number;
    helpItems?: HelpItem[];
    searchText?: string;
    feedbackType?: string;
    feedbackContent?: string;
    contactInfo?: string;
    loadingState?: LoadingState;
    deviceUtils?;
    feedbackTypes?: FeedbackType[];
}
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { DeviceUtils } from "@normalized:N&&&entry/src/main/ets/utils/DeviceUtils&";
import { LoadingView, LoadingState } from "@normalized:N&&&entry/src/main/ets/components/LoadingView&";
import hilog from "@ohos:hilog";
import { LengthMetrics } from "@ohos:arkui.node";
/**
 * 帮助项模型
 */
interface HelpItem {
    id: number;
    title: string;
    content: string;
    category: string;
    isExpanded?: boolean;
}
/**
 * 反馈类型
 */
interface FeedbackType {
    id: string;
    name: string;
    icon: string;
}
class HelpPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__selectedTab = new ObservedPropertySimplePU(0, this, "selectedTab");
        this.__helpItems = new ObservedPropertyObjectPU([], this, "helpItems");
        this.__searchText = new ObservedPropertySimplePU('', this, "searchText");
        this.__feedbackType = new ObservedPropertySimplePU('', this, "feedbackType");
        this.__feedbackContent = new ObservedPropertySimplePU('', this, "feedbackContent");
        this.__contactInfo = new ObservedPropertySimplePU('', this, "contactInfo");
        this.__loadingState = new ObservedPropertySimplePU(LoadingState.SUCCESS, this, "loadingState");
        this.deviceUtils = DeviceUtils.getInstance();
        this.feedbackTypes = [
            { id: 'bug', name: '功能异常', icon: '🐛' },
            { id: 'suggestion', name: '功能建议', icon: '💡' },
            { id: 'ui', name: '界面问题', icon: '🎨' },
            { id: 'performance', name: '性能问题', icon: '⚡' },
            { id: 'other', name: '其他问题', icon: '❓' }
        ];
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: HelpPage_Params) {
        if (params.selectedTab !== undefined) {
            this.selectedTab = params.selectedTab;
        }
        if (params.helpItems !== undefined) {
            this.helpItems = params.helpItems;
        }
        if (params.searchText !== undefined) {
            this.searchText = params.searchText;
        }
        if (params.feedbackType !== undefined) {
            this.feedbackType = params.feedbackType;
        }
        if (params.feedbackContent !== undefined) {
            this.feedbackContent = params.feedbackContent;
        }
        if (params.contactInfo !== undefined) {
            this.contactInfo = params.contactInfo;
        }
        if (params.loadingState !== undefined) {
            this.loadingState = params.loadingState;
        }
        if (params.deviceUtils !== undefined) {
            this.deviceUtils = params.deviceUtils;
        }
        if (params.feedbackTypes !== undefined) {
            this.feedbackTypes = params.feedbackTypes;
        }
    }
    updateStateVars(params: HelpPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__selectedTab.purgeDependencyOnElmtId(rmElmtId);
        this.__helpItems.purgeDependencyOnElmtId(rmElmtId);
        this.__searchText.purgeDependencyOnElmtId(rmElmtId);
        this.__feedbackType.purgeDependencyOnElmtId(rmElmtId);
        this.__feedbackContent.purgeDependencyOnElmtId(rmElmtId);
        this.__contactInfo.purgeDependencyOnElmtId(rmElmtId);
        this.__loadingState.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__selectedTab.aboutToBeDeleted();
        this.__helpItems.aboutToBeDeleted();
        this.__searchText.aboutToBeDeleted();
        this.__feedbackType.aboutToBeDeleted();
        this.__feedbackContent.aboutToBeDeleted();
        this.__contactInfo.aboutToBeDeleted();
        this.__loadingState.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __selectedTab: ObservedPropertySimplePU<number>; // 0: 帮助中心, 1: 意见反馈
    get selectedTab() {
        return this.__selectedTab.get();
    }
    set selectedTab(newValue: number) {
        this.__selectedTab.set(newValue);
    }
    private __helpItems: ObservedPropertyObjectPU<HelpItem[]>;
    get helpItems() {
        return this.__helpItems.get();
    }
    set helpItems(newValue: HelpItem[]) {
        this.__helpItems.set(newValue);
    }
    private __searchText: ObservedPropertySimplePU<string>;
    get searchText() {
        return this.__searchText.get();
    }
    set searchText(newValue: string) {
        this.__searchText.set(newValue);
    }
    private __feedbackType: ObservedPropertySimplePU<string>;
    get feedbackType() {
        return this.__feedbackType.get();
    }
    set feedbackType(newValue: string) {
        this.__feedbackType.set(newValue);
    }
    private __feedbackContent: ObservedPropertySimplePU<string>;
    get feedbackContent() {
        return this.__feedbackContent.get();
    }
    set feedbackContent(newValue: string) {
        this.__feedbackContent.set(newValue);
    }
    private __contactInfo: ObservedPropertySimplePU<string>;
    get contactInfo() {
        return this.__contactInfo.get();
    }
    set contactInfo(newValue: string) {
        this.__contactInfo.set(newValue);
    }
    private __loadingState: ObservedPropertySimplePU<LoadingState>;
    get loadingState() {
        return this.__loadingState.get();
    }
    set loadingState(newValue: LoadingState) {
        this.__loadingState.set(newValue);
    }
    private deviceUtils;
    private feedbackTypes: FeedbackType[];
    aboutToAppear() {
        this.loadHelpItems();
    }
    /**
     * 加载帮助项目
     */
    private loadHelpItems() {
        this.helpItems = [
            {
                id: 1,
                title: '如何下载应用？',
                content: '1. 在首页或分类页面找到您想要的应用\n2. 点击应用图标进入详情页面\n3. 点击"下载"按钮开始下载\n4. 下载完成后可在"我的下载"中查看',
                category: '下载安装',
                isExpanded: false
            },
            {
                id: 2,
                title: '如何管理我的下载？',
                content: '在个人中心页面点击"我的下载"，您可以：\n• 查看所有已下载的应用\n• 查看下载进度\n• 暂停或继续下载\n• 删除下载记录',
                category: '下载安装',
                isExpanded: false
            },
            {
                id: 3,
                title: '如何收藏应用？',
                content: '在应用详情页面点击❤️图标即可收藏应用。收藏的应用可以在个人中心的"我的收藏"中查看。',
                category: '收藏管理',
                isExpanded: false
            },
            {
                id: 4,
                title: '如何搜索应用？',
                content: '1. 点击首页顶部的搜索框\n2. 输入应用名称或关键词\n3. 点击搜索按钮或按回车键\n4. 浏览搜索结果',
                category: '搜索功能',
                isExpanded: false
            },
            {
                id: 5,
                title: '如何查看应用详情？',
                content: '点击任意应用图标或名称即可进入应用详情页面，您可以查看：\n• 应用介绍和截图\n• 用户评价和评分\n• 版本信息和更新日志\n• 开发者信息',
                category: '应用详情',
                isExpanded: false
            },
            {
                id: 6,
                title: '如何发表评论？',
                content: '1. 进入应用详情页面\n2. 滑动到评论区域\n3. 点击"写评论"按钮\n4. 选择评分并输入评论内容\n5. 点击"发布"按钮',
                category: '评论功能',
                isExpanded: false
            },
            {
                id: 7,
                title: '账号登录问题',
                content: '如果遇到登录问题，请检查：\n• 网络连接是否正常\n• 用户名和密码是否正确\n• 是否需要验证码\n如仍无法解决，请联系客服',
                category: '账号问题',
                isExpanded: false
            },
            {
                id: 8,
                title: '应用无法下载怎么办？',
                content: '请尝试以下解决方案：\n• 检查网络连接\n• 确保存储空间充足\n• 重启应用后重试\n• 清除应用缓存\n如问题持续，请联系技术支持',
                category: '常见问题',
                isExpanded: false
            }
        ];
    }
    /**
     * 获取过滤后的帮助项目
     */
    private getFilteredHelpItems(): HelpItem[] {
        if (!this.searchText.trim()) {
            return this.helpItems;
        }
        const keyword = this.searchText.toLowerCase();
        return this.helpItems.filter(item => item.title.toLowerCase().includes(keyword) ||
            item.content.toLowerCase().includes(keyword) ||
            item.category.toLowerCase().includes(keyword));
    }
    /**
     * 切换帮助项目展开状态
     */
    private toggleHelpItem(item: HelpItem) {
        const index = this.helpItems.findIndex(h => h.id === item.id);
        if (index !== -1) {
            this.helpItems[index].isExpanded = !this.helpItems[index].isExpanded;
        }
    }
    /**
     * 提交反馈
     */
    private async submitFeedback() {
        if (!this.feedbackType || !this.feedbackContent.trim()) {
            // 这里应该显示提示信息
            hilog.warn(0x0000, 'HelpPage', '请填写完整的反馈信息');
            return;
        }
        try {
            this.loadingState = LoadingState.LOADING;
            // 这里应该调用API提交反馈
            // 暂时模拟提交过程
            await new Promise<void>(resolve => setTimeout(resolve, 2000));
            // 清空表单
            this.feedbackType = '';
            this.feedbackContent = '';
            this.contactInfo = '';
            this.loadingState = LoadingState.SUCCESS;
            // 这里应该显示成功提示
            hilog.info(0x0000, 'HelpPage', '反馈提交成功');
        }
        catch (error) {
            hilog.error(0x0000, 'HelpPage', '提交反馈失败: %{public}s', JSON.stringify(error));
            this.loadingState = LoadingState.ERROR;
        }
    }
    /**
     * 标签栏
     */
    private TabBar(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.width('100%');
            Row.backgroundColor(Constants.COLORS.WHITE);
            Row.justifyContent(FlexAlign.SpaceAround);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const title = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(title);
                    Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                    Text.fontColor(this.selectedTab === index ? Constants.COLORS.PRIMARY : Constants.COLORS.TEXT_SECONDARY);
                    Text.fontWeight(this.selectedTab === index ? FontWeight.Bold : FontWeight.Normal);
                    Text.padding({ left: 16, right: 16, top: 12, bottom: 12 });
                    Text.onClick(() => {
                        this.selectedTab = index;
                    });
                }, Text);
                Text.pop();
            };
            this.forEachUpdateFunction(elmtId, ['帮助中心', '意见反馈'], forEachItemGenFunction, (title: string) => title, true, false);
        }, ForEach);
        ForEach.pop();
        Row.pop();
    }
    /**
     * 搜索框
     */
    private SearchBox(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 8 });
            Row.width('100%');
            Row.height(40);
            Row.padding({ left: 12, right: 12 });
            Row.backgroundColor(Constants.COLORS.BACKGROUND_LIGHT);
            Row.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            Row.margin({ left: 16, right: 16, top: 12, bottom: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🔍');
            Text.fontSize(16);
            Text.fontColor(Constants.COLORS.TEXT_HINT);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '搜索帮助内容...', text: this.searchText });
            TextInput.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            TextInput.fontColor(Constants.COLORS.TEXT_PRIMARY);
            TextInput.backgroundColor('transparent');
            TextInput.border({ width: 0 });
            TextInput.layoutWeight(1);
            TextInput.onChange((value: string) => {
                this.searchText = value;
            });
        }, TextInput);
        Row.pop();
    }
    /**
     * 帮助项目
     */
    private HelpItemView(item: HelpItem, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width('100%');
            Column.padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM));
            Column.backgroundColor(Constants.COLORS.WHITE);
            Column.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            Column.margin({ left: 16, right: 16, bottom: 8 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 12 });
            Row.width('100%');
            Row.onClick(() => {
                this.toggleHelpItem(item);
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 4 });
            Column.alignItems(HorizontalAlign.Start);
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(item.title);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.fontWeight(FontWeight.Medium);
            Text.width('100%');
            Text.textAlign(TextAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(item.category);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.PRIMARY);
            Text.backgroundColor({ "id": 16777232, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Text.padding({ left: 8, right: 8, top: 2, bottom: 2 });
            Text.borderRadius(Constants.BORDER_RADIUS.SMALL);
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(item.isExpanded ? '▲' : '▼');
            Text.fontSize(12);
            Text.fontColor(Constants.COLORS.TEXT_HINT);
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (item.isExpanded) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(item.content);
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                        Text.lineHeight(20);
                        Text.width('100%');
                        Text.margin({ top: 12 });
                        Text.textAlign(TextAlign.Start);
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    /**
     * 帮助中心内容
     */
    private HelpContent(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.layoutWeight(1);
        }, Column);
        this.SearchBox.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.scrollable(ScrollDirection.Vertical);
            Scroll.scrollBar(BarState.Auto);
            Scroll.layoutWeight(1);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.getFilteredHelpItems().length === 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create({ space: 16 });
                        Column.width('100%');
                        Column.height(200);
                        Column.justifyContent(FlexAlign.Center);
                        Column.alignItems(HorizontalAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('❓');
                        Text.fontSize(48);
                        Text.fontColor(Constants.COLORS.TEXT_HINT);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('未找到相关帮助内容');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('请尝试其他关键词或联系客服');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                        Text.fontColor(Constants.COLORS.TEXT_HINT);
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const item = _item;
                            this.HelpItemView.bind(this)(item);
                        };
                        this.forEachUpdateFunction(elmtId, this.getFilteredHelpItems(), forEachItemGenFunction, (item: HelpItem) => item.id.toString(), false, false);
                    }, ForEach);
                    ForEach.pop();
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 底部间距
            Column.create();
            // 底部间距
            Column.height(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE));
        }, Column);
        // 底部间距
        Column.pop();
        Column.pop();
        Scroll.pop();
        Column.pop();
    }
    /**
     * 反馈类型选择
     */
    private FeedbackTypeSelector(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 8 });
            Column.width('100%');
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('反馈类型 *');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.fontWeight(FontWeight.Medium);
            Text.width('100%');
            Text.textAlign(TextAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Flex.create({ wrap: FlexWrap.Wrap, space: { main: LengthMetrics.vp(8), cross: LengthMetrics.vp(8) } });
            Flex.width('100%');
        }, Flex);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const type = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create({ space: 6 });
                    Row.padding({ left: 12, right: 12, top: 8, bottom: 8 });
                    Row.backgroundColor(this.feedbackType === type.id ? Constants.COLORS.PRIMARY : { "id": 16777231, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                    Row.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
                    Row.onClick(() => {
                        this.feedbackType = type.id;
                    });
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(type.icon);
                    Text.fontSize(16);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(type.name);
                    Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                    Text.fontColor(this.feedbackType === type.id ? Constants.COLORS.WHITE : Constants.COLORS.TEXT_SECONDARY);
                }, Text);
                Text.pop();
                Row.pop();
            };
            this.forEachUpdateFunction(elmtId, this.feedbackTypes, forEachItemGenFunction, (type: FeedbackType) => type.id.toString(), false, false);
        }, ForEach);
        ForEach.pop();
        Flex.pop();
        Column.pop();
    }
    /**
     * 意见反馈内容
     */
    private FeedbackContent(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.scrollable(ScrollDirection.Vertical);
            Scroll.scrollBar(BarState.Auto);
            Scroll.layoutWeight(1);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 16 });
            Column.padding(16);
        }, Column);
        // 反馈类型
        this.FeedbackTypeSelector.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 反馈内容
            Column.create({ space: 8 });
            // 反馈内容
            Column.width('100%');
            // 反馈内容
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('问题描述 *');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.fontWeight(FontWeight.Medium);
            Text.width('100%');
            Text.textAlign(TextAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextArea.create({ placeholder: '请详细描述您遇到的问题或建议...', text: this.feedbackContent });
            TextArea.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            TextArea.fontColor(Constants.COLORS.TEXT_PRIMARY);
            TextArea.backgroundColor(Constants.COLORS.BACKGROUND_LIGHT);
            TextArea.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            TextArea.height(120);
            TextArea.onChange((value: string) => {
                this.feedbackContent = value;
            });
        }, TextArea);
        // 反馈内容
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 联系方式
            Column.create({ space: 8 });
            // 联系方式
            Column.width('100%');
            // 联系方式
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('联系方式（可选）');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.fontWeight(FontWeight.Medium);
            Text.width('100%');
            Text.textAlign(TextAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入您的邮箱或手机号，以便我们联系您', text: this.contactInfo });
            TextInput.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            TextInput.fontColor(Constants.COLORS.TEXT_PRIMARY);
            TextInput.backgroundColor(Constants.COLORS.BACKGROUND_LIGHT);
            TextInput.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            TextInput.onChange((value: string) => {
                this.contactInfo = value;
            });
        }, TextInput);
        // 联系方式
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 提交按钮
            Button.createWithLabel('提交反馈');
            // 提交按钮
            Button.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            // 提交按钮
            Button.fontColor(Constants.COLORS.WHITE);
            // 提交按钮
            Button.backgroundColor(Constants.COLORS.PRIMARY);
            // 提交按钮
            Button.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            // 提交按钮
            Button.width('100%');
            // 提交按钮
            Button.height(48);
            // 提交按钮
            Button.enabled(this.feedbackType !== '' && this.feedbackContent.trim() !== '');
            // 提交按钮
            Button.opacity(this.feedbackType !== '' && this.feedbackContent.trim() !== '' ? 1 : 0.5);
            // 提交按钮
            Button.onClick(() => {
                this.submitFeedback();
            });
        }, Button);
        // 提交按钮
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 底部间距
            Column.create();
            // 底部间距
            Column.height(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE));
        }, Column);
        // 底部间距
        Column.pop();
        Column.pop();
        Scroll.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor(Constants.COLORS.BACKGROUND);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 标题栏
            Row.create();
            // 标题栏
            Row.width('100%');
            // 标题栏
            Row.height(56);
            // 标题栏
            Row.padding({ left: 16, right: 16 });
            // 标题栏
            Row.backgroundColor(Constants.COLORS.WHITE);
            // 标题栏
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('←');
            Text.fontSize(24);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.onClick(() => {
                this.getUIContext().getRouter().back();
            });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('帮助与反馈');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.fontWeight(FontWeight.Bold);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.width(24);
        }, Text);
        Text.pop();
        // 标题栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.loadingState === LoadingState.LOADING) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, { state: LoadingState.LOADING }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/HelpPage.ets", line: 467, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.LOADING
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.LOADING
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.layoutWeight(1);
                    }, Column);
                    // 标签栏
                    this.TabBar.bind(this)();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Divider.create();
                        Divider.color(Constants.COLORS.BORDER);
                    }, Divider);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        // 内容区域
                        if (this.selectedTab === 0) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.HelpContent.bind(this)();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                                this.FeedbackContent.bind(this)();
                            });
                        }
                    }, If);
                    If.pop();
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "HelpPage";
    }
}
export { HelpPage };
registerNamedRoute(() => new HelpPage(undefined, {}), "", { bundleName: "com.example.nexushub", moduleName: "entry", pagePath: "pages/HelpPage", pageFullPath: "entry/src/main/ets/pages/HelpPage", integratedHsp: "false", moduleType: "followWithHap" });
