package api

import (
	"strconv"

	"nexushub-oh-back/internal/models"
	"nexushub-oh-back/internal/services"
	"nexushub-oh-back/pkg/logger"
	"nexushub-oh-back/pkg/response"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// NotificationController 通知控制器
type NotificationController struct {
	notificationService *services.NotificationService
}

// NewNotificationController 创建通知控制器实例
func NewNotificationController(notificationService *services.NotificationService) *NotificationController {
	return &NotificationController{
		notificationService: notificationService,
	}
}

// GetNotificationsRequest 获取通知列表请求
type GetNotificationsRequest struct {
	Page       int  `form:"page,default=1" binding:"min=1"`
	PageSize   int  `form:"page_size,default=20" binding:"min=1,max=100"`
	UnreadOnly bool `form:"unread_only,default=false"`
}

// GetNotificationsResponse 获取通知列表响应
type GetNotificationsResponse struct {
	Notifications []models.Notification `json:"notifications"`
	Total         int64                 `json:"total"`
	Page          int                   `json:"page"`
	PageSize      int                   `json:"page_size"`
	UnreadCount   int64                 `json:"unread_count"`
}

// GetNotifications 获取用户通知列表
//
//	@Summary		获取用户通知列表
//	@Description	获取当前用户的通知列表，支持分页和筛选未读通知
//	@Tags			通知管理
//	@Accept			json
//	@Produce		json
//	@Param			page		query		int										false	"页码"	default(1)
//	@Param			page_size	query		int										false	"每页数量"	default(20)
//	@Param			unread_only	query		bool									false	"仅显示未读"	default(false)
//	@Success		200			{object}	response.Response{data=GetNotificationsResponse}	"获取成功"
//	@Failure		400			{object}	response.Response												"请求参数错误"
//	@Failure		401			{object}	response.Response												"未授权"
//	@Failure		500			{object}	response.Response												"服务器错误"
//	@Router			/api/v1/notifications [get]
func (nc *NotificationController) GetNotifications(c *gin.Context) {
	// 获取当前用户ID
	userID := GetCurrentUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "用户未登录")
		return
	}

	// 绑定请求参数
	var req GetNotificationsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequest(c, "请求参数错误")
		return
	}

	// 获取通知列表
	notifications, total, err := nc.notificationService.GetUserNotifications(userID, req.Page, req.PageSize, req.UnreadOnly)
	if err != nil {
		logger.Error("获取通知列表失败", zap.Error(err), zap.Uint("user_id", userID))
		response.InternalServerError(c, "获取通知列表失败")
		return
	}

	// 获取未读通知数量
	unreadCount, err := nc.notificationService.GetUnreadCount(userID)
	if err != nil {
		logger.Error("获取未读通知数量失败", zap.Error(err), zap.Uint("user_id", userID))
		unreadCount = 0 // 不影响主要功能
	}

	responseData := GetNotificationsResponse{
		Notifications: notifications,
		Total:         total,
		Page:          req.Page,
		PageSize:      req.PageSize,
		UnreadCount:   unreadCount,
	}

	response.SuccessWithMessage(c, "获取通知列表成功", responseData)
}

// MarkAsReadRequest 标记已读请求
type MarkAsReadRequest struct {
	NotificationID uint `json:"notification_id" binding:"required"`
}

// GetCurrentUserID 获取当前用户ID
func GetCurrentUserID(c *gin.Context) uint {
	// 从JWT token中获取用户ID
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(uint); ok {
			return id
		}
	}
	return 0
}

// MarkAsRead 标记通知为已读
//
//	@Summary		标记通知为已读
//	@Description	标记指定通知为已读状态
//	@Tags			通知管理
//	@Accept			json
//	@Produce		json
//	@Param			request	body		MarkAsReadRequest	true	"标记已读请求"
//	@Success		200		{object}	response.Response			"标记成功"
//	@Failure		400		{object}	response.Response			"请求参数错误"
//	@Failure		401		{object}	response.Response			"未授权"
//	@Failure		500		{object}	response.Response			"服务器错误"
//	@Router			/api/v1/notifications/read [post]
func (nc *NotificationController) MarkAsRead(c *gin.Context) {
	// 获取当前用户ID
	userID := GetCurrentUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "用户未登录")
		return
	}

	// 绑定请求参数
	var req MarkAsReadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误")
		return
	}

	// 标记为已读
	if err := nc.notificationService.MarkAsRead(req.NotificationID, userID); err != nil {
		logger.Error("标记通知已读失败", zap.Error(err),
			zap.Uint("user_id", userID),
			zap.Uint("notification_id", req.NotificationID),
		)
		response.InternalServerError(c, "标记通知已读失败")
		return
	}

	response.SuccessWithMessage(c, "标记已读成功", nil)
}

// MarkAllAsRead 标记所有通知为已读
//
//	@Summary		标记所有通知为已读
//	@Description	标记当前用户的所有未读通知为已读状态
//	@Tags			通知管理
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	response.Response	"标记成功"
//	@Failure		401	{object}	response.Response	"未授权"
//	@Failure		500	{object}	response.Response	"服务器错误"
//	@Router			/api/v1/notifications/read-all [post]
func (nc *NotificationController) MarkAllAsRead(c *gin.Context) {
	// 获取当前用户ID
	userID := GetCurrentUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "用户未登录")
		return
	}

	// 标记所有为已读
	if err := nc.notificationService.MarkAllAsRead(userID); err != nil {
		logger.Error("标记所有通知已读失败", zap.Error(err), zap.Uint("user_id", userID))
		response.InternalServerError(c, "标记所有通知已读失败")
		return
	}

	response.SuccessWithMessage(c, "标记所有通知已读成功", nil)
}

// GetUnreadCount 获取未读通知数量
//
//	@Summary		获取未读通知数量
//	@Description	获取当前用户的未读通知数量
//	@Tags			通知管理
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	response.Response{data=map[string]int64}	"获取成功"
//	@Failure		401	{object}	response.Response												"未授权"
//	@Failure		500	{object}	response.Response												"服务器错误"
//	@Router			/api/v1/notifications/unread-count [get]
func (nc *NotificationController) GetUnreadCount(c *gin.Context) {
	// 获取当前用户ID
	userID := GetCurrentUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "用户未登录")
		return
	}

	// 获取未读数量
	count, err := nc.notificationService.GetUnreadCount(userID)
	if err != nil {
		logger.Error("获取未读通知数量失败", zap.Error(err), zap.Uint("user_id", userID))
		response.InternalServerError(c, "获取未读通知数量失败")
		return
	}

	response.SuccessWithMessage(c, "获取未读通知数量成功", map[string]int64{"count": count})
}

// GetNotificationSettings 获取通知设置
//
//	@Summary		获取通知设置
//	@Description	获取当前用户的通知设置
//	@Tags			通知管理
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	response.Response{data=models.NotificationSettings}	"获取成功"
//	@Failure		401	{object}	response.Response													"未授权"
//	@Failure		500	{object}	response.Response													"服务器错误"
//	@Router			/api/v1/notifications/settings [get]
func (nc *NotificationController) GetNotificationSettings(c *gin.Context) {
	// 获取当前用户ID
	userID := GetCurrentUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "用户未登录")
		return
	}

	// 获取通知设置
	settings, err := nc.notificationService.GetNotificationSettings(userID)
	if err != nil {
		logger.Error("获取通知设置失败", zap.Error(err), zap.Uint("user_id", userID))
		response.InternalServerError(c, "获取通知设置失败")
		return
	}

	response.SuccessWithMessage(c, "获取通知设置成功", settings)
}

// UpdateNotificationSettingsRequest 更新通知设置请求
type UpdateNotificationSettingsRequest struct {
	WebNotificationEnabled      bool `json:"web_notification_enabled"`
	EmailNotificationEnabled    bool `json:"email_notification_enabled"`
	AppReviewWebEnabled         bool `json:"app_review_web_enabled"`
	AppReviewEmailEnabled       bool `json:"app_review_email_enabled"`
	DeveloperVerifyWebEnabled   bool `json:"developer_verify_web_enabled"`
	DeveloperVerifyEmailEnabled bool `json:"developer_verify_email_enabled"`
	VersionUpdateWebEnabled     bool `json:"version_update_web_enabled"`
	VersionUpdateEmailEnabled   bool `json:"version_update_email_enabled"`
	SystemWebEnabled            bool `json:"system_web_enabled"`
	SystemEmailEnabled          bool `json:"system_email_enabled"`
}

// UpdateNotificationSettings 更新通知设置
//
//	@Summary		更新通知设置
//	@Description	更新当前用户的通知设置
//	@Tags			通知管理
//	@Accept			json
//	@Produce		json
//	@Param			request	body		UpdateNotificationSettingsRequest	true	"通知设置"
//	@Success		200		{object}	response.Response												"更新成功"
//	@Failure		400		{object}	response.Response							"请求参数错误"
//	@Failure		401		{object}	response.Response							"未授权"
//	@Failure		500		{object}	response.Response							"服务器错误"
//	@Router			/api/v1/notifications/settings [put]
func (nc *NotificationController) UpdateNotificationSettings(c *gin.Context) {
	// 获取当前用户ID
	userID := GetCurrentUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "用户未登录")
		return
	}

	// 绑定请求参数
	var req UpdateNotificationSettingsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误")
		return
	}

	// 构建设置对象
	settings := &models.NotificationSettings{
		WebNotificationEnabled:      req.WebNotificationEnabled,
		EmailNotificationEnabled:    req.EmailNotificationEnabled,
		AppReviewWebEnabled:         req.AppReviewWebEnabled,
		AppReviewEmailEnabled:       req.AppReviewEmailEnabled,
		DeveloperVerifyWebEnabled:   req.DeveloperVerifyWebEnabled,
		DeveloperVerifyEmailEnabled: req.DeveloperVerifyEmailEnabled,
		VersionUpdateWebEnabled:     req.VersionUpdateWebEnabled,
		VersionUpdateEmailEnabled:   req.VersionUpdateEmailEnabled,
		SystemWebEnabled:            req.SystemWebEnabled,
		SystemEmailEnabled:          req.SystemEmailEnabled,
	}

	// 更新设置
	if err := nc.notificationService.UpdateNotificationSettings(userID, settings); err != nil {
		logger.Error("更新通知设置失败", zap.Error(err), zap.Uint("user_id", userID))
		response.InternalServerError(c, "更新通知设置失败")
		return
	}

	response.SuccessWithMessage(c, "更新通知设置成功", nil)
}

// DeleteNotification 删除通知
//
//	@Summary		删除通知
//	@Description	删除指定的通知
//	@Tags			通知管理
//	@Accept			json
//	@Produce		json
//	@Param			id	path		int			true	"通知ID"
//	@Success		200	{object}	response.Response	"删除成功"
//	@Failure		400	{object}	response.Response	"请求参数错误"
//	@Failure		401	{object}	response.Response	"未授权"
//	@Failure		404	{object}	response.Response	"通知不存在"
//	@Failure		500	{object}	response.Response	"服务器错误"
//	@Router			/api/v1/notifications/{id} [delete]
func (nc *NotificationController) DeleteNotification(c *gin.Context) {
	// 获取当前用户ID
	userID := GetCurrentUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "用户未登录")
		return
	}

	// 获取通知ID
	notificationIDStr := c.Param("id")
	notificationID, err := strconv.ParseUint(notificationIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的通知ID")
		return
	}

	// 删除通知（软删除，只能删除属于当前用户的通知）
	result := nc.notificationService.GetDB().Where("id = ? AND user_id = ?", notificationID, userID).Delete(&models.Notification{})
	if result.Error != nil {
		logger.Error("删除通知失败", zap.Error(result.Error),
			zap.Uint("user_id", userID),
			zap.Uint64("notification_id", notificationID),
		)
		response.InternalServerError(c, "删除通知失败")
		return
	}

	if result.RowsAffected == 0 {
		response.NotFound(c, "通知不存在或无权限删除")
		return
	}

	response.SuccessWithMessage(c, "删除通知成功", nil)
}
