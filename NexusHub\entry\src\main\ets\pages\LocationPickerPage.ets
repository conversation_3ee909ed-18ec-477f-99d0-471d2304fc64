import { Constants } from '../utils/Constants';
import { ApiService } from '../services/ApiService';
import { LoadingView, LoadingState } from '../components/LoadingView';
import { router } from '@kit.ArkUI';
import { hilog } from '@kit.PerformanceAnalysisKit';

// 定义响应数据接口
interface ResponseData {
  code?: number;
  message?: string;
  data?: Object;
}

/**
 * 地理位置数据模型
 */
interface LocationModel {
  id: number;
  name: string;
  code?: string;
  parent_id?: number;
}

/**
 * 地理位置列表响应数据模型
 */
interface LocationListData {
  locations?: LocationModel[];
}

/**
 * 页面参数
 */
interface LocationPickerParams {
  type: 'country' | 'province' | 'city' | 'district' | 'street';
  parentId?: number;
  selectedLocation?: LocationModel;
  title?: string;
}

/**
 * 地理位置选择页面
 */
@Entry
@Component
struct LocationPickerPage {
  @State locations: LocationModel[] = [];
  @State loadingState: LoadingState = LoadingState.LOADING;
  @State selectedLocation: LocationModel | null = null;
  @State searchKeyword: string = '';
  @State filteredLocations: LocationModel[] = [];
  @State showSearch: boolean = false;

  private apiService = ApiService.getInstance();
  private pageParams: LocationPickerParams = {
    type: 'country'
  };

  aboutToAppear() {
    // 获取页面参数
    const params = this.getUIContext().getRouter().getParams() as LocationPickerParams;
    if (params) {
      this.pageParams = params;
      this.selectedLocation = params.selectedLocation || null;
    }
    
    this.loadLocations();
  }

  /**
   * 加载地理位置数据
   */
  private async loadLocations(): Promise<void> {
    try {
      this.loadingState = LoadingState.LOADING;
      let response: ResponseData;
      
      switch (this.pageParams.type) {
        case 'country':
          response = await this.apiService.getCountries();
          break;
        case 'province':
          response = await this.apiService.getProvinces(this.pageParams.parentId!.toString());
          break;
        case 'city':
          response = await this.apiService.getCities(this.pageParams.parentId!.toString());
          break;
        case 'district':
          response = await this.apiService.getDistricts(this.pageParams.parentId!.toString());
          break;
        case 'street':
          response = await this.apiService.getStreets(this.pageParams.parentId!.toString());
          break;
        default:
          response = await this.apiService.getCountries();
      }
      
      if (response.code === 200 && response.data) {
        this.locations = (response.data as LocationListData).locations || (response.data as LocationModel[]);
        this.filteredLocations = [...this.locations];
        this.loadingState = LoadingState.SUCCESS;
      } else {
        this.loadingState = LoadingState.ERROR;
      }
    } catch (error) {
      hilog.error(0x0000, 'LocationPickerPage', '加载地理位置数据失败: %{public}s', JSON.stringify(error));
      this.loadingState = LoadingState.ERROR;
    }
  }

  /**
   * 搜索地理位置
   */
  private searchLocations() {
    if (!this.searchKeyword.trim()) {
      this.filteredLocations = [...this.locations];
      return;
    }
    
    const keyword = this.searchKeyword.toLowerCase();
    this.filteredLocations = this.locations.filter(location => 
      location.name.toLowerCase().includes(keyword) ||
      (location.code && location.code.toLowerCase().includes(keyword))
    );
  }

  /**
   * 选择地理位置
   */
  private selectLocation(location: LocationModel) {
    this.selectedLocation = location;
    
    // 返回选择结果
    this.getUIContext().getRouter().back({
      url: '',
      params: {
        selectedLocation: location,
        type: this.pageParams.type
      }
    });
  }

  /**
   * 获取页面标题
   */
  private getPageTitle(): string {
    if (this.pageParams.title) {
      return this.pageParams.title;
    }
    
    switch (this.pageParams.type) {
      case 'country':
        return '选择国家';
      case 'province':
        return '选择省份';
      case 'city':
        return '选择城市';
      case 'district':
        return '选择区县';
      case 'street':
        return '选择街道';
      default:
        return '选择位置';
    }
  }

  build() {
    Column() {
      // 标题栏
      Row() {
        Button() {
          Image($r('app.media.ic_back'))
            .width(24)
            .height(24)
            .fillColor(Constants.COLORS.TEXT_PRIMARY)
        }
        .width(40)
        .height(40)
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          this.getUIContext().getRouter().back();
        })

        Text(this.getPageTitle())
          .fontSize(Constants.FONT_SIZE.LARGE)
          .fontWeight(FontWeight.Medium)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Button() {
          Image($r('app.media.ic_search'))
            .width(24)
            .height(24)
            .fillColor(this.showSearch ? Constants.COLORS.PRIMARY : Constants.COLORS.TEXT_PRIMARY)
        }
        .width(40)
        .height(40)
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          this.showSearch = !this.showSearch;
          if (!this.showSearch) {
            this.searchKeyword = '';
            this.searchLocations();
          }
        })
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor(Constants.COLORS.WHITE)

      // 搜索框
      if (this.showSearch) {
        Row() {
          TextInput({ placeholder: '搜索位置', text: this.searchKeyword })
            .layoutWeight(1)
            .fontSize(Constants.FONT_SIZE.NORMAL)
            .backgroundColor(Constants.COLORS.BACKGROUND_LIGHT)
            .borderRadius(8)
            .padding({ left: 12, right: 12 })
            .onChange((value: string) => {
              this.searchKeyword = value;
              this.searchLocations();
            })
          
          if (this.searchKeyword) {
            Button() {
              Image($r('app.media.ic_close'))
                .width(20)
                .height(20)
                .fillColor(Constants.COLORS.TEXT_HINT)
            }
            .width(32)
            .height(32)
            .backgroundColor(Color.Transparent)
            .margin({ left: 8 })
            .onClick(() => {
              this.searchKeyword = '';
              this.searchLocations();
            })
          }
        }
        .width('100%')
        .padding(16)
        .backgroundColor(Constants.COLORS.WHITE)
      }

      // 内容区域
      if (this.loadingState === LoadingState.LOADING) {
        LoadingView()
          .layoutWeight(1)
      } else if (this.loadingState === LoadingState.ERROR) {
        Column() {
          Image($r('app.media.ic_error'))
            .width(64)
            .height(64)
            .fillColor(Constants.COLORS.TEXT_HINT)
            .margin({ bottom: 16 })
          
          Text('加载失败')
            .fontSize(Constants.FONT_SIZE.MEDIUM)
            .fontColor(Constants.COLORS.TEXT_SECONDARY)
            .margin({ bottom: 16 })
          
          Button('重试')
            .fontSize(Constants.FONT_SIZE.NORMAL)
            .fontColor(Constants.COLORS.WHITE)
            .backgroundColor(Constants.COLORS.PRIMARY)
            .borderRadius(8)
            .padding({ left: 24, right: 24, top: 8, bottom: 8 })
            .onClick(() => {
              this.loadLocations();
            })
        }
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
      } else if (!this.filteredLocations || this.filteredLocations.length === 0) {
        Column() {
          Image($r('app.media.ic_empty'))
            .width(120)
            .height(120)
            .fillColor(Constants.COLORS.TEXT_HINT)
            .margin({ bottom: 16 })
          
          Text(this.searchKeyword ? '未找到相关位置' : '暂无数据')
            .fontSize(Constants.FONT_SIZE.MEDIUM)
            .fontColor(Constants.COLORS.TEXT_SECONDARY)
        }
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
      } else {
        List() {
          ForEach(this.filteredLocations, (location: LocationModel) => {
            ListItem() {
              Row() {
                Column() {
                  Text(location.name)
                    .fontSize(Constants.FONT_SIZE.NORMAL)
                    .fontColor(Constants.COLORS.TEXT_PRIMARY)
                    .fontWeight(FontWeight.Medium)
                    .margin({ bottom: location.code ? 4 : 0 })
                  
                  if (location.code) {
                    Text(location.code)
                      .fontSize(Constants.FONT_SIZE.SMALL)
                      .fontColor(Constants.COLORS.TEXT_HINT)
                  }
                }
                .layoutWeight(1)
                .alignItems(HorizontalAlign.Start)
                
                if (this.selectedLocation && this.selectedLocation.id === location.id) {
                  Image($r('app.media.ic_check'))
                    .width(24)
                    .height(24)
                    .fillColor(Constants.COLORS.PRIMARY)
                }
              }
              .width('100%')
              .padding(16)
              .backgroundColor(Constants.COLORS.WHITE)
            }
            .onClick(() => {
              this.selectLocation(location);
            })
          })
        }
        .layoutWeight(1)
        .divider({
          strokeWidth: 1,
          color: Constants.COLORS.BORDER,
          startMargin: 16,
          endMargin: 16
        })
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Constants.COLORS.BACKGROUND)
  }
}