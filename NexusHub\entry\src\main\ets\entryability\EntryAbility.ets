import { UIAbility, Want, AbilityConstant, ConfigurationConstant } from '@kit.AbilityKit';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { window } from '@kit.ArkUI';
import { preferences } from '@kit.ArkData';
import { Constants } from '../utils/Constants';

const DOMAIN = 0x0000;

export default class EntryAbility extends UIAbility {
  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    this.initializeColorMode();
    hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onCreate');
  }

  /**
   * 初始化颜色模式设置
   */
  private async initializeColorMode(): Promise<void> {
    try {
      const dataPreferences = await preferences.getPreferences(this.context, 'app_settings');
      
      // 获取深色模式跟随系统设置
      const followSystem = await dataPreferences.get(Constants.STORAGE_KEYS.DARK_MODE_FOLLOW_SYSTEM, true) as boolean;
      
      if (followSystem) {
        // 跟随系统设置
        this.context.getApplicationContext().setColorMode(ConfigurationConstant.ColorMode.COLOR_MODE_NOT_SET);
        hilog.info(DOMAIN, 'EntryAbility', '深色模式设置为跟随系统');
      } else {
        // 手动设置
        const manualDarkMode = await dataPreferences.get(Constants.STORAGE_KEYS.DARK_MODE_MANUAL, false) as boolean;
        if (manualDarkMode) {
          this.context.getApplicationContext().setColorMode(ConfigurationConstant.ColorMode.COLOR_MODE_DARK);
          hilog.info(DOMAIN, 'EntryAbility', '深色模式设置为手动开启');
        } else {
          this.context.getApplicationContext().setColorMode(ConfigurationConstant.ColorMode.COLOR_MODE_LIGHT);
          hilog.info(DOMAIN, 'EntryAbility', '深色模式设置为手动关闭');
        }
      }
    } catch (error) {
      hilog.error(DOMAIN, 'EntryAbility', '初始化颜色模式失败: %{public}s', JSON.stringify(error));
      // 默认跟随系统
      this.context.getApplicationContext().setColorMode(ConfigurationConstant.ColorMode.COLOR_MODE_NOT_SET);
    }
  }

  onDestroy(): void {
    hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onDestroy');
  }

  onWindowStageCreate(windowStage: window.WindowStage): void {
    // Main window is created, set main page for this ability
    hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onWindowStageCreate');

    // 加载欢迎页面作为启动页面
    windowStage.loadContent('pages/WelcomePage', (err) => {
      if (err.code) {
        hilog.error(DOMAIN, 'testTag', 'Failed to load the welcome page. Cause: %{public}s', JSON.stringify(err));
        // 如果欢迎页面加载失败，回退到主页面
        windowStage.loadContent('pages/Index', (fallbackErr) => {
          if (fallbackErr.code) {
            hilog.error(DOMAIN, 'testTag', 'Failed to load fallback page. Cause: %{public}s', JSON.stringify(fallbackErr));
          } else {
            hilog.info(DOMAIN, 'testTag', 'Fallback to main page succeeded.');
          }
        });
        return;
      }
      hilog.info(DOMAIN, 'testTag', 'Welcome page loaded successfully.');
    });
  }

  onWindowStageDestroy(): void {
    // Main window is destroyed, release UI related resources
    hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onWindowStageDestroy');
  }

  onForeground(): void {
    // Ability has brought to foreground
    hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onForeground');
  }

  onBackground(): void {
    // Ability has back to background
    hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onBackground');
  }
}