import { Constants } from '../utils/Constants';
import { ApiService } from '../services/ApiService';
import { LoadingView, LoadingState } from '../components/LoadingView';
import { router } from '@kit.ArkUI';
import { hilog } from '@kit.PerformanceAnalysisKit';

/**
 * 系统健康状态模型
 */
interface SystemHealth {
  status: string;
  timestamp: string;
  services: ServiceStatus[];
  metrics: SystemMetrics;
}

/**
 * 服务状态模型
 */
interface ServiceStatus {
  name: string;
  status: string;
  response_time?: number;
  last_check: string;
  message?: string;
}

/**
 * 系统指标模型
 */
interface SystemMetrics {
  cpu_usage?: number;
  memory_usage?: number;
  disk_usage?: number;
  active_connections?: number;
  requests_per_minute?: number;
}

/**
 * 功能配置类 - 使用Record类型替代索引签名
 */
type FeatureConfig = Record<string, boolean>;

/**
 * 限制配置类 - 使用Record类型替代索引签名
 */
type LimitConfig = Record<string, number>;

/**
 * 系统配置模型
 */
interface SystemConfig {
  app_name: string;
  version: string;
  environment: string;
  maintenance_mode: boolean;
  features: FeatureConfig;
  limits: LimitConfig;
}

/**
 * 状态颜色映射类
 */
class StatusColorMap {
  healthy: string | Resource = $r('app.color.status_success');
  up: string | Resource = $r('app.color.status_success');
  warning: string | Resource = $r('app.color.status_warning');
  degraded: string | Resource = $r('app.color.status_warning');
  error: string | Resource = $r('app.color.status_error');
  down: string | Resource = $r('app.color.status_error');
}

/**
 * 状态文本映射类
 */
class StatusTextMap {
  healthy: string = '健康';
  warning: string = '警告';
  error: string = '错误';
  up: string = '正常';
  down: string = '离线';
  degraded: string = '降级';
}

/**
 * 功能条目类
 */
class FeatureEntry {
  key: string = '';
  value: boolean = false;
}

/**
 * 限制条目类
 */
class LimitEntry {
  key: string = '';
  value: number = 0;
}

/**
 * 系统状态页面
 */
@Entry
@Component
struct SystemStatusPage {
  @State healthData: SystemHealth | null = null;
  @State configData: SystemConfig | null = null;
  @State loadingState: LoadingState = LoadingState.LOADING;
  @State selectedTab: number = 0;
  @State autoRefresh: boolean = false;
  @State refreshInterval: number = 30; // 秒

  private apiService = ApiService.getInstance();
  private refreshTimer: number = -1;

  /**
   * 获取功能开关条目
   */
  private getFeatureEntries(): FeatureEntry[] {
    if (!this.configData?.features) return [];
    const entries: FeatureEntry[] = [];
    const keys = Object.keys(this.configData.features);
    for (let i = 0; i < keys.length; i++) {
      const key = keys[i];
      const entry = new FeatureEntry();
      entry.key = key;
      entry.value = this.configData.features[key];
      entries.push(entry);
    }
    return entries;
  }

  /**
   * 获取系统限制条目
   */
  private getLimitEntries(): LimitEntry[] {
    if (!this.configData?.limits) return [];
    const entries: LimitEntry[] = [];
    const keys = Object.keys(this.configData.limits);
    for (let i = 0; i < keys.length; i++) {
      const key = keys[i];
      const entry = new LimitEntry();
      entry.key = key;
      entry.value = this.configData.limits[key];
      entries.push(entry);
    }
    return entries;
  }

  aboutToAppear() {
    this.loadSystemData();
  }

  aboutToDisappear() {
    this.stopAutoRefresh();
  }

  /**
   * 加载系统数据
   */
  private async loadSystemData() {
    try {
      this.loadingState = LoadingState.LOADING;
      
      // 并行加载健康检查和配置数据
      const responses = await Promise.all([
        this.apiService.healthCheck(),
        this.apiService.getConfig()
      ]);
      
      if (responses[0].code === 200) {
        this.healthData = responses[0].data as SystemHealth;
      }
      
      if (responses[1].code === 200) {
        this.configData = responses[1].data as SystemConfig;
      }
      
      this.loadingState = LoadingState.SUCCESS;
    } catch (error) {
      hilog.error(0x0000, 'SystemStatusPage', '加载系统数据失败: %{public}s', JSON.stringify(error));
      this.loadingState = LoadingState.ERROR;
    }
  }

  /**
   * 开始自动刷新
   */
  private startAutoRefresh() {
    this.stopAutoRefresh();
    this.refreshTimer = setInterval(() => {
      this.loadSystemData();
    }, this.refreshInterval * 1000);
  }

  /**
   * 停止自动刷新
   */
  private stopAutoRefresh() {
    if (this.refreshTimer !== -1) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = -1;
    }
  }

  /**
   * 切换自动刷新
   */
  private toggleAutoRefresh() {
    this.autoRefresh = !this.autoRefresh;
    if (this.autoRefresh) {
      this.startAutoRefresh();
    } else {
      this.stopAutoRefresh();
    }
  }

  /**
   * 获取状态颜色
   */
  private getStatusColor(status: string): string | Resource {
    const colorMap = new StatusColorMap();
    switch (status) {
      case 'healthy':
        return colorMap.healthy;
      case 'up':
        return colorMap.up;
      case 'warning':
        return colorMap.warning;
      case 'degraded':
        return colorMap.degraded;
      case 'error':
        return colorMap.error;
      case 'down':
        return colorMap.down;
      default:
        return Constants.COLORS.TEXT_HINT;
    }
  }

  /**
   * 获取状态文本
   */
  private getStatusText(status: string): string {
    const textMap = new StatusTextMap();
    switch (status) {
      case 'healthy':
        return textMap.healthy;
      case 'warning':
        return textMap.warning;
      case 'error':
        return textMap.error;
      case 'up':
        return textMap.up;
      case 'down':
        return textMap.down;
      case 'degraded':
        return textMap.degraded;
      default:
        return '未知';
    }
  }

  /**
   * 格式化时间
   */
  private formatTime(timeStr: string): string {
    const time = new Date(timeStr);
    return time.toLocaleString();
  }

  /**
   * 格式化百分比
   */
  private formatPercentage(value: number): string {
    return `${Math.round(value)}%`;
  }

  /**
   * 构建健康状态页面
   */
  @Builder
  buildHealthTab() {
    if (this.healthData) {
      Scroll() {
      Column() {
        // 总体状态
        Column() {
          Row() {
            Circle()
              .width(12)
              .height(12)
              .fill(this.getStatusColor(this.healthData.status))
              .margin({ right: 8 })
            
            Text('系统状态')
              .fontSize(Constants.FONT_SIZE.NORMAL)
              .fontColor(Constants.COLORS.TEXT_PRIMARY)
              .fontWeight(FontWeight.Medium)
              .layoutWeight(1)
            
            Text(this.getStatusText(this.healthData.status))
              .fontSize(Constants.FONT_SIZE.NORMAL)
              .fontColor(this.getStatusColor(this.healthData.status))
              .fontWeight(FontWeight.Medium)
          }
          .width('100%')
          .margin({ bottom: 8 })
          
          Text(`最后检查: ${this.formatTime(this.healthData.timestamp)}`)
            .fontSize(Constants.FONT_SIZE.SMALL)
            .fontColor(Constants.COLORS.TEXT_HINT)
            .alignSelf(ItemAlign.Start)
        }
        .width('100%')
        .padding(16)
        .backgroundColor(Constants.COLORS.WHITE)
        .borderRadius(12)
        .margin({ left: 16, right: 16, bottom: 16 })
        
        // 系统指标
        if (this.healthData.metrics) {
          Column() {
            Text('系统指标')
              .fontSize(Constants.FONT_SIZE.NORMAL)
              .fontColor(Constants.COLORS.TEXT_PRIMARY)
              .fontWeight(FontWeight.Medium)
              .margin({ bottom: 12 })
              .alignSelf(ItemAlign.Start)
            
            if (this.healthData.metrics.cpu_usage !== undefined) {
              Row() {
                Text('CPU使用率')
                  .fontSize(Constants.FONT_SIZE.SMALL)
                  .fontColor(Constants.COLORS.TEXT_SECONDARY)
                  .layoutWeight(1)
                
                Text(this.formatPercentage(this.healthData.metrics.cpu_usage))
                  .fontSize(Constants.FONT_SIZE.SMALL)
                  .fontColor(Constants.COLORS.TEXT_PRIMARY)
                  .fontWeight(FontWeight.Medium)
              }
              .width('100%')
              .margin({ bottom: 8 })
            }
            
            if (this.healthData.metrics.memory_usage !== undefined) {
              Row() {
                Text('内存使用率')
                  .fontSize(Constants.FONT_SIZE.SMALL)
                  .fontColor(Constants.COLORS.TEXT_SECONDARY)
                  .layoutWeight(1)
                
                Text(this.formatPercentage(this.healthData.metrics.memory_usage))
                  .fontSize(Constants.FONT_SIZE.SMALL)
                  .fontColor(Constants.COLORS.TEXT_PRIMARY)
                  .fontWeight(FontWeight.Medium)
              }
              .width('100%')
              .margin({ bottom: 8 })
            }
            
            if (this.healthData.metrics.active_connections !== undefined) {
              Row() {
                Text('活跃连接数')
                  .fontSize(Constants.FONT_SIZE.SMALL)
                  .fontColor(Constants.COLORS.TEXT_SECONDARY)
                  .layoutWeight(1)
                
                Text(this.healthData.metrics.active_connections.toString())
                  .fontSize(Constants.FONT_SIZE.SMALL)
                  .fontColor(Constants.COLORS.TEXT_PRIMARY)
                  .fontWeight(FontWeight.Medium)
              }
              .width('100%')
              .margin({ bottom: 8 })
            }
            
            if (this.healthData.metrics.requests_per_minute !== undefined) {
              Row() {
                Text('每分钟请求数')
                  .fontSize(Constants.FONT_SIZE.SMALL)
                  .fontColor(Constants.COLORS.TEXT_SECONDARY)
                  .layoutWeight(1)
                
                Text(this.healthData.metrics.requests_per_minute.toString())
                  .fontSize(Constants.FONT_SIZE.SMALL)
                  .fontColor(Constants.COLORS.TEXT_PRIMARY)
                  .fontWeight(FontWeight.Medium)
              }
              .width('100%')
            }
          }
          .width('100%')
          .padding(16)
          .backgroundColor(Constants.COLORS.WHITE)
          .borderRadius(12)
          .margin({ left: 16, right: 16, bottom: 16 })
        }
        
        // 服务状态
        if (this.healthData.services && this.healthData.services.length > 0) {
          Column() {
            Text('服务状态')
              .fontSize(Constants.FONT_SIZE.NORMAL)
              .fontColor(Constants.COLORS.TEXT_PRIMARY)
              .fontWeight(FontWeight.Medium)
              .margin({ bottom: 12 })
              .alignSelf(ItemAlign.Start)
            
            ForEach(this.healthData.services, (service: ServiceStatus, index: number) => {
              Column() {
                Row() {
                  Circle()
                    .width(8)
                    .height(8)
                    .fill(this.getStatusColor(service.status))
                    .margin({ right: 8 })
                  
                  Text(service.name)
                    .fontSize(Constants.FONT_SIZE.SMALL)
                    .fontColor(Constants.COLORS.TEXT_PRIMARY)
                    .fontWeight(FontWeight.Medium)
                    .layoutWeight(1)
                  
                  Text(this.getStatusText(service.status))
                    .fontSize(Constants.FONT_SIZE.SMALL)
                    .fontColor(this.getStatusColor(service.status))
                }
                .width('100%')
                .margin({ bottom: service.response_time || service.message ? 4 : 0 })
                
                if (service.response_time) {
                  Text(`响应时间: ${service.response_time}ms`)
                    .fontSize(Constants.FONT_SIZE.SMALL)
                    .fontColor(Constants.COLORS.TEXT_HINT)
                    .alignSelf(ItemAlign.Start)
                    .margin({ left: 16, bottom: 4 })
                }
                
                if (service.message) {
                  Text(service.message)
                    .fontSize(Constants.FONT_SIZE.SMALL)
                    .fontColor(Constants.COLORS.TEXT_HINT)
                    .alignSelf(ItemAlign.Start)
                    .margin({ left: 16, bottom: 4 })
                }
                
                Text(`最后检查: ${this.formatTime(service.last_check)}`)
                  .fontSize(Constants.FONT_SIZE.SMALL)
                  .fontColor(Constants.COLORS.TEXT_HINT)
                  .alignSelf(ItemAlign.Start)
                  .margin({ left: 16 })
                
                if (index < this.healthData!.services.length - 1) {
                  Divider()
                    .color(Constants.COLORS.BORDER)
                    .margin({ top: 12, bottom: 12 })
                }
              }
            })
          }
          .width('100%')
          .padding(16)
          .backgroundColor(Constants.COLORS.WHITE)
          .borderRadius(12)
          .margin({ left: 16, right: 16, bottom: 16 })
        }
      }
      }
      .layoutWeight(1)
      .scrollBar(BarState.Off)
    }
  }

  /**
   * 构建配置页面
   */
  @Builder
  buildConfigTab() {
    if (this.configData) {
      Scroll() {
      Column() {
        // 基本信息
        Column() {
          Text('基本信息')
            .fontSize(Constants.FONT_SIZE.NORMAL)
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .fontWeight(FontWeight.Medium)
            .margin({ bottom: 12 })
            .alignSelf(ItemAlign.Start)
          
          Row() {
            Text('应用名称')
              .fontSize(Constants.FONT_SIZE.SMALL)
              .fontColor(Constants.COLORS.TEXT_SECONDARY)
              .layoutWeight(1)
            
            Text(this.configData.app_name)
              .fontSize(Constants.FONT_SIZE.SMALL)
              .fontColor(Constants.COLORS.TEXT_PRIMARY)
              .fontWeight(FontWeight.Medium)
          }
          .width('100%')
          .margin({ bottom: 8 })
          
          Row() {
            Text('版本')
              .fontSize(Constants.FONT_SIZE.SMALL)
              .fontColor(Constants.COLORS.TEXT_SECONDARY)
              .layoutWeight(1)
            
            Text(this.configData.version)
              .fontSize(Constants.FONT_SIZE.SMALL)
              .fontColor(Constants.COLORS.TEXT_PRIMARY)
              .fontWeight(FontWeight.Medium)
          }
          .width('100%')
          .margin({ bottom: 8 })
          
          Row() {
            Text('环境')
              .fontSize(Constants.FONT_SIZE.SMALL)
              .fontColor(Constants.COLORS.TEXT_SECONDARY)
              .layoutWeight(1)
            
            Text(this.configData.environment)
              .fontSize(Constants.FONT_SIZE.SMALL)
              .fontColor(Constants.COLORS.TEXT_PRIMARY)
              .fontWeight(FontWeight.Medium)
          }
          .width('100%')
          .margin({ bottom: 8 })
          
          Row() {
            Text('维护模式')
              .fontSize(Constants.FONT_SIZE.SMALL)
              .fontColor(Constants.COLORS.TEXT_SECONDARY)
              .layoutWeight(1)
            
            Text(this.configData.maintenance_mode ? '开启' : '关闭')
              .fontSize(Constants.FONT_SIZE.SMALL)
              .fontColor(this.configData.maintenance_mode ? $r('app.color.status_error') : $r('app.color.status_success'))
              .fontWeight(FontWeight.Medium)
          }
          .width('100%')
        }
        .width('100%')
        .padding(16)
        .backgroundColor(Constants.COLORS.WHITE)
        .borderRadius(12)
        .margin({ left: 16, right: 16, bottom: 16 })
        
        // 功能开关
        if (this.configData.features && Object.keys(this.configData.features).length > 0) {
          Column() {
            Text('功能开关')
              .fontSize(Constants.FONT_SIZE.NORMAL)
              .fontColor(Constants.COLORS.TEXT_PRIMARY)
              .fontWeight(FontWeight.Medium)
              .margin({ bottom: 12 })
              .alignSelf(ItemAlign.Start)
            
            ForEach(this.getFeatureEntries(), (item: FeatureEntry) => {
              Row() {
                Text(item.key)
                  .fontSize(Constants.FONT_SIZE.SMALL)
                  .fontColor(Constants.COLORS.TEXT_SECONDARY)
                  .layoutWeight(1)
                
                Text(item.value ? '开启' : '关闭')
                  .fontSize(Constants.FONT_SIZE.SMALL)
                  .fontColor(item.value ? $r('app.color.status_success') : Constants.COLORS.TEXT_HINT)
                  .fontWeight(FontWeight.Medium)
              }
              .width('100%')
              .margin({ bottom: 8 })
            }, (item: FeatureEntry) => item.key)
          }
          .width('100%')
          .padding(16)
          .backgroundColor(Constants.COLORS.WHITE)
          .borderRadius(12)
          .margin({ left: 16, right: 16, bottom: 16 })
        }
        
        // 系统限制
        if (this.configData.limits && Object.keys(this.configData.limits).length > 0) {
          Column() {
            Text('系统限制')
              .fontSize(Constants.FONT_SIZE.NORMAL)
              .fontColor(Constants.COLORS.TEXT_PRIMARY)
              .fontWeight(FontWeight.Medium)
              .margin({ bottom: 12 })
              .alignSelf(ItemAlign.Start)
            
            ForEach(this.getLimitEntries(), (item: LimitEntry) => {
              Row() {
                Text(item.key)
                  .fontSize(Constants.FONT_SIZE.SMALL)
                  .fontColor(Constants.COLORS.TEXT_SECONDARY)
                  .layoutWeight(1)
                
                Text(item.value.toString())
                  .fontSize(Constants.FONT_SIZE.SMALL)
                  .fontColor(Constants.COLORS.TEXT_PRIMARY)
                  .fontWeight(FontWeight.Medium)
              }
              .width('100%')
              .margin({ bottom: 8 })
            }, (item: LimitEntry) => item.key)
          }
          .width('100%')
          .padding(16)
          .backgroundColor(Constants.COLORS.WHITE)
          .borderRadius(12)
          .margin({ left: 16, right: 16, bottom: 16 })
        }
      }
      }
      .layoutWeight(1)
      .scrollBar(BarState.Off)
    }
  }

  build() {
    Column() {
      // 标题栏
      Row() {
        Button() {
          Image($r('app.media.ic_back'))
            .width(24)
            .height(24)
            .fillColor(Constants.COLORS.TEXT_PRIMARY)
        }
        .width(40)
        .height(40)
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          this.getUIContext().getRouter().back();
        })

        Text('系统状态')
          .fontSize(Constants.FONT_SIZE.LARGE)
          .fontWeight(FontWeight.Medium)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Row() {
          Button() {
            Image($r('app.media.ic_refresh'))
              .width(20)
              .height(20)
              .fillColor(Constants.COLORS.TEXT_PRIMARY)
          }
          .width(32)
          .height(32)
          .backgroundColor(Color.Transparent)
          .onClick(() => {
            this.loadSystemData();
          })
          
          Button() {
            Image($r('app.media.ic_refresh'))
              .width(20)
              .height(20)
              .fillColor(this.autoRefresh ? Constants.COLORS.WHITE : Constants.COLORS.TEXT_SECONDARY)
              .position({ x: 10, y: 10 })
          }
          .width(32)
          .height(32)
          .backgroundColor(Color.Transparent)
          .margin({ left: 8 })
          .onClick(() => {
            this.toggleAutoRefresh();
          })
        }
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor(Constants.COLORS.WHITE)

      // 标签页
      Row() {
        Button('健康状态')
          .fontSize(Constants.FONT_SIZE.NORMAL)
          .fontColor(this.selectedTab === 0 ? Constants.COLORS.PRIMARY : Constants.COLORS.TEXT_SECONDARY)
          .backgroundColor(Color.Transparent)
          .layoutWeight(1)
          .onClick(() => {
            this.selectedTab = 0;
          })
        
        Button('系统配置')
          .fontSize(Constants.FONT_SIZE.NORMAL)
          .fontColor(this.selectedTab === 1 ? Constants.COLORS.PRIMARY : Constants.COLORS.TEXT_SECONDARY)
          .backgroundColor(Color.Transparent)
          .layoutWeight(1)
          .onClick(() => {
            this.selectedTab = 1;
          })
      }
      .width('100%')
      .height(48)
      .backgroundColor(Constants.COLORS.WHITE)

      // 内容区域
      if (this.loadingState === LoadingState.LOADING) {
        LoadingView()
          .layoutWeight(1)
      } else if (this.loadingState === LoadingState.ERROR) {
        Column() {
          Image($r('app.media.ic_error'))
            .width(64)
            .height(64)
            .fillColor(Constants.COLORS.TEXT_HINT)
            .margin({ bottom: 16 })
          
          Text('加载失败')
            .fontSize(Constants.FONT_SIZE.MEDIUM)
            .fontColor(Constants.COLORS.TEXT_SECONDARY)
            .margin({ bottom: 16 })
          
          Button('重试')
            .fontSize(Constants.FONT_SIZE.NORMAL)
            .fontColor(Constants.COLORS.WHITE)
            .backgroundColor(Constants.COLORS.PRIMARY)
            .borderRadius(8)
            .padding({ left: 24, right: 24, top: 8, bottom: 8 })
            .onClick(() => {
              this.loadSystemData();
            })
        }
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
      } else {
        if (this.selectedTab === 0) {
          this.buildHealthTab()
        } else {
          this.buildConfigTab()
        }
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Constants.COLORS.BACKGROUND)
  }
}