.container {
  max-width: 1200px;
  margin: 0 auto;
}

.stepsCard {
  margin-bottom: 24px;
  
  .steps {
    padding: 24px 0;
  }
}

.verifyCard {
  .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
  }
  
  .ant-form-item-label {
    font-weight: 500;
  }
  
  .ant-upload-list-picture-card {
    .ant-upload-list-item {
      width: 104px;
      height: 104px;
    }
  }
}

.statusCard {
  .statusContent {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    
    .anticon {
      font-size: 32px;
      margin-top: 8px;
    }
    
    .statusInfo {
      flex: 1;
      
      .ant-typography-title {
        margin-bottom: 8px;
      }
      
      .ant-typography-paragraph {
        margin-bottom: 16px;
        color: rgba(0, 0, 0, 0.65);
      }
    }
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }
  
  .verifyCard {
    .ant-col {
      margin-bottom: 16px;
    }
  }
  
  .statusCard {
    .statusContent {
      flex-direction: column;
      text-align: center;
      
      .anticon {
        margin-top: 0;
      }
    }
  }
}