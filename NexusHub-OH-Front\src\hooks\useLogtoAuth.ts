import { useLog<PERSON> } from '@logto/react';
import { useCallback, useEffect, useState } from 'react';
import type { LogtoUserInfo } from '../config/logto';

export const useLogtoAuth = () => {
  const {
    isAuthenticated,
    isLoading,
    signIn,
    signOut,
    getAccessToken,
    getIdToken,
    getIdTokenClaims,
    fetchUserInfo,
    error,
  } = useLogto();

  const [userInfo, setUserInfo] = useState<LogtoUserInfo | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(null);

  // 获取用户信息
  const getUserInfo = useCallback(async () => {
    if (!isAuthenticated) {
      setUserInfo(null);
      return null;
    }

    try {
      const info = await fetchUserInfo();
      const logtoUserInfo: LogtoUserInfo = {
        sub: info.sub,
        email: info.email,
        name: info.name,
        picture: info.picture,
        username: info.username,
        phone_number: info.phone_number,
        email_verified: info.email_verified,
        phone_number_verified: info.phone_number_verified,
      };
      setUserInfo(logtoUserInfo);
      return logtoUserInfo;
    } catch (err) {
      console.error('Failed to fetch user info:', err);
      return null;
    }
  }, [isAuthenticated, fetchUserInfo]);

  // 获取访问令牌
  const getToken = useCallback(async (resource?: string) => {
    if (!isAuthenticated) {
      setAccessToken(null);
      localStorage.removeItem('logto_access_token');
      return null;
    }

    try {
      const token = await getAccessToken(resource);
      setAccessToken(token);
      // 将token存储到localStorage，供请求拦截器使用
      if (token) {
        localStorage.setItem('logto_access_token', token);
      }
      return token;
    } catch (err) {
      console.error('Failed to get access token:', err);
      localStorage.removeItem('logto_access_token');
      return null;
    }
  }, [isAuthenticated, getAccessToken]);

  // 处理登录
  const handleSignIn = useCallback(async (redirectUri?: string) => {
    try {
      await signIn(redirectUri || window.location.origin + '/callback');
    } catch (err) {
      console.error('Sign in failed:', err);
      throw err;
    }
  }, [signIn]);

  // 处理登出
  const handleSignOut = useCallback(async (postLogoutRedirectUri?: string) => {
    try {
      setUserInfo(null);
      setAccessToken(null);
      // 清除存储的token
      localStorage.removeItem('logto_access_token');
      await signOut(postLogoutRedirectUri || window.location.origin);
    } catch (err) {
      console.error('Sign out failed:', err);
      throw err;
    }
  }, [signOut]);

  // 检查权限
  const hasPermission = useCallback((permission: string) => {
    // 这里可以根据实际需求实现权限检查逻辑
    // 例如检查用户的角色或权限列表
    return true;
  }, []);

  // 自动获取用户信息和token
  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      getUserInfo();
      getToken(process.env.REACT_APP_LOGTO_API_RESOURCE);
    } else if (!isAuthenticated) {
      // 用户未认证时清除token
      localStorage.removeItem('logto_access_token');
    }
  }, [isAuthenticated, isLoading, getUserInfo, getToken]);

  return {
    // 状态
    isAuthenticated,
    isLoading,
    userInfo,
    accessToken,
    error,

    // 方法
    signIn: handleSignIn,
    signOut: handleSignOut,
    getUserInfo,
    getToken,
    getIdToken,
    getIdTokenClaims,
    hasPermission,
  };
};

export default useLogtoAuth;