package middleware

import (
	"net/http"
	"sync"
	"time"

	"nexushub-oh-back/config"
	"nexushub-oh-back/pkg/logger"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// RateLimiter 请求限流器
type RateLimiter struct {
	ips      map[string][]time.Time
	duration time.Duration
	limit    int
	mu       sync.Mutex
}

// NewRateLimiter 创建新的限流器
// duration: 时间窗口
// limit: 时间窗口内的最大请求数
func NewRateLimiter(duration time.Duration, limit int) *RateLimiter {
	return &RateLimiter{
		ips:      make(map[string][]time.Time),
		duration: duration,
		limit:    limit,
		mu:       sync.Mutex{},
	}
}

// NewRateLimiterWithConfig 使用配置创建新的限流器
func NewRateLimiterWithConfig(cfg *config.RateLimitConfig) *RateLimiter {
	// 默认配置
	duration := 1 * time.Minute
	limit := 60

	// 如果配置有效，使用配置值
	if cfg != nil && cfg.Enable {
		if cfg.Requests > 0 {
			limit = cfg.Requests
		}

		if cfg.Period > 0 {
			duration = cfg.Period
		}
	}

	limiter := NewRateLimiter(duration, limit)

	// 如果配置了自动清理，启动清理任务
	if cfg != nil && cfg.Enable && cfg.CleanupTime > 0 {
		limiter.StartCleanupTask(cfg.CleanupTime)
	}

	return limiter
}

// LimitByIP 根据IP限流
func (rl *RateLimiter) LimitByIP() gin.HandlerFunc {
	return func(c *gin.Context) {
		ip := c.ClientIP()

		rl.mu.Lock()
		defer rl.mu.Unlock()

		// 清理过期的请求记录
		now := time.Now()
		if _, exists := rl.ips[ip]; exists {
			var validTimes []time.Time
			for _, t := range rl.ips[ip] {
				if now.Sub(t) <= rl.duration {
					validTimes = append(validTimes, t)
				}
			}
			rl.ips[ip] = validTimes
		}

		// 检查是否超出限制
		if len(rl.ips[ip]) >= rl.limit {
			logger.Warn("请求被限流", zap.String("ip", ip))
			c.JSON(http.StatusTooManyRequests, gin.H{
				"code":    429,
				"message": "请求过于频繁，请稍后再试",
			})
			c.Abort()
			return
		}

		// 记录当前请求
		rl.ips[ip] = append(rl.ips[ip], now)

		c.Next()
	}
}

// 启动定期清理任务
func (rl *RateLimiter) StartCleanupTask(interval time.Duration) {
	ticker := time.NewTicker(interval)
	go func() {
		for range ticker.C {
			rl.cleanup()
		}
	}()
}

// 清理过期的请求记录
func (rl *RateLimiter) cleanup() {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	now := time.Now()
	for ip, times := range rl.ips {
		var validTimes []time.Time
		for _, t := range times {
			if now.Sub(t) <= rl.duration {
				validTimes = append(validTimes, t)
			}
		}

		if len(validTimes) > 0 {
			rl.ips[ip] = validTimes
		} else {
			delete(rl.ips, ip)
		}
	}
}
