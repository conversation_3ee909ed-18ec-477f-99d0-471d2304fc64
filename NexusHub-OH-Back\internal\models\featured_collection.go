package models

import (
	"time"

	"gorm.io/gorm"
)

// FeaturedCollection 精选集模型
//
//	@Description	精选集模型
type FeaturedCollection struct {
	// gorm.Model 展开为以下字段
	ID          uint          `gorm:"primaryKey" json:"id"`                                      // 精选集ID
	CreatedAt   time.Time     `json:"created_at"`                                                // 创建时间
	UpdatedAt   time.Time     `json:"updated_at"`                                                // 更新时间
	DeletedAt   *time.Time    `gorm:"index" json:"deleted_at"`                                   // 删除时间
	Title       string        `gorm:"type:varchar(100);not null" json:"title"`                   // 精选集标题
	Description string        `gorm:"type:text" json:"description"`                              // 精选集描述
	Icon        string        `gorm:"type:varchar(255)" json:"icon"`                             // 精选集图标URL
	CoverImage  string        `gorm:"type:varchar(255)" json:"cover_image"`                      // 封面图片URL
	SortOrder   int           `gorm:"default:0" json:"sort_order"`                               // 排序权重
	IsActive    bool          `gorm:"default:true" json:"is_active"`                             // 是否启用
	IsPublic    bool          `gorm:"default:true" json:"is_public"`                             // 是否公开
	CreatorID   uint          `gorm:"not null" json:"creator_id"`                                // 创建者ID
	Creator     User          `gorm:"foreignKey:CreatorID" json:"creator,omitempty"`             // 创建者信息
	Apps        []Application `gorm:"many2many:featured_collection_apps;" json:"apps,omitempty"` // 关联的应用列表
}

// FeaturedCollectionApp 精选集应用关联表
type FeaturedCollectionApp struct {
	FeaturedCollectionID uint      `gorm:"primaryKey" json:"featured_collection_id"` // 精选集ID
	ApplicationID        uint      `gorm:"primaryKey" json:"application_id"`         // 应用ID
	SortOrder            int       `gorm:"default:0" json:"sort_order"`              // 在精选集中的排序
	AddedAt              time.Time `gorm:"autoCreateTime" json:"added_at"`           // 添加时间
}

// TableName 指定表名
func (FeaturedCollection) TableName() string {
	return "featured_collections"
}

// TableName 指定关联表名
func (FeaturedCollectionApp) TableName() string {
	return "featured_collection_apps"
}

// CreateFeaturedCollection 创建精选集
func CreateFeaturedCollection(db *gorm.DB, collection *FeaturedCollection) error {
	return db.Create(collection).Error
}

// GetFeaturedCollectionByID 通过ID获取精选集
func GetFeaturedCollectionByID(db *gorm.DB, id uint) (*FeaturedCollection, error) {
	var collection FeaturedCollection
	err := db.Preload("Creator").Preload("Apps").First(&collection, id).Error
	return &collection, err
}

// GetFeaturedCollectionByTitle 通过标题获取精选集
func GetFeaturedCollectionByTitle(db *gorm.DB, title string) (*FeaturedCollection, error) {
	var collection FeaturedCollection
	err := db.Where("title = ?", title).First(&collection).Error
	return &collection, err
}

// UpdateFeaturedCollection 更新精选集
func UpdateFeaturedCollection(db *gorm.DB, collection *FeaturedCollection) error {
	return db.Save(collection).Error
}

// DeleteFeaturedCollection 删除精选集
func DeleteFeaturedCollection(db *gorm.DB, id uint) error {
	return db.Delete(&FeaturedCollection{}, id).Error
}

// ListFeaturedCollections 获取精选集列表
func ListFeaturedCollections(db *gorm.DB, includeInactive bool, includePrivate bool, page, pageSize int) ([]FeaturedCollection, int64, error) {
	var collections []FeaturedCollection
	var total int64

	query := db.Model(&FeaturedCollection{}).Preload("Creator")

	if !includeInactive {
		query = query.Where("is_active = ?", true)
	}

	if !includePrivate {
		query = query.Where("is_public = ?", true)
	}

	// 计算总数
	query.Count(&total)

	// 分页查询
	offset := (page - 1) * pageSize
	err := query.Order("sort_order DESC, created_at DESC").Offset(offset).Limit(pageSize).Find(&collections).Error

	return collections, total, err
}

// GetFeaturedCollectionsByCreator 获取指定创建者的精选集
func GetFeaturedCollectionsByCreator(db *gorm.DB, creatorID uint, includeInactive bool) ([]FeaturedCollection, error) {
	var collections []FeaturedCollection
	query := db.Where("creator_id = ?", creatorID).Preload("Creator")

	if !includeInactive {
		query = query.Where("is_active = ?", true)
	}

	err := query.Order("sort_order DESC, created_at DESC").Find(&collections).Error
	return collections, err
}

// AddAppToFeaturedCollection 向精选集添加应用
func AddAppToFeaturedCollection(db *gorm.DB, collectionID, appID uint, sortOrder int) error {
	// 检查是否已存在
	var existing FeaturedCollectionApp
	err := db.Where("featured_collection_id = ? AND application_id = ?", collectionID, appID).First(&existing).Error
	if err == nil {
		// 已存在，更新排序
		existing.SortOrder = sortOrder
		return db.Save(&existing).Error
	}

	// 不存在，创建新关联
	association := FeaturedCollectionApp{
		FeaturedCollectionID: collectionID,
		ApplicationID:        appID,
		SortOrder:            sortOrder,
	}
	return db.Create(&association).Error
}

// RemoveAppFromFeaturedCollection 从精选集移除应用
func RemoveAppFromFeaturedCollection(db *gorm.DB, collectionID, appID uint) error {
	return db.Where("featured_collection_id = ? AND application_id = ?", collectionID, appID).Delete(&FeaturedCollectionApp{}).Error
}

// GetFeaturedCollectionApps 获取精选集中的应用列表
func GetFeaturedCollectionApps(db *gorm.DB, collectionID uint, page, pageSize int) ([]Application, int64, error) {
	var apps []Application
	var total int64

	// 通过关联表查询应用
	subQuery := db.Table("featured_collection_apps").Select("application_id").Where("featured_collection_id = ?", collectionID)
	query := db.Where("id IN (?)", subQuery).Preload("Category").Preload("Tags")

	// 计算总数
	query.Model(&Application{}).Count(&total)

	// 分页查询，按照在精选集中的排序
	offset := (page - 1) * pageSize
	err := db.Table("applications").
		Select("applications.*, featured_collection_apps.sort_order as collection_sort_order").
		Joins("JOIN featured_collection_apps ON applications.id = featured_collection_apps.application_id").
		Where("featured_collection_apps.featured_collection_id = ?", collectionID).
		Order("featured_collection_apps.sort_order ASC, applications.created_at DESC").
		Offset(offset).Limit(pageSize).
		Preload("Category").Preload("Tags").
		Find(&apps).Error

	return apps, total, err
}
