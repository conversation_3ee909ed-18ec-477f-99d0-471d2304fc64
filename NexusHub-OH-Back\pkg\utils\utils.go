package utils

import (
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
)

// GetUserID 从gin上下文中获取用户ID
func GetUserID(c *gin.Context) (uint, error) {
	userID, exists := c.Get("user_id")
	if !exists {
		return 0, nil
	}
	return userID.(uint), nil
}

// GetUserRole 从gin上下文中获取用户角色
func GetUserRole(c *gin.Context) string {
	role, exists := c.Get("role")
	if !exists {
		return ""
	}
	return role.(string)
}

// GetUsername 从gin上下文中获取用户名
func GetUsername(c *gin.Context) string {
	username, exists := c.Get("username")
	if !exists {
		return ""
	}
	return username.(string)
}

// ParsePagination 解析分页参数
func ParsePagination(c *gin.Context) (page, limit int) {
	pageStr := c.DefaultQuery("page", "1")
	limitStr := c.DefaultQuery("limit", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	limit, err = strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 10
	}

	return page, limit
}

// GetOffset 计算数据库查询偏移量
func GetOffset(page, limit int) int {
	return (page - 1) * limit
}

// GetClientIP 获取客户端IP地址
func GetClientIP(c *gin.Context) string {
	// 尝试从X-Forwarded-For头获取
	xForwardedFor := c.GetHeader("X-Forwarded-For")
	if xForwardedFor != "" {
		// X-Forwarded-For可能包含多个IP，取第一个
		ips := strings.Split(xForwardedFor, ",")
		if len(ips) > 0 {
			return strings.TrimSpace(ips[0])
		}
	}

	// 尝试从X-Real-IP头获取
	xRealIP := c.GetHeader("X-Real-IP")
	if xRealIP != "" {
		return xRealIP
	}

	// 使用gin的ClientIP方法
	return c.ClientIP()
}

// GetUserAgent 获取用户代理字符串
func GetUserAgent(c *gin.Context) string {
	return c.GetHeader("User-Agent")
}

// GetReferer 获取来源页面
func GetReferer(c *gin.Context) string {
	return c.GetHeader("Referer")
}

// ParseTimeRange 解析时间范围参数
func ParseTimeRange(c *gin.Context) (startTime, endTime *time.Time) {
	startStr := c.Query("start_time")
	endStr := c.Query("end_time")

	if startStr != "" {
		if start, err := time.Parse("2006-01-02", startStr); err == nil {
			startTime = &start
		}
	}

	if endStr != "" {
		if end, err := time.Parse("2006-01-02", endStr); err == nil {
			// 设置为当天的23:59:59
			end = end.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
			endTime = &end
		}
	}

	return startTime, endTime
}

// IsAdmin 检查用户是否为管理员
func IsAdmin(c *gin.Context) bool {
	role := GetUserRole(c)
	return role == "admin" || role == "super_admin"
}

// StringPtr 返回字符串指针
func StringPtr(s string) *string {
	return &s
}

// UintPtr 返回uint指针
func UintPtr(u uint) *uint {
	return &u
}

// TimePtr 返回时间指针
func TimePtr(t time.Time) *time.Time {
	return &t
}

// HashPassword 加密密码
func HashPassword(password string) (string, error) {
	hashed, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashed), nil
}

// CheckPassword 验证密码
func CheckPassword(hashedPassword, password string) error {
	return bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
}
