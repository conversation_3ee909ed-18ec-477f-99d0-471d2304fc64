/*
 * @Author: xiaoxiaozhou <EMAIL>
 * @Date: 2025-06-07 00:57:05
 * @LastEditors: xiaoxiaozhou <EMAIL>
 * @LastEditTime: 2025-06-07 01:51:04
 * @FilePath: \NexusHub-OH\NexusHub-OH-Front\src\pages\dashboard\analysis\service.ts
 * @Description: 
 * 技术生的工作日常
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
import { request } from '@umijs/max';
import type { AnalysisData } from './data';

/**
 * 获取分析页摘要数据
 * 包括总用户数、总应用数、总下载量等统计信息
 */
export async function getSummaryData(): Promise<{ data: any }> {
  return request('/dashboard/analytics/summary');
}

/**
 * 获取趋势分析数据
 * 获取用户、应用、下载、开发者等趋势数据
 */
export async function getTrendData(days: number = 30): Promise<{ data: any }> {
  return request('/dashboard/analytics/trend', {
    params: { days },
  });
}

/**
 * 获取分类统计数据
 * 获取各应用分类的应用数量和下载统计
 */
export async function getCategoriesData(): Promise<{ data: any }> {
  return request('/dashboard/analytics/categories');
}

/**
 * 获取热门应用
 * 获取平台上下载量和评分最高的应用
 */
export async function getPopularApps(limit: number = 10): Promise<{ data: any }> {
  return request('/dashboard/analytics/popular-apps', {
    params: { limit },
  });
}
