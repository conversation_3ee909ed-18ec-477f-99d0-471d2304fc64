{"schemes": ["http", "https"], "swagger": "2.0", "info": {"description": "NexusHub-OH 应用商店后端 API，为 OpenHarmony 系统应用商店提供完整的 API 支持", "title": "NexusHub-OH API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.nexushub-oh.top/support", "email": "<EMAIL>"}, "version": "1.0"}, "host": "localhost:8080", "basePath": "/api/v1", "paths": {"/admin/developers/verify": {"get": {"security": [{"Bearer": []}], "description": "管理员获取待审核的开发者列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["开发者"], "summary": "获取待审核的开发者", "parameters": [{"type": "integer", "default": 1, "description": "页码，默认1", "name": "page", "in": "query"}, {"type": "integer", "default": 20, "description": "每页数量，默认20", "name": "page_size", "in": "query"}, {"type": "string", "default": "\"pending\"", "description": "认证状态(pending/approved/rejected)", "name": "status", "in": "query"}], "responses": {"200": {"description": "返回待审核的开发者列表", "schema": {"allOf": [{"$ref": "#/definitions/api.PageResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/api.DeveloperVerifyResponse"}}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "403": {"description": "非管理员，无权访问", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/admin/developers/{id}/verify": {"post": {"security": [{"Bearer": []}], "description": "管理员审核开发者认证申请", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["开发者"], "summary": "审核开发者", "parameters": [{"type": "integer", "description": "用户ID", "name": "id", "in": "path", "required": true}, {"description": "审核结果", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/api.AdminVerifyRequest"}}], "responses": {"200": {"description": "审核成功，返回更新后的认证信息", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/api.DeveloperVerifyResponse"}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "403": {"description": "非管理员，无权操作", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "用户不存在", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/admin/users": {"get": {"description": "管理员获取所有用户列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["管理员"], "summary": "获取用户列表", "parameters": [{"type": "integer", "description": "页码，默认1", "name": "page", "in": "query"}, {"type": "integer", "description": "每页数量，默认10", "name": "pageSize", "in": "query"}, {"type": "string", "description": "搜索关键词（用户名/邮箱/手机）", "name": "keyword", "in": "query"}, {"type": "string", "description": "用户角色", "name": "role", "in": "query"}, {"type": "string", "description": "用户状态", "name": "status", "in": "query"}, {"type": "string", "description": "开发者状态", "name": "developerStatus", "in": "query"}], "responses": {"200": {"description": "用户列表", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/api.UserResponse"}}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "403": {"description": "无权限", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}, "post": {"security": [{"Bearer": []}], "description": "管理员创建新用户并指定角色", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["管理员"], "summary": "管理员创建用户", "parameters": [{"description": "用户信息", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/api.AdminCreateUserRequest"}}], "responses": {"200": {"description": "创建成功", "schema": {"$ref": "#/definitions/api.Response"}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/admin/users/{id}/role": {"put": {"security": [{"Bearer": []}], "description": "管理员更新用户角色", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["管理员"], "summary": "更新用户角色", "parameters": [{"type": "integer", "description": "用户ID", "name": "id", "in": "path", "required": true}, {"description": "角色信息", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/api.UpdateUserRoleRequest"}}], "responses": {"200": {"description": "更新成功", "schema": {"$ref": "#/definitions/api.Response"}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "用户不存在", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/admin/users/{id}/status": {"put": {"security": [{"Bearer": []}], "description": "管理员更新用户状态（禁用/启用）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["管理员"], "summary": "更新用户状态", "parameters": [{"type": "integer", "description": "用户ID", "name": "id", "in": "path", "required": true}, {"description": "状态信息", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/api.UpdateUserStatusRequest"}}], "responses": {"200": {"description": "更新成功", "schema": {"$ref": "#/definitions/api.Response"}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "用户不存在", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/api/v1/messages/activity": {"post": {"description": "记录用户活动到消息队列", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["消息队列"], "summary": "记录用户活动", "parameters": [{"description": "用户活动请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/api.UserActivityRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/api.Response"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/api/v1/messages/app-review/{app_id}": {"post": {"description": "触发应用审核流程", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["消息队列"], "summary": "触发应用审核", "parameters": [{"type": "integer", "description": "应用ID", "name": "app_id", "in": "path", "required": true}, {"enum": ["submit", "approve", "reject"], "type": "string", "description": "操作类型", "name": "action", "in": "query", "required": true}, {"type": "string", "description": "原因（拒绝时必填）", "name": "reason", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/api.Response"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/api/v1/messages/email": {"post": {"description": "发送邮件到消息队列", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["消息队列"], "summary": "发送邮件消息", "parameters": [{"description": "邮件请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/api.SendEmailRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/api.Response"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/api/v1/messages/notification": {"post": {"description": "向指定用户发送通知消息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["消息队列"], "summary": "发送通知消息", "parameters": [{"description": "通知请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/api.SendNotificationRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/api.Response"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/api/v1/messages/status": {"get": {"description": "获取消息队列状态信息", "produces": ["application/json"], "tags": ["消息队列"], "summary": "获取队列状态", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/api/v1/upload/token": {"get": {"security": [{"Bearer": []}], "description": "获取上传到对象存储的预签名URL", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["开发者"], "summary": "获取文件上传凭证", "parameters": [{"type": "string", "description": "文件类型(avatar/license/identity/screenshot/package)", "name": "file_type", "in": "query", "required": true}, {"type": "string", "description": "文件名称", "name": "file_name", "in": "query", "required": true}], "responses": {"200": {"description": "返回上传凭证", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/api.UploadResult"}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/apps": {"get": {"description": "分页获取应用列表，支持类别和关键字筛选", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["应用"], "summary": "获取应用列表", "parameters": [{"type": "integer", "default": 1, "description": "页码，默认1", "name": "page", "in": "query"}, {"type": "integer", "default": 20, "description": "每页数量，默认20", "name": "page_size", "in": "query"}, {"type": "string", "description": "应用类别", "name": "category", "in": "query"}, {"type": "string", "description": "搜索关键字", "name": "keyword", "in": "query"}], "responses": {"200": {"description": "返回应用列表和分页信息", "schema": {"allOf": [{"$ref": "#/definitions/api.PageResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/api.AppDetailsResponse"}}}}]}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}, "post": {"security": [{"Bearer": []}], "description": "开发者创建新应用", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["应用"], "summary": "创建应用", "parameters": [{"description": "应用信息", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/api.CreateAppRequest"}}], "responses": {"200": {"description": "创建成功，返回应用详情", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/api.AppDetailsResponse"}}}]}}, "400": {"description": "参数错误/包名已被使用", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "403": {"description": "非开发者或开发者未通过审核", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/apps/{id}": {"get": {"description": "获取应用的详细信息，包括截图和版本", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["应用"], "summary": "获取应用详情", "parameters": [{"type": "integer", "description": "应用ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "返回应用详情，包括应用基本信息、截图和版本", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": true}}}]}}, "404": {"description": "应用不存在", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}, "put": {"security": [{"Bearer": []}], "description": "开发者更新应用信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["应用"], "summary": "更新应用", "parameters": [{"type": "integer", "description": "应用ID", "name": "id", "in": "path", "required": true}, {"description": "更新的应用信息", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/api.UpdateAppRequest"}}], "responses": {"200": {"description": "更新成功，返回应用详情", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/api.AppDetailsResponse"}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "403": {"description": "非应用的开发者", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "应用不存在", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/apps/{id}/reviews": {"get": {"description": "获取应用的评论和评分", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["评论"], "summary": "获取应用评论", "parameters": [{"type": "integer", "description": "应用ID", "name": "id", "in": "path", "required": true}, {"type": "integer", "default": 1, "description": "页码，默认1", "name": "page", "in": "query"}, {"type": "integer", "default": 20, "description": "每页数量，默认20", "name": "page_size", "in": "query"}], "responses": {"200": {"description": "返回评论列表和分页信息", "schema": {"allOf": [{"$ref": "#/definitions/api.PageResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/api.ReviewResponse"}}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "应用不存在", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}, "post": {"security": [{"Bearer": []}], "description": "用户为应用发表评价和评分", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["评论"], "summary": "发表应用评论", "parameters": [{"type": "integer", "description": "应用ID", "name": "id", "in": "path", "required": true}, {"description": "评论内容", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/api.CreateReviewRequest"}}], "responses": {"200": {"description": "创建成功，返回评论详情", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/api.ReviewResponse"}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "应用不存在", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/apps/{id}/reviews/{review_id}/like": {"post": {"security": [{"Bearer": []}], "description": "给应用评论点赞", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["评论"], "summary": "点赞评论", "parameters": [{"type": "integer", "description": "应用ID", "name": "id", "in": "path", "required": true}, {"type": "integer", "description": "评论ID", "name": "review_id", "in": "path", "required": true}], "responses": {"200": {"description": "点赞成功", "schema": {"$ref": "#/definitions/api.Response"}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "评论不存在", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/apps/{id}/reviews/{review_id}/respond": {"post": {"security": [{"Bearer": []}], "description": "应用开发者回复用户评论", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["评论"], "summary": "开发者回复评论", "parameters": [{"type": "integer", "description": "应用ID", "name": "id", "in": "path", "required": true}, {"type": "integer", "description": "评论ID", "name": "review_id", "in": "path", "required": true}, {"description": "回复内容", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/api.DevResponseRequest"}}], "responses": {"200": {"description": "回复成功，返回更新后的评论", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/api.ReviewResponse"}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "403": {"description": "非应用开发者，无权回复", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "评论不存在", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/apps/{id}/reviews/{review_id}/unlike": {"post": {"security": [{"Bearer": []}], "description": "取消对应用评论的点赞", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["评论"], "summary": "取消点赞评论", "parameters": [{"type": "integer", "description": "应用ID", "name": "id", "in": "path", "required": true}, {"type": "integer", "description": "评论ID", "name": "review_id", "in": "path", "required": true}], "responses": {"200": {"description": "取消点赞成功", "schema": {"$ref": "#/definitions/api.Response"}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "评论不存在", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/apps/{id}/tags": {"get": {"description": "获取应用的所有标签", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["标签管理"], "summary": "获取应用的标签", "parameters": [{"type": "integer", "description": "应用ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "标签列表", "schema": {"type": "array", "items": {"$ref": "#/definitions/api.TagResponse"}}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "404": {"description": "应用不存在", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}, "post": {"description": "为应用添加多个标签", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["标签管理"], "summary": "为应用添加标签", "parameters": [{"type": "string", "description": "Bearer 用户令牌", "name": "Authorization", "in": "header", "required": true}, {"type": "integer", "description": "应用ID", "name": "id", "in": "path", "required": true}, {"description": "标签ID列表", "name": "tags", "in": "body", "required": true, "schema": {"$ref": "#/definitions/api.AppTagRequest"}}], "responses": {"200": {"description": "添加成功", "schema": {"$ref": "#/definitions/api.SuccessResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "404": {"description": "应用或标签不存在", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/apps/{id}/tags/{tag_id}": {"delete": {"description": "删除应用的指定标签", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["标签管理"], "summary": "删除应用的标签", "parameters": [{"type": "string", "description": "Bearer 用户令牌", "name": "Authorization", "in": "header", "required": true}, {"type": "integer", "description": "应用ID", "name": "id", "in": "path", "required": true}, {"type": "integer", "description": "标签ID", "name": "tag_id", "in": "path", "required": true}], "responses": {"200": {"description": "删除成功", "schema": {"$ref": "#/definitions/api.SuccessResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "404": {"description": "应用或标签不存在", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/apps/{id}/versions/{version_id}/download": {"post": {"security": [{"Bearer": []}], "description": "记录用户下载应用的行为，更新下载统计", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["统计"], "summary": "记录应用下载", "parameters": [{"type": "integer", "description": "应用ID", "name": "id", "in": "path", "required": true}, {"type": "integer", "description": "版本ID", "name": "version_id", "in": "path", "required": true}], "responses": {"200": {"description": "记录成功", "schema": {"$ref": "#/definitions/api.Response"}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "应用或版本不存在", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/categories": {"get": {"description": "获取所有应用分类", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["分类管理"], "summary": "获取应用分类列表", "parameters": [{"type": "boolean", "default": false, "description": "是否包含未激活的分类", "name": "include_inactive", "in": "query"}], "responses": {"200": {"description": "分类列表", "schema": {"type": "array", "items": {"$ref": "#/definitions/api.CategoryResponse"}}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}, "post": {"description": "创建一个新的应用分类", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["分类管理"], "summary": "创建应用分类", "parameters": [{"type": "string", "description": "Bearer 用户令牌", "name": "Authorization", "in": "header", "required": true}, {"description": "分类信息", "name": "category", "in": "body", "required": true, "schema": {"$ref": "#/definitions/api.CreateCategoryRequest"}}], "responses": {"201": {"description": "创建成功", "schema": {"$ref": "#/definitions/api.CategoryResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/categories/root": {"get": {"description": "获取所有根应用分类（没有父分类的分类）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["分类管理"], "summary": "获取根应用分类列表", "parameters": [{"type": "boolean", "default": false, "description": "是否包含未激活的分类", "name": "include_inactive", "in": "query"}], "responses": {"200": {"description": "根分类列表", "schema": {"type": "array", "items": {"$ref": "#/definitions/api.CategoryResponse"}}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/categories/{id}": {"get": {"description": "获取应用分类详情", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["分类管理"], "summary": "获取应用分类", "parameters": [{"type": "integer", "description": "分类ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "分类详情", "schema": {"$ref": "#/definitions/api.CategoryDetailResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "404": {"description": "分类不存在", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}, "put": {"description": "更新应用分类信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["分类管理"], "summary": "更新应用分类", "parameters": [{"type": "string", "description": "Bearer 用户令牌", "name": "Authorization", "in": "header", "required": true}, {"type": "integer", "description": "分类ID", "name": "id", "in": "path", "required": true}, {"description": "分类信息", "name": "category", "in": "body", "required": true, "schema": {"$ref": "#/definitions/api.UpdateCategoryRequest"}}], "responses": {"200": {"description": "更新成功", "schema": {"$ref": "#/definitions/api.CategoryResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "404": {"description": "分类不存在", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}, "delete": {"description": "删除应用分类", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["分类管理"], "summary": "删除应用分类", "parameters": [{"type": "string", "description": "Bearer 用户令牌", "name": "Authorization", "in": "header", "required": true}, {"type": "integer", "description": "分类ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "删除成功", "schema": {"$ref": "#/definitions/api.SuccessResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "404": {"description": "分类不存在", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/dashboard/analytics/categories": {"get": {"security": [{"Bearer": []}], "description": "获取各应用分类的应用数量和下载统计", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仪表盘"], "summary": "获取分类统计", "responses": {"200": {"description": "返回分类统计数据", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/models.CategoryStats"}}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/dashboard/analytics/popular-apps": {"get": {"security": [{"Bearer": []}], "description": "获取平台上下载量和评分最高的应用", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仪表盘"], "summary": "获取热门应用", "parameters": [{"type": "integer", "default": 10, "description": "返回数量，默认10个", "name": "limit", "in": "query"}], "responses": {"200": {"description": "返回热门应用列表", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/models.PopularApp"}}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/dashboard/analytics/summary": {"get": {"security": [{"Bearer": []}], "description": "获取平台总体数据摘要，包括用户、应用、下载、评论等统计", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仪表盘"], "summary": "获取分析页摘要数据", "responses": {"200": {"description": "返回分析页摘要数据", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.AnalyticsSummary"}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/dashboard/analytics/trend": {"get": {"security": [{"Bearer": []}], "description": "获取用户、应用、下载、开发者等趋势数据", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仪表盘"], "summary": "获取趋势分析数据", "parameters": [{"type": "integer", "default": 30, "description": "统计天数，默认30天", "name": "days", "in": "query"}], "responses": {"200": {"description": "返回趋势分析数据", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.AnalyticsTrend"}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/dashboard/monitoring/alerts": {"get": {"security": [{"Bearer": []}], "description": "获取系统告警事件", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仪表盘"], "summary": "获取告警事件", "parameters": [{"type": "integer", "default": 1, "description": "页码，默认1", "name": "page", "in": "query"}, {"type": "integer", "default": 20, "description": "每页数量，默认20", "name": "page_size", "in": "query"}, {"type": "string", "description": "状态过滤(active/resolved)", "name": "status", "in": "query"}], "responses": {"200": {"description": "返回告警事件和分页信息", "schema": {"allOf": [{"$ref": "#/definitions/api.PageResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/models.AlertEvent"}}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/dashboard/monitoring/data": {"get": {"security": [{"Bearer": []}], "description": "获取系统运行状态监控数据", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仪表盘"], "summary": "获取监控数据", "responses": {"200": {"description": "返回监控数据", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/api.MonitoringData"}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/dashboard/monitoring/logs": {"get": {"security": [{"Bearer": []}], "description": "获取系统运行日志记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仪表盘"], "summary": "获取系统日志", "parameters": [{"type": "integer", "default": 1, "description": "页码，默认1", "name": "page", "in": "query"}, {"type": "integer", "default": 20, "description": "每页数量，默认20", "name": "page_size", "in": "query"}, {"type": "string", "description": "日志级别过滤(info/warning/error/critical)", "name": "level", "in": "query"}], "responses": {"200": {"description": "返回系统日志和分页信息", "schema": {"allOf": [{"$ref": "#/definitions/api.PageResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/models.SystemLog"}}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/dashboard/workbench/activities": {"get": {"security": [{"Bearer": []}], "description": "获取当前用户的最近活动记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仪表盘"], "summary": "获取最近活动", "parameters": [{"type": "integer", "default": 10, "description": "返回数量，默认10条", "name": "limit", "in": "query"}], "responses": {"200": {"description": "返回最近活动列表", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/models.RecentActivity"}}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/dashboard/workbench/summary": {"get": {"security": [{"Bearer": []}], "description": "获取当前用户的工作台摘要数据", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仪表盘"], "summary": "获取工作台摘要", "responses": {"200": {"description": "返回工作台摘要数据", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.WorkbenchSummary"}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/dashboard/workbench/tasks": {"get": {"security": [{"Bearer": []}], "description": "获取当前用户的任务列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仪表盘"], "summary": "获取任务列表", "parameters": [{"type": "integer", "default": 1, "description": "页码，默认1", "name": "page", "in": "query"}, {"type": "integer", "default": 20, "description": "每页数量，默认20", "name": "page_size", "in": "query"}, {"type": "string", "description": "任务状态过滤(pending/in_progress/completed)", "name": "status", "in": "query"}], "responses": {"200": {"description": "返回任务列表和分页信息", "schema": {"allOf": [{"$ref": "#/definitions/api.PageResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/models.TaskItem"}}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的任务", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仪表盘"], "summary": "创建任务", "parameters": [{"description": "任务信息", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.TaskItem"}}], "responses": {"200": {"description": "创建成功，返回任务详情", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.TaskItem"}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/dashboard/workbench/tasks/{id}": {"put": {"security": [{"Bearer": []}], "description": "更新任务信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仪表盘"], "summary": "更新任务", "parameters": [{"type": "integer", "description": "任务ID", "name": "id", "in": "path", "required": true}, {"description": "任务信息", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.TaskItem"}}], "responses": {"200": {"description": "更新成功，返回任务详情", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.TaskItem"}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "403": {"description": "无权限操作此任务", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "任务不存在", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定的任务", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["仪表盘"], "summary": "删除任务", "parameters": [{"type": "integer", "description": "任务ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "删除成功", "schema": {"$ref": "#/definitions/api.Response"}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "403": {"description": "无权限操作此任务", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "任务不存在", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/developers/verify": {"post": {"security": [{"Bearer": []}], "description": "用户提交开发者认证申请", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["开发者"], "summary": "提交开发者认证", "parameters": [{"description": "开发者认证信息", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/api.DeveloperVerifyRequest"}}], "responses": {"200": {"description": "提交成功，返回认证信息", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/api.DeveloperVerifyResponse"}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "403": {"description": "已是开发者或认证正在处理中", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/developers/verify/status": {"get": {"security": [{"Bearer": []}], "description": "获取当前用户的开发者认证状态", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["开发者"], "summary": "获取开发者认证状态", "responses": {"200": {"description": "返回认证状态", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/api.DeveloperVerifyResponse"}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "用户不存在", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/health": {"get": {"description": "检查API服务是否正常运行", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统"], "summary": "健康检查接口", "responses": {"200": {"description": "返回服务状态和时间", "schema": {"type": "object", "additionalProperties": true}}}}}, "/reviewer/apps/pending": {"get": {"security": [{"Bearer": []}], "description": "获取所有待审核的应用", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["审核员"], "summary": "获取待审核应用列表", "parameters": [{"type": "integer", "description": "页码，默认1", "name": "page", "in": "query"}, {"type": "integer", "description": "每页数量，默认20", "name": "limit", "in": "query"}], "responses": {"200": {"description": "返回待审核应用列表", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/api.PagedResponse"}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/reviewer/apps/{id}/review": {"post": {"security": [{"Bearer": []}], "description": "审核员审核应用", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["审核员"], "summary": "审核应用", "parameters": [{"type": "integer", "description": "应用ID", "name": "id", "in": "path", "required": true}, {"description": "审核信息", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/api.AppReviewRequest"}}], "responses": {"200": {"description": "审核成功", "schema": {"$ref": "#/definitions/api.Response"}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "应用不存在", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/search/apps": {"get": {"description": "使用Elasticsearch搜索应用，支持关键词、分类、标签等多种过滤条件", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["搜索"], "summary": "搜索应用", "parameters": [{"type": "string", "description": "搜索关键词", "name": "keyword", "in": "query"}, {"type": "string", "description": "应用分类", "name": "category", "in": "query"}, {"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "description": "标签列表", "name": "tags", "in": "query"}, {"type": "number", "description": "最低评分", "name": "min_rating", "in": "query"}, {"type": "boolean", "description": "是否认证", "name": "is_verified", "in": "query"}, {"type": "boolean", "description": "是否推荐", "name": "is_featured", "in": "query"}, {"enum": ["name", "download_count", "rating"], "type": "string", "description": "排序字段(name/download_count/rating)", "name": "sort_by", "in": "query"}, {"enum": ["asc", "desc"], "type": "string", "description": "排序方向(asc/desc)", "name": "sort_order", "in": "query"}, {"type": "integer", "default": 1, "description": "页码，默认1", "name": "page", "in": "query"}, {"type": "integer", "default": 20, "description": "每页数量，默认20", "name": "page_size", "in": "query"}], "responses": {"200": {"description": "搜索结果", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/services.SearchResponse"}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/search/initialize": {"post": {"security": [{"Bearer": []}], "description": "创建Elasticsearch索引并同步所有应用数据（管理员功能）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["搜索"], "summary": "初始化搜索索引", "responses": {"200": {"description": "初始化成功", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/search/initialize-all": {"post": {"security": [{"Bearer": []}], "description": "初始化应用、用户、评论、标签的Elasticsearch索引（管理员功能）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["搜索"], "summary": "初始化所有搜索索引", "responses": {"200": {"description": "初始化成功", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/search/reviews": {"get": {"description": "使用Elasticsearch搜索评论，支持评论内容、用户、应用等搜索", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["搜索"], "summary": "搜索评论", "parameters": [{"type": "string", "description": "搜索关键词", "name": "keyword", "in": "query"}, {"type": "integer", "description": "用户ID", "name": "user_id", "in": "query"}, {"type": "integer", "description": "应用ID", "name": "application_id", "in": "query"}, {"type": "integer", "description": "最低评分", "name": "min_rating", "in": "query"}, {"type": "integer", "description": "最高评分", "name": "max_rating", "in": "query"}, {"type": "string", "description": "评论状态", "name": "status", "in": "query"}, {"type": "boolean", "description": "是否有开发者回复", "name": "has_dev_reply", "in": "query"}, {"enum": ["created_at", "rating", "like_count"], "type": "string", "description": "排序字段", "name": "sort_by", "in": "query"}, {"enum": ["asc", "desc"], "type": "string", "description": "排序方向", "name": "sort_order", "in": "query"}, {"type": "integer", "default": 1, "description": "页码，默认1", "name": "page", "in": "query"}, {"type": "integer", "default": 20, "description": "每页数量，默认20", "name": "page_size", "in": "query"}], "responses": {"200": {"description": "搜索结果", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/services.ReviewSearchResponse"}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/search/suggestions": {"get": {"description": "根据输入的关键词获取搜索建议", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["搜索"], "summary": "获取搜索建议", "parameters": [{"type": "string", "description": "搜索关键词", "name": "q", "in": "query", "required": true}, {"type": "integer", "default": 10, "description": "建议数量限制，默认10", "name": "limit", "in": "query"}], "responses": {"200": {"description": "搜索建议列表", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "string"}}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/search/sync": {"post": {"security": [{"Bearer": []}], "description": "重新同步所有应用数据到Elasticsearch（管理员功能）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["搜索"], "summary": "同步搜索索引", "responses": {"200": {"description": "同步成功", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/search/tags": {"get": {"description": "使用Elasticsearch搜索标签，支持标签名称、描述等搜索", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["搜索"], "summary": "搜索标签", "parameters": [{"type": "string", "description": "搜索关键词", "name": "keyword", "in": "query"}, {"type": "boolean", "description": "是否活跃", "name": "is_active", "in": "query"}, {"enum": ["name", "app_count", "created_at"], "type": "string", "description": "排序字段", "name": "sort_by", "in": "query"}, {"enum": ["asc", "desc"], "type": "string", "description": "排序方向", "name": "sort_order", "in": "query"}, {"type": "integer", "default": 1, "description": "页码，默认1", "name": "page", "in": "query"}, {"type": "integer", "default": 20, "description": "每页数量，默认20", "name": "page_size", "in": "query"}], "responses": {"200": {"description": "搜索结果", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/services.TagSearchResponse"}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/search/tags/stats": {"get": {"security": [{"Bearer": []}], "description": "获取标签使用统计信息（管理员功能）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["搜索"], "summary": "获取标签统计", "responses": {"200": {"description": "统计结果", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/services.TagStatsDocument"}}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/search/tags/suggest": {"get": {"description": "根据输入前缀提供标签建议", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["搜索"], "summary": "标签自动补全", "parameters": [{"type": "string", "description": "搜索前缀", "name": "prefix", "in": "query", "required": true}, {"type": "integer", "default": 10, "description": "建议数量限制，默认10", "name": "limit", "in": "query"}], "responses": {"200": {"description": "建议列表", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "string"}}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/search/users": {"get": {"security": [{"Bearer": []}], "description": "使用Elasticsearch搜索用户，支持用户名、邮箱、开发者信息等搜索（管理员功能）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["搜索"], "summary": "搜索用户", "parameters": [{"type": "string", "description": "搜索关键词", "name": "keyword", "in": "query"}, {"type": "string", "description": "用户角色", "name": "role", "in": "query"}, {"type": "string", "description": "用户状态", "name": "status", "in": "query"}, {"type": "boolean", "description": "是否为开发者", "name": "is_developer", "in": "query"}, {"type": "string", "description": "认证状态", "name": "verify_status", "in": "query"}, {"enum": ["username", "created_at", "last_login_at", "login_count"], "type": "string", "description": "排序字段", "name": "sort_by", "in": "query"}, {"enum": ["asc", "desc"], "type": "string", "description": "排序方向", "name": "sort_order", "in": "query"}, {"type": "integer", "default": 1, "description": "页码，默认1", "name": "page", "in": "query"}, {"type": "integer", "default": 20, "description": "每页数量，默认20", "name": "page_size", "in": "query"}], "responses": {"200": {"description": "搜索结果", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/services.UserSearchResponse"}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/stats/apps/{id}/downloads": {"get": {"security": [{"Bearer": []}], "description": "获取应用的下载统计数据，包括总数、每日统计和设备类型统计", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["统计"], "summary": "获取应用下载统计", "parameters": [{"type": "integer", "description": "应用ID", "name": "id", "in": "path", "required": true}, {"type": "integer", "default": 30, "description": "统计天数，默认30天", "name": "days", "in": "query"}], "responses": {"200": {"description": "返回下载统计数据", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/api.AppDownloadStatsResponse"}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "403": {"description": "无权限查看此应用统计", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "应用不存在", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/stats/downloads": {"get": {"security": [{"Bearer": []}], "description": "获取当前登录用户的应用下载记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["统计"], "summary": "获取用户下载记录", "parameters": [{"type": "integer", "default": 1, "description": "页码，默认1", "name": "page", "in": "query"}, {"type": "integer", "default": 20, "description": "每页数量，默认20", "name": "page_size", "in": "query"}], "responses": {"200": {"description": "返回下载记录和分页信息", "schema": {"allOf": [{"$ref": "#/definitions/api.PageResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/api.DownloadRecordResponse"}}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/tags": {"get": {"description": "获取所有应用标签", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["标签管理"], "summary": "获取应用标签列表", "parameters": [{"type": "boolean", "default": false, "description": "是否包含未激活的标签", "name": "include_inactive", "in": "query"}], "responses": {"200": {"description": "标签列表", "schema": {"type": "array", "items": {"$ref": "#/definitions/api.TagResponse"}}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}, "post": {"description": "创建一个新的应用标签", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["标签管理"], "summary": "创建应用标签", "parameters": [{"type": "string", "description": "Bearer 用户令牌", "name": "Authorization", "in": "header", "required": true}, {"description": "标签信息", "name": "tag", "in": "body", "required": true, "schema": {"$ref": "#/definitions/api.CreateTagRequest"}}], "responses": {"201": {"description": "创建成功", "schema": {"$ref": "#/definitions/api.TagResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/tags/{id}": {"get": {"description": "获取应用标签详情", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["标签管理"], "summary": "获取应用标签", "parameters": [{"type": "integer", "description": "标签ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "标签详情", "schema": {"$ref": "#/definitions/api.TagAppCountResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "404": {"description": "标签不存在", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}, "put": {"description": "更新应用标签信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["标签管理"], "summary": "更新应用标签", "parameters": [{"type": "string", "description": "Bearer 用户令牌", "name": "Authorization", "in": "header", "required": true}, {"type": "integer", "description": "标签ID", "name": "id", "in": "path", "required": true}, {"description": "标签信息", "name": "tag", "in": "body", "required": true, "schema": {"$ref": "#/definitions/api.UpdateTagRequest"}}], "responses": {"200": {"description": "更新成功", "schema": {"$ref": "#/definitions/api.TagResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "404": {"description": "标签不存在", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}, "delete": {"description": "删除应用标签", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["标签管理"], "summary": "删除应用标签", "parameters": [{"type": "string", "description": "Bearer 用户令牌", "name": "Authorization", "in": "header", "required": true}, {"type": "integer", "description": "标签ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "删除成功", "schema": {"$ref": "#/definitions/api.SuccessResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "404": {"description": "标签不存在", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/tags/{id}/apps": {"get": {"description": "获取包含指定标签的所有应用", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["标签管理"], "summary": "获取指定标签的应用", "parameters": [{"type": "integer", "description": "标签ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "应用列表", "schema": {"type": "array", "items": {"$ref": "#/definitions/api.AppResponse"}}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "404": {"description": "标签不存在", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/users/login": {"post": {"description": "登录获取认证令牌", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户"], "summary": "用户登录", "parameters": [{"description": "登录信息", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/api.LoginRequest"}}], "responses": {"200": {"description": "登录成功，返回token和用户信息", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": true}}}]}}, "400": {"description": "参数错误/用户名或密码错误", "schema": {"$ref": "#/definitions/api.Response"}}, "403": {"description": "账号已被禁用", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/users/logout": {"post": {"security": [{"Bearer": []}], "description": "使当前用户的JWT令牌失效", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户"], "summary": "用户退出登录", "responses": {"200": {"description": "登出成功", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/users/profile": {"get": {"security": [{"Bearer": []}], "description": "获取当前登录用户的详细资料", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户"], "summary": "获取用户资料", "responses": {"200": {"description": "返回用户资料", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/api.UserResponse"}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "用户不存在", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}, "put": {"security": [{"Bearer": []}], "description": "更新当前登录用户的资料信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户"], "summary": "更新用户资料", "parameters": [{"description": "更新的用户资料", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/api.UpdateProfileRequest"}}], "responses": {"200": {"description": "更新成功，返回更新后的用户资料", "schema": {"allOf": [{"$ref": "#/definitions/api.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/api.UserResponse"}}}]}}, "400": {"description": "参数错误/用户名或邮箱已存在/密码错误", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "用户不存在", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/users/register": {"post": {"description": "创建新用户账号", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户"], "summary": "用户注册", "parameters": [{"description": "用户注册信息", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/api.RegisterRequest"}}], "responses": {"200": {"description": "注册成功", "schema": {"$ref": "#/definitions/api.Response"}}, "400": {"description": "参数错误/用户名或邮箱已存在", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "服务器错误", "schema": {"$ref": "#/definitions/api.Response"}}}}}}, "definitions": {"api.AdminCreateUserRequest": {"type": "object", "required": ["email", "password", "role", "username"], "properties": {"email": {"type": "string"}, "password": {"type": "string", "maxLength": 50, "minLength": 6}, "phone": {"type": "string"}, "role": {"type": "string", "enum": ["user", "developer", "operator", "reviewer", "admin"]}, "username": {"type": "string", "maxLength": 50, "minLength": 3}}}, "api.AdminVerifyRequest": {"type": "object", "required": ["verify_status"], "properties": {"verify_reason": {"type": "string", "maxLength": 255}, "verify_status": {"type": "string", "enum": ["pending", "approved", "rejected"]}}}, "api.AppDetailsResponse": {"type": "object", "properties": {"average_rating": {"type": "number"}, "banner_image": {"type": "string"}, "category": {"type": "string"}, "company_name": {"type": "string"}, "created_at": {"type": "string"}, "current_version": {"type": "string"}, "description": {"type": "string"}, "developer_id": {"type": "integer"}, "developer_name": {"type": "string"}, "download_count": {"type": "integer"}, "icon": {"type": "string"}, "id": {"type": "integer"}, "is_editor": {"type": "boolean"}, "is_featured": {"type": "boolean"}, "is_top": {"type": "boolean"}, "is_verified": {"type": "boolean"}, "min_open_harmony_os_ver": {"type": "string"}, "name": {"type": "string"}, "package": {"type": "string"}, "privacy_url": {"type": "string"}, "rating_count": {"type": "integer"}, "release_date": {"type": "string"}, "short_desc": {"type": "string"}, "size": {"type": "integer"}, "status": {"type": "string"}, "tags": {"type": "string"}, "updated_at": {"type": "string"}, "website": {"type": "string"}, "website_url": {"type": "string"}}}, "api.AppDownloadStatsResponse": {"type": "object", "properties": {"daily_stats": {"type": "array", "items": {"$ref": "#/definitions/models.AppDownloadStats"}}, "device_stats": {"type": "array", "items": {"$ref": "#/definitions/models.DeviceDownloadStats"}}, "total_downloads": {"type": "integer"}}}, "api.AppResponse": {"description": "应用响应", "type": "object", "properties": {"average_rating": {"type": "number"}, "category_id": {"type": "integer"}, "created_at": {"type": "string"}, "current_versions": {"type": "string"}, "deleted_at": {"type": "string"}, "description": {"type": "string"}, "developer_id": {"type": "integer"}, "download_count": {"type": "integer"}, "icon": {"type": "string"}, "id": {"type": "integer"}, "min_android_sdk": {"type": "integer"}, "name": {"type": "string"}, "package_name": {"type": "string"}, "review_count": {"type": "integer"}, "status": {"type": "string"}, "updated_at": {"type": "string"}}}, "api.AppReviewRequest": {"type": "object", "required": ["status"], "properties": {"reason": {"type": "string"}, "status": {"type": "string", "enum": ["approved", "rejected"]}}}, "api.AppTagRequest": {"type": "object", "required": ["tag_ids"], "properties": {"tag_ids": {"type": "array", "items": {"type": "integer"}}}}, "api.CategoryDetailResponse": {"description": "分类详情与子分类", "type": "object", "properties": {"category": {"description": "分类信息", "allOf": [{"$ref": "#/definitions/api.CategoryResponse"}]}, "subcategories": {"description": "子分类列表", "type": "array", "items": {"$ref": "#/definitions/api.CategoryResponse"}}}}, "api.CategoryResponse": {"description": "分类信息响应", "type": "object", "properties": {"created_at": {"description": "创建时间", "type": "string"}, "deleted_at": {"description": "删除时间", "type": "string"}, "description": {"description": "分类描述", "type": "string"}, "icon": {"description": "分类图标URL", "type": "string"}, "id": {"description": "分类ID", "type": "integer"}, "is_active": {"description": "是否启用", "type": "boolean"}, "name": {"description": "分类名称", "type": "string"}, "parent_id": {"description": "父分类ID", "type": "integer"}, "sort_order": {"description": "排序权重", "type": "integer"}, "updated_at": {"description": "更新时间", "type": "string"}}}, "api.CreateAppRequest": {"type": "object", "required": ["category", "icon", "min_open_harmony_os_ver", "name", "package"], "properties": {"banner_image": {"type": "string"}, "category": {"type": "string"}, "description": {"type": "string"}, "icon": {"type": "string"}, "min_open_harmony_os_ver": {"type": "string"}, "name": {"type": "string", "maxLength": 100, "minLength": 2}, "package": {"type": "string", "maxLength": 100, "minLength": 3}, "privacy_url": {"type": "string"}, "short_desc": {"type": "string", "maxLength": 200}, "tags": {"type": "string", "maxLength": 255}, "website_url": {"type": "string"}}}, "api.CreateCategoryRequest": {"type": "object", "required": ["name"], "properties": {"description": {"type": "string", "maxLength": 1000}, "icon": {"type": "string", "maxLength": 255}, "name": {"type": "string", "maxLength": 50}, "parent_id": {"type": "integer"}, "sort_order": {"type": "integer"}}}, "api.CreateReviewRequest": {"type": "object", "required": ["app_version", "content", "rating", "title"], "properties": {"app_version": {"type": "string", "maxLength": 50}, "content": {"type": "string", "maxLength": 1000, "minLength": 1}, "rating": {"type": "integer", "maximum": 5, "minimum": 1}, "title": {"type": "string", "maxLength": 100}}}, "api.CreateTagRequest": {"type": "object", "required": ["name"], "properties": {"color": {"type": "string", "maxLength": 20}, "description": {"type": "string", "maxLength": 1000}, "name": {"type": "string", "maxLength": 50}}}, "api.DevResponseRequest": {"type": "object", "required": ["content"], "properties": {"content": {"type": "string", "maxLength": 1000, "minLength": 1}}}, "api.DeveloperVerifyRequest": {"type": "object", "required": ["contact_email", "contact_phone", "description", "developer_address", "developer_name", "identity_card"], "properties": {"business_license": {"type": "string", "maxLength": 255}, "company_name": {"type": "string", "maxLength": 100}, "contact_email": {"type": "string"}, "contact_phone": {"type": "string"}, "description": {"type": "string", "maxLength": 1000, "minLength": 10}, "developer_address": {"type": "string", "maxLength": 255, "minLength": 5}, "developer_avatar": {"type": "string", "maxLength": 255}, "developer_name": {"type": "string", "maxLength": 100, "minLength": 2}, "identity_card": {"type": "string", "maxLength": 255}, "website": {"type": "string", "maxLength": 255}}}, "api.DeveloperVerifyResponse": {"type": "object", "properties": {"business_license": {"type": "string"}, "company_name": {"type": "string"}, "contact_email": {"type": "string"}, "contact_phone": {"type": "string"}, "description": {"type": "string"}, "developer_address": {"type": "string"}, "developer_avatar": {"type": "string"}, "developer_name": {"type": "string"}, "id": {"type": "integer"}, "identity_card": {"type": "string"}, "submitted_at": {"type": "string"}, "username": {"type": "string"}, "verified_at": {"type": "string"}, "verify_reason": {"type": "string"}, "verify_status": {"type": "string"}, "website": {"type": "string"}}}, "api.DownloadRecordResponse": {"type": "object", "properties": {"app_icon": {"type": "string"}, "app_name": {"type": "string"}, "application_id": {"type": "integer"}, "created_at": {"type": "string"}, "device_model": {"type": "string"}, "device_os": {"type": "string"}, "device_type": {"type": "string"}, "id": {"type": "integer"}, "status": {"type": "string"}, "user_id": {"type": "integer"}, "version_name": {"type": "string"}}}, "api.ErrorResponse": {"description": "错误响应", "type": "object", "properties": {"code": {"type": "integer", "example": 400}, "message": {"type": "string", "example": "请求参数错误"}}}, "api.LoginRequest": {"type": "object", "required": ["password", "username_or_email"], "properties": {"password": {"type": "string"}, "username_or_email": {"type": "string"}}}, "api.MonitoringData": {"type": "object", "properties": {"summary": {"$ref": "#/definitions/models.MonitoringSummary"}, "system_status": {"type": "object", "additionalProperties": true}}}, "api.PageResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "data": {}, "message": {"type": "string", "example": "操作成功"}, "page": {"type": "integer", "example": 1}, "page_size": {"type": "integer", "example": 20}, "total": {"type": "integer", "example": 100}}}, "api.PagedResponse": {"type": "object", "properties": {"data": {"description": "数据"}, "page": {"description": "当前页码", "type": "integer"}, "page_size": {"description": "每页数量", "type": "integer"}, "total": {"description": "总记录数", "type": "integer"}}}, "api.RegisterRequest": {"type": "object", "required": ["email", "password", "username"], "properties": {"email": {"type": "string"}, "password": {"type": "string", "maxLength": 50, "minLength": 6}, "phone": {"type": "string"}, "username": {"type": "string", "maxLength": 50, "minLength": 3}}}, "api.Response": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "data": {}, "message": {"type": "string", "example": "操作成功"}}}, "api.ReviewResponse": {"type": "object", "properties": {"app_name": {"type": "string"}, "app_version": {"type": "string"}, "application_id": {"type": "integer"}, "avatar": {"type": "string"}, "content": {"type": "string"}, "created_at": {"type": "string"}, "dev_response": {"type": "string"}, "id": {"type": "integer"}, "like_count": {"type": "integer"}, "rating": {"type": "integer"}, "status": {"type": "string"}, "title": {"type": "string"}, "updated_at": {"type": "string"}, "user_id": {"type": "integer"}, "username": {"type": "string"}}}, "api.SendEmailRequest": {"type": "object", "required": ["body", "subject", "to"], "properties": {"body": {"type": "string"}, "is_html": {"type": "boolean"}, "subject": {"type": "string"}, "to": {"type": "string"}}}, "api.SendNotificationRequest": {"type": "object", "required": ["content", "title", "type", "user_id"], "properties": {"content": {"type": "string"}, "title": {"type": "string"}, "type": {"type": "string", "enum": ["info", "warning", "error", "success"]}, "user_id": {"type": "integer"}}}, "api.SuccessResponse": {"description": "成功响应", "type": "object", "properties": {"code": {"type": "integer", "example": 200}, "data": {}, "message": {"type": "string", "example": "操作成功"}}}, "api.TagAppCountResponse": {"description": "标签及应用数量响应", "type": "object", "properties": {"app_count": {"type": "integer"}, "tag": {"$ref": "#/definitions/api.TagResponse"}}}, "api.TagResponse": {"description": "标签响应", "type": "object", "properties": {"color": {"type": "string"}, "created_at": {"type": "string"}, "deleted_at": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "integer"}, "is_active": {"type": "boolean"}, "name": {"type": "string"}, "updated_at": {"type": "string"}}}, "api.UpdateAppRequest": {"type": "object", "properties": {"banner_image": {"type": "string"}, "category": {"type": "string"}, "description": {"type": "string"}, "icon": {"type": "string"}, "min_open_harmony_os_ver": {"type": "string"}, "name": {"type": "string", "maxLength": 100, "minLength": 2}, "privacy_url": {"type": "string"}, "short_desc": {"type": "string", "maxLength": 200}, "tags": {"type": "string", "maxLength": 255}, "website_url": {"type": "string"}}}, "api.UpdateCategoryRequest": {"type": "object", "properties": {"description": {"type": "string", "maxLength": 1000}, "icon": {"type": "string", "maxLength": 255}, "is_active": {"type": "boolean"}, "name": {"type": "string", "maxLength": 50}, "parent_id": {"type": "integer"}, "sort_order": {"type": "integer"}}}, "api.UpdateProfileRequest": {"type": "object", "properties": {"avatar": {"type": "string"}, "developer_info": {"type": "object", "properties": {"company_name": {"type": "string"}, "description": {"type": "string"}, "developer_name": {"type": "string"}, "website": {"type": "string"}}}, "email": {"type": "string"}, "is_developer": {"type": "boolean"}, "new_password": {"type": "string", "maxLength": 50, "minLength": 6}, "old_password": {"type": "string"}, "phone": {"type": "string"}, "username": {"type": "string", "maxLength": 50, "minLength": 3}}}, "api.UpdateTagRequest": {"type": "object", "properties": {"color": {"type": "string", "maxLength": 20}, "description": {"type": "string", "maxLength": 1000}, "is_active": {"type": "boolean"}, "name": {"type": "string", "maxLength": 50}}}, "api.UpdateUserRoleRequest": {"type": "object", "required": ["role"], "properties": {"role": {"type": "string", "enum": ["user", "developer", "operator", "reviewer", "admin"]}}}, "api.UpdateUserStatusRequest": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string", "enum": ["active", "suspended", "banned"]}}}, "api.UploadResult": {"type": "object", "properties": {"file_url": {"type": "string"}}}, "api.UserActivityRequest": {"type": "object", "required": ["action", "resource", "user_id"], "properties": {"action": {"type": "string"}, "resource": {"type": "string"}, "resource_id": {"type": "integer"}, "user_id": {"type": "integer"}}}, "api.UserResponse": {"type": "object", "properties": {"avatar": {"type": "string"}, "company_name": {"type": "string"}, "created_at": {"type": "string"}, "description": {"type": "string"}, "developer_name": {"type": "string"}, "email": {"type": "string"}, "id": {"type": "integer"}, "is_developer": {"type": "boolean"}, "last_login_at": {"type": "string"}, "phone": {"type": "string"}, "role": {"type": "string"}, "status": {"type": "string"}, "username": {"type": "string"}, "verify_status": {"type": "string"}, "website": {"type": "string"}}}, "models.AlertEvent": {"type": "object", "properties": {"created_at": {"description": "创建时间", "type": "string"}, "description": {"description": "告警描述", "type": "string"}, "id": {"type": "integer"}, "resolved_at": {"description": "解决时间", "type": "string"}, "severity": {"description": "严重程度：low, medium, high, critical", "type": "string"}, "status": {"description": "状态：active, resolved", "type": "string"}, "type": {"description": "告警类型", "type": "string"}}}, "models.AnalyticsSummary": {"type": "object", "properties": {"new_apps_today": {"description": "今日新增应用", "type": "integer"}, "new_downloads_today": {"description": "今日新增下载", "type": "integer"}, "new_users_today": {"description": "今日新增用户", "type": "integer"}, "pending_apps_count": {"description": "待审核应用数", "type": "integer"}, "pending_reviews_count": {"description": "待审核评论数", "type": "integer"}, "total_apps": {"description": "总应用数", "type": "integer"}, "total_developers": {"description": "总开发者数", "type": "integer"}, "total_downloads": {"description": "总下载量", "type": "integer"}, "total_reviews": {"description": "总评论数", "type": "integer"}, "total_users": {"description": "总用户数", "type": "integer"}}}, "models.AnalyticsTrend": {"type": "object", "properties": {"app_trend": {"description": "应用增长趋势", "type": "array", "items": {"$ref": "#/definitions/models.DateValue"}}, "developer_trend": {"description": "开发者增长趋势", "type": "array", "items": {"$ref": "#/definitions/models.DateValue"}}, "download_trend": {"description": "下载增长趋势", "type": "array", "items": {"$ref": "#/definitions/models.DateValue"}}, "user_trend": {"description": "用户增长趋势", "type": "array", "items": {"$ref": "#/definitions/models.DateValue"}}}}, "models.AppDownloadStats": {"type": "object", "properties": {"date": {"type": "string"}, "downloads": {"type": "integer"}}}, "models.CategoryStats": {"type": "object", "properties": {"app_count": {"description": "应用数量", "type": "integer"}, "category_id": {"description": "分类ID", "type": "integer"}, "category_name": {"description": "分类名称", "type": "string"}, "download_count": {"description": "下载数量", "type": "integer"}}}, "models.DateValue": {"type": "object", "properties": {"date": {"description": "日期，格式 YYYY-MM-DD", "type": "string"}, "value": {"description": "值", "type": "integer"}}}, "models.DeviceDownloadStats": {"type": "object", "properties": {"device_type": {"type": "string"}, "downloads": {"type": "integer"}}}, "models.MonitoringSummary": {"type": "object", "properties": {"average_response_time": {"description": "平均响应时间(ms)", "type": "number"}, "cpu_usage": {"description": "CPU使用率", "type": "number"}, "database_connections": {"description": "数据库连接数", "type": "integer"}, "disk_usage": {"description": "磁盘使用率", "type": "number"}, "error_rate": {"description": "错误率", "type": "number"}, "memory_usage": {"description": "内存使用率", "type": "number"}, "requests_per_minute": {"description": "每分钟请求数", "type": "integer"}, "server_status": {"description": "服务器状态", "type": "string"}, "uptime_hours": {"description": "运行时间(小时)", "type": "number"}}}, "models.PopularApp": {"type": "object", "properties": {"app_icon": {"type": "string"}, "app_id": {"type": "integer"}, "app_name": {"type": "string"}, "category_name": {"type": "string"}, "developer_name": {"type": "string"}, "download_count": {"type": "integer"}, "rating": {"type": "number"}}}, "models.RecentActivity": {"type": "object", "properties": {"content": {"description": "活动内容", "type": "string"}, "created_at": {"description": "创建时间", "type": "string"}, "id": {"type": "integer"}, "type": {"description": "活动类型：app_create, app_update, review, download, etc.", "type": "string"}, "user_id": {"description": "活动所属用户ID", "type": "integer"}}}, "models.SystemLog": {"type": "object", "properties": {"created_at": {"description": "创建时间", "type": "string"}, "id": {"type": "integer"}, "level": {"description": "日志级别：info, warning, error, critical", "type": "string"}, "message": {"description": "日志消息", "type": "string"}, "source": {"description": "日志来源", "type": "string"}}}, "models.TaskItem": {"type": "object", "properties": {"completed_at": {"description": "完成时间", "type": "string"}, "created_at": {"description": "创建时间", "type": "string"}, "description": {"description": "任务描述", "type": "string"}, "due_date": {"description": "截止日期", "type": "string"}, "id": {"type": "integer"}, "priority": {"description": "优先级：low, medium, high", "type": "string"}, "status": {"description": "任务状态：pending, in_progress, completed", "type": "string"}, "title": {"description": "任务标题", "type": "string"}, "updated_at": {"description": "更新时间", "type": "string"}, "user_id": {"description": "任务所属用户ID", "type": "integer"}}}, "models.WorkbenchSummary": {"type": "object", "properties": {"average_rating": {"description": "我的应用平均评分", "type": "number"}, "completed_task_count": {"description": "已完成任务数量", "type": "integer"}, "my_app_count": {"description": "我的应用数量", "type": "integer"}, "new_reviews_count": {"description": "我的应用新评论数量", "type": "integer"}, "pending_apps_count": {"description": "我的待审核应用数量", "type": "integer"}, "total_downloads": {"description": "我的应用总下载量", "type": "integer"}, "total_task_count": {"description": "总任务数量", "type": "integer"}}}, "services.AppDocument": {"type": "object", "properties": {"average_rating": {"type": "number"}, "category": {"type": "string"}, "current_version": {"type": "string"}, "description": {"type": "string"}, "developer_id": {"type": "integer"}, "developer_name": {"type": "string"}, "download_count": {"type": "integer"}, "id": {"type": "integer"}, "is_editor": {"type": "boolean"}, "is_featured": {"type": "boolean"}, "is_top": {"type": "boolean"}, "is_verified": {"type": "boolean"}, "keywords": {"description": "用于搜索的关键词", "type": "array", "items": {"type": "string"}}, "min_open_harmony_os_ver": {"type": "string"}, "name": {"type": "string"}, "package": {"type": "string"}, "rating_count": {"type": "integer"}, "short_desc": {"type": "string"}, "status": {"type": "string"}, "tags": {"type": "string"}}}, "services.ReviewDocument": {"type": "object", "properties": {"app_name": {"type": "string"}, "app_package": {"type": "string"}, "app_version": {"type": "string"}, "application_id": {"type": "integer"}, "content": {"type": "string"}, "created_at": {"type": "string"}, "dev_response": {"type": "string"}, "id": {"type": "integer"}, "keywords": {"description": "用于搜索的关键词", "type": "array", "items": {"type": "string"}}, "like_count": {"type": "integer"}, "rating": {"type": "integer"}, "status": {"type": "string"}, "title": {"type": "string"}, "updated_at": {"type": "string"}, "user_id": {"type": "integer"}, "username": {"type": "string"}}}, "services.ReviewSearchResponse": {"type": "object", "properties": {"page": {"type": "integer"}, "page_size": {"type": "integer"}, "reviews": {"type": "array", "items": {"$ref": "#/definitions/services.ReviewDocument"}}, "total": {"type": "integer"}, "total_pages": {"type": "integer"}}}, "services.SearchResponse": {"type": "object", "properties": {"apps": {"type": "array", "items": {"$ref": "#/definitions/services.AppDocument"}}, "page": {"type": "integer"}, "page_size": {"type": "integer"}, "total": {"type": "integer"}, "total_pages": {"type": "integer"}}}, "services.TagDocument": {"type": "object", "properties": {"app_count": {"description": "使用该标签的应用数量", "type": "integer"}, "color": {"type": "string"}, "created_at": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "integer"}, "is_active": {"type": "boolean"}, "keywords": {"description": "用于搜索的关键词", "type": "array", "items": {"type": "string"}}, "name": {"type": "string"}, "updated_at": {"type": "string"}}}, "services.TagSearchResponse": {"type": "object", "properties": {"page": {"type": "integer"}, "page_size": {"type": "integer"}, "tags": {"type": "array", "items": {"$ref": "#/definitions/services.TagDocument"}}, "total": {"type": "integer"}, "total_pages": {"type": "integer"}}}, "services.TagStatsDocument": {"type": "object", "properties": {"app_count": {"type": "integer"}, "tag_id": {"type": "integer"}, "tag_name": {"type": "string"}, "total_apps": {"type": "integer"}, "usage_rate": {"description": "使用率", "type": "number"}}}, "services.UserDocument": {"type": "object", "properties": {"company_name": {"type": "string"}, "contact_email": {"type": "string"}, "contact_phone": {"type": "string"}, "created_at": {"type": "string"}, "description": {"type": "string"}, "developer_address": {"type": "string"}, "developer_name": {"type": "string"}, "email": {"type": "string"}, "id": {"type": "integer"}, "is_developer": {"type": "boolean"}, "keywords": {"description": "用于搜索的关键词", "type": "array", "items": {"type": "string"}}, "last_login_at": {"type": "string"}, "login_count": {"type": "integer"}, "phone": {"type": "string"}, "role": {"type": "string"}, "status": {"type": "string"}, "updated_at": {"type": "string"}, "username": {"type": "string"}, "verify_status": {"type": "string"}, "website": {"type": "string"}}}, "services.UserSearchResponse": {"type": "object", "properties": {"page": {"type": "integer"}, "page_size": {"type": "integer"}, "total": {"type": "integer"}, "total_pages": {"type": "integer"}, "users": {"type": "array", "items": {"$ref": "#/definitions/services.UserDocument"}}}}}, "securityDefinitions": {"Bearer": {"description": "请在值前加上 \"Bearer \" 前缀, 例如 \"Bearer abcde12345\". 所有需要认证的API都需要在请求头中带上此令牌。", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}