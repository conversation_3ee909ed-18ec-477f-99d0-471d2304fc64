if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface AppListPage_Params {
    apps?: AppModel[];
    loadingState?: LoadingState;
    currentPage?: number;
    hasMore?: boolean;
    isLoadingMore?: boolean;
    refreshing?: boolean;
    viewMode?: 'list' | 'grid';
    showFilter?: boolean;
    sortBy?: string;
    filterOptions?: FilterOptions;
    pageTitle?: string;
    searchParams?: AppSearchParams;
    deviceUtils?;
    apiService?;
}
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { DeviceUtils } from "@normalized:N&&&entry/src/main/ets/utils/DeviceUtils&";
import { ApiService } from "@normalized:N&&&entry/src/main/ets/services/ApiService&";
import { LoadingView, LoadingState, LoadMoreView } from "@normalized:N&&&entry/src/main/ets/components/LoadingView&";
import { AppCard } from "@normalized:N&&&entry/src/main/ets/components/AppCard&";
import type { AppModel, AppSearchParams } from '../models/App';
import preferences from "@ohos:data.preferences";
import hilog from "@ohos:hilog";
interface SortItem {
    key: string;
    label: string;
}
interface FilterOptions {
    isFree?: boolean;
    minRating?: number;
    maxSize?: number;
}
interface AppListPageParams {
    title?: string;
    searchParams?: string;
}
class AppListPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__apps = new ObservedPropertyObjectPU([], this, "apps");
        this.__loadingState = new ObservedPropertySimplePU(LoadingState.LOADING, this, "loadingState");
        this.__currentPage = new ObservedPropertySimplePU(1, this, "currentPage");
        this.__hasMore = new ObservedPropertySimplePU(true, this, "hasMore");
        this.__isLoadingMore = new ObservedPropertySimplePU(false, this, "isLoadingMore");
        this.__refreshing = new ObservedPropertySimplePU(false, this, "refreshing");
        this.__viewMode = new ObservedPropertySimplePU('list', this, "viewMode");
        this.__showFilter = new ObservedPropertySimplePU(false, this, "showFilter");
        this.__sortBy = new ObservedPropertySimplePU('downloadCount', this, "sortBy");
        this.__filterOptions = new ObservedPropertyObjectPU({}, this, "filterOptions");
        this.pageTitle = '应用列表';
        this.searchParams = {
            page: 1,
            page_size: 20
        };
        this.deviceUtils = DeviceUtils.getInstance();
        this.apiService = ApiService.getInstance();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: AppListPage_Params) {
        if (params.apps !== undefined) {
            this.apps = params.apps;
        }
        if (params.loadingState !== undefined) {
            this.loadingState = params.loadingState;
        }
        if (params.currentPage !== undefined) {
            this.currentPage = params.currentPage;
        }
        if (params.hasMore !== undefined) {
            this.hasMore = params.hasMore;
        }
        if (params.isLoadingMore !== undefined) {
            this.isLoadingMore = params.isLoadingMore;
        }
        if (params.refreshing !== undefined) {
            this.refreshing = params.refreshing;
        }
        if (params.viewMode !== undefined) {
            this.viewMode = params.viewMode;
        }
        if (params.showFilter !== undefined) {
            this.showFilter = params.showFilter;
        }
        if (params.sortBy !== undefined) {
            this.sortBy = params.sortBy;
        }
        if (params.filterOptions !== undefined) {
            this.filterOptions = params.filterOptions;
        }
        if (params.pageTitle !== undefined) {
            this.pageTitle = params.pageTitle;
        }
        if (params.searchParams !== undefined) {
            this.searchParams = params.searchParams;
        }
        if (params.deviceUtils !== undefined) {
            this.deviceUtils = params.deviceUtils;
        }
        if (params.apiService !== undefined) {
            this.apiService = params.apiService;
        }
    }
    updateStateVars(params: AppListPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__apps.purgeDependencyOnElmtId(rmElmtId);
        this.__loadingState.purgeDependencyOnElmtId(rmElmtId);
        this.__currentPage.purgeDependencyOnElmtId(rmElmtId);
        this.__hasMore.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoadingMore.purgeDependencyOnElmtId(rmElmtId);
        this.__refreshing.purgeDependencyOnElmtId(rmElmtId);
        this.__viewMode.purgeDependencyOnElmtId(rmElmtId);
        this.__showFilter.purgeDependencyOnElmtId(rmElmtId);
        this.__sortBy.purgeDependencyOnElmtId(rmElmtId);
        this.__filterOptions.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__apps.aboutToBeDeleted();
        this.__loadingState.aboutToBeDeleted();
        this.__currentPage.aboutToBeDeleted();
        this.__hasMore.aboutToBeDeleted();
        this.__isLoadingMore.aboutToBeDeleted();
        this.__refreshing.aboutToBeDeleted();
        this.__viewMode.aboutToBeDeleted();
        this.__showFilter.aboutToBeDeleted();
        this.__sortBy.aboutToBeDeleted();
        this.__filterOptions.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __apps: ObservedPropertyObjectPU<AppModel[]>;
    get apps() {
        return this.__apps.get();
    }
    set apps(newValue: AppModel[]) {
        this.__apps.set(newValue);
    }
    private __loadingState: ObservedPropertySimplePU<LoadingState>;
    get loadingState() {
        return this.__loadingState.get();
    }
    set loadingState(newValue: LoadingState) {
        this.__loadingState.set(newValue);
    }
    private __currentPage: ObservedPropertySimplePU<number>;
    get currentPage() {
        return this.__currentPage.get();
    }
    set currentPage(newValue: number) {
        this.__currentPage.set(newValue);
    }
    private __hasMore: ObservedPropertySimplePU<boolean>;
    get hasMore() {
        return this.__hasMore.get();
    }
    set hasMore(newValue: boolean) {
        this.__hasMore.set(newValue);
    }
    private __isLoadingMore: ObservedPropertySimplePU<boolean>;
    get isLoadingMore() {
        return this.__isLoadingMore.get();
    }
    set isLoadingMore(newValue: boolean) {
        this.__isLoadingMore.set(newValue);
    }
    private __refreshing: ObservedPropertySimplePU<boolean>;
    get refreshing() {
        return this.__refreshing.get();
    }
    set refreshing(newValue: boolean) {
        this.__refreshing.set(newValue);
    }
    private __viewMode: ObservedPropertySimplePU<'list' | 'grid'>;
    get viewMode() {
        return this.__viewMode.get();
    }
    set viewMode(newValue: 'list' | 'grid') {
        this.__viewMode.set(newValue);
    }
    private __showFilter: ObservedPropertySimplePU<boolean>;
    get showFilter() {
        return this.__showFilter.get();
    }
    set showFilter(newValue: boolean) {
        this.__showFilter.set(newValue);
    }
    private __sortBy: ObservedPropertySimplePU<string>;
    get sortBy() {
        return this.__sortBy.get();
    }
    set sortBy(newValue: string) {
        this.__sortBy.set(newValue);
    }
    private __filterOptions: ObservedPropertyObjectPU<FilterOptions>;
    get filterOptions() {
        return this.__filterOptions.get();
    }
    set filterOptions(newValue: FilterOptions) {
        this.__filterOptions.set(newValue);
    }
    private pageTitle: string;
    private searchParams: AppSearchParams;
    private deviceUtils;
    private apiService;
    aboutToAppear() {
        const params = this.getUIContext().getRouter().getParams() as AppListPageParams;
        this.pageTitle = params?.title || '应用列表';
        if (params?.searchParams) {
            try {
                const parsedParams = JSON.parse(params.searchParams) as AppSearchParams;
                this.searchParams = {
                    page: parsedParams.page ?? this.searchParams.page,
                    page_size: parsedParams.page_size ?? this.searchParams.page_size,
                    category: parsedParams.category ?? this.searchParams.category,
                    sort: parsedParams.sort ?? this.searchParams.sort,
                    keyword: parsedParams.keyword ?? this.searchParams.keyword
                };
            }
            catch (error) {
                hilog.error(0x0000, 'AppListPage', '解析搜索参数失败: %{public}s', JSON.stringify(error));
            }
        }
        this.loadApps();
    }
    /**
     * 检查并设置认证token
     */
    private async checkAndSetAuthToken(): Promise<void> {
        try {
            const context = this.getUIContext().getHostContext();
            const options: preferences.Options = { name: 'user_data' };
            const dataPreferences = preferences.getPreferencesSync(context, options);
            const token = dataPreferences.getSync('token', '') as string;
            if (token) {
                this.apiService.setAuthToken(token);
            }
        }
        catch (error) {
            hilog.error(0x0000, 'AppListPage', '检查登录状态失败: %{public}s', JSON.stringify(error));
        }
    }
    /**
     * 加载应用列表
     */
    private async loadApps(loadMore: boolean = false) {
        try {
            if (!loadMore) {
                this.loadingState = LoadingState.LOADING;
                this.currentPage = 1;
                this.apps = [];
                // 检查登录状态并设置token
                await this.checkAndSetAuthToken();
            }
            else {
                this.isLoadingMore = true;
            }
            const additionalParams: AppSearchParams = {
                sort: this.sortBy,
                page: loadMore ? this.currentPage + 1 : 1,
                page_size: 20
            };
            const params: AppSearchParams = {
                page: additionalParams.page ?? this.searchParams.page,
                page_size: additionalParams.page_size ?? this.searchParams.page_size,
                category: this.searchParams.category,
                sort: additionalParams.sort ?? this.searchParams.sort,
                keyword: this.searchParams.keyword
            };
            const response = await this.apiService.getAppList(params);
            if (response.code === 200 && response.data) {
                if (loadMore) {
                    // 使用concat方法替代spread操作符，并处理可能的undefined
                    const newApps = response.data.list || response.data.data || [];
                    this.apps = this.apps.concat(newApps);
                    this.currentPage++;
                }
                else {
                    this.apps = response.data.list || response.data.data || [];
                    this.currentPage = 1;
                }
                this.hasMore = response.data.pagination?.hasNext ?? false;
                if (!this.apps || this.apps.length === 0) {
                    this.loadingState = LoadingState.EMPTY;
                }
                else {
                    this.loadingState = LoadingState.SUCCESS;
                }
            }
            else {
                this.loadingState = LoadingState.ERROR;
            }
        }
        catch (error) {
            hilog.error(0x0000, 'AppListPage', '加载应用列表失败: %{public}s', JSON.stringify(error));
            this.loadingState = LoadingState.ERROR;
        }
        finally {
            this.isLoadingMore = false;
        }
    }
    /**
     * 刷新数据
     */
    private async refreshData() {
        this.refreshing = true;
        await this.loadApps();
        this.refreshing = false;
    }
    /**
     * 跳转到应用详情
     */
    private navigateToDetail(appId: string): void {
        this.getUIContext().getRouter().pushUrl({
            url: 'pages/AppDetailPage',
            params: { appId }
        });
    }
    /**
     * 应用筛选器
     */
    private FilterPanel(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.showFilter) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE) });
                        Context.animation({ duration: 300, curve: Curve.EaseInOut });
                        Column.width('100%');
                        Column.padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE));
                        Column.backgroundColor(Constants.COLORS.WHITE);
                        Column.borderRadius({ topLeft: Constants.BORDER_RADIUS.LARGE, topRight: Constants.BORDER_RADIUS.LARGE });
                        Context.animation(null);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 标题
                        Row.create();
                        // 标题
                        Row.width('100%');
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('筛选条件');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
                        Text.fontWeight(FontWeight.Bold);
                        Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                        Text.layoutWeight(1);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('×');
                        Text.fontSize(24);
                        Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                        Text.onClick((): void => {
                            this.showFilter = false;
                        });
                    }, Text);
                    Text.pop();
                    // 标题
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 排序方式
                        Column.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.SMALL) });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('排序方式');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Text.fontWeight(FontWeight.Medium);
                        Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                        Text.alignSelf(ItemAlign.Start);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Flex.create({ wrap: FlexWrap.Wrap });
                    }, Flex);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const sort = _item;
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create(sort.label);
                                Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                                Text.fontColor(this.sortBy === sort.key ? Constants.COLORS.WHITE : Constants.COLORS.TEXT_PRIMARY);
                                Text.backgroundColor(this.sortBy === sort.key ? Constants.COLORS.PRIMARY : Constants.COLORS.BACKGROUND_LIGHT);
                                Text.padding({ left: '12vp', right: '12vp', top: '6vp', bottom: '6vp' });
                                Text.borderRadius(Constants.BORDER_RADIUS.SMALL);
                                Text.onClick((): void => {
                                    this.sortBy = sort.key;
                                });
                            }, Text);
                            Text.pop();
                        };
                        this.forEachUpdateFunction(elmtId, [
                            { key: 'downloadCount', label: '下载量' },
                            { key: 'rating', label: '评分' },
                            { key: 'updatedAt', label: '更新时间' },
                            { key: 'createdAt', label: '发布时间' },
                            { key: 'name', label: '名称' },
                            { key: 'size', label: '大小' }
                        ], forEachItemGenFunction);
                    }, ForEach);
                    ForEach.pop();
                    Flex.pop();
                    // 排序方式
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Divider.create();
                        Divider.color(Constants.COLORS.BORDER);
                    }, Divider);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 价格筛选
                        Column.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.SMALL) });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('价格');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Text.fontWeight(FontWeight.Medium);
                        Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                        Text.alignSelf(ItemAlign.Start);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create({ space: 12 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('全部');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                        Text.fontColor(this.filterOptions.isFree === undefined ? Constants.COLORS.WHITE : Constants.COLORS.TEXT_PRIMARY);
                        Text.backgroundColor(this.filterOptions.isFree === undefined ? Constants.COLORS.PRIMARY : Constants.COLORS.BACKGROUND_LIGHT);
                        Text.padding({ left: '12vp', right: '12vp', top: '6vp', bottom: '6vp' });
                        Text.borderRadius(Constants.BORDER_RADIUS.SMALL);
                        Text.onClick((): void => {
                            this.filterOptions.isFree = undefined;
                        });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('免费');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                        Text.fontColor(this.filterOptions.isFree === true ? Constants.COLORS.WHITE : Constants.COLORS.TEXT_PRIMARY);
                        Text.backgroundColor(this.filterOptions.isFree === true ? Constants.COLORS.PRIMARY : Constants.COLORS.BACKGROUND_LIGHT);
                        Text.padding({ left: '12vp', right: '12vp', top: '6vp', bottom: '6vp' });
                        Text.borderRadius(Constants.BORDER_RADIUS.SMALL);
                        Text.onClick((): void => {
                            this.filterOptions.isFree = true;
                        });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('付费');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                        Text.fontColor(this.filterOptions.isFree === false ? Constants.COLORS.WHITE : Constants.COLORS.TEXT_PRIMARY);
                        Text.backgroundColor(this.filterOptions.isFree === false ? Constants.COLORS.PRIMARY : Constants.COLORS.BACKGROUND_LIGHT);
                        Text.padding({ left: '12vp', right: '12vp', top: '6vp', bottom: '6vp' });
                        Text.borderRadius(Constants.BORDER_RADIUS.SMALL);
                        Text.onClick((): void => {
                            this.filterOptions.isFree = false;
                        });
                    }, Text);
                    Text.pop();
                    Row.pop();
                    // 价格筛选
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Divider.create();
                        Divider.color(Constants.COLORS.BORDER);
                    }, Divider);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 评分筛选
                        Column.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.SMALL) });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('最低评分');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Text.fontWeight(FontWeight.Medium);
                        Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                        Text.alignSelf(ItemAlign.Start);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create({ space: 8 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const rating = _item;
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Row.create({ space: 4 });
                                Row.padding({ left: '8vp', right: '8vp', top: '4vp', bottom: '4vp' });
                                Row.backgroundColor(this.filterOptions.minRating === rating + 1 ? Constants.COLORS.PRIMARY : Constants.COLORS.BACKGROUND_LIGHT);
                                Row.borderRadius(Constants.BORDER_RADIUS.SMALL);
                                Row.onClick(() => {
                                    this.filterOptions.minRating = this.filterOptions.minRating === rating + 1 ? undefined : rating + 1;
                                });
                            }, Row);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                ForEach.create();
                                const forEachItemGenFunction = _item => {
                                    const star = _item;
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        Text.create('★');
                                        Text.fontSize(12);
                                        Text.fontColor(star <= rating + 1 ? Constants.COLORS.WARNING : Constants.COLORS.BORDER);
                                    }, Text);
                                    Text.pop();
                                };
                                this.forEachUpdateFunction(elmtId, [1, 2, 3, 4, 5], forEachItemGenFunction, (star: number) => star.toString(), false, false);
                            }, ForEach);
                            ForEach.pop();
                            Row.pop();
                        };
                        this.forEachUpdateFunction(elmtId, [0, 1, 2, 3, 4] as number[], forEachItemGenFunction, (rating: number) => rating.toString(), false, false);
                    }, ForEach);
                    ForEach.pop();
                    Row.pop();
                    // 评分筛选
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 操作按钮
                        Row.create({ space: 12 });
                        // 操作按钮
                        Row.width('100%');
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('重置');
                        Button.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Button.fontColor(Constants.COLORS.TEXT_PRIMARY);
                        Button.backgroundColor(Constants.COLORS.BACKGROUND_LIGHT);
                        Button.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
                        Button.layoutWeight(1);
                        Button.onClick(() => {
                            this.filterOptions = {};
                            this.sortBy = 'downloadCount';
                        });
                    }, Button);
                    Button.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('确定');
                        Button.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Button.fontColor(Constants.COLORS.WHITE);
                        Button.backgroundColor(Constants.COLORS.PRIMARY);
                        Button.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
                        Button.layoutWeight(1);
                        Button.onClick(() => {
                            this.showFilter = false;
                            this.loadApps();
                        });
                    }, Button);
                    Button.pop();
                    // 操作按钮
                    Row.pop();
                    Column.pop();
                });
            }
            else /**
             * 应用网格（平板设备）
             */ {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
    }
    /**
     * 应用网格（平板设备）
     */
    private AppGrid(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Grid.create();
            Grid.columnsTemplate(this.deviceUtils.isTablet() ? '1fr 1fr 1fr' : '1fr 1fr');
            Grid.rowsGap(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM));
            Grid.columnsGap(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM));
            Grid.padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM));
            Grid.scrollBar(BarState.Auto);
        }, Grid);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const app = _item;
                {
                    const itemCreation2 = (elmtId, isInitialRender) => {
                        GridItem.create(() => { }, false);
                    };
                    const observedDeepRender = () => {
                        this.observeComponentCreation2(itemCreation2, GridItem);
                        {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                if (isInitialRender) {
                                    let componentCall = new AppCard(this, {
                                        app: app,
                                        cardType: 'grid',
                                        showDownloadButton: true,
                                        onAppClick: (app: AppModel): void => this.navigateToDetail(app.id.toString())
                                    }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/AppListPage.ets", line: 342, col: 11 });
                                    ViewPU.create(componentCall);
                                    let paramsLambda = () => {
                                        return {
                                            app: app,
                                            cardType: 'grid',
                                            showDownloadButton: true,
                                            onAppClick: (app: AppModel): void => this.navigateToDetail(app.id.toString())
                                        };
                                    };
                                    componentCall.paramsGenerator_ = paramsLambda;
                                }
                                else {
                                    this.updateStateVarsOfChildByElmtId(elmtId, {
                                        app: app,
                                        cardType: 'grid',
                                        showDownloadButton: true
                                    });
                                }
                            }, { name: "AppCard" });
                        }
                        GridItem.pop();
                    };
                    observedDeepRender();
                }
            };
            this.forEachUpdateFunction(elmtId, this.apps, forEachItemGenFunction);
        }, ForEach);
        ForEach.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 加载更多
            if (this.hasMore || this.isLoadingMore) {
                this.ifElseBranchUpdateFunction(0, () => {
                    {
                        const itemCreation2 = (elmtId, isInitialRender) => {
                            GridItem.create(() => { }, false);
                            GridItem.columnStart(0);
                            GridItem.columnEnd(this.deviceUtils.isTablet() ? 2 : 1);
                        };
                        const observedDeepRender = () => {
                            this.observeComponentCreation2(itemCreation2, GridItem);
                            {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    if (isInitialRender) {
                                        let componentCall = new LoadMoreView(this, {
                                            isLoading: this.isLoadingMore,
                                            hasMore: this.hasMore,
                                            onLoadMore: () => {
                                                if (!this.isLoadingMore && this.hasMore) {
                                                    this.loadApps(true);
                                                }
                                            }
                                        }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/AppListPage.ets", line: 354, col: 11 });
                                        ViewPU.create(componentCall);
                                        let paramsLambda = () => {
                                            return {
                                                isLoading: this.isLoadingMore,
                                                hasMore: this.hasMore,
                                                onLoadMore: () => {
                                                    if (!this.isLoadingMore && this.hasMore) {
                                                        this.loadApps(true);
                                                    }
                                                }
                                            };
                                        };
                                        componentCall.paramsGenerator_ = paramsLambda;
                                    }
                                    else {
                                        this.updateStateVarsOfChildByElmtId(elmtId, {
                                            isLoading: this.isLoadingMore,
                                            hasMore: this.hasMore
                                        });
                                    }
                                }, { name: "LoadMoreView" });
                            }
                            GridItem.pop();
                        };
                        observedDeepRender();
                    }
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Grid.pop();
    }
    /**
     * 应用列表（手机设备）
     */
    private AppList(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            List.create({ space: 8 });
            List.scrollBar(BarState.Auto);
        }, List);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const app = _item;
                {
                    const itemCreation = (elmtId, isInitialRender) => {
                        ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                        itemCreation2(elmtId, isInitialRender);
                        if (!isInitialRender) {
                            ListItem.pop();
                        }
                        ViewStackProcessor.StopGetAccessRecording();
                    };
                    const itemCreation2 = (elmtId, isInitialRender) => {
                        ListItem.create(deepRenderFunction, true);
                    };
                    const deepRenderFunction = (elmtId, isInitialRender) => {
                        itemCreation(elmtId, isInitialRender);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            __Common__.create();
                            __Common__.margin({ left: 16, right: 16 });
                        }, __Common__);
                        {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                if (isInitialRender) {
                                    let componentCall = new AppCard(this, {
                                        app: app,
                                        cardType: 'list',
                                        showDownloadButton: true,
                                        onAppClick: (app: AppModel): void => this.navigateToDetail(app.id.toString())
                                    }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/AppListPage.ets", line: 383, col: 11 });
                                    ViewPU.create(componentCall);
                                    let paramsLambda = () => {
                                        return {
                                            app: app,
                                            cardType: 'list',
                                            showDownloadButton: true,
                                            onAppClick: (app: AppModel): void => this.navigateToDetail(app.id.toString())
                                        };
                                    };
                                    componentCall.paramsGenerator_ = paramsLambda;
                                }
                                else {
                                    this.updateStateVarsOfChildByElmtId(elmtId, {
                                        app: app,
                                        cardType: 'list',
                                        showDownloadButton: true
                                    });
                                }
                            }, { name: "AppCard" });
                        }
                        __Common__.pop();
                        ListItem.pop();
                    };
                    this.observeComponentCreation2(itemCreation2, ListItem);
                    ListItem.pop();
                }
            };
            this.forEachUpdateFunction(elmtId, this.apps, forEachItemGenFunction);
        }, ForEach);
        ForEach.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 加载更多
            if (this.hasMore || this.isLoadingMore) {
                this.ifElseBranchUpdateFunction(0, () => {
                    {
                        const itemCreation = (elmtId, isInitialRender) => {
                            ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                            itemCreation2(elmtId, isInitialRender);
                            if (!isInitialRender) {
                                ListItem.pop();
                            }
                            ViewStackProcessor.StopGetAccessRecording();
                        };
                        const itemCreation2 = (elmtId, isInitialRender) => {
                            ListItem.create(deepRenderFunction, true);
                        };
                        const deepRenderFunction = (elmtId, isInitialRender) => {
                            itemCreation(elmtId, isInitialRender);
                            {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    if (isInitialRender) {
                                        let componentCall = new LoadMoreView(this, {
                                            isLoading: this.isLoadingMore,
                                            hasMore: this.hasMore,
                                            onLoadMore: () => {
                                                if (!this.isLoadingMore && this.hasMore) {
                                                    this.loadApps(true);
                                                }
                                            }
                                        }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/AppListPage.ets", line: 396, col: 11 });
                                        ViewPU.create(componentCall);
                                        let paramsLambda = () => {
                                            return {
                                                isLoading: this.isLoadingMore,
                                                hasMore: this.hasMore,
                                                onLoadMore: () => {
                                                    if (!this.isLoadingMore && this.hasMore) {
                                                        this.loadApps(true);
                                                    }
                                                }
                                            };
                                        };
                                        componentCall.paramsGenerator_ = paramsLambda;
                                    }
                                    else {
                                        this.updateStateVarsOfChildByElmtId(elmtId, {
                                            isLoading: this.isLoadingMore,
                                            hasMore: this.hasMore
                                        });
                                    }
                                }, { name: "LoadMoreView" });
                            }
                            ListItem.pop();
                        };
                        this.observeComponentCreation2(itemCreation2, ListItem);
                        ListItem.pop();
                    }
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        List.pop();
    }
    /**
     * 获取排序标签
     */
    private getSortLabel(): string {
        switch (this.sortBy) {
            case 'downloadCount':
                return '下载量';
            case 'rating':
                return '评分';
            case 'updatedAt':
                return '更新时间';
            case 'createdAt':
                return '发布时间';
            case 'name':
                return '名称';
            case 'size':
                return '大小';
            default:
                return '排序';
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create({ alignContent: Alignment.Bottom });
            Stack.width('100%');
            Stack.height('100%');
            Stack.onClick(() => {
                if (this.showFilter) {
                    this.showFilter = false;
                }
            });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor(Constants.COLORS.BACKGROUND);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(56);
            // 顶部导航栏
            Row.padding({ left: '16vp', right: '16vp' });
            // 顶部导航栏
            Row.justifyContent(FlexAlign.SpaceBetween);
            // 顶部导航栏
            Row.alignItems(VerticalAlign.Center);
            // 顶部导航栏
            Row.backgroundColor(Constants.COLORS.WHITE);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('←');
            Text.fontSize(24);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.onClick((): void => {
                this.getUIContext().getRouter().back();
            });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.pageTitle);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
            Text.maxLines(1);
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 视图切换（仅平板显示）
            if (this.deviceUtils.isTablet()) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.viewMode === 'list' ? '☰' : '⊞');
                        Text.fontSize(20);
                        Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                        Text.onClick(() => {
                            this.viewMode = this.viewMode === 'list' ? 'grid' : 'list';
                        });
                    }, Text);
                    Text.pop();
                });
            }
            // 筛选按钮
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 筛选按钮
            Text.create('⚙');
            // 筛选按钮
            Text.fontSize(20);
            // 筛选按钮
            Text.fontColor(this.showFilter ? Constants.COLORS.PRIMARY : Constants.COLORS.TEXT_PRIMARY);
            // 筛选按钮
            Text.onClick((): void => {
                this.showFilter = !this.showFilter;
            });
        }, Text);
        // 筛选按钮
        Text.pop();
        Row.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 排序信息
            if (this.loadingState === LoadingState.SUCCESS && this.apps && this.apps.length > 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.width('100%');
                        Row.padding({ left: '16vp', right: '16vp', top: '8vp', bottom: '8vp' });
                        Row.backgroundColor(Constants.COLORS.BACKGROUND_LIGHT);
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`共 ${this.apps.length} 个应用`);
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                        Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                        Text.layoutWeight(1);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`按${this.getSortLabel()}排序`);
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                        Text.fontColor(Constants.COLORS.TEXT_HINT);
                    }, Text);
                    Text.pop();
                    Row.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.loadingState === LoadingState.LOADING) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, { state: LoadingState.LOADING }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/AppListPage.ets", line: 499, col: 11 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.LOADING
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.LOADING
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else if (this.loadingState === LoadingState.ERROR) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, {
                                    state: LoadingState.ERROR,
                                    onRetry: () => { this.loadApps(); }
                                }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/AppListPage.ets", line: 502, col: 11 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.ERROR,
                                        onRetry: () => { this.loadApps(); }
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.ERROR
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else if (this.loadingState === LoadingState.EMPTY) {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, {
                                    state: LoadingState.EMPTY,
                                    message: '暂无应用数据'
                                }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/AppListPage.ets", line: 508, col: 11 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.EMPTY,
                                        message: '暂无应用数据'
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.EMPTY,
                                    message: '暂无应用数据'
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(3, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 应用列表
                        Refresh.create({ refreshing: this.refreshing, offset: 64, friction: 100 });
                        // 应用列表
                        Refresh.onStateChange((refreshStatus: RefreshStatus) => {
                            if (refreshStatus === RefreshStatus.Refresh) {
                                this.refreshData();
                            }
                        });
                        // 应用列表
                        Refresh.layoutWeight(1);
                    }, Refresh);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.deviceUtils.isTablet() && this.viewMode === 'grid') {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.AppGrid.bind(this)();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                                this.AppList.bind(this)();
                            });
                        }
                    }, If);
                    If.pop();
                    // 应用列表
                    Refresh.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
        // 筛选面板
        this.FilterPanel.bind(this)();
        Stack.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "AppListPage";
    }
}
registerNamedRoute(() => new AppListPage(undefined, {}), "", { bundleName: "com.example.nexushub", moduleName: "entry", pagePath: "pages/AppListPage", pageFullPath: "entry/src/main/ets/pages/AppListPage", integratedHsp: "false", moduleType: "followWithHap" });
