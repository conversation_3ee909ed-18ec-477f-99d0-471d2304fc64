/**
 * 应用分类模型
 */
export interface CategoryModel {
  id: number;
  name: string;
  name_en?: string; // 可选字段，前端生成
  description: string;
  icon: string;
  color?: string; // 可选字段，前端生成
  sort_order: number;
  is_active: boolean;
  app_count?: number; // 可选字段，前端计算
  created_at: string;
  updated_at: string;
  deleted_at?: string | null; // 后端字段
  parent_id?: number | null; // 后端字段
}

/**
 * 分类列表响应模型
 */
export interface CategoryListResponse {
  code: number;
  message: string;
  data: CategoryModel[];
}

/**
 * 预定义分类
 */
export const PREDEFINED_CATEGORIES: CategoryModel[] = [
  {
    id: 1,
    name: '工具',
    name_en: 'Tools',
    description: '实用工具类应用',
    icon: '🔧',
    color: '#FF6B6B',
    sort_order: 1,
    is_active: true,
    app_count: 0,
    created_at: '',
    updated_at: ''
  },
  {
    id: 2,
    name: '游戏',
    name_en: 'Games',
    description: '休闲娱乐游戏',
    icon: '🎮',
    color: '#4ECDC4',
    sort_order: 2,
    is_active: true,
    app_count: 0,
    created_at: '',
    updated_at: ''
  },
  {
    id: 3,
    name: '教育',
    name_en: 'Education',
    description: '学习教育类应用',
    icon: '📚',
    color: '#45B7D1',
    sort_order: 3,
    is_active: true,
    app_count: 0,
    created_at: '',
    updated_at: ''
  },
  {
    id: 4,
    name: '生活',
    name_en: 'Lifestyle',
    description: '生活服务类应用',
    icon: '🏠',
    color: '#96CEB4',
    sort_order: 4,
    is_active: true,
    app_count: 0,
    created_at: '',
    updated_at: ''
  },
  {
    id: 5,
    name: '社交',
    name_en: 'Social',
    description: '社交通讯类应用',
    icon: '💬',
    color: '#FFEAA7',
    sort_order: 5,
    is_active: true,
    app_count: 0,
    created_at: '',
    updated_at: ''
  },
  {
    id: 6,
    name: '摄影',
    name_en: 'Photography',
    description: '摄影图像类应用',
    icon: '📷',
    color: '#DDA0DD',
    sort_order: 6,
    is_active: true,
    app_count: 0,
    created_at: '',
    updated_at: ''
  },
  {
    id: 7,
    name: '音乐',
    name_en: 'Music',
    description: '音乐音频类应用',
    icon: '🎵',
    color: '#98D8C8',
    sort_order: 7,
    is_active: true,
    app_count: 0,
    created_at: '',
    updated_at: ''
  },
  {
    id: 8,
    name: '视频',
    name_en: 'Video',
    description: '视频播放类应用',
    icon: '🎬',
    color: '#F7DC6F',
    sort_order: 8,
    is_active: true,
    app_count: 0,
    created_at: '',
    updated_at: ''
  },
  {
    id: 9,
    name: '新闻',
    name_en: 'News',
    description: '新闻资讯类应用',
    icon: '📰',
    color: '#BB8FCE',
    sort_order: 9,
    is_active: true,
    app_count: 0,
    created_at: '',
    updated_at: ''
  },
  {
    id: 10,
    name: '购物',
    name_en: 'Shopping',
    description: '购物消费类应用',
    icon: '🛒',
    color: '#85C1E9',
    sort_order: 10,
    is_active: true,
    app_count: 0,
    created_at: '',
    updated_at: ''
  },
  {
    id: 11,
    name: '旅行',
    name_en: 'Travel',
    description: '旅行出行类应用',
    icon: '✈️',
    color: '#82E0AA',
    sort_order: 11,
    is_active: true,
    app_count: 0,
    created_at: '',
    updated_at: ''
  },
  {
    id: 12,
    name: '健康',
    name_en: 'Health',
    description: '健康医疗类应用',
    icon: '🏥',
    color: '#F8C471',
    sort_order: 12,
    is_active: true,
    app_count: 0,
    created_at: '',
    updated_at: ''
  }
];