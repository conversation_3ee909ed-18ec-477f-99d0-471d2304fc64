package api

import (
	"net/http"
	"time"

	"nexushub-oh-back/config"
	"nexushub-oh-back/internal/middleware"
	"nexushub-oh-back/internal/models"
	"nexushub-oh-back/internal/services"
	"nexushub-oh-back/pkg/auth"
	"nexushub-oh-back/pkg/database"
	"nexushub-oh-back/pkg/logger"
	"nexushub-oh-back/pkg/messaging"
	"nexushub-oh-back/pkg/response"
	"nexushub-oh-back/pkg/storage"

	"github.com/gin-gonic/gin"

	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// SetupRouter 设置API路由
func SetupRouter(
	router *gin.Engine,
	db *database.PostgresDB,
	redis *database.RedisClient,
	storageClient storage.StorageProvider,
	jwtService *auth.JWTService,
	logtoService *auth.LogtoService,
	searchService *services.SearchService,
	userSearchService *services.UserSearchService,
	reviewSearchService *services.ReviewSearchService,
	tagSearchService *services.TagSearchService,
	rabbitMQClient *messaging.RabbitMQClient,
	cfg *config.Config,
) {
	// 全局中间件
	router.Use(middleware.Cors())
	router.Use(logger.GinLogger())

	// Swagger 文档路由
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// 快速API测试路由
	router.GET("/config", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"server_port":  cfg.Server.Port,
			"server_mode":  cfg.Server.Mode,
			"db_host":      cfg.Database.Host,
			"redis_addr":   cfg.Redis.Addr,
			"storage_type": cfg.Storage.Type,
		})
	})

	// 限流器
	var rateLimiter *middleware.RateLimiter
	if cfg != nil && cfg.RateLimit.Enable {
		rateLimiter = middleware.NewRateLimiterWithConfig(&cfg.RateLimit)
	} else {
		// 使用默认配置
		rateLimiter = middleware.NewRateLimiter(time.Minute, 60) // 每分钟60个请求
		rateLimiter.StartCleanupTask(time.Minute * 10)           // 每10分钟清理一次过期记录
	}

	// 检查数据库连接
	if db == nil {
		router.GET("/", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"status":        "running",
				"message":       "服务器运行中，但未连接数据库",
				"config_loaded": cfg != nil,
			})
		})
		return
	}

	// 控制器
	var gormDB *gorm.DB
	if db != nil {
		gormDB = db.DB
	}

	userController := NewUserController(gormDB, jwtService)
	appController := NewAppController(gormDB, storageClient, searchService)
	reviewController := NewReviewController(gormDB)
	statsController := NewStatsController(gormDB)
	categoryController := NewCategoryController(gormDB)
	tagController := NewTagController(gormDB)
	featuredCollectionController := NewFeaturedCollectionController(gormDB)
	dashboardController := NewDashboardController(gormDB)
	geographicController := NewGeographicController(gormDB)
	harmonyVersionController := NewHarmonyVersionController(gormDB)
	browseHistoryController := NewBrowseHistoryController(gormDB)
	helpCenterController := NewHelpCenterController(gormDB)
	feedbackController := NewFeedbackController(gormDB)
	carouselController := NewCarouselController(gormDB)

	// 消息队列控制器（仅在RabbitMQ客户端可用时初始化）
	var messageController *MessageController
	var messageService *services.MessageService
	if rabbitMQClient != nil {
		messageService = services.NewMessageService(rabbitMQClient)
		messageController = NewMessageController(messageService)
		// 初始化队列
		if err := messageService.InitializeQueues(); err != nil {
			logger.Warn("初始化消息队列失败", zap.Error(err))
		}
	}

	// 通知控制器
	var notificationController *NotificationController
	var notificationService *services.NotificationService
	if gormDB != nil {
		notificationService = services.NewNotificationService(gormDB, messageService, logger.Logger)
		notificationController = NewNotificationController(notificationService)
	}

	// 开发者控制器需要MessageService和NotificationService
	developerController := NewDeveloperController(gormDB, storageClient, messageService, notificationService)

	// 搜索控制器（仅在搜索服务可用时初始化）
	var searchController *SearchController
	if searchService != nil && userSearchService != nil && reviewSearchService != nil && tagSearchService != nil {
		searchController = NewSearchController(searchService, userSearchService, reviewSearchService, tagSearchService)
	}

	// API基础路径
	api := router.Group("/api/v1")
	{
		// 不需要认证的路由
		api.GET("/health", HealthCheck)

		// 公开API接口（无需认证）
		public := api.Group("/public")
		{
			// 地理位置相关
			public.GET("/geographic/country", geographicController.GetCountries)
			public.GET("/geographic/province", geographicController.GetProvinces)
			public.GET("/geographic/city/:province", geographicController.GetCities)
			public.GET("/geographic/district/:city", geographicController.GetDistricts)
			public.GET("/geographic/street/:district", geographicController.GetStreets)
			public.GET("/geographic/level", geographicController.GetGeographicByLevel)

			// 搜索相关
			if searchController != nil {
				public.GET("/search/apps", searchController.SearchApps)
				public.GET("/search/suggestions", searchController.SearchSuggestions)
				public.GET("/search/reviews", searchController.SearchReviews)
				public.GET("/search/tags", searchController.SearchTags)
				public.GET("/search/tags/suggest", searchController.SuggestTags)
			}

			// 获取应用详情
			public.GET("/apps/:id", appController.GetAppAnonymous)
			// 下载记录
			public.POST("/apps/:id/versions/:version_id/download", appController.RecordDownloadAnonymous)
			// 下载应用（获取下载链接）
			public.POST("/apps/:id/versions/:version_id/download-link", appController.DownloadApp)

			// 获取推荐应用
			public.GET("/apps/recommended", appController.GetRecommendedApps)
			// 获取热门应用
			public.GET("/apps/popular", appController.GetPopularApps)
			// 获取最新应用
			public.GET("/apps/latest", appController.GetLatestApps)
			// 获取应用列表（公开版本）
			public.GET("/apps", appController.GetPublicApps)
			// 获取精选应用
			public.GET("/apps/featured", appController.GetFeaturedApps)
			// 获取最近更新应用
			public.GET("/apps/recent", appController.GetRecentlyUpdatedApps)
			// 获取免费应用
			public.GET("/apps/free", appController.GetFreeApps)
			// 获取轮播图应用
			public.GET("/apps/carousel", appController.GetCarouselApps)
			// 应用评论相关
			if reviewController != nil {
				public.GET("/apps/:id/reviews", reviewController.GetAppReviews)
			}

			// 获取分类列表（公开接口）
			public.GET("/categories", categoryController.ListPublicCategories)

			// 获取标签列表（公开接口）
			public.GET("/tags", tagController.ListTags)

			// 精选集相关（公开接口）
			public.GET("/featured-collections", featuredCollectionController.GetPublicCollections)
			public.GET("/featured-collections/:id", featuredCollectionController.GetCollectionDetail)
			public.GET("/featured-collections/:id/apps", featuredCollectionController.GetCollectionApps)

			// 帮助中心相关（公开接口）
			public.GET("/help/categories", helpCenterController.GetHelpCategories)
			public.GET("/help/articles", helpCenterController.GetHelpArticles)
			public.GET("/help/articles/:id", helpCenterController.GetHelpArticle)

			// 意见反馈分类（公开接口）
			public.GET("/feedback/categories", feedbackController.GetFeedbackCategories)

			// 轮播图相关
			public.GET("/carousels", carouselController.GetActiveCarousels)
			public.POST("/carousels/click", carouselController.ClickCarousel)
		}

		// 消息队列相关（需要认证）（公开API）
		if messageController != nil {
			messages := api.Group("/messages")
			messages.Use(middleware.HybridAuth(jwtService, logtoService))
			{
				messages.POST("/notification", messageController.SendNotification)
				messages.POST("/email", messageController.SendEmail)
				messages.POST("/activity", messageController.RecordUserActivity)
				messages.POST("/app-review", messageController.TriggerAppReview)
				messages.GET("/queue-status", messageController.GetQueueStatus)
			}
		}

		// 通知相关（需要认证）
		if notificationController != nil {
			notifications := api.Group("/notifications")
			notifications.Use(middleware.HybridAuth(jwtService, logtoService))
			{
				notifications.GET("", notificationController.GetNotifications)
				notifications.PUT("/:id/read", notificationController.MarkAsRead)
				notifications.PUT("/read-all", notificationController.MarkAllAsRead)
				notifications.GET("/unread-count", notificationController.GetUnreadCount)
				notifications.GET("/settings", notificationController.GetNotificationSettings)
				notifications.PUT("/settings", notificationController.UpdateNotificationSettings)
				notifications.DELETE("/:id", notificationController.DeleteNotification)
			}
		}

		// 用户相关
		users := api.Group("/users")
		{
			// 注册和登录
			users.POST("/register", userController.Register)
			users.POST("/login", userController.Login)

			// 需要认证的用户接口
			auth := users.Group("")
			auth.Use(middleware.HybridAuth(jwtService, logtoService))
			{
				auth.GET("/profile", userController.GetProfile)
				auth.PUT("/profile", userController.UpdateProfile)
				auth.POST("/change-password", userController.ChangePassword)
				auth.POST("/logout", userController.Logout)
			}
		}

		// 应用相关
		apps := api.Group("/apps")
		{
			// 需要认证的应用接口
			auth := apps.Group("")
			auth.Use(middleware.HybridAuth(jwtService, logtoService))
			{
				// 应用管理
				auth.POST("", appController.CreateApp)
				auth.PUT("/:id", appController.UpdateApp)
				auth.DELETE("/:id", appController.DeleteApp)
				auth.GET("/:id", appController.GetApp)
				auth.GET("", appController.GetApps)

				// 版本管理
				auth.POST("/:id/versions", appController.CreateVersion)
				auth.PUT("/:id/versions/:version_id", appController.UpdateVersion)
				auth.DELETE("/:id/versions/:version_id", appController.DeleteVersion)
				auth.GET("/:id/versions", appController.GetVersions)

				// 下载统计
				auth.GET("/:id/stats", statsController.GetAppDownloadStats)
				auth.GET("/stats/overview", statsController.GetAppOverallStats)
				auth.GET("/downloads", statsController.GetUserDownloads)
				auth.POST("/:id/versions/:version_id/download", statsController.RecordDownload)

				// 评论相关
				if reviewController != nil {
					auth.POST("/:id/reviews", reviewController.CreateReview)
					auth.POST("/reviews/:id/like", reviewController.LikeReview)
					auth.DELETE("/reviews/:id/like", reviewController.UnlikeReview)
					auth.POST("/:app_id/reviews/:review_id/respond", reviewController.RespondToReview)
				}

				// 标签管理
				auth.POST("/:id/tags", tagController.AddAppTags)
				auth.DELETE("/:id/tags/:tag_id", tagController.RemoveAppTag)
				auth.GET("/:id/tags", tagController.GetAppTags)

				// 浏览历史
				auth.POST("/:id/browse", browseHistoryController.CreateBrowseHistory)
			auth.GET("/browse-history", browseHistoryController.GetMyBrowseHistory)
			auth.DELETE("/browse-history/:id", browseHistoryController.DeleteBrowseHistory)
			auth.DELETE("/browse-history", browseHistoryController.ClearMyBrowseHistory)
			}
		}

		// 搜索相关
		if searchController != nil {
			search := api.Group("/search")
			{
				// 管理员搜索索引管理
				admin := search.Group("/admin")
				admin.Use(middleware.HybridAuth(jwtService, logtoService))
				admin.Use(middleware.RequireRole(string(models.UserRoleAdmin)))
				{
					admin.POST("/initialize", searchController.InitializeSearchIndex)
					admin.POST("/sync", searchController.SyncSearchIndex)
					admin.POST("/initialize-all", searchController.InitializeAllSearchIndexes)
				}

				// 需要认证的搜索接口
				auth := search.Group("")
				auth.Use(middleware.HybridAuth(jwtService, logtoService))
				{
					auth.GET("/users", searchController.SearchUsers)
					auth.GET("/tags/stats", searchController.GetTagStats)
				}
			}
		}

		// 开发者相关
		developer := api.Group("/developer")
		developer.Use(middleware.HybridAuth(jwtService, logtoService))
		{
			developer.POST("/apply", developerController.ApplyDeveloper)
			developer.GET("/status", developerController.GetDeveloperStatus)
			developer.GET("/profile", developerController.GetDeveloperProfile)
			developer.PUT("/profile", developerController.UpdateDeveloperProfile)
			developer.GET("/apps", developerController.GetDeveloperApps)
			developer.GET("/stats", developerController.GetDeveloperStats)
		}

		// 意见反馈相关
		feedback := api.Group("/feedback")
		feedback.Use(middleware.HybridAuth(jwtService, logtoService))
		{
			feedback.POST("", feedbackController.CreateFeedback)
			feedback.GET("", feedbackController.GetMyFeedback)
			feedback.GET("/:id", feedbackController.GetFeedbackDetail)
		}

		// 仪表盘相关
		dashboard := api.Group("/dashboard")
		dashboard.Use(middleware.HybridAuth(jwtService, logtoService))
		{
			// 分析页
			analytics := dashboard.Group("/analytics")
			{
				analytics.GET("/summary", dashboardController.GetAnalyticsSummary)
				analytics.GET("/trend", dashboardController.GetAnalyticsTrend)
				analytics.GET("/categories", dashboardController.GetCategoryStats)
				analytics.GET("/popular-apps", dashboardController.GetPopularApps)
			}

			// 监控页
			monitoring := dashboard.Group("/monitoring")
			{
				monitoring.GET("/data", dashboardController.GetMonitoringData)
				monitoring.GET("/logs", dashboardController.GetSystemLogs)
				monitoring.GET("/alerts", dashboardController.GetAlertEvents)
			}

			// 工作台
			workbench := dashboard.Group("/workbench")
			{
				workbench.GET("/summary", dashboardController.GetWorkbenchSummary)
				workbench.GET("/activities", dashboardController.GetRecentActivities)
				workbench.GET("/tasks", dashboardController.GetTasks)
				workbench.POST("/tasks", dashboardController.CreateTask)
				workbench.PUT("/tasks/:id", dashboardController.UpdateTask)
				workbench.DELETE("/tasks/:id", dashboardController.DeleteTask)
			}
		}

		// 管理员接口
		admin := api.Group("/admin")
		admin.Use(middleware.HybridAuth(jwtService, logtoService))
		{
			// 用户管理
			admin.GET("/users", userController.GetUsers)
			admin.POST("/users", userController.CreateUser)
			admin.PUT("/users/:id", userController.UpdateUser)
			admin.POST("/users/:id/reset-password", userController.ResetPassword)

			// 应用管理
			admin.GET("/apps", appController.GetAllApps)
			admin.PUT("/apps/:id/status", appController.UpdateAppStatus)
			admin.PUT("/apps/:id/featured", appController.UpdateAppFeatured)

			// 分类管理
			admin.POST("/categories", categoryController.CreateCategory)
			admin.PUT("/categories/:id", categoryController.UpdateCategory)
			admin.DELETE("/categories/:id", categoryController.DeleteCategory)
			admin.GET("/categories", categoryController.ListCategories)

			// 标签管理
			admin.POST("/tags", tagController.CreateTag)
			admin.PUT("/tags/:id", tagController.UpdateTag)
			admin.DELETE("/tags/:id", tagController.DeleteTag)
			admin.GET("/tags", tagController.ListTags)
			admin.GET("/tags/:id", tagController.GetTag)
			admin.GET("/tags/:id/apps", tagController.GetAppsByTag)

			// 精选集管理
			admin.POST("/featured-collections", featuredCollectionController.CreateCollection)
			admin.PUT("/featured-collections/:id", featuredCollectionController.UpdateCollection)
			admin.DELETE("/featured-collections/:id", featuredCollectionController.DeleteCollection)
			admin.GET("/featured-collections", featuredCollectionController.GetCollections)
			admin.POST("/featured-collections/:id/apps", featuredCollectionController.AddAppToCollection)
			admin.DELETE("/featured-collections/:id/apps/:app_id", featuredCollectionController.RemoveAppFromCollection)

			// 开发者管理
			admin.GET("/developers", developerController.GetDevelopers)
			admin.PUT("/developers/:id/status", developerController.UpdateDeveloperStatus)

			// 意见反馈管理
			admin.GET("/feedback", feedbackController.GetAllFeedback)
			admin.PUT("/feedback/:id/status", feedbackController.UpdateFeedbackStatus)
			admin.POST("/feedback/:id/reply", feedbackController.ReplyFeedback)
			admin.POST("/feedback/categories", feedbackController.CreateFeedbackCategory)
			admin.PUT("/feedback/categories/:id", feedbackController.UpdateFeedbackCategory)
			admin.DELETE("/feedback/categories/:id", feedbackController.DeleteFeedbackCategory)

			// 帮助中心管理
			admin.POST("/help/categories", helpCenterController.CreateHelpCategory)
			admin.PUT("/help/categories/:id", helpCenterController.UpdateHelpCategory)
			admin.DELETE("/help/categories/:id", helpCenterController.DeleteHelpCategory)
			admin.POST("/help/articles", helpCenterController.CreateHelpArticle)
			admin.PUT("/help/articles/:id", helpCenterController.UpdateHelpArticle)
			admin.DELETE("/help/articles/:id", helpCenterController.DeleteHelpArticle)

			// 轮播图管理
			admin.POST("/carousels", carouselController.CreateCarousel)
			admin.PUT("/carousels/:id", carouselController.UpdateCarousel)
			admin.DELETE("/carousels/:id", carouselController.DeleteCarousel)
			admin.GET("/carousels", carouselController.GetCarousels)
			admin.PUT("/carousels/:id/status", carouselController.UpdateCarouselStatus)
		}
	}
}

// HealthCheck 健康检查
//
//	@Summary		健康检查接口
//	@Description	检查API服务是否正常运行
//	@Tags			系统
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	map[string]interface{}	"返回服务状态和时间"
//	@Router			/health [get]
func HealthCheck(c *gin.Context) {
	response.Success(c, gin.H{
		"status": "ok",
		"time":   time.Now().Format(time.RFC3339),
	})
}
