/*
 * @Author: xiaoxiaozhou <EMAIL>
 * @Date: 2025-06-07 11:53:23
 * @LastEditors: xiaoxiaozhou <EMAIL>
 * @LastEditTime: 2025-06-16 12:08:16
 * @FilePath: \NexusHub-OH\NexusHub-OH-Front\src\pages\list\table-list\components\CreateForm.tsx
 * @Description: 
 * 技术生的工作日常
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
import { Modal } from 'antd';
import React from 'react';

type CreateFormProps = {
  modalVisible: boolean;
  children?: React.ReactNode;
  onCancel: () => void;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const { modalVisible, onCancel } = props;

  return (
    <Modal
      destroyOnHidden
      title="新建规则"
      open={modalVisible}
      onCancel={() => onCancel()}
      footer={null}
    >
      {props.children}
    </Modal>
  );
};

export default CreateForm;
