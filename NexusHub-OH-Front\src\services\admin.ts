// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/**
 * 获取用户列表（管理员权限）
 * @param params - 查询参数
 */
export async function getUserList(params?: {
  page?: number;
  pageSize?: number;
  keyword?: string;
  role?: 'user' | 'developer' | 'admin';
  status?: 'active' | 'suspended' | 'banned';
  developerStatus?: 'pending' | 'approved' | 'rejected';
}) {
  return request('admin/users', {
    method: 'GET',
    params,
  });
}

/**
 * 更新用户状态（管理员权限）
 * @param id - 用户ID
 * @param status - 新状态
 */
export async function updateUserStatus(id: string, status: 'active' | 'suspended' | 'banned') {
  return request(`/admin/users/${id}/status`, {
    method: 'PUT',
    data: { status },
  });
}

/**
 * 获取待审核应用列表（管理员权限）
 * @param params - 查询参数
 */
export async function getPendingApps(params?: {
  page?: number;
  pageSize?: number;
}) {
  return request('/admin/app/pending', {
    method: 'GET',
    params,
  });
}

/**
 * 审核应用（管理员权限）
 * @param id - 应用ID
 * @param data - 审核结果
 */
export async function auditApp(id: string, data: {
  status: 'approved' | 'rejected';
  reason?: string;
}) {
  return request(`/admin/app/audit/${id}`, {
    method: 'PUT',
    data,
  });
}

/**
 * 获取待审核评论列表（管理员权限）
 * @param params - 查询参数
 */
export async function getPendingReviews(params?: {
  page?: number;
  pageSize?: number;
}) {
  return request('/admin/review/pending', {
    method: 'GET',
    params,
  });
}

/**
 * 审核评论（管理员权限）
 * @param id - 评论ID
 * @param data - 审核结果
 */
export async function auditReview(id: string, data: {
  status: 'approved' | 'rejected';
  reason?: string;
}) {
  return request(`/admin/review/audit/${id}`, {
    method: 'PUT',
    data,
  });
}

/**
 * 获取开发者申请列表（管理员权限）
 * @param params - 查询参数
 */
export async function getDeveloperApplications(params?: {
  page?: number;
  pageSize?: number;
  status?: 'pending' | 'approved' | 'rejected';
}) {
  return request('/admin/developer/applications', {
    method: 'GET',
    params,
  });
}

/**
 * 审核开发者申请（管理员权限）
 * @param id - 申请ID
 * @param data - 审核结果
 */
export async function auditDeveloperApplication(id: string, data: {
  status: 'approved' | 'rejected';
  reason?: string;
}) {
  return request(`/admin/developer/audit/${id}`, {
    method: 'PUT',
    data,
  });
}