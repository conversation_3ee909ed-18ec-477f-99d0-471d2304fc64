if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface AboutPage_Params {
    selectedTab?: number;
    appInfo?: AppInfo;
    teamMembers?: TeamMember[];
    updateLogs?: UpdateLog[];
    loadingState?: LoadingState;
    deviceUtils?;
}
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { DeviceUtils } from "@normalized:N&&&entry/src/main/ets/utils/DeviceUtils&";
import { LoadingView, LoadingState } from "@normalized:N&&&entry/src/main/ets/components/LoadingView&";
import hilog from "@ohos:hilog";
import { LengthMetrics } from "@ohos:arkui.node";
/**
 * 团队成员模型
 */
interface TeamMember {
    id: number;
    name: string;
    role: string;
    avatar: string;
    description: string;
}
/**
 * 更新日志模型
 */
interface UpdateLog {
    version: string;
    date: string;
    features: string[];
    fixes: string[];
}
/**
 * 应用信息接口
 */
interface AppInfo {
    name: string;
    version: string;
    buildNumber: string;
    description: string;
    developer: string;
    website: string;
    email: string;
    privacy: string;
    terms: string;
    license: string;
    copyright: string;
    features: string[];
    technologies: string[];
}
class AboutPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__selectedTab = new ObservedPropertySimplePU(0, this, "selectedTab");
        this.__appInfo = new ObservedPropertyObjectPU({} as AppInfo, this, "appInfo");
        this.__teamMembers = new ObservedPropertyObjectPU([], this, "teamMembers");
        this.__updateLogs = new ObservedPropertyObjectPU([], this, "updateLogs");
        this.__loadingState = new ObservedPropertySimplePU(LoadingState.SUCCESS, this, "loadingState");
        this.deviceUtils = DeviceUtils.getInstance();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: AboutPage_Params) {
        if (params.selectedTab !== undefined) {
            this.selectedTab = params.selectedTab;
        }
        if (params.appInfo !== undefined) {
            this.appInfo = params.appInfo;
        }
        if (params.teamMembers !== undefined) {
            this.teamMembers = params.teamMembers;
        }
        if (params.updateLogs !== undefined) {
            this.updateLogs = params.updateLogs;
        }
        if (params.loadingState !== undefined) {
            this.loadingState = params.loadingState;
        }
        if (params.deviceUtils !== undefined) {
            this.deviceUtils = params.deviceUtils;
        }
    }
    updateStateVars(params: AboutPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__selectedTab.purgeDependencyOnElmtId(rmElmtId);
        this.__appInfo.purgeDependencyOnElmtId(rmElmtId);
        this.__teamMembers.purgeDependencyOnElmtId(rmElmtId);
        this.__updateLogs.purgeDependencyOnElmtId(rmElmtId);
        this.__loadingState.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__selectedTab.aboutToBeDeleted();
        this.__appInfo.aboutToBeDeleted();
        this.__teamMembers.aboutToBeDeleted();
        this.__updateLogs.aboutToBeDeleted();
        this.__loadingState.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __selectedTab: ObservedPropertySimplePU<number>; // 0: 应用信息, 1: 团队介绍, 2: 更新日志
    get selectedTab() {
        return this.__selectedTab.get();
    }
    set selectedTab(newValue: number) {
        this.__selectedTab.set(newValue);
    }
    private __appInfo: ObservedPropertyObjectPU<AppInfo>;
    get appInfo() {
        return this.__appInfo.get();
    }
    set appInfo(newValue: AppInfo) {
        this.__appInfo.set(newValue);
    }
    private __teamMembers: ObservedPropertyObjectPU<TeamMember[]>;
    get teamMembers() {
        return this.__teamMembers.get();
    }
    set teamMembers(newValue: TeamMember[]) {
        this.__teamMembers.set(newValue);
    }
    private __updateLogs: ObservedPropertyObjectPU<UpdateLog[]>;
    get updateLogs() {
        return this.__updateLogs.get();
    }
    set updateLogs(newValue: UpdateLog[]) {
        this.__updateLogs.set(newValue);
    }
    private __loadingState: ObservedPropertySimplePU<LoadingState>;
    get loadingState() {
        return this.__loadingState.get();
    }
    set loadingState(newValue: LoadingState) {
        this.__loadingState.set(newValue);
    }
    private deviceUtils;
    aboutToAppear() {
        this.loadAppInfo();
        this.loadTeamMembers();
        this.loadUpdateLogs();
    }
    /**
     * 加载应用信息
     */
    private loadAppInfo() {
        this.appInfo = {
            name: 'NexusHub',
            version: '1.0.0',
            buildNumber: '100',
            description: 'NexusHub是一个专为HarmonyOS设计的应用商店客户端，提供丰富的应用资源和优质的用户体验。',
            developer: 'NexusHub团队',
            website: 'https://nexushub.com',
            email: '<EMAIL>',
            privacy: 'https://nexushub.com/privacy',
            terms: 'https://nexushub.com/terms',
            license: 'MIT License',
            copyright: '© 2024 NexusHub团队. All rights reserved.',
            features: [
                '🔍 智能搜索和推荐',
                '📱 精美的应用界面',
                '⚡ 快速下载和安装',
                '❤️ 收藏和评价功能',
                '🔒 安全可靠的应用审核',
                '🌟 个性化推荐算法'
            ],
            technologies: [
                'HarmonyOS SDK',
                'ArkTS',
                'ArkUI',
                'TypeScript',
            ]
        };
    }
    /**
     * 加载团队成员
     */
    private loadTeamMembers() {
        this.teamMembers = [
            {
                id: 1,
                name: '张三',
                role: '产品经理',
                avatar: '👨‍💼',
                description: '负责产品规划和用户体验设计，拥有5年移动应用产品经验。'
            },
            {
                id: 2,
                name: '李四',
                role: '技术负责人',
                avatar: '👨‍💻',
                description: '资深HarmonyOS开发工程师，专注于应用架构和性能优化。'
            },
            {
                id: 3,
                name: '王五',
                avatar: '👩‍💻',
                role: '前端开发',
                description: '专业的UI/UX开发工程师，致力于打造优秀的用户界面。'
            },
            {
                id: 4,
                name: '赵六',
                role: '后端开发',
                avatar: '👨‍🔧',
                description: '后端架构师，负责服务器端开发和数据库设计。'
            },
            {
                id: 5,
                name: '钱七',
                role: 'UI设计师',
                avatar: '👩‍🎨',
                description: '创意设计师，为应用提供美观且实用的视觉设计。'
            },
            {
                id: 6,
                name: '孙八',
                role: '测试工程师',
                avatar: '👨‍🔬',
                description: '质量保证专家，确保应用的稳定性和可靠性。'
            }
        ];
    }
    /**
     * 加载更新日志
     */
    private loadUpdateLogs() {
        this.updateLogs = [
            {
                version: '1.0.0',
                date: '2024-01-15',
                features: [
                    '🎉 NexusHub正式发布',
                    '📱 支持应用搜索和浏览',
                    '⬇️ 应用下载和安装功能',
                    '❤️ 收藏和评价系统',
                    '👤 用户账号管理',
                    '🔔 消息通知功能'
                ],
                fixes: []
            },
            {
                version: '0.9.5',
                date: '2024-01-10',
                features: [
                    '🔍 优化搜索算法',
                    '🎨 更新UI设计',
                    '⚡ 提升应用启动速度'
                ],
                fixes: [
                    '修复下载进度显示问题',
                    '解决部分设备兼容性问题',
                    '优化内存使用'
                ]
            },
            {
                version: '0.9.0',
                date: '2024-01-05',
                features: [
                    '📊 添加应用统计功能',
                    '🌙 支持深色模式',
                    '🔄 自动更新检查'
                ],
                fixes: [
                    '修复登录状态异常',
                    '解决图片加载失败问题',
                    '优化网络请求性能'
                ]
            }
        ];
    }
    /**
     * 打开外部链接
     */
    private openExternalLink(url: string) {
        // 这里应该调用系统浏览器打开链接
        hilog.info(0x0000, 'AboutPage', '打开链接: %{public}s', url);
    }
    /**
     * 标签栏
     */
    private TabBar(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.width('100%');
            Row.backgroundColor(Constants.COLORS.WHITE);
            Row.justifyContent(FlexAlign.SpaceAround);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const title = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(title);
                    Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                    Text.fontColor(this.selectedTab === index ? Constants.COLORS.PRIMARY : Constants.COLORS.TEXT_SECONDARY);
                    Text.fontWeight(this.selectedTab === index ? FontWeight.Bold : FontWeight.Normal);
                    Text.padding({ left: 16, right: 16, top: 12, bottom: 12 });
                    Text.onClick(() => {
                        this.selectedTab = index;
                    });
                }, Text);
                Text.pop();
            };
            this.forEachUpdateFunction(elmtId, ['应用信息', '团队介绍', '更新日志'], forEachItemGenFunction, (title: string) => title, true, false);
        }, ForEach);
        ForEach.pop();
        Row.pop();
    }
    /**
     * 应用信息内容
     */
    private AppInfoContent(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.scrollable(ScrollDirection.Vertical);
            Scroll.scrollBar(BarState.Auto);
            Scroll.layoutWeight(1);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 16 });
            Column.padding(16);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 应用图标和基本信息
            Column.create({ space: 12 });
            // 应用图标和基本信息
            Column.width('100%');
            // 应用图标和基本信息
            Column.padding(16);
            // 应用图标和基本信息
            Column.backgroundColor(Constants.COLORS.WHITE);
            // 应用图标和基本信息
            Column.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            // 应用图标和基本信息
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📱');
            Text.fontSize(64);
            Text.fontColor(Constants.COLORS.PRIMARY);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.appInfo.name);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.EXTRA_LARGE));
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.fontWeight(FontWeight.Bold);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`版本 ${this.appInfo.version} (${this.appInfo.buildNumber})`);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.appInfo.description);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
            Text.textAlign(TextAlign.Center);
            Text.lineHeight(20);
            Text.margin({ left: 16, right: 16 });
        }, Text);
        Text.pop();
        // 应用图标和基本信息
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 核心功能
            Column.create({ space: 12 });
            // 核心功能
            Column.width('100%');
            // 核心功能
            Column.padding(16);
            // 核心功能
            Column.backgroundColor(Constants.COLORS.WHITE);
            // 核心功能
            Column.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            // 核心功能
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('✨ 核心功能');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.fontWeight(FontWeight.Bold);
            Text.width('100%');
            Text.textAlign(TextAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 8 });
            Column.width('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const feature = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(feature);
                    Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                    Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                    Text.width('100%');
                    Text.textAlign(TextAlign.Start);
                }, Text);
                Text.pop();
            };
            this.forEachUpdateFunction(elmtId, this.appInfo.features, forEachItemGenFunction, (feature: string) => feature, false, false);
        }, ForEach);
        ForEach.pop();
        Column.pop();
        // 核心功能
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 技术栈
            Column.create({ space: 12 });
            // 技术栈
            Column.width('100%');
            // 技术栈
            Column.padding(16);
            // 技术栈
            Column.backgroundColor(Constants.COLORS.WHITE);
            // 技术栈
            Column.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            // 技术栈
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🛠️ 技术栈');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.fontWeight(FontWeight.Bold);
            Text.width('100%');
            Text.textAlign(TextAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Flex.create({ wrap: FlexWrap.Wrap, space: { main: LengthMetrics.vp(8), cross: LengthMetrics.vp(8) } });
            Flex.width('100%');
        }, Flex);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const tech = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(tech);
                    Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                    Text.fontColor(Constants.COLORS.PRIMARY);
                    Text.backgroundColor({ "id": 16777232, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                    Text.padding({ left: '12vp', right: '12vp', top: '6vp', bottom: '6vp' });
                    Text.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
                }, Text);
                Text.pop();
            };
            this.forEachUpdateFunction(elmtId, this.appInfo.technologies, forEachItemGenFunction, (tech: string) => tech, false, false);
        }, ForEach);
        ForEach.pop();
        Flex.pop();
        // 技术栈
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 联系信息
            Column.create({ space: 12 });
            // 联系信息
            Column.width('100%');
            // 联系信息
            Column.padding(16);
            // 联系信息
            Column.backgroundColor(Constants.COLORS.WHITE);
            // 联系信息
            Column.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            // 联系信息
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📞 联系我们');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.fontWeight(FontWeight.Bold);
            Text.width('100%');
            Text.textAlign(TextAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 8 });
            Column.width('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 12 });
            Row.width('100%');
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🌐');
            Text.fontSize(16);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('官方网站');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.appInfo.website);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.PRIMARY);
            Text.onClick(() => {
                this.openExternalLink(this.appInfo.website);
            });
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 12 });
            Row.width('100%');
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📧');
            Text.fontSize(16);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('客服邮箱');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.appInfo.email);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.PRIMARY);
            Text.onClick(() => {
                this.openExternalLink(`mailto:${this.appInfo.email}`);
            });
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 12 });
            Row.width('100%');
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🔒');
            Text.fontSize(16);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('隐私政策');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('查看详情');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.PRIMARY);
            Text.onClick(() => {
                this.openExternalLink(this.appInfo.privacy);
            });
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 12 });
            Row.width('100%');
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📋');
            Text.fontSize(16);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('服务条款');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('查看详情');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.PRIMARY);
            Text.onClick(() => {
                this.openExternalLink(this.appInfo.terms);
            });
        }, Text);
        Text.pop();
        Row.pop();
        Column.pop();
        // 联系信息
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 版权信息
            Text.create(this.appInfo.copyright);
            // 版权信息
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            // 版权信息
            Text.fontColor(Constants.COLORS.TEXT_HINT);
            // 版权信息
            Text.textAlign(TextAlign.Center);
            // 版权信息
            Text.width('100%');
            // 版权信息
            Text.margin({ top: 8, bottom: 16 });
        }, Text);
        // 版权信息
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 底部间距
            Column.create();
            // 底部间距
            Column.height(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE));
        }, Column);
        // 底部间距
        Column.pop();
        Column.pop();
        Scroll.pop();
    }
    /**
     * 团队成员卡片
     */
    private TeamMemberCard(member: TeamMember, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 12 });
            Column.width('100%');
            Column.padding(16);
            Column.backgroundColor(Constants.COLORS.WHITE);
            Column.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(member.avatar);
            Text.fontSize(48);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(member.name);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.fontWeight(FontWeight.Bold);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(member.role);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.PRIMARY);
            Text.backgroundColor('rgba(33, 150, 243, 0.1)');
            Text.padding({ left: 12, right: 12, top: 4, bottom: 4 });
            Text.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(member.description);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
            Text.textAlign(TextAlign.Center);
            Text.lineHeight(18);
        }, Text);
        Text.pop();
        Column.pop();
    }
    /**
     * 团队介绍内容
     */
    private TeamContent(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.scrollable(ScrollDirection.Vertical);
            Scroll.scrollBar(BarState.Auto);
            Scroll.layoutWeight(1);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 16 });
            Column.padding(16);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 团队介绍
            Column.create({ space: 12 });
            // 团队介绍
            Column.width('100%');
            // 团队介绍
            Column.padding(16);
            // 团队介绍
            Column.backgroundColor(Constants.COLORS.WHITE);
            // 团队介绍
            Column.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            // 团队介绍
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('👥 我们的团队');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.EXTRA_LARGE));
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.fontWeight(FontWeight.Bold);
            Text.width('100%');
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('NexusHub团队由一群热爱技术、追求卓越的开发者组成。我们致力于为HarmonyOS生态提供优质的应用商店体验，让每一位用户都能轻松发现和使用优秀的应用。');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
            Text.textAlign(TextAlign.Center);
            Text.lineHeight(20);
            Text.margin({ left: 16, right: 16 });
        }, Text);
        Text.pop();
        // 团队介绍
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 团队成员
            Grid.create();
            // 团队成员
            Grid.columnsTemplate('1fr 1fr');
            // 团队成员
            Grid.rowsGap(16);
            // 团队成员
            Grid.columnsGap(16);
            // 团队成员
            Grid.width('100%');
        }, Grid);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const member = _item;
                {
                    const itemCreation2 = (elmtId, isInitialRender) => {
                        GridItem.create(() => { }, false);
                    };
                    const observedDeepRender = () => {
                        this.observeComponentCreation2(itemCreation2, GridItem);
                        this.TeamMemberCard.bind(this)(member);
                        GridItem.pop();
                    };
                    observedDeepRender();
                }
            };
            this.forEachUpdateFunction(elmtId, this.teamMembers, forEachItemGenFunction);
        }, ForEach);
        ForEach.pop();
        // 团队成员
        Grid.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 底部间距
            Column.create();
            // 底部间距
            Column.height(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE));
        }, Column);
        // 底部间距
        Column.pop();
        Column.pop();
        Scroll.pop();
    }
    /**
     * 更新日志项
     */
    private UpdateLogItem(log: UpdateLog, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 12 });
            Column.width('100%');
            Column.padding(16);
            Column.backgroundColor(Constants.COLORS.WHITE);
            Column.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 12 });
            Row.width('100%');
            Row.alignItems(VerticalAlign.Top);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🚀');
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 4 });
            Column.alignItems(HorizontalAlign.Start);
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`版本 ${log.version}`);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.fontWeight(FontWeight.Bold);
            Text.width('100%');
            Text.textAlign(TextAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(log.date);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_HINT);
            Text.width('100%');
            Text.textAlign(TextAlign.Start);
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (log.features.length > 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create({ space: 8 });
                        Column.width('100%');
                        Column.alignItems(HorizontalAlign.Start);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('✨ 新功能');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Text.fontColor(Constants.COLORS.PRIMARY);
                        Text.fontWeight(FontWeight.Medium);
                        Text.width('100%');
                        Text.textAlign(TextAlign.Start);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create({ space: 4 });
                        Column.width('100%');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const feature = _item;
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create(`• ${feature}`);
                                Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                                Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                                Text.width('100%');
                                Text.textAlign(TextAlign.Start);
                            }, Text);
                            Text.pop();
                        };
                        this.forEachUpdateFunction(elmtId, log.features, forEachItemGenFunction, (feature: string) => feature, false, false);
                    }, ForEach);
                    ForEach.pop();
                    Column.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (log.fixes.length > 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create({ space: 8 });
                        Column.width('100%');
                        Column.alignItems(HorizontalAlign.Start);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('🔧 问题修复');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Text.fontColor(Constants.COLORS.SUCCESS);
                        Text.fontWeight(FontWeight.Medium);
                        Text.width('100%');
                        Text.textAlign(TextAlign.Start);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create({ space: 4 });
                        Column.width('100%');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const fix = _item;
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create(`• ${fix}`);
                                Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                                Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                                Text.width('100%');
                                Text.textAlign(TextAlign.Start);
                            }, Text);
                            Text.pop();
                        };
                        this.forEachUpdateFunction(elmtId, log.fixes, forEachItemGenFunction, (fix: string) => fix, false, false);
                    }, ForEach);
                    ForEach.pop();
                    Column.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    /**
     * 更新日志内容
     */
    private UpdateLogContent(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.scrollable(ScrollDirection.Vertical);
            Scroll.scrollBar(BarState.Auto);
            Scroll.layoutWeight(1);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 16 });
            Column.padding(16);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const log = _item;
                this.UpdateLogItem.bind(this)(log);
            };
            this.forEachUpdateFunction(elmtId, this.updateLogs, forEachItemGenFunction, (log: UpdateLog) => log.version, false, false);
        }, ForEach);
        ForEach.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 底部间距
            Column.create();
            // 底部间距
            Column.height(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE));
        }, Column);
        // 底部间距
        Column.pop();
        Column.pop();
        Scroll.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor(Constants.COLORS.BACKGROUND);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 标题栏
            Row.create();
            // 标题栏
            Row.width('100%');
            // 标题栏
            Row.height(56);
            // 标题栏
            Row.padding({ left: 16, right: 16 });
            // 标题栏
            Row.backgroundColor(Constants.COLORS.WHITE);
            // 标题栏
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('←');
            Text.fontSize(24);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.onClick(() => {
                this.getUIContext().getRouter().back();
            });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('关于我们');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.fontWeight(FontWeight.Bold);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.width(24);
        }, Text);
        Text.pop();
        // 标题栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.loadingState === LoadingState.LOADING) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, { state: LoadingState.LOADING }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/AboutPage.ets", line: 645, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.LOADING
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.LOADING
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.layoutWeight(1);
                    }, Column);
                    // 标签栏
                    this.TabBar.bind(this)();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Divider.create();
                        Divider.color(Constants.COLORS.BORDER);
                    }, Divider);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        // 内容区域
                        if (this.selectedTab === 0) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.AppInfoContent.bind(this)();
                            });
                        }
                        else if (this.selectedTab === 1) {
                            this.ifElseBranchUpdateFunction(1, () => {
                                this.TeamContent.bind(this)();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(2, () => {
                                this.UpdateLogContent.bind(this)();
                            });
                        }
                    }, If);
                    If.pop();
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "AboutPage";
    }
}
export { AboutPage };
registerNamedRoute(() => new AboutPage(undefined, {}), "", { bundleName: "com.example.nexushub", moduleName: "entry", pagePath: "pages/AboutPage", pageFullPath: "entry/src/main/ets/pages/AboutPage", integratedHsp: "false", moduleType: "followWithHap" });
