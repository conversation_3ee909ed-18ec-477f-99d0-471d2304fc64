if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface LocationPickerPage_Params {
    locations?: LocationModel[];
    loadingState?: LoadingState;
    selectedLocation?: LocationModel | null;
    searchKeyword?: string;
    filteredLocations?: LocationModel[];
    showSearch?: boolean;
    apiService?;
    pageParams?: LocationPickerParams;
}
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { ApiService } from "@normalized:N&&&entry/src/main/ets/services/ApiService&";
import { LoadingView, LoadingState } from "@normalized:N&&&entry/src/main/ets/components/LoadingView&";
import hilog from "@ohos:hilog";
// 定义响应数据接口
interface ResponseData {
    code?: number;
    message?: string;
    data?: Object;
}
/**
 * 地理位置数据模型
 */
interface LocationModel {
    id: number;
    name: string;
    code?: string;
    parent_id?: number;
}
/**
 * 地理位置列表响应数据模型
 */
interface LocationListData {
    locations?: LocationModel[];
}
/**
 * 页面参数
 */
interface LocationPickerParams {
    type: 'country' | 'province' | 'city' | 'district' | 'street';
    parentId?: number;
    selectedLocation?: LocationModel;
    title?: string;
}
class LocationPickerPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__locations = new ObservedPropertyObjectPU([], this, "locations");
        this.__loadingState = new ObservedPropertySimplePU(LoadingState.LOADING, this, "loadingState");
        this.__selectedLocation = new ObservedPropertyObjectPU(null, this, "selectedLocation");
        this.__searchKeyword = new ObservedPropertySimplePU('', this, "searchKeyword");
        this.__filteredLocations = new ObservedPropertyObjectPU([], this, "filteredLocations");
        this.__showSearch = new ObservedPropertySimplePU(false, this, "showSearch");
        this.apiService = ApiService.getInstance();
        this.pageParams = {
            type: 'country'
        };
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: LocationPickerPage_Params) {
        if (params.locations !== undefined) {
            this.locations = params.locations;
        }
        if (params.loadingState !== undefined) {
            this.loadingState = params.loadingState;
        }
        if (params.selectedLocation !== undefined) {
            this.selectedLocation = params.selectedLocation;
        }
        if (params.searchKeyword !== undefined) {
            this.searchKeyword = params.searchKeyword;
        }
        if (params.filteredLocations !== undefined) {
            this.filteredLocations = params.filteredLocations;
        }
        if (params.showSearch !== undefined) {
            this.showSearch = params.showSearch;
        }
        if (params.apiService !== undefined) {
            this.apiService = params.apiService;
        }
        if (params.pageParams !== undefined) {
            this.pageParams = params.pageParams;
        }
    }
    updateStateVars(params: LocationPickerPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__locations.purgeDependencyOnElmtId(rmElmtId);
        this.__loadingState.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedLocation.purgeDependencyOnElmtId(rmElmtId);
        this.__searchKeyword.purgeDependencyOnElmtId(rmElmtId);
        this.__filteredLocations.purgeDependencyOnElmtId(rmElmtId);
        this.__showSearch.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__locations.aboutToBeDeleted();
        this.__loadingState.aboutToBeDeleted();
        this.__selectedLocation.aboutToBeDeleted();
        this.__searchKeyword.aboutToBeDeleted();
        this.__filteredLocations.aboutToBeDeleted();
        this.__showSearch.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __locations: ObservedPropertyObjectPU<LocationModel[]>;
    get locations() {
        return this.__locations.get();
    }
    set locations(newValue: LocationModel[]) {
        this.__locations.set(newValue);
    }
    private __loadingState: ObservedPropertySimplePU<LoadingState>;
    get loadingState() {
        return this.__loadingState.get();
    }
    set loadingState(newValue: LoadingState) {
        this.__loadingState.set(newValue);
    }
    private __selectedLocation: ObservedPropertyObjectPU<LocationModel | null>;
    get selectedLocation() {
        return this.__selectedLocation.get();
    }
    set selectedLocation(newValue: LocationModel | null) {
        this.__selectedLocation.set(newValue);
    }
    private __searchKeyword: ObservedPropertySimplePU<string>;
    get searchKeyword() {
        return this.__searchKeyword.get();
    }
    set searchKeyword(newValue: string) {
        this.__searchKeyword.set(newValue);
    }
    private __filteredLocations: ObservedPropertyObjectPU<LocationModel[]>;
    get filteredLocations() {
        return this.__filteredLocations.get();
    }
    set filteredLocations(newValue: LocationModel[]) {
        this.__filteredLocations.set(newValue);
    }
    private __showSearch: ObservedPropertySimplePU<boolean>;
    get showSearch() {
        return this.__showSearch.get();
    }
    set showSearch(newValue: boolean) {
        this.__showSearch.set(newValue);
    }
    private apiService;
    private pageParams: LocationPickerParams;
    aboutToAppear() {
        // 获取页面参数
        const params = this.getUIContext().getRouter().getParams() as LocationPickerParams;
        if (params) {
            this.pageParams = params;
            this.selectedLocation = params.selectedLocation || null;
        }
        this.loadLocations();
    }
    /**
     * 加载地理位置数据
     */
    private async loadLocations(): Promise<void> {
        try {
            this.loadingState = LoadingState.LOADING;
            let response: ResponseData;
            switch (this.pageParams.type) {
                case 'country':
                    response = await this.apiService.getCountries();
                    break;
                case 'province':
                    response = await this.apiService.getProvinces(this.pageParams.parentId!.toString());
                    break;
                case 'city':
                    response = await this.apiService.getCities(this.pageParams.parentId!.toString());
                    break;
                case 'district':
                    response = await this.apiService.getDistricts(this.pageParams.parentId!.toString());
                    break;
                case 'street':
                    response = await this.apiService.getStreets(this.pageParams.parentId!.toString());
                    break;
                default:
                    response = await this.apiService.getCountries();
            }
            if (response.code === 200 && response.data) {
                this.locations = (response.data as LocationListData).locations || (response.data as LocationModel[]);
                this.filteredLocations = [...this.locations];
                this.loadingState = LoadingState.SUCCESS;
            }
            else {
                this.loadingState = LoadingState.ERROR;
            }
        }
        catch (error) {
            hilog.error(0x0000, 'LocationPickerPage', '加载地理位置数据失败: %{public}s', JSON.stringify(error));
            this.loadingState = LoadingState.ERROR;
        }
    }
    /**
     * 搜索地理位置
     */
    private searchLocations() {
        if (!this.searchKeyword.trim()) {
            this.filteredLocations = [...this.locations];
            return;
        }
        const keyword = this.searchKeyword.toLowerCase();
        this.filteredLocations = this.locations.filter(location => location.name.toLowerCase().includes(keyword) ||
            (location.code && location.code.toLowerCase().includes(keyword)));
    }
    /**
     * 选择地理位置
     */
    private selectLocation(location: LocationModel) {
        this.selectedLocation = location;
        // 返回选择结果
        this.getUIContext().getRouter().back({
            url: '',
            params: {
                selectedLocation: location,
                type: this.pageParams.type
            }
        });
    }
    /**
     * 获取页面标题
     */
    private getPageTitle(): string {
        if (this.pageParams.title) {
            return this.pageParams.title;
        }
        switch (this.pageParams.type) {
            case 'country':
                return '选择国家';
            case 'province':
                return '选择省份';
            case 'city':
                return '选择城市';
            case 'district':
                return '选择区县';
            case 'street':
                return '选择街道';
            default:
                return '选择位置';
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor(Constants.COLORS.BACKGROUND);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 标题栏
            Row.create();
            // 标题栏
            Row.width('100%');
            // 标题栏
            Row.height(56);
            // 标题栏
            Row.padding({ left: 16, right: 16 });
            // 标题栏
            Row.backgroundColor(Constants.COLORS.WHITE);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.width(40);
            Button.height(40);
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                this.getUIContext().getRouter().back();
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777246, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(24);
            Image.height(24);
            Image.fillColor(Constants.COLORS.TEXT_PRIMARY);
        }, Image);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getPageTitle());
            Text.fontSize(Constants.FONT_SIZE.LARGE);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.width(40);
            Button.height(40);
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                this.showSearch = !this.showSearch;
                if (!this.showSearch) {
                    this.searchKeyword = '';
                    this.searchLocations();
                }
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777270, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(24);
            Image.height(24);
            Image.fillColor(this.showSearch ? Constants.COLORS.PRIMARY : Constants.COLORS.TEXT_PRIMARY);
        }, Image);
        Button.pop();
        // 标题栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 搜索框
            if (this.showSearch) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.width('100%');
                        Row.padding(16);
                        Row.backgroundColor(Constants.COLORS.WHITE);
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        TextInput.create({ placeholder: '搜索位置', text: this.searchKeyword });
                        TextInput.layoutWeight(1);
                        TextInput.fontSize(Constants.FONT_SIZE.NORMAL);
                        TextInput.backgroundColor(Constants.COLORS.BACKGROUND_LIGHT);
                        TextInput.borderRadius(8);
                        TextInput.padding({ left: 12, right: 12 });
                        TextInput.onChange((value: string) => {
                            this.searchKeyword = value;
                            this.searchLocations();
                        });
                    }, TextInput);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.searchKeyword) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Button.createWithChild();
                                    Button.width(32);
                                    Button.height(32);
                                    Button.backgroundColor(Color.Transparent);
                                    Button.margin({ left: 8 });
                                    Button.onClick(() => {
                                        this.searchKeyword = '';
                                        this.searchLocations();
                                    });
                                }, Button);
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Image.create({ "id": 16777249, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                                    Image.width(20);
                                    Image.height(20);
                                    Image.fillColor(Constants.COLORS.TEXT_HINT);
                                }, Image);
                                Button.pop();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    Row.pop();
                });
            }
            // 内容区域
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 内容区域
            if (this.loadingState === LoadingState.LOADING) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/LocationPickerPage.ets", line: 251, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {};
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {});
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else if (this.loadingState === LoadingState.ERROR) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.layoutWeight(1);
                        Column.justifyContent(FlexAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Image.create({ "id": 16777254, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                        Image.width(64);
                        Image.height(64);
                        Image.fillColor(Constants.COLORS.TEXT_HINT);
                        Image.margin({ bottom: 16 });
                    }, Image);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('加载失败');
                        Text.fontSize(Constants.FONT_SIZE.MEDIUM);
                        Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                        Text.margin({ bottom: 16 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('重试');
                        Button.fontSize(Constants.FONT_SIZE.NORMAL);
                        Button.fontColor(Constants.COLORS.WHITE);
                        Button.backgroundColor(Constants.COLORS.PRIMARY);
                        Button.borderRadius(8);
                        Button.padding({ left: 24, right: 24, top: 8, bottom: 8 });
                        Button.onClick(() => {
                            this.loadLocations();
                        });
                    }, Button);
                    Button.pop();
                    Column.pop();
                });
            }
            else if (!this.filteredLocations || this.filteredLocations.length === 0) {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.layoutWeight(1);
                        Column.justifyContent(FlexAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Image.create({ "id": 16777253, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                        Image.width(120);
                        Image.height(120);
                        Image.fillColor(Constants.COLORS.TEXT_HINT);
                        Image.margin({ bottom: 16 });
                    }, Image);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.searchKeyword ? '未找到相关位置' : '暂无数据');
                        Text.fontSize(Constants.FONT_SIZE.MEDIUM);
                        Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(3, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        List.create();
                        List.layoutWeight(1);
                        List.divider({
                            strokeWidth: 1,
                            color: Constants.COLORS.BORDER,
                            startMargin: 16,
                            endMargin: 16
                        });
                    }, List);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const location = _item;
                            {
                                const itemCreation = (elmtId, isInitialRender) => {
                                    ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                    itemCreation2(elmtId, isInitialRender);
                                    if (!isInitialRender) {
                                        ListItem.pop();
                                    }
                                    ViewStackProcessor.StopGetAccessRecording();
                                };
                                const itemCreation2 = (elmtId, isInitialRender) => {
                                    ListItem.create(deepRenderFunction, true);
                                    ListItem.onClick(() => {
                                        this.selectLocation(location);
                                    });
                                };
                                const deepRenderFunction = (elmtId, isInitialRender) => {
                                    itemCreation(elmtId, isInitialRender);
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        Row.create();
                                        Row.width('100%');
                                        Row.padding(16);
                                        Row.backgroundColor(Constants.COLORS.WHITE);
                                    }, Row);
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        Column.create();
                                        Column.layoutWeight(1);
                                        Column.alignItems(HorizontalAlign.Start);
                                    }, Column);
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        Text.create(location.name);
                                        Text.fontSize(Constants.FONT_SIZE.NORMAL);
                                        Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                                        Text.fontWeight(FontWeight.Medium);
                                        Text.margin({ bottom: location.code ? 4 : 0 });
                                    }, Text);
                                    Text.pop();
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        If.create();
                                        if (location.code) {
                                            this.ifElseBranchUpdateFunction(0, () => {
                                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                    Text.create(location.code);
                                                    Text.fontSize(Constants.FONT_SIZE.SMALL);
                                                    Text.fontColor(Constants.COLORS.TEXT_HINT);
                                                }, Text);
                                                Text.pop();
                                            });
                                        }
                                        else {
                                            this.ifElseBranchUpdateFunction(1, () => {
                                            });
                                        }
                                    }, If);
                                    If.pop();
                                    Column.pop();
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        If.create();
                                        if (this.selectedLocation && this.selectedLocation.id === location.id) {
                                            this.ifElseBranchUpdateFunction(0, () => {
                                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                    Image.create({ "id": 16777248, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                                                    Image.width(24);
                                                    Image.height(24);
                                                    Image.fillColor(Constants.COLORS.PRIMARY);
                                                }, Image);
                                            });
                                        }
                                        else {
                                            this.ifElseBranchUpdateFunction(1, () => {
                                            });
                                        }
                                    }, If);
                                    If.pop();
                                    Row.pop();
                                    ListItem.pop();
                                };
                                this.observeComponentCreation2(itemCreation2, ListItem);
                                ListItem.pop();
                            }
                        };
                        this.forEachUpdateFunction(elmtId, this.filteredLocations, forEachItemGenFunction);
                    }, ForEach);
                    ForEach.pop();
                    List.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "LocationPickerPage";
    }
}
registerNamedRoute(() => new LocationPickerPage(undefined, {}), "", { bundleName: "com.example.nexushub", moduleName: "entry", pagePath: "pages/LocationPickerPage", pageFullPath: "entry/src/main/ets/pages/LocationPickerPage", integratedHsp: "false", moduleType: "followWithHap" });
