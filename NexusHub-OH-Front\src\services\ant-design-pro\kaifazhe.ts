// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 审核开发者 管理员审核开发者认证申请 POST /admin/developers/${param0}/verify */
export async function postAdminDevelopersIdVerify(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postAdminDevelopersIdVerifyParams,
  body: API.AdminVerifyRequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Response & { data?: API.DeveloperVerifyResponse }>(
    `/admin/developers/${param0}/verify`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      params: { ...queryParams },
      data: body,
      ...(options || {}),
    },
  );
}

/** 获取待审核的开发者 管理员获取待审核的开发者列表 GET /admin/developers/verify */
export async function getAdminDevelopersVerify(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAdminDevelopersVerifyParams,
  options?: { [key: string]: any },
) {
  return request<API.PageResponse & { data?: API.DeveloperVerifyResponse[] }>(
    '/admin/developers/verify',
    {
      method: 'GET',
      params: {
        // page has a default value: 1
        page: '1',
        // page_size has a default value: 20
        page_size: '20',
        // status has a default value: "pending"
        status: '"pending"',
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 获取开发者认证统计数据 管理员获取开发者认证统计信息 GET /admin/developers/stats */
export async function getAdminDevelopersStats(
  options?: { [key: string]: any },
) {
  return request<API.Response & { data?: API.DeveloperStatsResponse }>(
    '/admin/developers/stats',
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/** 获取最近的开发者申请 管理员获取最近的开发者认证申请 GET /admin/developers/recent */
export async function getAdminDevelopersRecent(
  params?: { limit?: number },
  options?: { [key: string]: any },
) {
  return request<API.Response & { data?: API.RecentApplicationResponse[] }>(
    '/admin/developers/recent',
    {
      method: 'GET',
      params: {
        limit: 10,
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 获取文件上传凭证 获取上传到对象存储的预签名URL GET /api/v1/upload/token */
export async function getApiV1UploadToken(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getApiV1UploadTokenParams,
  options?: { [key: string]: any },
) {
  return request<API.Response & { data?: API.UploadResult }>('/upload/token', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 提交开发者认证 用户提交开发者认证申请 POST /developers/verify */
export async function postDevelopersVerify(
  body: API.DeveloperVerifyRequest,
  options?: { [key: string]: any },
) {
  return request<API.Response & { data?: API.DeveloperVerifyResponse }>('/developers/verify', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取开发者认证状态 获取当前用户的开发者认证状态 GET /developers/verify/status */
export async function getDevelopersVerifyStatus(options?: { [key: string]: any }) {
  return request<API.Response & { data?: API.DeveloperVerifyResponse }>(
    '/developers/verify/status',
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}
