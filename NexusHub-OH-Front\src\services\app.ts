// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/**
 * 获取应用列表
 * @param params - 查询参数
 */
export async function getAppList(params?: {
  page?: number;
  pageSize?: number;
  keyword?: string;
  category?: number;
  status?: 'pending' | 'approved' | 'rejected' | 'published' | 'featured';
}) {
  return request('/apps', {
    method: 'GET',
    params,
  });
}

/**
 * 获取应用详情
 * @param id - 应用ID
 */
export async function getAppDetail(id: string) {
  return request(`/apps/${id}`, {
    method: 'GET',
  });
}

/**
 * 搜索应用
 * @param params - 搜索参数
 */
export async function searchApps(params: {
  keyword: string;
  page?: number;
  pageSize?: number;
  category?: number;
  sort?: 'downloads' | 'rating' | 'price' | 'newest';
}) {
  return request('/app/search', {
    method: 'GET',
    params,
  });
}

/**
 * 获取应用分类
 */
export async function getAppCategories() {
  return request('/app/category', {
    method: 'GET',
  });
}

/**
 * 创建应用
 * @param params - 应用信息
 */
export async function createApp(params: {
  name: string;
  package: string;
  description: string;
  short_desc?: string;
  icon: string;
  banner_image?: string;
  category: string;
  current_version: string;
  size: string;
  min_open_harmony_os_ver?: string;
  tags?: string;
  website_url?: string;
  privacy_url?: string;
}) {
  return request('/apps', {
    method: 'POST',
    data: params,
  });
}

/**
 * 上传应用
 * @param params - 应用信息
 */
export async function uploadApp(params: {
  name: string;
  package_name: string;
  description: string;
  short_desc: string;
  category_id: number;
  price: number;
  version: string;
  version_code: number;
  min_os_version?: string;
}) {
  return request('/app/upload', {
    method: 'POST',
    data: params,
  });
}

/**
 * 上传应用图标
 * @param appId - 应用ID
 * @param formData - 包含图标文件的FormData
 */
export async function uploadAppIcon(appId: string, formData: FormData) {
  return request(`/app/icon/${appId}`, {
    method: 'POST',
    data: formData,
  });
}

/**
 * 上传应用截图
 * @param appId - 应用ID
 * @param formData - 包含截图文件的FormData
 */
export async function uploadAppScreenshot(appId: string, formData: FormData) {
  return request(`/app/screenshot/${appId}`, {
    method: 'POST',
    data: formData,
  });
}

/**
 * 上传应用安装包
 * @param appId - 应用ID
 * @param formData - 包含安装包文件的FormData
 */
export async function uploadAppPackage(appId: string, formData: FormData) {
  return request(`/app/package/${appId}`, {
    method: 'POST',
    data: formData,
  });
}

/**
 * 更新应用信息
 * @param id - 应用ID
 * @param params - 更新参数
 */
export async function updateApp(id: string, params: {
  name?: string;
  description?: string;
  short_desc?: string;
  category?: string;
  icon?: string;
  price?: number;
  current_version?: string;
  size?: number;
  min_open_harmony_os_ver?: string;
  tags?: string[];
  website_url?: string;
  privacy_url?: string;
  developer?: string;
  package?: string;
}) {
  return request(`/apps/${id}`, {
    method: 'PUT',
    data: params,
  });
}

/**
 * 删除应用
 * @param id - 应用ID
 */
export async function deleteApp(id: string) {
  return request(`/app/delete/${id}`, {
    method: 'DELETE',
  });
}

/**
 * 提交应用审核
 * @param id - 应用ID
 */
export async function submitAppForReview(id: string) {
  return request(`/apps/${id}/submit`, {
    method: 'POST',
  });
}