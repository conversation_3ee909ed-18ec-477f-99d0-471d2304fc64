if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface SystemStatusPage_Params {
    healthData?: SystemHealth | null;
    configData?: SystemConfig | null;
    loadingState?: LoadingState;
    selectedTab?: number;
    autoRefresh?: boolean;
    refreshInterval?: number;
    apiService?;
    refreshTimer?: number;
}
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { ApiService } from "@normalized:N&&&entry/src/main/ets/services/ApiService&";
import { LoadingView, LoadingState } from "@normalized:N&&&entry/src/main/ets/components/LoadingView&";
import hilog from "@ohos:hilog";
/**
 * 系统健康状态模型
 */
interface SystemHealth {
    status: string;
    timestamp: string;
    services: ServiceStatus[];
    metrics: SystemMetrics;
}
/**
 * 服务状态模型
 */
interface ServiceStatus {
    name: string;
    status: string;
    response_time?: number;
    last_check: string;
    message?: string;
}
/**
 * 系统指标模型
 */
interface SystemMetrics {
    cpu_usage?: number;
    memory_usage?: number;
    disk_usage?: number;
    active_connections?: number;
    requests_per_minute?: number;
}
/**
 * 功能配置类 - 使用Record类型替代索引签名
 */
type FeatureConfig = Record<string, boolean>;
/**
 * 限制配置类 - 使用Record类型替代索引签名
 */
type LimitConfig = Record<string, number>;
/**
 * 系统配置模型
 */
interface SystemConfig {
    app_name: string;
    version: string;
    environment: string;
    maintenance_mode: boolean;
    features: FeatureConfig;
    limits: LimitConfig;
}
/**
 * 状态颜色映射类
 */
class StatusColorMap {
    healthy: string | Resource = { "id": 16777237, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" };
    up: string | Resource = { "id": 16777237, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" };
    warning: string | Resource = { "id": 16777238, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" };
    degraded: string | Resource = { "id": 16777238, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" };
    error: string | Resource = { "id": 16777236, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" };
    down: string | Resource = { "id": 16777236, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" };
}
/**
 * 状态文本映射类
 */
class StatusTextMap {
    healthy: string = '健康';
    warning: string = '警告';
    error: string = '错误';
    up: string = '正常';
    down: string = '离线';
    degraded: string = '降级';
}
/**
 * 功能条目类
 */
class FeatureEntry {
    key: string = '';
    value: boolean = false;
}
/**
 * 限制条目类
 */
class LimitEntry {
    key: string = '';
    value: number = 0;
}
class SystemStatusPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__healthData = new ObservedPropertyObjectPU(null, this, "healthData");
        this.__configData = new ObservedPropertyObjectPU(null, this, "configData");
        this.__loadingState = new ObservedPropertySimplePU(LoadingState.LOADING, this, "loadingState");
        this.__selectedTab = new ObservedPropertySimplePU(0, this, "selectedTab");
        this.__autoRefresh = new ObservedPropertySimplePU(false, this, "autoRefresh");
        this.__refreshInterval = new ObservedPropertySimplePU(30, this, "refreshInterval");
        this.apiService = ApiService.getInstance();
        this.refreshTimer = -1;
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: SystemStatusPage_Params) {
        if (params.healthData !== undefined) {
            this.healthData = params.healthData;
        }
        if (params.configData !== undefined) {
            this.configData = params.configData;
        }
        if (params.loadingState !== undefined) {
            this.loadingState = params.loadingState;
        }
        if (params.selectedTab !== undefined) {
            this.selectedTab = params.selectedTab;
        }
        if (params.autoRefresh !== undefined) {
            this.autoRefresh = params.autoRefresh;
        }
        if (params.refreshInterval !== undefined) {
            this.refreshInterval = params.refreshInterval;
        }
        if (params.apiService !== undefined) {
            this.apiService = params.apiService;
        }
        if (params.refreshTimer !== undefined) {
            this.refreshTimer = params.refreshTimer;
        }
    }
    updateStateVars(params: SystemStatusPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__healthData.purgeDependencyOnElmtId(rmElmtId);
        this.__configData.purgeDependencyOnElmtId(rmElmtId);
        this.__loadingState.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedTab.purgeDependencyOnElmtId(rmElmtId);
        this.__autoRefresh.purgeDependencyOnElmtId(rmElmtId);
        this.__refreshInterval.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__healthData.aboutToBeDeleted();
        this.__configData.aboutToBeDeleted();
        this.__loadingState.aboutToBeDeleted();
        this.__selectedTab.aboutToBeDeleted();
        this.__autoRefresh.aboutToBeDeleted();
        this.__refreshInterval.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __healthData: ObservedPropertyObjectPU<SystemHealth | null>;
    get healthData() {
        return this.__healthData.get();
    }
    set healthData(newValue: SystemHealth | null) {
        this.__healthData.set(newValue);
    }
    private __configData: ObservedPropertyObjectPU<SystemConfig | null>;
    get configData() {
        return this.__configData.get();
    }
    set configData(newValue: SystemConfig | null) {
        this.__configData.set(newValue);
    }
    private __loadingState: ObservedPropertySimplePU<LoadingState>;
    get loadingState() {
        return this.__loadingState.get();
    }
    set loadingState(newValue: LoadingState) {
        this.__loadingState.set(newValue);
    }
    private __selectedTab: ObservedPropertySimplePU<number>;
    get selectedTab() {
        return this.__selectedTab.get();
    }
    set selectedTab(newValue: number) {
        this.__selectedTab.set(newValue);
    }
    private __autoRefresh: ObservedPropertySimplePU<boolean>;
    get autoRefresh() {
        return this.__autoRefresh.get();
    }
    set autoRefresh(newValue: boolean) {
        this.__autoRefresh.set(newValue);
    }
    private __refreshInterval: ObservedPropertySimplePU<number>; // 秒
    get refreshInterval() {
        return this.__refreshInterval.get();
    }
    set refreshInterval(newValue: number) {
        this.__refreshInterval.set(newValue);
    }
    private apiService;
    private refreshTimer: number;
    /**
     * 获取功能开关条目
     */
    private getFeatureEntries(): FeatureEntry[] {
        if (!this.configData?.features)
            return [];
        const entries: FeatureEntry[] = [];
        const keys = Object.keys(this.configData.features);
        for (let i = 0; i < keys.length; i++) {
            const key = keys[i];
            const entry = new FeatureEntry();
            entry.key = key;
            entry.value = this.configData.features[key];
            entries.push(entry);
        }
        return entries;
    }
    /**
     * 获取系统限制条目
     */
    private getLimitEntries(): LimitEntry[] {
        if (!this.configData?.limits)
            return [];
        const entries: LimitEntry[] = [];
        const keys = Object.keys(this.configData.limits);
        for (let i = 0; i < keys.length; i++) {
            const key = keys[i];
            const entry = new LimitEntry();
            entry.key = key;
            entry.value = this.configData.limits[key];
            entries.push(entry);
        }
        return entries;
    }
    aboutToAppear() {
        this.loadSystemData();
    }
    aboutToDisappear() {
        this.stopAutoRefresh();
    }
    /**
     * 加载系统数据
     */
    private async loadSystemData() {
        try {
            this.loadingState = LoadingState.LOADING;
            // 并行加载健康检查和配置数据
            const responses = await Promise.all([
                this.apiService.healthCheck(),
                this.apiService.getConfig()
            ]);
            if (responses[0].code === 200) {
                this.healthData = responses[0].data as SystemHealth;
            }
            if (responses[1].code === 200) {
                this.configData = responses[1].data as SystemConfig;
            }
            this.loadingState = LoadingState.SUCCESS;
        }
        catch (error) {
            hilog.error(0x0000, 'SystemStatusPage', '加载系统数据失败: %{public}s', JSON.stringify(error));
            this.loadingState = LoadingState.ERROR;
        }
    }
    /**
     * 开始自动刷新
     */
    private startAutoRefresh() {
        this.stopAutoRefresh();
        this.refreshTimer = setInterval(() => {
            this.loadSystemData();
        }, this.refreshInterval * 1000);
    }
    /**
     * 停止自动刷新
     */
    private stopAutoRefresh() {
        if (this.refreshTimer !== -1) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = -1;
        }
    }
    /**
     * 切换自动刷新
     */
    private toggleAutoRefresh() {
        this.autoRefresh = !this.autoRefresh;
        if (this.autoRefresh) {
            this.startAutoRefresh();
        }
        else {
            this.stopAutoRefresh();
        }
    }
    /**
     * 获取状态颜色
     */
    private getStatusColor(status: string): string | Resource {
        const colorMap = new StatusColorMap();
        switch (status) {
            case 'healthy':
                return colorMap.healthy;
            case 'up':
                return colorMap.up;
            case 'warning':
                return colorMap.warning;
            case 'degraded':
                return colorMap.degraded;
            case 'error':
                return colorMap.error;
            case 'down':
                return colorMap.down;
            default:
                return Constants.COLORS.TEXT_HINT;
        }
    }
    /**
     * 获取状态文本
     */
    private getStatusText(status: string): string {
        const textMap = new StatusTextMap();
        switch (status) {
            case 'healthy':
                return textMap.healthy;
            case 'warning':
                return textMap.warning;
            case 'error':
                return textMap.error;
            case 'up':
                return textMap.up;
            case 'down':
                return textMap.down;
            case 'degraded':
                return textMap.degraded;
            default:
                return '未知';
        }
    }
    /**
     * 格式化时间
     */
    private formatTime(timeStr: string): string {
        const time = new Date(timeStr);
        return time.toLocaleString();
    }
    /**
     * 格式化百分比
     */
    private formatPercentage(value: number): string {
        return `${Math.round(value)}%`;
    }
    /**
     * 构建健康状态页面
     */
    buildHealthTab(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.healthData) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Scroll.create();
                        Scroll.layoutWeight(1);
                        Scroll.scrollBar(BarState.Off);
                    }, Scroll);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 总体状态
                        Column.create();
                        // 总体状态
                        Column.width('100%');
                        // 总体状态
                        Column.padding(16);
                        // 总体状态
                        Column.backgroundColor(Constants.COLORS.WHITE);
                        // 总体状态
                        Column.borderRadius(12);
                        // 总体状态
                        Column.margin({ left: 16, right: 16, bottom: 16 });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.width('100%');
                        Row.margin({ bottom: 8 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Circle.create();
                        Circle.width(12);
                        Circle.height(12);
                        Circle.fill(this.getStatusColor(this.healthData.status));
                        Circle.margin({ right: 8 });
                    }, Circle);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('系统状态');
                        Text.fontSize(Constants.FONT_SIZE.NORMAL);
                        Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                        Text.fontWeight(FontWeight.Medium);
                        Text.layoutWeight(1);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.getStatusText(this.healthData.status));
                        Text.fontSize(Constants.FONT_SIZE.NORMAL);
                        Text.fontColor(this.getStatusColor(this.healthData.status));
                        Text.fontWeight(FontWeight.Medium);
                    }, Text);
                    Text.pop();
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`最后检查: ${this.formatTime(this.healthData.timestamp)}`);
                        Text.fontSize(Constants.FONT_SIZE.SMALL);
                        Text.fontColor(Constants.COLORS.TEXT_HINT);
                        Text.alignSelf(ItemAlign.Start);
                    }, Text);
                    Text.pop();
                    // 总体状态
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        // 系统指标
                        if (this.healthData.metrics) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Column.create();
                                    Column.width('100%');
                                    Column.padding(16);
                                    Column.backgroundColor(Constants.COLORS.WHITE);
                                    Column.borderRadius(12);
                                    Column.margin({ left: 16, right: 16, bottom: 16 });
                                }, Column);
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create('系统指标');
                                    Text.fontSize(Constants.FONT_SIZE.NORMAL);
                                    Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                                    Text.fontWeight(FontWeight.Medium);
                                    Text.margin({ bottom: 12 });
                                    Text.alignSelf(ItemAlign.Start);
                                }, Text);
                                Text.pop();
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    If.create();
                                    if (this.healthData.metrics.cpu_usage !== undefined) {
                                        this.ifElseBranchUpdateFunction(0, () => {
                                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                Row.create();
                                                Row.width('100%');
                                                Row.margin({ bottom: 8 });
                                            }, Row);
                                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                Text.create('CPU使用率');
                                                Text.fontSize(Constants.FONT_SIZE.SMALL);
                                                Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                                                Text.layoutWeight(1);
                                            }, Text);
                                            Text.pop();
                                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                Text.create(this.formatPercentage(this.healthData.metrics.cpu_usage));
                                                Text.fontSize(Constants.FONT_SIZE.SMALL);
                                                Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                                                Text.fontWeight(FontWeight.Medium);
                                            }, Text);
                                            Text.pop();
                                            Row.pop();
                                        });
                                    }
                                    else {
                                        this.ifElseBranchUpdateFunction(1, () => {
                                        });
                                    }
                                }, If);
                                If.pop();
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    If.create();
                                    if (this.healthData.metrics.memory_usage !== undefined) {
                                        this.ifElseBranchUpdateFunction(0, () => {
                                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                Row.create();
                                                Row.width('100%');
                                                Row.margin({ bottom: 8 });
                                            }, Row);
                                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                Text.create('内存使用率');
                                                Text.fontSize(Constants.FONT_SIZE.SMALL);
                                                Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                                                Text.layoutWeight(1);
                                            }, Text);
                                            Text.pop();
                                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                Text.create(this.formatPercentage(this.healthData.metrics.memory_usage));
                                                Text.fontSize(Constants.FONT_SIZE.SMALL);
                                                Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                                                Text.fontWeight(FontWeight.Medium);
                                            }, Text);
                                            Text.pop();
                                            Row.pop();
                                        });
                                    }
                                    else {
                                        this.ifElseBranchUpdateFunction(1, () => {
                                        });
                                    }
                                }, If);
                                If.pop();
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    If.create();
                                    if (this.healthData.metrics.active_connections !== undefined) {
                                        this.ifElseBranchUpdateFunction(0, () => {
                                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                Row.create();
                                                Row.width('100%');
                                                Row.margin({ bottom: 8 });
                                            }, Row);
                                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                Text.create('活跃连接数');
                                                Text.fontSize(Constants.FONT_SIZE.SMALL);
                                                Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                                                Text.layoutWeight(1);
                                            }, Text);
                                            Text.pop();
                                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                Text.create(this.healthData.metrics.active_connections.toString());
                                                Text.fontSize(Constants.FONT_SIZE.SMALL);
                                                Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                                                Text.fontWeight(FontWeight.Medium);
                                            }, Text);
                                            Text.pop();
                                            Row.pop();
                                        });
                                    }
                                    else {
                                        this.ifElseBranchUpdateFunction(1, () => {
                                        });
                                    }
                                }, If);
                                If.pop();
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    If.create();
                                    if (this.healthData.metrics.requests_per_minute !== undefined) {
                                        this.ifElseBranchUpdateFunction(0, () => {
                                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                Row.create();
                                                Row.width('100%');
                                            }, Row);
                                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                Text.create('每分钟请求数');
                                                Text.fontSize(Constants.FONT_SIZE.SMALL);
                                                Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                                                Text.layoutWeight(1);
                                            }, Text);
                                            Text.pop();
                                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                Text.create(this.healthData.metrics.requests_per_minute.toString());
                                                Text.fontSize(Constants.FONT_SIZE.SMALL);
                                                Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                                                Text.fontWeight(FontWeight.Medium);
                                            }, Text);
                                            Text.pop();
                                            Row.pop();
                                        });
                                    }
                                    else {
                                        this.ifElseBranchUpdateFunction(1, () => {
                                        });
                                    }
                                }, If);
                                If.pop();
                                Column.pop();
                            });
                        }
                        // 服务状态
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        // 服务状态
                        if (this.healthData.services && this.healthData.services.length > 0) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Column.create();
                                    Column.width('100%');
                                    Column.padding(16);
                                    Column.backgroundColor(Constants.COLORS.WHITE);
                                    Column.borderRadius(12);
                                    Column.margin({ left: 16, right: 16, bottom: 16 });
                                }, Column);
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create('服务状态');
                                    Text.fontSize(Constants.FONT_SIZE.NORMAL);
                                    Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                                    Text.fontWeight(FontWeight.Medium);
                                    Text.margin({ bottom: 12 });
                                    Text.alignSelf(ItemAlign.Start);
                                }, Text);
                                Text.pop();
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    ForEach.create();
                                    const forEachItemGenFunction = (_item, index: number) => {
                                        const service = _item;
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Column.create();
                                        }, Column);
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Row.create();
                                            Row.width('100%');
                                            Row.margin({ bottom: service.response_time || service.message ? 4 : 0 });
                                        }, Row);
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Circle.create();
                                            Circle.width(8);
                                            Circle.height(8);
                                            Circle.fill(this.getStatusColor(service.status));
                                            Circle.margin({ right: 8 });
                                        }, Circle);
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Text.create(service.name);
                                            Text.fontSize(Constants.FONT_SIZE.SMALL);
                                            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                                            Text.fontWeight(FontWeight.Medium);
                                            Text.layoutWeight(1);
                                        }, Text);
                                        Text.pop();
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Text.create(this.getStatusText(service.status));
                                            Text.fontSize(Constants.FONT_SIZE.SMALL);
                                            Text.fontColor(this.getStatusColor(service.status));
                                        }, Text);
                                        Text.pop();
                                        Row.pop();
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            If.create();
                                            if (service.response_time) {
                                                this.ifElseBranchUpdateFunction(0, () => {
                                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                        Text.create(`响应时间: ${service.response_time}ms`);
                                                        Text.fontSize(Constants.FONT_SIZE.SMALL);
                                                        Text.fontColor(Constants.COLORS.TEXT_HINT);
                                                        Text.alignSelf(ItemAlign.Start);
                                                        Text.margin({ left: 16, bottom: 4 });
                                                    }, Text);
                                                    Text.pop();
                                                });
                                            }
                                            else {
                                                this.ifElseBranchUpdateFunction(1, () => {
                                                });
                                            }
                                        }, If);
                                        If.pop();
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            If.create();
                                            if (service.message) {
                                                this.ifElseBranchUpdateFunction(0, () => {
                                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                        Text.create(service.message);
                                                        Text.fontSize(Constants.FONT_SIZE.SMALL);
                                                        Text.fontColor(Constants.COLORS.TEXT_HINT);
                                                        Text.alignSelf(ItemAlign.Start);
                                                        Text.margin({ left: 16, bottom: 4 });
                                                    }, Text);
                                                    Text.pop();
                                                });
                                            }
                                            else {
                                                this.ifElseBranchUpdateFunction(1, () => {
                                                });
                                            }
                                        }, If);
                                        If.pop();
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Text.create(`最后检查: ${this.formatTime(service.last_check)}`);
                                            Text.fontSize(Constants.FONT_SIZE.SMALL);
                                            Text.fontColor(Constants.COLORS.TEXT_HINT);
                                            Text.alignSelf(ItemAlign.Start);
                                            Text.margin({ left: 16 });
                                        }, Text);
                                        Text.pop();
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            If.create();
                                            if (index < this.healthData!.services.length - 1) {
                                                this.ifElseBranchUpdateFunction(0, () => {
                                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                        Divider.create();
                                                        Divider.color(Constants.COLORS.BORDER);
                                                        Divider.margin({ top: 12, bottom: 12 });
                                                    }, Divider);
                                                });
                                            }
                                            else {
                                                this.ifElseBranchUpdateFunction(1, () => {
                                                });
                                            }
                                        }, If);
                                        If.pop();
                                        Column.pop();
                                    };
                                    this.forEachUpdateFunction(elmtId, this.healthData.services, forEachItemGenFunction, undefined, true, false);
                                }, ForEach);
                                ForEach.pop();
                                Column.pop();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    Column.pop();
                    Scroll.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
    }
    /**
     * 构建配置页面
     */
    buildConfigTab(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.configData) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Scroll.create();
                        Scroll.layoutWeight(1);
                        Scroll.scrollBar(BarState.Off);
                    }, Scroll);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 基本信息
                        Column.create();
                        // 基本信息
                        Column.width('100%');
                        // 基本信息
                        Column.padding(16);
                        // 基本信息
                        Column.backgroundColor(Constants.COLORS.WHITE);
                        // 基本信息
                        Column.borderRadius(12);
                        // 基本信息
                        Column.margin({ left: 16, right: 16, bottom: 16 });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('基本信息');
                        Text.fontSize(Constants.FONT_SIZE.NORMAL);
                        Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                        Text.fontWeight(FontWeight.Medium);
                        Text.margin({ bottom: 12 });
                        Text.alignSelf(ItemAlign.Start);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.width('100%');
                        Row.margin({ bottom: 8 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('应用名称');
                        Text.fontSize(Constants.FONT_SIZE.SMALL);
                        Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                        Text.layoutWeight(1);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.configData.app_name);
                        Text.fontSize(Constants.FONT_SIZE.SMALL);
                        Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                        Text.fontWeight(FontWeight.Medium);
                    }, Text);
                    Text.pop();
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.width('100%');
                        Row.margin({ bottom: 8 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('版本');
                        Text.fontSize(Constants.FONT_SIZE.SMALL);
                        Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                        Text.layoutWeight(1);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.configData.version);
                        Text.fontSize(Constants.FONT_SIZE.SMALL);
                        Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                        Text.fontWeight(FontWeight.Medium);
                    }, Text);
                    Text.pop();
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.width('100%');
                        Row.margin({ bottom: 8 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('环境');
                        Text.fontSize(Constants.FONT_SIZE.SMALL);
                        Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                        Text.layoutWeight(1);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.configData.environment);
                        Text.fontSize(Constants.FONT_SIZE.SMALL);
                        Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                        Text.fontWeight(FontWeight.Medium);
                    }, Text);
                    Text.pop();
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.width('100%');
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('维护模式');
                        Text.fontSize(Constants.FONT_SIZE.SMALL);
                        Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                        Text.layoutWeight(1);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.configData.maintenance_mode ? '开启' : '关闭');
                        Text.fontSize(Constants.FONT_SIZE.SMALL);
                        Text.fontColor(this.configData.maintenance_mode ? { "id": 16777236, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" } : { "id": 16777237, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                        Text.fontWeight(FontWeight.Medium);
                    }, Text);
                    Text.pop();
                    Row.pop();
                    // 基本信息
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        // 功能开关
                        if (this.configData.features && Object.keys(this.configData.features).length > 0) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Column.create();
                                    Column.width('100%');
                                    Column.padding(16);
                                    Column.backgroundColor(Constants.COLORS.WHITE);
                                    Column.borderRadius(12);
                                    Column.margin({ left: 16, right: 16, bottom: 16 });
                                }, Column);
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create('功能开关');
                                    Text.fontSize(Constants.FONT_SIZE.NORMAL);
                                    Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                                    Text.fontWeight(FontWeight.Medium);
                                    Text.margin({ bottom: 12 });
                                    Text.alignSelf(ItemAlign.Start);
                                }, Text);
                                Text.pop();
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    ForEach.create();
                                    const forEachItemGenFunction = _item => {
                                        const item = _item;
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Row.create();
                                            Row.width('100%');
                                            Row.margin({ bottom: 8 });
                                        }, Row);
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Text.create(item.key);
                                            Text.fontSize(Constants.FONT_SIZE.SMALL);
                                            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                                            Text.layoutWeight(1);
                                        }, Text);
                                        Text.pop();
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Text.create(item.value ? '开启' : '关闭');
                                            Text.fontSize(Constants.FONT_SIZE.SMALL);
                                            Text.fontColor(item.value ? { "id": 16777237, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" } : Constants.COLORS.TEXT_HINT);
                                            Text.fontWeight(FontWeight.Medium);
                                        }, Text);
                                        Text.pop();
                                        Row.pop();
                                    };
                                    this.forEachUpdateFunction(elmtId, this.getFeatureEntries(), forEachItemGenFunction, (item: FeatureEntry) => item.key, false, false);
                                }, ForEach);
                                ForEach.pop();
                                Column.pop();
                            });
                        }
                        // 系统限制
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        // 系统限制
                        if (this.configData.limits && Object.keys(this.configData.limits).length > 0) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Column.create();
                                    Column.width('100%');
                                    Column.padding(16);
                                    Column.backgroundColor(Constants.COLORS.WHITE);
                                    Column.borderRadius(12);
                                    Column.margin({ left: 16, right: 16, bottom: 16 });
                                }, Column);
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create('系统限制');
                                    Text.fontSize(Constants.FONT_SIZE.NORMAL);
                                    Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                                    Text.fontWeight(FontWeight.Medium);
                                    Text.margin({ bottom: 12 });
                                    Text.alignSelf(ItemAlign.Start);
                                }, Text);
                                Text.pop();
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    ForEach.create();
                                    const forEachItemGenFunction = _item => {
                                        const item = _item;
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Row.create();
                                            Row.width('100%');
                                            Row.margin({ bottom: 8 });
                                        }, Row);
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Text.create(item.key);
                                            Text.fontSize(Constants.FONT_SIZE.SMALL);
                                            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                                            Text.layoutWeight(1);
                                        }, Text);
                                        Text.pop();
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Text.create(item.value.toString());
                                            Text.fontSize(Constants.FONT_SIZE.SMALL);
                                            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                                            Text.fontWeight(FontWeight.Medium);
                                        }, Text);
                                        Text.pop();
                                        Row.pop();
                                    };
                                    this.forEachUpdateFunction(elmtId, this.getLimitEntries(), forEachItemGenFunction, (item: LimitEntry) => item.key, false, false);
                                }, ForEach);
                                ForEach.pop();
                                Column.pop();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    Column.pop();
                    Scroll.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor(Constants.COLORS.BACKGROUND);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 标题栏
            Row.create();
            // 标题栏
            Row.width('100%');
            // 标题栏
            Row.height(56);
            // 标题栏
            Row.padding({ left: 16, right: 16 });
            // 标题栏
            Row.backgroundColor(Constants.COLORS.WHITE);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.width(40);
            Button.height(40);
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                this.getUIContext().getRouter().back();
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777246, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(24);
            Image.height(24);
            Image.fillColor(Constants.COLORS.TEXT_PRIMARY);
        }, Image);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('系统状态');
            Text.fontSize(Constants.FONT_SIZE.LARGE);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.width(32);
            Button.height(32);
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                this.loadSystemData();
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777269, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(20);
            Image.height(20);
            Image.fillColor(Constants.COLORS.TEXT_PRIMARY);
        }, Image);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.width(32);
            Button.height(32);
            Button.backgroundColor(Color.Transparent);
            Button.margin({ left: 8 });
            Button.onClick(() => {
                this.toggleAutoRefresh();
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777269, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(20);
            Image.height(20);
            Image.fillColor(this.autoRefresh ? Constants.COLORS.WHITE : Constants.COLORS.TEXT_SECONDARY);
            Image.position({ x: 10, y: 10 });
        }, Image);
        Button.pop();
        Row.pop();
        // 标题栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 标签页
            Row.create();
            // 标签页
            Row.width('100%');
            // 标签页
            Row.height(48);
            // 标签页
            Row.backgroundColor(Constants.COLORS.WHITE);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('健康状态');
            Button.fontSize(Constants.FONT_SIZE.NORMAL);
            Button.fontColor(this.selectedTab === 0 ? Constants.COLORS.PRIMARY : Constants.COLORS.TEXT_SECONDARY);
            Button.backgroundColor(Color.Transparent);
            Button.layoutWeight(1);
            Button.onClick(() => {
                this.selectedTab = 0;
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('系统配置');
            Button.fontSize(Constants.FONT_SIZE.NORMAL);
            Button.fontColor(this.selectedTab === 1 ? Constants.COLORS.PRIMARY : Constants.COLORS.TEXT_SECONDARY);
            Button.backgroundColor(Color.Transparent);
            Button.layoutWeight(1);
            Button.onClick(() => {
                this.selectedTab = 1;
            });
        }, Button);
        Button.pop();
        // 标签页
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 内容区域
            if (this.loadingState === LoadingState.LOADING) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/SystemStatusPage.ets", line: 712, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {};
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {});
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else if (this.loadingState === LoadingState.ERROR) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.layoutWeight(1);
                        Column.justifyContent(FlexAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Image.create({ "id": 16777254, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                        Image.width(64);
                        Image.height(64);
                        Image.fillColor(Constants.COLORS.TEXT_HINT);
                        Image.margin({ bottom: 16 });
                    }, Image);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('加载失败');
                        Text.fontSize(Constants.FONT_SIZE.MEDIUM);
                        Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                        Text.margin({ bottom: 16 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('重试');
                        Button.fontSize(Constants.FONT_SIZE.NORMAL);
                        Button.fontColor(Constants.COLORS.WHITE);
                        Button.backgroundColor(Constants.COLORS.PRIMARY);
                        Button.borderRadius(8);
                        Button.padding({ left: 24, right: 24, top: 8, bottom: 8 });
                        Button.onClick(() => {
                            this.loadSystemData();
                        });
                    }, Button);
                    Button.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.selectedTab === 0) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.buildHealthTab.bind(this)();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                                this.buildConfigTab.bind(this)();
                            });
                        }
                    }, If);
                    If.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "SystemStatusPage";
    }
}
registerNamedRoute(() => new SystemStatusPage(undefined, {}), "", { bundleName: "com.example.nexushub", moduleName: "entry", pagePath: "pages/SystemStatusPage", pageFullPath: "entry/src/main/ets/pages/SystemStatusPage", integratedHsp: "false", moduleType: "followWithHap" });
