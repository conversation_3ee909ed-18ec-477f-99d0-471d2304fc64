import { InfoCircleOutlined } from '@ant-design/icons';
import { Area } from '@ant-design/plots';
import { Card, Col, Row, Table, Tooltip, Avatar } from 'antd';
import { AppstoreOutlined } from '@ant-design/icons';
import numeral from 'numeral';
import React from 'react';
import type { DataItem } from '../data.d';
import useStyles from '../style.style';
import NumberInfo from './NumberInfo';
import Trend from './Trend';

const TopSearch = ({
  loading,
  visitData2,
  searchData,
  dropdownGroup,
}: {
  loading: boolean;
  visitData2: DataItem[];
  dropdownGroup: React.ReactNode;
  searchData: DataItem[];
}) => {
  const { styles } = useStyles();
  const columns = [
    {
      title: '排名',
      key: 'index',
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: '应用',
      key: 'app',
      render: (text: any, record: any) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar 
              src={record.app_icon} 
              size="small" 
              style={{ marginRight: 8 }}
              icon={<AppstoreOutlined />}
              onError={() => {
                console.warn('应用图标加载失败:', record.app_icon);
                return false;
              }}
            />
          <a href="/">{record.app_name}</a>
        </div>
      ),
    },
    {
      title: '开发者',
      dataIndex: 'developer_name',
      key: 'developer_name',
    },
    {
      title: '分类',
      dataIndex: 'category_name',
      key: 'category_name',
    },
    {
      title: '下载量',
      dataIndex: 'download_count',
      key: 'download_count',
      sorter: (a: any, b: any) => a.download_count - b.download_count,
      render: (text: number) => numeral(text).format('0,0'),
    },
    {
      title: '评分',
      dataIndex: 'rating',
      key: 'rating',
      sorter: (a: any, b: any) => a.rating - b.rating,
      render: (text: number) => text ? text.toFixed(1) : '-',
    },
  ];
  return (
    <Card
      loading={loading}
      bordered={false}
      title="热门应用"
      extra={dropdownGroup}
      style={{
        height: '100%',
      }}
    >
      <Row gutter={68}>
        <Col
          sm={12}
          xs={24}
          style={{
            marginBottom: 24,
          }}
        >
          <NumberInfo
            subTitle={
              <span>
                总下载量
                <Tooltip title="所有热门应用的总下载量">
                  <InfoCircleOutlined
                    style={{
                      marginLeft: 8,
                    }}
                  />
                </Tooltip>
              </span>
            }
            gap={8}
            total={numeral(searchData.reduce((sum, item) => sum + (item.download_count || 0), 0)).format('0,0')}
            status="up"
            subTotal={17.1}
          />
          <Area
            xField="x"
            yField="y"
            shapeField="smooth"
            height={45}
            axis={false}
            padding={-12}
            style={{ fill: 'linear-gradient(-90deg, white 0%, #6294FA 100%)', fillOpacity: 0.4 }}
            data={visitData2}
          />
        </Col>
        <Col
          sm={12}
          xs={24}
          style={{
            marginBottom: 24,
          }}
        >
          <NumberInfo
            subTitle={
              <span>
                平均评分
                <Tooltip title="所有热门应用的平均评分">
                  <InfoCircleOutlined
                    style={{
                      marginLeft: 8,
                    }}
                  />
                </Tooltip>
              </span>
            }
            total={(searchData.reduce((sum, item) => sum + (item.rating || 0), 0) / (searchData.length || 1)).toFixed(1)}
            status="up"
            subTotal={2.3}
            gap={8}
          />
          <Area
            xField="x"
            yField="y"
            shapeField="smooth"
            height={45}
            padding={-12}
            style={{ fill: 'linear-gradient(-90deg, white 0%, #6294FA 100%)', fillOpacity: 0.4 }}
            data={visitData2}
            axis={false}
          />
        </Col>
      </Row>
      <Table<any>
        rowKey={(record) => record.app_id}
        size="small"
        columns={columns}
        dataSource={searchData}
        pagination={{
          style: {
            marginBottom: 0,
          },
          pageSize: 5,
        }}
      />
    </Card>
  );
};
export default TopSearch;
