import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Upload,
  message,
  Steps,
  Row,
  Col,
  Alert,
  Space,
  Typography,
  Divider,
  Spin,
  Radio,
  Select,
} from 'antd';
import { UploadOutlined, CheckCircleOutlined, ClockCircleOutlined, CloseCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { postDevelopersVerify, getDevelopersVerifyStatus, getApiV1UploadToken } from '@/services/ant-design-pro/kaifazhe';
import type { UploadFile } from 'antd/es/upload/interface';
import DeveloperVerifyStatus from '@/components/DeveloperVerifyStatus';
import dayjs from 'dayjs';
import styles from './index.less';

const { Step } = Steps;
const { Title, Paragraph, Text } = Typography;
const { TextArea } = Input;

interface DeveloperVerifyData {
  developer_type: 'individual' | 'enterprise';
  developer_name: string;
  company_name?: string;
  contact_email: string;
  contact_phone: string;
  website?: string;
  description: string;
  developer_address: string;
  developer_avatar?: string;
  business_license?: string;
  identity_card: string;
}

interface VerifyStatus {
  verify_status: 'pending' | 'approved' | 'rejected';
  verify_reason?: string;
  submitted_at?: string;
  verified_at?: string;
}

const DeveloperVerify: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [statusLoading, setStatusLoading] = useState(true);
  const [currentStep, setCurrentStep] = useState(0);
  const [verifyStatus, setVerifyStatus] = useState<VerifyStatus | null>(null);
  const [avatarFileList, setAvatarFileList] = useState<UploadFile[]>([]);
  const [licenseFileList, setLicenseFileList] = useState<UploadFile[]>([]);
  const [idCardFileList, setIdCardFileList] = useState<UploadFile[]>([]);
  const [developerType, setDeveloperType] = useState<'individual' | 'enterprise'>('individual');

  // 获取认证状态
  const fetchVerifyStatus = async () => {
    setStatusLoading(true);
    try {
      const response = await getDevelopersVerifyStatus();
      if (response.code === 200) {
        // 如果有数据且有认证状态（非空字符串），则设置状态
        if (response.data && response.data.verify_status && response.data.verify_status.trim() !== '') {
          setVerifyStatus(response.data);
          if (response.data.verify_status === 'pending') {
            setCurrentStep(1);
          } else if (response.data.verify_status === 'approved') {
            setCurrentStep(2);
          } else if (response.data.verify_status === 'rejected') {
            setCurrentStep(0);
          }
        } else {
          // 没有认证记录或认证状态为空，设置为初始状态
          setVerifyStatus(null);
          setCurrentStep(0);
        }
      } else {
        // API返回错误，设置为初始状态
        setVerifyStatus(null);
        setCurrentStep(0);
      }
    } catch (error) {
      console.error('获取认证状态失败:', error);
      // 发生错误时，设置为初始状态而不是显示错误
      setVerifyStatus(null);
      setCurrentStep(0);
    } finally {
      setStatusLoading(false);
    }
  };

  useEffect(() => {
    fetchVerifyStatus();
  }, []);

  // 重新申请处理
  const handleResubmit = () => {
    setCurrentStep(0);
    form.resetFields();
    setAvatarFileList([]);
    setLicenseFileList([]);
    setIdCardFileList([]);
    setDeveloperType('individual');
    message.info('请重新填写认证信息');
  };

  // 刷新状态
  const handleRefreshStatus = () => {
    fetchVerifyStatus();
    message.info('状态已刷新');
  };

  // 自定义上传函数
  const customUpload = async (options: any, fileType: string) => {
    const { file, onSuccess, onError } = options;
    
    try {
      // 获取上传凭证
      const tokenResponse = await getApiV1UploadToken({
        file_type: fileType,
        file_name: file.name,
      });
      
      if (tokenResponse.code === 200 && tokenResponse.data) {
        // 模拟上传成功
        onSuccess({
          url: tokenResponse.data.file_url,
        });
        message.success('文件上传成功');
      } else {
        onError(new Error('获取上传凭证失败'));
      }
    } catch (error) {
      onError(error);
      message.error('文件上传失败');
    }
  };

  // 处理文件上传
  const handleUploadChange = (info: any, type: 'avatar' | 'license' | 'identity') => {
    const { fileList } = info;
    
    if (type === 'avatar') {
      setAvatarFileList(fileList);
    } else if (type === 'license') {
      setLicenseFileList(fileList);
    } else if (type === 'identity') {
      setIdCardFileList(fileList);
    }
  };

  // 提交认证申请
  const handleSubmit = async (values: DeveloperVerifyData) => {
    setLoading(true);
    
    try {
      // 验证必需的文件上传
      if (!idCardFileList.length || !idCardFileList[0]?.response?.url) {
        message.error('请上传身份证照片');
        setLoading(false);
        return;
      }
      
      if (values.developer_type === 'enterprise' && (!licenseFileList.length || !licenseFileList[0]?.response?.url)) {
        message.error('企业开发者请上传营业执照');
        setLoading(false);
        return;
      }
      
      // 获取上传的文件URL
      const avatarUrl = avatarFileList[0]?.response?.url || '';
      const licenseUrl = licenseFileList[0]?.response?.url || '';
      const idCardUrl = idCardFileList[0]?.response?.url || '';
      
      const submitData = {
        ...values,
        developer_avatar: avatarUrl,
        business_license: licenseUrl,
        identity_card: idCardUrl,
      };
      
      const response = await postDevelopersVerify(submitData);
      
      if (response.code === 200) {
        message.success('开发者认证申请提交成功，请等待审核');
        setCurrentStep(1);
        await fetchVerifyStatus();
        // 清空表单
        form.resetFields();
        setAvatarFileList([]);
        setLicenseFileList([]);
        setIdCardFileList([]);
      } else {
        message.error(response.message || '提交失败');
      }
    } catch (error) {
      console.error('提交认证申请失败:', error);
      message.error('提交失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 渲染状态图标
  const renderStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <ClockCircleOutlined style={{ color: '#faad14' }} />;
      case 'approved':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'rejected':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return null;
    }
  };

  // 渲染认证表单
  const renderVerifyForm = () => (
    <Card title="开发者认证申请" className={styles.verifyCard}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          developer_type: 'individual',
          developer_name: '',
          company_name: '',
          contact_email: '',
          contact_phone: '',
          website: '',
          description: '',
          developer_address: '',
        }}
        onValuesChange={(changedValues) => {
          if (changedValues.developer_type) {
            setDeveloperType(changedValues.developer_type);
          }
        }}
      >
        {/* 开发者类型选择 */}
        <Card size="small" style={{ marginBottom: 24, backgroundColor: '#fafafa' }}>
          <Form.Item
            name="developer_type"
            label="开发者类型"
            rules={[{ required: true, message: '请选择开发者类型' }]}
          >
            <Radio.Group>
              <Radio value="individual">个人开发者</Radio>
              <Radio value="enterprise">企业开发者</Radio>
            </Radio.Group>
          </Form.Item>
          <Alert
            message={developerType === 'individual' ? '个人开发者说明' : '企业开发者说明'}
            description={
              developerType === 'individual'
                ? '个人开发者适用于个人独立开发者，需要提供个人身份证明和相关资料。'
                : '企业开发者适用于公司或团队，需要提供营业执照、企业信息等相关资料。'
            }
            type="info"
            showIcon
            style={{ marginTop: 12 }}
          />
        </Card>

        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="developer_name"
              label={developerType === 'individual' ? '开发者姓名' : '联系人姓名'}
              rules={[
                { required: true, message: `请输入${developerType === 'individual' ? '开发者姓名' : '联系人姓名'}` },
                { min: 2, max: 100, message: '姓名长度为2-100个字符' },
              ]}
            >
              <Input placeholder={`请输入${developerType === 'individual' ? '开发者姓名' : '联系人姓名'}`} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="company_name"
              label={developerType === 'individual' ? '工作单位' : '公司名称'}
              rules={developerType === 'enterprise' ? [
                { required: true, message: '请输入公司名称' },
                { min: 2, max: 200, message: '公司名称长度为2-200个字符' },
              ] : []}
            >
              <Input placeholder={developerType === 'individual' ? '请输入工作单位（可选）' : '请输入公司名称'} />
            </Form.Item>
          </Col>
        </Row>
        
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="contact_email"
              label="联系邮箱"
              rules={[
                { required: true, message: '请输入联系邮箱' },
                { type: 'email', message: '请输入有效的邮箱地址' },
              ]}
            >
              <Input placeholder="请输入联系邮箱" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="contact_phone"
              label="联系电话"
              rules={[
                { required: true, message: '请输入联系电话' },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' },
              ]}
            >
              <Input placeholder="请输入联系电话" />
            </Form.Item>
          </Col>
        </Row>
        
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="website"
              label="个人网站"
              rules={[
                { type: 'url', message: '请输入有效的网站地址' },
              ]}
            >
              <Input placeholder="请输入个人网站（可选）" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="developer_address"
              label="联系地址"
              rules={[
                { required: true, message: '请输入联系地址' },
                { min: 5, max: 255, message: '联系地址长度为5-255个字符' },
              ]}
            >
              <Input placeholder="请输入联系地址" />
            </Form.Item>
          </Col>
        </Row>
        
        <Form.Item
          name="description"
          label={developerType === 'individual' ? '个人简介' : '企业简介'}
          rules={[
            { required: true, message: `请输入${developerType === 'individual' ? '个人简介' : '企业简介'}` },
            { min: 10, max: 1000, message: '简介长度为10-1000个字符' },
          ]}
        >
          <TextArea
            rows={4}
            placeholder={
              developerType === 'individual'
                ? '请详细描述您的开发经验、技能专长、项目经历等（10-1000字符）'
                : '请详细描述企业的业务范围、技术实力、团队规模等（10-1000字符）'
            }
          />
        </Form.Item>
        
        <Divider>上传认证材料</Divider>
        
        <Alert
          message="上传要求"
          description={
            developerType === 'individual'
              ? '个人开发者需要上传：个人头像（可选）、身份证正面照片（必需）'
              : '企业开发者需要上传：企业Logo（可选）、营业执照（必需）、身份证正面照片（必需）'
          }
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <Row gutter={24}>
          <Col span={8}>
            <Form.Item label={developerType === 'individual' ? '个人头像' : '企业Logo'}>
              <Upload
                customRequest={(options) => customUpload(options, 'avatar')}
                fileList={avatarFileList}
                onChange={(info) => handleUploadChange(info, 'avatar')}
                maxCount={1}
                accept="image/*"
                listType="picture-card"
              >
                {avatarFileList.length === 0 && (
                  <div>
                    <UploadOutlined />
                    <div style={{ marginTop: 8 }}>
                      {developerType === 'individual' ? '上传头像' : '上传Logo'}
                    </div>
                  </div>
                )}
              </Upload>
              <Text type="secondary" style={{ fontSize: 12 }}>
                支持 JPG、PNG 格式，文件大小不超过 2MB（可选）
              </Text>
            </Form.Item>
          </Col>
          
          {developerType === 'enterprise' && (
            <Col span={8}>
              <Form.Item label="营业执照" required>
                <Upload
                  customRequest={(options) => customUpload(options, 'license')}
                  fileList={licenseFileList}
                  onChange={(info) => handleUploadChange(info, 'license')}
                  maxCount={1}
                  accept="image/*"
                  listType="picture-card"
                >
                  {licenseFileList.length === 0 && (
                    <div>
                      <UploadOutlined />
                      <div style={{ marginTop: 8 }}>上传执照</div>
                    </div>
                  )}
                </Upload>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  请上传清晰的营业执照照片
                </Text>
              </Form.Item>
            </Col>
          )}
          
          <Col span={8}>
            <Form.Item label="身份证明" required>
              <Upload
                customRequest={(options) => customUpload(options, 'identity')}
                fileList={idCardFileList}
                onChange={(info) => handleUploadChange(info, 'identity')}
                maxCount={1}
                accept="image/*"
                listType="picture-card"
              >
                {idCardFileList.length === 0 && (
                  <div>
                    <UploadOutlined />
                    <div style={{ marginTop: 8 }}>上传身份证</div>
                  </div>
                )}
              </Upload>
              <Text type="secondary" style={{ fontSize: 12 }}>
                请上传身份证正面照片
              </Text>
            </Form.Item>
          </Col>
        </Row>
        
        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" loading={loading} size="large">
              提交认证申请
            </Button>
            <Button onClick={() => form.resetFields()} size="large">
              重置表单
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );

  // 渲染认证状态
  const renderVerifyStatus = () => {
    if (!verifyStatus) return null;
    
    const { verify_status, verify_reason, submitted_at, verified_at } = verifyStatus;
    
    return (
      <Card title="认证状态" className={styles.statusCard}>
        <div className={styles.statusContent}>
          {renderStatusIcon(verify_status)}
          <div className={styles.statusInfo}>
            <Title level={4}>
              {verify_status === 'pending' && '认证审核中'}
              {verify_status === 'approved' && '认证已通过'}
              {verify_status === 'rejected' && '认证被拒绝'}
            </Title>
            
            {verify_status === 'pending' && (
              <Paragraph>
                您的开发者认证申请已提交，我们将在 1-3 个工作日内完成审核。
                <br />
                提交时间：{submitted_at}
              </Paragraph>
            )}
            
            {verify_status === 'approved' && (
              <Paragraph>
                恭喜！您的开发者认证已通过审核，现在可以发布应用了。
                <br />
                审核通过时间：{verified_at}
              </Paragraph>
            )}
            
            {verify_status === 'rejected' && (
              <div>
                <Paragraph>
                  很抱歉，您的开发者认证申请被拒绝。
                  <br />
                  拒绝时间：{verified_at}
                </Paragraph>
                {verify_reason && (
                  <Alert
                    message="拒绝原因"
                    description={verify_reason}
                    type="error"
                    showIcon
                    style={{ marginBottom: 16 }}
                  />
                )}
                <Button type="primary" onClick={() => setCurrentStep(0)}>
                  重新申请
                </Button>
              </div>
            )}
          </div>
        </div>
      </Card>
    );
  };

  if (statusLoading) {
    return (
      <PageContainer
        title="开发者认证"
        subTitle="成为认证开发者，发布您的应用到 NexusHub-OH 应用商店"
      >
        <Card>
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>正在加载认证状态...</div>
          </div>
        </Card>
      </PageContainer>
    );
  }

  return (
    <PageContainer
      title="开发者认证"
      subTitle="成为认证开发者，发布您的应用到 NexusHub-OH 应用商店"
      extra={[
        <Button
          key="refresh"
          icon={<ReloadOutlined />}
          onClick={handleRefreshStatus}
        >
          刷新状态
        </Button>,
      ]}
    >
      <div className={styles.container}>
        <Card className={styles.stepsCard}>
          <Steps current={currentStep} className={styles.steps}>
            <Step title="提交申请" description="填写认证信息" />
            <Step title="审核中" description="等待平台审核" />
            <Step title="认证完成" description="开始发布应用" />
          </Steps>
        </Card>

        {/* 使用新的状态组件 */}
        {verifyStatus && (
          <DeveloperVerifyStatus
            data={verifyStatus}
            onResubmit={handleResubmit}
            showResubmitButton={verifyStatus.verify_status === 'rejected'}
          />
        )}

        {/* 只有在未提交或被拒绝时显示表单 */}
        {(!verifyStatus || !verifyStatus.verify_status || verifyStatus.verify_status === 'rejected') && renderVerifyForm()}
      </div>
    </PageContainer>
  );
};

export default DeveloperVerify;