{"compileConfig": {"deviceType": "default", "buildMode": "debug", "compilerType": "ark", "note": "false", "logLevel": "3", "hapMode": "false", "img2bin": "true", "Path": "C:\\Program Files\\Huawei\\DevEco Studio\\tools\\node\\", "projectProfilePath": "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build-profile.json5", "localPropertiesPath": "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\local.properties", "appResource": "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt", "cachePath": "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\cache\\default\\default@CompileArkTS\\esmodule\\debug", "aceBuildJson": "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\loader.json", "aceModuleJsonPath": "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json", "aceSoPath": "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\nativeDependencies.txt", "aceModuleRoot": "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets", "aceModuleBuild": "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets", "aceProfilePath": "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", "aceSuperVisualPath": "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\supervisual", "watchMode": "false"}, "patchConfig": {"changedFileList": "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\patch\\default\\changedFileList.json"}}