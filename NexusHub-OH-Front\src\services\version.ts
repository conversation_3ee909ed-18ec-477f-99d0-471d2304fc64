/*
 * @Author: xiaoxiaozhou <EMAIL>
 * @Date: 2025-06-12 01:13:46
 * @LastEditors: xiaoxiaozhou <EMAIL>
 * @LastEditTime: 2025-06-12 01:41:37
 * @FilePath: \NexusHub-OH\NexusHub-OH-Front\src\services\version.ts
 * @Description: 
 * 技术生的工作日常
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
import { request } from '@umijs/max';

// 版本相关的类型定义
export interface AppVersion {
  id: number;
  application_id?: number;
  appId?: number;
  appName?: string;
  developerName?: string;
  versionName: string;
  versionCode: number;
  fileSize?: number;
  updateDescription?: string;
  status: 'pending' | 'approved' | 'rejected' | 'published';
  downloadUrl?: string;
  createdAt?: string;
  created_at?: string;
  updatedAt?: string;
  updated_at?: string;
  reviewComment?: string;
}

// 版本审核请求类型
export interface VersionReviewRequest {
  status: 'approved' | 'rejected';
  reason?: string;
}

// 版本相关的 API 接口

// 获取应用版本列表
export const getAppVersions = async (appId: string, params?: any) => {
  return request(`/apps/${appId}/versions`, {
    method: 'GET',
    params,
  });
};

// 获取版本详情
export const getAppVersionDetail = async (appId: string, versionId: string) => {
  return request(`/apps/${appId}/versions/${versionId}`, {
    method: 'GET',
  });
};

// 创建新版本
export const createAppVersion = async (appId: string, data: any) => {
  return request(`/apps/${appId}/versions`, {
    method: 'POST',
    data,
  });
};

// 更新版本
export const updateAppVersion = async (appId: string, versionId: string, data: any) => {
  return request(`/apps/${appId}/versions/${versionId}`, {
    method: 'PUT',
    data,
  });
};

// 删除版本
export const deleteAppVersion = async (appId: string, versionId: string) => {
  return request(`/apps/${appId}/versions/${versionId}`, {
    method: 'DELETE',
  });
};

// 获取待审核版本列表
export const getPendingVersions = async (params?: any) => {
  return request('/admin/versions/pending', {
    method: 'GET',
    params,
  });
};

// 审核版本
export const reviewAppVersion = async (versionId: string, data: VersionReviewRequest) => {
  return request(`/admin/versions/${versionId}/review`, {
    method: 'POST',
    data,
  });
};

// 发布版本
export const publishAppVersion = async (appId: string, versionId: string) => {
  return request(`/apps/${appId}/versions/${versionId}/publish`, {
    method: 'POST',
  });
};

// 下架版本
export const unpublishAppVersion = async (appId: string, versionId: string) => {
  return request(`/apps/${appId}/versions/${versionId}/unpublish`, {
    method: 'POST',
  });
};