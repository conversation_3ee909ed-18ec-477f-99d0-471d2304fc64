import { <PERSON><PERSON>ontainer } from '@ant-design/pro-components';
import { Card, Descriptions, Button, Tabs, Table, Tag, Avatar, Badge, Space, message, Modal, Spin } from 'antd';
import { UserOutlined, MailOutlined, PhoneOutlined, LockOutlined, UnlockOutlined, EditOutlined, RollbackOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { useRequest, history, useParams } from '@umijs/max';
import type { ColumnsType } from 'antd/es/table';

// 修改接口结构以匹配API返回格式
interface UserDetailData {
  id: number;
  username: string;
  nickname?: string;
  avatar: string;
  email: string;
  phone: string;
  role: 'admin' | 'user' | 'developer';
  status: 'active' | 'inactive' | 'banned';
  created_at: string;
  last_login_at: string;
  login_count: number;
  is_developer: boolean;
  developer_name?: string;
  company_name?: string;
  website?: string;
  description?: string;
  contact_email?: string;
  contact_phone?: string;
  business_license?: string;
  identity_card?: string;
  developer_avatar?: string;
  developer_address?: string;
  submitted_at?: string;
  verified_at?: string;
  verify_reason?: string;
  verify_status?: 'pending' | 'approved' | 'rejected';
}

interface UserDetailResponse {
  data: UserDetailData;
}

interface LoginRecord {
  id: string;
  loginTime: string;
  ip: string;
  device: string;
  location: string;
  status: 'success' | 'failed';
}

interface LoginRecordsResponse {
  data: LoginRecord[];
}

interface AppRecord {
  id: string;
  appName: string;
  downloadTime: string;
  version: string;
  status: 'installed' | 'uninstalled' | 'updated';
}

interface AppRecordsResponse {
  data: AppRecord[];
}

// 导入API函数
import { getUserDetail, getUserLoginRecords, getUserAppRecords, toggleUserLockStatus } from '@/services/user';

// 使用真实API函数替代模拟函数
const fetchUserDetail = async (id: string): Promise<UserDetailResponse> => {
  if (!id) {
    throw new Error('用户ID不能为空');
  }
  console.log('Fetching user detail with id:', id);
  try {
    return await getUserDetail(id);
  } catch (error) {
    console.error('获取用户详情失败:', error);
    throw error;
  }
};

const fetchLoginRecords = async (userId: string): Promise<LoginRecordsResponse> => {
  if (!userId) {
    throw new Error('用户ID不能为空');
  }
  console.log('Fetching login records for user:', userId);
  try {
    return await getUserLoginRecords(userId);
  } catch (error) {
    console.error('获取用户登录记录失败:', error);
    throw error;
  }
};

const fetchAppRecords = async (userId: string): Promise<AppRecordsResponse> => {
  if (!userId) {
    throw new Error('用户ID不能为空');
  }
  console.log('Fetching app records for user:', userId);
  try {
    return await getUserAppRecords(userId);
  } catch (error) {
    console.error('获取用户应用记录失败:', error);
    throw error;
  }
};

// 锁定或解锁用户
const toggleUserStatus = async (userId: string, action: 'lock' | 'unlock', reason?: string) => {
  if (!userId) {
    throw new Error('用户ID不能为空');
  }
  try {
    return await toggleUserLockStatus(userId, action, reason);
  } catch (error) {
    console.error(`${action === 'lock' ? '锁定' : '解锁'}用户失败:`, error);
    throw error;
  }
};

const UserDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [lockModalVisible, setLockModalVisible] = useState(false);
  const [lockReason, setLockReason] = useState('');
  const [lockLoading, setLockLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  
  // 确保 id 参数存在并且有效
  console.log('用户详情页面接收到的ID:', id);
  
  // 路由守卫：如果ID无效，重定向到列表页面
  React.useEffect(() => {
    if (!id) {
      message.error('无效的用户ID');
      history.push('/user-management/list');
    }
  }, [id]);
  
  const { data: userData, loading: userLoading, error: userError, refresh: refreshUserData } = useRequest(
    () => fetchUserDetail(id || ''), 
    {
      refreshDeps: [id],
      onError: (error) => {
        setErrorMessage(`获取用户详情失败: ${error.message}`);
        message.error('获取用户详情失败，请稍后重试');
      },
      onSuccess: (data) => {
        // 用户详情获取成功
      }
    }
  );

  const { data: loginRecords, loading: loginLoading, error: loginError } = useRequest(
    () => fetchLoginRecords(id || ''), 
    {
      refreshDeps: [id],
      onError: (error) => {
        console.log('获取登录记录错误:', error);
        message.error('获取登录记录失败，请稍后重试');
      }
    }
  );

  const { data: appRecords, loading: appLoading, error: appError } = useRequest(
    () => fetchAppRecords(id || ''), 
    {
      refreshDeps: [id],
      onError: (error) => {
        console.log('获取应用记录错误:', error);
        message.error('获取应用记录失败，请稍后重试');
      }
    }
  );

  const handleBack = () => {
    history.push('/user-management/list');
  };

  const handleEdit = () => {
    message.info(`编辑用户 ${id}`);
    // 实际项目中应该跳转到编辑页面或打开编辑弹窗
  };

  const handleLock = () => {
    setLockModalVisible(true);
  };

  const handleLockConfirm = async () => {
    if (!userData || !id) return;
    
    const action = userData.status === 'banned' ? 'unlock' : 'lock';
    const actionText = action === 'lock' ? '锁定' : '解锁';
    
    try {
      setLockLoading(true);
      await toggleUserStatus(id, action, action === 'lock' ? lockReason : undefined);
      message.success(`用户 ${userData.username} 已${actionText}`);
      setLockModalVisible(false);
      setLockReason('');
      // 刷新用户数据
      refreshUserData();
    } catch (error) {
      if (error instanceof Error) {
        message.error(`${actionText}用户失败: ${error.message}`);
      } else {
        message.error(`${actionText}用户失败`);
      }
    } finally {
      setLockLoading(false);
    }
  };

  const getRoleTag = (role: string) => {
    switch (role) {
      case 'admin':
        return <Tag color="red">管理员</Tag>;
      case 'developer':
        return <Tag color="blue">开发者</Tag>;
      case 'user':
        return <Tag color="green">普通用户</Tag>;
      default:
        return <Tag>{role}</Tag>;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge status="success" text="活跃" />;
      case 'inactive':
        return <Badge status="default" text="非活跃" />;
      case 'banned':
        return <Badge status="error" text="已锁定" />;
      default:
        return <Badge status="processing" text={status} />;
    }
  };

  const loginColumns: ColumnsType<LoginRecord> = [
    {
      title: '登录时间',
      dataIndex: 'loginTime',
      key: 'loginTime',
      sorter: (a, b) => (a.loginTime || '').localeCompare(b.loginTime || ''),
      defaultSortOrder: 'descend',
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
    },
    {
      title: '设备',
      dataIndex: 'device',
      key: 'device',
    },
    {
      title: '位置',
      dataIndex: 'location',
      key: 'location',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text: string) => {
        if (text === 'success') {
          return <Badge status="success" text="成功" />;
        }
        return <Badge status="error" text="失败" />;
      },
    },
  ];

  const appColumns: ColumnsType<AppRecord> = [
    {
      title: '应用名称',
      dataIndex: 'appName',
      key: 'appName',
    },
    {
      title: '下载时间',
      dataIndex: 'downloadTime',
      key: 'downloadTime',
      sorter: (a, b) => (a.downloadTime || '').localeCompare(b.downloadTime || ''),
      defaultSortOrder: 'descend',
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text: string) => {
        switch (text) {
          case 'installed':
            return <Badge status="success" text="已安装" />;
          case 'uninstalled':
            return <Badge status="default" text="已卸载" />;
          case 'updated':
            return <Badge status="processing" text="已更新" />;
          default:
            return <Badge status="default" text={text} />;
        }
      },
    },
  ];

  // 处理加载状态和错误状态
  if (userLoading) {
    return (
      <PageContainer>
        <Card loading variant="borderless">
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <Spin size="large" tip="加载用户数据中..." />
          </div>
        </Card>
      </PageContainer>
    );
  }

  // 处理错误状态
  if (errorMessage || userError) {
    return (
      <PageContainer
        header={{
          title: '用户详情',
          onBack: handleBack,
        }}
      >
        <Card variant="borderless">
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <h3 style={{ color: '#ff4d4f' }}>{errorMessage || '无法加载用户数据'}</h3>
            <p>请检查网络连接或稍后重试</p>
            <Button type="primary" onClick={handleBack}>返回用户列表</Button>
          </div>
        </Card>
      </PageContainer>
    );
  }

  // 安全地获取用户详情数据 - 修复：API返回的数据结构是response.data，而useRequest已经提取了data部分
  const userDetailData = userData;
  
  // 渲染用户详情页面
  
  // 如果数据还没加载完成，显示加载状态
   if (!userDetailData) {
     console.log('DEBUG: 进入 !userDetailData 分支，显示加载状态');
     return (
       <PageContainer header={{ title: '用户详情', onBack: handleBack }}>
         <div style={{ textAlign: 'center', padding: '50px' }}>
           <Spin size="large" spinning={true} tip="加载用户详情中...">
             <div style={{ minHeight: '200px' }} />
           </Spin>
         </div>
       </PageContainer>
     );
   }

  // 显示加载状态
  if (userLoading) {
    return (
      <PageContainer header={{ title: '用户详情', onBack: handleBack }}>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" spinning={true} tip="加载用户详情中...">
            <div style={{ minHeight: '200px' }} />
          </Spin>
        </div>
      </PageContainer>
    );
  }

  // 显示错误状态
  if (userError && !userData) {
    return (
      <PageContainer header={{ title: '用户详情', onBack: handleBack }}>
        <Card>
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <h3 style={{ color: '#ff4d4f', marginBottom: '20px' }}>获取用户详情失败</h3>
            <p>可能是网络问题或该用户不存在</p>
            <Button type="primary" onClick={() => window.location.reload()} style={{ marginTop: '20px' }}>
              重试
            </Button>
          </div>
        </Card>
      </PageContainer>
    );
  }

  return (
    <PageContainer
      header={{
        title: '用户详情',
        onBack: handleBack,
        extra: [
          <Button key="edit" type="primary" icon={<EditOutlined />} onClick={handleEdit}>编辑</Button>,
          <Button 
            key="lock" 
            danger={userDetailData.status !== 'banned'}
            icon={userDetailData.status === 'banned' ? <UnlockOutlined /> : <LockOutlined />}
            onClick={handleLock}
          >
            {userDetailData.status === 'banned' ? '解锁' : '锁定'}
          </Button>,
          <Button key="back" icon={<RollbackOutlined />} onClick={handleBack}>返回</Button>,
        ],
      }}
    >
      <Card loading={userLoading} variant="borderless">
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <Space align="start" size="large" style={{ width: '100%' }}>
            <Card variant="borderless">
              <Space direction="vertical" size="large" style={{ width: '100%', textAlign: 'center' }}>
                <Avatar 
                  size={120} 
                  src={userDetailData.avatar} 
                  icon={<UserOutlined />}
                />
                <div>
                  <h2>{userDetailData.nickname}</h2>
                  <p>@{userDetailData.username}</p>
                  <div>
                    {getRoleTag(userDetailData.role)}
                    {getStatusBadge(userDetailData.status)}
                  </div>
                </div>
                <div>
                  <Space>
                    <Button type="primary" shape="circle" icon={<UserOutlined />} />
                    <Button type="primary" shape="circle" icon={<MailOutlined />} />
                    <Button type="primary" shape="circle" icon={<PhoneOutlined />} />
                  </Space>
                </div>
              </Space>
            </Card>
            <Card style={{ flex: 1 }}>
              <Descriptions bordered column={2} size="small">
                <Descriptions.Item label="用户ID">{userDetailData.id}</Descriptions.Item>
                <Descriptions.Item label="状态">{getStatusBadge(userDetailData.status)}</Descriptions.Item>
                <Descriptions.Item label="用户名">{userDetailData.username}</Descriptions.Item>
                <Descriptions.Item label="昵称">{userDetailData.nickname || userDetailData.username}</Descriptions.Item>
                <Descriptions.Item label="邮箱">{userDetailData.email}</Descriptions.Item>
                <Descriptions.Item label="手机">{userDetailData.phone || '未设置'}</Descriptions.Item>
                <Descriptions.Item label="注册时间">{new Date(userDetailData.created_at).toLocaleString()}</Descriptions.Item>
                <Descriptions.Item label="最后登录">{new Date(userDetailData.last_login_at).toLocaleString()}</Descriptions.Item>
                <Descriptions.Item label="登录次数">{userDetailData.login_count}</Descriptions.Item>
                <Descriptions.Item label="是否开发者">{userDetailData.is_developer ? '是' : '否'}</Descriptions.Item>
                <Descriptions.Item label="个人简介" span={2}>
                  {userDetailData.description || '暂无简介'}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Space>

          {userDetailData.is_developer && (
            <Card title="开发者信息" variant="borderless">
              <Descriptions bordered column={2}>
                <Descriptions.Item label="开发者名称">{userDetailData.developer_name || '未设置'}</Descriptions.Item>
                <Descriptions.Item label="认证状态">
                  {userDetailData.verify_status === 'approved' ? 
                    <Badge status="success" text="已认证" /> : 
                    userDetailData.verify_status === 'pending' ?
                    <Badge status="processing" text="审核中" /> :
                    <Badge status="error" text="未通过" />
                  }
                </Descriptions.Item>
                <Descriptions.Item label="公司名称">{userDetailData.company_name || '未设置'}</Descriptions.Item>
                <Descriptions.Item label="网站">{userDetailData.website || '未设置'}</Descriptions.Item>
                <Descriptions.Item label="联系邮箱">{userDetailData.contact_email || '未设置'}</Descriptions.Item>
                <Descriptions.Item label="联系电话">{userDetailData.contact_phone || '未设置'}</Descriptions.Item>
                <Descriptions.Item label="地址">{userDetailData.developer_address || '未设置'}</Descriptions.Item>
                {userDetailData.submitted_at && (
                  <Descriptions.Item label="提交时间">{new Date(userDetailData.submitted_at).toLocaleString()}</Descriptions.Item>
                )}
                {userDetailData.verified_at && (
                  <Descriptions.Item label="认证时间">{new Date(userDetailData.verified_at).toLocaleString()}</Descriptions.Item>
                )}
                {userDetailData.verify_reason && (
                  <Descriptions.Item label="审核意见" span={2}>{userDetailData.verify_reason}</Descriptions.Item>
                )}
              </Descriptions>
            </Card>
          )}

          <Card variant="borderless">
            <Tabs defaultActiveKey="login">
              <Tabs.TabPane tab="登录记录" key="login">
                <Table 
                  columns={loginColumns} 
                  dataSource={loginRecords?.data} 
                  rowKey="id" 
                  loading={loginLoading} 
                />
              </Tabs.TabPane>
              <Tabs.TabPane tab="应用记录" key="app">
                <Table 
                  columns={appColumns} 
                  dataSource={appRecords?.data} 
                  rowKey="id" 
                  loading={appLoading} 
                />
              </Tabs.TabPane>
            </Tabs>
          </Card>
        </Space>
      </Card>

      <Modal
        title={userDetailData.status === 'banned' ? '解锁用户' : '锁定用户'}
        open={lockModalVisible}
        onOk={handleLockConfirm}
        onCancel={() => setLockModalVisible(false)}
        confirmLoading={lockLoading}
        okText={lockLoading ? '处理中...' : '确认'}
        cancelButtonProps={{ disabled: lockLoading }}
      >
        {userDetailData.status !== 'banned' && (
          <>
            <p>确定要锁定用户 <strong>{userDetailData.username}</strong> 吗？</p>
            <p>锁定原因：</p>
            <textarea 
              rows={4} 
              style={{ width: '100%' }} 
              value={lockReason}
              onChange={(e) => setLockReason(e.target.value)}
              disabled={lockLoading}
              placeholder="请输入锁定用户的原因"
            />
          </>
        )}
        {userDetailData.status === 'banned' && (
          <p>确定要解锁用户 <strong>{userDetailData.username}</strong> 吗？</p>
        )}
      </Modal>
    </PageContainer>
  );
};

export default UserDetail;