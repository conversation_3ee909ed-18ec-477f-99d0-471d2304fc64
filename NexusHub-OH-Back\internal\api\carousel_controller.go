package api

import (
	"net/http"
	"strconv"

	"nexushub-oh-back/internal/models"
	"nexushub-oh-back/internal/services"
	"nexushub-oh-back/pkg/response"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// CarouselController 轮播图控制器
type CarouselController struct {
	carouselService *services.CarouselService
}

// NewCarouselController 创建轮播图控制器实例
func NewCarouselController(db *gorm.DB) *CarouselController {
	return &CarouselController{
		carouselService: services.NewCarouselService(db),
	}
}

// CreateCarousel 创建轮播图
// @Summary 创建轮播图
// @Description 管理员创建新的轮播图
// @Tags 轮播图管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param carousel body models.CarouselCreateRequest true "轮播图信息"
// @Success 201 {object} response.Response{data=models.CarouselResponse} "创建成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 403 {object} response.Response "权限不足"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/admin/carousels [post]
func (c *CarouselController) CreateCarousel(ctx *gin.Context) {
	var req models.CarouselCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, response.Response{
			Code:    http.StatusBadRequest,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, response.Response{
			Code:    http.StatusUnauthorized,
			Message: "未授权",
		})
		return
	}

	carousel, err := c.carouselService.CreateCarousel(&req, userID.(uint))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response.Response{
			Code:    http.StatusInternalServerError,
			Message: err.Error(),
		})
		return
	}

	// 获取完整信息
	carouselDetail, err := c.carouselService.GetCarouselByID(carousel.ID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response.Response{
			Code:    http.StatusInternalServerError,
			Message: "获取轮播图详情失败",
		})
		return
	}

	ctx.JSON(http.StatusCreated, response.Response{
		Code:    http.StatusCreated,
		Message: "创建成功",
		Data:    c.convertToResponse(carouselDetail),
	})
}

// GetCarouselList 获取轮播图列表
// @Summary 获取轮播图列表
// @Description 管理员获取轮播图列表
// @Tags 轮播图管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param status query string false "状态" Enums(active,inactive,draft)
// @Param type query string false "类型" Enums(app,collection,external,announcement)
// @Param keyword query string false "关键词搜索"
// @Success 200 {object} response.Response{data=models.CarouselListResponse} "获取成功"
// @Failure 401 {object} response.Response "未授权"
// @Failure 403 {object} response.Response "权限不足"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/admin/carousels [get]
func (c *CarouselController) GetCarouselList(ctx *gin.Context) {
	var req models.CarouselListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, response.Response{
			Code:    http.StatusBadRequest,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	result, err := c.carouselService.GetCarouselList(&req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response.Response{
			Code:    http.StatusInternalServerError,
			Message: err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, response.Response{
		Code:    http.StatusOK,
		Message: "获取成功",
		Data:    result,
	})
}

// GetCarousel 获取轮播图详情
// @Summary 获取轮播图详情
// @Description 管理员获取轮播图详情
// @Tags 轮播图管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "轮播图ID"
// @Success 200 {object} response.Response{data=models.CarouselResponse} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 403 {object} response.Response "权限不足"
// @Failure 404 {object} response.Response "轮播图不存在"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/admin/carousels/{id} [get]
func (c *CarouselController) GetCarousel(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, response.Response{
			Code:    http.StatusBadRequest,
			Message: "无效的轮播图ID",
		})
		return
	}

	carousel, err := c.carouselService.GetCarouselByID(uint(id))
	if err != nil {
		if err.Error() == "轮播图不存在" {
			ctx.JSON(http.StatusNotFound, response.Response{
				Code:    http.StatusNotFound,
				Message: err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, response.Response{
				Code:    http.StatusInternalServerError,
				Message: err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, response.Response{
		Code:    http.StatusOK,
		Message: "获取成功",
		Data:    c.convertToResponse(carousel),
	})
}

// UpdateCarousel 更新轮播图
// @Summary 更新轮播图
// @Description 管理员更新轮播图信息
// @Tags 轮播图管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "轮播图ID"
// @Param carousel body models.CarouselUpdateRequest true "轮播图信息"
// @Success 200 {object} response.Response{data=models.CarouselResponse} "更新成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 403 {object} response.Response "权限不足"
// @Failure 404 {object} response.Response "轮播图不存在"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/admin/carousels/{id} [put]
func (c *CarouselController) UpdateCarousel(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, response.Response{
			Code:    http.StatusBadRequest,
			Message: "无效的轮播图ID",
		})
		return
	}

	var req models.CarouselUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, response.Response{
			Code:    http.StatusBadRequest,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, response.Response{
			Code:    http.StatusUnauthorized,
			Message: "未授权",
		})
		return
	}

	carousel, err := c.carouselService.UpdateCarousel(uint(id), &req, userID.(uint))
	if err != nil {
		if err.Error() == "轮播图不存在" {
			ctx.JSON(http.StatusNotFound, response.Response{
				Code:    http.StatusNotFound,
				Message: err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, response.Response{
				Code:    http.StatusInternalServerError,
				Message: err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, response.Response{
		Code:    http.StatusOK,
		Message: "更新成功",
		Data:    c.convertToResponse(carousel),
	})
}

// DeleteCarousel 删除轮播图
// @Summary 删除轮播图
// @Description 管理员删除轮播图
// @Tags 轮播图管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "轮播图ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 403 {object} response.Response "权限不足"
// @Failure 404 {object} response.Response "轮播图不存在"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/admin/carousels/{id} [delete]
func (c *CarouselController) DeleteCarousel(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, response.Response{
			Code:    http.StatusBadRequest,
			Message: "无效的轮播图ID",
		})
		return
	}

	err = c.carouselService.DeleteCarousel(uint(id))
	if err != nil {
		if err.Error() == "轮播图不存在" {
			ctx.JSON(http.StatusNotFound, response.Response{
				Code:    http.StatusNotFound,
				Message: err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, response.Response{
				Code:    http.StatusInternalServerError,
				Message: err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, response.Response{
		Code:    http.StatusOK,
		Message: "删除成功",
	})
}

// UpdateCarouselSortOrder 批量更新轮播图排序
// @Summary 批量更新轮播图排序
// @Description 管理员批量更新轮播图排序
// @Tags 轮播图管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param updates body models.CarouselSortRequest true "排序更新列表"
// @Success 200 {object} response.Response "更新成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 403 {object} response.Response "权限不足"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/admin/carousels/sort [put]
func (c *CarouselController) UpdateCarouselSortOrder(ctx *gin.Context) {
	var updates models.CarouselSortRequest

	if err := ctx.ShouldBindJSON(&updates); err != nil {
		ctx.JSON(http.StatusBadRequest, response.Response{
			Code:    http.StatusBadRequest,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, response.Response{
			Code:    http.StatusUnauthorized,
			Message: "未授权",
		})
		return
	}

	err := c.carouselService.UpdateSortOrder(updates, userID.(uint))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response.Response{
			Code:    http.StatusInternalServerError,
			Message: err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, response.Response{
		Code:    http.StatusOK,
		Message: "更新成功",
	})
}

// GetActiveCarousels 获取激活的轮播图列表（公开接口）
// @Summary 获取激活的轮播图列表
// @Description 获取当前激活且在有效期内的轮播图列表
// @Tags 轮播图
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]models.CarouselResponse} "获取成功"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/public/carousels [get]
func (c *CarouselController) GetActiveCarousels(ctx *gin.Context) {
	carousels, err := c.carouselService.GetActiveCarousels()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response.Response{
			Code:    http.StatusInternalServerError,
			Message: err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, response.Response{
		Code:    http.StatusOK,
		Message: "获取成功",
		Data:    carousels,
	})
}

// ClickCarousel 轮播图点击统计
// @Summary 轮播图点击统计
// @Description 记录轮播图点击次数
// @Tags 轮播图
// @Accept json
// @Produce json
// @Param click body models.CarouselClickRequest true "点击信息"
// @Success 200 {object} response.Response "记录成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "轮播图不存在"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/public/carousels/click [post]
func (c *CarouselController) ClickCarousel(ctx *gin.Context) {
	var req models.CarouselClickRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, response.Response{
			Code:    http.StatusBadRequest,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	err := c.carouselService.IncrementClickCount(req.CarouselID)
	if err != nil {
		if err.Error() == "轮播图不存在" {
			ctx.JSON(http.StatusNotFound, response.Response{
				Code:    http.StatusNotFound,
				Message: err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, response.Response{
				Code:    http.StatusInternalServerError,
				Message: err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, response.Response{
		Code:    http.StatusOK,
		Message: "记录成功",
	})
}

// convertToResponse 转换为响应格式
func (c *CarouselController) convertToResponse(carousel *models.Carousel) models.CarouselResponse {
	response := models.CarouselResponse{
		ID:         carousel.ID,
		Title:      carousel.Title,
		Subtitle:   carousel.Subtitle,
		ImageURL:   carousel.ImageURL,
		Type:       carousel.Type,
		TargetID:   carousel.TargetID,
		TargetURL:  carousel.TargetURL,
		Status:     carousel.Status,
		SortOrder:  carousel.SortOrder,
		StartTime:  carousel.StartTime,
		EndTime:    carousel.EndTime,
		ClickCount: carousel.ClickCount,
		CreatedBy:  carousel.CreatedBy,
		UpdatedBy:  carousel.UpdatedBy,
		CreatedAt:  carousel.CreatedAt,
		UpdatedAt:  carousel.UpdatedAt,
	}

	// 设置创建者和更新者名称
	if carousel.Creator.ID != 0 {
		response.CreatorName = carousel.Creator.Username
	}
	if carousel.Updater.ID != 0 {
		response.UpdaterName = carousel.Updater.Username
	}

	// 设置目标信息
	switch carousel.Type {
	case models.CarouselTypeApp:
		if carousel.TargetApp != nil {
			response.TargetInfo = map[string]interface{}{
				"id":          carousel.TargetApp.ID,
				"name":        carousel.TargetApp.Name,
				"icon":        carousel.TargetApp.Icon,
				"description": carousel.TargetApp.Description,
			}
		}
	case models.CarouselTypeCollection:
		if carousel.TargetCollection != nil {
			response.TargetInfo = map[string]interface{}{
				"id":          carousel.TargetCollection.ID,
				"name":        carousel.TargetCollection.Title,
				"description": carousel.TargetCollection.Description,
				"cover_image": carousel.TargetCollection.CoverImage,
			}
		}
	}

	return response
}
