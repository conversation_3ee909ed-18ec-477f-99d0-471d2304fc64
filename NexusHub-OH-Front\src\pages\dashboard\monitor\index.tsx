import { Gauge, Liquid, WordCloud } from '@ant-design/plots';
import { GridContent } from '@ant-design/pro-components';
import { Card, Col, Progress, Row, Statistic, Table } from 'antd';
import numeral from 'numeral';
import type { FC } from 'react';
import { useEffect, useState } from 'react';
import ActiveChart from './components/ActiveChart';
import Map from './components/Map';
import { getMonitoringData, getMonitoringLogs, getMonitoringAlerts } from './service';
import useStyles from './style.style';

const { Countdown } = Statistic;
const deadline = Date.now() + 1000 * 60 * 60 * 24 * 2 + 1000 * 30;

const Monitor: FC = () => {
  const { styles } = useStyles();
  const [monitoringData, setMonitoringData] = useState<any>({});
  const [logs, setLogs] = useState<any[]>([]);
  const [alerts, setAlerts] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const [dataResult, logsResult, alertsResult] = await Promise.all([
          getMonitoringData(),
          getMonitoringLogs(),
          getMonitoringAlerts(),
        ]);
        
        setMonitoringData(dataResult.data || {});
        setLogs(logsResult.data || []);
        setAlerts(alertsResult.data || []);
      } catch (error) {
        console.error('获取监控数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // 将监控数据转换为词云数据格式
  const wordCloudData = Object.entries(monitoringData.tags || {}).map(([key, value]) => ({
    id: key,
    word: key,
    weight: value,
  }));

  // 日志列配置
  const logColumns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
    },
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
    },
    {
      title: '消息',
      dataIndex: 'message',
      key: 'message',
    },
  ];

  return (
    <GridContent>
      <>
        <Row gutter={24}>
          <Col
            xl={18}
            lg={24}
            md={24}
            sm={24}
            xs={24}
            style={{
              marginBottom: 24,
            }}
          >
            <Card title="系统资源监控" bordered={false} loading={loading}>
              <Row>
                <Col md={6} sm={12} xs={24}>
                  <Statistic
                    title="CPU 使用率"
                    suffix="%"
                    value={monitoringData.cpu_usage || 0}
                    precision={2}
                  />
                </Col>
                <Col md={6} sm={12} xs={24}>
                  <Statistic 
                    title="内存使用率" 
                    suffix="%" 
                    value={monitoringData.memory_usage || 0}
                    precision={2}
                  />
                </Col>
                <Col md={6} sm={12} xs={24}>
                  <Statistic 
                    title="磁盘使用率" 
                    suffix="%" 
                    value={monitoringData.disk_usage || 0}
                    precision={2}
                  />
                </Col>
                <Col md={6} sm={12} xs={24}>
                  <Statistic 
                    title="网络带宽" 
                    suffix="Mbps" 
                    value={monitoringData.network_bandwidth || 0}
                    precision={2}
                  />
                </Col>
              </Row>
              <div className={styles.mapChart}>
                <Map />
              </div>
            </Card>
          </Col>
          <Col xl={6} lg={24} md={24} sm={24} xs={24}>
            <Card
              title="系统健康状态"
              style={{
                marginBottom: 24,
              }}
              bordered={false}
              loading={loading}
            >
              <ActiveChart />
            </Card>
            <Card
              title="系统响应时间"
              style={{
                marginBottom: 24,
              }}
              bodyStyle={{
                textAlign: 'center',
              }}
              bordered={false}
              loading={loading}
            >
              <Gauge
                height={180}
                data={
                  {
                    target: 100,
                    total: 200,
                    name: '响应时间',
                    value: monitoringData.response_time || 0,
                    thresholds: [20, 50, 100, 150, 200],
                  } as any
                }
                padding={-16}
                meta={{
                  color: {
                    range: ['#30BF78', '#FAAD14', '#F4664A', '#FF0000'],
                  },
                }}
              />
            </Card>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col
            xl={12}
            lg={24}
            sm={24}
            xs={24}
            style={{
              marginBottom: 24,
            }}
          >
            <Card title="系统日志" bordered={false} loading={loading}>
              <Table
                dataSource={logs}
                columns={logColumns}
                pagination={{ pageSize: 5 }}
                size="small"
              />
            </Card>
          </Col>
          <Col
            xl={6}
            lg={12}
            sm={24}
            xs={24}
            style={{
              marginBottom: 24,
            }}
          >
            <Card
              title="告警事件"
              loading={loading}
              bordered={false}
              bodyStyle={{
                overflow: 'hidden',
              }}
            >
              <WordCloud
                data={wordCloudData}
                height={162}
                textField="word"
                weightField="weight"
                colorField="word"
                layout={{ spiral: 'rectangular', fontSize: [10, 20] }}
              />
            </Card>
          </Col>
          <Col
            xl={6}
            lg={12}
            sm={24}
            xs={24}
            style={{
              marginBottom: 24,
            }}
          >
            <Card
              title="资源剩余"
              bodyStyle={{
                textAlign: 'center',
                fontSize: 0,
              }}
              bordered={false}
              loading={loading}
            >
              <Liquid 
                height={160} 
                percent={(100 - (monitoringData.disk_usage || 0)) / 100} 
              />
            </Card>
          </Col>
        </Row>
      </>
    </GridContent>
  );
};
export default Monitor;
