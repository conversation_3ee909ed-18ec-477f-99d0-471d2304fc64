import { Request, Response } from 'express';
import moment from 'moment';
import { parse } from 'url';

// 生成mock数据
const genCategories = (count: number) => {
  const categories = [];
  const now = moment().format('YYYY-MM-DD HH:mm:ss');
  
  const baseCategories = [
    { name: '游戏', description: '游戏应用分类', icon: 'game-icon.png', is_active: true },
    { name: '工具', description: '实用工具应用', icon: 'tool-icon.png', is_active: true },
    { name: '社交', description: '社交媒体应用', icon: 'social-icon.png', is_active: true },
    { name: '教育', description: '教育学习应用', icon: 'edu-icon.png', is_active: false },
    { name: '办公', description: '办公应用', icon: 'office-icon.png', is_active: true },
  ];
  
  for (let i = 0; i < count; i++) {
    const index = i % baseCategories.length;
    categories.push({
      id: i + 1,
      name: baseCategories[index].name + (i >= baseCategories.length ? ` ${Math.floor(i / baseCategories.length) + 1}` : ''),
      description: baseCategories[index].description,
      icon: baseCategories[index].icon,
      parent_id: i >= baseCategories.length ? (i % baseCategories.length) + 1 : null,
      sort_order: i,
      is_active: baseCategories[index].is_active,
      created_at: now,
      updated_at: now,
    });
  }
  
  return categories;
};

const categories = genCategories(20);
const rootCategories = categories.filter(item => item.parent_id === null);

export default {
  'GET categories': (req: Request, res: Response) => {
    const { query } = parse(req.url, true);
    const { include_inactive } = query;
    
    let result = [...categories];
    
    // 如果不包含未激活的分类
    if (include_inactive === 'false') {
      result = result.filter(item => item.is_active);
    }
    
    res.json(result);
  },
  
  'GET categories/root': (req: Request, res: Response) => {
    const { query } = parse(req.url, true);
    const { include_inactive } = query;
    
    let result = [...rootCategories];
    
    // 如果不包含未激活的分类
    if (include_inactive === 'false') {
      result = result.filter(item => item.is_active);
    }
    
    res.json(result);
  },
  
  'GET categories/:id': (req: Request, res: Response) => {
    const { id } = req.params;
    const category = categories.find(item => item.id === parseInt(id, 10));
    
    if (category) {
      const subcategories = categories.filter(item => item.parent_id === category.id);
      res.json({
        category,
        subcategories,
      });
    } else {
      res.status(404).json({
        code: 404,
        message: '分类不存在',
      });
    }
  },
  
  'POST categories': (req: Request, res: Response) => {
    const newCategory = {
      id: categories.length + 1,
      ...req.body,
      created_at: moment().format('YYYY-MM-DD HH:mm:ss'),
      updated_at: moment().format('YYYY-MM-DD HH:mm:ss'),
    };
    
    categories.push(newCategory);
    
    if (newCategory.parent_id === null) {
      rootCategories.push(newCategory);
    }
    
    res.json(newCategory);
  },
  
  'PUT categories/:id': (req: Request, res: Response) => {
    const { id } = req.params;
    const categoryIndex = categories.findIndex(item => item.id === parseInt(id, 10));
    
    if (categoryIndex >= 0) {
      const updatedCategory = {
        ...categories[categoryIndex],
        ...req.body,
        updated_at: moment().format('YYYY-MM-DD HH:mm:ss'),
      };
      
      categories[categoryIndex] = updatedCategory;
      
      const rootIndex = rootCategories.findIndex(item => item.id === parseInt(id, 10));
      if (rootIndex >= 0 && updatedCategory.parent_id !== null) {
        // 如果更新后不再是根分类，从根分类列表中移除
        rootCategories.splice(rootIndex, 1);
      } else if (rootIndex === -1 && updatedCategory.parent_id === null) {
        // 如果更新后变成根分类，添加到根分类列表
        rootCategories.push(updatedCategory);
      }
      
      res.json(updatedCategory);
    } else {
      res.status(404).json({
        code: 404,
        message: '分类不存在',
      });
    }
  },
  
  'DELETE categories/:id': (req: Request, res: Response) => {
    const { id } = req.params;
    const categoryIndex = categories.findIndex(item => item.id === parseInt(id, 10));
    
    if (categoryIndex >= 0) {
      categories.splice(categoryIndex, 1);
      
      const rootIndex = rootCategories.findIndex(item => item.id === parseInt(id, 10));
      if (rootIndex >= 0) {
        rootCategories.splice(rootIndex, 1);
      }
      
      res.json({
        code: 200,
        message: '删除成功',
      });
    } else {
      res.status(404).json({
        code: 404,
        message: '分类不存在',
      });
    }
  },
}; 