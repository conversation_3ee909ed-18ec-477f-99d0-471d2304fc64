import { PlusOutlined } from '@ant-design/icons';
import { ProTable, ProColumns } from '@ant-design/pro-components';
import { Button, message, Modal, Space, Form, Input, InputNumber, Switch, Tree, TreeDataNode } from 'antd';
import type { DataNode } from 'antd/es/tree';
import { useState, useEffect } from 'react';
import { 
  getCategories, 
  getCategoriesRoot, 
  postCategories, 
  putCategoriesId, 
  deleteCategoriesId 
} from '@/services/ant-design-pro/fenleiguanli';

// 使用API.CategoryResponse作为基础，但确保id等必要字段为必选
type CategoryItem = {
  id: number;
  name: string;
  description?: string;
  icon?: string;
  parent_id?: number;
  sort_order?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  children?: CategoryItem[];
};

const CategoryManagement = () => {
  const [categories, setCategories] = useState<CategoryItem[]>([]);
  const [rootCategories, setRootCategories] = useState<CategoryItem[]>([]);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [categoryForm] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [currentCategory, setCurrentCategory] = useState<CategoryItem | null>(null);
  const [treeModalVisible, setTreeModalVisible] = useState<boolean>(false);
  const [selectedParentId, setSelectedParentId] = useState<number | null>(null);

  // 获取所有分类
  const fetchCategories = async () => {
    try {
      const response = await getCategories({});
      // 转换数据以确保类型安全
      const safeData: CategoryItem[] = (response || []).map(item => ({
        id: item.id || 0,
        name: item.name || '',
        description: item.description,
        icon: item.icon,
        parent_id: item.parent_id,
        sort_order: item.sort_order,
        is_active: item.is_active || false,
        created_at: item.created_at || '',
        updated_at: item.updated_at || '',
      }));
      setCategories(safeData);
    } catch (error) {
      message.error('获取分类列表失败');
    }
  };

  // 获取根分类
  const fetchRootCategories = async () => {
    try {
      const response = await getCategoriesRoot({});
      // 转换数据以确保类型安全
      const safeData: CategoryItem[] = (response || []).map(item => ({
        id: item.id || 0,
        name: item.name || '',
        description: item.description,
        icon: item.icon,
        parent_id: item.parent_id,
        sort_order: item.sort_order,
        is_active: item.is_active || false,
        created_at: item.created_at || '',
        updated_at: item.updated_at || '',
      }));
      setRootCategories(safeData);
    } catch (error) {
      message.error('获取根分类失败');
    }
  };

  useEffect(() => {
    fetchCategories();
    fetchRootCategories();
  }, []);

  // 处理创建或更新分类
  const handleSaveCategory = async () => {
    try {
      const values = await categoryForm.validateFields();
      setConfirmLoading(true);
      
      if (currentCategory) {
        // 更新分类
        await putCategoriesId(
          { id: currentCategory.id },
          {
            ...values,
            parent_id: selectedParentId,
          }
        );
        message.success('更新分类成功');
      } else {
        // 创建分类
        await postCategories({
          ...values,
          parent_id: selectedParentId,
        });
        message.success('创建分类成功');
      }
      setModalVisible(false);
      fetchCategories();
      fetchRootCategories();
    } catch (error) {
      message.error('操作失败，请检查输入');
    } finally {
      setConfirmLoading(false);
    }
  };

  // 处理删除分类
  const handleDeleteCategory = async (id: number) => {
    Modal.confirm({
      title: '确认删除',
      content: '删除分类将会影响关联的应用，确定要删除吗？',
      onOk: async () => {
        try {
          await deleteCategoriesId({ id });
          message.success('删除成功');
          fetchCategories();
          fetchRootCategories();
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  // 打开分类模态框
  const openCategoryModal = (category?: CategoryItem) => {
    categoryForm.resetFields();
    if (category) {
      setCurrentCategory(category);
      setSelectedParentId(category.parent_id || null);
      categoryForm.setFieldsValue({
        name: category.name,
        description: category.description,
        icon: category.icon,
        sort_order: category.sort_order,
        is_active: category.is_active,
      });
    } else {
      setCurrentCategory(null);
      setSelectedParentId(null);
    }
    setModalVisible(true);
  };

  // 递归构建分类树数据
  const buildTreeData = (data: CategoryItem[]): DataNode[] => {
    return data.map((item) => ({
      title: item.name,
      key: item.id,
      children: item.children ? buildTreeData(item.children) : undefined,
    }));
  };

  // 树形选择弹窗
  const showParentSelectModal = () => {
    setTreeModalVisible(true);
  };

  // 处理选择父分类
  const handleSelectParent = (selectedKeys: React.Key[]) => {
    if (selectedKeys.length > 0) {
      setSelectedParentId(selectedKeys[0] as number);
    } else {
      setSelectedParentId(null);
    }
    setTreeModalVisible(false);
  };

  const columns: ProColumns<CategoryItem>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '排序',
      dataIndex: 'sort_order',
      key: 'sort_order',
      width: 100,
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 100,
      render: (_, record) => (record.is_active ? '启用' : '禁用'),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      render: (_, record) => (
        <Space size="middle">
          <a onClick={() => openCategoryModal(record)}>编辑</a>
          <a onClick={() => handleDeleteCategory(record.id)}>删除</a>
        </Space>
      ),
    },
  ];

  const rootNode: DataNode = {
    title: '根分类',
    key: 'root',
  };

  return (
    <div>
      <ProTable<CategoryItem>
        headerTitle="分类管理"
        rowKey="id"
        search={false}
        dataSource={categories}
        columns={columns}
        pagination={{
          showQuickJumper: true,
        }}
        toolBarRender={() => [
          <Button
            key="button"
            icon={<PlusOutlined />}
            type="primary"
            onClick={() => openCategoryModal()}
          >
            新建分类
          </Button>,
        ]}
      />

      {/* 分类表单弹窗 */}
      <Modal
        title={currentCategory ? '编辑分类' : '新建分类'}
        open={modalVisible}
        onOk={handleSaveCategory}
        confirmLoading={confirmLoading}
        onCancel={() => setModalVisible(false)}
      >
        <Form
          form={categoryForm}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="分类名称"
            rules={[{ required: true, message: '请输入分类名称' }]}
          >
            <Input placeholder="请输入分类名称" />
          </Form.Item>
          <Form.Item
            name="description"
            label="分类描述"
          >
            <Input.TextArea rows={4} placeholder="请输入分类描述" />
          </Form.Item>
          <Form.Item
            name="icon"
            label="图标URL"
          >
            <Input placeholder="请输入图标URL" />
          </Form.Item>
          <Form.Item label="父分类">
            <Button onClick={showParentSelectModal}>
              {selectedParentId ? `已选择ID: ${selectedParentId}` : '选择父分类'}
            </Button>
          </Form.Item>
          <Form.Item
            name="sort_order"
            label="排序权重"
            initialValue={0}
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            name="is_active"
            label="状态"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>
        </Form>
      </Modal>

      {/* 父分类选择弹窗 */}
      <Modal
        title="选择父分类"
        open={treeModalVisible}
        onOk={() => setTreeModalVisible(false)}
        onCancel={() => setTreeModalVisible(false)}
        footer={[
          <Button key="clear" onClick={() => setSelectedParentId(null)}>
            清除选择
          </Button>,
          <Button key="cancel" onClick={() => setTreeModalVisible(false)}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={() => setTreeModalVisible(false)}>
            确定
          </Button>,
        ]}
      >
        <Tree
          treeData={[rootNode, ...buildTreeData(rootCategories)]}
          defaultExpandAll
          onSelect={handleSelectParent}
          selectedKeys={selectedParentId ? [selectedParentId] : []}
        />
      </Modal>
    </div>
  );
};

export default CategoryManagement; 