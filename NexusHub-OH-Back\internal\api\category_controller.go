package api

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"nexushub-oh-back/internal/models"
	"nexushub-oh-back/pkg/response"
)

// CategoryController 分类控制器
type CategoryController struct {
	db *gorm.DB
}

// NewCategoryController 创建分类控制器
func NewCategoryController(db *gorm.DB) *CategoryController {
	return &CategoryController{db: db}
}

// CategoryResponse 分类响应结构体
//
//	@Description	分类信息响应
type CategoryResponse struct {
	ID          uint       `json:"id"`          // 分类ID
	CreatedAt   time.Time  `json:"created_at"`  // 创建时间
	UpdatedAt   time.Time  `json:"updated_at"`  // 更新时间
	DeletedAt   *time.Time `json:"deleted_at"`  // 删除时间
	Name        string     `json:"name"`        // 分类名称
	Description string     `json:"description"` // 分类描述
	Icon        string     `json:"icon"`        // 分类图标URL
	SortOrder   int        `json:"sort_order"`  // 排序权重
	IsActive    bool       `json:"is_active"`   // 是否启用
	ParentID    *uint      `json:"parent_id"`   // 父分类ID
}

// CreateCategoryRequest 创建分类请求
type CreateCategoryRequest struct {
	Name        string `json:"name" binding:"required,max=50"`
	Description string `json:"description" binding:"max=1000"`
	Icon        string `json:"icon" binding:"max=255"`
	SortOrder   int    `json:"sort_order"`
	ParentID    *uint  `json:"parent_id"`
}

// UpdateCategoryRequest 更新分类请求
type UpdateCategoryRequest struct {
	Name        string `json:"name" binding:"max=50"`
	Description string `json:"description" binding:"max=1000"`
	Icon        string `json:"icon" binding:"max=255"`
	SortOrder   int    `json:"sort_order"`
	IsActive    *bool  `json:"is_active"`
	ParentID    *uint  `json:"parent_id"`
}

// CategoryDetailResponse 分类详情响应
//
//	@Description	分类详情与子分类
type CategoryDetailResponse struct {
	Category      CategoryResponse   `json:"category"`      // 分类信息
	Subcategories []CategoryResponse `json:"subcategories"` // 子分类列表
}

// CreateCategory 创建分类
//
//	@Summary		创建应用分类
//	@Description	创建一个新的应用分类
//	@Tags			分类管理
//	@Accept			json
//	@Produce		json
//	@Param			Authorization	header		string					true	"Bearer 用户令牌"
//	@Param			category		body		CreateCategoryRequest	true	"分类信息"
//	@Success		201				{object}	CategoryResponse		"创建成功"
//	@Failure		400				{object}	response.ErrorResponse			"请求参数错误"
//	@Failure		401				{object}	response.ErrorResponse			"未授权"
//	@Failure		403				{object}	response.ErrorResponse			"权限不足"
//	@Failure		500				{object}	response.ErrorResponse			"内部服务器错误"
//	@Router			/categories [post]
func (c *CategoryController) CreateCategory(ctx *gin.Context) {
	var req CreateCategoryRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, "无效的请求参数")
		return
	}

	// 检查分类名称是否已存在
	existingCategory, err := models.GetCategoryByName(c.db, req.Name)
	if err == nil && existingCategory.ID > 0 {
		response.BadRequest(ctx, "分类名称已存在")
		return
	}

	// 如果指定了父分类，检查父分类是否存在
	if req.ParentID != nil && *req.ParentID > 0 {
		parentCategory, err := models.GetCategoryByID(c.db, *req.ParentID)
		if err != nil || parentCategory.ID == 0 {
			response.BadRequest(ctx, "父分类不存在")
			return
		}
	}

	category := models.Category{
		Name:        req.Name,
		Description: req.Description,
		Icon:        req.Icon,
		SortOrder:   req.SortOrder,
		ParentID:    req.ParentID,
		IsActive:    true,
	}

	if err := models.CreateCategory(c.db, &category); err != nil {
		response.InternalServerError(ctx, "创建分类失败")
		return
	}

	// 转换为响应结构体
	response := CategoryResponse{
		ID:          category.ID,
		CreatedAt:   category.CreatedAt,
		UpdatedAt:   category.UpdatedAt,
		DeletedAt:   category.DeletedAt,
		Name:        category.Name,
		Description: category.Description,
		Icon:        category.Icon,
		SortOrder:   category.SortOrder,
		IsActive:    category.IsActive,
		ParentID:    category.ParentID,
	}

	ctx.JSON(http.StatusCreated, response)
}

// GetCategory 获取分类
//
//	@Summary		获取应用分类
//	@Description	获取应用分类详情
//	@Tags			分类管理
//	@Accept			json
//	@Produce		json
//	@Param			id	path		int						true	"分类ID"
//	@Success		200	{object}	CategoryDetailResponse	"分类详情"
//	@Failure		400	{object}	response.ErrorResponse			"请求参数错误"
//	@Failure		404	{object}	response.ErrorResponse			"分类不存在"
//	@Failure		500	{object}	response.ErrorResponse			"内部服务器错误"
//	@Router			/categories/{id} [get]
func (c *CategoryController) GetCategory(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(ctx, "无效的分类ID")
		return
	}

	category, err := models.GetCategoryByID(c.db, uint(id))
	if err != nil {
		response.NotFound(ctx, "分类不存在")
		return
	}

	// 获取子分类
	subcategories, err := models.GetSubcategories(c.db, category.ID, false)
	if err != nil {
		response.InternalServerError(ctx, "获取子分类失败")
		return
	}

	// 转换为响应结构体
	categoryResp := CategoryResponse{
		ID:          category.ID,
		CreatedAt:   category.CreatedAt,
		UpdatedAt:   category.UpdatedAt,
		DeletedAt:   category.DeletedAt,
		Name:        category.Name,
		Description: category.Description,
		Icon:        category.Icon,
		SortOrder:   category.SortOrder,
		IsActive:    category.IsActive,
		ParentID:    category.ParentID,
	}

	// 转换子分类
	var subcategoryResps []CategoryResponse
	for _, sub := range subcategories {
		subcategoryResps = append(subcategoryResps, CategoryResponse{
			ID:          sub.ID,
			CreatedAt:   sub.CreatedAt,
			UpdatedAt:   sub.UpdatedAt,
			DeletedAt:   sub.DeletedAt,
			Name:        sub.Name,
			Description: sub.Description,
			Icon:        sub.Icon,
			SortOrder:   sub.SortOrder,
			IsActive:    sub.IsActive,
			ParentID:    sub.ParentID,
		})
	}

	ctx.JSON(http.StatusOK, gin.H{
		"category":      categoryResp,
		"subcategories": subcategoryResps,
	})
}

// UpdateCategory 更新分类
//
//	@Summary		更新应用分类
//	@Description	更新应用分类信息
//	@Tags			分类管理
//	@Accept			json
//	@Produce		json
//	@Param			Authorization	header		string					true	"Bearer 用户令牌"
//	@Param			id				path		int						true	"分类ID"
//	@Param			category		body		UpdateCategoryRequest	true	"分类信息"
//	@Success		200				{object}	CategoryResponse		"更新成功"
//	@Failure		400				{object}	response.ErrorResponse			"请求参数错误"
//	@Failure		401				{object}	response.ErrorResponse			"未授权"
//	@Failure		403				{object}	response.ErrorResponse			"权限不足"
//	@Failure		404				{object}	response.ErrorResponse			"分类不存在"
//	@Failure		500				{object}	response.ErrorResponse			"内部服务器错误"
//	@Router			/categories/{id} [put]
func (c *CategoryController) UpdateCategory(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(ctx, "无效的分类ID")
		return
	}

	category, err := models.GetCategoryByID(c.DB, uint(id))
	if err != nil {
		response.NotFound(ctx, "分类不存在")
		return
	}

	var req UpdateCategoryRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, "无效的请求参数")
		return
	}

	// 如果更新了名称，检查是否与其他分类重名
	if req.Name != "" && req.Name != category.Name {
		existingCategory, err := models.GetCategoryByName(c.db, req.Name)
		if err == nil && existingCategory.ID > 0 && existingCategory.ID != category.ID {
			response.BadRequest(ctx, "分类名称已存在")
			return
		}
		category.Name = req.Name
	}

	// 更新其他字段
	if req.Description != "" {
		category.Description = req.Description
	}
	if req.Icon != "" {
		category.Icon = req.Icon
	}
	category.SortOrder = req.SortOrder

	// 更新父分类
	if req.ParentID != nil {
		// 防止循环依赖
		if *req.ParentID == category.ID {
			response.BadRequest(ctx, "不能将分类设为自己的父分类")
			return
		}

		// 如果设置了父分类，检查父分类是否存在
		if *req.ParentID > 0 {
			parentCategory, err := models.GetCategoryByID(c.db, *req.ParentID)
			if err != nil || parentCategory.ID == 0 {
				response.BadRequest(ctx, "父分类不存在")
				return
			}
		}
		category.ParentID = req.ParentID
	}

	// 更新状态
	if req.IsActive != nil {
		category.IsActive = *req.IsActive
	}

	if err := models.UpdateCategory(c.db, category); err != nil {
		response.InternalServerError(ctx, "更新分类失败")
		return
	}

	// 转换为响应结构体
	response := CategoryResponse{
		ID:          category.ID,
		CreatedAt:   category.CreatedAt,
		UpdatedAt:   category.UpdatedAt,
		DeletedAt:   category.DeletedAt,
		Name:        category.Name,
		Description: category.Description,
		Icon:        category.Icon,
		SortOrder:   category.SortOrder,
		IsActive:    category.IsActive,
		ParentID:    category.ParentID,
	}

	ctx.JSON(http.StatusOK, response)
}

// DeleteCategory 删除分类
//
//	@Summary		删除应用分类
//	@Description	删除应用分类
//	@Tags			分类管理
//	@Accept			json
//	@Produce		json
//	@Param			Authorization	header		string			true	"Bearer 用户令牌"
//	@Param			id				path		int				true	"分类ID"
//	@Success		200				{object}	response.SuccessResponse	"删除成功"
//	@Failure		400				{object}	response.ErrorResponse	"请求参数错误"
//	@Failure		401				{object}	response.ErrorResponse	"未授权"
//	@Failure		403				{object}	response.ErrorResponse	"权限不足"
//	@Failure		404				{object}	response.ErrorResponse	"分类不存在"
//	@Failure		500				{object}	response.ErrorResponse	"内部服务器错误"
//	@Router			/categories/{id} [delete]
func (c *CategoryController) DeleteCategory(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(ctx, "无效的分类ID")
		return
	}

	// 检查分类是否存在
	category, err := models.GetCategoryByID(c.DB, uint(id))
	if err != nil {
		response.NotFound(ctx, "分类不存在")
		return
	}

	// 检查是否有子分类
	subcategories, err := models.GetSubcategories(c.db, category.ID, true)
	if err != nil {
		response.InternalServerError(ctx, "检查子分类失败")
		return
	}

	if len(subcategories) > 0 {
		response.BadRequest(ctx, "无法删除含有子分类的分类，请先删除子分类")
		return
	}

	// 检查是否有应用使用此分类
	var appCount int64
	if err := c.db.Model(&models.Application{}).Where("category = ?", category.Name).Count(&appCount).Error; err != nil {
		response.InternalServerError(ctx, "检查应用失败")
		return
	}

	if appCount > 0 {
		response.BadRequest(ctx, "无法删除含有应用的分类，请先更改这些应用的分类")
		return
	}

	// 删除分类
	if err := models.DeleteCategory(c.db, uint(id)); err != nil {
		response.InternalServerError(ctx, "删除分类失败")
		return
	}

	response.SuccessWithMessage(ctx, "分类删除成功", nil)
}

// ListCategories 获取分类列表
//
//	@Summary		获取应用分类列表
//	@Description	获取所有应用分类
//	@Tags			分类管理
//	@Accept			json
//	@Produce		json
//	@Param			include_inactive	query		bool				false	"是否包含未激活的分类"	default(false)
//	@Success		200					{array}		CategoryResponse	"分类列表"
//	@Failure		500					{object}	response.ErrorResponse		"内部服务器错误"
//	@Router			/categories [get]
func (c *CategoryController) ListCategories(ctx *gin.Context) {
	includeInactive := ctx.Query("include_inactive") == "true"

	categories, err := models.ListCategories(c.db, includeInactive)
	if err != nil {
		response.InternalServerError(ctx, "获取分类列表失败")
		return
	}

	// 转换为响应结构体
	var responses []CategoryResponse
	for _, category := range categories {
		responses = append(responses, CategoryResponse{
			ID:          category.ID,
			CreatedAt:   category.CreatedAt,
			UpdatedAt:   category.UpdatedAt,
			DeletedAt:   category.DeletedAt,
			Name:        category.Name,
			Description: category.Description,
			Icon:        category.Icon,
			SortOrder:   category.SortOrder,
			IsActive:    category.IsActive,
			ParentID:    category.ParentID,
		})
	}

	ctx.JSON(http.StatusOK, responses)
}

// ListPublicCategories 获取公开分类列表
//
//	@Summary		获取公开分类列表
//	@Description	获取所有激活的应用分类，无需认证
//	@Tags			分类
//	@Accept			json
//	@Produce		json
//	@Param			include_subcategories	query		bool				false	"是否包含子分类"	default(true)
//	@Success		200						{array}		CategoryResponse	"分类列表"
//	@Failure		500						{object}	response.ErrorResponse		"内部服务器错误"
//	@Router			/public/categories [get]
func (c *CategoryController) ListPublicCategories(ctx *gin.Context) {
	includeSubcategories := ctx.DefaultQuery("include_subcategories", "true") == "true"

	// 获取所有激活的分类
	categories, err := models.ListCategories(c.db, false) // false表示不包含未激活的分类
	if err != nil {
		response.InternalServerError(ctx, "获取分类列表失败")
		return
	}

	// 如果不包含子分类，只返回顶级分类
	if !includeSubcategories {
		var topLevelCategories []models.Category
		for _, category := range categories {
			if category.ParentID == nil {
				topLevelCategories = append(topLevelCategories, category)
			}
		}
		categories = topLevelCategories
	}

	// 转换为响应结构体
	var responses []CategoryResponse
	for _, category := range categories {
		responses = append(responses, CategoryResponse{
			ID:          category.ID,
			CreatedAt:   category.CreatedAt,
			UpdatedAt:   category.UpdatedAt,
			DeletedAt:   category.DeletedAt,
			Name:        category.Name,
			Description: category.Description,
			Icon:        category.Icon,
			SortOrder:   category.SortOrder,
			IsActive:    category.IsActive,
			ParentID:    category.ParentID,
		})
	}

	ctx.JSON(http.StatusOK, responses)
}

// GetAppsByCategory 获取分类下的应用
//
//	@Summary		获取分类下的应用
//	@Description	获取指定分类下的所有已审核通过的应用
//	@Tags			分类管理
//	@Accept			json
//	@Produce		json
//	@Param			id			path		int										true	"分类ID"
//	@Param			page		query		int										false	"页码，默认1"									default(1)
//	@Param			page_size	query		int										false	"每页数量，默认20"									default(20)
//	@Param			sort		query		string									false	"排序方式：latest(最新)、popular(热门)、rating(评分)"	default(latest)
//	@Success		200			{object}	response.PageResponse{data=[]AppDetailsResponse}	"返回应用列表"
//	@Failure		400			{object}	response.ErrorResponse							"参数错误"
//	@Failure		404			{object}	response.ErrorResponse							"分类不存在"
//	@Failure		500			{object}	response.ErrorResponse							"内部服务器错误"
//	@Router			/categories/{id}/apps [get]
func (c *CategoryController) GetAppsByCategory(ctx *gin.Context) {
	// 获取分类ID
	categoryID, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		response.BadRequest(ctx, "无效的分类ID")
		return
	}

	// 检查分类是否存在
	category, err := models.GetCategoryByID(c.db, uint(categoryID))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "分类不存在")
		} else {
			response.InternalServerError(ctx, "获取分类信息失败")
		}
		return
	}

	// 解析分页参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))
	sort := ctx.DefaultQuery("sort", "latest")

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize

	// 构建查询 - 只显示已审核通过的应用
	query := c.db.Model(&models.Application{}).Where("status = ? AND category = ?", models.ApplicationStatusApproved, category.Name)

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		response.InternalServerError(ctx, "获取应用总数失败")
		return
	}

	// 根据排序方式设置排序
	var orderBy string
	switch sort {
	case "popular":
		orderBy = "download_count DESC, average_rating DESC"
	case "rating":
		orderBy = "average_rating DESC, rating_count DESC"
	case "latest":
		fallthrough
	default:
		orderBy = "created_at DESC"
	}

	// 获取应用列表
	var apps []models.Application
	if err := query.Preload("Developer").Order(orderBy).Offset(offset).Limit(pageSize).Find(&apps).Error; err != nil {
		response.InternalServerError(ctx, "获取应用列表失败")
		return
	}

	// 构建返回结果
	result := make([]AppDetailsResponse, 0, len(apps))
	for _, app := range apps {
		var releaseDate time.Time
		if app.ReleaseDate != nil {
			releaseDate = *app.ReleaseDate
		}

		result = append(result, AppDetailsResponse{
			ID:                  app.ID,
			Name:                app.Name,
			Package:             app.Package,
			ShortDesc:           app.ShortDesc,
			Description:         app.Description,
			Icon:                app.Icon,
			Category:            app.Category,
			DeveloperID:         app.DeveloperID,
			DeveloperName:       app.Developer.DeveloperName,
			CurrentVersion:      app.CurrentVersion,
			ReleaseDate:         releaseDate,
			Size:                app.Size,
			DownloadCount:       app.DownloadCount,
			AverageRating:       app.AverageRating,
			RatingCount:         app.RatingCount,
			MinOpenHarmonyOSVer: app.MinOpenHarmonyOSVer,
			Tags:                app.Tags,
			WebsiteURL:          app.WebsiteURL,
			PrivacyURL:          app.PrivacyURL,
			IsVerified:          app.IsVerified,
			IsFeatured:          app.IsFeatured,
			IsEditor:            app.IsEditor,
			IsTop:               app.IsTop,
			Status:              string(app.Status),
			CreatedAt:           app.CreatedAt,
			UpdatedAt:           app.UpdatedAt,
		})
	}

	// 返回分页结果
	response.Success(ctx, PagedResponse{
		Total:    total,
		Page:     page,
		PageSize: pageSize,
		Data:     result,
	})
}

// ListRootCategories 获取根分类列表
//
//	@Summary		获取根应用分类列表
//	@Description	获取所有根应用分类（没有父分类的分类）
//	@Tags			分类管理
//	@Accept			json
//	@Produce		json
//	@Param			include_inactive	query		bool				false	"是否包含未激活的分类"	default(false)
//	@Success		200					{array}		CategoryResponse	"根分类列表"
//	@Failure		500					{object}	response.ErrorResponse		"内部服务器错误"
//	@Router			/categories/root [get]
func (c *CategoryController) ListRootCategories(ctx *gin.Context) {
	includeInactive := ctx.Query("include_inactive") == "true"

	categories, err := models.ListRootCategories(c.db, includeInactive)
	if err != nil {
		response.InternalServerError(ctx, "获取根分类列表失败")
		return
	}

	// 转换为响应结构体
	var responses []CategoryResponse
	for _, category := range categories {
		responses = append(responses, CategoryResponse{
			ID:          category.ID,
			CreatedAt:   category.CreatedAt,
			UpdatedAt:   category.UpdatedAt,
			DeletedAt:   category.DeletedAt,
			Name:        category.Name,
			Description: category.Description,
			Icon:        category.Icon,
			SortOrder:   category.SortOrder,
			IsActive:    category.IsActive,
			ParentID:    category.ParentID,
		})
	}

	ctx.JSON(http.StatusOK, responses)
}
