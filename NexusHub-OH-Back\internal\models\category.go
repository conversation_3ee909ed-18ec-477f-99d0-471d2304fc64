package models

import (
	"time"

	"gorm.io/gorm"
)

// Category 应用分类模型
//	@Description	应用分类模型
type Category struct {
	// gorm.Model 展开为以下字段
	ID          uint       `gorm:"primaryKey" json:"id"`                              // 分类ID
	CreatedAt   time.Time  `json:"created_at"`                                        // 创建时间
	UpdatedAt   time.Time  `json:"updated_at"`                                        // 更新时间
	DeletedAt   *time.Time `gorm:"index" json:"deleted_at"`                           // 删除时间
	Name        string     `gorm:"type:varchar(50);uniqueIndex;not null" json:"name"` // 分类名称
	Description string     `gorm:"type:text" json:"description"`                      // 分类描述
	Icon        string     `gorm:"type:varchar(255)" json:"icon"`                     // 分类图标URL
	SortOrder   int        `gorm:"default:0" json:"sort_order"`                       // 排序权重
	IsActive    bool       `gorm:"default:true" json:"is_active"`                     // 是否启用
	ParentID    *uint      `gorm:"default:null" json:"parent_id"`                     // 父分类ID，支持多级分类
	Parent      *Category  `gorm:"foreignKey:ParentID" json:"-"`                      // 父分类，不输出到JSON
}

// TableName 指定表名
func (Category) TableName() string {
	return "categories"
}

// CreateCategory 创建分类
func CreateCategory(db *gorm.DB, category *Category) error {
	return db.Create(category).Error
}

// GetCategoryByID 通过ID获取分类
func GetCategoryByID(db *gorm.DB, id uint) (*Category, error) {
	var category Category
	err := db.First(&category, id).Error
	return &category, err
}

// GetCategoryByName 通过名称获取分类
func GetCategoryByName(db *gorm.DB, name string) (*Category, error) {
	var category Category
	err := db.Where("name = ?", name).First(&category).Error
	return &category, err
}

// UpdateCategory 更新分类
func UpdateCategory(db *gorm.DB, category *Category) error {
	return db.Save(category).Error
}

// DeleteCategory 删除分类
func DeleteCategory(db *gorm.DB, id uint) error {
	return db.Delete(&Category{}, id).Error
}

// ListCategories 获取所有分类
func ListCategories(db *gorm.DB, includeInactive bool) ([]Category, error) {
	var categories []Category
	query := db.Order("sort_order, name")

	if !includeInactive {
		query = query.Where("is_active = ?", true)
	}

	err := query.Find(&categories).Error
	return categories, err
}

// ListRootCategories 获取所有根分类（没有父分类的）
func ListRootCategories(db *gorm.DB, includeInactive bool) ([]Category, error) {
	var categories []Category
	query := db.Where("parent_id IS NULL").Order("sort_order, name")

	if !includeInactive {
		query = query.Where("is_active = ?", true)
	}

	err := query.Find(&categories).Error
	return categories, err
}

// GetSubcategories 获取指定分类的子分类
func GetSubcategories(db *gorm.DB, parentID uint, includeInactive bool) ([]Category, error) {
	var categories []Category
	query := db.Where("parent_id = ?", parentID).Order("sort_order, name")

	if !includeInactive {
		query = query.Where("is_active = ?", true)
	}

	err := query.Find(&categories).Error
	return categories, err
}
