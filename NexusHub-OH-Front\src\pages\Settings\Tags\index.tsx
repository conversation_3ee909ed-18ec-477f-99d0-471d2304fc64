import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Space,
  message,
  Popconfirm,
  Tooltip,
  Tag as AntdTag,
  Grid,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import {
  getTags,
  postTags as createTag,
  putTagsId as updateTag,
  deleteTagsId as deleteTag,
} from '@/services/ant-design-pro/biaoqianguanli';

const { useBreakpoint } = Grid;

const { Option } = Select;

const TagsManagement: React.FC = () => {
  console.log('🔍 [TagsManagement] 组件初始化');
  
  const screens = useBreakpoint();
  const [searchParams, setSearchParams] = useState<API.getTagsParams>({
    include_inactive: true
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingTag, setEditingTag] = useState<API.TagResponse | null>(null);
  const [form] = Form.useForm();

  // 响应式配置
  const isMobile = !screens.md;
  const isTablet = screens.md && !screens.lg;

  const [data, setData] = useState<API.TagResponse[]>([]);
  const [loading, setLoading] = useState(false);
  
  const fetchTags = async () => {
    setLoading(true);
    try {
      const response = await getTags(searchParams);
      
      // 如果响应直接是数组，直接使用
      if (Array.isArray(response)) {
        setData(response);
      } else if (response && response.data) {
        setData(response.data);
      } else {
        setData([]);
      }
    } catch (error) {
      console.error('标签数据请求失败:', error);
      setData([]);
    } finally {
      setLoading(false);
    }
  };
  
  const refresh = fetchTags;
  
  useEffect(() => {
    fetchTags();
  }, [searchParams]);

  const handleSearch = (values: any) => {
    setSearchParams({
      ...searchParams,
      ...values,
    });
  };

  const showAddModal = () => {
    setEditingTag(null);
    form.resetFields();
    setModalVisible(true);
  };

  const showEditModal = (record: API.TagResponse) => {
    setEditingTag(record);
    form.setFieldsValue({
      name: record.name,
      color: record.color,
      description: record.description,
      is_active: record.is_active,
    });
    setModalVisible(true);
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingTag) {
        // 更新标签
        await updateTag(
          { id: editingTag.id as number },
          values as API.UpdateTagRequest
        );
        message.success(`标签 "${values.name}" 已更新`);
      } else {
        // 创建标签
        try {
          await createTag(values as API.CreateTagRequest);
          message.success(`标签 "${values.name}" 已创建`);
          setModalVisible(false);
          refresh();
        } catch (error: any) {
          // 处理API错误响应
          if (error.response && error.response.status === 400) {
            // 尝试从响应中获取错误信息
            const errorData = error.response.data;
            if (errorData && errorData.message) {
              message.error(errorData.message);
            } else if (errorData && errorData.code === 400) {
              message.error('标签名称已存在');
            } else {
              message.error('创建标签失败，请检查输入');
            }
          } else {
            message.error('创建标签失败，请稍后重试');
          }
          return; // 出错时不关闭模态框，让用户修改
        }
      }
      setModalVisible(false);
      refresh();
    } catch (error) {
      console.error('表单验证或提交错误:', error);
    }
  };

  const handleDelete = async (id: number, name: string) => {
    try {
      await deleteTag({ id });
      message.success(`标签 "${name}" 已删除`);
      refresh();
    } catch (error) {
      message.error(`删除失败: ${error}`);
    }
  };

  const handleBatchDelete = async () => {
    // 实际项目中这里应该实现批量删除API
    // 这里简化为逐个删除
    try {
      for (const id of selectedRowKeys) {
        await deleteTag({ id: id as number });
      }
      message.success(`已删除 ${selectedRowKeys.length} 个标签`);
      setSelectedRowKeys([]);
      refresh();
    } catch (error) {
      message.error(`批量删除失败: ${error}`);
    }
  };

  const colorOptions = [
    { label: '红色', value: '#f5222d' },
    { label: '橙色', value: '#fa8c16' },
    { label: '黄色', value: '#fadb14' },
    { label: '绿色', value: '#52c41a' },
    { label: '青色', value: '#13c2c2' },
    { label: '蓝色', value: '#1890ff' },
    { label: '紫色', value: '#722ed1' },
    { label: '粉色', value: '#eb2f96' },
  ];

  const columns: ColumnsType<API.TagResponse> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      sorter: (a, b) => a.id - b.id,
      hidden: isMobile, // 移动端隐藏ID列
    },
    {
      title: '标签信息',
      key: 'tagInfo',
      render: (_, record: API.TagResponse) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
            {record.name}
          </div>
          <Space size="small">
            <div
              style={{
                width: 16,
                height: 16,
                backgroundColor: record.color,
                borderRadius: '50%',
                border: '1px solid #d9d9d9',
              }}
            />
            <AntdTag color={record.color} size="small">{record.name}</AntdTag>
            <AntdTag color={record.is_active ? 'green' : 'red'} size="small">
              {record.is_active ? '启用' : '禁用'}
            </AntdTag>
          </Space>
          {!isMobile && record.description && (
            <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>
              {record.description}
            </div>
          )}
        </div>
      ),
    },
    // 桌面端显示的详细列
    ...(!isMobile ? [
      {
        title: '颜色',
        dataIndex: 'color',
        key: 'color',
        width: 120,
        render: (color: string, record: API.TagResponse) => (
          <Space>
            <div
              style={{
                width: 20,
                height: 20,
                backgroundColor: color,
                borderRadius: '50%',
                border: '1px solid #d9d9d9',
              }}
            />
            <AntdTag color={color}>{record.name}</AntdTag>
          </Space>
        ),
      },
      {
        title: '描述',
        dataIndex: 'description',
        key: 'description',
        ellipsis: {
          showTitle: false,
        },
        render: (description: string) => (
          <Tooltip placement="topLeft" title={description}>
            {description || '-'}
          </Tooltip>
        ),
      },
      {
        title: '状态',
        dataIndex: 'is_active',
        key: 'is_active',
        width: 100,
        render: (isActive: boolean) => (
          <AntdTag color={isActive ? 'green' : 'red'}>
            {isActive ? '启用' : '禁用'}
          </AntdTag>
        ),
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        width: 180,
        sorter: (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
        render: (date: string) => new Date(date).toLocaleString(),
      },
      {
        title: '更新时间',
        dataIndex: 'updated_at',
        key: 'updated_at',
        width: 180,
        sorter: (a, b) => new Date(a.updated_at).getTime() - new Date(b.updated_at).getTime(),
        render: (date: string) => new Date(date).toLocaleString(),
      },
    ] : []),
    {
      title: '操作',
      key: 'action',
      width: isMobile ? 80 : 150,
      fixed: isMobile ? 'right' : undefined,
      render: (_, record: API.TagResponse) => (
        <Space size="small" direction={isMobile ? 'vertical' : 'horizontal'}>
          <Tooltip title="编辑">
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => showEditModal(record)}
              size="small"
            >
              {isMobile ? '' : '编辑'}
            </Button>
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除这个标签吗？"
              onConfirm={() => handleDelete(record.id as number, record.name as string)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="link"
                icon={<DeleteOutlined />}
                danger
                size="small"
              >
                {isMobile ? '' : '删除'}
              </Button>
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 数据状态
  const dataLength = data?.length || 0;
  const hasData = dataLength > 0;

  return (
    <PageContainer
      header={{
        title: '标签管理',
        subTitle: '管理系统中的所有标签',
      }}
    >
      <Card>
        {/* 搜索表单 */}
        <Form
          layout={isMobile ? 'vertical' : 'inline'}
          onFinish={handleSearch}
          style={{ marginBottom: 16 }}
        >
          <Form.Item name="keyword" label="关键词" style={{ marginBottom: isMobile ? 12 : 16 }}>
            <Input 
              placeholder="搜索标签名称" 
              prefix={<SearchOutlined />}
              style={{ width: isMobile ? '100%' : 200 }}
              allowClear
            />
          </Form.Item>
          <Form.Item name="include_inactive" label="显示禁用标签" style={{ marginBottom: isMobile ? 12 : 16 }}>
            <Select 
              placeholder="是否显示禁用标签" 
              style={{ width: isMobile ? '100%' : 150 }}
              defaultValue={true}
              allowClear
            >
              <Option value={true}>显示所有</Option>
              <Option value={false}>仅显示启用</Option>
            </Select>
          </Form.Item>
          <Form.Item style={{ marginBottom: isMobile ? 12 : 16 }}>
            <Space direction={isMobile ? 'horizontal' : 'horizontal'} style={{ width: isMobile ? '100%' : 'auto' }}>
              <Button 
                type="primary" 
                htmlType="submit"
                icon={<SearchOutlined />}
                style={{ flex: isMobile ? 1 : 'none' }}
              >
                搜索
              </Button>
              <Button 
                onClick={() => setSearchParams({ include_inactive: true })}
                style={{ flex: isMobile ? 1 : 'none' }}
              >
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>

        {/* 操作按钮 */}
        <div style={{ marginBottom: 16 }}>
          <Space 
            direction={isMobile ? 'vertical' : 'horizontal'} 
            style={{ width: isMobile ? '100%' : 'auto' }}
            size={isMobile ? 8 : 'small'}
          >
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={showAddModal}
              style={{ width: isMobile ? '100%' : 'auto' }}
            >
              新建标签
            </Button>
            <Popconfirm
              title="确定要删除选中的标签吗？"
              onConfirm={handleBatchDelete}
              okText="确定"
              cancelText="取消"
              disabled={selectedRowKeys.length === 0}
            >
              <Button 
                danger 
                disabled={selectedRowKeys.length === 0}
                style={{ width: isMobile ? '100%' : 'auto' }}
              >
                批量删除 ({selectedRowKeys.length})
              </Button>
            </Popconfirm>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={refresh}
              style={{ width: isMobile ? '100%' : 'auto' }}
            >
              刷新
            </Button>
          </Space>
        </div>


        
        <Table
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
          columns={columns}
          dataSource={data || []}
          rowKey="id"
          loading={loading}
          scroll={{
            x: isMobile ? 800 : undefined,
            y: isMobile ? 400 : undefined,
          }}
          size={isMobile ? 'small' : 'middle'}
          pagination={{
            current: searchParams.page,
            pageSize: searchParams.pageSize,
            total: data?.length || 0,
            showSizeChanger: !isMobile,
            showQuickJumper: !isMobile,
            simple: isMobile,
            showTotal: !isMobile ? (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条` : undefined,
            onChange: (page, pageSize) => {
              setSearchParams({
                ...searchParams,
                page,
                pageSize: pageSize || 10,
              });
            },
            position: [isMobile ? 'bottomCenter' : 'bottomRight'],
          }}
        />
      </Card>

      <Modal
        title={editingTag ? '编辑标签' : '新建标签'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => {
          setModalVisible(false);
          setEditingTag(null);
          form.resetFields();
        }}
        width={isMobile ? '95%' : 600}
        centered={isMobile}
        destroyOnHidden
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="标签名称"
            rules={[{ required: true, message: '请输入标签名称' }]}
          >
            <Input placeholder="请输入标签名称" />
          </Form.Item>
          <Form.Item
            name="color"
            label="标签颜色"
          >
            <Select placeholder="请选择标签颜色">
              {colorOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <div style={{ 
                      width: 16, 
                      height: 16, 
                      backgroundColor: option.value, 
                      marginRight: 8,
                      borderRadius: '50%'
                    }} />
                    {option.label}
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="description"
            label="标签描述"
          >
            <Input.TextArea rows={3} placeholder="请输入标签描述" />
          </Form.Item>
          <Form.Item
            name="is_active"
            label="状态"
            valuePropName="checked"
            initialValue={true}
          >
            <Select defaultValue={true}>
              <Option value={true}>启用</Option>
              <Option value={false}>禁用</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default TagsManagement;