<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="8b34cf39-6aa3-4715-a962-3bf04a3dd565" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/API-文档.md" beforeDir="false" afterPath="$PROJECT_DIR$/API-文档.md" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HiLogStateProjectLevelPreference">
    <panelStates>
      <hilogPanelState>
        <option name="logPanelType" value="ONLINE" />
        <option name="processName" value="com.example.nexushub" />
      </hilogPanelState>
    </panelStates>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/entry/src/main/ets/pages/FeaturedCollectionDetailPage.ets" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
    <option name="showPreview" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2yGMZS1ZkZqJnaz6DRzljuxzdB9" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "127.0.0.1:5555": "5375923811750066965507",
    "3BPUT24709004706": "5370969571750248721623",
    "Application.entry.executor": "Run",
    "MODULE_HAP_TIME": "{\"3BPUT24709004706entry\":\"2025-06-18T12:11:42.533561800Z\",\"127.0.0.1:5555entry\":\"2025-06-16T09:42:44.677639500Z\"}",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "ace.nodejs.path": "C:/Program Files/Huawei/DevEco Studio/tools/node",
    "ace.nodejs.version": "18.20.1",
    "auto_debug_bundleName": "0000002435292F7806EDA377CA251AA221E4512C0414E7A79A491D578B4DD5B663B9D444FF499BFD047C35E7238FD1C35580AF44",
    "auto_debug_cert_list": "000000233BF5094526D6098CDF72E4081EA745F0352DEE603BE4E30C427A52C8E9C40E0533626E6599CED940C4C55ED74FCE75",
    "auto_debug_device_list": "0000002313BA51ABDD5CE786534F5D727583CE19BE4EC39FE67B90460A70BB8EDDD9D279CAD43030A2C4428B17477EA54A4C5B",
    "auto_debug_package_name": "00000024F976E368671F43DB8973043C87B8F81597F3D4B641C811A925D548E42ADDC91FF31E3CCB2DD2675B3567525FE33B550F",
    "auto_debug_team_id": "00000022C26AB75840D59DE739A2F907B62585F575E5FCA1B1B9E6615C0986187A671892AA11D5D10EE46A144A64ECE4AD8E",
    "last_opened_file_path": "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub",
    "projectSizeLastTraceTimestamp-99786312e4bbb4e99ccce6ea69035ba1580211f70c2b92c5c5d21fa1a4f7": "1750242922641",
    "resolution": "1080*2340",
    "run.code.analysis.last.selected.profile": "pProject Default",
    "settings.editor.selected.configurable": "com.huawei.deveco.sdkmanager.ohos.idea.ui.OhSdkConfigProvider",
    "shape": "phone"
  },
  "keyToStringList": {
    "reqPermissions": [
    ]
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CreateTestDialog.RecentsKey">
      <recent name="C:\Users\<USER>\Documents\NexusHub-OH\NexusHub\entry\src\ohosTest\ets\test" />
    </key>
  </component>
  <component name="RunManager" selected="Application.entry">
    <configuration name="entry" type="HotReLoadTask" factoryName="Hot Reload">
      <MODULE_NAME />
      <REINSTALL_APPLICATION>true</REINSTALL_APPLICATION>
      <ENVIRONMENT_VARIABLES>[]</ENVIRONMENT_VARIABLES>
      <DEBUG_LINE_ENABLE_DATA>false</DEBUG_LINE_ENABLE_DATA>
      <AUTO_DEPENDENCIES_DATA>true</AUTO_DEPENDENCIES_DATA>
      <WEB_VIEW_DEBUG>false</WEB_VIEW_DEBUG>
      <INSTALL_FLAGS_CONFIG_KEY />
      <LAUNCH_FLAGS_CONFIG_KEY />
      <LAUNCH_ABILITY_CONFIG_TYPE>Default Ability</LAUNCH_ABILITY_CONFIG_TYPE>
      <LAUNCH_ABILITY_CONFIG_VALUE />
      <SYMBOL_DIRS>[]</SYMBOL_DIRS>
      <DEBUGGER_TYPE>Detect Automatically</DEBUGGER_TYPE>
      <MULTI_HAP_MODULE_DATA>[]</MULTI_HAP_MODULE_DATA>
      <SHOW_STATIC_VARIABLES>true</SHOW_STATIC_VARIABLES>
      <LOGGING_TARGET_CHANNELS>lldb process:gdb-remote packets</LOGGING_TARGET_CHANNELS>
      <USER_POST_ATTACH_COMMANDS>[]</USER_POST_ATTACH_COMMANDS>
      <USER_STARTUP_COMMANDS>[]</USER_STARTUP_COMMANDS>
      <ETS_SOURCE_PARIS>[]</ETS_SOURCE_PARIS>
      <DEPLOY_MULTI_HAP>false</DEPLOY_MULTI_HAP>
      <ASAN_ENABLE_DATA>false</ASAN_ENABLE_DATA>
      <TSAN_ENABLE_DATA>false</TSAN_ENABLE_DATA>
      <ERROR_INFO_ENHANCE_ENABLE_DATA>false</ERROR_INFO_ENHANCE_ENABLE_DATA>
      <MULTI_THREAD_CHECK_SELECTED_DATA>false</MULTI_THREAD_CHECK_SELECTED_DATA>
      <UBSAN_ENABLE_DATA>false</UBSAN_ENABLE_DATA>
      <HWASAN_ENABLE_DATA>false</HWASAN_ENABLE_DATA>
      <HOT_RELOAD_MODULE_NAME>entry</HOT_RELOAD_MODULE_NAME>
      <method v="2">
        <option name="Build.Hvigor.HotReloadBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="entry" type="OhosDebugTask" factoryName="OpenHarmony App">
      <MODULE_NAME>entry</MODULE_NAME>
      <REINSTALL_APPLICATION>true</REINSTALL_APPLICATION>
      <ENVIRONMENT_VARIABLES>[]</ENVIRONMENT_VARIABLES>
      <DEBUG_LINE_ENABLE_DATA>false</DEBUG_LINE_ENABLE_DATA>
      <AUTO_DEPENDENCIES_DATA>true</AUTO_DEPENDENCIES_DATA>
      <WEB_VIEW_DEBUG>false</WEB_VIEW_DEBUG>
      <INSTALL_FLAGS_CONFIG_KEY />
      <LAUNCH_FLAGS_CONFIG_KEY />
      <LAUNCH_ABILITY_CONFIG_TYPE>Default Ability</LAUNCH_ABILITY_CONFIG_TYPE>
      <LAUNCH_ABILITY_CONFIG_VALUE />
      <SYMBOL_DIRS>[]</SYMBOL_DIRS>
      <DEBUGGER_TYPE>Detect Automatically</DEBUGGER_TYPE>
      <MULTI_HAP_MODULE_DATA>[]</MULTI_HAP_MODULE_DATA>
      <SHOW_STATIC_VARIABLES>true</SHOW_STATIC_VARIABLES>
      <LOGGING_TARGET_CHANNELS>lldb process:gdb-remote packets</LOGGING_TARGET_CHANNELS>
      <USER_POST_ATTACH_COMMANDS>[]</USER_POST_ATTACH_COMMANDS>
      <USER_STARTUP_COMMANDS>[]</USER_STARTUP_COMMANDS>
      <ETS_SOURCE_PARIS>[]</ETS_SOURCE_PARIS>
      <DEPLOY_MULTI_HAP>false</DEPLOY_MULTI_HAP>
      <ASAN_ENABLE_DATA>false</ASAN_ENABLE_DATA>
      <TSAN_ENABLE_DATA>false</TSAN_ENABLE_DATA>
      <ERROR_INFO_ENHANCE_ENABLE_DATA>false</ERROR_INFO_ENHANCE_ENABLE_DATA>
      <MULTI_THREAD_CHECK_SELECTED_DATA>false</MULTI_THREAD_CHECK_SELECTED_DATA>
      <UBSAN_ENABLE_DATA>false</UBSAN_ENABLE_DATA>
      <HWASAN_ENABLE_DATA>false</HWASAN_ENABLE_DATA>
      <method v="2">
        <option name="Build.Hvigor.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>