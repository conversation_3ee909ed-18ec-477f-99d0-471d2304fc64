package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"nexushub-oh-back/pkg/logger"
	"nexushub-oh-back/pkg/messaging"

	"go.uber.org/zap"
)

// MessageService 消息服务
type MessageService struct {
	rabbitMQClient *messaging.RabbitMQClient
}

// NewMessageService 创建新的消息服务
func NewMessageService(rabbitMQClient *messaging.RabbitMQClient) *MessageService {
	return &MessageService{
		rabbitMQClient: rabbitMQClient,
	}
}

// 队列名称常量
const (
	QueueAppReview     = "app_review_queue"     // 应用审核队列
	QueueNotification  = "notification_queue"  // 通知队列
	QueueEmailSend     = "email_send_queue"     // 邮件发送队列
	QueueAppAnalytics  = "app_analytics_queue"  // 应用分析队列
	QueueUserActivity  = "user_activity_queue"  // 用户活动队列
)

// 消息类型定义
type MessageType string

const (
	MsgTypeAppSubmitted    MessageType = "app_submitted"    // 应用提交
	MsgTypeAppApproved     MessageType = "app_approved"     // 应用通过
	MsgTypeAppRejected     MessageType = "app_rejected"     // 应用拒绝
	MsgTypeUserRegistered  MessageType = "user_registered"  // 用户注册
	MsgTypeReviewSubmitted MessageType = "review_submitted" // 评论提交
	MsgTypeAppDownloaded   MessageType = "app_downloaded"   // 应用下载
	MsgTypeUserLogin       MessageType = "user_login"       // 用户登录
)

// BaseMessage 基础消息结构
type BaseMessage struct {
	ID        string      `json:"id"`
	Type      MessageType `json:"type"`
	Timestamp time.Time   `json:"timestamp"`
	Data      interface{} `json:"data"`
}

// AppReviewMessage 应用审核消息
type AppReviewMessage struct {
	AppID       uint   `json:"app_id"`
	AppName     string `json:"app_name"`
	DeveloperID uint   `json:"developer_id"`
	Action      string `json:"action"` // submit, approve, reject
	Reason      string `json:"reason,omitempty"`
}

// NotificationMessage 通知消息
type NotificationMessage struct {
	UserID  uint   `json:"user_id"`
	Title   string `json:"title"`
	Content string `json:"content"`
	Type    string `json:"type"` // info, warning, error, success
}

// EmailMessage 邮件消息
type EmailMessage struct {
	To      string `json:"to"`
	Subject string `json:"subject"`
	Body    string `json:"body"`
	IsHTML  bool   `json:"is_html"`
}

// UserActivityMessage 用户活动消息
type UserActivityMessage struct {
	UserID     uint      `json:"user_id"`
	Action     string    `json:"action"`
	Resource   string    `json:"resource"`
	ResourceID uint      `json:"resource_id,omitempty"`
	IP         string    `json:"ip"`
	UserAgent  string    `json:"user_agent"`
	Timestamp  time.Time `json:"timestamp"`
}

// InitializeQueues 初始化所有队列
func (s *MessageService) InitializeQueues() error {
	if s.rabbitMQClient == nil {
		return fmt.Errorf("RabbitMQ客户端未初始化")
	}

	queues := []string{
		QueueAppReview,
		QueueNotification,
		QueueEmailSend,
		QueueAppAnalytics,
		QueueUserActivity,
	}

	for _, queueName := range queues {
		_, err := s.rabbitMQClient.DeclareQueue(
			queueName,
			true,  // durable
			false, // autoDelete
			false, // exclusive
			false, // noWait
		)
		if err != nil {
			return fmt.Errorf("声明队列 %s 失败: %w", queueName, err)
		}
		logger.Info("队列初始化成功", zap.String("queue", queueName))
	}

	return nil
}

// PublishMessage 发布消息到指定队列
func (s *MessageService) PublishMessage(ctx context.Context, queueName string, msgType MessageType, data interface{}) error {
	if s.rabbitMQClient == nil {
		return fmt.Errorf("RabbitMQ客户端未初始化")
	}

	message := BaseMessage{
		ID:        fmt.Sprintf("%d", time.Now().UnixNano()),
		Type:      msgType,
		Timestamp: time.Now(),
		Data:      data,
	}

	msgBytes, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("序列化消息失败: %w", err)
	}

	err = s.rabbitMQClient.PublishMessage(ctx, "", queueName, msgBytes)
	if err != nil {
		return fmt.Errorf("发布消息到队列 %s 失败: %w", queueName, err)
	}

	logger.Info("消息发布成功",
		zap.String("queue", queueName),
		zap.String("type", string(msgType)),
		zap.String("message_id", message.ID),
	)

	return nil
}

// PublishAppReviewMessage 发布应用审核消息
func (s *MessageService) PublishAppReviewMessage(ctx context.Context, appID uint, appName string, developerID uint, action, reason string) error {
	msgData := AppReviewMessage{
		AppID:       appID,
		AppName:     appName,
		DeveloperID: developerID,
		Action:      action,
		Reason:      reason,
	}

	var msgType MessageType
	switch action {
	case "submit":
		msgType = MsgTypeAppSubmitted
	case "approve":
		msgType = MsgTypeAppApproved
	case "reject":
		msgType = MsgTypeAppRejected
	default:
		msgType = MsgTypeAppSubmitted
	}

	return s.PublishMessage(ctx, QueueAppReview, msgType, msgData)
}

// PublishNotificationMessage 发布通知消息
func (s *MessageService) PublishNotificationMessage(ctx context.Context, userID uint, title, content, notificationType string) error {
	msgData := NotificationMessage{
		UserID:  userID,
		Title:   title,
		Content: content,
		Type:    notificationType,
	}

	return s.PublishMessage(ctx, QueueNotification, "notification", msgData)
}

// PublishEmailMessage 发布邮件消息
func (s *MessageService) PublishEmailMessage(ctx context.Context, to, subject, body string, isHTML bool) error {
	msgData := EmailMessage{
		To:      to,
		Subject: subject,
		Body:    body,
		IsHTML:  isHTML,
	}

	return s.PublishMessage(ctx, QueueEmailSend, "email_send", msgData)
}

// PublishUserActivityMessage 发布用户活动消息
func (s *MessageService) PublishUserActivityMessage(ctx context.Context, userID uint, action, resource string, resourceID uint, ip, userAgent string) error {
	msgData := UserActivityMessage{
		UserID:     userID,
		Action:     action,
		Resource:   resource,
		ResourceID: resourceID,
		IP:         ip,
		UserAgent:  userAgent,
		Timestamp:  time.Now(),
	}

	return s.PublishMessage(ctx, QueueUserActivity, MsgTypeUserLogin, msgData)
}

// StartConsumer 启动消费者
func (s *MessageService) StartConsumer(queueName string, handler func([]byte) error) error {
	if s.rabbitMQClient == nil {
		return fmt.Errorf("RabbitMQ客户端未初始化")
	}

	messages, err := s.rabbitMQClient.ConsumeMessages(
		queueName,
		"",    // consumer name
		false, // autoAck
		false, // exclusive
		false, // noLocal
		false, // noWait
	)
	if err != nil {
		return fmt.Errorf("启动消费者失败: %w", err)
	}

	go func() {
		for msg := range messages {
			err := handler(msg.Body)
			if err != nil {
				logger.Error("处理消息失败",
					zap.String("queue", queueName),
					zap.Error(err),
				)
				// 拒绝消息并重新入队
				msg.Nack(false, true)
			} else {
				// 确认消息
				msg.Ack(false)
				logger.Debug("消息处理成功", zap.String("queue", queueName))
			}
		}
	}()

	logger.Info("消费者启动成功", zap.String("queue", queueName))
	return nil
}