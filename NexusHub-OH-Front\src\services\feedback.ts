/* eslint-disable */
import { request } from '@umijs/max';

// 意见反馈相关接口

/**
 * 创建反馈
 * @param data - 反馈数据
 */
export async function createFeedback(data: {
  title: string;
  content: string;
  type: 'bug' | 'feature' | 'improvement' | 'complaint' | 'other';
  category_id?: number;
  contact_email?: string;
  contact_phone?: string;
  device_info?: string;
  system_version?: string;
  app_version?: string;
}) {
  return request('/feedback', {
    method: 'POST',
    data,
  });
}

/**
 * 获取我的反馈列表
 * @param params - 查询参数
 */
export async function getMyFeedbackList(params?: {
  page?: number;
  page_size?: number;
  type?: 'bug' | 'feature' | 'improvement' | 'complaint' | 'other';
  status?: 'pending' | 'processing' | 'resolved' | 'closed' | 'rejected';
}) {
  return request('/feedback/my', {
    method: 'GET',
    params,
  });
}

/**
 * 获取反馈详情
 * @param id - 反馈ID
 */
export async function getFeedbackDetail(id: string) {
  return request(`/feedback/${id}`, {
    method: 'GET',
  });
}

/**
 * 获取反馈分类列表
 */
export async function getFeedbackCategories() {
  return request('/feedback/categories', {
    method: 'GET',
  });
}

// 管理员接口

/**
 * 获取所有反馈列表（管理员）
 * @param params - 查询参数
 */
export async function getAdminFeedbackList(params?: {
  page?: number;
  page_size?: number;
  type?: 'bug' | 'feature' | 'improvement' | 'complaint' | 'other';
  status?: 'pending' | 'processing' | 'resolved' | 'closed' | 'rejected';
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  user_id?: number;
  keyword?: string;
}) {
  return request('/admin/feedback', {
    method: 'GET',
    params,
  });
}

/**
 * 更新反馈状态（管理员）
 * @param id - 反馈ID
 * @param data - 更新数据
 */
export async function updateFeedbackStatus(id: string, data: {
  status: 'pending' | 'processing' | 'resolved' | 'closed' | 'rejected';
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  resolution?: string;
}) {
  return request(`/admin/feedback/${id}`, {
    method: 'PUT',
    data,
  });
}

/**
 * 回复反馈（管理员）
 * @param id - 反馈ID
 * @param data - 回复内容
 */
export async function replyFeedback(id: string, data: {
  content: string;
  is_public?: boolean;
}) {
  return request(`/admin/feedback/${id}/reply`, {
    method: 'POST',
    data,
  });
}

/**
 * 获取反馈统计（管理员）
 */
export async function getFeedbackStats() {
  return request('/admin/feedback/stats', {
    method: 'GET',
  });
}

// 反馈分类管理接口（管理员）

/**
 * 创建反馈分类（管理员）
 * @param data - 分类数据
 */
export async function createFeedbackCategory(data: {
  name: string;
  description?: string;
  icon?: string;
  sort_order?: number;
  is_active?: boolean;
}) {
  return request('/admin/feedback/categories', {
    method: 'POST',
    data,
  });
}

/**
 * 更新反馈分类（管理员）
 * @param id - 分类ID
 * @param data - 更新数据
 */
export async function updateFeedbackCategory(id: string, data: {
  name?: string;
  description?: string;
  icon?: string;
  sort_order?: number;
  is_active?: boolean;
}) {
  return request(`/admin/feedback/categories/${id}`, {
    method: 'PUT',
    data,
  });
}

/**
 * 删除反馈分类（管理员）
 * @param id - 分类ID
 */
export async function deleteFeedbackCategory(id: string) {
  return request(`/admin/feedback/categories/${id}`, {
    method: 'DELETE',
  });
}