import { deviceInfo } from '@kit.BasicServicesKit';
import { display } from '@kit.ArkUI';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { Constants } from './Constants';

/**
 * 设备工具类
 */
export class DeviceUtils {
  private static instance: DeviceUtils;
  private deviceType: string = Constants.DEVICE_TYPE.PHONE;
  private isLandscape: boolean = false;
  private screenWidth: number = 0;
  private screenHeight: number = 0;

  private constructor() {
    this.initDeviceInfo();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): DeviceUtils {
    if (!DeviceUtils.instance) {
      DeviceUtils.instance = new DeviceUtils();
    }
    return DeviceUtils.instance;
  }

  /**
   * 初始化设备信息
   */
  private async initDeviceInfo() {
    try {
      // 使用官方API获取设备类型
      this.deviceType = deviceInfo.deviceType;
      hilog.info(0x0000, 'DeviceUtils', 'Device type detected: %{public}s', this.deviceType);
      
      // 获取屏幕信息
      const displayInfo = await display.getDefaultDisplaySync();
      this.screenWidth = displayInfo.width;
      this.screenHeight = displayInfo.height;
      this.isLandscape = this.screenWidth > this.screenHeight;
      
      hilog.info(0x0000, 'DeviceUtils', 'Screen info: width=%{public}d, height=%{public}d, isLandscape=%{public}s, deviceType=%{public}s', 
        this.screenWidth, this.screenHeight, this.isLandscape.toString(), this.deviceType);
    } catch (error) {
      hilog.error(0x0000, 'DeviceUtils', 'Failed to get device info: %{public}s', JSON.stringify(error));
      // 降级处理：如果API调用失败，使用屏幕尺寸判断
      this.detectDeviceTypeByScreen();
    }
  }

  /**
   * 通过屏幕尺寸检测设备类型（降级方案）
   */
  private detectDeviceTypeByScreen() {
    const minSize = Math.min(this.screenWidth, this.screenHeight);
    const maxSize = Math.max(this.screenWidth, this.screenHeight);
    
    // 根据屏幕尺寸判断设备类型
    if (minSize >= 600) {
      // 平板设备
      this.deviceType = Constants.DEVICE_TYPE.TABLET;
    } else if (maxSize >= 900 && this.isLandscape) {
      // 2in1设备（横屏时）
      this.deviceType = Constants.DEVICE_TYPE.TWO_IN_ONE;
    } else {
      // 手机设备
      this.deviceType = Constants.DEVICE_TYPE.PHONE;
    }
    hilog.info(0x0000, 'DeviceUtils', 'Device type detected by screen size: %{public}s', this.deviceType);
  }

  /**
   * 获取设备类型
   */
  getDeviceType(): string {
    return this.deviceType;
  }

  /**
   * 是否为手机设备
   */
  isPhone(): boolean {
    return this.deviceType === Constants.DEVICE_TYPE.PHONE;
  }

  /**
   * 是否为平板设备
   */
  isTablet(): boolean {
    return this.deviceType === Constants.DEVICE_TYPE.TABLET;
  }

  /**
   * 是否为2in1设备
   */
  isTwoInOne(): boolean {
    return this.deviceType === Constants.DEVICE_TYPE.TWO_IN_ONE;
  }

  /**
   * 是否为横屏
   */
  isLandscapeMode(): boolean {
    return this.isLandscape;
  }

  /**
   * 是否为竖屏
   */
  isPortraitMode(): boolean {
    return !this.isLandscape;
  }

  /**
   * 获取屏幕宽度
   */
  getScreenWidth(): number {
    return this.screenWidth;
  }

  /**
   * 获取屏幕高度
   */
  getScreenHeight(): number {
    return this.screenHeight;
  }

  /**
   * 获取适配的列数（用于网格布局）
   */
  getGridColumns(): number {
    if (this.isPhone()) {
      return this.isLandscape ? 3 : 2;
    } else if (this.isTablet()) {
      return this.isLandscape ? 5 : 3;
    } else {
      return this.isLandscape ? 6 : 4;
    }
  }

  /**
   * 获取适配的侧边栏宽度
   */
  getSidebarWidth(): number {
    if (this.isPhone()) {
      return 0; // 手机不显示侧边栏
    } else if (this.isTablet()) {
      return 160; // 平板设备使用更窄的左侧导航栏
    } else {
      return 180; // 2in1设备使用更窄的左侧导航栏
    }
  }

  /**
   * 是否显示侧边导航
   */
  shouldShowSideNavigation(): boolean {
    return !this.isPhone() && this.isLandscape;
  }

  /**
   * 是否显示底部导航
   */
  shouldShowBottomNavigation(): boolean {
    return this.isPhone() || !this.isLandscape;
  }

  /**
   * 获取内容区域宽度
   */
  getContentWidth(): number {
    if (this.shouldShowSideNavigation()) {
      return this.screenWidth - this.getSidebarWidth();
    }
    return this.screenWidth;
  }

  /**
   * 获取适配的字体大小
   */
  getAdaptiveFontSize(baseSize: number): number {
    const scale = this.isTablet() || this.isTwoInOne() ? 1.2 : 1.0;
    return Math.round(baseSize * scale);
  }

  /**
   * 获取适配的间距
   */
  getAdaptiveSpacing(baseSpacing: number): number {
    const scale = this.isTablet() || this.isTwoInOne() ? 1.5 : 1.0;
    return Math.round(baseSpacing * scale);
  }

  /**
   * 更新设备信息（屏幕旋转时调用）
   */
  async updateDeviceInfo() {
    await this.initDeviceInfo();
  }
}