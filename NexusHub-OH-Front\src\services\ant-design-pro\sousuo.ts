// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 搜索应用 使用Elasticsearch搜索应用，支持关键词、分类、标签等多种过滤条件 GET /search/apps */
export async function getSearchApps(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSearchAppsParams,
  options?: { [key: string]: any },
) {
  return request<API.Response & { data?: API.SearchResponse }>('/search/apps', {
    method: 'GET',
    params: {
      // page has a default value: 1
      page: '1',
      // page_size has a default value: 20
      page_size: '20',
      ...params,
    },
    ...(options || {}),
  });
}

/** 初始化搜索索引 创建Elasticsearch索引并同步所有应用数据（管理员功能） POST /search/initialize */
export async function postSearchInitialize(options?: { [key: string]: any }) {
  return request<API.Response>('/search/initialize', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 初始化所有搜索索引 初始化应用、用户、评论、标签的Elasticsearch索引（管理员功能） POST /search/initialize-all */
export async function postSearchInitializeAll(options?: { [key: string]: any }) {
  return request<API.Response>('/search/initialize-all', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 搜索评论 使用Elasticsearch搜索评论，支持评论内容、用户、应用等搜索 GET /search/reviews */
export async function getSearchReviews(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSearchReviewsParams,
  options?: { [key: string]: any },
) {
  return request<API.Response & { data?: API.ReviewSearchResponse }>('/search/reviews', {
    method: 'GET',
    params: {
      // page has a default value: 1
      page: '1',
      // page_size has a default value: 20
      page_size: '20',
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取搜索建议 根据输入的关键词获取搜索建议 GET /search/suggestions */
export async function getSearchSuggestions(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSearchSuggestionsParams,
  options?: { [key: string]: any },
) {
  return request<API.Response & { data?: string[] }>('/search/suggestions', {
    method: 'GET',
    params: {
      // limit has a default value: 10
      limit: '10',
      ...params,
    },
    ...(options || {}),
  });
}

/** 同步搜索索引 重新同步所有应用数据到Elasticsearch（管理员功能） POST /search/sync */
export async function postSearchSync(options?: { [key: string]: any }) {
  return request<API.Response>('/search/sync', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 搜索标签 使用Elasticsearch搜索标签，支持标签名称、描述等搜索 GET /search/tags */
export async function getSearchTags(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSearchTagsParams,
  options?: { [key: string]: any },
) {
  return request<API.Response & { data?: API.TagSearchResponse }>('/search/tags', {
    method: 'GET',
    params: {
      // page has a default value: 1
      page: '1',
      // page_size has a default value: 20
      page_size: '20',
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取标签统计 获取标签使用统计信息（管理员功能） GET /search/tags/stats */
export async function getSearchTagsStats(options?: { [key: string]: any }) {
  return request<API.Response & { data?: API.TagStatsDocument[] }>('/search/tags/stats', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 标签自动补全 根据输入前缀提供标签建议 GET /search/tags/suggest */
export async function getSearchTagsSuggest(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSearchTagsSuggestParams,
  options?: { [key: string]: any },
) {
  return request<API.Response & { data?: string[] }>('/search/tags/suggest', {
    method: 'GET',
    params: {
      // limit has a default value: 10
      limit: '10',
      ...params,
    },
    ...(options || {}),
  });
}

/** 搜索用户 使用Elasticsearch搜索用户，支持用户名、邮箱、开发者信息等搜索（管理员功能） GET /search/users */
export async function getSearchUsers(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getSearchUsersParams,
  options?: { [key: string]: any },
) {
  return request<API.Response & { data?: API.UserSearchResponse }>('/admin/search/users', {
    method: 'GET',
    params: {
      // page has a default value: 1
      page: '1',
      // page_size has a default value: 20
      page_size: '20',
      ...params,
    },
    ...(options || {}),
  });
}
