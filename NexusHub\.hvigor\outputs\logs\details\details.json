{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug"}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"CreateModuleInfo": 4226400, "ConfigureCmake": 545800, "PreCheckSyscap": 1379800, "ProcessIntegratedHsp": 7029200, "BuildNativeWithCmake": 753100, "ProcessStartupConfig": 14943300, "BuildNativeWithNinja": 3698600, "BuildJS": 12293100, "CollectDebugSymbol": 14217800, "assembleHap": 242100}}, "CONFIG_PROPERTIES": {"enableSignTask": true, "skipNativeIncremental": false, "hvigor_keepDependency": true}, "TOTAL_TIME": 1110540700, "BUILD_ID": "202506182011595390"}}