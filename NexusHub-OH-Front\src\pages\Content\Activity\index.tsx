import { PageContainer } from '@ant-design/pro-components';
import { Card, Table, Button, Tag, Badge, Space, Modal, Form, Input, Upload, Select, message, DatePicker, Tabs, Row, Col, Tooltip, Switch } from 'antd';
import { PlusOutlined, UploadOutlined, DeleteOutlined, EditOutlined, EyeOutlined, CalendarOutlined, AppstoreOutlined, LinkOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { useRequest } from '@umijs/max';
import type { ColumnsType } from 'antd/es/table';
import type { UploadFile } from 'antd/es/upload/interface';
import moment from 'moment';
import { RangePickerProps } from 'antd/es/date-picker';

interface ActivityItem {
  id: string;
  title: string;
  description: string;
  bannerUrl: string;
  startTime: string;
  endTime: string;
  status: 'draft' | 'active' | 'inactive' | 'expired';
  type: 'promotion' | 'event' | 'announcement';
  targetType: 'all' | 'app' | 'category';
  targetId?: string;
  discount?: number;
  createdTime: string;
  createdBy: string;
  updatedTime?: string;
  updatedBy?: string;
}

// 模拟数据获取函数
const fetchActivityList = async (params: any) => {
  // 实际项目中应该调用API
  console.log('Fetching activity list with params:', params);
  
  // 模拟数据
  const mockData: ActivityItem[] = [
    {
      id: '1',
      title: '618促销活动',
      description: '年中大促，全场应用最低5折起',
      bannerUrl: 'https://gw.alipayobjects.com/zos/rmsportal/JiqGstEfoWAOHiTxclqi.png',
      startTime: '2023-06-01 00:00:00',
      endTime: '2023-06-18 23:59:59',
      status: 'active',
      type: 'promotion',
      targetType: 'all',
      discount: 50,
      createdTime: '2023-05-15 10:00:00',
      createdBy: 'admin',
      updatedTime: '2023-05-20 14:30:00',
      updatedBy: 'admin',
    },
    {
      id: '2',
      title: '开发者大会',
      description: '探讨应用开发的未来趋势',
      bannerUrl: 'https://gw.alipayobjects.com/zos/rmsportal/JiqGstEfoWAOHiTxclqi.png',
      startTime: '2023-07-15 09:00:00',
      endTime: '2023-07-16 18:00:00',
      status: 'draft',
      type: 'event',
      targetType: 'all',
      createdTime: '2023-05-10 11:20:00',
      createdBy: 'admin',
    },
    {
      id: '3',
      title: '游戏专区优惠',
      description: '游戏类应用限时优惠',
      bannerUrl: 'https://gw.alipayobjects.com/zos/rmsportal/JiqGstEfoWAOHiTxclqi.png',
      startTime: '2023-05-01 00:00:00',
      endTime: '2023-05-31 23:59:59',
      status: 'expired',
      type: 'promotion',
      targetType: 'category',
      targetId: 'cat001',
      discount: 30,
      createdTime: '2023-04-20 09:15:00',
      createdBy: 'admin',
      updatedTime: '2023-04-25 16:40:00',
      updatedBy: 'admin',
    },
    {
      id: '4',
      title: '系统维护公告',
      description: '系统将于2023年6月10日凌晨2点至4点进行维护升级',
      bannerUrl: 'https://gw.alipayobjects.com/zos/rmsportal/JiqGstEfoWAOHiTxclqi.png',
      startTime: '2023-06-05 00:00:00',
      endTime: '2023-06-10 04:00:00',
      status: 'active',
      type: 'announcement',
      targetType: 'all',
      createdTime: '2023-06-01 10:30:00',
      createdBy: 'admin',
    },
  ];

  // 根据类型过滤
  const typeFilteredData = params.type ? mockData.filter(item => item.type === params.type) : mockData;
  
  // 根据状态过滤
  const statusFilteredData = params.status ? typeFilteredData.filter(item => item.status === params.status) : typeFilteredData;
  
  // 根据关键词过滤
  const keywordFilteredData = params.keyword
    ? statusFilteredData.filter(item => 
        item.title.toLowerCase().includes(params.keyword.toLowerCase()) ||
        item.description.toLowerCase().includes(params.keyword.toLowerCase())
      )
    : statusFilteredData;

  return { data: keywordFilteredData, total: keywordFilteredData.length };
};

const ActivityManagement: React.FC = () => {
  const [searchParams, setSearchParams] = useState({});
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [currentActivity, setCurrentActivity] = useState<ActivityItem | null>(null);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [editForm] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10 });

  const { data, loading, refresh } = useRequest(() => fetchActivityList({
    ...searchParams,
    ...pagination,
  }), {
    refreshDeps: [searchParams, pagination],
  });

  const handleSearch = (values: any) => {
    setSearchParams(values);
    setPagination({ ...pagination, current: 1 });
  };

  const handleTableChange = (newPagination: any) => {
    setPagination({
      current: newPagination.current,
      pageSize: newPagination.pageSize,
    });
  };

  const handleAdd = () => {
    setCurrentActivity(null);
    editForm.resetFields();
    setFileList([]);
    setEditModalVisible(true);
  };

  const handleEdit = (record: ActivityItem) => {
    setCurrentActivity(record);
    editForm.setFieldsValue({
      title: record.title,
      description: record.description,
      type: record.type,
      targetType: record.targetType,
      targetId: record.targetId,
      discount: record.discount,
      status: record.status,
      timeRange: [moment(record.startTime), moment(record.endTime)],
    });
    setFileList(record.bannerUrl ? [{
      uid: '-1',
      name: 'banner.png',
      status: 'done',
      url: record.bannerUrl,
    }] : []);
    setEditModalVisible(true);
  };

  const handlePreview = (imageUrl: string) => {
    setPreviewImage(imageUrl);
    setPreviewVisible(true);
  };

  const handleStatusChange = (checked: boolean, record: ActivityItem) => {
    console.log(`Change status of ${record.id} to ${checked ? 'active' : 'inactive'}`);
    // 实际项目中应该调用API
    message.success(`状态已更新为${checked ? '启用' : '禁用'}`);
    refresh();
  };

  const handleDelete = (record: ActivityItem) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除「${record.title}」吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        console.log(`Delete activity ${record.id}`);
        // 实际项目中应该调用API
        message.success('删除成功');
        refresh();
      },
    });
  };

  const handleEditSubmit = () => {
    editForm.validateFields().then(values => {
      console.log('Form values:', values);
      // 实际项目中应该调用API
      message.success(currentActivity ? '更新成功' : '添加成功');
      setEditModalVisible(false);
      refresh();
    });
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'draft':
        return <Tag color="default">草稿</Tag>;
      case 'active':
        return <Tag color="success">启用</Tag>;
      case 'inactive':
        return <Tag color="warning">禁用</Tag>;
      case 'expired':
        return <Tag color="error">已过期</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  const getTypeTag = (type: string) => {
    switch (type) {
      case 'promotion':
        return <Tag color="blue">促销</Tag>;
      case 'event':
        return <Tag color="purple">活动</Tag>;
      case 'announcement':
        return <Tag color="orange">公告</Tag>;
      default:
        return <Tag>{type}</Tag>;
    }
  };

  const getTargetTypeText = (targetType: string, targetId?: string) => {
    switch (targetType) {
      case 'all':
        return '全部';
      case 'app':
        return `应用 (ID: ${targetId || 'N/A'})`;
      case 'category':
        return `分类 (ID: ${targetId || 'N/A'})`;
      default:
        return targetType;
    }
  };

  const columns: ColumnsType<ActivityItem> = [
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => (
        <a onClick={() => handleEdit(record)}>{text}</a>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => getTypeTag(type),
      filters: [
        { text: '促销', value: 'promotion' },
        { text: '活动', value: 'event' },
        { text: '公告', value: 'announcement' },
      ],
      onFilter: (value, record) => record.type === value,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => (
        <Space>
          {getStatusTag(status)}
          {status !== 'expired' && (
            <Switch 
              checked={status === 'active'} 
              onChange={(checked) => handleStatusChange(checked, record)}
              size="small"
              disabled={status === 'expired'}
            />
          )}
        </Space>
      ),
      filters: [
        { text: '草稿', value: 'draft' },
        { text: '启用', value: 'active' },
        { text: '禁用', value: 'inactive' },
        { text: '已过期', value: 'expired' },
      ],
      onFilter: (value, record) => record.status === value,
    },
    {
      title: '时间范围',
      key: 'timeRange',
      render: (_, record) => (
        <div>
          <div>开始: {record.startTime}</div>
          <div>结束: {record.endTime}</div>
        </div>
      ),
    },
    {
      title: '目标',
      key: 'target',
      render: (_, record) => getTargetTypeText(record.targetType, record.targetId),
    },
    {
      title: '折扣',
      dataIndex: 'discount',
      key: 'discount',
      render: (discount) => discount ? `${discount}%` : '-',
    },
    {
      title: '创建信息',
      key: 'created',
      render: (_, record) => (
        <div>
          <div>创建人: {record.createdBy}</div>
          <div>创建时间: {record.createdTime}</div>
          {record.updatedTime && (
            <div>更新时间: {record.updatedTime}</div>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <a onClick={() => handleEdit(record)}><EditOutlined /> 编辑</a>
          <a onClick={() => handleDelete(record)}><DeleteOutlined /> 删除</a>
          <a onClick={() => handlePreview(record.bannerUrl)}><EyeOutlined /> 预览</a>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer
      header={{
        title: '活动策划管理',
      }}
    >
      <Card bordered={false}>
        <Form layout="inline" onFinish={handleSearch} style={{ marginBottom: 24 }}>
          <Form.Item name="keyword" label="关键词">
            <Input placeholder="标题/描述" style={{ width: 200 }} />
          </Form.Item>
          <Form.Item name="type" label="类型">
            <Select
              placeholder="选择类型"
              style={{ width: 120 }}
              allowClear
              options={[
                { value: 'promotion', label: '促销' },
                { value: 'event', label: '活动' },
                { value: 'announcement', label: '公告' },
              ]}
            />
          </Form.Item>
          <Form.Item name="status" label="状态">
            <Select
              placeholder="选择状态"
              style={{ width: 120 }}
              allowClear
              options={[
                { value: 'draft', label: '草稿' },
                { value: 'active', label: '启用' },
                { value: 'inactive', label: '禁用' },
                { value: 'expired', label: '已过期' },
              ]}
            />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit">
              搜索
            </Button>
          </Form.Item>
        </Form>

        <div style={{ marginBottom: 16 }}>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            添加活动
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={data?.data}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            total: data?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          onChange={handleTableChange}
        />
      </Card>

      {/* 编辑弹窗 */}
      <Modal
        title={currentActivity ? '编辑活动' : '添加活动'}
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
        width={700}
      >
        <Form form={editForm} layout="vertical" onFinish={handleEditSubmit}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="title"
                label="活动标题"
                rules={[{ required: true, message: '请输入活动标题' }]}
              >
                <Input placeholder="请输入活动标题" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="活动类型"
                rules={[{ required: true, message: '请选择活动类型' }]}
              >
                <Select placeholder="请选择活动类型">
                  <Select.Option value="promotion">促销</Select.Option>
                  <Select.Option value="event">活动</Select.Option>
                  <Select.Option value="announcement">公告</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="活动描述"
            rules={[{ required: true, message: '请输入活动描述' }]}
          >
            <Input.TextArea rows={4} placeholder="请输入活动描述" />
          </Form.Item>

          <Form.Item
            name="banner"
            label="活动横幅"
            rules={[{ required: true, message: '请上传活动横幅' }]}
          >
            <Upload
              listType="picture-card"
              fileList={fileList}
              beforeUpload={() => false}
              onChange={({ fileList }) => setFileList(fileList)}
              onPreview={() => handlePreview(fileList[0]?.url || fileList[0]?.thumbUrl || '')}
              maxCount={1}
            >
              {fileList.length === 0 && (
                <div>
                  <UploadOutlined />
                  <div style={{ marginTop: 8 }}>上传图片</div>
                </div>
              )}
            </Upload>
          </Form.Item>

          <Form.Item
            name="timeRange"
            label="活动时间"
            rules={[{ required: true, message: '请选择活动时间范围' }]}
          >
            <DatePicker.RangePicker 
              showTime 
              style={{ width: '100%' }} 
              placeholder={['开始时间', '结束时间']}
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="targetType"
                label="目标类型"
                rules={[{ required: true, message: '请选择目标类型' }]}
              >
                <Select placeholder="请选择目标类型">
                  <Select.Option value="all">全部</Select.Option>
                  <Select.Option value="app">应用</Select.Option>
                  <Select.Option value="category">分类</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) => prevValues.targetType !== currentValues.targetType}
              >
                {({ getFieldValue }) => {
                  const targetType = getFieldValue('targetType');
                  if (targetType === 'app') {
                    return (
                      <Form.Item
                        name="targetId"
                        label="选择应用"
                        rules={[{ required: true, message: '请选择应用' }]}
                      >
                        <Select placeholder="请选择应用">
                          <Select.Option value="app001">超级游戏A</Select.Option>
                          <Select.Option value="app002">效率工具B</Select.Option>
                          <Select.Option value="app003">社交平台C</Select.Option>
                        </Select>
                      </Form.Item>
                    );
                  }
                  if (targetType === 'category') {
                    return (
                      <Form.Item
                        name="targetId"
                        label="选择分类"
                        rules={[{ required: true, message: '请选择分类' }]}
                      >
                        <Select placeholder="请选择分类">
                          <Select.Option value="cat001">游戏</Select.Option>
                          <Select.Option value="cat002">工具</Select.Option>
                          <Select.Option value="cat003">社交</Select.Option>
                        </Select>
                      </Form.Item>
                    );
                  }
                  return null;
                }}
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type}
          >
            {({ getFieldValue }) => {
              const type = getFieldValue('type');
              if (type === 'promotion') {
                return (
                  <Form.Item
                    name="discount"
                    label="折扣百分比"
                    rules={[{ required: true, message: '请输入折扣百分比' }]}
                  >
                    <Input type="number" min={1} max={100} addonAfter="%" placeholder="例如：80表示8折" />
                  </Form.Item>
                );
              }
              return null;
            }}
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select placeholder="请选择状态">
              <Select.Option value="draft">草稿</Select.Option>
              <Select.Option value="active">启用</Select.Option>
              <Select.Option value="inactive">禁用</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                提交
              </Button>
              <Button onClick={() => setEditModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 图片预览 */}
      <Modal
        open={previewVisible}
        footer={null}
        onCancel={() => setPreviewVisible(false)}
      >
        <img alt="预览" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </PageContainer>
  );
};

export default ActivityManagement;