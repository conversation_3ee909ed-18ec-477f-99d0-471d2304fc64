import type { ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Badge, Card, Descriptions, Divider, Avatar, Row, Col, Statistic, Tag } from 'antd';
import { UserOutlined, MailOutlined, PhoneOutlined, HomeOutlined } from '@ant-design/icons';
import type { FC } from 'react';
import React from 'react';
import useStyles from './style.style';

interface UserProfile {
  id: string;
  name: string;
  username: string;
  email: string;
  phone: string;
  address: string;
  avatar: string;
  registerTime: string;
  lastLogin: string;
  tags: string[];
  bio: string;
}

interface UserActivity {
  id: string;
  action: string;
  time: string;
  ip: string;
  status: 'success' | 'processing' | 'error';
}

interface ProfileData {
  userProfile: UserProfile;
  userActivities: UserActivity[];
}

// 模拟数据获取函数
const queryUserProfile = (): Promise<ProfileData> => {
  return Promise.resolve({
    userProfile: {
      id: 'U10001',
      name: '张小明',
      username: 'z<PERSON><PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
      phone: '13800138000',
      address: '浙江省杭州市西湖区万塘路18号',
      avatar: 'https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png',
      registerTime: '2023-01-05 10:30:00',
      lastLogin: '2023-06-20 15:45:30',
      tags: ['高级用户', '开发者', '设计师'],
      bio: '资深产品设计师，拥有10年UI/UX设计经验，专注于移动应用和Web平台的用户体验设计。'
    },
    userActivities: [
      {
        id: '1',
        action: '登录系统',
        time: '2023-06-20 15:45:30',
        ip: '***********',
        status: 'success'
      },
      {
        id: '2',
        action: '更新个人资料',
        time: '2023-06-18 11:20:15',
        ip: '***********',
        status: 'success'
      },
      {
        id: '3',
        action: '下载应用',
        time: '2023-06-15 09:35:42',
        ip: '***********',
        status: 'success'
      },
      {
        id: '4',
        action: '修改密码',
        time: '2023-06-10 14:22:18',
        ip: '***********',
        status: 'success'
      },
      {
        id: '5',
        action: '登录失败',
        time: '2023-06-05 08:15:30',
        ip: '***********',
        status: 'error'
      }
    ]
  });
};

const activityColumns: ProColumns<UserActivity>[] = [
  {
    title: '操作时间',
    dataIndex: 'time',
    key: 'time',
  },
  {
    title: '用户操作',
    dataIndex: 'action',
    key: 'action',
  },
  {
    title: 'IP地址',
    dataIndex: 'ip',
    key: 'ip',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    render: (_, entity) => {
      if (entity.status === 'success') {
        return <Badge status="success" text="成功" />;
      }
      if (entity.status === 'processing') {
        return <Badge status="processing" text="进行中" />;
      }
      return <Badge status="error" text="失败" />;
    },
  },
];

const Basic: FC = () => {
  const { styles } = useStyles();
  const { data, loading } = useRequest(() => {
    return queryUserProfile();
  });
  
  // 确保数据存在
  const profileData: ProfileData = data || {
    userProfile: {} as UserProfile,
    userActivities: [] as UserActivity[],
  };

  const { userProfile, userActivities } = profileData;

  return (
    <PageContainer>
      <Card bordered={false}>
        <Row gutter={24}>
          <Col span={8}>
            <Card bordered={false} style={{ textAlign: 'center' }}>
              <Avatar 
                size={120} 
                src={userProfile.avatar} 
                icon={<UserOutlined />} 
                style={{ marginBottom: 20 }}
              />
              <h2>{userProfile.name}</h2>
              <p>@{userProfile.username}</p>
              <div style={{ margin: '16px 0' }}>
                {userProfile.tags?.map((tag: string) => (
                  <Tag key={tag} color="blue">{tag}</Tag>
                ))}
              </div>
              <p>{userProfile.bio}</p>
            </Card>
            <Card title="统计数据" style={{ marginTop: 24 }}>
              <Row gutter={12}>
                <Col span={8}>
                  <Statistic title="应用数" value={18} />
                </Col>
                <Col span={8}>
                  <Statistic title="积分" value={2580} />
                </Col>
                <Col span={8}>
                  <Statistic title="等级" value={5} suffix="级" />
                </Col>
              </Row>
            </Card>
          </Col>
          <Col span={16}>
            <Descriptions title="用户信息" bordered>
              <Descriptions.Item label="用户ID">{userProfile.id}</Descriptions.Item>
              <Descriptions.Item label="用户名">{userProfile.username}</Descriptions.Item>
              <Descriptions.Item label="姓名">{userProfile.name}</Descriptions.Item>
              <Descriptions.Item label="邮箱">
                <MailOutlined style={{ marginRight: 8 }} />
                {userProfile.email}
              </Descriptions.Item>
              <Descriptions.Item label="手机号" span={2}>
                <PhoneOutlined style={{ marginRight: 8 }} />
                {userProfile.phone}
              </Descriptions.Item>
              <Descriptions.Item label="地址" span={3}>
                <HomeOutlined style={{ marginRight: 8 }} />
                {userProfile.address}
              </Descriptions.Item>
              <Descriptions.Item label="注册时间">{userProfile.registerTime}</Descriptions.Item>
              <Descriptions.Item label="最后登录时间" span={2}>{userProfile.lastLogin}</Descriptions.Item>
            </Descriptions>
            
            <Divider style={{ margin: '32px 0' }} />
            
            <div className={styles.title}>用户活动记录</div>
            <ProTable
              style={{ marginBottom: 24 }}
              pagination={false}
              search={false}
              loading={loading}
              options={false}
              toolBarRender={false}
              dataSource={userActivities}
              columns={activityColumns}
              rowKey="id"
            />
          </Col>
        </Row>
      </Card>
    </PageContainer>
  );
};

export default Basic;
