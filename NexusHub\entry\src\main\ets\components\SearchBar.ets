import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';
import { LengthMetrics } from '@kit.ArkUI';
import { hilog } from '@kit.PerformanceAnalysisKit';

/**
 * 搜索栏组件
 */
@Component
export struct SearchBar {
  @State searchText: string = '';
  @Prop placeholder: string = '搜索应用';
  @Prop showCancelButton: boolean = false;
  @Prop showFilterButton: boolean = false;
  @Prop isLoading: boolean = false;
  onSearch?: (keyword: string) => void;
  onTextChange?: (text: string) => void;
  onCancel?: () => void;
  onFilter?: () => void;
  onSearchFocus?: () => void;
  onSearchBlur?: () => void;
  onSearchClick?: () => void;

  private deviceUtils = DeviceUtils.getInstance();

  /**
   * 清除搜索文本
   */
  private clearSearch() {
    this.searchText = '';
    this.onTextChange?.(this.searchText);
  }

  /**
   * 执行搜索
   */
  private performSearch() {
    if (this.searchText.trim()) {
      this.onSearch?.(this.searchText.trim());
    }
  }

  build() {
    Row({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.SMALL) }) {
      // 搜索输入框容器
      Row({ space: '8vp' }) {
        // 搜索图标
        Image($r('app.media.icons'))
          .width(20)
          .height(20)
          .objectFit(ImageFit.Contain)
          .onClick(() => {
            this.onSearchClick?.();
          })

        // 搜索输入框
        TextInput({ 
          placeholder: this.placeholder,
          text: this.searchText 
        })
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontColor($r('sys.color.ohos_id_color_text_primary'))
          .backgroundColor('transparent')
          .border({ width: 0 })
          .layoutWeight(1)
          .onChange((value: string) => {
            this.searchText = value;
            this.onTextChange?.(value);
          })
          .onSubmit(() => {
            this.performSearch();
          })
          .onClick(() => {
            this.onSearchClick?.();
          })
          .onBlur(() => {
          this.onSearchBlur?.();
        })

        // 清除按钮
        if (this.searchText.length > 0) {
          Image($r('app.media.icons'))
            .width(20)
            .height(20)
            .objectFit(ImageFit.Contain)
            .onClick(() => {
              this.clearSearch();
            })
        }

        // 加载指示器
        if (this.isLoading) {
          LoadingProgress()
            .width(20)
            .height(20)
            .color(Constants.COLORS.PRIMARY)
        }
      }
      .width('100%')
      .height(40)
      .padding({ left: '12vp', right: '12vp' })
      .backgroundColor($r('sys.color.ohos_id_color_background'))
      .borderRadius(Constants.BORDER_RADIUS.LARGE)
      .layoutWeight(1)


      // 筛选按钮
      if (this.showFilterButton) {
        Button() {
          Text('⚙')
            .fontSize(20)
            .fontColor($r('sys.color.ohos_id_color_text_secondary'))
        }
        .width(40)
        .height(40)
        .backgroundColor($r('sys.color.ohos_id_color_background'))
        .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
        .border({
          width: 1,
          color: $r('sys.color.ohos_id_color_list_separator')
        })
        .onClick(() => {
          this.onFilter?.();
        })
      }

      // 取消按钮
      if (this.showCancelButton) {
        Button('取消')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontColor($r('sys.color.ohos_id_color_text_secondary'))
          .backgroundColor('transparent')
          .border({ width: 0 })
          .onClick(() => {
            this.clearSearch();
            this.onCancel?.();
          })
      }
    }
    .width('100%')
    .padding({
      left: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM),
      right: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM),
      top: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.SMALL),
      bottom: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.SMALL)
    })
  }
}

/**
 * 搜索建议组件
 */
@Component
export struct SearchSuggestions {
  @Prop suggestions: string[] = [];
  @Prop hotKeywords: string[] = [];
  @Prop searchHistory: string[] = [];
  @Prop showHistory: boolean = true;
  onSuggestionClick?: (keyword: string) => void;
  onClearHistory?: () => void | Promise<void>;

  private deviceUtils = DeviceUtils.getInstance();

  build() {
    Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) }) {
      // 搜索建议
      if (this.suggestions.length > 0) {
        Column({ space: '8vp' }) {
          Text('搜索建议')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor($r('sys.color.ohos_id_color_text_primary'))
            .fontWeight(FontWeight.Medium)
            .alignSelf(ItemAlign.Start)

          ForEach(this.suggestions, (suggestion: string) => {
            Row() {
              Text('🔍')
                .fontSize(16)
                .fontColor($r('sys.color.ohos_id_color_text_hint'))
                .margin({ right: '8vp' })

              Text(suggestion)
                .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
                .fontColor($r('sys.color.ohos_id_color_text_primary'))
                .layoutWeight(1)
            }
            .width('100%')
            .padding('12vp')
            .onClick(() => {
              this.onSuggestionClick?.(suggestion);
            })
          })
        }
        .width('100%')
        .alignItems(HorizontalAlign.Start)
      }

      // 热门搜索
      if (this.hotKeywords.length > 0) {
        Column({ space: '8vp' }) {
          Text('热门搜索')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor($r('sys.color.ohos_id_color_text_primary'))
            .fontWeight(FontWeight.Medium)
            .alignSelf(ItemAlign.Start)

          Flex({ wrap: FlexWrap.Wrap, space: { main: LengthMetrics.vp(8), cross: LengthMetrics.vp(8) } }) {
            ForEach(this.hotKeywords, (keyword: string) => {
              Text(keyword)
                .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
                .fontColor($r('sys.color.ohos_id_color_text_secondary'))
                .padding({ left: '12vp', right: '12vp', top: '6vp', bottom: '6vp' })
                .backgroundColor($r('sys.color.ohos_id_color_background'))
                .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
                .onClick(() => {
                  this.onSuggestionClick?.(keyword);
                })
            })
          }
          .width('100%')
        }
        .width('100%')
        .alignItems(HorizontalAlign.Start)
      }

      // 搜索历史
      if (this.showHistory && this.searchHistory.length > 0) {
        Column({ space: '8vp' }) {
          Row() {
            Text('搜索历史')
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
              .fontColor($r('sys.color.ohos_id_color_text_primary'))
              .fontWeight(FontWeight.Medium)
              .layoutWeight(1)

            Button('清除')
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
              .fontColor($r('sys.color.ohos_id_color_text_hint'))
              .backgroundColor('transparent')
              .border({ width: 0 })
              .onClick(() => {
                this.onClearHistory?.();
              })
          }
          .width('100%')

          ForEach(this.searchHistory, (history: string) => {
            Row() {
              Image($r('app.media.ic_history'))
                .width(16)
                .height(16)
                .objectFit(ImageFit.Contain)
                .fillColor($r('sys.color.ohos_id_color_text_hint'))
                .margin({ right: '8vp' })

              Text(history)
                .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
                .fontColor($r('sys.color.ohos_id_color_text_secondary'))
                .layoutWeight(1)
            }
            .width('100%')
            .padding('12vp')
            .onClick(() => {
              this.onSuggestionClick?.(history);
            })
          })
        }
        .width('100%')
        .alignItems(HorizontalAlign.Start)
      }
    }
    .width('100%')
    .padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
    .backgroundColor($r('sys.color.ohos_id_color_background'))
  }
}