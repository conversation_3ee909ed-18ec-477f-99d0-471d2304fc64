package controllers

import (
	"strconv"
	"time"

	"nexushub-oh-back/internal/models"
	"nexushub-oh-back/pkg/response"
	"nexushub-oh-back/pkg/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// BrowseHistoryController 浏览历史控制器
type BrowseHistoryController struct {
	DB *gorm.DB
}

// NewBrowseHistoryController 创建浏览历史控制器
func NewBrowseHistoryController(db *gorm.DB) *BrowseHistoryController {
	return &BrowseHistoryController{
		DB: db,
	}
}

// CreateBrowseHistory 创建浏览记录
// @Summary 创建浏览记录
// @Description 记录用户浏览应用的行为
// @Tags 浏览历史
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param request body CreateBrowseHistoryRequest true "浏览记录信息"
// @Success 200 {object} response.Response{data=models.BrowseHistoryResponse} "创建成功"
// @Failure 400 {object} response.ErrorResponse "参数错误"
// @Failure 401 {object} response.ErrorResponse "未授权"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/browse-history [post]
func (c *BrowseHistoryController) CreateBrowseHistory(ctx *gin.Context) {
	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "用户未登录")
		return
	}

	var req CreateBrowseHistoryRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	// 验证应用是否存在
	var app models.Application
	if err := c.DB.First(&app, req.AppID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.BadRequest(ctx, "应用不存在")
			return
		}
		response.InternalServerError(ctx, "查询应用失败")
		return
	}

	// 获取客户端信息
	clientIP := ctx.ClientIP()
	userAgent := utils.GetUserAgent(ctx)
	referrer := utils.GetReferer(ctx)

	// 创建浏览记录
	browseHistory := models.BrowseHistory{
		UserID:     userID.(uint),
		AppID:      req.AppID,
		BrowseTime: time.Now(),
		Duration:   req.Duration,
		IPAddress:  clientIP,
		UserAgent:  userAgent,
		Referrer:   referrer,
		ActionType: req.ActionType,
		DeviceType: req.DeviceType,
		Platform:   req.Platform,
	}

	if err := c.DB.Create(&browseHistory).Error; err != nil {
		response.InternalServerError(ctx, "创建浏览记录失败")
		return
	}

	// 预加载关联数据
	if err := c.DB.Preload("App").Preload("User").First(&browseHistory, browseHistory.ID).Error; err != nil {
		response.InternalServerError(ctx, "查询浏览记录失败")
		return
	}

	// 转换为响应格式
	resp := convertToBrowseHistoryResponse(browseHistory)
	response.Success(ctx, resp)
}

// GetMyBrowseHistory 获取我的浏览历史
// @Summary 获取我的浏览历史
// @Description 获取当前用户的浏览历史记录，支持分页和筛选
// @Tags 浏览历史
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Param app_id query int false "应用ID"
// @Param action_type query string false "操作类型"
// @Param device_type query string false "设备类型"
// @Param platform query string false "平台"
// @Param start_date query string false "开始日期 (YYYY-MM-DD)"
// @Param end_date query string false "结束日期 (YYYY-MM-DD)"
// @Success 200 {object} response.PageResponse{data=[]models.BrowseHistoryResponse} "获取成功"
// @Failure 401 {object} response.ErrorResponse "未授权"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/browse-history [get]
func (c *BrowseHistoryController) GetMyBrowseHistory(ctx *gin.Context) {
	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "用户未登录")
		return
	}

	// 获取分页参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}
	offset := (page - 1) * limit

	// 构建查询条件
	query := c.DB.Model(&models.BrowseHistory{}).Where("user_id = ?", userID)

	// 应用筛选条件
	if appIDStr := ctx.Query("app_id"); appIDStr != "" {
		if appID, err := strconv.Atoi(appIDStr); err == nil {
			query = query.Where("app_id = ?", appID)
		}
	}

	if actionType := ctx.Query("action_type"); actionType != "" {
		query = query.Where("action_type = ?", actionType)
	}

	if deviceType := ctx.Query("device_type"); deviceType != "" {
		query = query.Where("device_type = ?", deviceType)
	}

	if platform := ctx.Query("platform"); platform != "" {
		query = query.Where("platform = ?", platform)
	}

	// 日期范围筛选
	if startDate := ctx.Query("start_date"); startDate != "" {
		if t, err := time.Parse("2006-01-02", startDate); err == nil {
			query = query.Where("browse_time >= ?", t)
		}
	}

	if endDate := ctx.Query("end_date"); endDate != "" {
		if t, err := time.Parse("2006-01-02", endDate); err == nil {
			// 结束日期包含当天，所以加一天
			query = query.Where("browse_time < ?", t.AddDate(0, 0, 1))
		}
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		response.InternalServerError(ctx, "查询浏览记录总数失败")
		return
	}

	// 获取数据
	var browseHistories []models.BrowseHistory
	if err := query.Preload("App").Preload("User").
		Order("browse_time DESC").
		Offset(offset).Limit(limit).
		Find(&browseHistories).Error; err != nil {
		response.InternalServerError(ctx, "查询浏览记录失败")
		return
	}

	// 转换为响应格式
	var result []models.BrowseHistoryResponse
	for _, history := range browseHistories {
		result = append(result, convertToBrowseHistoryResponse(history))
	}

	response.SuccessWithPagination(ctx, result, total, page, limit)
}

// DeleteBrowseHistory 删除浏览记录
// @Summary 删除浏览记录
// @Description 删除指定的浏览记录（只能删除自己的记录）
// @Tags 浏览历史
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "浏览记录ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.ErrorResponse "参数错误"
// @Failure 401 {object} response.ErrorResponse "未授权"
// @Failure 403 {object} response.ErrorResponse "无权限"
// @Failure 404 {object} response.ErrorResponse "记录不存在"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/browse-history/{id} [delete]
func (c *BrowseHistoryController) DeleteBrowseHistory(ctx *gin.Context) {
	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "用户未登录")
		return
	}

	// 获取记录ID
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.BadRequest(ctx, "无效的记录ID")
		return
	}

	// 查询记录是否存在且属于当前用户
	var browseHistory models.BrowseHistory
	if err := c.DB.Where("id = ? AND user_id = ?", id, userID).First(&browseHistory).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(ctx, "浏览记录不存在")
			return
		}
		response.InternalServerError(ctx, "查询浏览记录失败")
		return
	}

	// 删除记录
	if err := c.DB.Delete(&browseHistory).Error; err != nil {
		response.InternalServerError(ctx, "删除浏览记录失败")
		return
	}

	response.Success(ctx, nil)
}

// ClearMyBrowseHistory 清空我的浏览历史
// @Summary 清空我的浏览历史
// @Description 清空当前用户的所有浏览历史记录
// @Tags 浏览历史
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} response.Response "清空成功"
// @Failure 401 {object} response.ErrorResponse "未授权"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/browse-history/clear [delete]
func (c *BrowseHistoryController) ClearMyBrowseHistory(ctx *gin.Context) {
	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "用户未登录")
		return
	}

	// 删除用户的所有浏览记录
	if err := c.DB.Where("user_id = ?", userID).Delete(&models.BrowseHistory{}).Error; err != nil {
		response.InternalServerError(ctx, "清空浏览记录失败")
		return
	}

	response.Success(ctx, nil)
}

// GetBrowseStats 获取浏览统计
// @Summary 获取浏览统计
// @Description 获取当前用户的浏览统计信息
// @Tags 浏览历史
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param period query string false "统计周期" Enums(day,week,month,year) default(week)
// @Success 200 {object} response.Response{data=BrowseStatsResponse} "获取成功"
// @Failure 401 {object} response.ErrorResponse "未授权"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/browse-history/stats [get]
func (c *BrowseHistoryController) GetBrowseStats(ctx *gin.Context) {
	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "用户未登录")
		return
	}

	period := ctx.DefaultQuery("period", "week")

	// 计算时间范围
	now := time.Now()
	var startTime time.Time
	switch period {
	case "day":
		startTime = now.AddDate(0, 0, -1)
	case "week":
		startTime = now.AddDate(0, 0, -7)
	case "month":
		startTime = now.AddDate(0, -1, 0)
	case "year":
		startTime = now.AddDate(-1, 0, 0)
	default:
		startTime = now.AddDate(0, 0, -7)
	}

	// 总浏览次数
	var totalViews int64
	c.DB.Model(&models.BrowseHistory{}).
		Where("user_id = ? AND browse_time >= ?", userID, startTime).
		Count(&totalViews)

	// 浏览的应用数量
	var uniqueApps int64
	c.DB.Model(&models.BrowseHistory{}).
		Where("user_id = ? AND browse_time >= ?", userID, startTime).
		Distinct("app_id").
		Count(&uniqueApps)

	// 总浏览时长
	var totalDuration int64
	c.DB.Model(&models.BrowseHistory{}).
		Where("user_id = ? AND browse_time >= ?", userID, startTime).
		Select("COALESCE(SUM(duration), 0)").
		Scan(&totalDuration)

	// 最常浏览的应用（前5个）
	type AppViewCount struct {
		AppID     uint   `json:"app_id"`
		AppName   string `json:"app_name"`
		AppIcon   string `json:"app_icon"`
		ViewCount int64  `json:"view_count"`
	}

	var topApps []AppViewCount
	c.DB.Table("browse_histories bh").
		Select("bh.app_id, a.name as app_name, a.icon as app_icon, COUNT(*) as view_count").
		Joins("LEFT JOIN applications a ON bh.app_id = a.id").
		Where("bh.user_id = ? AND bh.browse_time >= ?", userID, startTime).
		Group("bh.app_id, a.name, a.icon").
		Order("view_count DESC").
		Limit(5).
		Scan(&topApps)

	// 按设备类型统计
	type DeviceStats struct {
		DeviceType string `json:"device_type"`
		Count      int64  `json:"count"`
	}

	var deviceStats []DeviceStats
	c.DB.Model(&models.BrowseHistory{}).
		Select("device_type, COUNT(*) as count").
		Where("user_id = ? AND browse_time >= ?", userID, startTime).
		Group("device_type").
		Scan(&deviceStats)

	stats := BrowseStatsResponse{
		Period:        period,
		TotalViews:    totalViews,
		UniqueApps:    uniqueApps,
		TotalDuration: totalDuration,
		TopApps:       topApps,
		DeviceStats:   deviceStats,
	}

	response.Success(ctx, stats)
}

// 请求结构体

// CreateBrowseHistoryRequest 创建浏览记录请求
type CreateBrowseHistoryRequest struct {
	AppID        uint   `json:"app_id" binding:"required"`
	Duration     int    `json:"duration"`
	ActionType   string `json:"action_type"`
	DeviceType   string `json:"device_type"`
	Platform     string `json:"platform"`
	ScreenWidth  int    `json:"screen_width"`
	ScreenHeight int    `json:"screen_height"`
	Language     string `json:"language"`
	Timezone     string `json:"timezone"`
}

// 响应结构体

// BrowseStatsResponse 浏览统计响应
type BrowseStatsResponse struct {
	Period        string      `json:"period"`
	TotalViews    int64       `json:"total_views"`
	UniqueApps    int64       `json:"unique_apps"`
	TotalDuration int64       `json:"total_duration"`
	TopApps       interface{} `json:"top_apps"`
	DeviceStats   interface{} `json:"device_stats"`
}

// 辅助函数

// convertToBrowseHistoryResponse 转换为浏览历史响应格式
func convertToBrowseHistoryResponse(history models.BrowseHistory) models.BrowseHistoryResponse {
	response := models.BrowseHistoryResponse{
		ID:         history.ID,
		BrowseTime: history.BrowseTime,
		Duration:   history.Duration,
		ActionType: history.ActionType,
		DeviceType: history.DeviceType,
		Platform:   history.Platform,
	}

	// 填充应用信息
	if history.App.ID != 0 {
		response.App.ID = history.App.ID
		response.App.Name = history.App.Name
		response.App.PackageName = history.App.Package
		response.App.Icon = history.App.Icon
		response.App.Description = history.App.Description
	}

	return response
}
