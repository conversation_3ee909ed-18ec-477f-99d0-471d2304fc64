import { history, RunTimeLayoutConfig } from '@umijs/max';
import { message, ConfigProvider } from 'antd';
import defaultSettings from '../config/defaultSettings';
import requestConfig, { API_BASE_URL } from './requestConfig';
import React from 'react';
import { getUserProfile } from '@/services/user';
import RightContent from './components/RightContent';
import { AppWrapper } from './components/AppWrapper';

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  settings?: typeof defaultSettings;
  currentUser?: API.UserResponse;
  loading?: boolean;
  fetchUserInfo?: () => Promise<API.UserResponse | undefined>;
  useLogto?: boolean;
}> {
  const fetchUserInfo = async () => {
    try {
      // 检查是否启用Logto认证
      const useLogto = process.env.REACT_APP_USE_LOGTO === 'true';
      
      if (useLogto) {
        // 使用Logto认证时，用户信息将通过Logto Hook获取
        return undefined;
      }
      
      // 传统token认证方式
      const token = localStorage.getItem('token');
      console.log('获取token:', token);
      
      if (!token) return undefined;
      
      // 使用services中的getUserProfile方法替代直接fetch
      const result = await getUserProfile();
      console.log('获取用户信息结果:', result);
      return result.data;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      // 清除无效的token
      localStorage.removeItem('token');
      return undefined;
    }
  };

  // 检查是否启用Logto认证
  const useLogto = process.env.REACT_APP_USE_LOGTO === 'true';
  
  // 如果不是登录页面和回调页面，获取用户信息
  const { pathname } = location;
  if (pathname !== '/user/login' && pathname !== '/user/register' && pathname !== '/callback') {
    const currentUser = await fetchUserInfo();
    return {
      fetchUserInfo,
      currentUser,
      settings: defaultSettings,
      useLogto,
    };
  }
  
  return {
    fetchUserInfo,
    settings: defaultSettings,
    useLogto,
  };
}

// 布局配置
export const layout = ({ initialState }: { initialState: any }) => {
  return {
    logo: '/logo.svg',
    menu: {
      locale: true,
    },
    
    // 水印设置
    waterMarkProps: {
      content: initialState?.currentUser?.username,
    },
    
    // 页脚设置
    footerRender: () => (
      <div style={{ textAlign: 'center', padding: '16px' }}>
        NexusHub 应用商店管理系统 ©2025 Created by NexusHub Team
      </div>
    ),
    
    // 添加右侧内容渲染
    rightContentRender: () => <RightContent />,
    
    // 登录状态校验
    onPageChange: () => {
      const { location } = history;
      
      // 跳过回调页面的认证检查
      if (location.pathname === '/callback') {
        return;
      }
      
      // 如果启用了Logto认证，跳过传统的认证检查
      // Logto认证状态将由LogtoProvider管理
      if (initialState?.useLogto) {
        return;
      }
      
      // 传统认证方式：如果没有登录，重定向到登录页
      if (
        !initialState?.currentUser && 
        location.pathname !== '/user/login' &&
        location.pathname !== '/user/register'
      ) {
        message.warning('请先登录');
        history.push('/user/login');
      }
    },
    
    // 继承默认设置
    ...initialState?.settings,
  };
};

// 请求配置
export const request = requestConfig;

export const rootContainer = (container: React.ReactNode) => {
  // 检查是否启用Logto认证
  const useLogto = process.env.REACT_APP_USE_LOGTO === 'true';
  
  return React.createElement(ConfigProvider, null, 
    React.createElement(AppWrapper, { useLogto }, container)
  );
};
