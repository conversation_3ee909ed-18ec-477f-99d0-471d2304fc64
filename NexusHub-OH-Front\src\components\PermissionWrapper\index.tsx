import React from 'react';
import { useAccess } from '@umijs/max';
import { Result, Button } from 'antd';
import { history } from '@umijs/max';

interface PermissionWrapperProps {
  children: React.ReactNode;
  permission?: string | string[];
  fallback?: React.ReactNode;
  requireAll?: boolean; // 是否需要所有权限，默认false（只需要任一权限）
}

const PermissionWrapper: React.FC<PermissionWrapperProps> = ({
  children,
  permission,
  fallback,
  requireAll = false,
}) => {
  const access = useAccess();

  // 如果没有指定权限，直接渲染子组件
  if (!permission) {
    return <>{children}</>;
  }

  // 权限检查逻辑
  const checkPermission = () => {
    if (typeof permission === 'string') {
      return access[permission] || false;
    }

    if (Array.isArray(permission)) {
      if (requireAll) {
        // 需要所有权限
        return permission.every(p => access[p] || false);
      } else {
        // 只需要任一权限
        return permission.some(p => access[p] || false);
      }
    }

    return false;
  };

  const hasPermission = checkPermission();

  // 如果有权限，渲染子组件
  if (hasPermission) {
    return <>{children}</>;
  }

  // 如果有自定义fallback，使用自定义fallback
  if (fallback) {
    return <>{fallback}</>;
  }

  // 默认无权限页面
  return (
    <Result
      status="403"
      title="403"
      subTitle="抱歉，您没有权限访问此页面。"
      extra={
        <Button type="primary" onClick={() => history.back()}>
          返回上一页
        </Button>
      }
    />
  );
};

export default PermissionWrapper;

// 权限检查Hook
export const usePermission = () => {
  const access = useAccess();

  const checkPermission = (permission: string | string[], requireAll = false) => {
    if (typeof permission === 'string') {
      return access[permission] || false;
    }

    if (Array.isArray(permission)) {
      if (requireAll) {
        return permission.every(p => access[p] || false);
      } else {
        return permission.some(p => access[p] || false);
      }
    }

    return false;
  };

  return {
    checkPermission,
    access,
  };
};

// 权限控制的按钮组件
interface PermissionButtonProps {
  permission: string | string[];
  requireAll?: boolean;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export const PermissionButton: React.FC<PermissionButtonProps> = ({
  permission,
  requireAll = false,
  children,
  fallback = null,
}) => {
  const { checkPermission } = usePermission();
  const hasPermission = checkPermission(permission, requireAll);

  if (hasPermission) {
    return <>{children}</>;
  }

  return <>{fallback}</>;
};