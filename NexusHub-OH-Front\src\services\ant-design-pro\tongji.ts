// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 记录应用下载 记录用户下载应用的行为，更新下载统计 POST /apps/${param0}/versions/${param1}/download */
export async function postAppsIdVersionsVersionIdDownload(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.postAppsIdVersionsVersionIdDownloadParams,
  options?: { [key: string]: any },
) {
  const { id: param0, version_id: param1, ...queryParams } = params;
  return request<API.Response>(`/apps/${param0}/versions/${param1}/download`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取应用下载统计 获取应用的下载统计数据，包括总数、每日统计和设备类型统计 GET /stats/apps/${param0}/downloads */
export async function getStatsAppsIdDownloads(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getStatsAppsIdDownloadsParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Response & { data?: API.AppDownloadStatsResponse }>(
    `/stats/apps/${param0}/downloads`,
    {
      method: 'GET',
      params: {
        // days has a default value: 30
        days: '30',
        ...queryParams,
      },
      ...(options || {}),
    },
  );
}

/** 获取用户下载记录 获取当前登录用户的应用下载记录 GET /stats/downloads */
export async function getStatsDownloads(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getStatsDownloadsParams,
  options?: { [key: string]: any },
) {
  return request<API.PageResponse & { data?: API.DownloadRecordResponse[] }>('/stats/downloads', {
    method: 'GET',
    params: {
      // page has a default value: 1
      page: '1',
      // page_size has a default value: 20
      page_size: '20',
      ...params,
    },
    ...(options || {}),
  });
}
