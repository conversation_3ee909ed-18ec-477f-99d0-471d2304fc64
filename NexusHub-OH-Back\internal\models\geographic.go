package models

import (
	"time"

	"gorm.io/gorm"
)

// GeographicData 地理位置数据结构 - 基于CSV数据
type GeographicData struct {
	ID        int            `json:"id" gorm:"column:id;primaryKey"`
	ParentID  int            `json:"parent_id" gorm:"column:parent_id"`
	Deep      int            `json:"deep" gorm:"column:deep"` // 0-省份，1-城市，2-镇/区，3-街道
	Name      string         `json:"name" gorm:"column:name"`
	Short     string         `json:"short" gorm:"column:short"`
	Pinyin    string         `json:"pinyin" gorm:"column:pinyin"`
	Code      string         `json:"code" gorm:"column:code"`
	FullName  string         `json:"full_name" gorm:"column:full_name"`
	CreatedAt time.Time      `json:"created_at" gorm:"column:created_at"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"column:updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at" gorm:"column:deleted_at;index"`
}

// TableName 指定表名
func (GeographicData) TableName() string {
	return "geographic_data"
}

// GetLevelName 获取级别名称
func (g *GeographicData) GetLevelName() string {
	switch g.Deep {
	case 0:
		return "省份"
	case 1:
		return "城市"
	case 2:
		return "区/镇"
	case 3:
		return "街道"
	default:
		return "未知"
	}
}

// IsProvince 是否为省份
func (g *GeographicData) IsProvince() bool {
	return g.Deep == 0
}

// IsCity 是否为城市
func (g *GeographicData) IsCity() bool {
	return g.Deep == 1
}

// IsDistrict 是否为区/镇
func (g *GeographicData) IsDistrict() bool {
	return g.Deep == 2
}

// IsStreet 是否为街道
func (g *GeographicData) IsStreet() bool {
	return g.Deep == 3
}