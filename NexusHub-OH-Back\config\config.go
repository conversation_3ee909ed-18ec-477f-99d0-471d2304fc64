package config

import (
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"github.com/joho/godotenv"
	"gopkg.in/yaml.v3"
)

// Config 应用配置结构体
type Config struct {
	Server    ServerConfig    `yaml:"server" mapstructure:"server"`
	Database  DatabaseConfig  `yaml:"database" mapstructure:"database"`
	Redis     RedisConfig     `yaml:"redis" mapstructure:"redis"`
	JWT       JWTConfig       `yaml:"jwt" mapstructure:"jwt"`
	Logto     LogtoConfig     `yaml:"logto" mapstructure:"logto"`
	Storage   StorageConfig   `yaml:"storage" mapstructure:"storage"`
	RabbitMQ  RabbitMQConfig  `yaml:"rabbitmq" mapstructure:"rabbitmq"`
	ES        ESConfig        `yaml:"elasticsearch" mapstructure:"elasticsearch"`
	Log       LogConfig       `yaml:"log" mapstructure:"log"`
	Cors      CorsConfig      `yaml:"cors" mapstructure:"cors"`
	RateLimit RateLimitConfig `yaml:"rate_limit" mapstructure:"rate_limit"`
	Dashboard DashboardConfig `yaml:"dashboard" mapstructure:"dashboard"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host string `yaml:"host"`
	Port int    `yaml:"port"`
	Mode string `yaml:"mode"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	User     string `yaml:"user"`
	Password string `yaml:"password"`
	DBName   string `yaml:"dbname"`
	SSLMode  string `yaml:"sslmode"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Addr     string `yaml:"addr"`
	Password string `yaml:"password"`
	DB       int    `yaml:"db"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret string        `yaml:"secret" mapstructure:"secret"`
	Expire time.Duration `yaml:"expire" mapstructure:"expire"` // 过期时间，单位：小时
}

// LogtoConfig Logto配置
type LogtoConfig struct {
	Endpoint     string `yaml:"endpoint" mapstructure:"endpoint"`           // Logto服务端点
	AppID        string `yaml:"app_id" mapstructure:"app_id"`               // 应用ID
	AppSecret    string `yaml:"app_secret" mapstructure:"app_secret"`       // 应用密钥
	APIResource  string `yaml:"api_resource" mapstructure:"api_resource"`   // API资源标识符
	JWKSEndpoint string `yaml:"jwks_endpoint" mapstructure:"jwks_endpoint"` // JWKS端点
	Enabled      bool   `yaml:"enabled" mapstructure:"enabled"`             // 是否启用Logto
}

// StorageConfig 存储配置
type StorageConfig struct {
	Type      string `yaml:"type"` // local, s3, oss
	Bucket    string `yaml:"bucket"`
	Endpoint  string `yaml:"endpoint"`
	Region    string `yaml:"region"`
	AccessKey string `yaml:"access_key"`
	SecretKey string `yaml:"secret_key"`
	BasePath  string `yaml:"base_path"`
}

// RabbitMQConfig RabbitMQ配置
type RabbitMQConfig struct {
	Host     string `yaml:"host"`
	Port     string `yaml:"port"`
	User     string `yaml:"user"`
	Password string `yaml:"password"`
}

// ESConfig Elasticsearch配置
type ESConfig struct {
	Host     string `yaml:"host"`
	Port     string `yaml:"port"`
	UseAuth  bool   `yaml:"use_auth"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string `yaml:"level"`
	File       string `yaml:"file"`
	MaxSize    int    `yaml:"max_size"`
	MaxBackups int    `yaml:"max_backups"`
	MaxAge     int    `yaml:"max_age"`
	Compress   bool   `yaml:"compress"`
}

// CorsConfig 跨域配置
type CorsConfig struct {
	AllowOrigins     []string `yaml:"allow_origins"`
	AllowMethods     []string `yaml:"allow_methods"`
	AllowHeaders     []string `yaml:"allow_headers"`
	ExposeHeaders    []string `yaml:"expose_headers"`
	AllowCredentials bool     `yaml:"allow_credentials"`
	MaxAge           string   `yaml:"max_age"`
}

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	Enable      bool          `yaml:"enable"`
	Period      time.Duration `yaml:"period"`       // 限流周期
	Requests    int           `yaml:"requests"`     // 周期内允许的请求数
	CleanupTime time.Duration `yaml:"cleanup_time"` // 清理周期
}

// DashboardConfig 仪表盘配置
type DashboardConfig struct {
	AnalyticsEnabled   bool          `yaml:"analytics_enabled"`    // 是否启用分析页
	MonitoringEnabled  bool          `yaml:"monitoring_enabled"`   // 是否启用监控页
	WorkbenchEnabled   bool          `yaml:"workbench_enabled"`    // 是否启用工作台
	MonitoringInterval time.Duration `yaml:"monitoring_interval"`  // 监控数据刷新间隔
	AlertEnabled       bool          `yaml:"alert_enabled"`        // 是否启用告警
	AlertCheckInterval time.Duration `yaml:"alert_check_interval"` // 告警检查间隔
}

// LoadConfig 从环境变量或配置文件加载配置
func LoadConfig() (*Config, error) {
	// 首先尝试加载.env文件，这不会覆盖已设置的环境变量
	_ = godotenv.Load()

	// 配置文件路径列表，按优先级排序
	configPaths := []string{
		"config.yaml",           // 根目录下的配置文件
		"config/config.yaml",    // config目录下的配置文件
		"../config/config.yaml", // 上级目录的config目录下的配置文件
	}

	// 尝试从配置文件加载
	var yamlConfig *Config
	var err error
	for _, path := range configPaths {
		yamlConfig, err = loadFromYaml(path)
		if err == nil {
			return yamlConfig, nil
		}
	}

	// 如果YAML文件不存在或解析失败，从环境变量加载
	log.Println("无法从YAML加载配置，使用环境变量:", err)
	return loadFromEnv(), nil
}

// loadFromYaml 从YAML文件加载配置
func loadFromYaml(filePath string) (*Config, error) {
	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件 %s 不存在", filePath)
	}

	// 读取文件内容
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析YAML
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析YAML配置失败: %w", err)
	}

	return &config, nil
}

// loadFromEnv 从环境变量加载配置
func loadFromEnv() *Config {
	return &Config{
		Server: ServerConfig{
			Host: getEnv("SERVER_HOST", "0.0.0.0"),
			Port: getEnvAsInt("SERVER_PORT", 8080),
			Mode: getEnv("GIN_MODE", "debug"),
		},
		Database: DatabaseConfig{
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnvAsInt("DB_PORT", 5432),
			User:     getEnv("DB_USER", "postgres"),
			Password: getEnv("DB_PASSWORD", "postgres"),
			DBName:   getEnv("DB_NAME", "nexushub_db"),
			SSLMode:  getEnv("DB_SSLMODE", "disable"),
		},
		Redis: RedisConfig{
			Addr:     getEnv("REDIS_ADDR", "localhost:6379"),
			Password: getEnv("REDIS_PASSWORD", ""),
			DB:       getEnvAsInt("REDIS_DB", 0),
		},
		JWT: JWTConfig{
			Secret: getEnv("JWT_SECRET", "your-secret-key"),
			Expire: time.Duration(getEnvAsInt("JWT_EXPIRE", 24)) * time.Hour,
		},
		Storage: StorageConfig{
			Type:      getEnv("STORAGE_TYPE", "local"),
			Bucket:    getEnv("STORAGE_BUCKET", "uploads"),
			Endpoint:  getEnv("STORAGE_ENDPOINT", ""),
			AccessKey: getEnv("STORAGE_ACCESS_KEY", ""),
			SecretKey: getEnv("STORAGE_SECRET_KEY", ""),
			Region:    getEnv("STORAGE_REGION", ""),
			BasePath:  getEnv("STORAGE_BASE_PATH", "./uploads"),
		},
		RabbitMQ: RabbitMQConfig{
			Host:     getEnv("RABBITMQ_HOST", "**********"),
			Port:     getEnv("RABBITMQ_PORT", "5672"),
			User:     getEnv("RABBITMQ_USER", "rabbitmq"),
			Password: getEnv("RABBITMQ_PASSWORD", "RabbitMQ"),
		},
		ES: ESConfig{
			Host:     getEnv("ES_HOST", "**********"),
			Port:     getEnv("ES_PORT", "9200"),
			UseAuth:  getEnvAsBool("ES_USE_AUTH", true),
			Username: getEnv("ES_USERNAME", "elastic"),
			Password: getEnv("ES_PASSWORD", "Elastic_fdTWPJ"),
		},
		Log: LogConfig{
			Level:      getEnv("LOG_LEVEL", "info"),
			File:       getEnv("LOG_FILE", "logs/app.log"),
			MaxSize:    getEnvAsInt("LOG_MAX_SIZE", 100),
			MaxBackups: getEnvAsInt("LOG_MAX_BACKUPS", 10),
			MaxAge:     getEnvAsInt("LOG_MAX_AGE", 30),
			Compress:   getEnvAsBool("LOG_COMPRESS", true),
		},
		Cors: CorsConfig{
			AllowOrigins:     []string{"*"},
			AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"},
			AllowHeaders:     []string{"Origin", "Content-Type", "Content-Length", "Accept-Encoding", "X-CSRF-Token", "Authorization", "Accept"},
			ExposeHeaders:    []string{"Content-Length"},
			AllowCredentials: true,
			MaxAge:           "12h",
		},
		RateLimit: RateLimitConfig{
			Enable:      getEnvAsBool("RATE_LIMIT_ENABLE", true),
			Period:      time.Duration(getEnvAsInt("RATE_LIMIT_PERIOD", 60)) * time.Second,
			Requests:    getEnvAsInt("RATE_LIMIT_REQUESTS", 100),
			CleanupTime: time.Duration(getEnvAsInt("RATE_LIMIT_CLEANUP", 600)) * time.Second,
		},
		Dashboard: DashboardConfig{
			AnalyticsEnabled:   getEnvAsBool("DASHBOARD_ANALYTICS_ENABLED", true),
			MonitoringEnabled:  getEnvAsBool("DASHBOARD_MONITORING_ENABLED", true),
			WorkbenchEnabled:   getEnvAsBool("DASHBOARD_WORKBENCH_ENABLED", true),
			MonitoringInterval: time.Duration(getEnvAsInt("DASHBOARD_MONITORING_INTERVAL", 60)) * time.Second,
			AlertEnabled:       getEnvAsBool("DASHBOARD_ALERT_ENABLED", true),
			AlertCheckInterval: time.Duration(getEnvAsInt("DASHBOARD_ALERT_CHECK_INTERVAL", 300)) * time.Second,
		},
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

// getEnvAsInt 获取环境变量并转换为int，如果不存在或转换失败则返回默认值
func getEnvAsInt(key string, defaultValue int) int {
	valueStr := getEnv(key, "")
	if valueStr == "" {
		return defaultValue
	}
	value, err := strconv.Atoi(valueStr)
	if err != nil {
		log.Printf("警告: 环境变量 %s 不是有效的整数，使用默认值 %d", key, defaultValue)
		return defaultValue
	}
	return value
}

// getEnvAsBool 获取环境变量并转换为bool，如果不存在或转换失败则返回默认值
func getEnvAsBool(key string, defaultValue bool) bool {
	valueStr := getEnv(key, "")
	if valueStr == "" {
		return defaultValue
	}
	value, err := strconv.ParseBool(valueStr)
	if err != nil {
		log.Printf("警告: 环境变量 %s 不是有效的布尔值，使用默认值 %v", key, defaultValue)
		return defaultValue
	}
	return value
}
