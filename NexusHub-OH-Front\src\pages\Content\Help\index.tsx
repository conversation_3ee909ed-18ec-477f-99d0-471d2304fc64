import React, { useState, useRef } from 'react';
import {
  Card,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  message,
  Drawer,
  Switch,
  InputNumber,
  Tabs,
  Descriptions,
  Typography,
  Popconfirm,
} from 'antd';
import {
  PlusOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useRequest } from '@umijs/max';
import {
  getAdminHelpArticles,
  getAdminHelpCategories,
  createHelpArticle,
  updateHelpArticle,
  deleteHelpArticle,
  createHelpCategory,
  updateHelpCategory,
  deleteHelpCategory,
} from '@/services/help';

const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;
const { Text } = Typography;

interface HelpArticle {
  id: number;
  title: string;
  content: string;
  summary?: string;
  category_id: number;
  category?: {
    id: number;
    name: string;
  };
  creator?: {
    id: number;
    username: string;
    avatar?: string;
  };
  tags?: string[];
  sort_order: number;
  is_published: boolean;
  is_featured: boolean;
  view_count: number;
  like_count: number;
  created_at: string;
  updated_at: string;
  published_at?: string;
  thumbnail?: string;
}

interface HelpCategory {
  id: number;
  name: string;
  description?: string;
  icon?: string;
  sort_order: number;
  is_active: boolean;
  articles_count: number;
  created_at: string;
}

const HelpContentManagement: React.FC = () => {
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [categoryModalVisible, setCategoryModalVisible] = useState(false);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [currentHelp, setCurrentHelp] = useState<HelpArticle | null>(null);
  const [currentCategory, setCurrentCategory] = useState<HelpCategory | null>(null);
  const [editForm] = Form.useForm();
  const [categoryForm] = Form.useForm();
  const [activeTab, setActiveTab] = useState('articles');
  const actionRef = useRef<ActionType>();
  const categoryActionRef = useRef<ActionType>();

  // 获取分类列表
  const { data: categories } = useRequest(getAdminHelpCategories);

  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  // 文章相关操作
  const handleAddArticle = () => {
    setCurrentHelp(null);
    editForm.resetFields();
    setEditModalVisible(true);
  };

  const handleEditArticle = (record: HelpArticle) => {
    setCurrentHelp(record);
    editForm.setFieldsValue({
      title: record.title,
      content: record.content,
      summary: record.summary,
      category_id: record.category_id,
      tags: record.tags ? (typeof record.tags === 'string' ? record.tags.split(',') : record.tags) : [],
      sort_order: record.sort_order,
      is_published: record.is_published,
    });
    setEditModalVisible(true);
  };

  const handlePreviewArticle = (record: HelpArticle) => {
    setCurrentHelp(record);
    setPreviewModalVisible(true);
  };

  const handleDeleteArticle = async (record: HelpArticle) => {
    try {
      await deleteHelpArticle(record.id.toString());
      message.success('删除成功');
      actionRef.current?.reload();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleArticleSubmit = async () => {
    try {
      const values = await editForm.validateFields();
      // 处理tags字段：将数组转换为字符串
      if (values.tags && Array.isArray(values.tags)) {
        values.tags = values.tags.join(',');
      }
      if (currentHelp) {
        await updateHelpArticle(currentHelp.id.toString(), values);
        message.success('更新成功');
      } else {
        await createHelpArticle(values);
        message.success('创建成功');
      }
      setEditModalVisible(false);
      actionRef.current?.reload();
    } catch (error) {
      message.error(currentHelp ? '更新失败' : '创建失败');
    }
  };

  // 分类相关操作
  const handleAddCategory = () => {
    setCurrentCategory(null);
    categoryForm.resetFields();
    setCategoryModalVisible(true);
  };

  const handleEditCategory = (record: HelpCategory) => {
    setCurrentCategory(record);
    categoryForm.setFieldsValue({
      name: record.name,
      description: record.description,
      icon: record.icon,
      sort_order: record.sort_order,
      is_active: record.is_active,
    });
    setCategoryModalVisible(true);
  };

  const handleDeleteCategory = async (record: HelpCategory) => {
    try {
      await deleteHelpCategory(record.id.toString());
      message.success('删除成功');
      categoryActionRef.current?.reload();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleCategorySubmit = async () => {
    try {
      const values = await categoryForm.validateFields();
      if (currentCategory) {
        await updateHelpCategory(currentCategory.id.toString(), values);
        message.success('更新成功');
      } else {
        await createHelpCategory(values);
        message.success('创建成功');
      }
      setCategoryModalVisible(false);
      categoryActionRef.current?.reload();
    } catch (error) {
      message.error(currentCategory ? '更新失败' : '创建失败');
    }
  };

  const getStatusTag = (isPublished: boolean) => {
    return isPublished ? (
      <Tag color="success">已发布</Tag>
    ) : (
      <Tag color="default">草稿</Tag>
    );
  };

  const getFeaturedTag = (isFeatured: boolean) => {
    return isFeatured ? (
      <Tag color="gold">精选</Tag>
    ) : null;
  };

  const getActiveTag = (isActive: boolean) => {
    return isActive ? (
      <Tag color="success">激活</Tag>
    ) : (
      <Tag color="default">未激活</Tag>
    );
  };

  // 文章表格列定义
  const articleColumns: ProColumns<HelpArticle>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      search: false,
    },
    {
      title: '标题',
      dataIndex: 'title',
      ellipsis: true,
      width: 200,
    },
    {
      title: '分类',
      dataIndex: 'category_id',
      width: 120,
      valueType: 'select',
      valueEnum: categories?.data?.reduce((acc: any, cat: HelpCategory) => {
        acc[cat.id] = { text: cat.name };
        return acc;
      }, {}) || {},
      render: (_, record) => record.category?.name || '-',
    },
    {
      title: '状态',
      dataIndex: 'is_published',
      width: 100,
      valueType: 'select',
      valueEnum: {
        true: { text: '已发布' },
        false: { text: '草稿' },
      },
      render: (_, record) => (
        <Space>
          {getStatusTag(record.is_published)}
          {getFeaturedTag(record.is_featured)}
        </Space>
      ),
    },
    {
      title: '排序',
      dataIndex: 'sort_order',
      width: 80,
      search: false,
    },
    {
      title: '浏览量',
      dataIndex: 'view_count',
      width: 100,
      search: false,
    },
    {
      title: '点赞数',
      dataIndex: 'like_count',
      width: 100,
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      width: 150,
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 200,
      render: (_, record) => [
        <Button
          key="view"
          type="link"
          size="small"
          icon={<EyeOutlined />}
          onClick={() => handlePreviewArticle(record)}
        >
          预览
        </Button>,
        <Button
          key="edit"
          type="link"
          size="small"
          icon={<EditOutlined />}
          onClick={() => handleEditArticle(record)}
        >
          编辑
        </Button>,
        <Popconfirm
          key="delete"
          title="确认删除"
          description={`确定要删除「${record.title}」吗？`}
          onConfirm={() => handleDeleteArticle(record)}
          okText="确认"
          cancelText="取消"
        >
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
          >
            删除
          </Button>
        </Popconfirm>,
      ],
    },
  ];

  // 分类表格列定义
  const categoryColumns: ProColumns<HelpCategory>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      search: false,
    },
    {
      title: '名称',
      dataIndex: 'name',
      width: 150,
    },
    {
      title: '描述',
      dataIndex: 'description',
      ellipsis: true,
      search: false,
    },
    {
      title: '图标',
      dataIndex: 'icon',
      width: 100,
      search: false,
      render: (text) => text || '-',
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      width: 100,
      valueType: 'select',
      valueEnum: {
        true: { text: '激活' },
        false: { text: '未激活' },
      },
      render: (_, record) => getActiveTag(record.is_active),
    },
    {
      title: '排序',
      dataIndex: 'sort_order',
      width: 80,
      search: false,
    },
    {
      title: '文章数量',
      dataIndex: 'articles_count',
      width: 100,
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      width: 150,
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 150,
      render: (_, record) => [
        <Button
          key="edit"
          type="link"
          size="small"
          icon={<EditOutlined />}
          onClick={() => handleEditCategory(record)}
        >
          编辑
        </Button>,
        <Popconfirm
          key="delete"
          title="确认删除"
          description={`确定要删除分类「${record.name}」吗？`}
          onConfirm={() => handleDeleteCategory(record)}
          okText="确认"
          cancelText="取消"
        >
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
          >
            删除
          </Button>
        </Popconfirm>,
      ],
    },
  ];

  return (
    <PageContainer>
      <Card>
        <Tabs 
          activeKey={activeTab} 
          onChange={handleTabChange}
          items={[
            {
              key: 'articles',
              label: '文章管理',
              children: (
                <ProTable<HelpArticle>
                  headerTitle="帮助文章"
                  actionRef={actionRef}
                  rowKey="id"
                  search={{
                    labelWidth: 120,
                  }}
                  toolBarRender={() => [
                    <Button
                      key="add"
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={handleAddArticle}
                    >
                      新建文章
                    </Button>,
                    <Button
                      key="refresh"
                      icon={<ReloadOutlined />}
                      onClick={() => actionRef.current?.reload()}
                    >
                      刷新
                    </Button>,
                  ]}
                  request={async (params) => {
                    const response = await getAdminHelpArticles({
                      page: params.current,
                      page_size: params.pageSize,
                      category_id: params.category_id,
                      is_published: params.is_published,
                      keyword: params.title,
                    });
                    return {
                      data: response.data?.list || [],
                      success: response.success,
                      total: response.data?.total || 0,
                    };
                  }}
                  columns={articleColumns}
                  pagination={{
                    defaultPageSize: 10,
                    showSizeChanger: true,
                  }}
                />
              ),
            },
            {
              key: 'categories',
              label: '分类管理',
              children: (
                <ProTable<HelpCategory>
                  headerTitle="帮助分类"
                  actionRef={categoryActionRef}
                  rowKey="id"
                  search={{
                    labelWidth: 120,
                  }}
                  toolBarRender={() => [
                    <Button
                      key="add"
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={handleAddCategory}
                    >
                      新建分类
                    </Button>,
                    <Button
                      key="refresh"
                      icon={<ReloadOutlined />}
                      onClick={() => categoryActionRef.current?.reload()}
                    >
                      刷新
                    </Button>,
                  ]}
                  request={async (params) => {
                    const response = await getAdminHelpCategories({
                      page: params.current,
                      page_size: params.pageSize,
                      is_active: params.is_active,
                      keyword: params.name,
                    });
                    return {
                      data: response.data || [],
                      success: response.success,
                      total: response.total || 0,
                    };
                  }}
                  columns={categoryColumns}
                  pagination={{
                    defaultPageSize: 10,
                    showSizeChanger: true,
                  }}
                />
              ),
            },
          ]}
        />
      </Card>

      {/* 文章编辑模态框 */}
      <Modal
        title={currentHelp ? '编辑文章' : '新建文章'}
        open={editModalVisible}
        onOk={handleArticleSubmit}
        onCancel={() => setEditModalVisible(false)}
        width={800}
        destroyOnHidden
      >
        <Form form={editForm} layout="vertical">
          <Form.Item
            name="title"
            label="标题"
            rules={[{ required: true, message: '请输入标题' }]}
          >
            <Input placeholder="请输入文章标题" />
          </Form.Item>
          <Form.Item
            name="category_id"
            label="分类"
            rules={[{ required: true, message: '请选择分类' }]}
          >
            <Select placeholder="请选择分类" loading={!categories}>
              {categories?.map((cat: HelpCategory) => (
                <Option key={cat.id} value={cat.id}>
                  {cat.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="summary" label="摘要">
            <TextArea rows={3} placeholder="请输入文章摘要" />
          </Form.Item>
          <Form.Item
            name="content"
            label="内容"
            rules={[{ required: true, message: '请输入内容' }]}
          >
            <TextArea rows={10} placeholder="请输入文章内容" />
          </Form.Item>
          <Form.Item name="tags" label="标签">
            <Select mode="tags" placeholder="请输入标签，按回车添加" />
          </Form.Item>
          <Form.Item name="sort_order" label="排序" initialValue={0}>
            <InputNumber min={0} placeholder="排序值，数字越小越靠前" />
          </Form.Item>
          <Form.Item name="is_published" label="发布状态" valuePropName="checked">
            <Switch checkedChildren="已发布" unCheckedChildren="草稿" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 分类编辑模态框 */}
      <Modal
        title={currentCategory ? '编辑分类' : '新建分类'}
        open={categoryModalVisible}
        onOk={handleCategorySubmit}
        onCancel={() => setCategoryModalVisible(false)}
        width={600}
        destroyOnHidden
      >
        <Form form={categoryForm} layout="vertical">
          <Form.Item
            name="name"
            label="名称"
            rules={[{ required: true, message: '请输入分类名称' }]}
          >
            <Input placeholder="请输入分类名称" />
          </Form.Item>
          <Form.Item name="description" label="描述">
            <TextArea rows={3} placeholder="请输入分类描述" />
          </Form.Item>
          <Form.Item name="icon" label="图标">
            <Input placeholder="请输入图标名称或URL" />
          </Form.Item>
          <Form.Item name="sort_order" label="排序" initialValue={0}>
            <InputNumber min={0} placeholder="排序值，数字越小越靠前" />
          </Form.Item>
          <Form.Item name="is_active" label="激活状态" valuePropName="checked" initialValue={true}>
            <Switch checkedChildren="激活" unCheckedChildren="未激活" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 文章预览抽屉 */}
      <Drawer
        title="文章预览"
        width={800}
        open={previewModalVisible}
        onClose={() => setPreviewModalVisible(false)}
      >
        {currentHelp && (
          <div>
            <Descriptions column={2} bordered>
              <Descriptions.Item label="标题" span={2}>
                <Text strong>{currentHelp.title}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="分类">
                {currentHelp.category?.name || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Space>
                  {getStatusTag(currentHelp.is_published)}
                  {getFeaturedTag(currentHelp.is_featured)}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="浏览量">
                {currentHelp.views_count}
              </Descriptions.Item>
              <Descriptions.Item label="点赞数">
                {currentHelp.likes_count}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间" span={2}>
                {currentHelp.created_at}
              </Descriptions.Item>
              {currentHelp.summary && (
                <Descriptions.Item label="摘要" span={2}>
                  {currentHelp.summary}
                </Descriptions.Item>
              )}
              {currentHelp.tags && currentHelp.tags.length > 0 && (
                <Descriptions.Item label="标签" span={2}>
                  {currentHelp.tags.map((tag, index) => (
                    <Tag key={index}>{tag}</Tag>
                  ))}
                </Descriptions.Item>
              )}
            </Descriptions>
            <Card title="文章内容" style={{ marginTop: 16 }}>
              <div style={{ whiteSpace: 'pre-wrap' }}>
                {currentHelp.content}
              </div>
            </Card>
          </div>
        )}
      </Drawer>
    </PageContainer>
  );
};

export default HelpContentManagement;