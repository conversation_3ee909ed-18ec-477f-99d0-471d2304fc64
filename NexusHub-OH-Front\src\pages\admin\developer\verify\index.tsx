import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  message,
  Tag,
  Space,
  Image,
  Descriptions,
  Radio,
  Typography,
  Divider,
  Row,
  Col,
  Select,
} from 'antd';
import { EyeOutlined, CheckOutlined, CloseOutlined, SearchOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { getAdminDevelopersVerify, postAdminDevelopersIdVerify } from '@/services/ant-design-pro/kaifazhe';
import { getSearchUsers } from '@/services/ant-design-pro/sousuo';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import styles from './index.less';

const { TextArea } = Input;
const { Title, Paragraph } = Typography;

interface DeveloperVerifyRecord {
  id: number;
  username: string;
  developer_name: string;
  company_name?: string;
  contact_email: string;
  contact_phone: string;
  website?: string;
  description: string;
  developer_address: string;
  developer_avatar?: string;
  business_license?: string;
  identity_card: string;
  submitted_at: string;
  verified_at?: string;
  verify_reason?: string;
  verify_status: 'pending' | 'approved' | 'rejected';
}

const DeveloperVerifyAdmin: React.FC = () => {
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<DeveloperVerifyRecord[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [statusFilter, setStatusFilter] = useState<string>('all'); // 状态筛选
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [verifyModalVisible, setVerifyModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<DeveloperVerifyRecord | null>(null);

  // 获取开发者认证列表
  const fetchDeveloperList = async () => {
    console.log('fetchDeveloperList called with:', {
      currentPage,
      pageSize,
      statusFilter,
      searchKeyword
    });
    setLoading(true);
    try {
      let response;
      
      // 如果有搜索关键词，使用搜索API
      if (searchKeyword.trim()) {
        console.log('=== 使用搜索API ===');
        console.log('搜索参数:', {
          keyword: searchKeyword,
          is_developer: true,
          verify_status: statusFilter === 'all' ? undefined : statusFilter,
          page: currentPage.toString(),
          page_size: pageSize.toString(),
        });
        
        response = await getSearchUsers({
          keyword: searchKeyword,
          is_developer: true,
          verify_status: statusFilter === 'all' ? undefined : statusFilter,
          page: currentPage.toString(),
          page_size: pageSize.toString(),
        });
        
        console.log('搜索API响应:', response);
        
        if (response.code === 200) {
          // 转换搜索结果为开发者认证记录格式
          const convertedData = response.data?.users?.map((user: any) => {
            console.log('转换用户数据:', user);
            return {
              id: user.id,
              username: user.username || '',
              developer_name: user.developer_name || user.username || '',
              company_name: user.company_name || '',
              contact_email: user.contact_email || user.email || '',
              contact_phone: user.contact_phone || user.phone || '',
              website: user.website || '',
              description: user.description || '',
              developer_address: user.developer_address || '',
              developer_avatar: user.developer_avatar || user.avatar || '',
              business_license: user.business_license || '',
              identity_card: user.identity_card || '',
              submitted_at: user.submitted_at || user.created_at || '',
              verified_at: user.verified_at || '',
              verify_reason: user.verify_reason || '',
              verify_status: user.verify_status || 'pending',
            };
          }) || [];
          
          console.log('转换后的数据:', convertedData);
          setDataSource(convertedData);
          setTotal(response.data?.total || 0);
        } else {
          console.error('搜索失败:', response.message);
          message.error(response.message || '搜索失败');
          setDataSource([]);
          setTotal(0);
        }
      } else {
        // 使用原有的开发者认证API
        console.log('=== 使用常规API ===');
        console.log('常规API参数:', {
          page: currentPage,
          page_size: pageSize,
          status: statusFilter === 'all' ? undefined : statusFilter,
        });
        
        response = await getAdminDevelopersVerify({
          page: currentPage.toString(),
          page_size: pageSize.toString(),
          status: statusFilter === 'all' ? undefined : statusFilter,
        });
        
        console.log('常规API响应:', response);
        
        if (response.code === 200) {
          console.log('API response data:', response.data);
          console.log('API response total:', response.total);
          
          // 确保数据结构完整
          const processedData = (response.data || []).map((item: any) => ({
            ...item,
            username: item.username || '',
            developer_name: item.developer_name || item.username || '',
            company_name: item.company_name || '',
            contact_email: item.contact_email || '',
            contact_phone: item.contact_phone || '',
            website: item.website || '',
            description: item.description || '',
            developer_address: item.developer_address || '',
            developer_avatar: item.developer_avatar || '',
            business_license: item.business_license || '',
            identity_card: item.identity_card || '',
            submitted_at: item.submitted_at || '',
            verified_at: item.verified_at || '',
            verify_reason: item.verify_reason || '',
            verify_status: item.verify_status || 'pending',
          }));
          
          console.log('Processed data:', processedData);
          console.log('Processed data length:', processedData.length);
          
          setDataSource(processedData);
          setTotal(response.total || 0);
        } else {
          message.error(response.message || '获取数据失败');
        }
      }
    } catch (error) {
      console.error('获取开发者认证列表失败:', error);
      message.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log('=== DEVELOPER VERIFY PAGE useEffect triggered ===');
    console.log('Dependencies:', { currentPage, pageSize, statusFilter, searchKeyword });
    fetchDeveloperList();
  }, [currentPage, pageSize, statusFilter, searchKeyword]);

  useEffect(() => {
    console.log('=== DataSource updated ===');
    console.log('Current dataSource:', dataSource);
    console.log('DataSource length:', dataSource.length);
  }, [dataSource]);

  // 查看详情
  const handleViewDetail = (record: DeveloperVerifyRecord) => {
    setCurrentRecord(record);
    setDetailModalVisible(true);
  };

  // 打开审核模态框
  const handleOpenVerify = (record: DeveloperVerifyRecord) => {
    setCurrentRecord(record);
    form.resetFields();
    setVerifyModalVisible(true);
  };

  // 提交审核
  const handleSubmitVerify = async () => {
    if (!currentRecord) return;
    
    try {
      const values = await form.validateFields();
      const response = await postAdminDevelopersIdVerify(
        { id: currentRecord.id.toString() },
        {
          verify_status: values.verify_status,
          verify_reason: values.verify_reason || '',
        }
      );
      
      if (response.code === 200) {
        message.success('审核完成');
        setVerifyModalVisible(false);
        fetchDeveloperList();
      } else {
        message.error(response.message || '审核失败');
      }
    } catch (error) {
      console.error('审核失败:', error);
      message.error('审核失败');
    }
  };

  // 渲染状态标签
  const renderStatusTag = (status: string) => {
    const statusMap = {
      pending: { color: 'orange', text: '待审核' },
      approved: { color: 'green', text: '已通过' },
      rejected: { color: 'red', text: '已拒绝' },
    };
    
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 表格列定义
  const columns: ColumnsType<DeveloperVerifyRecord> = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      width: 120,
    },
    {
      title: '开发者姓名',
      dataIndex: 'developer_name',
      key: 'developer_name',
      width: 120,
    },
    {
      title: '公司名称',
      dataIndex: 'company_name',
      key: 'company_name',
      width: 150,
      render: (text) => text || '-',
    },
    {
      title: '联系邮箱',
      dataIndex: 'contact_email',
      key: 'contact_email',
      width: 200,
    },
    {
      title: '联系电话',
      dataIndex: 'contact_phone',
      key: 'contact_phone',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'verify_status',
      key: 'verify_status',
      width: 100,
      render: renderStatusTag,
    },
    {
      title: '提交时间',
      dataIndex: 'submitted_at',
      key: 'submitted_at',
      width: 150,
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            查看详情
          </Button>
          {record.verify_status === 'pending' && (
            <Button
              type="primary"
              size="small"
              onClick={() => handleOpenVerify(record)}
            >
              审核
            </Button>
          )}
        </Space>
      ),
    },
  ];



  return (
    <PageContainer
      title="开发者认证审核"
      subTitle="管理和审核开发者认证申请"
    >
      <Card>
        <div className={styles.filterBar}>
          <Space direction="vertical" style={{ width: '100%' }}>
            {/* 搜索表单 */}
            <Form
              form={searchForm}
              layout="inline"
              onFinish={(values) => {
                setSearchKeyword(values.keyword || '');
                setCurrentPage(1);
              }}
            >
              <Form.Item name="keyword">
                <Input
                  placeholder="搜索开发者姓名、用户名、公司名称或邮箱"
                  prefix={<SearchOutlined />}
                  style={{ width: 300 }}
                  allowClear
                />
              </Form.Item>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                    搜索
                  </Button>
                  <Button
                    onClick={() => {
                      searchForm.resetFields();
                      setSearchKeyword('');
                      setCurrentPage(1);
                    }}
                  >
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
            
            {/* 状态筛选 */}
            <Space>
              <span>状态筛选：</span>
              <Radio.Group
                value={statusFilter}
                onChange={(e) => {
                  console.log('状态筛选变更:', e.target.value);
                  setStatusFilter(e.target.value);
                  setCurrentPage(1);
                }}
              >
                <Radio.Button value="all">全部</Radio.Button>
                <Radio.Button value="pending">待审核</Radio.Button>
                <Radio.Button value="approved">已通过</Radio.Button>
                <Radio.Button value="rejected">已拒绝</Radio.Button>
              </Radio.Group>
            </Space>
          </Space>
        </div>
        
        <Table
          columns={columns}
          dataSource={dataSource}
          loading={loading}
          rowKey="id"
          scroll={{ x: 1200 }}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || 10);
            },
          }}
        />
      </Card>

      {/* 详情模态框 */}
      <Modal
        title="开发者认证详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
          currentRecord?.verify_status === 'pending' && (
            <Button
              key="verify"
              type="primary"
              onClick={() => {
                setDetailModalVisible(false);
                handleOpenVerify(currentRecord);
              }}
            >
              立即审核
            </Button>
          ),
        ]}
        width={800}
        className={styles.detailModal}
      >
        {currentRecord && (
          <div>
            <Descriptions title="基本信息" column={2} bordered>
              <Descriptions.Item label="用户名">{currentRecord.username}</Descriptions.Item>
              <Descriptions.Item label="开发者姓名">{currentRecord.developer_name}</Descriptions.Item>
              <Descriptions.Item label="公司名称">
                {currentRecord.company_name || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="联系邮箱">{currentRecord.contact_email}</Descriptions.Item>
              <Descriptions.Item label="联系电话">{currentRecord.contact_phone}</Descriptions.Item>
              <Descriptions.Item label="个人网站">
                {currentRecord.website ? (
                  <a href={currentRecord.website} target="_blank" rel="noopener noreferrer">
                    {currentRecord.website}
                  </a>
                ) : (
                  '-'
                )}
              </Descriptions.Item>
              <Descriptions.Item label="联系地址" span={2}>
                {currentRecord.developer_address}
              </Descriptions.Item>
              <Descriptions.Item label="个人简介" span={2}>
                {currentRecord.description}
              </Descriptions.Item>
              <Descriptions.Item label="认证状态">
                {renderStatusTag(currentRecord.verify_status)}
              </Descriptions.Item>
              <Descriptions.Item label="提交时间">
                {dayjs(currentRecord.submitted_at).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              {currentRecord.verified_at && (
                <Descriptions.Item label="审核时间">
                  {dayjs(currentRecord.verified_at).format('YYYY-MM-DD HH:mm:ss')}
                </Descriptions.Item>
              )}
              {currentRecord.verify_reason && (
                <Descriptions.Item label="审核意见" span={2}>
                  {currentRecord.verify_reason}
                </Descriptions.Item>
              )}
            </Descriptions>
            
            <Divider>认证材料</Divider>
            
            <Row gutter={24}>
              {currentRecord.developer_avatar && (
                <Col span={8}>
                  <div className={styles.imageItem}>
                    <Title level={5}>开发者头像</Title>
                    <Image
                      src={currentRecord.developer_avatar}
                      alt="开发者头像"
                      width={150}
                      height={150}
                      style={{ objectFit: 'cover' }}
                    />
                  </div>
                </Col>
              )}
              
              {currentRecord.business_license && (
                <Col span={8}>
                  <div className={styles.imageItem}>
                    <Title level={5}>营业执照</Title>
                    <Image
                      src={currentRecord.business_license}
                      alt="营业执照"
                      width={150}
                      height={150}
                      style={{ objectFit: 'cover' }}
                    />
                  </div>
                </Col>
              )}
              
              <Col span={8}>
                <div className={styles.imageItem}>
                  <Title level={5}>身份证明</Title>
                  <Image
                    src={currentRecord.identity_card}
                    alt="身份证明"
                    width={150}
                    height={150}
                    style={{ objectFit: 'cover' }}
                  />
                </div>
              </Col>
            </Row>
          </div>
        )}
      </Modal>

      {/* 审核模态框 */}
      <Modal
        title="审核开发者认证"
        open={verifyModalVisible}
        onCancel={() => setVerifyModalVisible(false)}
        onOk={handleSubmitVerify}
        okText="提交审核"
        cancelText="取消"
        width={600}
      >
        {currentRecord && (
          <div>
            <Descriptions column={1} bordered size="small" style={{ marginBottom: 24 }}>
              <Descriptions.Item label="开发者姓名">{currentRecord.developer_name}</Descriptions.Item>
              <Descriptions.Item label="联系邮箱">{currentRecord.contact_email}</Descriptions.Item>
              <Descriptions.Item label="联系电话">{currentRecord.contact_phone}</Descriptions.Item>
            </Descriptions>
            
            <Form form={form} layout="vertical">
              <Form.Item
                name="verify_status"
                label="审核结果"
                rules={[{ required: true, message: '请选择审核结果' }]}
              >
                <Radio.Group>
                  <Radio value="approved">
                    <CheckOutlined style={{ color: '#52c41a' }} /> 通过认证
                  </Radio>
                  <Radio value="rejected">
                    <CloseOutlined style={{ color: '#ff4d4f' }} /> 拒绝认证
                  </Radio>
                </Radio.Group>
              </Form.Item>
              
              <Form.Item
                name="verify_reason"
                label="审核意见"
                rules={[
                  {
                    validator: (_, value) => {
                      const status = form.getFieldValue('verify_status');
                      if (status === 'rejected' && !value) {
                        return Promise.reject(new Error('拒绝认证时必须填写拒绝理由'));
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <TextArea
                  rows={4}
                  placeholder="请填写审核意见（拒绝时必填）"
                  maxLength={500}
                  showCount
                />
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>
    </PageContainer>
  );
};

export default DeveloperVerifyAdmin;