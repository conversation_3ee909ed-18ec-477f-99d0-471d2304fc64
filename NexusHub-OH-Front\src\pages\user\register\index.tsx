import {
  Button,
  Col,
  Form,
  Input,
  Popover,
  Progress,
  Row,
  Select,
  message,
  Steps,
  Typography,
  Divider,
  Space,
  Result,
} from 'antd';
import type { Store } from 'antd/es/form/interface';
import type { FC } from 'react';
import { useEffect, useState } from 'react';
import type { StateType } from './service';
import { Footer } from '@/components';
import { FormattedMessage, Helmet, useIntl, useRequest, history, Link, useModel } from '@umijs/max';
import { SelectLang } from '@/components/RightContent';
import { LockOutlined, MailOutlined, UserOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { createStyles } from 'antd-style';
import Settings from '../../../../config/defaultSettings';

const FormItem = Form.Item;
const { Option } = Select;
const { Step } = Steps;
const { Title, Paragraph, Text } = Typography;

// 使用与登录页相同的样式，但进行优化
const useStyles = createStyles(({ token }) => {
  return {
    action: {
      marginLeft: '8px',
      color: 'rgba(0, 0, 0, 0.2)',
      fontSize: '24px',
      verticalAlign: 'middle',
      cursor: 'pointer',
      transition: 'color 0.3s',
      '&:hover': {
        color: token.colorPrimaryActive,
      },
    },
    lang: {
      width: 42,
      height: 42,
      lineHeight: '42px',
      position: 'fixed',
      right: 16,
      top: 16,
      borderRadius: token.borderRadius,
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
      zIndex: 1000,
      cursor: 'pointer',
      ':hover': {
        backgroundColor: token.colorBgTextHover,
      },
    },
    container: {
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      overflow: 'auto',
      backgroundImage:
        "url('https://images.unsplash.com/photo-1579548122080-c35fd6820ecb?q=80&w=2070&auto=format&fit=crop')",
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      position: 'relative',
      '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.4)',
        backdropFilter: 'blur(2px)',
      },
    },
    content: {
      flex: '1',
      padding: '32px 0',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      position: 'relative',
      zIndex: 1,
    },
    form: {
      padding: '32px 24px',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderRadius: '12px',
      boxShadow: '0 8px 24px rgba(0, 0, 0, 0.2)',
      transition: 'all 0.3s ease',
      maxWidth: '90%',
      width: '420px',
    },
    header: {
      display: 'flex',
      alignItems: 'center',
      marginBottom: '28px',
    },
    logo: {
      height: '44px',
      marginRight: '16px',
    },
    title: {
      fontSize: '33px',
      fontWeight: 'bold',
      color: token.colorPrimary,
      marginBottom: '4px',
    },
    desc: {
      fontSize: '14px',
      color: token.colorTextSecondary,
      marginTop: '8px',
    },
    main: {
      width: '100%',
      margin: '0 auto',
    },
    password: {
      marginBottom: '24px',
      '.ant-form-item-explain': { display: 'none' },
    },
    getCaptcha: {
      display: 'block',
      width: '100%',
      borderRadius: '6px',
      height: '40px',
    },
    footer: {
      width: '100%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginTop: '16px',
    },
    submit: {
      width: '50%',
      height: '40px',
      borderRadius: '6px',
      fontSize: '16px',
      fontWeight: 500,
    },
    success: {
      transition: 'color 0.3s',
      color: token.colorSuccess,
    },
    warning: {
      transition: 'color 0.3s',
      color: token.colorWarning,
    },
    error: {
      transition: 'color 0.3s',
      color: token.colorError,
    },
    'progress-pass > .progress': {
      '.ant-progress-bg': { backgroundColor: token.colorWarning },
    },
    stepContent: {
      padding: '24px 0',
      minHeight: '240px',
    },
    stepsContainer: {
      marginBottom: '32px',
      width: '100%',
      '.ant-steps-item-icon': {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      },
      '.ant-steps-item-title': {
        fontSize: '14px',
        fontWeight: 500,
      },
      '.ant-steps-item-description': {
        fontSize: '12px',
      },
    },
    formItem: {
      marginBottom: '24px',
    },
    loginLink: {
      color: token.colorPrimary,
      transition: 'color 0.3s',
      '&:hover': {
        color: token.colorPrimaryActive,
        textDecoration: 'underline',
      },
    },
    stepTitle: {
      fontSize: '18px',
      fontWeight: 500,
      marginBottom: '16px',
      color: token.colorTextHeading,
    },
    confirmInfo: {
      backgroundColor: 'rgba(240, 242, 245, 0.6)',
      padding: '16px',
      borderRadius: '8px',
      marginBottom: '16px',
    },
    infoItem: {
      display: 'flex',
      marginBottom: '8px',
      '&:last-child': {
        marginBottom: 0,
      },
    },
    infoLabel: {
      width: '80px',
      color: token.colorTextSecondary,
      flexShrink: 0,
    },
    infoValue: {
      fontWeight: 500,
      color: token.colorText,
      flexGrow: 1,
    },
  };
});

const Lang = () => {
  const { styles } = useStyles();

  return (
    <div className={styles.lang} data-lang>
      <SelectLang style={{ height: '100%', width: '100%' }} />
    </div>
  );
};

const passwordProgressMap: {
  ok: 'success';
  pass: 'normal';
  poor: 'exception';
} = {
  ok: 'success',
  pass: 'normal',
  poor: 'exception',
};

const Register: FC = () => {
  const { styles } = useStyles();
  const [open, setVisible]: [boolean, any] = useState(false);
  const [popover, setPopover]: [boolean, any] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState({});
  const confirmDirty = false;
  const intl = useIntl();

  const passwordStatusMap = {
    ok: (
      <div className={styles.success}>
        <span>强度：强</span>
      </div>
    ),
    pass: (
      <div className={styles.warning}>
        <span>强度：中</span>
      </div>
    ),
    poor: (
      <div className={styles.error}>
        <span>强度：太短</span>
      </div>
    ),
  };

  const [form] = Form.useForm();
  
  // 自动保存表单数据到localStorage
  useEffect(() => {
    // 从localStorage恢复表单数据
    const savedData = localStorage.getItem('registerFormData');
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        form.setFieldsValue(parsedData);
        setFormData(parsedData);
      } catch (error) {
        console.error('解析保存的表单数据失败:', error);
      }
    }

    // 自动保存表单数据
    const autoSaveInterval = setInterval(() => {
      const currentValues = form.getFieldsValue();
      localStorage.setItem('registerFormData', JSON.stringify(currentValues));
      message.success('表单数据已自动保存', 1);
    }, 30000);

    return () => {
      clearInterval(autoSaveInterval);
    };
  }, [form]);

  const getPasswordStatus = () => {
    const value = form.getFieldValue('password');
    if (value && value.length > 9) {
      return 'ok';
    }
    if (value && value.length > 5) {
      return 'pass';
    }
    return 'poor';
  };
  const { loading: submitting, run: register } = useRequest<{
    data: StateType;
  }>(
    async (params) => {
      // 使用真实的注册API
      const response = await import('@/services/user').then(module => module.register(params));
      // 检查响应状态
      if (response && (response.code === 200 || response.success)) {
        return { status: 'ok', data: response };
      } else {
        throw new Error(response?.message || '注册失败');
      }
    },
    {
      manual: true,
      onSuccess: (data, params) => {
        message.success('注册成功！');
        // 清除localStorage中的表单数据
        localStorage.removeItem('registerFormData');
        history.push({
          pathname: `/user/register-result?account=${params[0].email}`,
        });
      },
      onError: (error) => {
        console.error('注册失败:', error);
        const errorMessage = error?.message || error?.response?.data?.message || '注册失败，请重试';
        message.error(errorMessage);
      },
    }
  );
  // 检查用户名是否已存在
  const checkUsernameUnique = async (username: string) => {
    // 这里应该调用后端API检查用户名是否已存在
    // 示例代码，实际实现需要根据后端API进行调整
    try {
      // 模拟API调用，实际项目中应替换为真实API
      // const response = await fetch('/api/check-username', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ username }),
      // });
      // const data = await response.json();
      // return data.isUnique;
      
      // 目前返回true表示用户名可用
      return true;
    } catch (error) {
      console.error('检查用户名失败:', error);
      return false;
    }
  };

  const onFinish = async (values: Store) => {
    // 保存当前步骤的表单数据
    const newFormData = { ...formData, ...values };
    setFormData(newFormData);
    localStorage.setItem('registerFormData', JSON.stringify(newFormData));

    // 如果不是最后一步，进入下一步
    if (currentStep < 1) {
      setCurrentStep(currentStep + 1);
      return;
    }

    // 最后一步，提交表单
    // 检查用户名是否唯一
    const isUsernameUnique = await checkUsernameUnique(newFormData.username);
    if (!isUsernameUnique) {
      message.error('用户名已存在，请选择其他用户名');
      setCurrentStep(0); // 返回第一步重新输入
      return;
    }

    // 提交注册数据
    register({
      username: newFormData.username,
      password: newFormData.password,
      email: newFormData.email,
      nickname: newFormData.username, // 使用用户名作为昵称
    });
  };

  // 上一步按钮处理函数
  const handlePrevStep = () => {
    setCurrentStep(currentStep - 1);
  };

  // 手动保存表单数据
  const handleSaveForm = () => {
    const currentValues = form.getFieldsValue();
    const newFormData = { ...formData, ...currentValues };
    setFormData(newFormData);
    localStorage.setItem('registerFormData', JSON.stringify(newFormData));
    message.success('表单数据已保存');
  };
  const checkConfirm = (_: any, value: string) => {
    const promise = Promise;
    if (value && value !== form.getFieldValue('password')) {
      return promise.reject('两次输入的密码不匹配!');
    }
    return promise.resolve();
  };
  const checkPassword = (_: any, value: string) => {
    const promise = Promise;
    // 没有值的情况
    if (!value) {
      setVisible(!!value);
      return promise.reject('请输入密码!');
    }
    // 有值的情况
    if (!open) {
      setVisible(!!value);
    }
    setPopover(!popover);
    if (value.length < 6) {
      return promise.reject('');
    }
    if (value && confirmDirty) {
      form.validateFields(['confirm']);
    }
    return promise.resolve();
  };
  const renderPasswordProgress = () => {
    const value = form.getFieldValue('password');
    const passwordStatus = getPasswordStatus();
    return value && value.length ? (
      <div className={styles[`progress-${passwordStatus}`]}>
        <Progress
          status={passwordProgressMap[passwordStatus]}
          strokeWidth={6}
          percent={value.length * 10 > 100 ? 100 : value.length * 10}
          showInfo={false}
        />
      </div>
    ) : null;
  };
  
  // 渲染步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className={styles.stepContent}>
            <div className={styles.stepTitle}>设置您的账号信息</div>
            <FormItem
              name="username"
              className={styles.formItem}
              rules={[
                {
                  required: true,
                  message: '请输入用户名!',
                },
                {
                  min: 3,
                  message: '用户名至少3个字符!',
                },
                {
                  max: 20,
                  message: '用户名最多20个字符!',
                },
                {
                  pattern: /^[a-zA-Z0-9_]+$/,
                  message: '用户名只能包含字母、数字和下划线!',
                },
              ]}
              help="用户名将作为您的唯一标识，只能包含字母、数字和下划线"
            >
              <Input 
                size="large" 
                prefix={<UserOutlined />} 
                placeholder="用户名" 
                autoComplete="username"
              />
            </FormItem>
            <FormItem
              name="email"
              className={styles.formItem}
              rules={[
                {
                  required: true,
                  message: '请输入邮箱地址!',
                },
                {
                  type: 'email',
                  message: '邮箱地址格式错误!',
                },
              ]}
              help="请输入有效的邮箱地址，用于接收重要通知"
            >
              <Input 
                size="large" 
                prefix={<MailOutlined />} 
                placeholder="邮箱" 
                autoComplete="email"
              />
            </FormItem>
            <Popover
              getPopupContainer={(node) => {
                if (node && node.parentNode) {
                  return node.parentNode as HTMLElement;
                }
                return node;
              }}
              content={
                open && (
                  <div
                    style={{
                      padding: '4px 0',
                    }}
                  >
                    {passwordStatusMap[getPasswordStatus()]}
                    {renderPasswordProgress()}
                    <div
                      style={{
                        marginTop: 10,
                      }}
                    >
                      <span>请至少输入 6 个字符。请不要使用容易被猜到的密码。</span>
                    </div>
                  </div>
                )
              }
              overlayStyle={{
                width: 240,
              }}
              placement="right"
              open={open}
            >
              <FormItem
                name="password"
                className={`${styles.formItem} ${
                  form.getFieldValue('password') &&
                  form.getFieldValue('password').length > 0 &&
                  styles.password
                }`}
                rules={[
                  {
                    validator: checkPassword,
                  },
                ]}
                help="密码长度至少6位，建议使用字母、数字和特殊字符的组合"
              >
                <Input 
                  size="large" 
                  type="password" 
                  prefix={<LockOutlined />}
                  placeholder="至少6位密码，区分大小写" 
                  autoComplete="new-password"
                />
              </FormItem>
            </Popover>
            <FormItem
              name="confirm"
              className={styles.formItem}
              rules={[
                {
                  required: true,
                  message: '请确认密码!',
                },
                {
                  validator: checkConfirm,
                },
              ]}
              help="请再次输入密码以确认"
            >
              <Input 
                size="large" 
                type="password" 
                prefix={<LockOutlined />}
                placeholder="确认密码" 
                autoComplete="new-password"
              />
            </FormItem>
          </div>
        );
      case 1:
        return (
          <div className={styles.stepContent}>
            <div className={styles.stepTitle}>确认您的注册信息</div>
            <div className={styles.confirmInfo}>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>用户名:</span>
                <span className={styles.infoValue}>{formData.username}</span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>邮箱:</span>
                <span className={styles.infoValue}>{formData.email}</span>
              </div>
            </div>
            <Paragraph>
              <Text type="secondary">点击"注册"按钮完成账号创建，注册成功后您将可以使用NexusHub的所有功能。</Text>
            </Paragraph>
            <Paragraph>
              <Text type="secondary">注册即表示您同意我们的<Link to="/terms">服务条款</Link>和<Link to="/privacy">隐私政策</Link>。</Text>
            </Paragraph>
          </div>
        );
      default:
        return null;
    }
  };

  // 渲染步骤按钮
  const renderStepButtons = () => {
    if (currentStep === 0) {
      return (
        <Button type="primary" size="large" htmlType="submit" className={styles.submit}>
          下一步
        </Button>
      );
    }
    
    if (currentStep === 1) {
      return (
        <Space size="middle">
          <Button size="large" onClick={() => setCurrentStep(currentStep - 1)}>
            上一步
          </Button>
          <Button 
            type="primary" 
            size="large" 
            htmlType="submit" 
            loading={submitting}
            className={styles.submit}
          >
            注册
          </Button>
        </Space>
      );
    }
    
    return null;
  };

  return (
    <div className={styles.container}>
      <Helmet>
        <title>
          {intl.formatMessage({
            id: 'menu.register',
            defaultMessage: '注册页',
          })}
          - {Settings.title}
        </title>
      </Helmet>
      <Lang />
      <div className={styles.content}>
        <div className={styles.form}>
          <div className={styles.header}>
            <img alt="logo" className={styles.logo} src="/logo.svg" />
            <div>
              <h1 className={styles.title}>NexusHub</h1>
              <p className={styles.desc}>
                <FormattedMessage id="pages.login.appDesc" defaultMessage="一站式应用管理与分发平台" />
              </p>
            </div>
          </div>
          <Divider style={{ margin: '0 0 24px 0' }} />
          <div className={styles.main}>
            <div className={styles.stepsContainer}>
              <Steps current={currentStep} className={styles.steps}>
                <Step title="账号信息" description="设置用户名和密码" />
                <Step title="确认注册" description="确认信息并完成注册" />
              </Steps>
            </div>
            <Form form={form} name="UserRegister" onFinish={onFinish} layout="vertical">
              {renderStepContent()}
              <FormItem>
                {renderStepButtons()}
              </FormItem>
            </Form>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Register;