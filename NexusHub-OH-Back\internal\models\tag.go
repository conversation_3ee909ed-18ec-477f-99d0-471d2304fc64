package models

import (
	"time"

	"gorm.io/gorm"
)

// Tag 应用标签模型
type Tag struct {
	ID          uint      `gorm:"primarykey" json:"id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   *time.Time `gorm:"index" json:"deleted_at,omitempty"`
	Name        string `gorm:"type:varchar(50);uniqueIndex;not null" json:"name"`
	Description string `gorm:"type:text" json:"description"`
	Color       string `gorm:"type:varchar(20);default:'#666666'" json:"color"` // 标签颜色，十六进制
	IsActive    bool   `gorm:"default:true" json:"is_active"`                   // 是否启用
}

// TableName 指定表名
func (Tag) TableName() string {
	return "tags"
}

// AppTag 应用与标签的关联表
type AppTag struct {
	ID          uint      `gorm:"primarykey" json:"id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   *time.Time `gorm:"index" json:"deleted_at,omitempty"`
	ApplicationID uint `gorm:"index:idx_app_tag,unique" json:"application_id"` // 联合唯一索引
	TagID         uint `gorm:"index:idx_app_tag,unique" json:"tag_id"`         // 联合唯一索引
}

// TableName 指定表名
func (AppTag) TableName() string {
	return "app_tags"
}

// CreateTag 创建标签
func CreateTag(db *gorm.DB, tag *Tag) error {
	return db.Create(tag).Error
}

// GetTagByID 通过ID获取标签
func GetTagByID(db *gorm.DB, id uint) (*Tag, error) {
	var tag Tag
	err := db.First(&tag, id).Error
	return &tag, err
}

// GetTagByName 通过名称获取标签
func GetTagByName(db *gorm.DB, name string) (*Tag, error) {
	var tag Tag
	err := db.Where("name = ?", name).First(&tag).Error
	return &tag, err
}

// UpdateTag 更新标签
func UpdateTag(db *gorm.DB, tag *Tag) error {
	return db.Save(tag).Error
}

// DeleteTag 删除标签
func DeleteTag(db *gorm.DB, id uint) error {
	return db.Delete(&Tag{}, id).Error
}

// ListTags 获取所有标签
func ListTags(db *gorm.DB, includeInactive bool) ([]Tag, error) {
	var tags []Tag
	query := db.Order("name")

	if !includeInactive {
		query = query.Where("is_active = ?", true)
	}

	err := query.Find(&tags).Error
	return tags, err
}

// AddAppTag 为应用添加标签
func AddAppTag(db *gorm.DB, appID uint, tagID uint) error {
	appTag := AppTag{
		ApplicationID: appID,
		TagID:         tagID,
	}
	return db.Create(&appTag).Error
}

// RemoveAppTag 删除应用的标签
func RemoveAppTag(db *gorm.DB, appID uint, tagID uint) error {
	return db.Where("application_id = ? AND tag_id = ?", appID, tagID).Delete(&AppTag{}).Error
}

// GetAppTags 获取应用的所有标签
func GetAppTags(db *gorm.DB, appID uint) ([]Tag, error) {
	var tags []Tag
	err := db.Table("tags").
		Joins("JOIN app_tags ON app_tags.tag_id = tags.id").
		Where("app_tags.application_id = ? AND tags.is_active = ?", appID, true).
		Order("tags.name").
		Find(&tags).Error
	return tags, err
}

// GetAppsByTag 获取包含指定标签的应用
func GetAppsByTag(db *gorm.DB, tagID uint) ([]Application, error) {
	var apps []Application
	err := db.Table("applications").
		Joins("JOIN app_tags ON app_tags.application_id = applications.id").
		Where("app_tags.tag_id = ? AND applications.status = ?", tagID, ApplicationStatusApproved).
		Order("applications.download_count DESC").
		Find(&apps).Error
	return apps, err
}
