# NexusHub OpenHarmony 应用商店客户端

## 项目概述

NexusHub是一个专为OpenHarmony系统设计的应用商店客户端，提供应用浏览、下载、管理等功能。支持手机、平板和2in1设备的自适应界面设计。

## 功能特性

### 核心功能
- **首页**: 展示推荐应用、热门应用、最新应用等
- **分类**: 按类别浏览应用（游戏、工具、教育等）
- **搜索**: 支持应用名称、开发者、关键词搜索
- **应用详情**: 展示应用信息、截图、评论等
- **下载管理**: 应用下载、安装、更新管理
- **用户中心**: 个人信息、下载历史、设置等

### 界面适配
- **手机设备**: 竖屏布局，底部导航栏
- **平板/2in1设备**: 横屏布局，侧边导航栏
- **响应式设计**: 根据设备类型自动切换布局

## 技术架构

### 开发框架
- **平台**: OpenHarmony 5.0+
- **开发语言**: ArkTS
- **UI框架**: ArkUI
- **构建工具**: DevEco Studio
- **API版本**: 已适配鸿蒙Kit化API（@kit.ArkUI、@kit.ArkData等）

### 项目结构
```
src/main/ets/
├── entryability/          # 应用入口
├── pages/                 # 页面文件
│   ├── Index.ets         # 主页面
│   ├── Home.ets          # 首页
│   ├── Category.ets      # 分类页
│   ├── Search.ets        # 搜索页
│   ├── AppDetail.ets     # 应用详情页
│   ├── Download.ets      # 下载管理页
│   └── Profile.ets       # 个人中心页
├── components/            # 公共组件
│   ├── AppCard.ets       # 应用卡片组件
│   ├── NavigationBar.ets # 导航栏组件
│   ├── SearchBar.ets     # 搜索栏组件
│   └── LoadingView.ets   # 加载组件
├── services/             # 网络服务
│   ├── ApiService.ets    # API服务
│   └── HttpClient.ets    # HTTP客户端
├── models/               # 数据模型
│   ├── App.ets          # 应用模型
│   ├── Category.ets     # 分类模型
│   └── User.ets         # 用户模型
└── utils/                # 工具类
    ├── Constants.ets     # 常量定义
    ├── DeviceUtils.ets   # 设备工具
    └── FormatUtils.ets   # 格式化工具
```

## API接口

### 基础配置
- **服务器地址**: http://localhost:8080
- **API版本**: v1
- **认证方式**: JWT Token

### 主要接口

#### 公开接口（无需认证）
- `GET /api/v1/public/apps` - 获取应用列表
- `GET /api/v1/public/apps/{id}` - 获取应用详情
- `GET /api/v1/public/apps/recommended` - 获取推荐应用
- `GET /api/v1/public/apps/popular` - 获取热门应用
- `GET /api/v1/public/apps/latest` - 获取最新应用
- `GET /api/v1/public/categories` - 获取分类列表
- `GET /api/v1/public/categories/{id}/apps` - 根据分类获取应用
- `GET /api/v1/public/search/apps` - 搜索应用
- `GET /api/v1/public/search/suggestions` - 获取搜索建议
- `GET /api/v1/public/search/hot-keywords` - 获取热门关键词
- `POST /api/v1/public/apps/{id}/download` - 记录应用下载
- `GET /api/v1/apps/{id}/reviews` - 获取应用评论
- `GET /api/v1/public/health` - 健康检查
- `GET /api/v1/public/config` - 获取配置信息

#### 需要认证的接口
- `GET /api/v1/users/profile` - 获取用户信息
- `POST /api/v1/users/login` - 用户登录
- `POST /api/v1/users/register` - 用户注册

## 开发指南

### 环境要求
- DevEco Studio 5.0+
- OpenHarmony SDK 5.0+
- Node.js 16+

### 运行项目
1. 使用DevEco Studio打开项目
2. 配置OpenHarmony SDK
3. 连接设备或启动模拟器
4. 点击运行按钮

### 代码规范
- 使用ArkTS语言特性
- 遵循OpenHarmony开发规范
- 组件命名使用PascalCase
- 变量命名使用camelCase
- 常量命名使用UPPER_CASE

## 设计规范

### 颜色主题
- 主色调: #007DFF (蓝色)
- 辅助色: #36D1DC (青色)
- 背景色: #F5F5F5 (浅灰)
- 文字色: #333333 (深灰)

### 字体规范
- 标题: 18-24fp
- 正文: 14-16fp
- 辅助文字: 12fp

### 间距规范
- 页面边距: 16vp
- 组件间距: 8-12vp
- 内容间距: 4-8vp

## 版本历史

### v1.0.0 (开发中)
- 基础界面框架
- 首页和分类页面
- 应用详情页面
- 搜索功能
- 下载管理
- 用户中心

## 许可证

Apache-2.0 License

## 联系方式

- 作者: xiaoxiaozhou
- 邮箱: <EMAIL>
- 项目地址: https://github.com/nexushub/nexushub-oh
