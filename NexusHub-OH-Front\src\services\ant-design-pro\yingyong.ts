// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 获取应用列表 分页获取应用列表，支持类别和关键字筛选 GET /apps */
export async function getApps(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAppsParams,
  options?: { [key: string]: any },
) {
  return request<API.PageResponse & { data?: API.AppDetailsResponse[] }>('/apps', {
    method: 'GET',
    params: {
      // page has a default value: 1
      page: '1',
      // page_size has a default value: 20
      page_size: '20',

      ...params,
    },
    ...(options || {}),
  });
}

/** 创建应用 开发者创建新应用 POST /apps */
export async function postApps(body: API.CreateAppRequest, options?: { [key: string]: any }) {
  return request<API.Response & { data?: API.AppDetailsResponse }>('/apps', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取应用详情 获取应用的详细信息，包括截图和版本 GET /apps/${param0} */
export async function getAppsId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAppsIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Response & { data?: Record }>(`/apps/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 更新应用 开发者更新应用信息 PUT /apps/${param0} */
export async function putAppsId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putAppsIdParams,
  body: API.UpdateAppRequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Response & { data?: API.AppDetailsResponse }>(`/apps/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}
