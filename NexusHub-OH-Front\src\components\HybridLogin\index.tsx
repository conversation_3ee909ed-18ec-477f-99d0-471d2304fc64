import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, Divider, Space, Typography } from 'antd';
import { LogoutOutlined, UserOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { useLogtoAuth } from '@/hooks/useLogtoAuth';

const { Title, Text } = Typography;

interface HybridLoginProps {
  onTraditionalLogin?: () => void;
}

/**
 * 混合登录组件
 * 支持传统用户名密码登录和Logto SSO登录
 */
const HybridLogin: React.FC<HybridLoginProps> = ({ onTraditionalLogin }) => {
  const { initialState } = useModel('@@initialState');
  const [loginType, setLoginType] = useState<'traditional' | 'sso'>('traditional');

  // 如果没有启用Logto，只显示传统登录
  if (!initialState?.useLogto) {
    return null;
  }

  // 只有在启用Logto时才调用useLogtoAuth
  const { signIn, isLoading } = useLogtoAuth();

  const handleSSOLogin = async () => {
    try {
      await signIn();
    } catch (error) {
      console.error('SSO登录失败:', error);
    }
  };

  return (
    <div style={{ marginTop: 24 }}>
      <Divider>
        <Text type="secondary">或者</Text>
      </Divider>
      
      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        <Card 
          size="small" 
          style={{ 
            textAlign: 'center',
            background: '#f8f9fa',
            border: '1px dashed #d9d9d9'
          }}
        >
          <Title level={5} style={{ margin: '8px 0' }}>
            企业单点登录 (SSO)
          </Title>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            使用企业账号快速登录
          </Text>
        </Card>
        
        <Button
          type="primary"
          size="large"
          icon={<UserOutlined />}
          loading={isLoading}
          onClick={handleSSOLogin}
          style={{ width: '100%', height: '48px' }}
        >
          企业账号登录
        </Button>
        
        <div style={{ textAlign: 'center', marginTop: 16 }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            首次使用企业账号登录将自动创建账户
          </Text>
        </div>
      </Space>
    </div>
  );
};

export default HybridLogin;