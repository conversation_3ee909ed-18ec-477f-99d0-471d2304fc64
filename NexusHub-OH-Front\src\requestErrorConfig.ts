﻿import type { RequestOptions } from '@@/plugin-request/request';
import type { RequestConfig } from '@umijs/max';
import { message, notification } from 'antd';

// 错误处理方案： 错误类型
enum ErrorShowType {
  SILENT = 0,
  WARN_MESSAGE = 1,
  ERROR_MESSAGE = 2,
  NOTIFICATION = 3,
  REDIRECT = 9,
}

// 与后端约定的响应数据格式
interface ResponseStructure {
  code: number;
  message: string;
  data: any;
}

/**
 * @name 错误处理
 * pro 自带的错误处理， 可以在这里做自己的改动
 * @doc https://umijs.org/docs/max/request#配置
 */
export const errorConfig: RequestConfig = {
  // 错误处理： umi@3 的错误处理方案。
  errorConfig: {
    // 错误抛出
    errorThrower: (res) => {
      const { code, message, data } =
        res as unknown as ResponseStructure;
      if (code !== 200) {
        const error: any = new Error(message);
        error.name = 'BizError';
        error.info = { errorCode: code, errorMessage: message, data };
        throw error; // 抛出自制的错误
      }
    },
    // 错误接收及处理
    errorHandler: (error: any, opts: any) => {
      if (opts?.skipErrorHandler) throw error;
      // 我们的 errorThrower 抛出的错误。
      if (error.name === 'BizError') {
        const errorInfo: ResponseStructure | undefined = error.info;
        if (errorInfo) {
          const { errorMessage, errorCode } = errorInfo;
          switch (errorCode) {
            case 400:
              message.error(`请求参数错误: ${errorMessage}`);
              break;
            case 401:
              message.error('未授权，请重新登录');
              // 跳转到登录页
              window.location.href = '/user/login';
              break;
            case 403:
              message.error(`权限不足: ${errorMessage}`);
              break;
            case 404:
              message.error(`资源不存在: ${errorMessage}`);
              break;
            case 500:
              message.error(`服务器内部错误: ${errorMessage}`);
              break;
            default:
              message.error(errorMessage);
          }
        }
      } else if (error.response) {
        // Axios 的错误
        // 请求成功发出且服务器也响应了状态码，但状态代码超出了 2xx 的范围
        message.error(`Response status:${error.response.status}`);
      } else if (error.request) {
        // 请求已经成功发起，但没有收到响应
        // \`error.request\` 在浏览器中是 XMLHttpRequest 的实例，
        // 而在node.js中是 http.ClientRequest 的实例
        message.error('None response! Please retry.');
      } else {
        // 发送请求时出了点问题
        message.error('Request error, please retry.');
      }
    },
  },

  // 请求拦截器
  requestInterceptors: [
    (config: RequestOptions) => {
      // 拦截请求配置，进行个性化处理。
      // 从localStorage获取token
      const token = localStorage.getItem('token');
      // 如果有token则添加到请求头
      if (token) {
        config.headers = {
          ...config.headers,
          'Authorization': `Bearer ${token}`,
        };
      }
      return config;
    },
  ],
  
  // 响应拦截器
  responseInterceptors: [
    (response) => {
      // 拦截响应数据，进行个性化处理
      const { data } = response;
      // 根据API文档中的通用响应格式处理
      if (data && data.code !== undefined && data.code !== 200) {
        // 处理错误
        const error: any = new Error(data.message);
        error.name = 'BizError';
        error.info = { errorCode: data.code, errorMessage: data.message };
        throw error;
      }
      // 如果是标准格式，只返回data字段
      if (data && data.data !== undefined) {
        return { ...response, data: data.data };
      }
      return response;
    },
  ],
};
