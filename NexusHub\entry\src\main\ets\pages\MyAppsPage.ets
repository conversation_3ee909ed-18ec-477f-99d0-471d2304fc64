import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';
import { ApiService } from '../services/ApiService';
import { LoadingView, LoadingState } from '../components/LoadingView';
import { AppModel } from '../models/App';
import { router } from '@kit.ArkUI';
import { hilog } from '@kit.PerformanceAnalysisKit';

/**
 * 我的下载页面
 */
@Entry
@Component
struct MyAppsPage {
  @State downloadedApps: AppModel[] = [];
  @State loadingState: LoadingState = LoadingState.LOADING;
  @State selectedTab: number = 0; // 0: 全部, 1: 已安装, 2: 未安装

  private deviceUtils = DeviceUtils.getInstance();
  private apiService = ApiService.getInstance();

  aboutToAppear() {
    this.loadDownloadedApps();
  }

  /**
   * 加载已下载的应用
   */
  private async loadDownloadedApps() {
    try {
      this.loadingState = LoadingState.LOADING;
      // 这里应该调用API获取用户下载的应用
      // 暂时使用模拟数据
      await this.simulateApiCall();
      this.downloadedApps = this.getMockDownloadedApps();
      this.loadingState = LoadingState.SUCCESS;
    } catch (error) {
      hilog.error(0x0000, 'MyAppsPage', '加载下载应用失败: %{public}s', JSON.stringify(error));
      this.loadingState = LoadingState.ERROR;
    }
  }

  /**
   * 模拟API调用
   */
  private async simulateApiCall(): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 1000);
    });
  }

  /**
   * 获取模拟下载应用数据
   */
  private getMockDownloadedApps(): AppModel[] {
    return [
      {
        id: 1,
        created_at: '2024-01-15T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z',
        name: '微信',
        package_name: 'com.tencent.mm',
        description: '一个为智能终端提供即时通讯服务的免费应用程序',
        short_description: '即时通讯应用',
        icon: Constants.PLACEHOLDER_IMAGE,
        category_id: 1,
        category_name: '社交',
        developer_id: 1,
        developer_name: '腾讯科技',
        version: '8.0.32',
        version_code: 80032,
        min_sdk_version: 9,
        target_sdk_version: 12,
        size: 245760000,
        download_url: '',
        download_count: 1000000000,
        rating: 4.8,
        review_count: 50000,
        screenshots: [Constants.PLACEHOLDER_IMAGE],
        permissions: [],
        tags: ['社交', '通讯'],
        changelog: '修复已知问题',
        privacy_policy: '',
        support_email: '<EMAIL>',
        website: 'https://weixin.qq.com',
        status: 'installed',
        is_featured: false,
        is_editor_choice: false,
        is_top: false,
        published_at: '2024-01-15T00:00:00Z',
        review_status: 'approved',
        review_reason: '',
        reviewed_at: '2024-01-15T00:00:00Z',
        reviewer_id: 1
      },
      {
        id: 2,
        created_at: '2024-01-10T00:00:00Z',
        updated_at: '2024-01-10T00:00:00Z',
        name: '支付宝',
        package_name: 'com.eg.android.AlipayGphone',
        description: '数字生活开放平台',
        short_description: '移动支付应用',
        icon: Constants.PLACEHOLDER_IMAGE,
        category_id: 2,
        category_name: '金融',
        developer_id: 2,
        developer_name: '蚂蚁集团',
        version: '10.3.20',
        version_code: 103200,
        min_sdk_version: 9,
        target_sdk_version: 12,
        size: 156672000,
        download_url: '',
        download_count: 800000000,
        rating: 4.7,
        review_count: 30000,
        screenshots: [Constants.PLACEHOLDER_IMAGE],
        permissions: [],
        tags: ['支付', '金融'],
        changelog: '优化用户体验',
        privacy_policy: '',
        support_email: '<EMAIL>',
        website: 'https://www.alipay.com',
        status: 'published',
        is_featured: false,
        is_editor_choice: false,
        is_top: false,
        published_at: '2024-01-10T00:00:00Z',
        review_status: 'approved',
        review_reason: '',
        reviewed_at: '2024-01-10T00:00:00Z',
        reviewer_id: 1
      }
    ];
  }

  /**
   * 获取过滤后的应用列表
   */
  private getFilteredApps(): AppModel[] {
    switch (this.selectedTab) {
      case 1: // 已安装
        return this.downloadedApps.filter(app => app.status === 'installed');
      case 2: // 未安装
        return this.downloadedApps.filter(app => app.status !== 'installed');
      default: // 全部
        return this.downloadedApps;
    }
  }

  /**
   * 跳转到应用详情页面
   */
  private navigateToAppDetail(app: AppModel) {
    this.getUIContext().getRouter().pushUrl({
      url: 'pages/AppDetailPage',
      params: { appId: app.id }
    });
  }

  /**
   * 标签栏
   */
  @Builder
  private TabBar() {
    Row() {
      ForEach(['全部', '已安装', '未安装'], (title: string, index?: number) => {
        Text(title)
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontColor(this.selectedTab === (index ?? 0) ? Constants.COLORS.PRIMARY : Constants.COLORS.TEXT_SECONDARY)
          .fontWeight(this.selectedTab === (index ?? 0) ? FontWeight.Bold : FontWeight.Normal)
          .padding({ left: 16, right: 16, top: 12, bottom: 12 })
          .onClick(() => {
            this.selectedTab = index ?? 0;
          })
      }, (title: string, index?: number) => `tab_${index}_${title}`)
    }
    .width('100%')
    .backgroundColor(Constants.COLORS.WHITE)
    .justifyContent(FlexAlign.SpaceAround)
  }

  /**
   * 应用项
   */
  @Builder
  private AppItem(app: AppModel) {
    Row({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) }) {
      // 应用图标
      Image(app.icon)
        .width(this.deviceUtils.isTablet() ? 64 : 56)
        .height(this.deviceUtils.isTablet() ? 64 : 56)
        .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
        .objectFit(ImageFit.Cover)
        .backgroundColor(Constants.COLORS.BACKGROUND_LIGHT)

      // 应用信息
      Column({ space: 4 }) {
        Row() {
          Text(app.name)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .fontWeight(FontWeight.Medium)
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })
            .layoutWeight(1)

          Text(app.isInstalled ? '已安装' : '未安装')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(app.isInstalled ? Constants.COLORS.SUCCESS : Constants.COLORS.TEXT_HINT)
            .backgroundColor(app.isInstalled ? $r('app.color.overlay_medium') : $r('app.color.overlay_light'))
            .padding({ left: 8, right: 8, top: 2, bottom: 2 })
            .borderRadius(Constants.BORDER_RADIUS.SMALL)
        }
        .width('100%')

        Text(app.description)
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
          .maxLines(1)
          .textOverflow({ overflow: TextOverflow.Ellipsis })

        Row({ space: 8 }) {
          Text(`版本 ${app.version}`)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.TEXT_HINT)

          Text(`${((app.size || 0) / 1024 / 1024).toFixed(1)}MB`)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.TEXT_HINT)

          if (app.downloadTime) {
            Text(`下载于 ${app.downloadTime.split(' ')[0]}`)
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
              .fontColor(Constants.COLORS.TEXT_HINT)
          }
        }
      }
      .alignItems(HorizontalAlign.Start)
      .layoutWeight(1)
    }
    .width('100%')
    .padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
    .backgroundColor(Constants.COLORS.WHITE)
    .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
    .margin({ left: 16, right: 16, bottom: 8 })
    .onClick(() => this.navigateToAppDetail(app))
  }

  /**
   * 应用列表
   */
  @Builder
  private AppList() {
    if (this.getFilteredApps().length === 0) {
      Column({ space: 16 }) {
        Text('📱')
          .fontSize(48)
          .fontColor(Constants.COLORS.TEXT_HINT)
        
        Text('暂无下载记录')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
        
        Text('去应用商店发现更多精彩应用')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.TEXT_HINT)
      }
      .width('100%')
      .height(200)
      .justifyContent(FlexAlign.Center)
      .alignItems(HorizontalAlign.Center)
    } else {
      Column() {
        ForEach(this.getFilteredApps(), (app: AppModel) => {
          this.AppItem(app)
        }, (app: AppModel) => `app_${app.id}`)
      }
      .width('100%')
    }
  }

  build() {
    Column() {
      // 标题栏
      Row() {
        Text('←')
          .fontSize(24)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .onClick(() => {
            this.getUIContext().getRouter().back();
          })

        Text('我的下载')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .fontWeight(FontWeight.Bold)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('')
          .width(24)
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor(Constants.COLORS.WHITE)
      .alignItems(VerticalAlign.Center)

      if (this.loadingState === LoadingState.LOADING) {
        LoadingView({ state: LoadingState.LOADING })
          .layoutWeight(1)
      } else if (this.loadingState === LoadingState.ERROR) {
        LoadingView({ 
          state: LoadingState.ERROR,
          onRetry: (): Promise<void> => this.loadDownloadedApps()
        })
          .layoutWeight(1)
      } else {
        Column() {
          // 标签栏
          this.TabBar()
          
          Divider()
            .color(Constants.COLORS.BORDER)

          // 应用列表
          Scroll() {
            Column() {
              this.AppList()
              
              // 底部间距
              Column()
                .height(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE))
            }
          }
          .scrollable(ScrollDirection.Vertical)
          .scrollBar(BarState.Auto)
          .layoutWeight(1)
        }
        .layoutWeight(1)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Constants.COLORS.BACKGROUND)
  }
}

export { MyAppsPage };