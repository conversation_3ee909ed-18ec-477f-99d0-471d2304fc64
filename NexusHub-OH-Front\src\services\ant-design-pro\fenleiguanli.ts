// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 获取应用分类列表 获取所有应用分类 GET /categories */
export async function getCategories(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getCategoriesParams,
  options?: { [key: string]: any },
) {
  return request<API.CategoryResponse[]>('/categories', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建应用分类 创建一个新的应用分类 POST /categories */
export async function postCategories(
  body: API.CreateCategoryRequest,
  options?: { [key: string]: any },
) {
  return request<API.CategoryResponse>('/categories', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取应用分类 获取应用分类详情 GET /categories/${param0} */
export async function getCategoriesId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getCategoriesIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.CategoryDetailResponse>(`/categories/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 更新应用分类 更新应用分类信息 PUT /categories/${param0} */
export async function putCategoriesId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.putCategoriesIdParams,
  body: API.UpdateCategoryRequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.CategoryResponse>(`/categories/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 删除应用分类 删除应用分类 DELETE /categories/${param0} */
export async function deleteCategoriesId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteCategoriesIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.SuccessResponse>(`/categories/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取根应用分类列表 获取所有根应用分类（没有父分类的分类） GET /categories/root */
export async function getCategoriesRoot(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getCategoriesRootParams,
  options?: { [key: string]: any },
) {
  return request<API.CategoryResponse[]>('/categories/root', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
