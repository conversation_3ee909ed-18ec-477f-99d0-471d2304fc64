package models

import (
	"time"

	"gorm.io/gorm"
)

// HelpCategory 帮助分类模型
type HelpCategory struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	Name        string `gorm:"type:varchar(100);not null" json:"name"`        // 分类名称
	Description string `gorm:"type:text" json:"description"`                  // 分类描述
	Icon        string `gorm:"type:varchar(255)" json:"icon"`                 // 分类图标
	SortOrder   int    `gorm:"default:0" json:"sort_order"`                   // 排序
	IsActive    bool   `gorm:"default:true" json:"is_active"`                 // 是否启用

	// 创建者信息
	CreatedBy uint `gorm:"not null" json:"created_by"`
	Creator   User `gorm:"foreignKey:CreatedBy" json:"creator,omitempty"`

	// 关联的帮助文章
	Articles []HelpArticle `gorm:"foreignKey:CategoryID" json:"articles,omitempty"`
}

// TableName 指定表名
func (HelpCategory) TableName() string {
	return "help_categories"
}

// HelpArticle 帮助文章模型
type HelpArticle struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 基本信息
	Title       string `gorm:"type:varchar(200);not null" json:"title"`       // 标题
	Content     string `gorm:"type:text;not null" json:"content"`             // 内容
	Summary     string `gorm:"type:varchar(500)" json:"summary"`              // 摘要
	Thumbnail   string `gorm:"type:varchar(255)" json:"thumbnail"`            // 缩略图
	Tags        string `gorm:"type:varchar(500)" json:"tags"`                 // 标签，逗号分隔
	SortOrder   int    `gorm:"default:0" json:"sort_order"`                   // 排序
	ViewCount   int    `gorm:"default:0" json:"view_count"`                   // 浏览次数
	LikeCount   int    `gorm:"default:0" json:"like_count"`                   // 点赞次数
	IsPublished bool   `gorm:"default:false" json:"is_published"`             // 是否发布
	IsFeatured  bool   `gorm:"default:false" json:"is_featured"`              // 是否推荐

	// 分类信息
	CategoryID uint         `gorm:"not null;index" json:"category_id"`
	Category   HelpCategory `gorm:"foreignKey:CategoryID" json:"category,omitempty"`

	// 创建者和更新者信息
	CreatedBy uint `gorm:"not null" json:"created_by"`
	Creator   User `gorm:"foreignKey:CreatedBy" json:"creator,omitempty"`
	UpdatedBy uint `json:"updated_by"`
	Updater   User `gorm:"foreignKey:UpdatedBy" json:"updater,omitempty"`

	// SEO相关
	MetaTitle       string `gorm:"type:varchar(200)" json:"meta_title"`
	MetaDescription string `gorm:"type:varchar(500)" json:"meta_description"`
	MetaKeywords    string `gorm:"type:varchar(500)" json:"meta_keywords"`

	// 发布时间
	PublishedAt *time.Time `json:"published_at"`
}

// TableName 指定表名
func (HelpArticle) TableName() string {
	return "help_articles"
}

// HelpArticleView 帮助文章浏览记录
type HelpArticleView struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 文章信息
	ArticleID uint        `gorm:"not null;index" json:"article_id"`
	Article   HelpArticle `gorm:"foreignKey:ArticleID" json:"article,omitempty"`

	// 用户信息（可选，支持匿名浏览）
	UserID *uint `gorm:"index" json:"user_id"`
	User   *User `gorm:"foreignKey:UserID" json:"user,omitempty"`

	// 浏览信息
	IPAddress string `gorm:"type:varchar(45)" json:"ip_address"`
	UserAgent string `gorm:"type:text" json:"user_agent"`
	Referrer  string `gorm:"type:varchar(500)" json:"referrer"`
}

// TableName 指定表名
func (HelpArticleView) TableName() string {
	return "help_article_views"
}

// 响应结构体

// HelpCategoryResponse 帮助分类响应
type HelpCategoryResponse struct {
	ID           uint      `json:"id"`
	Name         string    `json:"name"`
	Description  string    `json:"description"`
	Icon         string    `json:"icon"`
	SortOrder    int       `json:"sort_order"`
	IsActive     bool      `json:"is_active"`
	CreatedAt    time.Time `json:"created_at"`
	ArticleCount int       `json:"article_count"` // 文章数量
}

// HelpArticleResponse 帮助文章响应
type HelpArticleResponse struct {
	ID          uint      `json:"id"`
	Title       string    `json:"title"`
	Content     string    `json:"content"`
	Summary     string    `json:"summary"`
	Thumbnail   string    `json:"thumbnail"`
	Tags        []string  `json:"tags"`
	SortOrder   int       `json:"sort_order"`
	ViewCount   int       `json:"view_count"`
	LikeCount   int       `json:"like_count"`
	IsPublished bool      `json:"is_published"`
	IsFeatured  bool      `json:"is_featured"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	PublishedAt *time.Time `json:"published_at"`

	// 分类信息
	Category HelpCategoryResponse `json:"category"`

	// 创建者信息
	Creator struct {
		ID       uint   `json:"id"`
		Username string `json:"username"`
		Avatar   string `json:"avatar"`
	} `json:"creator"`
}

// HelpArticleListResponse 帮助文章列表响应
type HelpArticleListResponse struct {
	ID          uint      `json:"id"`
	Title       string    `json:"title"`
	Summary     string    `json:"summary"`
	Thumbnail   string    `json:"thumbnail"`
	Tags        []string  `json:"tags"`
	ViewCount   int       `json:"view_count"`
	LikeCount   int       `json:"like_count"`
	IsPublished bool      `json:"is_published"`
	IsFeatured  bool      `json:"is_featured"`
	CreatedAt   time.Time `json:"created_at"`
	PublishedAt *time.Time `json:"published_at"`

	// 分类信息
	Category struct {
		ID   uint   `json:"id"`
		Name string `json:"name"`
	} `json:"category"`
}