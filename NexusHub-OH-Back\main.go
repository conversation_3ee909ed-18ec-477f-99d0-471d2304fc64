package main

import (
	"log"

	"nexushub-oh-back/cmd/server"
	_ "nexushub-oh-back/docs" // 导入 Swagger 生成的文档
)

//	@title			NexusHub-OH API
//	@version		1.0
//	@description	NexusHub-OH 应用商店后端 API，为 OpenHarmony 系统应用商店提供完整的 API
//	@termsOfService	http://swagger.io/terms/

//	@contact.name	API Support
//	@contact.url	http://www.nexushub-oh.top/support
//	@contact.email	<EMAIL>

//	@host		localhost:8080
//	@BasePath	/api/v1
//	@schemes	http https

//	@securityDefinitions.apikey	Bearer
//	@in							header
//	@name						Authorization
//	@description				请在值前加上 "Bearer " 前缀, 例如 "Bearer abcde12345". 所有需要认证的API都需要在请求头中带上此令牌。

func main() {
	log.Println("程序开始启动...")
	// 启动服务器
	if err := server.Run(); err != nil {
		log.Fatalf("无法启动服务器: %v", err)
	}
	log.Println("程序正常退出")
}
