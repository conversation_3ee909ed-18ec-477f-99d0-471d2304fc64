import { Radar } from '@ant-design/plots';
import { PageContainer } from '@ant-design/pro-components';
import { Avatar, Card, Col, List, Row, Skeleton, Statistic, Button, Modal, Form, Input, DatePicker, Tag } from 'antd';
import { AppstoreOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import type { FC } from 'react';
import { useState, useEffect } from 'react';
import EditableLinkGroup from './components/EditableLinkGroup';
import type { ActivitiesType, CurrentUser } from './data.d';
import { queryProjectNotice, queryActivities, queryTasks, createTask, updateTask, deleteTask } from './service';
import useStyles from './style.style';
dayjs.extend(relativeTime);

const links = [
  {
    title: '创建任务',
    href: '',
  },
  {
    title: '查看应用',
    href: '/app/list',
  },
  {
    title: '查看统计',
    href: '/statistics/app',
  },
  {
    title: '发布应用',
    href: '/app/create',
  },
  {
    title: '系统监控',
    href: '/dashboard/monitor',
  },
];

const PageHeaderContent: FC<{
  currentUser: Partial<CurrentUser>;
}> = ({ currentUser }) => {
  const { styles } = useStyles();
  const loading = currentUser && Object.keys(currentUser).length;
  if (!loading) {
    return (
      <Skeleton
        avatar
        paragraph={{
          rows: 1,
        }}
        active
      />
    );
  }
  return (
    <div className={styles.pageHeaderContent}>
      <div className={styles.avatar}>
        <Avatar size="large" src={currentUser.avatar} />
      </div>
      <div className={styles.content}>
        <div className={styles.contentTitle}>
          欢迎回来，
          {currentUser.name}
          ，祝你开心每一天！
        </div>
        <div>
          {currentUser.title} |{currentUser.group}
        </div>
      </div>
    </div>
  );
};

const ExtraContent: FC<Record<string, any>> = ({ appCount, downloadCount, rating }) => {
  const { styles } = useStyles();
  return (
    <div className={styles.extraContent}>
      <div className={styles.statItem}>
        <Statistic title="我的应用" value={appCount} />
      </div>
      <div className={styles.statItem}>
        <Statistic title="总下载量" value={downloadCount} />
      </div>
      <div className={styles.statItem}>
        <Statistic title="总评分" value={rating} precision={1} suffix="/ 5" />
      </div>
    </div>
  );
};

const Workplace: FC = () => {
  const { styles } = useStyles();
  const [summary, setSummary] = useState<any>({});
  const [activities, setActivities] = useState<ActivitiesType[]>([]);
  const [tasks, setTasks] = useState<any[]>([]);
  const [projectLoading, setProjectLoading] = useState<boolean>(true);
  const [activitiesLoading, setActivitiesLoading] = useState<boolean>(true);
  const [taskModalVisible, setTaskModalVisible] = useState<boolean>(false);
  const [currentTask, setCurrentTask] = useState<any>(null);
  const [form] = Form.useForm();
  
  // 获取工作台数据
  useEffect(() => {
    const fetchData = async () => {
      setProjectLoading(true);
      setActivitiesLoading(true);
      try {
        // 获取摘要数据
        const summaryResult = await queryProjectNotice();
        setSummary(summaryResult.data || {});
        
        // 获取活动数据
        const activitiesResult = await queryActivities();
        setActivities(activitiesResult.data || []);
        
        // 获取任务数据
        const tasksResult = await queryTasks();
        setTasks(tasksResult.data || []);
      } catch (error) {
        console.error('获取工作台数据失败:', error);
      } finally {
        setProjectLoading(false);
        setActivitiesLoading(false);
      }
    };
    
    fetchData();
  }, []);
  
  // 渲染活动内容
  const renderActivities = (item: ActivitiesType) => {
    const events = item.template.split(/@\{([^{}]*)\}/gi).map((key) => {
      if (item[key as keyof ActivitiesType]) {
        const value = item[key as 'user'];
        return (
          <a href={value?.link} key={value?.name}>
            {value.name}
          </a>
        );
      }
      return key;
    });
    return (
      <List.Item key={item.id}>
        <List.Item.Meta
          avatar={<Avatar src={item.user.avatar} />}
          title={
            <span>
              <a className={styles.username}>{item.user.name}</a>
              &nbsp;
              <span className={styles.event}>{events}</span>
            </span>
          }
          description={
            <span className={styles.datetime} title={item.updatedAt}>
              {dayjs(item.updatedAt).fromNow()}
            </span>
          }
        />
      </List.Item>
    );
  };
  
  // 打开任务编辑模态框
  const openTaskModal = (task?: any) => {
    setCurrentTask(task || null);
    form.resetFields();
    if (task) {
      form.setFieldsValue({
        title: task.title,
        content: task.content,
        deadline: task.deadline ? dayjs(task.deadline) : null,
        priority: task.priority,
      });
    }
    setTaskModalVisible(true);
  };
  
  // 提交任务表单
  const handleTaskSubmit = async () => {
    try {
      const values = await form.validateFields();
      const taskData = {
        ...values,
        deadline: values.deadline ? values.deadline.format('YYYY-MM-DD HH:mm:ss') : null,
      };
      
      if (currentTask) {
        // 更新任务
        await updateTask(currentTask.id, taskData);
      } else {
        // 创建任务
        await createTask(taskData);
      }
      
      // 刷新任务列表
      const tasksResult = await queryTasks();
      setTasks(tasksResult.data || []);
      
      setTaskModalVisible(false);
    } catch (error) {
      console.error('保存任务失败:', error);
    }
  };
  
  // 删除任务
  const handleDeleteTask = async (id: string) => {
    try {
      await deleteTask(id);
      
      // 刷新任务列表
      const tasksResult = await queryTasks();
      setTasks(tasksResult.data || []);
    } catch (error) {
      console.error('删除任务失败:', error);
    }
  };

  return (
    <PageContainer
      content={
        <PageHeaderContent
          currentUser={{
            avatar: summary.avatar || 'https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png',
            name: summary.username || '开发者',
            userid: summary.id,
            email: summary.email,
            signature: summary.signature || '应用开发者',
            title: summary.role || '开发者',
            group: summary.organization || 'NexusHub',
          }}
        />
      }
      extraContent={
        <ExtraContent 
          appCount={summary.app_count}
          downloadCount={summary.download_count}
          rating={summary.avg_rating}
        />
      }
    >
      <Row gutter={24}>
        <Col xl={16} lg={24} md={24} sm={24} xs={24}>
          <Card
            className={styles.projectList}
            style={{
              marginBottom: 24,
            }}
            title="我的应用"
            bordered={false}
            extra={<a href="/app/list">查看全部</a>}
            loading={projectLoading}
            bodyStyle={{
              padding: 0,
            }}
          >
            {(summary.apps || []).map((item: any) => (
              <Card.Grid className={styles.projectGrid} key={item.id}>
                <Card
                  bodyStyle={{
                    padding: 0,
                  }}
                  bordered={false}
                >
                  <Card.Meta
                    title={
                      <div className={styles.cardTitle}>
                        <Avatar 
                    size="small" 
                    src={item.icon}
                    icon={<AppstoreOutlined />}
                    onError={() => {
                      console.warn('应用图标加载失败:', item.icon);
                      return false;
                    }}
                  />
                        <a href={`/app/detail/${item.id}`}>{item.name}</a>
                      </div>
                    }
                    description={item.description}
                  />
                  <div className={styles.projectItemContent}>
                    <div>下载量: {item.download_count}</div>
                    {item.updated_at && (
                      <span className={styles.datetime} title={item.updated_at}>
                        {dayjs(item.updated_at).fromNow()}
                      </span>
                    )}
                  </div>
                </Card>
              </Card.Grid>
            ))}
          </Card>
          <Card
            bodyStyle={{
              padding: 0,
            }}
            bordered={false}
            className={styles.activeCard}
            title="最近活动"
            loading={activitiesLoading}
          >
            <List<ActivitiesType>
              loading={activitiesLoading}
              renderItem={(item) => renderActivities(item)}
              dataSource={activities}
              className={styles.activitiesList}
              size="large"
            />
          </Card>
        </Col>
        <Col xl={8} lg={24} md={24} sm={24} xs={24}>
          <Card
            style={{
              marginBottom: 24,
            }}
            title="快速开始 / 便捷导航"
            bordered={false}
            bodyStyle={{
              padding: 0,
            }}
          >
            <EditableLinkGroup onAdd={() => {}} links={links} linkElement="a" />
          </Card>
          <Card
            style={{
              marginBottom: 24,
            }}
            bordered={false}
            title="任务列表"
            extra={<Button type="primary" size="small" onClick={() => openTaskModal()}>新建</Button>}
          >
            <div>
              {tasks.map(task => (
                <div key={task.id} className={styles.taskItem} style={{ marginBottom: 16, padding: 16, border: '1px solid #f0f0f0', borderRadius: 4 }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <h3>{task.title}</h3>
                    <div>
                      {task.priority === 'high' && <Tag color="red">高</Tag>}
                      {task.priority === 'medium' && <Tag color="orange">中</Tag>}
                      {task.priority === 'low' && <Tag color="green">低</Tag>}
                    </div>
                  </div>
                  <p>{task.content}</p>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: 8 }}>
                    <span>{task.deadline ? dayjs(task.deadline).format('YYYY-MM-DD') : '无截止日期'}</span>
                    <div>
                      <Button type="link" size="small" onClick={() => openTaskModal(task)}>编辑</Button>
                      <Button type="link" danger size="small" onClick={() => handleDeleteTask(task.id)}>删除</Button>
                    </div>
                  </div>
                </div>
              ))}
              {tasks.length === 0 && <div style={{ textAlign: 'center', padding: 16 }}>暂无任务</div>}
            </div>
          </Card>
        </Col>
      </Row>
      
      {/* 任务编辑模态框 */}
      <Modal
        title={currentTask ? '编辑任务' : '新建任务'}
        open={taskModalVisible}
        onOk={handleTaskSubmit}
        onCancel={() => setTaskModalVisible(false)}
      >
        <Form form={form} layout="vertical">
          <Form.Item name="title" label="任务标题" rules={[{ required: true, message: '请输入任务标题' }]}>
            <Input placeholder="请输入任务标题" />
          </Form.Item>
          <Form.Item name="content" label="任务内容">
            <Input.TextArea rows={4} placeholder="请输入任务内容" />
          </Form.Item>
          <Form.Item name="deadline" label="截止日期">
            <DatePicker showTime style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item name="priority" label="优先级" rules={[{ required: true, message: '请选择优先级' }]} initialValue="medium">
            <Input.Group compact>
              <div className="ant-radio-group">
                <span style={{ marginRight: 16 }}>
                  <Tag color="green" onClick={() => form.setFieldsValue({ priority: 'low' })} style={{ cursor: 'pointer', padding: '5px 10px' }}>
                    低优先级
                  </Tag>
                </span>
                <span style={{ marginRight: 16 }}>
                  <Tag color="orange" onClick={() => form.setFieldsValue({ priority: 'medium' })} style={{ cursor: 'pointer', padding: '5px 10px' }}>
                    中优先级
                  </Tag>
                </span>
                <span>
                  <Tag color="red" onClick={() => form.setFieldsValue({ priority: 'high' })} style={{ cursor: 'pointer', padding: '5px 10px' }}>
                    高优先级
                  </Tag>
                </span>
              </div>
            </Input.Group>
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default Workplace;
