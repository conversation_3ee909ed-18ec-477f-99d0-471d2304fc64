import { Line, Tiny } from '@ant-design/plots';
import { Card, Col, Row, Tabs } from 'antd';
import type { DataItem, OfflineDataType } from '../data.d';
import useStyles from '../style.style';
import NumberInfo from './NumberInfo';
import numeral from 'numeral';

const CustomTab = ({
  data,
  currentTabKey: currentKey,
}: {
  data: any;
  currentTabKey: string;
}) => {
  // 计算百分比：应用数量占总数的比例
  const total = data.app_count + data.download_count > 0 ? data.app_count / (data.app_count + data.download_count) : 0;
  
  return (
    <Row
      gutter={8}
      style={{
        width: 138,
        margin: '8px 0',
      }}
    >
      <Col span={12}>
        <NumberInfo
          title={data.category_name}
          subTitle="应用数量"
          gap={2}
          total={numeral(data.app_count).format('0,0')}
          theme={currentKey !== data.category_name ? 'light' : undefined}
        />
      </Col>
      <Col
        span={12}
        style={{
          paddingTop: 36,
        }}
      >
        <Tiny.Ring height={60} width={60} percent={total} color={['#E8EEF4', '#5FABF4']} />
      </Col>
    </Row>
  );
};

const OfflineData = ({
  activeKey,
  loading,
  offlineData,
  offlineChartData,
  handleTabChange,
}: {
  activeKey: string;
  loading: boolean;
  offlineData: any[];
  offlineChartData: DataItem[];
  handleTabChange: (activeKey: string) => void;
}) => {
  const { styles } = useStyles();
  return (
    <Card
      loading={loading}
      className={styles.offlineCard}
      bordered={false}
      style={{
        marginTop: 32,
      }}
      title="分类统计"
    >
      <Tabs
        activeKey={activeKey}
        onChange={handleTabChange}
        items={(offlineData || []).map((category) => ({
          key: category.category_name,
          label: <CustomTab data={category} currentTabKey={activeKey} />,
          children: (
            <div
              style={{
                padding: '0 24px',
              }}
            >
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Card bordered={false} title="应用数量">
                    <div style={{fontSize: '24px', fontWeight: 'bold', textAlign: 'center'}}>
                      {numeral(category.app_count).format('0,0')}
                    </div>
                  </Card>
                </Col>
                <Col span={12}>
                  <Card bordered={false} title="下载总量">
                    <div style={{fontSize: '24px', fontWeight: 'bold', textAlign: 'center'}}>
                      {numeral(category.download_count).format('0,0')}
                    </div>
                  </Card>
                </Col>
              </Row>
            </div>
          ),
        }))}
      />
    </Card>
  );
};
export default OfflineData;
