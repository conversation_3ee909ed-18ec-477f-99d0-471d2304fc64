if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface MyReviewsPage_Params {
    reviews?: ReviewModel[];
    loadingState?: LoadingState;
    selectedTab?: number;
    deviceUtils?;
    apiService?;
}
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { DeviceUtils } from "@normalized:N&&&entry/src/main/ets/utils/DeviceUtils&";
import { ApiService } from "@normalized:N&&&entry/src/main/ets/services/ApiService&";
import { LoadingView, LoadingState } from "@normalized:N&&&entry/src/main/ets/components/LoadingView&";
import hilog from "@ohos:hilog";
'@ohos.router';
/**
 * 评论模型
 */
interface ReviewModel {
    id: number;
    appId: number;
    appName: string;
    appIcon: string;
    rating: number;
    content: string;
    createTime: string;
    updateTime?: string;
    likeCount: number;
    replyCount: number;
    isLiked: boolean;
}
class MyReviewsPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__reviews = new ObservedPropertyObjectPU([], this, "reviews");
        this.__loadingState = new ObservedPropertySimplePU(LoadingState.LOADING, this, "loadingState");
        this.__selectedTab = new ObservedPropertySimplePU(0, this, "selectedTab");
        this.deviceUtils = DeviceUtils.getInstance();
        this.apiService = ApiService.getInstance();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: MyReviewsPage_Params) {
        if (params.reviews !== undefined) {
            this.reviews = params.reviews;
        }
        if (params.loadingState !== undefined) {
            this.loadingState = params.loadingState;
        }
        if (params.selectedTab !== undefined) {
            this.selectedTab = params.selectedTab;
        }
        if (params.deviceUtils !== undefined) {
            this.deviceUtils = params.deviceUtils;
        }
        if (params.apiService !== undefined) {
            this.apiService = params.apiService;
        }
    }
    updateStateVars(params: MyReviewsPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__reviews.purgeDependencyOnElmtId(rmElmtId);
        this.__loadingState.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedTab.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__reviews.aboutToBeDeleted();
        this.__loadingState.aboutToBeDeleted();
        this.__selectedTab.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __reviews: ObservedPropertyObjectPU<ReviewModel[]>;
    get reviews() {
        return this.__reviews.get();
    }
    set reviews(newValue: ReviewModel[]) {
        this.__reviews.set(newValue);
    }
    private __loadingState: ObservedPropertySimplePU<LoadingState>;
    get loadingState() {
        return this.__loadingState.get();
    }
    set loadingState(newValue: LoadingState) {
        this.__loadingState.set(newValue);
    }
    private __selectedTab: ObservedPropertySimplePU<number>; // 0: 全部, 1: 最新, 2: 最热
    get selectedTab() {
        return this.__selectedTab.get();
    }
    set selectedTab(newValue: number) {
        this.__selectedTab.set(newValue);
    }
    private deviceUtils;
    private apiService;
    aboutToAppear() {
        this.loadMyReviews();
    }
    /**
     * 加载我的评论
     */
    private async loadMyReviews() {
        try {
            this.loadingState = LoadingState.LOADING;
            // 这里应该调用API获取用户的评论
            // 暂时使用模拟数据
            await this.simulateApiCall();
            this.reviews = this.getMockReviews();
            this.loadingState = LoadingState.SUCCESS;
        }
        catch (error) {
            hilog.error(0x0000, 'MyReviewsPage', '加载评论失败: %{public}s', JSON.stringify(error));
            this.loadingState = LoadingState.ERROR;
        }
    }
    /**
     * 模拟API调用
     */
    private async simulateApiCall(): Promise<void> {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve();
            }, 1000);
        });
    }
    /**
     * 获取模拟评论数据
     */
    private getMockReviews(): ReviewModel[] {
        return [
            {
                id: 1,
                appId: 1,
                appName: '微信',
                appIcon: Constants.PLACEHOLDER_IMAGE,
                rating: 5,
                content: '非常好用的社交软件，界面简洁，功能强大，是日常生活中不可缺少的应用。',
                createTime: '2024-01-20 14:30:00',
                likeCount: 12,
                replyCount: 3,
                isLiked: false
            },
            {
                id: 2,
                appId: 2,
                appName: '支付宝',
                appIcon: Constants.PLACEHOLDER_IMAGE,
                rating: 4,
                content: '支付很方便，但是启动速度有点慢，希望能优化一下性能。',
                createTime: '2024-01-18 09:15:00',
                updateTime: '2024-01-18 10:20:00',
                likeCount: 8,
                replyCount: 1,
                isLiked: true
            },
            {
                id: 3,
                appId: 3,
                appName: '王者荣耀',
                appIcon: Constants.PLACEHOLDER_IMAGE,
                rating: 3,
                content: '游戏画质不错，但是匹配机制需要改进，经常遇到实力差距很大的队友。',
                createTime: '2024-01-15 20:45:00',
                likeCount: 25,
                replyCount: 7,
                isLiked: false
            },
            {
                id: 4,
                appId: 4,
                appName: '抖音',
                appIcon: Constants.PLACEHOLDER_IMAGE,
                rating: 5,
                content: '内容丰富多彩，推荐算法很精准，总能刷到感兴趣的视频。',
                createTime: '2024-01-12 16:20:00',
                likeCount: 18,
                replyCount: 2,
                isLiked: true
            }
        ];
    }
    /**
     * 获取排序后的评论列表
     */
    private getSortedReviews(): ReviewModel[] {
        let sortedReviews = [...this.reviews];
        switch (this.selectedTab) {
            case 1: // 最新
                sortedReviews.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime());
                break;
            case 2: // 最热
                sortedReviews.sort((a, b) => (b.likeCount + b.replyCount) - (a.likeCount + a.replyCount));
                break;
            default: // 全部
                break;
        }
        return sortedReviews;
    }
    /**
     * 跳转到应用详情页面
     */
    private navigateToAppDetail(review: ReviewModel) {
        this.getUIContext().getRouter().pushUrl({
            url: 'pages/AppDetailPage',
            params: { appId: review.appId }
        });
    }
    /**
     * 点赞/取消点赞
     */
    private async toggleLike(review: ReviewModel) {
        try {
            // 这里应该调用API进行点赞操作
            // 暂时直接修改本地状态
            const index = this.reviews.findIndex(item => item.id === review.id);
            if (index !== -1) {
                this.reviews[index].isLiked = !this.reviews[index].isLiked;
                this.reviews[index].likeCount += this.reviews[index].isLiked ? 1 : -1;
            }
        }
        catch (error) {
            hilog.error(0x0000, 'MyReviewsPage', '点赞操作失败: %{public}s', JSON.stringify(error));
        }
    }
    /**
     * 删除评论
     */
    private async deleteReview(review: ReviewModel) {
        try {
            // 这里应该调用API删除评论
            // 暂时直接从列表中移除
            const index = this.reviews.findIndex(item => item.id === review.id);
            if (index !== -1) {
                this.reviews.splice(index, 1);
            }
        }
        catch (error) {
            hilog.error(0x0000, 'MyReviewsPage', '删除评论失败: %{public}s', JSON.stringify(error));
        }
    }
    /**
     * 标签栏
     */
    private TabBar(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.width('100%');
            Row.backgroundColor(Constants.COLORS.WHITE);
            Row.justifyContent(FlexAlign.SpaceAround);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const title = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(title);
                    Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                    Text.fontColor(this.selectedTab === index ? Constants.COLORS.PRIMARY : Constants.COLORS.TEXT_SECONDARY);
                    Text.fontWeight(this.selectedTab === index ? FontWeight.Bold : FontWeight.Normal);
                    Text.padding({ left: 16, right: 16, top: 12, bottom: 12 });
                    Text.onClick(() => {
                        this.selectedTab = index;
                    });
                }, Text);
                Text.pop();
            };
            this.forEachUpdateFunction(elmtId, ['全部', '最新', '最热'], forEachItemGenFunction, (title: string) => title, true, false);
        }, ForEach);
        ForEach.pop();
        Row.pop();
    }
    /**
     * 星级评分
     */
    private StarRating(rating: number, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 2 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const i = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(i <= rating ? '⭐' : '☆');
                    Text.fontSize(14);
                    Text.fontColor(i <= rating ? { "id": 16777233, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" } : Constants.COLORS.TEXT_HINT);
                }, Text);
                Text.pop();
            };
            this.forEachUpdateFunction(elmtId, [1, 2, 3, 4, 5], forEachItemGenFunction, (i: number) => `star_${i}`, false, false);
        }, ForEach);
        ForEach.pop();
        Row.pop();
    }
    /**
     * 评论项
     */
    private ReviewItem(review: ReviewModel, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 12 });
            Column.width('100%');
            Column.padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM));
            Column.backgroundColor(Constants.COLORS.WHITE);
            Column.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            Column.margin({ left: 16, right: 16, bottom: 8 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 应用信息
            Row.create({ space: 12 });
            // 应用信息
            Row.width('100%');
            // 应用信息
            Row.onClick(() => this.navigateToAppDetail(review));
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create(review.appIcon);
            Image.width(40);
            Image.height(40);
            Image.borderRadius(Constants.BORDER_RADIUS.SMALL);
            Image.objectFit(ImageFit.Cover);
            Image.backgroundColor(Constants.COLORS.BACKGROUND_LIGHT);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 4 });
            Column.alignItems(HorizontalAlign.Start);
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(review.appName);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.fontWeight(FontWeight.Medium);
        }, Text);
        Text.pop();
        this.StarRating.bind(this)(review.rating);
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🗑️');
            Text.fontSize(16);
            Text.fontColor(Constants.COLORS.TEXT_HINT);
            Text.padding(8);
            Text.onClick(() => {
                this.deleteReview(review);
            });
        }, Text);
        Text.pop();
        // 应用信息
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 评论内容
            Text.create(review.content);
            // 评论内容
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            // 评论内容
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            // 评论内容
            Text.lineHeight(20);
            // 评论内容
            Text.width('100%');
        }, Text);
        // 评论内容
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 时间和互动信息
            Row.create();
            // 时间和互动信息
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(review.createTime.split(' ')[0]);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_HINT);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (review.updateTime && review.updateTime !== review.createTime) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('(已编辑)');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                        Text.fontColor(Constants.COLORS.TEXT_HINT);
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 4 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(review.isLiked ? '👍' : '👍🏻');
            Text.fontSize(14);
            Text.onClick(() => {
                this.toggleLike(review);
            });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(review.likeCount.toString());
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_HINT);
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 4 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💬');
            Text.fontSize(14);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(review.replyCount.toString());
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_HINT);
        }, Text);
        Text.pop();
        Row.pop();
        Row.pop();
        // 时间和互动信息
        Row.pop();
        Column.pop();
    }
    /**
     * 评论列表
     */
    private ReviewList(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.getSortedReviews().length === 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create({ space: 16 });
                        Column.width('100%');
                        Column.height(200);
                        Column.justifyContent(FlexAlign.Center);
                        Column.alignItems(HorizontalAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('💬');
                        Text.fontSize(48);
                        Text.fontColor(Constants.COLORS.TEXT_HINT);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无评论记录');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('去应用商店体验应用并发表评论');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                        Text.fontColor(Constants.COLORS.TEXT_HINT);
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.width('100%');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const review = _item;
                            this.ReviewItem.bind(this)(review);
                        };
                        this.forEachUpdateFunction(elmtId, this.getSortedReviews(), forEachItemGenFunction, (review: ReviewModel) => review.id.toString(), false, false);
                    }, ForEach);
                    ForEach.pop();
                    Column.pop();
                });
            }
        }, If);
        If.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor(Constants.COLORS.BACKGROUND);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 标题栏
            Row.create();
            // 标题栏
            Row.width('100%');
            // 标题栏
            Row.height(56);
            // 标题栏
            Row.padding({ left: 16, right: 16 });
            // 标题栏
            Row.backgroundColor(Constants.COLORS.WHITE);
            // 标题栏
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('←');
            Text.fontSize(24);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.onClick(() => {
                this.getUIContext().getRouter().back();
            });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('我的评论');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.fontWeight(FontWeight.Bold);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.width(24);
        }, Text);
        Text.pop();
        // 标题栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.loadingState === LoadingState.LOADING) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, { state: LoadingState.LOADING }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/MyReviewsPage.ets", line: 375, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.LOADING
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.LOADING
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else if (this.loadingState === LoadingState.ERROR) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, {
                                    state: LoadingState.ERROR,
                                    onRetry: (): Promise<void> => this.loadMyReviews()
                                }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/MyReviewsPage.ets", line: 378, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.ERROR,
                                        onRetry: (): Promise<void> => this.loadMyReviews()
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.ERROR
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.layoutWeight(1);
                    }, Column);
                    // 标签栏
                    this.TabBar.bind(this)();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Divider.create();
                        Divider.color(Constants.COLORS.BORDER);
                    }, Divider);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 评论列表
                        Scroll.create();
                        // 评论列表
                        Scroll.scrollable(ScrollDirection.Vertical);
                        // 评论列表
                        Scroll.scrollBar(BarState.Auto);
                        // 评论列表
                        Scroll.layoutWeight(1);
                    }, Scroll);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                    }, Column);
                    this.ReviewList.bind(this)();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 底部间距
                        Column.create();
                        // 底部间距
                        Column.height(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE));
                    }, Column);
                    // 底部间距
                    Column.pop();
                    Column.pop();
                    // 评论列表
                    Scroll.pop();
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "MyReviewsPage";
    }
}
export { MyReviewsPage };
registerNamedRoute(() => new MyReviewsPage(undefined, {}), "", { bundleName: "com.example.nexushub", moduleName: "entry", pagePath: "pages/MyReviewsPage", pageFullPath: "entry/src/main/ets/pages/MyReviewsPage", integratedHsp: "false", moduleType: "followWithHap" });
