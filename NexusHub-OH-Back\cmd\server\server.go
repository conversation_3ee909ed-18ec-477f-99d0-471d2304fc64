package server

import (
	"fmt"
	"os"
	"strings"

	"nexushub-oh-back/config"
	"nexushub-oh-back/internal/api"
	"nexushub-oh-back/internal/models"
	"nexushub-oh-back/internal/services"
	"nexushub-oh-back/internal/workers"
	"nexushub-oh-back/pkg/auth"
	"nexushub-oh-back/pkg/database"
	"nexushub-oh-back/pkg/elasticsearch"
	"nexushub-oh-back/pkg/logger"
	"nexushub-oh-back/pkg/messaging"
	"nexushub-oh-back/pkg/storage"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Run 启动服务器
func Run() error {
	// 判断是否跳过数据库连接
	skipDB := false
	for _, arg := range os.Args {
		if strings.ToLower(arg) == "--skip-db" {
			skipDB = true
			break
		}
	}

	// 初始化配置
	cfg, err := config.LoadConfig()
	if err != nil {
		return fmt.Errorf("加载配置失败: %v", err)
	}

	// 输出配置信息，检查是否正确加载
	fmt.Printf("配置信息:\n")
	fmt.Printf("服务器端口: %d\n", cfg.Server.Port)
	fmt.Printf("数据库地址: %s:%d\n", cfg.Database.Host, cfg.Database.Port)
	fmt.Printf("Redis地址: %s\n", cfg.Redis.Addr)
	fmt.Printf("存储类型: %s\n", cfg.Storage.Type)

	// 初始化日志
	logger.InitLogger(&cfg.Log)
	defer logger.Close()

	var db *database.PostgresDB
	var redis *database.RedisClient
	var storageClient storage.StorageProvider
	var esClient *elasticsearch.ESClient
	var searchService *services.SearchService
	var userSearchService *services.UserSearchService
	var reviewSearchService *services.ReviewSearchService
	var tagSearchService *services.TagSearchService
	var rabbitMQClient *messaging.RabbitMQClient

	// 初始化数据库（如果不跳过）
	if !skipDB {
		db, err = database.NewPostgresDB(&cfg.Database)
		if err != nil {
			return fmt.Errorf("连接数据库失败: %v", err)
		}

		// 初始化Redis
		redis, err = database.NewRedisClient(&cfg.Redis)
		if err != nil {
			logger.Warn("连接Redis失败，将使用内存缓存代替", zap.Error(err))
		}

		// 初始化存储服务
		storageClient, err = storage.NewStorageProvider(&cfg.Storage)
		if err != nil {
			return fmt.Errorf("初始化存储服务失败: %v", err)
		}

		// 自动迁移数据库表结构
		if err := models.AutoMigrate(db.DB); err != nil {
			return fmt.Errorf("数据库迁移失败: %v", err)
		}

		// 初始化基础数据
		if err := models.InitCategories(db.DB); err != nil {
			logger.Error("初始化分类数据失败", zap.Error(err))
		}

		if err := models.InitTags(db.DB); err != nil {
			logger.Error("初始化标签数据失败", zap.Error(err))
		}

		// 初始化Elasticsearch客户端
		esClient, err = elasticsearch.NewESClient(&cfg.ES)
		if err != nil {
			logger.Warn("连接Elasticsearch失败，搜索功能将不可用", zap.Error(err))
		} else {
			logger.Info("Elasticsearch连接成功")
			// 初始化搜索服务
			searchService = services.NewSearchService(db.DB, esClient)
			// 初始化用户搜索服务
			userSearchService = services.NewUserSearchService(db.DB, esClient)
			// 初始化评论搜索服务
			reviewSearchService = services.NewReviewSearchService(db.DB, esClient)
			// 初始化标签搜索服务
			tagSearchService = services.NewTagSearchService(db.DB, esClient)
		}

		// 初始化RabbitMQ客户端
		rabbitMQClient, err = messaging.NewRabbitMQClient(&cfg.RabbitMQ)
		if err != nil {
			logger.Warn("连接RabbitMQ失败，部分功能可能不可用", zap.Error(err))
		} else {
			logger.Info("RabbitMQ连接成功")

			// 启动消息队列工作者
			messageService := services.NewMessageService(rabbitMQClient)
			messageWorker := workers.NewMessageWorker(messageService, db.DB)
			if err := messageWorker.StartAllWorkers(); err != nil {
				logger.Warn("启动消息队列工作者失败", zap.Error(err))
			} else {
				logger.Info("消息队列工作者启动成功")
			}
		}
	} else {
		fmt.Println("已跳过数据库连接，将使用模拟数据")
	}

	// JWT服务
	jwtService := auth.NewJWTService(&cfg.JWT)

	// Logto服务
	logtoService, err := auth.NewLogtoService(&cfg.Logto)
	if err != nil {
		logger.Warn("初始化Logto服务失败，将仅使用传统JWT认证", zap.Error(err))
		logtoService = nil
	} else if logtoService.IsEnabled() {
		logger.Info("Logto服务初始化成功")
		defer logtoService.Close()
	}

	// 设置Gin模式
	if cfg.Server.Mode == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建Gin路由
	router := gin.Default()

	// 配置API路由
	api.SetupRouter(router, db, redis, storageClient, jwtService, logtoService, searchService, userSearchService, reviewSearchService, tagSearchService, rabbitMQClient, cfg)

	// 启动服务器
	serverAddr := fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port)
	fmt.Printf("服务器启动于: %s\n", serverAddr)
	logger.Info("服务器启动", zap.String("地址", serverAddr))

	if err := router.Run(serverAddr); err != nil {
		return fmt.Errorf("启动服务器失败: %v", err)
	}

	return nil
}
