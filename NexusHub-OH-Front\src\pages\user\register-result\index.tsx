import { Link, useSearchParams } from '@umijs/max';
import { Button, Result } from 'antd';
import React from 'react';
import useStyles from './style.style';

const RegisterResult: React.FC<Record<string, unknown>> = () => {
  const { styles } = useStyles();
  const [params] = useSearchParams();

  const actions = (
    <div className={styles.actions}>
      <Link to="/user/login">
        <Button size="large" type="primary">
          <span>立即登录</span>
        </Button>
      </Link>
      <Link to="/">
        <Button size="large">返回首页</Button>
      </Link>
    </div>
  );

  const email = params?.get('account') || '<EMAIL>';
  return (
    <Result
      className={styles.registerResult}
      status="success"
      title={
        <div className={styles.title}>
          <span>账户注册成功！</span>
        </div>
      }
      subTitle={`恭喜您！账户 ${email} 已成功注册。您现在可以使用用户名或邮箱登录系统，开始使用NexusHub的所有功能。`}
      extra={actions}
    />
  );
};
export default RegisterResult;
