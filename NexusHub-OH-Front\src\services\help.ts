/* eslint-disable */
import { request } from '@umijs/max';

// 帮助中心相关接口

/**
 * 获取帮助分类列表
 * @param params - 查询参数
 */
export async function getHelpCategories(params?: {
  page?: number;
  page_size?: number;
  is_active?: boolean;
}) {
  return request('/help/categories', {
    method: 'GET',
    params,
  });
}

/**
 * 获取帮助文章列表
 * @param params - 查询参数
 */
export async function getHelpArticles(params?: {
  page?: number;
  page_size?: number;
  category_id?: number;
  is_published?: boolean;
  is_featured?: boolean;
  keyword?: string;
}) {
  return request('/help/articles', {
    method: 'GET',
    params,
  });
}

/**
 * 获取帮助文章详情
 * @param id - 文章ID
 */
export async function getHelpArticleDetail(id: string) {
  return request(`/help/articles/${id}`, {
    method: 'GET',
  });
}

// 管理员接口

/**
 * 创建帮助分类（管理员）
 * @param data - 分类数据
 */
export async function createHelpCategory(data: {
  name: string;
  description?: string;
  icon?: string;
  sort_order?: number;
  is_active?: boolean;
}) {
  return request('/admin/help/categories', {
    method: 'POST',
    data,
  });
}

/**
 * 更新帮助分类（管理员）
 * @param id - 分类ID
 * @param data - 更新数据
 */
export async function updateHelpCategory(id: string, data: {
  name?: string;
  description?: string;
  icon?: string;
  sort_order?: number;
  is_active?: boolean;
}) {
  return request(`/admin/help/categories/${id}`, {
    method: 'PUT',
    data,
  });
}

/**
 * 删除帮助分类（管理员）
 * @param id - 分类ID
 */
export async function deleteHelpCategory(id: string) {
  return request(`/admin/help/categories/${id}`, {
    method: 'DELETE',
  });
}

/**
 * 创建帮助文章（管理员）
 * @param data - 文章数据
 */
export async function createHelpArticle(data: {
  title: string;
  content: string;
  summary?: string;
  category_id: number;
  tags?: string[];
  sort_order?: number;
  is_published?: boolean;
}) {
  return request('/admin/help/articles', {
    method: 'POST',
    data,
  });
}

/**
 * 更新帮助文章（管理员）
 * @param id - 文章ID
 * @param data - 更新数据
 */
export async function updateHelpArticle(id: string, data: {
  title?: string;
  content?: string;
  summary?: string;
  category_id?: number;
  tags?: string[];
  sort_order?: number;
  is_published?: boolean;
}) {
  return request(`/admin/help/articles/${id}`, {
    method: 'PUT',
    data,
  });
}

/**
 * 删除帮助文章（管理员）
 * @param id - 文章ID
 */
export async function deleteHelpArticle(id: string) {
  return request(`/admin/help/articles/${id}`, {
    method: 'DELETE',
  });
}

/**
 * 获取管理员帮助文章列表（包含草稿）
 * @param params - 查询参数
 */
export async function getAdminHelpArticles(params?: {
  page?: number;
  page_size?: number;
  category_id?: number;
  is_published?: boolean;
  is_featured?: boolean;
  keyword?: string;
}) {
  return request('/admin/help/articles', {
    method: 'GET',
    params,
  });
}

/**
 * 获取管理员帮助分类列表（包含未激活）
 * @param params - 查询参数
 */
export async function getAdminHelpCategories(params?: {
  page?: number;
  page_size?: number;
  is_active?: boolean;
}) {
  return request('/admin/help/categories', {
    method: 'GET',
    params,
  });
}