package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"nexushub-oh-back/internal/models"
	"nexushub-oh-back/pkg/elasticsearch"
	"nexushub-oh-back/pkg/logger"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// TagSearchService 标签搜索服务
type TagSearchService struct {
	DB       *gorm.DB
	ESClient *elasticsearch.ESClient
}

// NewTagSearchService 创建标签搜索服务
func NewTagSearchService(db *gorm.DB, esClient *elasticsearch.ESClient) *TagSearchService {
	return &TagSearchService{
		DB:       db,
		ESClient: esClient,
	}
}

// TagDocument Elasticsearch中的标签文档结构
type TagDocument struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Color       string    `json:"color"`
	IsActive    bool      `json:"is_active"`
	AppCount    int       `json:"app_count"`    // 使用该标签的应用数量
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	Keywords    []string  `json:"keywords"` // 用于搜索的关键词
}

// TagSearchRequest 标签搜索请求
type TagSearchRequest struct {
	Keyword   string `json:"keyword"`
	IsActive  *bool  `json:"is_active"`
	SortBy    string `json:"sort_by"`    // name, app_count, created_at
	SortOrder string `json:"sort_order"` // asc, desc
	Page      int    `json:"page"`
	PageSize  int    `json:"page_size"`
}

// TagSearchResponse 标签搜索响应
type TagSearchResponse struct {
	Tags       []TagDocument `json:"tags"`
	Total      int64         `json:"total"`
	Page       int           `json:"page"`
	PageSize   int           `json:"page_size"`
	TotalPages int           `json:"total_pages"`
}

// TagStatsDocument 标签统计文档
type TagStatsDocument struct {
	TagID     uint   `json:"tag_id"`
	TagName   string `json:"tag_name"`
	AppCount  int    `json:"app_count"`
	TotalApps int    `json:"total_apps"`
	UsageRate float64 `json:"usage_rate"` // 使用率
}

const (
	TagIndexName = "nexushub_tags"
)

// InitializeTagIndex 初始化标签Elasticsearch索引
func (s *TagSearchService) InitializeTagIndex(ctx context.Context) error {
	// 定义标签索引的映射
	mapping := `{
		"mappings": {
			"properties": {
				"id": {"type": "long"},
				"name": {
					"type": "text",
					"analyzer": "standard",
					"fields": {
						"keyword": {"type": "keyword"},
						"suggest": {
							"type": "completion",
							"analyzer": "simple"
						}
					}
				},
				"description": {
					"type": "text",
					"analyzer": "standard"
				},
				"color": {"type": "keyword"},
				"is_active": {"type": "boolean"},
				"app_count": {"type": "integer"},
				"created_at": {"type": "date"},
				"updated_at": {"type": "date"},
				"keywords": {
					"type": "text",
					"analyzer": "standard"
				}
			}
		}
	}`

	// 检查索引是否存在
	exists, err := s.ESClient.IndexExists(ctx, TagIndexName)
	if err != nil {
		return fmt.Errorf("检查标签索引是否存在失败: %w", err)
	}

	if !exists {
		if err := s.ESClient.CreateIndex(ctx, TagIndexName, mapping); err != nil {
			return fmt.Errorf("创建标签索引失败: %w", err)
		}
		logger.Info("标签索引创建成功", zap.String("index", TagIndexName))
	} else {
		logger.Info("标签索引已存在", zap.String("index", TagIndexName))
	}

	return nil
}

// IndexTag 索引标签到Elasticsearch
func (s *TagSearchService) IndexTag(ctx context.Context, tag *models.Tag) error {
	// 计算使用该标签的应用数量
	var appCount int64
	if err := s.DB.Model(&models.AppTag{}).Where("tag_id = ?", tag.ID).Count(&appCount).Error; err != nil {
		logger.Warn("计算标签应用数量失败", zap.Uint("tag_id", tag.ID), zap.Error(err))
		appCount = 0
	}

	// 构建关键词
	keywords := []string{
		tag.Name,
		tag.Description,
	}

	// 过滤空关键词
	var filteredKeywords []string
	for _, keyword := range keywords {
		if strings.TrimSpace(keyword) != "" {
			filteredKeywords = append(filteredKeywords, keyword)
		}
	}

	// 构建标签文档
	tagDoc := TagDocument{
		ID:          tag.ID,
		Name:        tag.Name,
		Description: tag.Description,
		Color:       tag.Color,
		IsActive:    tag.IsActive,
		AppCount:    int(appCount),
		CreatedAt:   tag.CreatedAt,
		UpdatedAt:   tag.UpdatedAt,
		Keywords:    filteredKeywords,
	}

	// 序列化文档
	docJSON, err := json.Marshal(tagDoc)
	if err != nil {
		return fmt.Errorf("序列化标签文档失败: %w", err)
	}

	// 索引文档
	documentID := strconv.FormatUint(uint64(tag.ID), 10)
	if err := s.ESClient.IndexDocument(ctx, TagIndexName, documentID, string(docJSON)); err != nil {
		return fmt.Errorf("索引标签文档失败: %w", err)
	}

	logger.Info("标签索引成功", zap.Uint("tag_id", tag.ID), zap.String("name", tag.Name))
	return nil
}

// SearchTags 搜索标签
func (s *TagSearchService) SearchTags(ctx context.Context, req *TagSearchRequest) (*TagSearchResponse, error) {
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.SortBy == "" {
		req.SortBy = "app_count"
	}
	if req.SortOrder == "" {
		req.SortOrder = "desc"
	}

	// 构建查询
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": []interface{}{},
				"filter": []interface{}{},
			},
		},
		"sort": []interface{}{
			map[string]interface{}{
				req.SortBy: map[string]interface{}{
					"order": req.SortOrder,
				},
			},
		},
		"from": (req.Page - 1) * req.PageSize,
		"size": req.PageSize,
	}

	mustQueries := query["query"].(map[string]interface{})["bool"].(map[string]interface{})["must"].([]interface{})
	filterQueries := query["query"].(map[string]interface{})["bool"].(map[string]interface{})["filter"].([]interface{})

	// 关键词搜索
	if req.Keyword != "" {
		mustQueries = append(mustQueries, map[string]interface{}{
			"multi_match": map[string]interface{}{
				"query": req.Keyword,
				"fields": []string{
					"name^3",
					"description^2",
					"keywords",
				},
				"type": "best_fields",
				"fuzziness": "AUTO",
			},
		})
	}

	// 活跃状态过滤
	if req.IsActive != nil {
		filterQueries = append(filterQueries, map[string]interface{}{
			"term": map[string]interface{}{
				"is_active": *req.IsActive,
			},
		})
	}

	// 更新查询
	query["query"].(map[string]interface{})["bool"].(map[string]interface{})["must"] = mustQueries
	query["query"].(map[string]interface{})["bool"].(map[string]interface{})["filter"] = filterQueries

	// 序列化查询
	queryJSON, err := json.Marshal(query)
	if err != nil {
		return nil, fmt.Errorf("序列化查询失败: %w", err)
	}

	// 执行搜索
	result, err := s.ESClient.Search(ctx, TagIndexName, string(queryJSON))
	if err != nil {
		return nil, fmt.Errorf("搜索标签失败: %w", err)
	}

	// 解析结果
	hits := result["hits"].(map[string]interface{})
	total := int64(hits["total"].(map[string]interface{})["value"].(float64))
	hitsList := hits["hits"].([]interface{})

	var tags []TagDocument
	for _, hit := range hitsList {
		hitMap := hit.(map[string]interface{})
		source := hitMap["_source"].(map[string]interface{})

		// 解析标签文档
		sourceJSON, err := json.Marshal(source)
		if err != nil {
			continue
		}

		var tag TagDocument
		if err := json.Unmarshal(sourceJSON, &tag); err != nil {
			continue
		}

		tags = append(tags, tag)
	}

	// 计算总页数
	totalPages := int(total) / req.PageSize
	if int(total)%req.PageSize > 0 {
		totalPages++
	}

	return &TagSearchResponse{
		Tags:       tags,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// SuggestTags 标签自动补全建议
func (s *TagSearchService) SuggestTags(ctx context.Context, prefix string, limit int) ([]string, error) {
	if limit <= 0 {
		limit = 10
	}

	// 构建建议查询
	query := map[string]interface{}{
		"suggest": map[string]interface{}{
			"tag_suggest": map[string]interface{}{
				"prefix": prefix,
				"completion": map[string]interface{}{
					"field": "name.suggest",
					"size":  limit,
				},
			},
		},
		"_source": false,
	}

	// 序列化查询
	queryJSON, err := json.Marshal(query)
	if err != nil {
		return nil, fmt.Errorf("序列化建议查询失败: %w", err)
	}

	// 执行搜索
	result, err := s.ESClient.Search(ctx, TagIndexName, string(queryJSON))
	if err != nil {
		return nil, fmt.Errorf("标签建议搜索失败: %w", err)
	}

	// 解析建议结果
	var suggestions []string
	if suggest, ok := result["suggest"]; ok {
		if tagSuggest, ok := suggest.(map[string]interface{})["tag_suggest"]; ok {
			if suggestArray, ok := tagSuggest.([]interface{}); ok && len(suggestArray) > 0 {
				if options, ok := suggestArray[0].(map[string]interface{})["options"]; ok {
					for _, option := range options.([]interface{}) {
						if text, ok := option.(map[string]interface{})["text"]; ok {
							suggestions = append(suggestions, text.(string))
						}
					}
				}
			}
		}
	}

	return suggestions, nil
}

// GetTagStats 获取标签统计信息
func (s *TagSearchService) GetTagStats(ctx context.Context) ([]TagStatsDocument, error) {
	// 构建聚合查询
	query := map[string]interface{}{
		"size": 0,
		"aggs": map[string]interface{}{
			"tag_stats": map[string]interface{}{
				"terms": map[string]interface{}{
					"field": "id",
					"size":  1000,
				},
				"aggs": map[string]interface{}{
					"tag_info": map[string]interface{}{
						"top_hits": map[string]interface{}{
							"_source": []string{"name", "app_count"},
							"size":     1,
						},
					},
				},
			},
		},
	}

	// 序列化查询
	queryJSON, err := json.Marshal(query)
	if err != nil {
		return nil, fmt.Errorf("序列化统计查询失败: %w", err)
	}

	// 执行搜索
	result, err := s.ESClient.Search(ctx, TagIndexName, string(queryJSON))
	if err != nil {
		return nil, fmt.Errorf("标签统计搜索失败: %w", err)
	}

	// 计算总应用数
	var totalApps int64
	if err := s.DB.Model(&models.Application{}).Count(&totalApps).Error; err != nil {
		logger.Warn("获取总应用数失败", zap.Error(err))
		totalApps = 1 // 避免除零错误
	}

	// 解析聚合结果
	var stats []TagStatsDocument
	if aggs, ok := result["aggregations"]; ok {
		if tagStats, ok := aggs.(map[string]interface{})["tag_stats"]; ok {
			if buckets, ok := tagStats.(map[string]interface{})["buckets"]; ok {
				for _, bucket := range buckets.([]interface{}) {
					bucketMap := bucket.(map[string]interface{})
					tagID := uint(bucketMap["key"].(float64))

					if tagInfo, ok := bucketMap["tag_info"]; ok {
						if hits, ok := tagInfo.(map[string]interface{})["hits"]; ok {
							if hitsList, ok := hits.(map[string]interface{})["hits"]; ok {
								if len(hitsList.([]interface{})) > 0 {
									hit := hitsList.([]interface{})[0].(map[string]interface{})
									source := hit["_source"].(map[string]interface{})

									tagName := source["name"].(string)
									appCount := int(source["app_count"].(float64))
									usageRate := float64(appCount) / float64(totalApps) * 100

									stats = append(stats, TagStatsDocument{
										TagID:     tagID,
										TagName:   tagName,
										AppCount:  appCount,
										TotalApps: int(totalApps),
										UsageRate: usageRate,
									})
								}
							}
						}
					}
				}
			}
		}
	}

	return stats, nil
}

// DeleteTag 从Elasticsearch删除标签
func (s *TagSearchService) DeleteTag(ctx context.Context, tagID uint) error {
	documentID := strconv.FormatUint(uint64(tagID), 10)
	if err := s.ESClient.DeleteDocument(ctx, TagIndexName, documentID); err != nil {
		return fmt.Errorf("删除标签文档失败: %w", err)
	}

	logger.Info("标签文档删除成功", zap.Uint("tag_id", tagID))
	return nil
}

// SyncAllTags 同步所有标签到Elasticsearch
func (s *TagSearchService) SyncAllTags(ctx context.Context) error {
	var tags []models.Tag
	if err := s.DB.Find(&tags).Error; err != nil {
		return fmt.Errorf("获取标签列表失败: %w", err)
	}

	for _, tag := range tags {
		if err := s.IndexTag(ctx, &tag); err != nil {
			logger.Error("同步标签失败", zap.Uint("tag_id", tag.ID), zap.Error(err))
			continue
		}
	}

	logger.Info("标签同步完成", zap.Int("total", len(tags)))
	return nil
}