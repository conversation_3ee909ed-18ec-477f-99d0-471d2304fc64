-- 创建地理位置数据表
CREATE TABLE IF NOT EXISTS geographic_data (
    id INTEGER PRIMARY KEY,
    parent_id INTEGER NOT NULL DEFAULT 0,
    deep INTEGER NOT NULL, -- 0-省份，1-城市，2-镇/区，3-街道
    name VARCHAR(100) NOT NULL,
    short VARCHAR(10) NOT NULL,
    pinyin VARCHAR(200) NOT NULL,
    code VARCHAR(20) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_geographic_data_deep ON geographic_data(deep);
CREATE INDEX IF NOT EXISTS idx_geographic_data_parent_id ON geographic_data(parent_id);
CREATE INDEX IF NOT EXISTS idx_geographic_data_deep_parent ON geographic_data(deep, parent_id);
CREATE INDEX IF NOT EXISTS idx_geographic_data_name ON geographic_data(name);

-- 插入一些测试数据
INSERT INTO geographic_data (id, parent_id, deep, name, short, pinyin, code, full_name) VALUES
-- 省份级别 (deep = 0)
(11, 0, 0, '北京', 'b', 'bei jing', '110000000000', '北京市'),
(12, 0, 0, '天津', 't', 'tian jin', '120000000000', '天津市'),
(13, 0, 0, '河北', 'h', 'he bei', '130000000000', '河北省'),
(14, 0, 0, '山西', 's', 'shan xi', '140000000000', '山西省'),
(15, 0, 0, '内蒙古', 'n', 'nei meng gu', '150000000000', '内蒙古自治区'),

-- 城市级别 (deep = 1) - 北京市下的区
(1101, 11, 1, '北京', 'b', 'bei jing', '110100000000', '北京市'),

-- 区/镇级别 (deep = 2) - 北京市下的区
(110101, 1101, 2, '东城', 'd', 'dong cheng', '110101000000', '东城区'),
(110102, 1101, 2, '西城', 'x', 'xi cheng', '110102000000', '西城区'),
(110105, 1101, 2, '朝阳', 'c', 'chao yang', '110105000000', '朝阳区'),
(110106, 1101, 2, '丰台', 'f', 'feng tai', '110106000000', '丰台区'),

-- 街道级别 (deep = 3) - 东城区下的街道
(110101001, 110101, 3, '东华门', 'd', 'dong hua men', '110101001000', '东华门街道'),
(110101002, 110101, 3, '景山', 'j', 'jing shan', '110101002000', '景山街道'),
(110101003, 110101, 3, '交道口', 'j', 'jiao dao kou', '110101003000', '交道口街道'),
(110101004, 110101, 3, '安定门', 'a', 'an ding men', '110101004000', '安定门街道')

ON CONFLICT (id) DO NOTHING;

-- 验证数据插入
SELECT 
    deep,
    CASE 
        WHEN deep = 0 THEN '省份'
        WHEN deep = 1 THEN '城市'
        WHEN deep = 2 THEN '区/镇'
        WHEN deep = 3 THEN '街道'
    END as level_name,
    COUNT(*) as count
FROM geographic_data 
GROUP BY deep 
ORDER BY deep;