if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface MyAppsPage_Params {
    downloadedApps?: AppModel[];
    loadingState?: LoadingState;
    selectedTab?: number;
    deviceUtils?;
    apiService?;
}
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { DeviceUtils } from "@normalized:N&&&entry/src/main/ets/utils/DeviceUtils&";
import { ApiService } from "@normalized:N&&&entry/src/main/ets/services/ApiService&";
import { LoadingView, LoadingState } from "@normalized:N&&&entry/src/main/ets/components/LoadingView&";
import type { AppModel } from '../models/App';
import hilog from "@ohos:hilog";
class MyAppsPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__downloadedApps = new ObservedPropertyObjectPU([], this, "downloadedApps");
        this.__loadingState = new ObservedPropertySimplePU(LoadingState.LOADING, this, "loadingState");
        this.__selectedTab = new ObservedPropertySimplePU(0, this, "selectedTab");
        this.deviceUtils = DeviceUtils.getInstance();
        this.apiService = ApiService.getInstance();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: MyAppsPage_Params) {
        if (params.downloadedApps !== undefined) {
            this.downloadedApps = params.downloadedApps;
        }
        if (params.loadingState !== undefined) {
            this.loadingState = params.loadingState;
        }
        if (params.selectedTab !== undefined) {
            this.selectedTab = params.selectedTab;
        }
        if (params.deviceUtils !== undefined) {
            this.deviceUtils = params.deviceUtils;
        }
        if (params.apiService !== undefined) {
            this.apiService = params.apiService;
        }
    }
    updateStateVars(params: MyAppsPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__downloadedApps.purgeDependencyOnElmtId(rmElmtId);
        this.__loadingState.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedTab.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__downloadedApps.aboutToBeDeleted();
        this.__loadingState.aboutToBeDeleted();
        this.__selectedTab.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __downloadedApps: ObservedPropertyObjectPU<AppModel[]>;
    get downloadedApps() {
        return this.__downloadedApps.get();
    }
    set downloadedApps(newValue: AppModel[]) {
        this.__downloadedApps.set(newValue);
    }
    private __loadingState: ObservedPropertySimplePU<LoadingState>;
    get loadingState() {
        return this.__loadingState.get();
    }
    set loadingState(newValue: LoadingState) {
        this.__loadingState.set(newValue);
    }
    private __selectedTab: ObservedPropertySimplePU<number>; // 0: 全部, 1: 已安装, 2: 未安装
    get selectedTab() {
        return this.__selectedTab.get();
    }
    set selectedTab(newValue: number) {
        this.__selectedTab.set(newValue);
    }
    private deviceUtils;
    private apiService;
    aboutToAppear() {
        this.loadDownloadedApps();
    }
    /**
     * 加载已下载的应用
     */
    private async loadDownloadedApps() {
        try {
            this.loadingState = LoadingState.LOADING;
            // 这里应该调用API获取用户下载的应用
            // 暂时使用模拟数据
            await this.simulateApiCall();
            this.downloadedApps = this.getMockDownloadedApps();
            this.loadingState = LoadingState.SUCCESS;
        }
        catch (error) {
            hilog.error(0x0000, 'MyAppsPage', '加载下载应用失败: %{public}s', JSON.stringify(error));
            this.loadingState = LoadingState.ERROR;
        }
    }
    /**
     * 模拟API调用
     */
    private async simulateApiCall(): Promise<void> {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve();
            }, 1000);
        });
    }
    /**
     * 获取模拟下载应用数据
     */
    private getMockDownloadedApps(): AppModel[] {
        return [
            {
                id: 1,
                created_at: '2024-01-15T00:00:00Z',
                updated_at: '2024-01-15T00:00:00Z',
                name: '微信',
                package_name: 'com.tencent.mm',
                description: '一个为智能终端提供即时通讯服务的免费应用程序',
                short_description: '即时通讯应用',
                icon: Constants.PLACEHOLDER_IMAGE,
                category_id: 1,
                category_name: '社交',
                developer_id: 1,
                developer_name: '腾讯科技',
                version: '8.0.32',
                version_code: 80032,
                min_sdk_version: 9,
                target_sdk_version: 12,
                size: 245760000,
                download_url: '',
                download_count: 1000000000,
                rating: 4.8,
                review_count: 50000,
                screenshots: [Constants.PLACEHOLDER_IMAGE],
                permissions: [],
                tags: ['社交', '通讯'],
                changelog: '修复已知问题',
                privacy_policy: '',
                support_email: '<EMAIL>',
                website: 'https://weixin.qq.com',
                status: 'installed',
                is_featured: false,
                is_editor_choice: false,
                is_top: false,
                published_at: '2024-01-15T00:00:00Z',
                review_status: 'approved',
                review_reason: '',
                reviewed_at: '2024-01-15T00:00:00Z',
                reviewer_id: 1
            },
            {
                id: 2,
                created_at: '2024-01-10T00:00:00Z',
                updated_at: '2024-01-10T00:00:00Z',
                name: '支付宝',
                package_name: 'com.eg.android.AlipayGphone',
                description: '数字生活开放平台',
                short_description: '移动支付应用',
                icon: Constants.PLACEHOLDER_IMAGE,
                category_id: 2,
                category_name: '金融',
                developer_id: 2,
                developer_name: '蚂蚁集团',
                version: '10.3.20',
                version_code: 103200,
                min_sdk_version: 9,
                target_sdk_version: 12,
                size: 156672000,
                download_url: '',
                download_count: 800000000,
                rating: 4.7,
                review_count: 30000,
                screenshots: [Constants.PLACEHOLDER_IMAGE],
                permissions: [],
                tags: ['支付', '金融'],
                changelog: '优化用户体验',
                privacy_policy: '',
                support_email: '<EMAIL>',
                website: 'https://www.alipay.com',
                status: 'published',
                is_featured: false,
                is_editor_choice: false,
                is_top: false,
                published_at: '2024-01-10T00:00:00Z',
                review_status: 'approved',
                review_reason: '',
                reviewed_at: '2024-01-10T00:00:00Z',
                reviewer_id: 1
            }
        ];
    }
    /**
     * 获取过滤后的应用列表
     */
    private getFilteredApps(): AppModel[] {
        switch (this.selectedTab) {
            case 1: // 已安装
                return this.downloadedApps.filter(app => app.status === 'installed');
            case 2: // 未安装
                return this.downloadedApps.filter(app => app.status !== 'installed');
            default: // 全部
                return this.downloadedApps;
        }
    }
    /**
     * 跳转到应用详情页面
     */
    private navigateToAppDetail(app: AppModel) {
        this.getUIContext().getRouter().pushUrl({
            url: 'pages/AppDetailPage',
            params: { appId: app.id }
        });
    }
    /**
     * 标签栏
     */
    private TabBar(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.width('100%');
            Row.backgroundColor(Constants.COLORS.WHITE);
            Row.justifyContent(FlexAlign.SpaceAround);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index?: number) => {
                const title = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(title);
                    Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                    Text.fontColor(this.selectedTab === (index ?? 0) ? Constants.COLORS.PRIMARY : Constants.COLORS.TEXT_SECONDARY);
                    Text.fontWeight(this.selectedTab === (index ?? 0) ? FontWeight.Bold : FontWeight.Normal);
                    Text.padding({ left: 16, right: 16, top: 12, bottom: 12 });
                    Text.onClick(() => {
                        this.selectedTab = index ?? 0;
                    });
                }, Text);
                Text.pop();
            };
            this.forEachUpdateFunction(elmtId, ['全部', '已安装', '未安装'], forEachItemGenFunction, (title: string, index?: number) => `tab_${index}_${title}`, true, true);
        }, ForEach);
        ForEach.pop();
        Row.pop();
    }
    /**
     * 应用项
     */
    private AppItem(app: AppModel, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) });
            Row.width('100%');
            Row.padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM));
            Row.backgroundColor(Constants.COLORS.WHITE);
            Row.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            Row.margin({ left: 16, right: 16, bottom: 8 });
            Row.onClick(() => this.navigateToAppDetail(app));
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 应用图标
            Image.create(app.icon);
            // 应用图标
            Image.width(this.deviceUtils.isTablet() ? 64 : 56);
            // 应用图标
            Image.height(this.deviceUtils.isTablet() ? 64 : 56);
            // 应用图标
            Image.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            // 应用图标
            Image.objectFit(ImageFit.Cover);
            // 应用图标
            Image.backgroundColor(Constants.COLORS.BACKGROUND_LIGHT);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 应用信息
            Column.create({ space: 4 });
            // 应用信息
            Column.alignItems(HorizontalAlign.Start);
            // 应用信息
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(app.name);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.fontWeight(FontWeight.Medium);
            Text.maxLines(1);
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(app.isInstalled ? '已安装' : '未安装');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(app.isInstalled ? Constants.COLORS.SUCCESS : Constants.COLORS.TEXT_HINT);
            Text.backgroundColor(app.isInstalled ? { "id": 16777232, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" } : { "id": 16777231, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Text.padding({ left: 8, right: 8, top: 2, bottom: 2 });
            Text.borderRadius(Constants.BORDER_RADIUS.SMALL);
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(app.description);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
            Text.maxLines(1);
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`版本 ${app.version}`);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_HINT);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`${((app.size || 0) / 1024 / 1024).toFixed(1)}MB`);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_HINT);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (app.downloadTime) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`下载于 ${app.downloadTime.split(' ')[0]}`);
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                        Text.fontColor(Constants.COLORS.TEXT_HINT);
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Row.pop();
        // 应用信息
        Column.pop();
        Row.pop();
    }
    /**
     * 应用列表
     */
    private AppList(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.getFilteredApps().length === 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create({ space: 16 });
                        Column.width('100%');
                        Column.height(200);
                        Column.justifyContent(FlexAlign.Center);
                        Column.alignItems(HorizontalAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('📱');
                        Text.fontSize(48);
                        Text.fontColor(Constants.COLORS.TEXT_HINT);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无下载记录');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('去应用商店发现更多精彩应用');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                        Text.fontColor(Constants.COLORS.TEXT_HINT);
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.width('100%');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const app = _item;
                            this.AppItem.bind(this)(app);
                        };
                        this.forEachUpdateFunction(elmtId, this.getFilteredApps(), forEachItemGenFunction, (app: AppModel) => `app_${app.id}`, false, false);
                    }, ForEach);
                    ForEach.pop();
                    Column.pop();
                });
            }
        }, If);
        If.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor(Constants.COLORS.BACKGROUND);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 标题栏
            Row.create();
            // 标题栏
            Row.width('100%');
            // 标题栏
            Row.height(56);
            // 标题栏
            Row.padding({ left: 16, right: 16 });
            // 标题栏
            Row.backgroundColor(Constants.COLORS.WHITE);
            // 标题栏
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('←');
            Text.fontSize(24);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.onClick(() => {
                this.getUIContext().getRouter().back();
            });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('我的下载');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.fontWeight(FontWeight.Bold);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.width(24);
        }, Text);
        Text.pop();
        // 标题栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.loadingState === LoadingState.LOADING) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, { state: LoadingState.LOADING }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/MyAppsPage.ets", line: 314, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.LOADING
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.LOADING
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else if (this.loadingState === LoadingState.ERROR) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, {
                                    state: LoadingState.ERROR,
                                    onRetry: (): Promise<void> => this.loadDownloadedApps()
                                }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/MyAppsPage.ets", line: 317, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.ERROR,
                                        onRetry: (): Promise<void> => this.loadDownloadedApps()
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.ERROR
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.layoutWeight(1);
                    }, Column);
                    // 标签栏
                    this.TabBar.bind(this)();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Divider.create();
                        Divider.color(Constants.COLORS.BORDER);
                    }, Divider);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 应用列表
                        Scroll.create();
                        // 应用列表
                        Scroll.scrollable(ScrollDirection.Vertical);
                        // 应用列表
                        Scroll.scrollBar(BarState.Auto);
                        // 应用列表
                        Scroll.layoutWeight(1);
                    }, Scroll);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                    }, Column);
                    this.AppList.bind(this)();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 底部间距
                        Column.create();
                        // 底部间距
                        Column.height(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE));
                    }, Column);
                    // 底部间距
                    Column.pop();
                    Column.pop();
                    // 应用列表
                    Scroll.pop();
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "MyAppsPage";
    }
}
export { MyAppsPage };
registerNamedRoute(() => new MyAppsPage(undefined, {}), "", { bundleName: "com.example.nexushub", moduleName: "entry", pagePath: "pages/MyAppsPage", pageFullPath: "entry/src/main/ets/pages/MyAppsPage", integratedHsp: "false", moduleType: "followWithHap" });
