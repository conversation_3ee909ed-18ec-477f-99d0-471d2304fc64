import React from 'react';
import { LogtoProvider } from '../LogtoProvider';

interface AppWrapperProps {
  children: React.ReactNode;
  useLogto?: boolean;
}

/**
 * 应用包装器组件
 * 根据配置决定是否启用Logto认证
 */
const AppWrapper: React.FC<AppWrapperProps> = ({ children, useLogto }) => {
  // 如果启用了Logto认证，使用LogtoProvider包装
  if (useLogto) {
    return (
      <LogtoProvider>
        {children}
      </LogtoProvider>
    );
  }
  
  // 否则直接返回子组件
  return <>{children}</>;
};

export { AppWrapper };
export default AppWrapper;