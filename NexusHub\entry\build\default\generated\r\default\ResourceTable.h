/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef RESOURCE_TABLE_H
#define RESOURCE_TABLE_H

#include<stdint.h>

namespace OHOS {
const int32_t STRING_ENTRYABILITY_DESC = 0x01000005;
const int32_t STRING_ENTRYABILITY_LABEL = 0x01000006;
const int32_t STRING_APP_NAME = 0x01000000;
const int32_t STRING_MODULE_DESC = 0x01000007;
const int32_t STRING_PERMISSION_INTERNET_REASON = 0x01000008;
const int32_t COLOR_BACKGROUND_PRIMARY = 0x01000009;
const int32_t COLOR_BACKGROUND_SECONDARY = 0x0100000a;
const int32_t COLOR_CARD_BACKGROUND = 0x0100000b;
const int32_t COLOR_DIVIDER = 0x0100000c;
const int32_t COLOR_GRADIENT_OVERLAY = 0x0100000d;
const int32_t COLOR_OVERLAY_DARK = 0x0100000e;
const int32_t COLOR_OVERLAY_LIGHT = 0x0100000f;
const int32_t COLOR_OVERLAY_MEDIUM = 0x01000010;
const int32_t COLOR_STAR_ACTIVE = 0x01000011;
const int32_t COLOR_STAR_INACTIVE = 0x01000012;
const int32_t COLOR_START_WINDOW_BACKGROUND = 0x01000013;
const int32_t COLOR_STATUS_ERROR = 0x01000014;
const int32_t COLOR_STATUS_SUCCESS = 0x01000015;
const int32_t COLOR_STATUS_WARNING = 0x01000016;
const int32_t COLOR_TEXT_HINT = 0x01000017;
const int32_t COLOR_TEXT_PRIMARY = 0x01000018;
const int32_t COLOR_TEXT_SECONDARY = 0x01000019;
const int32_t FLOAT_PAGE_TEXT_FONT_SIZE = 0x0100001a;
const int32_t MEDIA_APP_ICON = 0x0100001b;
const int32_t MEDIA_BACKGROUND = 0x01000001;
const int32_t MEDIA_FOREGROUND = 0x01000002;
const int32_t MEDIA_IC_ABOUT = 0x0100001c;
const int32_t MEDIA_IC_ARROW_RIGHT = 0x0100001d;
const int32_t MEDIA_IC_BACK = 0x0100001e;
const int32_t MEDIA_IC_CATEGORY = 0x0100001f;
const int32_t MEDIA_IC_CHECK = 0x01000020;
const int32_t MEDIA_IC_CLOSE = 0x01000021;
const int32_t MEDIA_IC_DARK_MODE = 0x01000022;
const int32_t MEDIA_IC_DELETE = 0x01000023;
const int32_t MEDIA_IC_DOWNLOAD = 0x01000024;
const int32_t MEDIA_IC_EMPTY = 0x01000025;
const int32_t MEDIA_IC_ERROR = 0x01000026;
const int32_t MEDIA_IC_FEATURED = 0x01000027;
const int32_t MEDIA_IC_FEEDBACK = 0x01000028;
const int32_t MEDIA_IC_FILTER = 0x01000029;
const int32_t MEDIA_IC_FOLDER = 0x0100002a;
const int32_t MEDIA_IC_HISTORY = 0x0100002b;
const int32_t MEDIA_IC_HOME = 0x0100002c;
const int32_t MEDIA_IC_INFO = 0x0100002d;
const int32_t MEDIA_IC_LANGUAGE = 0x0100002e;
const int32_t MEDIA_IC_LOCATION = 0x0100002f;
const int32_t MEDIA_IC_MENU = 0x01000030;
const int32_t MEDIA_IC_MORE = 0x01000031;
const int32_t MEDIA_IC_NOTIFICATION = 0x01000032;
const int32_t MEDIA_IC_NOTIFICATION_EMPTY = 0x01000033;
const int32_t MEDIA_IC_PROFILE = 0x01000034;
const int32_t MEDIA_IC_REFRESH = 0x01000035;
const int32_t MEDIA_IC_SEARCH = 0x01000036;
const int32_t MEDIA_IC_SETTINGS = 0x01000037;
const int32_t MEDIA_IC_SHARE = 0x01000038;
const int32_t MEDIA_IC_SORT = 0x01000039;
const int32_t MEDIA_IC_STAR = 0x0100003a;
const int32_t MEDIA_IC_STORAGE = 0x0100003b;
const int32_t MEDIA_IC_UPDATE = 0x0100003c;
const int32_t MEDIA_IC_WIFI = 0x0100003d;
const int32_t MEDIA_ICONS = 0x0100003e;
const int32_t MEDIA_LAYERED_IMAGE = 0x01000003;
const int32_t MEDIA_STARTICON = 0x0100003f;
const int32_t PROFILE_BACKUP_CONFIG = 0x01000040;
const int32_t PROFILE_MAIN_PAGES = 0x01000041;
const int32_t PROFILE_NETWORK_SECURITY_CONFIG = 0x01000004;
}
#endif