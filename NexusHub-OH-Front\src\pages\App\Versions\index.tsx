import { <PERSON>Container } from '@ant-design/pro-components';
import { Card, Table, Button, Space, Tag, message, Modal, Popconfirm } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, ReloadOutlined, UploadOutlined } from '@ant-design/icons';
import React, { useState, useEffect } from 'react';
import { useRequest, history, useParams } from '@umijs/max';
import type { ColumnsType } from 'antd/es/table';
import { getAppVersions, deleteAppVersion } from '@/services/version';
import type { AppVersion } from '@/services/version';

const DeveloperVersions: React.FC = () => {
  const params = useParams<{ id: string }>();
  const appId = params.id;
  const [searchParams, setSearchParams] = useState<any>({});

  // 获取应用版本列表
  const { data, loading, run } = useRequest(
    () => getAppVersions(Number(appId), {
      page: searchParams.page || 1,
      pageSize: searchParams.pageSize || 20,
    }),
    {
      refreshDeps: [searchParams, appId],
    }
  );

  // 删除版本
  const handleDelete = async (versionId: number) => {
    try {
      await deleteAppVersion(versionId);
      message.success('版本删除成功');
      run(); // 刷新列表
    } catch (error) {
      console.error('删除版本失败:', error);
      message.error('删除版本失败，请稍后重试');
    }
  };

  // 查看版本详情
  const handleViewDetail = (version: AppVersion) => {
    Modal.info({
      title: '版本详情',
      width: 600,
      content: (
        <div>
          <p><strong>版本号:</strong> {version.versionName}</p>
          <p><strong>版本代码:</strong> {version.versionCode}</p>
          <p><strong>文件大小:</strong> {version.fileSize ? `${(version.fileSize / 1024 / 1024).toFixed(2)} MB` : '未知'}</p>
          <p><strong>更新说明:</strong></p>
          <div style={{ background: '#f5f5f5', padding: '8px', borderRadius: '4px', maxHeight: '200px', overflow: 'auto' }}>
            {version.updateDescription || '无更新说明'}
          </div>
          <p><strong>创建时间:</strong> {(version.createdAt || version.created_at) ? new Date(version.createdAt || version.created_at).toLocaleString() : '-'}</p>
          <p><strong>更新时间:</strong> {(version.updatedAt || version.updated_at) ? new Date(version.updatedAt || version.updated_at).toLocaleString() : '-'}</p>
        </div>
      ),
    });
  };

  const columns: ColumnsType<AppVersion> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '版本号',
      dataIndex: 'versionName',
      key: 'versionName',
      width: 120,
    },
    {
      title: '版本代码',
      dataIndex: 'versionCode',
      key: 'versionCode',
      width: 100,
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
      width: 120,
      render: (size: number) => size ? `${(size / 1024 / 1024).toFixed(2)} MB` : '未知',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        let color = 'orange';
        let text = '待审核';
        
        if (status === 'approved') {
          color = 'green';
          text = '已通过';
        } else if (status === 'rejected') {
          color = 'red';
          text = '已拒绝';
        } else if (status === 'published') {
          color = 'blue';
          text = '已发布';
        } else if (status === 'draft') {
          color = 'gray';
          text = '草稿';
        }
        
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (time: string, record: AppVersion) => {
        const timeValue = time || record.created_at;
        return timeValue ? new Date(timeValue).toLocaleString() : '-';
      },
      sorter: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 150,
      render: (time: string, record: AppVersion) => {
        const timeValue = time || record.updated_at;
        return timeValue ? new Date(timeValue).toLocaleString() : '-';
      },
      sorter: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 250,
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="link" 
            icon={<EyeOutlined />} 
            size="small"
            onClick={() => handleViewDetail(record)}
          >
            查看
          </Button>
          {(record.status === 'draft' || record.status === 'rejected') && (
            <Button 
              type="link" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => history.push(`/app/list/versions/${appId}/edit/${record.id}`)}
            >
              编辑
            </Button>
          )}
          {record.status === 'draft' && (
            <Popconfirm
              title="确定要删除这个版本吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button 
                type="link" 
                danger
                icon={<DeleteOutlined />} 
                size="small"
              >
                删除
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  return (
    <PageContainer>
      <Card bordered={false}>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <span>版本管理</span>
            <Tag color="blue" style={{ marginLeft: 8 }}>应用ID: {appId}</Tag>
            <Tag color="green" style={{ marginLeft: 8 }}>总版本数: {data?.versions?.length || 0}</Tag>
          </div>
          <Space>
            <Button 
              type="primary"
              icon={<PlusOutlined />} 
              onClick={() => history.push(`/app/list/versions/${appId}/create`)}
            >
              创建新版本
            </Button>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={() => run()}
            >
              刷新
            </Button>
          </Space>
        </div>
        
        <Table 
          columns={columns} 
          dataSource={data?.versions} 
          rowKey="id" 
          loading={loading}
          scroll={{ x: 1000 }}
          pagination={{
            current: data?.page || 1,
            pageSize: data?.page_size || 20,
            total: data?.versions?.length || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => {
              setSearchParams({
                ...searchParams,
                page,
                pageSize,
              });
            },
          }}
        />
      </Card>
    </PageContainer>
  );
};

export default DeveloperVersions;