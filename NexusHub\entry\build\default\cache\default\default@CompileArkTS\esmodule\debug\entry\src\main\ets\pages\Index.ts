if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface Index_Params {
    currentTabIndex?: number;
    deviceUtils?;
    navItems?: NavItem[];
}
import { HomePage } from "@normalized:N&&&entry/src/main/ets/pages/HomePage&";
import { FeaturedPage } from "@normalized:N&&&entry/src/main/ets/pages/FeaturedPage&";
import { CategoryListPage } from "@normalized:N&&&entry/src/main/ets/pages/CategoryListPage&";
import { ProfilePage } from "@normalized:N&&&entry/src/main/ets/pages/ProfilePage&";
import { NavigationBar } from "@normalized:N&&&entry/src/main/ets/components/NavigationBar&";
import type { NavItem } from "@normalized:N&&&entry/src/main/ets/components/NavigationBar&";
import { DeviceUtils } from "@normalized:N&&&entry/src/main/ets/utils/DeviceUtils&";
class Index extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__currentTabIndex = new ObservedPropertySimplePU(0, this, "currentTabIndex");
        this.deviceUtils = DeviceUtils.getInstance();
        this.navItems = [
            {
                icon: { "id": 16777260, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" },
                activeIcon: { "id": 16777260, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" },
                label: '首页',
                route: 'pages/HomePage'
            },
            {
                icon: { "id": 16777255, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" },
                activeIcon: { "id": 16777255, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" },
                label: '精选',
                route: 'pages/FeaturedPage'
            },
            {
                icon: { "id": 16777247, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" },
                activeIcon: { "id": 16777247, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" },
                label: '分类',
                route: 'pages/CategoryListPage'
            },
            {
                icon: { "id": 16777268, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" },
                activeIcon: { "id": 16777268, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" },
                label: '我的',
                route: 'pages/ProfilePage'
            }
        ];
        this.setInitiallyProvidedValue(params);
        this.declareWatch("currentTabIndex", this.onTabIndexChange);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: Index_Params) {
        if (params.currentTabIndex !== undefined) {
            this.currentTabIndex = params.currentTabIndex;
        }
        if (params.deviceUtils !== undefined) {
            this.deviceUtils = params.deviceUtils;
        }
        if (params.navItems !== undefined) {
            this.navItems = params.navItems;
        }
    }
    updateStateVars(params: Index_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__currentTabIndex.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__currentTabIndex.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __currentTabIndex: ObservedPropertySimplePU<number>;
    get currentTabIndex() {
        return this.__currentTabIndex.get();
    }
    set currentTabIndex(newValue: number) {
        this.__currentTabIndex.set(newValue);
    }
    private deviceUtils;
    // 导航项配置
    private navItems: NavItem[];
    /**
     * 标签切换事件处理
     */
    private onTabChange(index: number) {
        this.currentTabIndex = index;
    }
    /**
     * 监听标签索引变化
     */
    private onTabIndexChange() {
        // 当切换到个人中心页面时，强制刷新页面状态
        if (this.currentTabIndex === 3) {
            // 通过延迟执行确保页面组件已经创建
            setTimeout(() => {
                // 这里可以添加额外的刷新逻辑
            }, 100);
        }
    }
    /**
     * 根据当前标签索引渲染对应页面
     */
    private CurrentPage(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.currentTabIndex === 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new HomePage(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/Index.ets", line: 74, col: 7 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {};
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {});
                            }
                        }, { name: "HomePage" });
                    }
                });
            }
            else if (this.currentTabIndex === 1) {
                this.ifElseBranchUpdateFunction(1, () => {
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new FeaturedPage(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/Index.ets", line: 76, col: 7 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {};
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {});
                            }
                        }, { name: "FeaturedPage" });
                    }
                });
            }
            else if (this.currentTabIndex === 2) {
                this.ifElseBranchUpdateFunction(2, () => {
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new CategoryListPage(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/Index.ets", line: 78, col: 7 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {};
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {});
                            }
                        }, { name: "CategoryListPage" });
                    }
                });
            }
            else if (this.currentTabIndex === 3) {
                this.ifElseBranchUpdateFunction(3, () => {
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new ProfilePage(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/Index.ets", line: 80, col: 7 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {};
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {});
                            }
                        }, { name: "ProfilePage" });
                    }
                });
            }
            else {
                this.ifElseBranchUpdateFunction(4, () => {
                });
            }
        }, If);
        If.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.deviceUtils.isPhone()) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 手机设备：使用底部导航栏
                        Column.create();
                        // 手机设备：使用底部导航栏
                        Column.width('100%');
                        // 手机设备：使用底部导航栏
                        Column.height('100%');
                        // 手机设备：使用底部导航栏
                        Column.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 主要内容区域
                        Column.create();
                        // 主要内容区域
                        Column.layoutWeight(1);
                    }, Column);
                    this.CurrentPage.bind(this)();
                    // 主要内容区域
                    Column.pop();
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new 
                                // 底部导航栏
                                NavigationBar(this, {
                                    navItems: this.navItems,
                                    currentIndex: this.currentTabIndex,
                                    onItemClick: (index: number): void => this.onTabChange(index),
                                    isBottomNav: true
                                }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/Index.ets", line: 95, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        navItems: this.navItems,
                                        currentIndex: this.currentTabIndex,
                                        onItemClick: (index: number): void => this.onTabChange(index),
                                        isBottomNav: true
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    navItems: this.navItems,
                                    currentIndex: this.currentTabIndex,
                                    isBottomNav: true
                                });
                            }
                        }, { name: "NavigationBar" });
                    }
                    // 手机设备：使用底部导航栏
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 平板和2in1设备：使用左侧导航栏
                        Row.create();
                        // 平板和2in1设备：使用左侧导航栏
                        Row.width('100%');
                        // 平板和2in1设备：使用左侧导航栏
                        Row.height('100%');
                        // 平板和2in1设备：使用左侧导航栏
                        Row.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                    }, Row);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new 
                                // 左侧导航栏
                                NavigationBar(this, {
                                    navItems: this.navItems,
                                    currentIndex: this.currentTabIndex,
                                    onItemClick: (index: number): void => this.onTabChange(index),
                                    isBottomNav: false
                                }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/Index.ets", line: 109, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        navItems: this.navItems,
                                        currentIndex: this.currentTabIndex,
                                        onItemClick: (index: number): void => this.onTabChange(index),
                                        isBottomNav: false
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    navItems: this.navItems,
                                    currentIndex: this.currentTabIndex,
                                    isBottomNav: false
                                });
                            }
                        }, { name: "NavigationBar" });
                    }
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 主要内容区域
                        Column.create();
                        // 主要内容区域
                        Column.layoutWeight(1);
                        // 主要内容区域
                        Column.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                    }, Column);
                    this.CurrentPage.bind(this)();
                    // 主要内容区域
                    Column.pop();
                    // 平板和2in1设备：使用左侧导航栏
                    Row.pop();
                });
            }
        }, If);
        If.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "Index";
    }
}
registerNamedRoute(() => new Index(undefined, {}), "", { bundleName: "com.example.nexushub", moduleName: "entry", pagePath: "pages/Index", pageFullPath: "entry/src/main/ets/pages/Index", integratedHsp: "false", moduleType: "followWithHap" });
