package database

import (
	"context"
	"fmt"
	"time"

	"nexushub-oh-back/config"

	"github.com/redis/go-redis/v9"
)

// RedisClient Redis 客户端结构体
type RedisClient struct {
	Client *redis.Client
}

// NewRedisClient 创建一个新的 Redis 客户端
func NewRedisClient(cfg *config.RedisConfig) (*RedisClient, error) {
	client := redis.NewClient(&redis.Options{
		Addr:     cfg.Addr,
		Password: cfg.Password,
		DB:       cfg.DB,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if _, err := client.Ping(ctx).Result(); err != nil {
		return nil, fmt.Errorf("无法连接到Redis: %w", err)
	}

	return &RedisClient{
		Client: client,
	}, nil
}

// Set 设置缓存
func (r *RedisClient) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	return r.Client.Set(ctx, key, value, expiration).Err()
}

// Get 获取缓存
func (r *RedisClient) Get(ctx context.Context, key string) (string, error) {
	return r.Client.Get(ctx, key).Result()
}

// Delete 删除缓存
func (r *RedisClient) Delete(ctx context.Context, key string) error {
	return r.Client.Del(ctx, key).Err()
}

// Close 关闭连接
func (r *RedisClient) Close() error {
	return r.Client.Close()
}
