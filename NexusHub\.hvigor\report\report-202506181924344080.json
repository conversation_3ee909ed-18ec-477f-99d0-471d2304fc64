{"version": "2.0", "ppid": 32784, "events": [{"head": {"id": "1bc6d07f-34d4-4ca0-9336-f48ee64717ef", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787214517700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "157f0641-b671-42f6-9ac1-ea39df649d04", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787214798900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7682bcd7-5321-461a-865a-9b888efa0053", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787282569000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cce83dd0-3e5d-46f0-9776-17cb04347fc5", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787283153600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3424de14-8473-43be-bf24-dacc52778b83", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787286072500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c9682db-d530-464a-b059-956688aeb68d", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 219787286693300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dcfd8c2-d114-4bf6-9aee-ef19824666d4", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244529917600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b58c1cfb-27fb-449f-9993-92b7e54a0499", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244579098300, "endTime": 221245157426900}, "additional": {"children": ["82ca1376-3b04-4cef-8fcb-90f05d729bf5", "1be38ddc-9924-470f-8729-f7ffce651327", "8507e425-4198-4865-885d-68a8e88c738f", "e7f61898-8efb-4829-842f-7ac7bffb9216", "55558e11-b34c-4e60-ac84-0645da45dafa", "d8f34289-f007-40dd-b740-8dadbfe4b307", "ab9f00c0-13da-4e25-bfb1-965531c7ebb1"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "d0328808-d86b-485b-95ad-ac2495e703b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82ca1376-3b04-4cef-8fcb-90f05d729bf5", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244579113600, "endTime": 221244628905200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b58c1cfb-27fb-449f-9993-92b7e54a0499", "logId": "11b7dfde-543b-4553-91bf-1e65311f3d2e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1be38ddc-9924-470f-8729-f7ffce651327", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244628943800, "endTime": 221245153722300}, "additional": {"children": ["23e321a4-272f-436b-b342-221d4f76245d", "8b728bb3-ab41-4c3e-aa07-9ba8b9262791", "7717c59e-27e3-48fc-bf0f-4666d0f03c58", "4fc85304-9453-4a5c-bc03-5018b9dc77e0", "ccbc9a0e-bdf0-410a-aca4-a7dc04037616", "2c0a75f3-6e4a-40a3-836f-a6ea393f08cc", "fe0dbfed-875d-4c20-8909-7f907b633930", "2ba479b9-f181-4e5d-a694-04f6b28b056f", "67168e41-becc-485e-a2dd-5faa1ae4a5be"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b58c1cfb-27fb-449f-9993-92b7e54a0499", "logId": "fde5cfc5-0f8f-4a72-950a-b82594083a5b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8507e425-4198-4865-885d-68a8e88c738f", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245153777500, "endTime": 221245156983800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b58c1cfb-27fb-449f-9993-92b7e54a0499", "logId": "da20d24e-f57e-4c41-983a-27160fd4fb15"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7f61898-8efb-4829-842f-7ac7bffb9216", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245156996100, "endTime": 221245157384000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b58c1cfb-27fb-449f-9993-92b7e54a0499", "logId": "c37200fc-720c-4921-9a48-924950925a0f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "55558e11-b34c-4e60-ac84-0645da45dafa", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244588408900, "endTime": 221244589384300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b58c1cfb-27fb-449f-9993-92b7e54a0499", "logId": "6c1feecb-4007-422c-a301-52338b7a704d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c1feecb-4007-422c-a301-52338b7a704d", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244588408900, "endTime": 221244589384300}, "additional": {"logType": "info", "children": [], "durationId": "55558e11-b34c-4e60-ac84-0645da45dafa", "parent": "d0328808-d86b-485b-95ad-ac2495e703b5"}}, {"head": {"id": "d8f34289-f007-40dd-b740-8dadbfe4b307", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244604024000, "endTime": 221244604056000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b58c1cfb-27fb-449f-9993-92b7e54a0499", "logId": "a4085d7a-6709-4bca-beba-71f00313eb3e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4085d7a-6709-4bca-beba-71f00313eb3e", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244604024000, "endTime": 221244604056000}, "additional": {"logType": "info", "children": [], "durationId": "d8f34289-f007-40dd-b740-8dadbfe4b307", "parent": "d0328808-d86b-485b-95ad-ac2495e703b5"}}, {"head": {"id": "cb72e725-24ca-48a0-ae4b-7dfe21e6e006", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244604862900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffb601da-15bc-42d4-9e7c-640b7a148d9f", "name": "Cache service initialization finished in 21 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244628555900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11b7dfde-543b-4553-91bf-1e65311f3d2e", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244579113600, "endTime": 221244628905200}, "additional": {"logType": "info", "children": [], "durationId": "82ca1376-3b04-4cef-8fcb-90f05d729bf5", "parent": "d0328808-d86b-485b-95ad-ac2495e703b5"}}, {"head": {"id": "23e321a4-272f-436b-b342-221d4f76245d", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244641790200, "endTime": 221244641803400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1be38ddc-9924-470f-8729-f7ffce651327", "logId": "1e480083-a947-4475-9485-6878f3d2080f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b728bb3-ab41-4c3e-aa07-9ba8b9262791", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244641829500, "endTime": 221244659582200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1be38ddc-9924-470f-8729-f7ffce651327", "logId": "84f5febf-7e1e-4572-9800-51296226b305"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7717c59e-27e3-48fc-bf0f-4666d0f03c58", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244659613800, "endTime": 221244898927700}, "additional": {"children": ["6c358fe9-e294-4fd6-bc1d-38f5848ebf78", "1981e5de-8a7c-4a4e-8d89-8118747ac47c", "231a9281-3679-4617-9c35-948b4da2c61b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1be38ddc-9924-470f-8729-f7ffce651327", "logId": "a6976c7b-eb63-4c23-85ed-9d6193c54ec1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4fc85304-9453-4a5c-bc03-5018b9dc77e0", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244898954800, "endTime": 221244962383400}, "additional": {"children": ["4ea494f9-f8e1-41f8-b797-ef35df778dcb"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1be38ddc-9924-470f-8729-f7ffce651327", "logId": "3057aa50-0fc3-4615-ae9b-6597594d72f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ccbc9a0e-bdf0-410a-aca4-a7dc04037616", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244962421400, "endTime": 221245094889600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1be38ddc-9924-470f-8729-f7ffce651327", "logId": "bab4f23f-5a05-4d83-af8b-aecae309b660"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c0a75f3-6e4a-40a3-836f-a6ea393f08cc", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245096688900, "endTime": 221245116122200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1be38ddc-9924-470f-8729-f7ffce651327", "logId": "3ba88f8f-66b3-4127-9fd6-0a2f4ee94ef1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe0dbfed-875d-4c20-8909-7f907b633930", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245116164900, "endTime": 221245153357900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1be38ddc-9924-470f-8729-f7ffce651327", "logId": "4b64aa89-0489-4cbc-bbe4-064cc55b8faf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ba479b9-f181-4e5d-a694-04f6b28b056f", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245153406600, "endTime": 221245153693800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1be38ddc-9924-470f-8729-f7ffce651327", "logId": "8ed352d7-194d-4cc5-9806-bbd3ab5b83f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e480083-a947-4475-9485-6878f3d2080f", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244641790200, "endTime": 221244641803400}, "additional": {"logType": "info", "children": [], "durationId": "23e321a4-272f-436b-b342-221d4f76245d", "parent": "fde5cfc5-0f8f-4a72-950a-b82594083a5b"}}, {"head": {"id": "84f5febf-7e1e-4572-9800-51296226b305", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244641829500, "endTime": 221244659582200}, "additional": {"logType": "info", "children": [], "durationId": "8b728bb3-ab41-4c3e-aa07-9ba8b9262791", "parent": "fde5cfc5-0f8f-4a72-950a-b82594083a5b"}}, {"head": {"id": "6c358fe9-e294-4fd6-bc1d-38f5848ebf78", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244662242100, "endTime": 221244662280800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7717c59e-27e3-48fc-bf0f-4666d0f03c58", "logId": "01315011-25fb-4d70-a751-a008cc2fd1a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "01315011-25fb-4d70-a751-a008cc2fd1a8", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244662242100, "endTime": 221244662280800}, "additional": {"logType": "info", "children": [], "durationId": "6c358fe9-e294-4fd6-bc1d-38f5848ebf78", "parent": "a6976c7b-eb63-4c23-85ed-9d6193c54ec1"}}, {"head": {"id": "1981e5de-8a7c-4a4e-8d89-8118747ac47c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244668209800, "endTime": 221244896997800}, "additional": {"children": ["ec23c41b-ba09-4464-a959-7d56109805cf", "af19bed7-662a-4471-bc07-816f8c90e996"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7717c59e-27e3-48fc-bf0f-4666d0f03c58", "logId": "81a0dec5-82a3-4217-adda-20df85a61b6b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec23c41b-ba09-4464-a959-7d56109805cf", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244668213500, "endTime": 221244704531500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1981e5de-8a7c-4a4e-8d89-8118747ac47c", "logId": "24881889-6684-4e96-8ef6-a52529bdb51a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af19bed7-662a-4471-bc07-816f8c90e996", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244704588800, "endTime": 221244896966800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1981e5de-8a7c-4a4e-8d89-8118747ac47c", "logId": "3a23fc94-cbcd-44f6-ad6e-35eb46dd4518"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f568bd4-c5c5-4cb7-9739-8326940160bd", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244668230400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7192e4f-c3a5-43da-8657-9d21d2bb1914", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244704261800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24881889-6684-4e96-8ef6-a52529bdb51a", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244668213500, "endTime": 221244704531500}, "additional": {"logType": "info", "children": [], "durationId": "ec23c41b-ba09-4464-a959-7d56109805cf", "parent": "81a0dec5-82a3-4217-adda-20df85a61b6b"}}, {"head": {"id": "5f6a6d78-d520-4bf4-860a-d473c4457d2d", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244704622700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ba09051-b488-4622-9564-ab4071f8595c", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244727537200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f1dd39c-ee97-4cd4-80be-57fcf4b93645", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244727870900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6aa53705-64bf-4011-bcb0-192d134d0e9d", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244728217900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cff54f7c-3137-4b03-ac5a-8457204b88ae", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244728554600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8147d5f8-17bf-4003-a894-bae39e435cab", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244736944500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35aaa4b0-85b3-4724-ad4e-831c240f9daf", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244786528500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "917d08a0-ce48-4f10-b931-44de94d32e46", "name": "Sdk init in 79 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244834791300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77f1641f-74e6-42ca-a727-46a2171c7d49", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244835715800}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 19, "minute": 24, "second": 34}, "markType": "other"}}, {"head": {"id": "0637ef7e-7e48-4212-84e4-366255af5b63", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244835922400}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 19, "minute": 24, "second": 34}, "markType": "other"}}, {"head": {"id": "6290e821-b200-4e41-abec-3cb7357f566c", "name": "Project task initialization takes 57 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244896081300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9815f20-050f-455f-b30a-6fb0ce91de7d", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244896397600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d43d1065-d2b5-4f75-9729-9fc08a877b39", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244896554400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49838615-3832-48eb-814a-8a09aecffd81", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244896788600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a23fc94-cbcd-44f6-ad6e-35eb46dd4518", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244704588800, "endTime": 221244896966800}, "additional": {"logType": "info", "children": [], "durationId": "af19bed7-662a-4471-bc07-816f8c90e996", "parent": "81a0dec5-82a3-4217-adda-20df85a61b6b"}}, {"head": {"id": "81a0dec5-82a3-4217-adda-20df85a61b6b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244668209800, "endTime": 221244896997800}, "additional": {"logType": "info", "children": ["24881889-6684-4e96-8ef6-a52529bdb51a", "3a23fc94-cbcd-44f6-ad6e-35eb46dd4518"], "durationId": "1981e5de-8a7c-4a4e-8d89-8118747ac47c", "parent": "a6976c7b-eb63-4c23-85ed-9d6193c54ec1"}}, {"head": {"id": "231a9281-3679-4617-9c35-948b4da2c61b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244898851900, "endTime": 221244898890400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7717c59e-27e3-48fc-bf0f-4666d0f03c58", "logId": "2cc594fc-e7ec-47c4-be2a-28fff49703ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2cc594fc-e7ec-47c4-be2a-28fff49703ec", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244898851900, "endTime": 221244898890400}, "additional": {"logType": "info", "children": [], "durationId": "231a9281-3679-4617-9c35-948b4da2c61b", "parent": "a6976c7b-eb63-4c23-85ed-9d6193c54ec1"}}, {"head": {"id": "a6976c7b-eb63-4c23-85ed-9d6193c54ec1", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244659613800, "endTime": 221244898927700}, "additional": {"logType": "info", "children": ["01315011-25fb-4d70-a751-a008cc2fd1a8", "81a0dec5-82a3-4217-adda-20df85a61b6b", "2cc594fc-e7ec-47c4-be2a-28fff49703ec"], "durationId": "7717c59e-27e3-48fc-bf0f-4666d0f03c58", "parent": "fde5cfc5-0f8f-4a72-950a-b82594083a5b"}}, {"head": {"id": "4ea494f9-f8e1-41f8-b797-ef35df778dcb", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244900315700, "endTime": 221244962352000}, "additional": {"children": ["09efa6e5-af58-43c6-a912-4b15e3c679c1", "e4b6b8ea-cc99-4e86-9a9a-0760df19b9da", "c52dc4f1-30d8-4b0f-b98a-a9b6b7dec164"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4fc85304-9453-4a5c-bc03-5018b9dc77e0", "logId": "a08cbf9c-25e2-417d-9217-d62513bc5b99"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "09efa6e5-af58-43c6-a912-4b15e3c679c1", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244911138600, "endTime": 221244911178800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4ea494f9-f8e1-41f8-b797-ef35df778dcb", "logId": "215583f6-1f82-429a-8048-eb7fec27911d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "215583f6-1f82-429a-8048-eb7fec27911d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244911138600, "endTime": 221244911178800}, "additional": {"logType": "info", "children": [], "durationId": "09efa6e5-af58-43c6-a912-4b15e3c679c1", "parent": "a08cbf9c-25e2-417d-9217-d62513bc5b99"}}, {"head": {"id": "e4b6b8ea-cc99-4e86-9a9a-0760df19b9da", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244915871200, "endTime": 221244958714500}, "additional": {"children": ["df9b6e4d-2d3d-4f0f-b4fc-93c7e80a36a7", "0ddd5d1f-f79e-4d08-a542-806d04d9af06"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4ea494f9-f8e1-41f8-b797-ef35df778dcb", "logId": "602f78cb-e683-4500-b6d0-c2e450a7502b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df9b6e4d-2d3d-4f0f-b4fc-93c7e80a36a7", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244915874400, "endTime": 221244922851200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4b6b8ea-cc99-4e86-9a9a-0760df19b9da", "logId": "e9ed4922-9214-47f7-8b3f-bc8d451b4ac1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ddd5d1f-f79e-4d08-a542-806d04d9af06", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244922884200, "endTime": 221244958687600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4b6b8ea-cc99-4e86-9a9a-0760df19b9da", "logId": "3e0c337f-ea94-444b-88d0-30325824392a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95fcbe3c-09f2-4948-994d-ecb5bbb82f46", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244915888000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "856f0dfe-9b47-44a3-a2f4-2e9280035b7d", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244922512500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9ed4922-9214-47f7-8b3f-bc8d451b4ac1", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244915874400, "endTime": 221244922851200}, "additional": {"logType": "info", "children": [], "durationId": "df9b6e4d-2d3d-4f0f-b4fc-93c7e80a36a7", "parent": "602f78cb-e683-4500-b6d0-c2e450a7502b"}}, {"head": {"id": "9a335f44-22bc-4886-9dba-bc785e838f47", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244922912300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "247fc7d3-cad2-4452-b406-5df071455c1b", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244941895600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca41659c-d9e5-4f20-8d48-1d2e9df4b017", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244942262300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c2c6636-b9df-42d3-a7fb-ac3d292a893b", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244942876000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24795b7a-9012-4dbc-9853-0990c7bbd6ce", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244943597300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05990a79-f0e5-41c0-993b-5be96e085a0d", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244943770800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "355e7996-c351-4ffc-8119-242c7da9fae0", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244944078400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43a9abe7-3f87-4a9b-8e99-bd2a4094945d", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244944549600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea62d92f-47ed-40e5-959e-5bff4ec6159d", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244944783800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8697c44a-27ba-4a82-841b-2ee5d87dc280", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244945270500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e451768b-5191-4ddc-a695-9d97eb17806b", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244945641200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c40be8b-755e-4657-9df2-b90b08a2d149", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244945838800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e312ed36-7170-498a-970f-4ef4ef3139a8", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244945953400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca8d3a4a-26b8-4a51-8a01-42895faf761c", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244946082200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d089927-7766-4a30-a2ec-0bc95ff52075", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244946224600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cade609-b6fa-497a-94f2-6b592130cefb", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244946607300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bd440c9-1e0d-4fa8-b551-e3db65600a2f", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244946855000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "425efe6d-d90d-48b6-8294-e1b4f37a79bb", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244947020300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbefea20-ae30-48ab-a7a9-3419daca310a", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244947146900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c754db8d-d92c-439a-b75c-644568069469", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244947313600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c26e300-fbb6-45f3-8b2e-0f3e0cd79e3c", "name": "Module entry task initialization takes 3 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244957710700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0623050c-097e-4342-ac17-42ca58425f33", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244958147000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bae1579d-e784-4363-b2d0-66172a12d884", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244958340400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b1e03e9-3639-44db-9ca0-6abf4e41eb3f", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244958511800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e0c337f-ea94-444b-88d0-30325824392a", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244922884200, "endTime": 221244958687600}, "additional": {"logType": "info", "children": [], "durationId": "0ddd5d1f-f79e-4d08-a542-806d04d9af06", "parent": "602f78cb-e683-4500-b6d0-c2e450a7502b"}}, {"head": {"id": "602f78cb-e683-4500-b6d0-c2e450a7502b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244915871200, "endTime": 221244958714500}, "additional": {"logType": "info", "children": ["e9ed4922-9214-47f7-8b3f-bc8d451b4ac1", "3e0c337f-ea94-444b-88d0-30325824392a"], "durationId": "e4b6b8ea-cc99-4e86-9a9a-0760df19b9da", "parent": "a08cbf9c-25e2-417d-9217-d62513bc5b99"}}, {"head": {"id": "c52dc4f1-30d8-4b0f-b98a-a9b6b7dec164", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244962282400, "endTime": 221244962317100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4ea494f9-f8e1-41f8-b797-ef35df778dcb", "logId": "c7169a7e-d53c-4439-b24b-2b3afb91646b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7169a7e-d53c-4439-b24b-2b3afb91646b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244962282400, "endTime": 221244962317100}, "additional": {"logType": "info", "children": [], "durationId": "c52dc4f1-30d8-4b0f-b98a-a9b6b7dec164", "parent": "a08cbf9c-25e2-417d-9217-d62513bc5b99"}}, {"head": {"id": "a08cbf9c-25e2-417d-9217-d62513bc5b99", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244900315700, "endTime": 221244962352000}, "additional": {"logType": "info", "children": ["215583f6-1f82-429a-8048-eb7fec27911d", "602f78cb-e683-4500-b6d0-c2e450a7502b", "c7169a7e-d53c-4439-b24b-2b3afb91646b"], "durationId": "4ea494f9-f8e1-41f8-b797-ef35df778dcb", "parent": "3057aa50-0fc3-4615-ae9b-6597594d72f3"}}, {"head": {"id": "3057aa50-0fc3-4615-ae9b-6597594d72f3", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244898954800, "endTime": 221244962383400}, "additional": {"logType": "info", "children": ["a08cbf9c-25e2-417d-9217-d62513bc5b99"], "durationId": "4fc85304-9453-4a5c-bc03-5018b9dc77e0", "parent": "fde5cfc5-0f8f-4a72-950a-b82594083a5b"}}, {"head": {"id": "b0eb3a09-4840-4d53-a8cc-5fdca3465208", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245008014400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1afb6e5-9795-4d1f-a6f8-0a0deca9c267", "name": "hvigorfile, resolve hvigorfile dependencies in 133 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245094615400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bab4f23f-5a05-4d83-af8b-aecae309b660", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244962421400, "endTime": 221245094889600}, "additional": {"logType": "info", "children": [], "durationId": "ccbc9a0e-bdf0-410a-aca4-a7dc04037616", "parent": "fde5cfc5-0f8f-4a72-950a-b82594083a5b"}}, {"head": {"id": "67168e41-becc-485e-a2dd-5faa1ae4a5be", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245096283100, "endTime": 221245096664900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1be38ddc-9924-470f-8729-f7ffce651327", "logId": "b2557d6e-30ff-458a-8c0f-ec135c5b38bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "184163e0-70ca-4c75-84ff-93a2893356d7", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245096360100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2557d6e-30ff-458a-8c0f-ec135c5b38bc", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245096283100, "endTime": 221245096664900}, "additional": {"logType": "info", "children": [], "durationId": "67168e41-becc-485e-a2dd-5faa1ae4a5be", "parent": "fde5cfc5-0f8f-4a72-950a-b82594083a5b"}}, {"head": {"id": "444b2fcc-242d-42b6-899b-4315c13ddbd0", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245100643200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47219ab2-a8e3-480a-8872-e02d935972da", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245114463200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ba88f8f-66b3-4127-9fd6-0a2f4ee94ef1", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245096688900, "endTime": 221245116122200}, "additional": {"logType": "info", "children": [], "durationId": "2c0a75f3-6e4a-40a3-836f-a6ea393f08cc", "parent": "fde5cfc5-0f8f-4a72-950a-b82594083a5b"}}, {"head": {"id": "bb93942b-5f87-4682-8983-0a04c4ec56cc", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245116197200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72699358-eadc-42da-816e-5b46b6bc2077", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245133771900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3adbf70b-c41c-43d5-afff-b156120fdaeb", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245134098300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b965147-da39-4917-832e-82a49a16367b", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245134651500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac122947-0c1b-4140-9b98-b8da98d617fd", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245142607700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7da66415-df33-4929-a7d0-5497ecd3cc20", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245142888900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b64aa89-0489-4cbc-bbe4-064cc55b8faf", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245116164900, "endTime": 221245153357900}, "additional": {"logType": "info", "children": [], "durationId": "fe0dbfed-875d-4c20-8909-7f907b633930", "parent": "fde5cfc5-0f8f-4a72-950a-b82594083a5b"}}, {"head": {"id": "9dad5cc4-0b17-48b2-bc47-e41e18db0886", "name": "Configuration phase cost:512 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245153461200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ed352d7-194d-4cc5-9806-bbd3ab5b83f3", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245153406600, "endTime": 221245153693800}, "additional": {"logType": "info", "children": [], "durationId": "2ba479b9-f181-4e5d-a694-04f6b28b056f", "parent": "fde5cfc5-0f8f-4a72-950a-b82594083a5b"}}, {"head": {"id": "fde5cfc5-0f8f-4a72-950a-b82594083a5b", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244628943800, "endTime": 221245153722300}, "additional": {"logType": "info", "children": ["1e480083-a947-4475-9485-6878f3d2080f", "84f5febf-7e1e-4572-9800-51296226b305", "a6976c7b-eb63-4c23-85ed-9d6193c54ec1", "3057aa50-0fc3-4615-ae9b-6597594d72f3", "bab4f23f-5a05-4d83-af8b-aecae309b660", "3ba88f8f-66b3-4127-9fd6-0a2f4ee94ef1", "4b64aa89-0489-4cbc-bbe4-064cc55b8faf", "8ed352d7-194d-4cc5-9806-bbd3ab5b83f3", "b2557d6e-30ff-458a-8c0f-ec135c5b38bc"], "durationId": "1be38ddc-9924-470f-8729-f7ffce651327", "parent": "d0328808-d86b-485b-95ad-ac2495e703b5"}}, {"head": {"id": "ab9f00c0-13da-4e25-bfb1-965531c7ebb1", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245156887500, "endTime": 221245156943700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b58c1cfb-27fb-449f-9993-92b7e54a0499", "logId": "92a7e670-833a-4ebd-9c57-197e0ae496c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92a7e670-833a-4ebd-9c57-197e0ae496c8", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245156887500, "endTime": 221245156943700}, "additional": {"logType": "info", "children": [], "durationId": "ab9f00c0-13da-4e25-bfb1-965531c7ebb1", "parent": "d0328808-d86b-485b-95ad-ac2495e703b5"}}, {"head": {"id": "da20d24e-f57e-4c41-983a-27160fd4fb15", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245153777500, "endTime": 221245156983800}, "additional": {"logType": "info", "children": [], "durationId": "8507e425-4198-4865-885d-68a8e88c738f", "parent": "d0328808-d86b-485b-95ad-ac2495e703b5"}}, {"head": {"id": "c37200fc-720c-4921-9a48-924950925a0f", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245156996100, "endTime": 221245157384000}, "additional": {"logType": "info", "children": [], "durationId": "e7f61898-8efb-4829-842f-7ac7bffb9216", "parent": "d0328808-d86b-485b-95ad-ac2495e703b5"}}, {"head": {"id": "d0328808-d86b-485b-95ad-ac2495e703b5", "name": "init", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244579098300, "endTime": 221245157426900}, "additional": {"logType": "info", "children": ["11b7dfde-543b-4553-91bf-1e65311f3d2e", "fde5cfc5-0f8f-4a72-950a-b82594083a5b", "da20d24e-f57e-4c41-983a-27160fd4fb15", "c37200fc-720c-4921-9a48-924950925a0f", "6c1feecb-4007-422c-a301-52338b7a704d", "a4085d7a-6709-4bca-beba-71f00313eb3e", "92a7e670-833a-4ebd-9c57-197e0ae496c8"], "durationId": "b58c1cfb-27fb-449f-9993-92b7e54a0499"}}, {"head": {"id": "eda089fa-1000-4e0f-a443-a6565b218dbe", "name": "Configuration task cost before running: 589 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245157889100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "086dd036-46c0-4e5d-8470-7b14d20fc7a2", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245185824800, "endTime": 221245217820600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "959bf2e2-2f7b-41ce-8f76-001644a9cff4", "logId": "3f739ca5-d683-4047-91a8-bd076dbf8e7d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "959bf2e2-2f7b-41ce-8f76-001644a9cff4", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245162005300}, "additional": {"logType": "detail", "children": [], "durationId": "086dd036-46c0-4e5d-8470-7b14d20fc7a2"}}, {"head": {"id": "80ce3ba8-2905-40d7-a3c9-116a9231c2fc", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245164198600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f72601b-598c-4598-ad85-b77102146bfc", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245164583000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2862c845-ea04-4030-82e6-0ef8edf55b32", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245167092600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbdc59dc-ca0a-4677-ac2e-e6e234365ce9", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245173058300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e43d9a4-cc30-4b73-ae0a-12a7009bc0c1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245175704900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89bac443-c90c-4248-ad0e-e2c76ba99df2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245175912000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cb511a6-061a-4c4f-af81-081f596f7940", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245185916400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3619a86-4f76-4480-8f78-d15dc4e2947d", "name": "Incremental task entry:default@PreBuild pre-execution cost: 21 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245217251100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94d79c36-5219-4caa-acfe-ce2ddfa548d3", "name": "entry : default@PreBuild cost memory 0.32913970947265625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245217567900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f739ca5-d683-4047-91a8-bd076dbf8e7d", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245185824800, "endTime": 221245217820600}, "additional": {"logType": "info", "children": [], "durationId": "086dd036-46c0-4e5d-8470-7b14d20fc7a2"}}, {"head": {"id": "deeba468-a768-4e5a-a4bf-57b676154d76", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245230452400, "endTime": 221245234151000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c6cebf1d-7442-42f3-980c-e23ea83e004c", "logId": "5aa7bc62-b737-4054-aa64-81fdc4e4c0ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c6cebf1d-7442-42f3-980c-e23ea83e004c", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245227318700}, "additional": {"logType": "detail", "children": [], "durationId": "deeba468-a768-4e5a-a4bf-57b676154d76"}}, {"head": {"id": "0a983f8a-0c7b-49db-a012-ff4109ce9810", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245229243400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0678abb-fa04-4162-9fd9-81e531ee35ab", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245229422200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f3c2eea-3129-49ce-86b2-64d91d28946f", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245230469200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03340639-c9b1-44a5-93c9-db804c277c83", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245231641300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb35527d-20cc-4fb5-a40e-be3b7849d47e", "name": "entry : default@CreateModuleInfo cost memory 0.06127166748046875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245233638300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23b668b8-0773-4400-9dba-04fbf1bd90ad", "name": "runTaskFromQueue task cost before running: 665 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245233942100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5aa7bc62-b737-4054-aa64-81fdc4e4c0ef", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245230452400, "endTime": 221245234151000, "totalTime": 3430600}, "additional": {"logType": "info", "children": [], "durationId": "deeba468-a768-4e5a-a4bf-57b676154d76"}}, {"head": {"id": "7222b340-07bf-477e-8dc7-211e7ef9af26", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245252433800, "endTime": 221245259238400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "db21d7c0-fae4-4880-b454-baafbe4cbe8e", "logId": "e75f773d-008d-4569-a751-16a3c5ab6cc8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db21d7c0-fae4-4880-b454-baafbe4cbe8e", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245239024900}, "additional": {"logType": "detail", "children": [], "durationId": "7222b340-07bf-477e-8dc7-211e7ef9af26"}}, {"head": {"id": "b6e9fd74-78a1-41cb-b715-10f987d2132e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245241226400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a68296ef-684b-4356-9e77-4c7b82e4e686", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245241443800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5de6af2c-0c54-406a-9f2d-f9802d342986", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245252486600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aef6e4e4-6f15-4724-b40a-4ad56ac71f47", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 3 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245255025000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f4b6be1-a32f-46c7-9812-71a2ff18f943", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245258837300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff005c3e-38dd-414a-9efa-3b4882189279", "name": "entry : default@GenerateMetadata cost memory 0.10363006591796875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245259093000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e75f773d-008d-4569-a751-16a3c5ab6cc8", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245252433800, "endTime": 221245259238400}, "additional": {"logType": "info", "children": [], "durationId": "7222b340-07bf-477e-8dc7-211e7ef9af26"}}, {"head": {"id": "11369c66-25e3-4b45-8cc0-d9ded6e059f2", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245266060000, "endTime": 221245267017800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "1a355d88-814f-4048-8ea4-331c737f8a0f", "logId": "d2f1908b-d7fa-4255-87ff-98f7363fbd49"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a355d88-814f-4048-8ea4-331c737f8a0f", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245262655600}, "additional": {"logType": "detail", "children": [], "durationId": "11369c66-25e3-4b45-8cc0-d9ded6e059f2"}}, {"head": {"id": "1914a41a-f732-49dd-bbc1-39aac29f7127", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245265466300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0688ddca-2ba4-4762-8a63-8caee617a9d3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245265732000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae06f74e-58d0-48b4-9fe5-cffdc67ea25f", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245266084600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cda5eb98-a885-49d1-bdb4-d345eb40dbe3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245266431700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "548fe50d-efb4-4d68-9d9b-ace7b1399139", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245266595900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d63d156e-77d5-4bbe-8a34-ce15a3d1b23a", "name": "entry : default@ConfigureCmake cost memory 0.0374755859375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245266751600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc015861-43e8-424d-a427-eb10b8472ddc", "name": "runTaskFromQueue task cost before running: 698 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245266910300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2f1908b-d7fa-4255-87ff-98f7363fbd49", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245266060000, "endTime": 221245267017800, "totalTime": 810300}, "additional": {"logType": "info", "children": [], "durationId": "11369c66-25e3-4b45-8cc0-d9ded6e059f2"}}, {"head": {"id": "cbc4e5d5-87e2-4472-bdaa-1c0d1a73af16", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245274567000, "endTime": 221245280139100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "a5c7d4ac-65e6-48f7-af61-df0aa954569c", "logId": "fc703d32-3868-45c9-8f06-53b4f994a3d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5c7d4ac-65e6-48f7-af61-df0aa954569c", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245270731400}, "additional": {"logType": "detail", "children": [], "durationId": "cbc4e5d5-87e2-4472-bdaa-1c0d1a73af16"}}, {"head": {"id": "05863c17-58b6-4bfb-9ba0-8efac7bc5a7a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245273052800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70fb1ab7-7591-41fa-b28f-9eaaa1e0399c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245273262800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36a3c5cd-ef3a-4925-b7f3-8afa0fbb6b47", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245274588900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4b5416a-3e5d-4917-a545-9a1be8bf850a", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245279734800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dea2ed9d-91b3-4896-b78a-37920e20ee33", "name": "entry : default@MergeProfile cost memory 0.1185302734375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245279979800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc703d32-3868-45c9-8f06-53b4f994a3d2", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245274567000, "endTime": 221245280139100}, "additional": {"logType": "info", "children": [], "durationId": "cbc4e5d5-87e2-4472-bdaa-1c0d1a73af16"}}, {"head": {"id": "cf516898-f291-4e6a-8ada-a9a384e539bf", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245286612000, "endTime": 221245291744600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "bbf01690-76cb-44c7-b98b-4a0c32329d29", "logId": "54339f08-e515-44da-b566-4beb49f4d8f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bbf01690-76cb-44c7-b98b-4a0c32329d29", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245283148800}, "additional": {"logType": "detail", "children": [], "durationId": "cf516898-f291-4e6a-8ada-a9a384e539bf"}}, {"head": {"id": "e8ed5b55-0fcd-4cd5-8c1e-d25d76a39be5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245284811100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b87045cf-6b61-4aeb-8984-b16614ad180a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245285014200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7d156d3-eb46-4d45-8713-1f43ce9a3514", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245286631400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2fa53f2-2c7f-4388-a7ae-b1d833fe86f1", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245288474700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf5eeb7d-c77e-40c1-a095-7934d2a7bc2b", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245291306200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dec9c56-a8f3-4910-b00b-c10b724a8205", "name": "entry : default@CreateBuildProfile cost memory 0.108673095703125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245291575500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54339f08-e515-44da-b566-4beb49f4d8f2", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245286612000, "endTime": 221245291744600}, "additional": {"logType": "info", "children": [], "durationId": "cf516898-f291-4e6a-8ada-a9a384e539bf"}}, {"head": {"id": "43d505fe-a7e0-4078-9ac6-b4ae9d4e4da6", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245298049400, "endTime": 221245299142200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "f4c812ef-df4c-45f8-9a7d-6730d8be4df0", "logId": "7be5ad67-180f-4e67-be1f-8deeeb30af59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4c812ef-df4c-45f8-9a7d-6730d8be4df0", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245294714000}, "additional": {"logType": "detail", "children": [], "durationId": "43d505fe-a7e0-4078-9ac6-b4ae9d4e4da6"}}, {"head": {"id": "daa27721-7e68-40be-bf48-1926ac4886a7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245296482000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bb45422-6a4a-4234-a347-4c4373c9280d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245296680500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d459d1cf-86a9-4640-a7e4-6202711e0e79", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245298065500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af1beae9-5dd8-4bd6-ac2b-20c95bc21514", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245298261700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05676d6d-09a8-4ed6-b50a-e18dea831589", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245298345600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b002b394-eabe-482d-a83d-ca2a1d8f24ba", "name": "entry : default@PreCheckSyscap cost memory 0.041107177734375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245298683000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2382d02-f9fc-4e00-9f35-643a52704335", "name": "runTaskFromQueue task cost before running: 730 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245299026600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7be5ad67-180f-4e67-be1f-8deeeb30af59", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245298049400, "endTime": 221245299142200, "totalTime": 933500}, "additional": {"logType": "info", "children": [], "durationId": "43d505fe-a7e0-4078-9ac6-b4ae9d4e4da6"}}, {"head": {"id": "a5342199-7cbf-41f6-9438-92b5090eae4e", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245307070600, "endTime": 221245316369300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "19d16895-eaf5-4796-a5fc-fcb68a6badda", "logId": "8dcfd392-6f26-4ad8-a30f-f31af5e2a79f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19d16895-eaf5-4796-a5fc-fcb68a6badda", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245302331200}, "additional": {"logType": "detail", "children": [], "durationId": "a5342199-7cbf-41f6-9438-92b5090eae4e"}}, {"head": {"id": "a6e1cf7f-04ed-4368-87d9-9da776b65832", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245304182100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59ed655d-5f53-482b-bd63-a034f03f18ec", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245304355800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdd0484a-b43e-48fd-9783-b0a2d0bef5d4", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245307096400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50706a00-6a52-4e0f-af04-50525f1ba33f", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245314313700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fa9eaf1-247b-4a65-b642-6bf4d716453b", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245316007400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e962d5f3-f24e-42e5-8678-0cfc4fb171e2", "name": "entry : default@GeneratePkgContextInfo cost memory 0.24726104736328125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245316229200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dcfd392-6f26-4ad8-a30f-f31af5e2a79f", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245307070600, "endTime": 221245316369300}, "additional": {"logType": "info", "children": [], "durationId": "a5342199-7cbf-41f6-9438-92b5090eae4e"}}, {"head": {"id": "afcb8ef6-10dc-496e-b545-6b549fbee41d", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245330088900, "endTime": 221245337284200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "f16c65f1-5343-4c60-a8fa-18aff6eae774", "logId": "5aba62ad-b355-4a6a-9e6b-a6c999ef6c87"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f16c65f1-5343-4c60-a8fa-18aff6eae774", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245319191000}, "additional": {"logType": "detail", "children": [], "durationId": "afcb8ef6-10dc-496e-b545-6b549fbee41d"}}, {"head": {"id": "c7dddc40-dc71-4af1-adcc-b6dfd03460e7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245320978300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62ef0006-1914-4563-a9d6-4a35886f83ba", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245321220700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1de5efb-0488-4528-b1c5-656641dd723e", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245330117200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e44c550e-412a-4d65-9cfc-3b50f14e5c52", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245333505400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f3c0913-a602-4632-abcf-38ef0a04437e", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245333736300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "780e1d2a-90e6-47d5-8054-ae4f999c4ba1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245333936500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c47968c-1460-48d2-9377-3a1aa9f922b3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245336838900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee4449c6-2202-4e02-b1e7-1c451d8404d4", "name": "entry : default@ProcessIntegratedHsp cost memory -5.1820831298828125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245336999000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a7784b9-bedc-433d-9858-09968b4e08b1", "name": "runTaskFromQueue task cost before running: 768 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245337134500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5aba62ad-b355-4a6a-9e6b-a6c999ef6c87", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245330088900, "endTime": 221245337284200, "totalTime": 7011800}, "additional": {"logType": "info", "children": [], "durationId": "afcb8ef6-10dc-496e-b545-6b549fbee41d"}}, {"head": {"id": "5791e103-906b-4c80-a048-9418fb37c926", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245346023000, "endTime": 221245346984300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "2c8b4d97-3112-4c56-9c70-1b403bcaa750", "logId": "9c5c513f-3456-4928-95f5-a55e3475939c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c8b4d97-3112-4c56-9c70-1b403bcaa750", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245340888400}, "additional": {"logType": "detail", "children": [], "durationId": "5791e103-906b-4c80-a048-9418fb37c926"}}, {"head": {"id": "c1e6b57e-d925-49a0-81c5-f51f6dd702d6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245343860000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3e1aac7-342e-4b1f-af4f-29c5438c2129", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245344098600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0bce995-cbba-492b-825d-25ff308962a3", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245346068900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "689dddb6-d80d-423f-bc10-84e7dc9fadfa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245346374200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98a2ffff-8b19-4b29-aa49-6b46b3b2bc18", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245346497400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9da959c5-d851-4f52-bf01-30b8b2110575", "name": "entry : default@BuildNativeWithCmake cost memory 0.03852081298828125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245346653000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "316a95b7-6afd-4fbe-8ebc-c25a9e3172a0", "name": "runTaskFromQueue task cost before running: 778 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245346812800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c5c513f-3456-4928-95f5-a55e3475939c", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245346023000, "endTime": 221245346984300, "totalTime": 755200}, "additional": {"logType": "info", "children": [], "durationId": "5791e103-906b-4c80-a048-9418fb37c926"}}, {"head": {"id": "67d91c8c-fca1-49c5-9a49-208003b1cc2a", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245354966700, "endTime": 221245365047500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1ccbe6d0-5f8a-4434-afd1-c83e98bf082c", "logId": "db6d3178-c8f5-480a-ac04-131973a7bbd0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ccbe6d0-5f8a-4434-afd1-c83e98bf082c", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245350596000}, "additional": {"logType": "detail", "children": [], "durationId": "67d91c8c-fca1-49c5-9a49-208003b1cc2a"}}, {"head": {"id": "0cd16ae4-22a3-41c8-a35a-af050d8578b7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245353140400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0a028e9-bc5e-4edf-9b2d-e7de916a0b66", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245353331000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33c9d95a-e7c8-443c-a5c4-af4931930179", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245354986800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "996ecdc0-17d7-4f98-8180-6272f75eb5ec", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245364494600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a053ce4e-fef9-4564-bc88-df8bc9d90b72", "name": "entry : default@MakePackInfo cost memory 0.1655731201171875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245364831000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db6d3178-c8f5-480a-ac04-131973a7bbd0", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245354966700, "endTime": 221245365047500}, "additional": {"logType": "info", "children": [], "durationId": "67d91c8c-fca1-49c5-9a49-208003b1cc2a"}}, {"head": {"id": "bfb379b2-1b9f-4063-b19c-0b2984e306e6", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245375474700, "endTime": 221245384549500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "543d3f40-49e5-4ebc-a931-9f9e4b28f8eb", "logId": "33fedaad-781a-4d15-bcab-5fc56b0492ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "543d3f40-49e5-4ebc-a931-9f9e4b28f8eb", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245370071500}, "additional": {"logType": "detail", "children": [], "durationId": "bfb379b2-1b9f-4063-b19c-0b2984e306e6"}}, {"head": {"id": "9ae84086-4ba9-4fa2-94f5-350c4c526998", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245372321500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c591f980-78b5-4309-b29e-3b992f5d05a1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245372521400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "659b6783-829b-4ee8-a2da-47e02591cc44", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245375498800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7243b48-547f-41fa-af72-2c05134ac982", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245376112400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1530418a-7cdd-42c1-b3b8-fcf7a843b490", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 3 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245377927200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6eb32d03-2b67-41b7-8ad7-025bed9f161e", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245384231400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f50b6bd-093f-4f6d-abc4-9f72258e28fe", "name": "entry : default@SyscapTransform cost memory 0.151947021484375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245384430600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33fedaad-781a-4d15-bcab-5fc56b0492ef", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245375474700, "endTime": 221245384549500}, "additional": {"logType": "info", "children": [], "durationId": "bfb379b2-1b9f-4063-b19c-0b2984e306e6"}}, {"head": {"id": "6308f1f3-7d9d-411f-8133-93ce6e5b1f43", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245393574600, "endTime": 221245399758600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "f7b8e88b-4f7b-4f57-83f4-14d615a6b789", "logId": "4c5e6572-7005-49e0-87af-b9d7c6b63942"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7b8e88b-4f7b-4f57-83f4-14d615a6b789", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245387980200}, "additional": {"logType": "detail", "children": [], "durationId": "6308f1f3-7d9d-411f-8133-93ce6e5b1f43"}}, {"head": {"id": "163d426e-2400-4159-aec4-0c7a0fbc4101", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245390350300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2de21947-8c73-4305-882d-50bf97f9c598", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245390559000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e00c3de-1a34-457c-883b-58483d399988", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245393601200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "922bbe95-3409-4b67-91cd-d99f53d39299", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245399301800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0e54f26-cc77-4660-ad0e-6da385f2c875", "name": "entry : default@ProcessProfile cost memory 0.125030517578125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245399548700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c5e6572-7005-49e0-87af-b9d7c6b63942", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245393574600, "endTime": 221245399758600}, "additional": {"logType": "info", "children": [], "durationId": "6308f1f3-7d9d-411f-8133-93ce6e5b1f43"}}, {"head": {"id": "ddfdb18b-26b0-45fb-9edf-a1d0bf9fec43", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245407916100, "endTime": 221245424490800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "84714362-c0eb-48de-8586-56ec9f21a7d1", "logId": "19307595-6090-4b20-9cdf-0937c5046074"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "84714362-c0eb-48de-8586-56ec9f21a7d1", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245401896500}, "additional": {"logType": "detail", "children": [], "durationId": "ddfdb18b-26b0-45fb-9edf-a1d0bf9fec43"}}, {"head": {"id": "529c543b-1c9d-4745-b947-b380685491b8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245404252700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "365efacf-5ea9-4f2a-901c-f0f2e625bf12", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245404440800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "542c8f14-1324-4ae2-91f2-60135a7dd9f8", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245407944800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dfcc386-bee4-474f-ace0-9492f1866953", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245424099900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1faf7f8-7a4f-41f2-ad16-74aa37a96714", "name": "entry : default@ProcessRouterMap cost memory 0.2351531982421875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245424329600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19307595-6090-4b20-9cdf-0937c5046074", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245407916100, "endTime": 221245424490800}, "additional": {"logType": "info", "children": [], "durationId": "ddfdb18b-26b0-45fb-9edf-a1d0bf9fec43"}}, {"head": {"id": "50eb56d7-8ff4-42d5-b36c-fc158e8741c0", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245432412300, "endTime": 221245443092400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "616cdffb-654e-4a7a-bbdf-73ce862cbc66", "logId": "18ec6629-1cb1-480b-9e75-5b5dbea87e13"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "616cdffb-654e-4a7a-bbdf-73ce862cbc66", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245429352900}, "additional": {"logType": "detail", "children": [], "durationId": "50eb56d7-8ff4-42d5-b36c-fc158e8741c0"}}, {"head": {"id": "f3909104-7582-4b33-b817-70d5612d1106", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245431947000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ee0f3b9-befe-40f3-9eb4-59b8497d0ab6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245432161900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9a8d748-496b-4582-afd9-68870faf0f74", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245432426400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faeb1985-fe8d-49b1-a289-715ef36514e9", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245432654700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba09179e-a9cb-4d81-ad95-e14e252d7a28", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245439899100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aae94a36-cb69-428e-bfaa-a2f287236c2d", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245440168600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a63969f5-0170-49cb-9334-6e8660c6e7ee", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245440387800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d82698b-e9ee-442f-95f5-370c8fbe7b1a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245440514300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "303589f9-5245-464a-b294-23143359d521", "name": "entry : default@ProcessStartupConfig cost memory 0.26116180419921875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245442717100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e5211d9-f12c-40f3-ad73-6fd20883bb90", "name": "runTaskFromQueue task cost before running: 874 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245442987100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18ec6629-1cb1-480b-9e75-5b5dbea87e13", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245432412300, "endTime": 221245443092400, "totalTime": 10511600}, "additional": {"logType": "info", "children": [], "durationId": "50eb56d7-8ff4-42d5-b36c-fc158e8741c0"}}, {"head": {"id": "71d39ea2-88e9-4c34-af90-7a52f36db9bb", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245453158000, "endTime": 221245456105900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "f6d5ec47-1859-49c8-82ea-cdc3a75866a8", "logId": "273cf197-13d3-48cb-8f73-d4feb25746f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f6d5ec47-1859-49c8-82ea-cdc3a75866a8", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245448665500}, "additional": {"logType": "detail", "children": [], "durationId": "71d39ea2-88e9-4c34-af90-7a52f36db9bb"}}, {"head": {"id": "ac2d59da-5b0f-46f2-95f4-64c376aecedf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245451272500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c87139b-95de-49e0-ab83-2820f65a94dd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245451490500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9566a28-1dc9-487f-9ac5-a10d9cd4351a", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245453180000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc3bcc84-bff3-41e7-baad-bc9dfe2204a6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245453434100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86b88454-f704-454e-8c5a-70708f68da97", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245453563200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39540700-5bee-4f2f-9c2a-eb8c7e9965f6", "name": "entry : default@BuildNativeWithNinja cost memory 0.05853271484375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245455730200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fd632fb-64b7-4067-be5d-ae723c2d37d7", "name": "runTaskFromQueue task cost before running: 887 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245456012300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "273cf197-13d3-48cb-8f73-d4feb25746f9", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245453158000, "endTime": 221245456105900, "totalTime": 2810300}, "additional": {"logType": "info", "children": [], "durationId": "71d39ea2-88e9-4c34-af90-7a52f36db9bb"}}, {"head": {"id": "ef36b3ff-59a9-4c57-afde-9d48af3226a7", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245467365700, "endTime": 221245482206900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "12515e80-2ac4-4cd7-9ebf-ac4d95dde910", "logId": "1550b106-c8ae-4b73-a1a5-e5a2869dcbac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12515e80-2ac4-4cd7-9ebf-ac4d95dde910", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245459811500}, "additional": {"logType": "detail", "children": [], "durationId": "ef36b3ff-59a9-4c57-afde-9d48af3226a7"}}, {"head": {"id": "3b30e24e-5417-4822-935d-514ec383ae94", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245462204900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04752df6-5d5c-4272-aba9-7d2178a60a74", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245462390800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77eea399-c815-4695-9d1f-7b202b8451f9", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245464990200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a444e119-3210-450f-8a24-704e02ac3cca", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245472315200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eff948ee-255e-41d4-95d7-ad85fe33660e", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245477498400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff12a97a-da35-4461-b7e4-a46367ecb3ac", "name": "entry : default@ProcessResource cost memory 0.163360595703125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245477749600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1550b106-c8ae-4b73-a1a5-e5a2869dcbac", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245467365700, "endTime": 221245482206900}, "additional": {"logType": "info", "children": [], "durationId": "ef36b3ff-59a9-4c57-afde-9d48af3226a7"}}, {"head": {"id": "05ce84b6-9eb9-4c8e-b010-e6ba5ebf2a2d", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245503374800, "endTime": 221245541330100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c706c6a4-2a3f-409c-b029-f144ead0305c", "logId": "60f7b1b0-7a8f-44b8-bbc8-3b8a9b40f2ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c706c6a4-2a3f-409c-b029-f144ead0305c", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245493961300}, "additional": {"logType": "detail", "children": [], "durationId": "05ce84b6-9eb9-4c8e-b010-e6ba5ebf2a2d"}}, {"head": {"id": "c996676d-7752-455c-8dd7-d8507eb5a988", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245497045700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3288a560-188b-4099-b098-0584b4445ef5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245497309000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "236f67b2-9ea6-49b5-af0d-f06a64453d1c", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245503393400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61b5e2bf-e5b4-4652-871c-732c6460f7ea", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 23 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245540861800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1de58056-344e-4c46-9ca8-7a3087752d6c", "name": "entry : default@GenerateLoaderJson cost memory 0.8891143798828125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245541185900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60f7b1b0-7a8f-44b8-bbc8-3b8a9b40f2ef", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245503374800, "endTime": 221245541330100}, "additional": {"logType": "info", "children": [], "durationId": "05ce84b6-9eb9-4c8e-b010-e6ba5ebf2a2d"}}, {"head": {"id": "1e8e2025-4c8f-4642-bb6f-f1f5bc1d57a8", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245564029600, "endTime": 221245574946300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "8b6a51e1-a314-49c8-81a7-f849eac59cb1", "logId": "f8083a59-d1c5-42cb-8296-e0ed6adcf05b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b6a51e1-a314-49c8-81a7-f849eac59cb1", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245559622900}, "additional": {"logType": "detail", "children": [], "durationId": "1e8e2025-4c8f-4642-bb6f-f1f5bc1d57a8"}}, {"head": {"id": "c1259374-db88-4e5f-8e14-abd997d90aa6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245561674700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d49c92b-627c-4bbf-8f4b-fc4d76255517", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245561842400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb48547c-75d4-4cde-89bc-75cd261d2610", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245564054400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fa7b617-5732-4e47-a670-f6d388cd39dd", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245574508000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee8cb3e8-2ae6-49d2-82ee-2890ae39056e", "name": "entry : default@ProcessLibs cost memory 0.1433868408203125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245574760200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8083a59-d1c5-42cb-8296-e0ed6adcf05b", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245564029600, "endTime": 221245574946300}, "additional": {"logType": "info", "children": [], "durationId": "1e8e2025-4c8f-4642-bb6f-f1f5bc1d57a8"}}, {"head": {"id": "d8fa9c3f-5f78-4da4-b3ac-4c1c14ca6ad1", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245590384800, "endTime": 221245651813200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "4682a3e0-5988-46e6-8c83-408b96f7da8e", "logId": "35f6bab5-881b-4b57-a61e-a36aad38bf90"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4682a3e0-5988-46e6-8c83-408b96f7da8e", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245578447500}, "additional": {"logType": "detail", "children": [], "durationId": "d8fa9c3f-5f78-4da4-b3ac-4c1c14ca6ad1"}}, {"head": {"id": "e39b2cdf-1654-43da-b627-756a8e7bfa3c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245580951600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03e05e0c-19b2-4c08-ba77-6123b1a24a95", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245581150700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8b97168-0fb3-4651-8f4b-0cf7f61a5189", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245583847500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6560b5f0-5a04-4a7e-9dcf-284063ff8b36", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245590441500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dbd8cfc-b81d-4429-97b2-5bd5ca705ce7", "name": "Incremental task entry:default@CompileResource pre-execution cost: 59 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245651112000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea9542a5-d4d5-4e0d-a793-a481b0957ca6", "name": "entry : default@CompileResource cost memory -4.911033630371094", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245651568700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35f6bab5-881b-4b57-a61e-a36aad38bf90", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245590384800, "endTime": 221245651813200}, "additional": {"logType": "info", "children": [], "durationId": "d8fa9c3f-5f78-4da4-b3ac-4c1c14ca6ad1"}}, {"head": {"id": "9f5c5d0c-fdd7-44a5-b256-56bb33c0f122", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245671018000, "endTime": 221245676250800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "547b2d6b-1675-4892-86e7-3e1dd81ce5cb", "logId": "f1eae2e8-afb4-49aa-b6ae-81245632dbd6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "547b2d6b-1675-4892-86e7-3e1dd81ce5cb", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245660929300}, "additional": {"logType": "detail", "children": [], "durationId": "9f5c5d0c-fdd7-44a5-b256-56bb33c0f122"}}, {"head": {"id": "e7d9e468-fad3-42f9-be70-e9e20bd8274a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245664329100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90eba87c-9422-461c-942e-b94df90fb88b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245664574800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17854eff-ec28-41e0-9274-733b2f7be3d1", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245671043200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "773f5c11-e971-4357-b00a-52e4af7e6908", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245672098800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29430fa0-d096-4b61-a020-f37cad9e1e46", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245675731200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df83acbe-317f-4f69-b57a-8a05416ab65d", "name": "entry : default@DoNativeStrip cost memory 0.0804290771484375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245676033700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1eae2e8-afb4-49aa-b6ae-81245632dbd6", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245671018000, "endTime": 221245676250800}, "additional": {"logType": "info", "children": [], "durationId": "9f5c5d0c-fdd7-44a5-b256-56bb33c0f122"}}, {"head": {"id": "4b80b486-de19-4bf4-acd8-d2d78289259c", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245695162400, "endTime": 221276586817600}, "additional": {"children": ["a80a04d7-a6a2-42ad-83aa-0aa26b6aaa1b", "8f16e73c-dbe9-44d1-a5db-8e82da00774a"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed."], "detailId": "9c31f063-dcd4-4e7a-adfa-2f1bbfb92af6", "logId": "13ef31b8-3104-4f83-a374-98dcf9f58a7a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c31f063-dcd4-4e7a-adfa-2f1bbfb92af6", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245679961100}, "additional": {"logType": "detail", "children": [], "durationId": "4b80b486-de19-4bf4-acd8-d2d78289259c"}}, {"head": {"id": "349dd3f5-9aa6-4336-a32e-19d456328a47", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245683552200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6892fca9-b779-4500-9d94-2933cb892a93", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245683849300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "709fa866-f888-4a3a-9989-7d2ae04202fb", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245695179700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bedbd006-9dc9-4c83-9969-5750476363aa", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245695489500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38c68cff-fddb-4a22-b614-699924c35ccc", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245765833800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "352651fa-58c1-46cb-a9a6-a4f670230d38", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 58 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245766121800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "958efe23-7735-40de-be3c-7ce43173452b", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245805829000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d65c924-342b-44d4-a5e0-abc4fa8e05cd", "name": "default@CompileArkTS work[4] is submitted.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245809910900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a80a04d7-a6a2-42ad-83aa-0aa26b6aaa1b", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 221252993307500, "endTime": 221276566259400}, "additional": {"children": ["3bd77efe-7c06-4c6d-b938-d26db6a157a9", "45682715-ce9f-4417-927f-ea99c5c41141", "b3149db2-343b-4e51-b7f4-342502016e6c", "9809dd3c-5dc2-493f-8089-04b76440a3fe", "02fb7ee6-aec3-4173-bf41-86d7984306ab", "7ae190ca-a3b8-4c82-8f83-794af73182e9", "4e79290b-5e81-4847-b18f-1b7fed894b75"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "4b80b486-de19-4bf4-acd8-d2d78289259c", "logId": "291df081-158f-4537-836a-02525d4253cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b93a71c7-2d9f-4154-88dd-c3b7f3c8de88", "name": "default@CompileArkTS work[4] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245811377200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7629f47-4b5b-4747-ae88-8f3b380b1973", "name": "default@CompileArkTS work[4] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245811558800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb4e7452-d6d4-4f65-85d9-0da66c27c981", "name": "CopyResources startTime: 221245811672600", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245811676600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6bcfdd3-bcc0-45cc-9d7e-826f38ca0581", "name": "default@CompileArkTS work[5] is submitted.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245811770200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f16e73c-dbe9-44d1-a5db-8e82da00774a", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker18", "startTime": 221248146414400, "endTime": 221248165872000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "4b80b486-de19-4bf4-acd8-d2d78289259c", "logId": "0fe8616f-94b5-4ff2-80ad-bb446aef095e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "302661c6-e82c-42cb-8367-44cdbcd95cf9", "name": "default@CompileArkTS work[5] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245812975200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60ae2abb-8611-4a11-a4f9-1cb4d4c2e673", "name": "default@CompileArkTS work[5] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245813223500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fb94780-fba1-432f-8bf6-7a93e76ac47f", "name": "entry : default@CompileArkTS cost memory 2.107574462890625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245813430900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "390af175-c224-4cc0-b355-88c91ccda5fc", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245829475000, "endTime": 221245845181300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "e5acf3e1-c2d9-4205-bba7-557ee56ace83", "logId": "b87527fd-f667-411f-89d8-f6cc551005f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5acf3e1-c2d9-4205-bba7-557ee56ace83", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245817034100}, "additional": {"logType": "detail", "children": [], "durationId": "390af175-c224-4cc0-b355-88c91ccda5fc"}}, {"head": {"id": "9fb23067-187d-4c25-b307-c9f87d8c1552", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245819217000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b451ef27-872c-400d-b594-80668bbf9e21", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245819425000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a3ea127-f186-4bd0-8148-e04d8305e013", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245829498200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0101e03a-2e31-4092-8898-7e7566636df2", "name": "entry : default@BuildJS cost memory 0.34830474853515625", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245844657000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7cd656c-a27b-44b6-a87e-5749caf4565d", "name": "runTaskFromQueue task cost before running: 1 s 276 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245844970700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b87527fd-f667-411f-89d8-f6cc551005f7", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245829475000, "endTime": 221245845181300, "totalTime": 15441200}, "additional": {"logType": "info", "children": [], "durationId": "390af175-c224-4cc0-b355-88c91ccda5fc"}}, {"head": {"id": "9a984d51-2e4c-4ec0-84a1-9cc896cc426d", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245853963500, "endTime": 221245861890700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "3b7e4d69-5774-46b8-811a-6ba848ee327c", "logId": "833b4ead-a389-4123-a243-a262e1e3b9f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b7e4d69-5774-46b8-811a-6ba848ee327c", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245848691800}, "additional": {"logType": "detail", "children": [], "durationId": "9a984d51-2e4c-4ec0-84a1-9cc896cc426d"}}, {"head": {"id": "0ac865d3-8b8b-40cd-83c0-a77d4772a294", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245850390700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2d219d9-87d7-404d-8cce-5503774af0ba", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245850620000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6810827a-14f4-40c1-bb94-d28a20595e6f", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245853982600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3aec7cb-d315-4a97-b712-0843b205f2bf", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245855234300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "160289f6-5ac2-4277-95cb-e6e7d1402beb", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245861208900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a8106d8-39ea-432a-9e1f-f788cc66d021", "name": "entry : default@CacheNativeLibs cost memory 0.0999603271484375", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245861653000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "833b4ead-a389-4123-a243-a262e1e3b9f9", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245853963500, "endTime": 221245861890700}, "additional": {"logType": "info", "children": [], "durationId": "9a984d51-2e4c-4ec0-84a1-9cc896cc426d"}}, {"head": {"id": "d6e86df9-ebdd-413e-bd4b-d8186914df10", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221246057735800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5748af5c-a747-4f0e-95ef-452f76fd6f68", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221246058204400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47d0c189-6f8c-4415-8bee-327cb091de2d", "name": "default@CompileArkTS work[5] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221246060009700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d600a70-5068-4bae-9acc-5cade4e8afc5", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221246706820100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec03ca3d-0222-465c-9e0e-d5b5cf6de402", "name": "A work dispatched to worker[18] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221246707470000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db0f2574-90f3-4acf-89fc-1c1e97d0ada9", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221246707680700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00358e10-2180-462d-b2b7-8dc906f012cc", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221246707817700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aba62c8a-4bd3-4729-a373-fbb71bb7a6a9", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221246708083200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3808cab7-6e6f-437f-a729-b42a079d31eb", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221246708252800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91532f77-1a75-4f6a-8d6b-ca8fe211ca73", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221246708386600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07abfe24-b932-443a-8590-153a3fea52e5", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221246708519700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f35605a0-473d-4d0b-a6f4-2d3e8603b2ff", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221246708640700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a887f1f-a96b-4b4a-9c8f-9f7b4684830a", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221246708757900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f61694e-17c0-4e20-a849-afbf6a57ae4a", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221246708873500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e27f22d-04d9-440f-b0b9-cd8cc2d1f52e", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221246708988200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68a6e579-63aa-4888-822d-3a7e99d274c4", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221246709137300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26e88f10-9313-4555-90c1-dc5311bc064b", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221246709253700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c210e3e-8f0c-413c-8382-f315290bda06", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221246709944700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7578bf7-5428-41fa-9cff-d1a5f97c0236", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221246710654700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d3334cb-2a51-4967-98dc-751362d65169", "name": "default@CompileArkTS work[4] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221246712471000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "476f03c6-05ea-4ac7-bb9e-5dfc00ad9b50", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221248166264700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75c4ce1b-ff9a-4dd0-a2a7-43cf51f31d0a", "name": "CopyResources is end, endTime: 221248166525100", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221248166534900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f848d440-def7-4284-a3fc-0fd3bd39417f", "name": "default@CompileArkTS work[5] done.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221248166808100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fe8616f-94b5-4ff2-80ad-bb446aef095e", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Worker18", "startTime": 221248146414400, "endTime": 221248165872000}, "additional": {"logType": "info", "children": [], "durationId": "8f16e73c-dbe9-44d1-a5db-8e82da00774a", "parent": "13ef31b8-3104-4f83-a374-98dcf9f58a7a"}}, {"head": {"id": "d375ffc3-b52e-45e3-84aa-706ae420a5d5", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221248713223300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dde4c079-05fd-4908-b119-a2a59a9b34f6", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276567173200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bd77efe-7c06-4c6d-b938-d26db6a157a9", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 221252995243400, "endTime": 221256026729200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a80a04d7-a6a2-42ad-83aa-0aa26b6aaa1b", "logId": "8b588bfb-c728-4c27-9468-66d6d476d385"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b588bfb-c728-4c27-9468-66d6d476d385", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221252995243400, "endTime": 221256026729200}, "additional": {"logType": "info", "children": [], "durationId": "3bd77efe-7c06-4c6d-b938-d26db6a157a9", "parent": "291df081-158f-4537-836a-02525d4253cd"}}, {"head": {"id": "45682715-ce9f-4417-927f-ea99c5c41141", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 221256030861300, "endTime": 221256200220000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a80a04d7-a6a2-42ad-83aa-0aa26b6aaa1b", "logId": "fc9d8dce-6b57-4213-b9fc-d5604a7267c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc9d8dce-6b57-4213-b9fc-d5604a7267c8", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221256030861300, "endTime": 221256200220000}, "additional": {"logType": "info", "children": [], "durationId": "45682715-ce9f-4417-927f-ea99c5c41141", "parent": "291df081-158f-4537-836a-02525d4253cd"}}, {"head": {"id": "b3149db2-343b-4e51-b7f4-342502016e6c", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 221256200454000, "endTime": 221256200992400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a80a04d7-a6a2-42ad-83aa-0aa26b6aaa1b", "logId": "3f8fbf1a-4751-453d-b38c-404b5f9a6e17"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f8fbf1a-4751-453d-b38c-404b5f9a6e17", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221256200454000, "endTime": 221256200992400}, "additional": {"logType": "info", "children": [], "durationId": "b3149db2-343b-4e51-b7f4-342502016e6c", "parent": "291df081-158f-4537-836a-02525d4253cd"}}, {"head": {"id": "9809dd3c-5dc2-493f-8089-04b76440a3fe", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 221256201160200, "endTime": 221276231689800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a80a04d7-a6a2-42ad-83aa-0aa26b6aaa1b", "logId": "d474d73b-3506-4483-93c1-06b4edadcd78"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d474d73b-3506-4483-93c1-06b4edadcd78", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221256201160200, "endTime": 221276231689800}, "additional": {"logType": "info", "children": [], "durationId": "9809dd3c-5dc2-493f-8089-04b76440a3fe", "parent": "291df081-158f-4537-836a-02525d4253cd"}}, {"head": {"id": "02fb7ee6-aec3-4173-bf41-86d7984306ab", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 221276232364800, "endTime": 221276250146700}, "additional": {"children": ["c60f3137-4212-48be-acc1-bda0742228e3", "f7cf37e7-6a31-486d-abf1-9d0ba1ce766e", "5fa5f359-e7bb-4a15-a612-668e0f4ba686"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a80a04d7-a6a2-42ad-83aa-0aa26b6aaa1b", "logId": "f475e1bb-a2de-42fb-b138-c88e8fa7836c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f475e1bb-a2de-42fb-b138-c88e8fa7836c", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276232364800, "endTime": 221276250146700}, "additional": {"logType": "info", "children": ["b67b9d57-29aa-46c8-83bc-d465097595ae", "34b8627a-061e-4cb4-9446-dffb726af244", "d9b4478a-faa3-4dbe-a4a4-a5a52cc44174"], "durationId": "02fb7ee6-aec3-4173-bf41-86d7984306ab", "parent": "291df081-158f-4537-836a-02525d4253cd"}}, {"head": {"id": "c60f3137-4212-48be-acc1-bda0742228e3", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 221276232749500, "endTime": 221276232820500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "02fb7ee6-aec3-4173-bf41-86d7984306ab", "logId": "b67b9d57-29aa-46c8-83bc-d465097595ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b67b9d57-29aa-46c8-83bc-d465097595ae", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276232749500, "endTime": 221276232820500}, "additional": {"logType": "info", "children": [], "durationId": "c60f3137-4212-48be-acc1-bda0742228e3", "parent": "f475e1bb-a2de-42fb-b138-c88e8fa7836c"}}, {"head": {"id": "f7cf37e7-6a31-486d-abf1-9d0ba1ce766e", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 221276232845300, "endTime": 221276239146000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "02fb7ee6-aec3-4173-bf41-86d7984306ab", "logId": "34b8627a-061e-4cb4-9446-dffb726af244"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34b8627a-061e-4cb4-9446-dffb726af244", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276232845300, "endTime": 221276239146000}, "additional": {"logType": "info", "children": [], "durationId": "f7cf37e7-6a31-486d-abf1-9d0ba1ce766e", "parent": "f475e1bb-a2de-42fb-b138-c88e8fa7836c"}}, {"head": {"id": "5fa5f359-e7bb-4a15-a612-668e0f4ba686", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 221276239171100, "endTime": 221276250064100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "02fb7ee6-aec3-4173-bf41-86d7984306ab", "logId": "d9b4478a-faa3-4dbe-a4a4-a5a52cc44174"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d9b4478a-faa3-4dbe-a4a4-a5a52cc44174", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276239171100, "endTime": 221276250064100}, "additional": {"logType": "info", "children": [], "durationId": "5fa5f359-e7bb-4a15-a612-668e0f4ba686", "parent": "f475e1bb-a2de-42fb-b138-c88e8fa7836c"}}, {"head": {"id": "7ae190ca-a3b8-4c82-8f83-794af73182e9", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 221276250215900, "endTime": 221276557147400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a80a04d7-a6a2-42ad-83aa-0aa26b6aaa1b", "logId": "c8ee57d1-ebfa-462b-9e51-3ec4d0b450b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8ee57d1-ebfa-462b-9e51-3ec4d0b450b5", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276250215900, "endTime": 221276557147400}, "additional": {"logType": "info", "children": [], "durationId": "7ae190ca-a3b8-4c82-8f83-794af73182e9", "parent": "291df081-158f-4537-836a-02525d4253cd"}}, {"head": {"id": "4e79290b-5e81-4847-b18f-1b7fed894b75", "name": "load compilation dependencies", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 221248373093500, "endTime": 221252988446700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a80a04d7-a6a2-42ad-83aa-0aa26b6aaa1b", "logId": "5685ed64-5477-4baf-b6e9-f66d7361ef7c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5685ed64-5477-4baf-b6e9-f66d7361ef7c", "name": "load compilation dependencies", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221248373093500, "endTime": 221252988446700}, "additional": {"logType": "info", "children": [], "durationId": "4e79290b-5e81-4847-b18f-1b7fed894b75", "parent": "291df081-158f-4537-836a-02525d4253cd"}}, {"head": {"id": "485e304d-4b89-4775-a3d8-f824551acece", "name": "default@CompileArkTS work[4] done.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276586450800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "291df081-158f-4537-836a-02525d4253cd", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Worker4", "startTime": 221252993307500, "endTime": 221276566259400}, "additional": {"logType": "info", "children": ["8b588bfb-c728-4c27-9468-66d6d476d385", "fc9d8dce-6b57-4213-b9fc-d5604a7267c8", "3f8fbf1a-4751-453d-b38c-404b5f9a6e17", "d474d73b-3506-4483-93c1-06b4edadcd78", "f475e1bb-a2de-42fb-b138-c88e8fa7836c", "c8ee57d1-ebfa-462b-9e51-3ec4d0b450b5", "5685ed64-5477-4baf-b6e9-f66d7361ef7c"], "durationId": "a80a04d7-a6a2-42ad-83aa-0aa26b6aaa1b", "parent": "13ef31b8-3104-4f83-a374-98dcf9f58a7a"}}, {"head": {"id": "13ef31b8-3104-4f83-a374-98dcf9f58a7a", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221245695162400, "endTime": 221276586817600, "totalTime": 23710810200}, "additional": {"logType": "info", "children": ["291df081-158f-4537-836a-02525d4253cd", "0fe8616f-94b5-4ff2-80ad-bb446aef095e"], "durationId": "4b80b486-de19-4bf4-acd8-d2d78289259c"}}, {"head": {"id": "c6b0c9bc-d3f6-43db-99b8-9d7be141887b", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276610705200, "endTime": 221276614228000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "01f88739-de6a-4278-91c9-a5a6a0815bd5", "logId": "e3c560a3-a4f3-4cd7-9f6a-af77e3af2055"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "01f88739-de6a-4278-91c9-a5a6a0815bd5", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276603905300}, "additional": {"logType": "detail", "children": [], "durationId": "c6b0c9bc-d3f6-43db-99b8-9d7be141887b"}}, {"head": {"id": "9276a79b-9212-4210-a078-7aa0b0d7d70b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276607212700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81e9540b-ef8d-4c5a-9d0b-07496c00bf35", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276607471900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf0c5689-7c55-492c-ada7-18a81c173982", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276610727900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dc441d7-cabb-49cb-9009-c71ab9559110", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276611480200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "488e8741-b35c-45a2-813b-7e4921a55cde", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276613864400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6da60b27-f36d-49c4-83eb-a068af22861f", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07904815673828125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276614074500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3c560a3-a4f3-4cd7-9f6a-af77e3af2055", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276610705200, "endTime": 221276614228000}, "additional": {"logType": "info", "children": [], "durationId": "c6b0c9bc-d3f6-43db-99b8-9d7be141887b"}}, {"head": {"id": "4da48320-57a1-4978-abe5-736d487891c1", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276642131200, "endTime": 221277168235500}, "additional": {"children": ["f1fe85a0-3ae3-4a8a-b349-5cacf2bf1d1c", "026368a4-b47b-4720-8bbb-91922d4e1f6f"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "65d403dc-3fd3-4b3b-a283-cfc1b5e1f247", "logId": "1ec84327-fdd0-43b7-99af-b7d398cccd61"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65d403dc-3fd3-4b3b-a283-cfc1b5e1f247", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276620048300}, "additional": {"logType": "detail", "children": [], "durationId": "4da48320-57a1-4978-abe5-736d487891c1"}}, {"head": {"id": "3f861cc3-a334-4198-b103-9d2914efa226", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276622367900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24cdac9c-8c57-4a31-8193-fb2f8f2cf851", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276622601700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3057d236-7b57-45ac-bbdc-d70a99554749", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276642160900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43f63776-92d9-445b-a4c5-d3a996120f46", "name": "entry:default@PackageHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276690183100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "350c91fb-d991-4e8a-90d9-8f2684b58ccc", "name": "Incremental task entry:default@PackageHap pre-execution cost: 42 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276690454300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d3e2824-f004-42a0-ac6d-cc087398c9c6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276690636400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ca58ca2-1966-4ef2-bcf5-b29659b7dfb4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276690755500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1fe85a0-3ae3-4a8a-b349-5cacf2bf1d1c", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276693904300, "endTime": 221276702060400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4da48320-57a1-4978-abe5-736d487891c1", "logId": "7cc2277f-f941-4c67-a75f-58daa3dc468a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "76d4b597-8459-44ac-83ff-d102aba09f44", "name": "Use tool [C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Huawei\\\\Sdk\\\\OpenHarmony\\\\Sdk\\\\18\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--rpcid-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\syscap\\\\default\\\\rpcid.sc',\n  '--ets-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets',\n  '--pkg-context-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader\\\\default\\\\pkgContextInfo.json'\n]", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276701138900}, "additional": {"logType": "debug", "children": [], "durationId": "4da48320-57a1-4978-abe5-736d487891c1"}}, {"head": {"id": "7cc2277f-f941-4c67-a75f-58daa3dc468a", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276693904300, "endTime": 221276702060400}, "additional": {"logType": "info", "children": [], "durationId": "f1fe85a0-3ae3-4a8a-b349-5cacf2bf1d1c", "parent": "1ec84327-fdd0-43b7-99af-b7d398cccd61"}}, {"head": {"id": "026368a4-b47b-4720-8bbb-91922d4e1f6f", "name": "submit HAP packaging task to java daemon server", "description": "Pack HAP in java daemon", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276703821700, "endTime": 221277146484500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4da48320-57a1-4978-abe5-736d487891c1", "logId": "e3e9ade6-ba8f-42d8-8a9d-bf71588b1d8f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d46d9933-431b-4c8c-8410-1e31fb945689", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277142221600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8679b844-6d5c-4923-9641-67f59dbe6640", "name": "java daemon socket received message:{\"code\":0,\"message\":\"pack success\"}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277145090500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3e9ade6-ba8f-42d8-8a9d-bf71588b1d8f", "name": "submit HAP packaging task to java daemon server", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276703821700, "endTime": 221277146467800}, "additional": {"logType": "info", "children": [], "durationId": "026368a4-b47b-4720-8bbb-91922d4e1f6f", "parent": "1ec84327-fdd0-43b7-99af-b7d398cccd61"}}, {"head": {"id": "606c3b4f-bcdd-4539-9cc0-52f9c2726364", "name": "entry : default@PackageHap cost memory -4.925750732421875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277167756400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9892d7b-9dfe-4dce-af99-bea5f8a35a2a", "name": "runTaskFromQueue task cost before running: 32 s 599 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277168094500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ec84327-fdd0-43b7-99af-b7d398cccd61", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221276642131200, "endTime": 221277168235500, "totalTime": 525896400}, "additional": {"logType": "info", "children": ["7cc2277f-f941-4c67-a75f-58daa3dc468a", "e3e9ade6-ba8f-42d8-8a9d-bf71588b1d8f"], "durationId": "4da48320-57a1-4978-abe5-736d487891c1"}}, {"head": {"id": "d86948b0-ba73-464e-b43b-b5cd990be1d9", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277190010700, "endTime": 221277686456800}, "additional": {"children": ["e1ee9336-0ccb-4885-86a0-7dce5b73ec16", "0391e183-a912-47c5-bce6-ac06b4e522de"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "eb7062d3-2aea-4aa5-aca3-5eb31e768784", "logId": "244bd37d-ce7c-4e95-bd52-0b32929c825c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb7062d3-2aea-4aa5-aca3-5eb31e768784", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277178985500}, "additional": {"logType": "detail", "children": [], "durationId": "d86948b0-ba73-464e-b43b-b5cd990be1d9"}}, {"head": {"id": "03e8fcaa-b010-4cb7-96fe-29a4df720594", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277182990900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaff3e84-1647-4196-9268-b024c4a4c10f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277183233400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03ad8442-f2a4-45d1-9141-064ef6cb9bd1", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277190038400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35382668-0813-4b6a-8829-928b7f2a127f", "name": "entry:default@SignHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277200512200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b71d4e9c-e61a-42c3-a198-502fec9e6d8b", "name": "Incremental task entry:default@SignHap pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277200838100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e625ec83-cbe6-46b7-bd8b-8898316d90c9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277201033400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ea6d994-ea1b-4772-88dc-a7454d178c97", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277201159800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1ee9336-0ccb-4885-86a0-7dce5b73ec16", "name": "generate hap signing command", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277205459400, "endTime": 221277400670000}, "additional": {"children": ["4c67fd70-d82c-4df4-b7a2-000b2e00f95a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d86948b0-ba73-464e-b43b-b5cd990be1d9", "logId": "d6f85b17-2f48-4013-a9d5-27adedb4daa2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c67fd70-d82c-4df4-b7a2-000b2e00f95a", "name": "verify signing configuration and get bundle name", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277260208900, "endTime": 221277396308800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e1ee9336-0ccb-4885-86a0-7dce5b73ec16", "logId": "344a9414-8f2e-4696-805c-7e161552ddbe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be594a72-21c5-4123-b74e-ed39b7db91c7", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277295090400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dbc4d07-3328-42ee-8030-24249422c6d9", "name": "java daemon socket received message:{\"code\":0,\"message\":\"verify profile success\"}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277395605500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "344a9414-8f2e-4696-805c-7e161552ddbe", "name": "verify signing configuration and get bundle name", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277260208900, "endTime": 221277396308800}, "additional": {"logType": "info", "children": [], "durationId": "4c67fd70-d82c-4df4-b7a2-000b2e00f95a", "parent": "d6f85b17-2f48-4013-a9d5-27adedb4daa2"}}, {"head": {"id": "d6f85b17-2f48-4013-a9d5-27adedb4daa2", "name": "generate hap signing command", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277205459400, "endTime": 221277400670000}, "additional": {"logType": "info", "children": ["344a9414-8f2e-4696-805c-7e161552ddbe"], "durationId": "e1ee9336-0ccb-4885-86a0-7dce5b73ec16", "parent": "244bd37d-ce7c-4e95-bd52-0b32929c825c"}}, {"head": {"id": "0391e183-a912-47c5-bce6-ac06b4e522de", "name": "execute hap signing command", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277402877100, "endTime": 221277685590700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d86948b0-ba73-464e-b43b-b5cd990be1d9", "logId": "bf9540b3-d253-4cd8-b29e-440add9bb1aa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "396a5ce3-0751-4ac8-98a2-a4043d087923", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277408981000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42055beb-a83e-4e13-adb2-5a7687c77fa0", "name": "java daemon socket received message:{\"code\":0,\"message\":\"sign app success\"}", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277684968200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf9540b3-d253-4cd8-b29e-440add9bb1aa", "name": "execute hap signing command", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277402877100, "endTime": 221277685590700}, "additional": {"logType": "info", "children": [], "durationId": "0391e183-a912-47c5-bce6-ac06b4e522de", "parent": "244bd37d-ce7c-4e95-bd52-0b32929c825c"}}, {"head": {"id": "7e983a27-d59e-4cf9-b30a-ee3c3cb01c4d", "name": "entry : default@SignHap cost memory -0.01476287841796875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277686089200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "879a6f36-6ed9-43ec-ad13-161be7bb3078", "name": "runTaskFromQueue task cost before running: 33 s 117 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277686334500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "244bd37d-ce7c-4e95-bd52-0b32929c825c", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277190010700, "endTime": 221277686456800, "totalTime": 496270400}, "additional": {"logType": "info", "children": ["d6f85b17-2f48-4013-a9d5-27adedb4daa2", "bf9540b3-d253-4cd8-b29e-440add9bb1aa"], "durationId": "d86948b0-ba73-464e-b43b-b5cd990be1d9"}}, {"head": {"id": "a418434a-896f-4e26-97f4-6d7812c2eb4e", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277699998600, "endTime": 221277713332400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7c8ccf5c-1f95-4ee1-a2cd-14a31c05a721", "logId": "7e0bec6c-4b11-463c-b9d5-efa7eb114acd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c8ccf5c-1f95-4ee1-a2cd-14a31c05a721", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277691980500}, "additional": {"logType": "detail", "children": [], "durationId": "a418434a-896f-4e26-97f4-6d7812c2eb4e"}}, {"head": {"id": "06978633-f8c7-4fb3-ac3c-9826f835e8f4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277694798000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b8d5344-2565-4d3b-80d0-c6323f47bed5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277695027400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaaa32d7-fff7-4f17-85ac-7379f1e6b3a6", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277700023900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8da26c0e-7fa6-41e1-9d4b-a229f87a552c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277712468200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3bf1fb9-9320-471e-993f-ae8737811eb2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277712724900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce0f0e43-fe9c-48d2-9d1f-d0a3e5127f05", "name": "entry : default@CollectDebugSymbol cost memory 0.2446746826171875", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277712989000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c22d409-a90f-4d67-902b-35428b4006f9", "name": "runTaskFromQueue task cost before running: 33 s 144 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277713196600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e0bec6c-4b11-463c-b9d5-efa7eb114acd", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277699998600, "endTime": 221277713332400, "totalTime": 13146500}, "additional": {"logType": "info", "children": [], "durationId": "a418434a-896f-4e26-97f4-6d7812c2eb4e"}}, {"head": {"id": "358c9bd2-f108-490b-99da-6d9e28524998", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277718430900, "endTime": 221277719209300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "fca404d7-a9ff-4008-bc88-6b402c640069", "logId": "fc709a7c-2768-4116-8d9c-611d474e0cd3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fca404d7-a9ff-4008-bc88-6b402c640069", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277718334100}, "additional": {"logType": "detail", "children": [], "durationId": "358c9bd2-f108-490b-99da-6d9e28524998"}}, {"head": {"id": "75dc32a1-8f5e-4052-a35d-05985c639b74", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277718449600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01d8c9c1-7c9d-4c7e-b328-b9131195956d", "name": "entry : assembleHap cost memory 0.011749267578125", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277718779200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d779c93-07ce-49a8-92fb-84c9ffd6997d", "name": "runTaskFromQueue task cost before running: 33 s 150 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277718962400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc709a7c-2768-4116-8d9c-611d474e0cd3", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277718430900, "endTime": 221277719209300, "totalTime": 493200}, "additional": {"logType": "info", "children": [], "durationId": "358c9bd2-f108-490b-99da-6d9e28524998"}}, {"head": {"id": "c6fa4a97-f561-4fb8-b4e9-347a66f785af", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277744361200, "endTime": 221277744431800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c87f63e7-e474-494a-b853-e8d4e00478a7", "logId": "19f17435-eab0-4e3a-b054-c94a511fb975"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19f17435-eab0-4e3a-b054-c94a511fb975", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277744361200, "endTime": 221277744431800}, "additional": {"logType": "info", "children": [], "durationId": "c6fa4a97-f561-4fb8-b4e9-347a66f785af"}}, {"head": {"id": "a6f00927-eb4c-442d-8bf9-c3db0facc273", "name": "BUILD SUCCESSFUL in 33 s 175 ms ", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277744665500}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "d2702aa9-ac53-41fc-aebb-abc3cc76c4ad", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221244569648400, "endTime": 221277745747400}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 19, "minute": 25, "second": 7}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "21907432-a49d-4136-a1ab-4a1ffb2e9fe7", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277745967700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56da28c4-bf0c-48af-9b7f-8ef3de5af17c", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277746223900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03095f67-9d20-4ea4-b889-9bf1493f489a", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277747277000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b23affea-3da4-4740-96f8-b70f786607ba", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277747473100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30ac1467-5dc2-414b-bc1f-8e9d78d00441", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277747615000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce951883-05f7-4c94-84d8-142f8918a36f", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277747949200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0392b900-e6b6-414f-8122-2868df58129f", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277748042400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c606808d-48ea-4781-98a0-951e3dbc06f5", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277749037900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e1e5bda-56e6-4d0a-bca0-20e234ad65f2", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277749494700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5662813d-603a-4107-8c65-f7d98a549e88", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277749621000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c663220a-ed8b-421b-ad61-e3c3d5f9b24b", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277749684300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ce5291b-71c4-4bf6-8cfe-2d80079e6819", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277749729500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "248e465e-2105-4914-8170-2b5e2cd74cb8", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277749765400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ea8dbb5-7f3d-4a8e-b603-8a122f2c6687", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277754219500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0709b9b-ccf3-4d36-b790-2e02c3b568c3", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277755182700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb8fd178-b19f-4e60-81a5-9c1318e181c4", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277755731300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7673630-64f9-47f5-bbae-3c5e6930fdac", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277755912400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55977cbb-dcbc-4241-ba44-e8aa3818eae6", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277756041500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "854be40f-a35c-4da5-bc8e-c39d1b2a1a83", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277756176500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b463c2f-fa21-4a44-8c3c-35af310be983", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277756286400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8f00bb9-f360-4f8f-8d74-69eb1676e640", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277756422300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55ebfa0d-f192-45a2-8dd5-64f15f34db66", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277766321100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e0b7aa9-e5e5-474d-9882-d6210995e582", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277771773100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ad8a995-e004-48be-b6e7-1bde2b3f29f9", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277773830200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb7fc630-d466-496a-80c0-5a2e4331353a", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277774880700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d35bab43-6697-4c58-b625-92da79369a09", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277775808800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95b44efb-5c8d-4826-b9b6-acf30282b72f", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277778473100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80790e4a-9a22-4123-ae42-ba89545670e2", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277778695000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a7f67d3-30b7-4e66-96b1-a5def5fab38f", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277779372400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fd77d1d-a2da-4e26-8dd1-f1bc18d794a1", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277780379400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43c7f385-a7a1-47da-88f9-02a779a5342a", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277782797200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f19c0f3-5546-4c7d-b047-293765d6d239", "name": "Incremental task entry:default@CompileArkTS post-execution cost:29 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277784762500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cb2dc80-c3d6-438f-9add-2494cdce45fd", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277792381700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0df5ddf5-944a-4561-87d4-9e522a3a3ffa", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277794709500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20dcf1d5-8407-41a6-bd24-0729ab3267bb", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277796045500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44def4bf-078b-40df-a6f1-6d62734dbfd8", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277796838700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cced77b7-de3d-4c89-b766-a348c07cd5de", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277797733800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93f25ee8-d7a1-491d-b207-97ae0a610010", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277800592600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9ddea22-ed80-4649-9500-f605dd13617b", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277804041300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca58d1f1-207b-4bc1-893c-7eb2f426090a", "name": "Incremental task entry:default@BuildJS post-execution cost:22 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277806070600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8097ae30-96e7-4b70-bdaa-7fe516299360", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277807624000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c05aae88-904c-4556-8655-d9d3c187c224", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277807827400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90471081-8456-49e5-9865-1a0ccc9924b0", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277813585400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "734863b2-46ee-4d85-a603-efb4c223e77c", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277815848300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bc42007-7466-46e7-af4d-0787e6f4037d", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277816954900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df81a07f-ea95-44af-84db-07928762ed9b", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277845043000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5aae2b41-5bfd-4a94-b608-b5c62f958f92", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277845968600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52de597a-066a-4aa1-ba9c-471f5e4b4a63", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277846770900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d23a7504-2062-4524-bbf4-34a405a12137", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277847561100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ded45905-e339-4f00-b62c-df8da5e0481c", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277847779500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f268e869-1147-41f4-8c7b-4342a6929870", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277848591500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f95f7730-f070-4e8f-85e9-8bc219b01b14", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277849431700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0e9d680-6f75-4ee4-ada7-b59362a4e025", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277851395700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94ecfc62-9282-46eb-976a-9a041c37bca2", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277852039000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bd6a06a-66ce-4ea1-a73e-a3e38ade4f49", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277852563000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a2ab7f2-5610-45ba-8107-7fafc9f0ce08", "name": "Incremental task entry:default@PackageHap post-execution cost:46 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277853187000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64f4c2b5-2fd8-4d9d-b589-37392d4fbe13", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.cer cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277853660200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efaa006c-e70c-4708-9b8b-fe70cb6ecd00", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p7b cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277854138200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7847baf5-9d5d-4720-ae3f-7e06cd76a80a", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p12 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277854627200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9236aab-78b7-4f9d-95e5-b648c1a8ac0c", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277855058400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff2dd841-0764-4245-b3b0-4932789194a3", "name": "Update task entry:default@SignHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277855185400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6386121-da1f-4261-82de-26cafd299e5b", "name": "Incremental task entry:default@SignHap post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277855637400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48b762e2-ef21-434e-8617-60a39d7eb883", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277864742200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77b7e007-2311-4122-bb03-42655dd736d7", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277865821600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1156417-a080-4fe6-b454-e7a7fe02327b", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277867427400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10536f15-e5c4-45a1-98e0-d5c62331f2aa", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:12 ms .", "description": "", "type": "log"}, "body": {"pid": 39308, "tid": "Main Thread", "startTime": 221277868055000}, "additional": {"logType": "debug", "children": []}}], "workLog": []}