// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：
import * as biaoqianguanli from './biaoqianguanli';
import * as fenleiguanli from './fenleiguanli';
import * as guan<PERSON><PERSON> from './guanliyuan';
import * as kaifazhe from './kaifazhe';
import * as pinglun from './pinglun';
import * as shenheyuan from './shenheyuan';
import * as sousuo from './sousuo';
import * as tongji from './tongji';
import * as xiaoxiduilie from './xiaoxiduilie';
import * as xitong from './xitong';
import * as yibiaopan from './yibiaopan';
import * as yingyong from './yingyong';
import * as yonghu from './yonghu';
export default {
  kaifazhe,
  guanliyuan,
  xiaoxiduilie,
  yingyong,
  pinglun,
  biaoqianguanli,
  tongji,
  fenleiguanli,
  yibiaopan,
  xitong,
  shenheyuan,
  sousuo,
  yonghu,
};
